#!/bin/bash
set -euo pipefail

# Hardcoded at generation time to avoid terraform calls at runtime
INSTANCE_ID="i-051a642d375450530"
RDS_HOST="bhc-rds-main.c36gygw02u26.ap-northeast-1.rds.amazonaws.com"
RDS_PORT="5432"

# Allow override via env
LOCAL_PORT=${LOCAL_PORT:-15432}
DOCUMENT_NAME="AWS-StartPortForwardingSessionToRemoteHost"

PARAMS=$(jq -n \
  --arg rds_port "$RDS_PORT" \
  --arg local_port "$LOCAL_PORT" \
  --arg host "$RDS_HOST" \
  '{portNumber:[$rds_port],localPortNumber:[$local_port],host:[$host]}')

set -x
aws ssm start-session \
  --target "$INSTANCE_ID" \
  --document-name "$DOCUMENT_NAME" \
  --parameters "$PARAMS"

