---
trigger: model_decision
description: when writing test
---

- テスト名は仕様を説明する日本語で書く
  - *what*（何をテストするか）と *when/if*（条件）と *then*（結果）を含める
  - 例: 有効な認証情報なら ok(true) を返す

- vitestのdescribe, it等はグローバルインポートされているので明示的にimportする必要はない

- テスト実行後にDBが自動的にクリーンアップされるので個別のテストファイルでのクリーンアップ処理は不要

- AAA（Arrange‑Act‑Assert）を明確なブロックとして区切る
  - 1行コメントか空行で `// ===== Arrange =====` のようにセクションを示す
  - 各ブロックは3～10行以内に収め、可読性を保つ

- Arrange では “入力” と “期待される外部状態” のみを準備する
  - Factory や Builder でテスト専用オブジェクトを生成
  - ハードコードされた値は定数化して再利用（例: `email`, `password`）
  - **テストごとのデータ作成・削除処理（例: `beforeEach` や `afterEach` での処理）も可能な限りファクトリに集約し、テストコードの可読性・保守性を高める。ファクトリ内でユニークなテストデータ（例: `crypto.randomUUID()` を利用したメールアドレス）を生成することを推奨する。**

- Act は 1行にまとめ、副作用を含めない
  - 例: `const result = await verifyLogin({...});`
  - ここではログや検証は行わない

- Assert では成功系は `isOk`・失敗系は `isErr` の両方を検証する
  - unwrap 系メソッドを使う場合は `_unsafeUnwrap()` や `_unsafeUnwrapErr()` と組み合わせ
  - 期待するエラー型まで明示する (`toBeInstanceOf(InvalidPasswordError)`)

- モックは基本作らない
  - 実装の詳細に立ち入るモックは後続リファクタで壊れやすい
  - 外部 I/O（HTTP）だけをスパイ/スタブする

テストの実装例
```login_verification.test.ts
import bcrypt from 'bcrypt';
import { verifyLogin, InvalidPasswordError } from './login_verification';
import { adminUserFactory } from '@web-test/factories/admin_user_factory';

describe('verifyLogin', () => {
  const email = '<EMAIL>';
  const password = 'password123';
  const salt = bcrypt.genSaltSync(10);

  it('有効な認証情報なら ok(true) を返す', async () => {
    // ===== Arrange =====
    const adminUser = await adminUserFactory.create({
      email,
      password_hash: bcrypt.hashSync(password, salt),
      salt,
    });

    // ===== Act =====
    const result = await verifyLogin({ user: adminUser, challengePassword: password });

    // ===== Assert =====
    expect(result.isOk()).toBe(true);
    expect(result._unsafeUnwrap()).toBe(true);
  });

  it('不正なパスワードなら err(InvalidPasswordError) を返す', async () => {
    // ===== Arrange =====
    const adminUser = await adminUserFactory.create({
      email,
      password_hash: bcrypt.hashSync(password, salt),
      salt,
    });

    // ===== Act =====
    const result = await verifyLogin({ user: adminUser, challengePassword: 'wrongPassword' });

    // ===== Assert =====
    expect(result.isErr()).toBe(true);
    expect(result._unsafeUnwrapErr()).toBeInstanceOf(InvalidPasswordError);
  });
});
```