---
trigger: always_on
---

<PERSON><PERSON><PERSON>一口馬主クラブの運用を目的としたシステムである。

### 1. プロジェクト全体の管理と構造

* **モノレポ:** `pnpm Workspaces` と `Turborepo` を使用し、複数のアプリケーションやライブラリを単一のリポジトリで管理している。
* **主要ディレクトリ構成:**

  * `apps/`: 各アプリケーションのソースコードを格納。

    * `core/`: クラブ運営者向けの基幹システムに関連するアプリケーション群。

      * `admin/`: 基幹システムの管理画面 (Next.js, React)
      * `api/`: 基幹システムのバックエンドAPI (**connectRPC (@connectrpc/connect)**)
    * `web/`: 一般ユーザーおよびコンテンツ管理者向けのアプリケーション群。

      * `admin/`: ユーザー向けコンテンツの管理画面 (Next.js, React)
      * `api/`: 一般ユーザー向け機能を提供するバックエンドAPI (**connectRPC**, Prisma)
      * `frontend/`: 一般ユーザー向けのフロントエンドUI (Next.js, React)
  * `packages/`: 複数のアプリケーション間で共有されるライブラリやスキーマ定義などを格納。

    * `admin-api-schema/`: 管理画面系APIのconnectRPCスキーマ定義。
    * `core-admin-api-schema/`: 基幹システム管理画面APIのconnectRPCスキーマ定義。
    * `prisma/`: Prismaのスキーマ、生成クライアント、マイグレーションを一元管理。
    * `web-api-schema/`: ユーザー向けAPIのconnectRPCスキーマ定義。
  * `infra/`: AWSインフラを管理するTerraformコードを格納。

    * `dev/`: 開発環境用の定義 (`vpc.tf`, `ecs.tf`, `rds.tf`, `alb.tf` など)
  * `db/`: （現在は`packages/prisma/`に集約されている可能性が高い）
  * `bin/`: 各種スクリプトファイル。
* **言語:** 全体を通してTypeScriptを使用している。

### 2. アプリケーションコンポーネントと技術スタック

#### 2.1 フロントエンド (`apps/core/admin`, `apps/web/admin`, `apps/web/frontend`)

* **フレームワーク:** `Next.js (v15)` を使用。SSR、SSG、React Server Components (RSC) に対応。
* **UIライブラリ:** `React (v19)`
* **スタイリング:** `Tailwind CSS (v4)`
* **状態管理・データフェッチ:** `TanStack Query (v5)` を利用。
* **APIクライアント:** connectRPCクライアント (`client`) を使用。`api_proxy.ts` を経由して呼び出す構成。

#### 2.2 バックエンドAPI (`apps/core/api`, `apps/web/api`)

* **フレームワーク:** `@connectrpc/connect` を全面採用。スキーマは `packages/` 内で管理。
* **ランタイム:** `Node.js`
* **バリデーション:** `Zod (v3)`
* **ORM:** `Prisma (v6)` を採用。共通スキーマは `packages/prisma/` で集中管理。
* **メール送信:** `Nodemailer` を使用。開発環境ではMailpitと連携。
* **エラーハンドリング:** `ConnectError` を使用し、明確なエラーコードで返却する設計。

#### 2.3 テスト

* **フレームワーク:** `Vitest`
* **スタイル:** AAA (Arrange, Act, Assert) パターンを採用。
* **APIテスト:** `getClient` を通じたconnectRPCクライアントで実行。

### 3. インフラストラクチャ (AWS)

* **構成管理:** `Terraform` を使用してコードでAWSインフラを管理。
* **ネットワーク:**

  * Public Subnet: ALB、WAF、IGWなど
  * Private Subnet A: `hami-web` ECSクラスター
  * Private Subnet B: `hami-core` ECSクラスター
  * DB Subnet: RDS Aurora (PostgreSQL)
* **コンテナ:** `Amazon ECS (Fargate)` を採用。

  * `hami-web`: `frontend`, `admin`, `api`
  * `hami-core`: `admin`, `api`
* **DB:** `Amazon RDS Aurora (PostgreSQL)` を採用。KMSによる暗号化を実施。
* **ロードバランシング:** `ALB`, `Route 53` を活用。
* **セキュリティ:**

  * `AWS WAF`, `Security Groups`, `IAM`, `Secrets Manager`, `KMS`, `ACM`
* **メール:** `Amazon SES`
* **モニタリング・ロギング:** `CloudWatch Logs`, `CloudTrail`
* **ストレージ:** `Amazon S3`

### 4. CI/CD

* **プラットフォーム:** `GitHub Actions`
* **ワークフロー:**

  * `terraform_plan.yaml`: PR時にTerraform Planを実行
  * `terraform_apply.yaml`: マージ時にTerraform Applyを実行
  * `deploy.yml`: ECS更新などを行う共通デプロイワークフロー
  * `*_deploy_dev.yml`: 各アプリケーションの開発環境デプロイをトリガー

### 5. 開発ツール

* `ESLint`: 静的解析
* `Prettier`: フォーマッター
* `tsx`: TypeScript実行ツール
* `Docker`: 開発・CI/CD用のコンテナ
