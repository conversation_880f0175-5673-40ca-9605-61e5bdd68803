resource "aws_vpc" "main" {
  cidr_block           = "*********/16"
  enable_dns_support   = true
  enable_dns_hostnames = true

  tags = {
    Name        = "bhc-prd-vpc"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Public Subnet A
resource "aws_subnet" "public_a" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = "*********/24"
  availability_zone       = "ap-northeast-1a"
  map_public_ip_on_launch = true

  tags = {
    Name        = "bhc-prd-public-a"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Public Subnet C (for HA)
resource "aws_subnet" "public_c" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = "*********/24"
  availability_zone       = "ap-northeast-1c"
  map_public_ip_on_launch = true

  tags = {
    Name        = "bhc-prd-public-c"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "igw" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name        = "bhc-prd-igw"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Public Route Table
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }

  tags = {
    Name        = "bhc-prd-public-rtb"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Public Route Table Associations
resource "aws_route_table_association" "public_a" {
  subnet_id      = aws_subnet.public_a.id
  route_table_id = aws_route_table.public.id
}

resource "aws_route_table_association" "public_c" {
  subnet_id      = aws_subnet.public_c.id
  route_table_id = aws_route_table.public.id
}

# Elastic IP for NAT Gateway
resource "aws_eip" "nat" {
  domain = "vpc"
  tags = {
    Name        = "bhc-prd-nat-eip"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# NAT Gateway (placed in Public Subnet A)
resource "aws_nat_gateway" "nat" {
  allocation_id = aws_eip.nat.id
  subnet_id     = aws_subnet.public_a.id

  tags = {
    Name        = "bhc-prd-nat-gw"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }

  depends_on = [aws_internet_gateway.igw]
}

# Private Subnet A
resource "aws_subnet" "private_a" {
  vpc_id            = aws_vpc.main.id
  cidr_block        = "*********/24"
  availability_zone = "ap-northeast-1a"

  tags = {
    Name        = "bhc-prd-private-a"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Private Subnet C
resource "aws_subnet" "private_c" {
  vpc_id            = aws_vpc.main.id
  cidr_block        = "*********/24"
  availability_zone = "ap-northeast-1c"

  tags = {
    Name        = "bhc-prd-private-c"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Private Route Tables
resource "aws_route_table" "private_a" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.nat.id
  }

  tags = {
    Name        = "bhc-prd-private-rtb-a"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_route_table" "private_c" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.nat.id
  }

  tags = {
    Name        = "bhc-prd-private-rtb-c"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Private Route Table Associations
resource "aws_route_table_association" "private_a" {
  subnet_id      = aws_subnet.private_a.id
  route_table_id = aws_route_table.private_a.id
}

resource "aws_route_table_association" "private_c" {
  subnet_id      = aws_subnet.private_c.id
  route_table_id = aws_route_table.private_c.id
}

# DB Subnets
resource "aws_subnet" "db_a" {
  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.10.4.0/24"
  availability_zone = "ap-northeast-1a"

  tags = {
    Name        = "bhc-prd-db-a"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_subnet" "db_c" {
  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.10.5.0/24"
  availability_zone = "ap-northeast-1c"

  tags = {
    Name        = "bhc-prd-db-c"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# DB Route Table (no internet route)
resource "aws_route_table" "db" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name        = "bhc-prd-db-rtb"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_route_table_association" "db_a" {
  subnet_id      = aws_subnet.db_a.id
  route_table_id = aws_route_table.db.id
}

resource "aws_route_table_association" "db_c" {
  subnet_id      = aws_subnet.db_c.id
  route_table_id = aws_route_table.db.id
}

# DB Subnet Group (for RDS)
resource "aws_db_subnet_group" "default" {
  name       = "bhc-prd-db-subnet-group"
  subnet_ids = [aws_subnet.db_a.id, aws_subnet.db_c.id]

  tags = {
    Name        = "bhc-prd-db-subnet-group"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Outputs
output "vpc_id" {
  value = aws_vpc.main.id
}

output "public_subnet_ids" {
  value = [aws_subnet.public_a.id, aws_subnet.public_c.id]
}

output "private_subnet_ids" {
  value = [aws_subnet.private_a.id, aws_subnet.private_c.id]
}

output "db_subnet_ids" {
  value = [aws_subnet.db_a.id, aws_subnet.db_c.id]
}

output "db_subnet_group_name" {
  value = aws_db_subnet_group.default.name
}

