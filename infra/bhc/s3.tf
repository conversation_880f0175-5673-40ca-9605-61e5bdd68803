# --- S3 Bucket for File Storage (bhc prd) ---

resource "aws_s3_bucket" "main" {
  bucket = "bhc-prd-files"

  tags = {
    Name        = "bhc-prd-files"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_s3_bucket_versioning" "main" {
  bucket = aws_s3_bucket.main.id
  versioning_configuration { status = "Enabled" }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "main" {
  bucket = aws_s3_bucket.main.id
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "main" {
  bucket                  = aws_s3_bucket.main.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# CORS configuration for frontend uploads
resource "aws_s3_bucket_cors_configuration" "main" {
  bucket = aws_s3_bucket.main.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = [
      "https://bloominghorseclub.co.jp",
      "https://admin.bloominghorseclub.co.jp",
      "http://localhost:3000" # For local development
    ]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

# SSM Parameter for S3 bucket name (optional, useful for apps)
resource "aws_ssm_parameter" "s3_bucket_name" {
  name  = "/bhc/prd/s3/bucket_name"
  type  = "String"
  value = aws_s3_bucket.main.bucket
  tags  = { Description = "S3 bucket name for file storage" }
}


# IAM Policy for S3 access (allow ECS tasks to upload horse images)
resource "aws_iam_policy" "s3_access" {
  name        = "bhc-s3-access-policy"
  description = "Allow ECS tasks to access S3 bucket for file uploads"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:GetObjectVersion",
          "s3:PutObjectAcl"
        ]
        Resource = "${aws_s3_bucket.main.arn}/*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:ListBucket",
          "s3:GetBucketLocation"
        ]
        Resource = aws_s3_bucket.main.arn
      }
    ]
  })
}

# Attach S3 policy to ECS task role
resource "aws_iam_role_policy_attachment" "ecs_task_s3_access" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.s3_access.arn
}
