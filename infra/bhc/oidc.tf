locals {
  github_actions_policy_arn = "arn:aws:iam::aws:policy/AdministratorAccess"
}

# GitHub OIDC Provider (create if not exists in this account)
resource "aws_iam_openid_connect_provider" "github" {
  url            = "https://token.actions.githubusercontent.com"
  client_id_list = ["sts.amazonaws.com"]
  # GitHub Actions OIDC root CA thumbprint
  thumbprint_list = ["6938fd4d98bab03faadb97b34396831e3780aea1"]
}

resource "aws_iam_role" "github_actions" {
  name = "bhc-prd-github-actions-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect    = "Allow"
        Principal = { Federated = aws_iam_openid_connect_provider.github.arn }
        Action    = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          StringLike = {
            "token.actions.githubusercontent.com:sub" = "repo:abelorg/hami:*"
          }
          StringEquals = {
            "token.actions.githubusercontent.com:aud" = "sts.amazonaws.com"
          }
        }
      },
    ]
  })

  tags = {
    Name        = "bhc-prd-github-actions-role"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_iam_role_policy_attachment" "github_actions" {
  role       = aws_iam_role.github_actions.name
  policy_arn = local.github_actions_policy_arn
}

output "bhc_prd_github_actions_role_arn" {
  description = "The ARN of the IAM role for GitHub Actions (bhc-prd)"
  value       = aws_iam_role.github_actions.arn
}

