# Security Groups (bhc/prd) - based on infra/dev/security_groups.tf

# --- Load Balancer Security Group ---
resource "aws_security_group" "lb" {
  name        = "bhc-prd-lb-sg"
  description = "Allow HTTP/HTTPS inbound traffic for ALB"
  vpc_id      = aws_vpc.main.id

  ingress {
    description      = "Allow HTTP from anywhere"
    from_port        = 80
    to_port          = 80
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  ingress {
    description      = "Allow HTTPS from anywhere"
    from_port        = 443
    to_port          = 443
    protocol         = "tcp"
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  egress {
    from_port        = 0
    to_port          = 0
    protocol         = "-1" # All protocols
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name        = "bhc-prd-lb-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# --- Internal Load Balancer Security Group ---
resource "aws_security_group" "internal_lb" {
  name        = "bhc-prd-internal-lb-sg"
  description = "Allow HTTP inbound traffic for Internal API ALB from VPC"
  vpc_id      = aws_vpc.main.id

  ingress {
    description = "Allow HTTP traffic from within the VPC"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.main.cidr_block]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "bhc-prd-internal-lb-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# --- ECS Security Groups ---

# ECS Frontend (Web)
resource "aws_security_group" "ecs_frontend" {
  name        = "bhc-prd-ecs-frontend-sg"
  description = "Allow traffic from LB to ECS Frontend instances"
  vpc_id      = aws_vpc.main.id

  # Allow traffic from the Load Balancer SG on port 3000
  ingress {
    description     = "Allow HTTP from LB for Frontend (3000)"
    from_port       = 3000 # Frontend container port
    to_port         = 3000 # Frontend container port
    protocol        = "tcp"
    security_groups = [aws_security_group.lb.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "bhc-prd-ecs-frontend-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# ECS API (Web API)
resource "aws_security_group" "ecs_api" {
  name        = "bhc-prd-ecs-api-sg"
  description = "Allow traffic from LB, Frontend, and Admin to ECS API instances"
  vpc_id      = aws_vpc.main.id

  # Allow traffic from the Public Load Balancer SG on port 8080
  ingress {
    description     = "Allow HTTP from Public LB for API (8080)"
    from_port       = 8080 # API container port
    to_port         = 8080 # API container port
    protocol        = "tcp"
    security_groups = [aws_security_group.lb.id]
  }

  # Allow traffic from the Internal Load Balancer SG on port 8080
  ingress {
    description     = "Allow HTTP from Internal LB for API (8080)"
    from_port       = 8080 # API container port
    to_port         = 8080 # API container port
    protocol        = "tcp"
    security_groups = [aws_security_group.internal_lb.id]
  }

  # Allow traffic from Frontend SG for internal communication
  ingress {
    description     = "Allow traffic from Frontend"
    from_port       = 8080 # API container port
    to_port         = 8080 # API container port
    protocol        = "tcp"
    security_groups = [aws_security_group.ecs_frontend.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "bhc-prd-ecs-api-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# --- Security Groups for Core Components (bhc/prd) ---
# ECS Core (core-api)
resource "aws_security_group" "ecs_core" {
  name        = "bhc-prd-ecs-core-sg"
  description = "Allow traffic from Internal LB to ECS Core API"
  vpc_id      = aws_vpc.main.id

  # Allow HTTP from Internal LB (Core API internal ALB)
  ingress {
    description     = "Allow HTTP from Internal LB for Core API (8080)"
    from_port       = 8080
    to_port         = 8080
    protocol        = "tcp"
    security_groups = [aws_security_group.internal_lb.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "bhc-prd-ecs-core-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Security Group for Core Admin ALB (temporarily open, restrict later)
resource "aws_security_group" "core_alb" {
  name        = "bhc-prd-core-alb-sg"
  description = "Allow HTTPS inbound traffic for Core Admin ALB"
  vpc_id      = aws_vpc.main.id

  ingress {
    description = "Allow HTTP from anywhere (temporary)"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    description = "Allow HTTPS from anywhere (temporary)"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "bhc-prd-core-alb-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Security Group for Core Admin ECS Service
resource "aws_security_group" "core_admin" {
  name        = "bhc-prd-core-admin-sg"
  description = "Allow traffic from Core Admin ALB to ECS Core Admin instances"
  vpc_id      = aws_vpc.main.id

  # Allow traffic from the Core Admin ALB SG on port 3000
  ingress {
    description     = "Allow HTTP from Core Admin LB (3000)"
    from_port       = 3000
    to_port         = 3000
    protocol        = "tcp"
    security_groups = [aws_security_group.core_alb.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "bhc-prd-core-admin-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}


# ECS Admin
resource "aws_security_group" "ecs_admin" {
  name        = "bhc-prd-ecs-admin-sg"
  description = "Allow traffic from LB to ECS Admin instances"
  vpc_id      = aws_vpc.main.id

  ingress {
    description     = "Allow HTTP from LB for Admin (3000)"
    from_port       = 3000 # Admin container port
    to_port         = 3000 # Admin container port
    protocol        = "tcp"
    security_groups = [aws_security_group.lb.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "bhc-prd-ecs-admin-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# --- RDS Security Groups (placeholders for future DB) ---
resource "aws_security_group" "rds_main" {
  name        = "bhc-prd-rds-main-sg"
  description = "Allow traffic from ECS to RDS Main"
  vpc_id      = aws_vpc.main.id

  # Allow PostgreSQL traffic from ECS API SG and Bastion
  ingress {
    description     = "Allow PostgreSQL from ECS API"
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.ecs_api.id]
  }

  ingress {
    description     = "Allow PostgreSQL from Bastion"
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.bastion.id]
  }

  ingress {
    description     = "Allow PostgreSQL from ECS Core"
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.ecs_core.id]
  }


  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "bhc-prd-rds-main-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

