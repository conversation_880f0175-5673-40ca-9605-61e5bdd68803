# ACM certificate for bhc-prd (bloominghorseclub.co.jp)
resource "aws_acm_certificate" "main" {
  domain_name               = "bloominghorseclub.co.jp"
  subject_alternative_names = ["*.bloominghorseclub.co.jp"]
  validation_method         = "DNS"

  tags = {
    Name        = "bhc-prd-certificate"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Outputs to help set DNS validation records at onamae.com
output "acm_domain_validation_options" {
  value = aws_acm_certificate.main.domain_validation_options
}

