# --- ECR Repositories (bhc prd) ---

resource "aws_ecr_repository" "web_api" {
  name                 = "bhc-prd-web-api"
  image_tag_mutability = "MUTABLE"
  image_scanning_configuration { scan_on_push = true }
  tags = {
    Name        = "bhc-prd-web-api-ecr"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_ecr_repository" "web_frontend" {
  name                 = "bhc-prd-web-frontend"
  image_tag_mutability = "MUTABLE"
  image_scanning_configuration { scan_on_push = true }
  tags = {
    Name        = "bhc-prd-web-frontend-ecr"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_ecr_repository" "web_admin" {
  name                 = "bhc-prd-web-admin"
  image_tag_mutability = "MUTABLE"
  image_scanning_configuration { scan_on_push = true }
  tags = {
    Name        = "bhc-prd-web-admin-ecr"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_ecr_repository" "core_api" {
  name                 = "bhc-prd-core-api"
  image_tag_mutability = "MUTABLE"
  image_scanning_configuration { scan_on_push = true }
  tags = {
    Name        = "bhc-prd-core-api-ecr"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_ecr_repository" "core_admin" {
  name                 = "bhc-prd-core-admin"
  image_tag_mutability = "MUTABLE"
  image_scanning_configuration { scan_on_push = true }
  tags = {
    Name        = "bhc-prd-core-admin-ecr"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_ecr_repository" "web_migrate" {
  name                 = "bhc-prd-web-migrate"
  image_tag_mutability = "MUTABLE"
  image_scanning_configuration { scan_on_push = true }
  tags = {
    Name        = "bhc-prd-web-migrate-ecr"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Outputs
output "ecr_repository_url_web_api" { value = aws_ecr_repository.web_api.repository_url }
output "ecr_repository_url_web_frontend" { value = aws_ecr_repository.web_frontend.repository_url }
output "ecr_repository_url_web_admin" { value = aws_ecr_repository.web_admin.repository_url }
output "ecr_repository_url_web_migrate" { value = aws_ecr_repository.web_migrate.repository_url }
output "ecr_repository_url_core_api" { value = aws_ecr_repository.core_api.repository_url }
output "ecr_repository_url_core_admin" { value = aws_ecr_repository.core_admin.repository_url }

