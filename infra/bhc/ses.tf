# SES domain verification for bhc/prd (bloominghorseclub.co.jp)
# Based on infra/dev/ses.tf

resource "aws_ses_domain_identity" "main" {
  domain = "bloominghorseclub.co.jp"
}

resource "aws_ses_domain_dkim" "main" {
  domain = aws_ses_domain_identity.main.domain
}

# TXT for domain verification
resource "aws_route53_record" "ses_domain_verification_record" {
  zone_id = aws_route53_zone.public.zone_id
  name    = "_amazonses.${aws_ses_domain_identity.main.id}"
  type    = "TXT"
  ttl     = "600"
  records = [aws_ses_domain_identity.main.verification_token]
}

# CNAME x3 for DKIM
resource "aws_route53_record" "ses_dkim_verification_records" {
  count = 3

  zone_id = aws_route53_zone.public.zone_id
  name    = "${element(aws_ses_domain_dkim.main.dkim_tokens, count.index)}._domainkey.${aws_ses_domain_identity.main.id}"
  type    = "CNAME"
  ttl     = "600"
  records = ["${element(aws_ses_domain_dkim.main.dkim_tokens, count.index)}.dkim.amazonses.com"]
}

