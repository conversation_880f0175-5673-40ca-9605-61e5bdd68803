terraform {
  required_version = ">= 1.2.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "s3" {
    bucket         = "hami-bhc-tf-218903531167"
    key            = "bhc/terraform.tfstate"
    region         = "ap-northeast-1"
    dynamodb_table = "hami-bhc-tf-locks"
    encrypt        = true
  }
}

provider "aws" {
  region = "ap-northeast-1"
}

