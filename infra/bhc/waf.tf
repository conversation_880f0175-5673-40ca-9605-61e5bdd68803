# WAF (bhc/prd) - Web ACL and associations

# Note:
# - 初期は Count モードで適用し、誤検知がないことを確認後に Blockへ切替を推奨します。
# - Blockへ切り替える場合は、各 rule の action { count {} } を action { block {} } に変更してください。

resource "aws_wafv2_web_acl" "main" {
  name  = "bhc-prd-waf"
  scope = "REGIONAL"

  default_action {
    allow {}
  }

  # AWS Managed Rules (Count mode)
  rule {
    name     = "AWSManagedRulesCommonRuleSet"
    priority = 10
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.19"
      }
    }
    override_action {
      count {}
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "aws-common"
      sampled_requests_enabled   = true
    }
  }

  # KnownBadInputs (Count)
  rule {
    name     = "AWSManagedRulesKnownBadInputsRuleSet"
    priority = 20
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.22"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "aws-known-bad-inputs"
      sampled_requests_enabled   = true
    }
  }

  # Amazon IP Reputation (Count) - no versioning supported
  rule {
    name     = "AWSManagedRulesAmazonIpReputationList"
    priority = 30
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "aws-ip-reputation"
      sampled_requests_enabled   = true
    }
  }

  # Rate limit per IP (Count)
  rule {
    name     = "RateLimit2000"
    priority = 100
    action {
      count {}
    }
    statement {
      rate_based_statement {
        aggregate_key_type = "IP"
        limit              = 2000
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "rate-limit-2000"
      sampled_requests_enabled   = true
    }
  }


  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "bhc-prd-waf"
    sampled_requests_enabled   = true
  }

  tags = {
    Name        = "bhc-prd-waf"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# --- Associate Web ACL with Public ALBs ---
# 本番サイト ALB
resource "aws_wafv2_web_acl_association" "main_alb" {
  resource_arn = aws_lb.main.arn
  web_acl_arn  = aws_wafv2_web_acl.main.arn
}

# 管理画面 ALB
resource "aws_wafv2_web_acl_association" "admin_alb" {
  resource_arn = aws_lb.admin.arn
  web_acl_arn  = aws_wafv2_web_acl.main.arn
}

# コア管理画面 ALB
resource "aws_wafv2_web_acl_association" "core_admin_alb" {
  resource_arn = aws_lb.core_admin.arn
  web_acl_arn  = aws_wafv2_web_acl.main.arn
}

