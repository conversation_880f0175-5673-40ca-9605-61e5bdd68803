# Bastion (SSM Session Manager) for prd/bhc

# Amazon Linux 2023 x86_64 AMI (固定)
# ap-northeast-1 (東京)
# 注意: セキュリティアップデートのため定期的にAMI IDを更新してください
# 最新AMI確認: aws ssm get-parameter --name "/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-6.1-x86_64" --query 'Parameter.Value' --output text
# 現在のAMI: al2023-ami-2023.8.20250908.0-kernel-6.1-x86_64 (2025-09-06作成)

# IAM role for SSM
resource "aws_iam_role" "bastion_ssm_role" {
  name = "bhc-prd-bastion-ssm-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "sts:AssumeRole",
        Effect    = "Allow",
        Principal = { Service = "ec2.amazonaws.com" }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "bastion_ssm_core" {
  role       = aws_iam_role.bastion_ssm_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_instance_profile" "bastion_ssm_profile" {
  name = "bhc-prd-bastion-ssm-profile"
  role = aws_iam_role.bastion_ssm_role.name
}

# Security Group for Bastion (no ingress needed for SSM; egress all)
resource "aws_security_group" "bastion" {
  name        = "bhc-prd-bastion-sg"
  description = "SSM bastion access (no inbound)"
  vpc_id      = aws_vpc.main.id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "bhc-prd-bastion-sg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# EC2 Bastion Instance (private subnet, reaches SSM via NAT GW)
resource "aws_instance" "bastion" {
  ami                         = "ami-0228232d282f16465" # Amazon Linux 2023 (固定)
  instance_type               = "t3.micro"
  subnet_id                   = aws_subnet.private_a.id
  vpc_security_group_ids      = [aws_security_group.bastion.id]
  iam_instance_profile        = aws_iam_instance_profile.bastion_ssm_profile.name
  associate_public_ip_address = false

  metadata_options {
    http_tokens = "required"
  }

  root_block_device {
    volume_size = 8
    volume_type = "gp3"
    encrypted   = true
  }

  tags = {
    Name        = "bhc-prd-bastion"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}


