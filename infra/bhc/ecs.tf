# ECS (bhc/prd) — based on infra/dev/ecs.tf, naming and ports aligned

# --- CloudWatch Logs (only those needed now) ---
resource "aws_cloudwatch_log_group" "web_migrate_logs" {
  name              = "/ecs/bhc-web-migrate"
  retention_in_days = 30
}

resource "aws_cloudwatch_log_group" "web_frontend_logs" {
  name              = "/ecs/bhc-web-frontend-task"
  retention_in_days = 7
}
resource "aws_cloudwatch_log_group" "web_api_logs" {
  name              = "/ecs/bhc-web-api-task"
  retention_in_days = 7
}

resource "aws_ecs_task_definition" "web_migrate" {
  family                   = "bhc-web-migrate-task"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = 256
  memory                   = 512
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_execution_role.arn

  container_definitions = jsonencode([
    {
      name       = "bhc-web-migrate-container"
      image      = "${aws_ecr_repository.web_migrate.repository_url}:latest"
      essential  = true
      entryPoint = ["pnpm", "prisma", "migrate", "deploy"]
      secrets = [
        {
          name      = "DATABASE_URL"
          valueFrom = aws_ssm_parameter.database_url.arn
        }
      ]
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = "/ecs/bhc-web-migrate"
          awslogs-region        = "ap-northeast-1"
          awslogs-stream-prefix = "ecs"
        }
      }
    }
  ])
}

# --- ECS Cluster ---
resource "aws_ecs_cluster" "main" {
  name = "bhc-prd-cluster"
}

# --- ECS Cluster (Core) ---
resource "aws_ecs_cluster" "core" {
  name = "bhc-prd-core-cluster"

  tags = {
    Name = "bhc-prd-core-cluster"
  }
}

# Allow listing account id for ARNs
data "aws_caller_identity" "current" {}


# --- IAM Roles for ECS ---
# Execution role (pull from ECR, push logs)
resource "aws_iam_role" "ecs_task_execution_role" {
  name = "bhc-ecs-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "sts:AssumeRole",
        Effect    = "Allow",
        Principal = { Service = "ecs-tasks.amazonaws.com" }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Allow ECS task execution role to read required SSM parameters
resource "aws_iam_policy" "ecs_task_execution_ssm_policy" {
  name        = "bhc-ecs-task-execution-ssm-policy"
  description = "Allows ECS tasks to read specific SSM parameters"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = ["ssm:GetParameters"],
        Resource = [
          data.aws_ssm_parameter.ses_smtp_username.arn,
          data.aws_ssm_parameter.ses_smtp_password.arn,
          data.aws_ssm_parameter.jwt_secret.arn,
          data.aws_ssm_parameter.database_url.arn,
          aws_ssm_parameter.database_url.arn,
          data.aws_ssm_parameter.gmo_pg_site_id.arn,
          data.aws_ssm_parameter.gmo_pg_site_pass.arn,
          data.aws_ssm_parameter.gmo_pg_shop_id.arn,
          data.aws_ssm_parameter.gmo_pg_shop_pass.arn,
          aws_ssm_parameter.s3_bucket_name.arn,
          data.aws_ssm_parameter.sentry_dsn.arn
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_ssm_attach" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = aws_iam_policy.ecs_task_execution_ssm_policy.arn
}

# Task role (for app runtime; can attach policies later as needed)
resource "aws_iam_role" "ecs_task_role" {
  name = "bhc-ecs-task-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "sts:AssumeRole",
        Effect    = "Allow",
        Principal = { Service = "ecs-tasks.amazonaws.com" }
      }
    ]
  })

  tags = { Name = "bhc-ecs-task-role" }
}

# --- Task Definition: web-frontend ---
resource "aws_ecs_task_definition" "web_frontend" {
  family                   = "bhc-web-frontend-task"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = 2048
  memory                   = 4096
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn

  container_definitions = jsonencode([
    {
      name      = "bhc-web-frontend-container",
      image     = "${aws_ecr_repository.web_frontend.repository_url}:latest",
      cpu       = 1024,
      memory    = 2048,
      essential = true,
      environment = [
        { name = "WEB_API_ENDPOINT", value = "http://${aws_lb.internal_api.dns_name}/" },
        { name = "BASIC_AUTH_ENABLED", value = "false" },
        { name = "NEXT_PUBLIC_FINANCIAL_REVIEW_COMPLETED", value = "false" },
        { name = "SENTRY_DSN", value = "https://<EMAIL>/4510062213791747" },
        { name = "FRONTEND_ENDPOINT", value = "https://bloominghorseclub.co.jp" },

      ],
      portMappings = [
        { containerPort = 3000, hostPort = 3000, protocol = "tcp" }
      ],

      logConfiguration = {
        logDriver = "awslogs",
        options = {
          "awslogs-group"         = "/ecs/bhc-web-frontend-task",
          "awslogs-region"        = "ap-northeast-1",
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = { Name = "bhc-web-frontend-task" }
}

# --- ECS Service: web-frontend ---
resource "aws_ecs_service" "web_frontend" {
  name            = "bhc-web-frontend-service"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.web_frontend.arn
  desired_count   = 4
  launch_type     = "FARGATE"

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }
  deployment_minimum_healthy_percent = 100
  deployment_maximum_percent         = 200


  network_configuration {
    subnets          = [aws_subnet.private_a.id, aws_subnet.private_c.id]
    security_groups  = [aws_security_group.ecs_frontend.id]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.web_frontend.arn
    container_name   = "bhc-web-frontend-container"
    container_port   = 3000
  }

  depends_on = [aws_lb_listener.https]

  tags = { Name = "bhc-web-frontend-service" }
}

# --- Task Definition: web-api ---
resource "aws_ecs_task_definition" "web_api" {
  family                   = "bhc-web-api-task"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = 2048
  memory                   = 4096
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn

  container_definitions = jsonencode([
    {
      name         = "bhc-web-api-container",
      image        = "${aws_ecr_repository.web_api.repository_url}:latest",
      cpu          = 1024,
      memory       = 2048,
      essential    = true,
      portMappings = [{ containerPort = 8080, hostPort = 8080, protocol = "tcp" }],
      environment = [
        { name = "SMTP_HOST", value = "email-smtp.ap-northeast-1.amazonaws.com" },
        { name = "SMTP_PORT", value = "465" },
        { name = "SMTP_SECURE", value = "true" },
        { name = "SMTP_REQUIRE_TLS", value = "false" },
        { name = "SMTP_FROM_ADDRESS", value = "<EMAIL>" },
        { name = "FRONTEND_ENDPOINT", value = "https://bloominghorseclub.co.jp" },
        { name = "PORT", value = "8080" },
        { name = "HTTP2_ENABLED", value = "false" },
        { name = "AWS_REGION", value = "ap-northeast-1" },
        { name = "S3_BUCKET_NAME", value = aws_s3_bucket.main.bucket },
        { name = "GMO_PG_BASE_URL", value = "https://pt01.mul-pay.jp" },
        { name = "GMO_PG_TIMEOUT", value = "30000" },
        { name = "GMO_PG_RETRY_COUNT", value = "3" }
      ],
      secrets = [
        { name = "SMTP_USER", valueFrom = data.aws_ssm_parameter.ses_smtp_username.arn },
        { name = "SMTP_PASSWORD", valueFrom = data.aws_ssm_parameter.ses_smtp_password.arn },
        { name = "DATABASE_URL", valueFrom = data.aws_ssm_parameter.database_url.arn },
        { name = "JWT_SECRET", valueFrom = data.aws_ssm_parameter.jwt_secret.arn },
        { name = "GMO_PG_SITE_ID", valueFrom = data.aws_ssm_parameter.gmo_pg_site_id.arn },
        { name = "GMO_PG_SITE_PASS", valueFrom = data.aws_ssm_parameter.gmo_pg_site_pass.arn },
        { name = "GMO_PG_SHOP_ID", valueFrom = data.aws_ssm_parameter.gmo_pg_shop_id.arn },
        { name = "GMO_PG_SHOP_PASS", valueFrom = data.aws_ssm_parameter.gmo_pg_shop_pass.arn },
        { name = "SENTRY_DSN", valueFrom = data.aws_ssm_parameter.sentry_dsn.arn }
      ],
      logConfiguration = {
        logDriver = "awslogs",
        options = {
          "awslogs-group"         = "/ecs/bhc-web-api-task",
          "awslogs-region"        = "ap-northeast-1",
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = { Name = "bhc-web-api-task" }
}

# --- Task Definition: web-admin ---
resource "aws_ecs_task_definition" "web_admin" {
  family                   = "bhc-web-admin-task"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = 512
  memory                   = 1024
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn

  container_definitions = jsonencode([
    {
      name      = "bhc-web-admin-container",
      image     = "${aws_ecr_repository.web_admin.repository_url}:latest",
      cpu       = 256,
      memory    = 512,
      essential = true,
      environment = [
        { name = "WEB_API_ENDPOINT", value = "http://${aws_lb.internal_api.dns_name}/" }
      ],
      portMappings = [
        { containerPort = 3000, hostPort = 3000, protocol = "tcp" }
      ],
      logConfiguration = {
        logDriver = "awslogs",
        options = {
          "awslogs-group"         = "/ecs/bhc-web-admin-task",
          "awslogs-region"        = "ap-northeast-1",
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = { Name = "bhc-web-admin-task" }
}

# --- ECS Service: web-admin ---
resource "aws_ecs_service" "web_admin" {
  name            = "bhc-web-admin-service"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.web_admin.arn
  desired_count   = 2
  launch_type     = "FARGATE"

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }
  deployment_minimum_healthy_percent = 100
  deployment_maximum_percent         = 200


  network_configuration {
    subnets          = [aws_subnet.private_a.id, aws_subnet.private_c.id]
    security_groups  = [aws_security_group.ecs_admin.id]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.web_admin.arn
    container_name   = "bhc-web-admin-container"
    container_port   = 3000
  }

  depends_on = [aws_lb_listener.admin_https]

  tags = { Name = "bhc-web-admin-service" }
}


# --- ECS Service: web-api ---
resource "aws_ecs_service" "web_api" {
  name            = "bhc-web-api-service"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.web_api.arn
  desired_count   = 4
  launch_type     = "FARGATE"

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }
  deployment_minimum_healthy_percent = 100
  deployment_maximum_percent         = 200


  network_configuration {
    subnets          = [aws_subnet.private_a.id, aws_subnet.private_c.id]
    security_groups  = [aws_security_group.ecs_api.id]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.internal_api.arn
    container_name   = "bhc-web-api-container"
    container_port   = 8080
  }

  depends_on = [aws_lb_listener.internal_api]

  tags = { Name = "bhc-web-api-service" }
}

# --- Task Definition: core-api ---
resource "aws_ecs_task_definition" "core_api" {
  family                   = "bhc-core-api-task"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = 1024
  memory                   = 2048
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn

  container_definitions = jsonencode([
    {
      name         = "bhc-core-api-container",
      image        = "${aws_ecr_repository.core_api.repository_url}:latest",
      cpu          = 256,
      memory       = 512,
      essential    = true,
      portMappings = [{ containerPort = 8080, hostPort = 8080, protocol = "tcp" }],
      logConfiguration = {
        logDriver = "awslogs",
        options = {
          "awslogs-group"         = "/ecs/bhc-core-api-task",
          "awslogs-region"        = "ap-northeast-1",
          "awslogs-stream-prefix" = "ecs"
        }
      },
      environment = [
        { name = "PORT", value = "8080" },
        { name = "HTTP2_ENABLED", value = "false" },
        { name = "FRONTEND_ENDPOINT", value = "https://bloominghorseclub.co.jp" },
        { name = "AWS_REGION", value = "ap-northeast-1" },
        { name = "S3_BUCKET_NAME", value = aws_s3_bucket.main.bucket },
        { name = "GMO_PG_BASE_URL", value = "https://pt01.mul-pay.jp" },
        { name = "GMO_PG_TIMEOUT", value = "30000" },
        { name = "GMO_PG_RETRY_COUNT", value = "3" },
        { name = "SMTP_HOST", value = "email-smtp.ap-northeast-1.amazonaws.com" },
        { name = "SMTP_PORT", value = "465" },
        { name = "SMTP_SECURE", value = "true" },
        { name = "SMTP_REQUIRE_TLS", value = "false" },
        { name = "SMTP_FROM_ADDRESS", value = "<EMAIL>" },
      ],
      secrets = [
        { name = "DATABASE_URL", valueFrom = aws_ssm_parameter.database_url.arn },
        { name = "JWT_SECRET", valueFrom = data.aws_ssm_parameter.jwt_secret.arn },
        { name = "GMO_PG_SITE_ID", valueFrom = data.aws_ssm_parameter.gmo_pg_site_id.arn },
        { name = "GMO_PG_SITE_PASS", valueFrom = data.aws_ssm_parameter.gmo_pg_site_pass.arn },
        { name = "GMO_PG_SHOP_ID", valueFrom = data.aws_ssm_parameter.gmo_pg_shop_id.arn },
        { name = "GMO_PG_SHOP_PASS", valueFrom = data.aws_ssm_parameter.gmo_pg_shop_pass.arn },
        { name = "SMTP_USER", valueFrom = data.aws_ssm_parameter.ses_smtp_username.arn },
        { name = "SMTP_PASSWORD", valueFrom = data.aws_ssm_parameter.ses_smtp_password.arn },
      ]
    }
  ])

  tags = { Name = "bhc-core-api-task" }
}

# --- Task Definition: core-admin ---
resource "aws_ecs_task_definition" "core_admin" {
  family                   = "bhc-core-admin-task"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = 512
  memory                   = 1024
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn

  container_definitions = jsonencode([
    {
      name      = "bhc-core-admin-container",
      image     = "${aws_ecr_repository.core_admin.repository_url}:latest",
      cpu       = 256,
      memory    = 512,
      essential = true,
      environment = [
        { name = "CORE_API_ENDPOINT", value = "http://${aws_lb.core_api_internal.dns_name}/" }
      ],
      portMappings = [
        { containerPort = 3000, hostPort = 3000, protocol = "tcp" }
      ],
      logConfiguration = {
        logDriver = "awslogs",
        options = {
          "awslogs-group"         = "/ecs/bhc-core-admin-task",
          "awslogs-region"        = "ap-northeast-1",
          "awslogs-stream-prefix" = "ecs"
        }
      }
    }
  ])

  tags = { Name = "bhc-core-admin-task" }
}

# --- ECS Service: core-api ---
resource "aws_ecs_service" "core_api" {
  name            = "bhc-core-api-service"
  cluster         = aws_ecs_cluster.core.id
  task_definition = aws_ecs_task_definition.core_api.arn
  desired_count   = 2
  launch_type     = "FARGATE"

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }
  deployment_minimum_healthy_percent = 100
  deployment_maximum_percent         = 200


  network_configuration {
    subnets          = [aws_subnet.private_a.id, aws_subnet.private_c.id]
    security_groups  = [aws_security_group.ecs_core.id]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.core_api_internal_alb.arn
    container_name   = "bhc-core-api-container"
    container_port   = 8080
  }

  depends_on = [aws_lb_listener.core_api_internal_alb]

  tags = { Name = "bhc-core-api-service" }
}

# --- ECS Service: core-admin ---
resource "aws_ecs_service" "core_admin" {
  name            = "bhc-core-admin-service"
  cluster         = aws_ecs_cluster.core.id
  task_definition = aws_ecs_task_definition.core_admin.arn
  desired_count   = 2
  launch_type     = "FARGATE"

  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }
  deployment_minimum_healthy_percent = 100
  deployment_maximum_percent         = 200


  network_configuration {
    subnets          = [aws_subnet.private_a.id, aws_subnet.private_c.id]
    security_groups  = [aws_security_group.core_admin.id]
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.core_admin.arn
    container_name   = "bhc-core-admin-container"
    container_port   = 3000
  }

  depends_on = [aws_lb_listener.core_admin_https]

  tags = { Name = "bhc-core-admin-service" }
}

