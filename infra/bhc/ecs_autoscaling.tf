# ECS Service Autoscaling (prd/bhc) - Align with dev

# Web API
resource "aws_appautoscaling_target" "web_api_target" {
  max_capacity       = 15
  min_capacity       = 4
  resource_id        = "service/${aws_ecs_cluster.main.name}/${aws_ecs_service.web_api.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_policy" "web_api_cpu_scaling" {
  name               = "bhc-web-api-cpu-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.web_api_target.resource_id
  scalable_dimension = aws_appautoscaling_target.web_api_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.web_api_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}

resource "aws_appautoscaling_policy" "web_api_memory_scaling" {
  name               = "bhc-web-api-memory-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.web_api_target.resource_id
  scalable_dimension = aws_appautoscaling_target.web_api_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.web_api_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}


# Web Frontend
resource "aws_appautoscaling_target" "web_frontend_target" {
  max_capacity       = 15
  min_capacity       = 4
  resource_id        = "service/${aws_ecs_cluster.main.name}/${aws_ecs_service.web_frontend.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"

}

resource "aws_appautoscaling_policy" "web_frontend_cpu_scaling" {
  name               = "bhc-web-frontend-cpu-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.web_frontend_target.resource_id
  scalable_dimension = aws_appautoscaling_target.web_frontend_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.web_frontend_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}

resource "aws_appautoscaling_policy" "web_frontend_memory_scaling" {
  name               = "bhc-web-frontend-memory-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.web_frontend_target.resource_id
  scalable_dimension = aws_appautoscaling_target.web_frontend_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.web_frontend_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}


# Web Admin
resource "aws_appautoscaling_target" "web_admin_target" {
  max_capacity       = 4
  min_capacity       = 2
  resource_id        = "service/${aws_ecs_cluster.main.name}/${aws_ecs_service.web_admin.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_policy" "web_admin_cpu_scaling" {
  name               = "bhc-web-admin-cpu-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.web_admin_target.resource_id
  scalable_dimension = aws_appautoscaling_target.web_admin_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.web_admin_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}

resource "aws_appautoscaling_policy" "web_admin_memory_scaling" {
  name               = "bhc-web-admin-memory-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.web_admin_target.resource_id
  scalable_dimension = aws_appautoscaling_target.web_admin_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.web_admin_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}


# Core API
resource "aws_appautoscaling_target" "core_api_target" {
  max_capacity       = 10
  min_capacity       = 2
  resource_id        = "service/${aws_ecs_cluster.core.name}/${aws_ecs_service.core_api.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"

}

resource "aws_appautoscaling_policy" "core_api_cpu_scaling" {
  name               = "bhc-core-api-cpu-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.core_api_target.resource_id
  scalable_dimension = aws_appautoscaling_target.core_api_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.core_api_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}

resource "aws_appautoscaling_policy" "core_api_memory_scaling" {
  name               = "bhc-core-api-memory-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.core_api_target.resource_id
  scalable_dimension = aws_appautoscaling_target.core_api_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.core_api_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}


# Core Admin
resource "aws_appautoscaling_target" "core_admin_target" {
  max_capacity       = 4
  min_capacity       = 2
  resource_id        = "service/${aws_ecs_cluster.core.name}/${aws_ecs_service.core_admin.name}"
  scalable_dimension = "ecs:service:DesiredCount"

  service_namespace = "ecs"
}

resource "aws_appautoscaling_policy" "core_admin_cpu_scaling" {
  name               = "bhc-core-admin-cpu-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.core_admin_target.resource_id
  scalable_dimension = aws_appautoscaling_target.core_admin_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.core_admin_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}

resource "aws_appautoscaling_policy" "core_admin_memory_scaling" {
  name               = "bhc-core-admin-memory-scaling-policy"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.core_admin_target.resource_id
  scalable_dimension = aws_appautoscaling_target.core_admin_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.core_admin_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value       = 75.0
    scale_in_cooldown  = 60
    scale_out_cooldown = 60
  }
}


