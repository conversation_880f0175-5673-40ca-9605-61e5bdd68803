# Route 53 hosted zone and records for bhc/prd (bloominghorseclub.co.jp)

# Public Hosted Zone
resource "aws_route53_zone" "public" {
  name          = "bloominghorseclub.co.jp"
  comment       = "bhc-prd public hosted zone"
  force_destroy = false

  tags = {
    Name        = "bhc-prd-public-zone"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Apex A (ALIAS) -> Public ALB
resource "aws_route53_record" "apex" {
  zone_id = aws_route53_zone.public.zone_id
  name    = "bloominghorseclub.co.jp"
  type    = "A"

  alias {
    name                   = aws_lb.main.dns_name
    zone_id                = aws_lb.main.zone_id
    evaluate_target_health = true
  }
}

# admin.bloominghorseclub.co.jp -> Admin ALB
resource "aws_route53_record" "admin" {
  zone_id = aws_route53_zone.public.zone_id
  name    = "admin.bloominghorseclub.co.jp"
  type    = "A"

  alias {
    name                   = aws_lb.admin.dns_name
    zone_id                = aws_lb.admin.zone_id
    evaluate_target_health = true
  }
}

# --- ACM DNS validation records & validation resource ---
# Create DNS validation records in the hosted zone for the existing certificate
resource "aws_route53_record" "acm_validation" {
  for_each = {
    for dvo in aws_acm_certificate.main.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }
  allow_overwrite = true
  zone_id         = aws_route53_zone.public.zone_id
  name            = each.value.name
  type            = each.value.type
  ttl             = 300
  records         = [each.value.record]
}

resource "aws_acm_certificate_validation" "main" {
  certificate_arn         = aws_acm_certificate.main.arn
  validation_record_fqdns = [for r in aws_route53_record.acm_validation : r.fqdn]
}

# Outputs to provide NS names for registrar
output "public_zone_name_servers" {
  description = "NS records for Route 53 hosted zone (set these at the registrar)"
  value       = aws_route53_zone.public.name_servers
}



# core.bloominghorseclub.co.jp -> Core Admin ALB
resource "aws_route53_record" "core_admin" {
  zone_id = aws_route53_zone.public.zone_id
  name    = "core.bloominghorseclub.co.jp"
  type    = "A"

  alias {
    name                   = aws_lb.core_admin.dns_name
    zone_id                = aws_lb.core_admin.zone_id
    evaluate_target_health = true
  }
}


# MX record for email
resource "aws_route53_record" "mx" {
  zone_id = aws_route53_zone.public.zone_id
  name    = "bloominghorseclub.co.jp"
  type    = "MX"
  ttl     = 3600
  records = ["10 smtp.google.com."]
}
