# RDS (bhc/prd) — based on infra/dev/rds.tf

# --- RDS Credentials (from SSM Parameter Store) ---
# NOTE: 事前に以下のパラメータを作成してください。
# - /bhc/prd/rds/main/username (String)
# - /bhc/prd/rds/main/password (SecureString)

data "aws_ssm_parameter" "rds_main_username" {
  name = "/bhc/prd/rds/main/username"
}

data "aws_ssm_parameter" "rds_main_password" {
  name            = "/bhc/prd/rds/main/password"
  with_decryption = true
}

# --- RDS Instance (Main) ---
resource "aws_db_instance" "main" {
  identifier             = "bhc-rds-main"
  allocated_storage      = 100
  max_allocated_storage  = 200
  storage_type           = "gp3"
  engine                 = "postgres"
  engine_version         = "17.4"
  instance_class         = "db.t4g.medium"
  db_name                = "bhc_main_db"
  username               = data.aws_ssm_parameter.rds_main_username.value
  password               = data.aws_ssm_parameter.rds_main_password.value
  db_subnet_group_name   = aws_db_subnet_group.default.name
  vpc_security_group_ids = [aws_security_group.rds_main.id]
  parameter_group_name   = "default.postgres17"

  multi_az                     = true
  publicly_accessible          = false
  skip_final_snapshot          = true
  backup_retention_period      = 7
  deletion_protection          = true
  performance_insights_enabled = true
  apply_immediately            = false

  tags = {
    Name        = "bhc-rds-main"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# --- Compose database_url into SSM (for apps) ---
resource "aws_ssm_parameter" "database_url" {
  name  = "/bhc/prd/database/url"
  type  = "SecureString"
  value = "postgresql://${data.aws_ssm_parameter.rds_main_username.value}:${data.aws_ssm_parameter.rds_main_password.value}@${aws_db_instance.main.address}:${aws_db_instance.main.port}/${aws_db_instance.main.db_name}"
  tags = {
    Name        = "bhc-database-url"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }


}

