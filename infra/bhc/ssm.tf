# SSM parameters references for ECS (bhc/prd)

# NOTE: These parameters must exist before starting ECS services.
# Please create them in AWS Console (SSM Parameter Store) or via CLI.

data "aws_ssm_parameter" "ses_smtp_username" {
  name = "/bhc/prd/ses/smtp_username"
}

data "aws_ssm_parameter" "ses_smtp_password" {
  name            = "/bhc/prd/ses/smtp_password"
  with_decryption = true
}

data "aws_ssm_parameter" "database_url" {
  name            = "/bhc/prd/database/url"
  with_decryption = true
}

data "aws_ssm_parameter" "jwt_secret" {
  name            = "/bhc/prd/jwt/secret"
  with_decryption = true
}

data "aws_ssm_parameter" "gmo_pg_site_id" {
  name = "/bhc/prd/gmo_pg/site_id"
}

data "aws_ssm_parameter" "gmo_pg_site_pass" {
  name            = "/bhc/prd/gmo_pg/site_pass"
  with_decryption = true
}

data "aws_ssm_parameter" "gmo_pg_shop_id" {
  name = "/bhc/prd/gmo_pg/shop_id"
}

data "aws_ssm_parameter" "gmo_pg_shop_pass" {
  name            = "/bhc/prd/gmo_pg/shop_pass"
  with_decryption = true
}



# Sentry DSN (SecureString) — must be created beforehand in SSM Parameter Store
# Example path: /bhc/prd/sentry/dsn
# Value: Your Sentry DSN
# Type: SecureString
# This is a reference only; Terraform does not create the parameter here.
data "aws_ssm_parameter" "sentry_dsn" {
  name            = "/bhc/prd/sentry/dsn"
  with_decryption = true
}
