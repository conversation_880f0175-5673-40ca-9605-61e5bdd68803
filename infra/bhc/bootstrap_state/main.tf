terraform {
  required_version = ">= 1.2.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  # Start with local backend for bootstrapping remote state resources
  backend "local" {}
}

provider "aws" {
  region = var.region
}

variable "region" {
  description = "AWS region"
  type        = string
  default     = "ap-northeast-1"
}

# Identify the current account for globally-unique bucket naming
data "aws_caller_identity" "current" {}

locals {
  # S3 bucket name must be globally unique; include account id to avoid collisions
  tf_state_bucket_name = "hami-bhc-tf-${data.aws_caller_identity.current.account_id}"
  tf_lock_table_name   = "hami-bhc-tf-locks"
  common_tags = {
    Project     = "hami"
    Environment = "bhc"
    ManagedBy   = "terraform"
  }
}

# --- S3 bucket for Terraform remote state ---
resource "aws_s3_bucket" "tf_state" {
  bucket        = local.tf_state_bucket_name
  force_destroy = false
  tags          = merge(local.common_tags, { Name = local.tf_state_bucket_name })
}

resource "aws_s3_bucket_versioning" "tf_state" {
  bucket = aws_s3_bucket.tf_state.id
  versioning_configuration { status = "Enabled" }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "tf_state" {
  bucket = aws_s3_bucket.tf_state.id
  rule {
    apply_server_side_encryption_by_default { sse_algorithm = "AES256" }
  }
}

resource "aws_s3_bucket_public_access_block" "tf_state" {
  bucket = aws_s3_bucket.tf_state.id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# --- DynamoDB table for state locking ---
resource "aws_dynamodb_table" "tf_lock" {
  name         = local.tf_lock_table_name
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "LockID"

  attribute {
    name = "LockID"
    type = "S"
  }

  tags = merge(local.common_tags, { Name = local.tf_lock_table_name })
}

