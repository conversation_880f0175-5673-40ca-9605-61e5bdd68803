# ALB setup (bhc/prd) - based on infra/dev/alb.tf

# Public ALB
resource "aws_lb" "main" {
  name               = "bhc-prd-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.lb.id]
  subnets = [
    aws_subnet.public_a.id,
    aws_subnet.public_c.id
  ]

  enable_deletion_protection = true

  tags = {
    Name        = "bhc-prd-alb"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Target Group for Web Frontend
resource "aws_lb_target_group" "web_frontend" {
  name        = "bhc-prd-web-frontend-tg"
  port        = 3000
  protocol    = "HTTP"
  vpc_id      = aws_vpc.main.id
  target_type = "ip"

  health_check {
    protocol            = "HTTP"
    path                = "/health"
    matcher             = "200-299"
    healthy_threshold   = 2
    unhealthy_threshold = 2
    interval            = 30
    timeout             = 10
  }

  tags = {
    Name        = "bhc-prd-web-frontend-tg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# HTTP Listener (Main ALB) - Redirect to HTTPS
resource "aws_lb_listener" "http" {
  load_balancer_arn = aws_lb.main.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      protocol    = "HTTPS"
      port        = "443"
      status_code = "HTTP_301"
    }
  }
}

# HTTPS Listener (Main ALB)
resource "aws_lb_listener" "https" {
  load_balancer_arn = aws_lb.main.arn
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = aws_acm_certificate.main.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.web_frontend.arn
  }

}

# --- Admin Load Balancer (Public) ---
resource "aws_lb" "admin" {
  name               = "bhc-admin-alb"
  internal           = false
  load_balancer_type = "application"
  subnets            = [aws_subnet.public_a.id, aws_subnet.public_c.id]
  security_groups    = [aws_security_group.lb.id]

  enable_deletion_protection = true

  tags = {
    Name        = "bhc-admin-alb"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# Target Group for Web Admin
resource "aws_lb_target_group" "web_admin" {
  name        = "bhc-web-admin-tg"
  port        = 3000
  protocol    = "HTTP"
  vpc_id      = aws_vpc.main.id
  target_type = "ip"

  health_check {
    protocol            = "HTTP"
    path                = "/login"
    matcher             = "200-299"
    interval            = 30
    timeout             = 5
    healthy_threshold   = 2
    unhealthy_threshold = 2
  }

  tags = {
    Name        = "bhc-web-admin-tg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

# HTTP Listener (Admin) - Redirect to HTTPS
resource "aws_lb_listener" "admin_http" {
  load_balancer_arn = aws_lb.admin.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      protocol    = "HTTPS"
      port        = "443"
      status_code = "HTTP_301"
    }
  }
}

# HTTPS Listener (Admin)
resource "aws_lb_listener" "admin_https" {
  load_balancer_arn = aws_lb.admin.arn
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = aws_acm_certificate.main.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.web_admin.arn
  }

  depends_on = [aws_acm_certificate_validation.main]
}

# --- Internal API (private ALB) ---
resource "aws_lb" "internal_api" {
  name               = "bhc-internal-api-alb"
  internal           = true
  load_balancer_type = "application"
  security_groups    = [aws_security_group.internal_lb.id]
  subnets            = [aws_subnet.private_a.id, aws_subnet.private_c.id]

  tags = {
    Name        = "bhc-internal-api-alb"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_lb_target_group" "internal_api" {
  name        = "bhc-internal-api-tg"
  port        = 8080
  protocol    = "HTTP"
  vpc_id      = aws_vpc.main.id
  target_type = "ip"

  health_check {
    enabled             = true
    healthy_threshold   = 5
    unhealthy_threshold = 2
    protocol            = "HTTP"
    path                = "/"
  }

  tags = {
    Name        = "bhc-internal-api-tg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_lb_listener" "internal_api" {
  load_balancer_arn = aws_lb.internal_api.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.internal_api.arn
  }

  depends_on = [aws_lb_target_group.internal_api]
}

# --- Core Admin Load Balancer (Public) ---
resource "aws_lb" "core_admin" {
  name               = "bhc-core-admin-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.core_alb.id]
  subnets            = [aws_subnet.public_a.id, aws_subnet.public_c.id]

  enable_deletion_protection = true

  tags = {
    Name        = "bhc-core-admin-alb"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_lb_target_group" "core_admin" {
  name        = "bhc-core-admin-tg"
  port        = 3000
  protocol    = "HTTP"
  vpc_id      = aws_vpc.main.id
  target_type = "ip"

  health_check {
    path                = "/login"
    protocol            = "HTTP"
    matcher             = "200-299"
    interval            = 30
    timeout             = 5
    healthy_threshold   = 2
    unhealthy_threshold = 2
  }

  tags = {
    Name        = "bhc-core-admin-tg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_lb_listener" "core_admin_http" {
  load_balancer_arn = aws_lb.core_admin.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      protocol    = "HTTPS"
      port        = "443"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "core_admin_https" {
  load_balancer_arn = aws_lb.core_admin.arn
  port              = 443
  protocol          = "HTTPS"
  certificate_arn   = aws_acm_certificate.main.arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.core_admin.arn
  }
}

# --- Core API Internal ALB ---
resource "aws_lb" "core_api_internal" {
  name               = "bhc-core-api-internal-alb"
  internal           = true
  load_balancer_type = "application"
  security_groups    = [aws_security_group.internal_lb.id]
  subnets            = [aws_subnet.private_a.id, aws_subnet.private_c.id]

  tags = {
    Name        = "bhc-core-api-internal-alb"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_lb_target_group" "core_api_internal_alb" {
  name        = "bhc-core-api-internal-alb-tg"
  port        = 8080
  protocol    = "HTTP"
  vpc_id      = aws_vpc.main.id
  target_type = "ip"

  health_check {
    enabled             = true
    interval            = 30
    path                = "/"
    protocol            = "HTTP"
    timeout             = 5
    healthy_threshold   = 2
    unhealthy_threshold = 2
    matcher             = "200"
  }

  tags = {
    Name        = "bhc-core-api-internal-alb-tg"
    Project     = "bhc"
    Environment = "prd"
    ManagedBy   = "terraform"
  }
}

resource "aws_lb_listener" "core_api_internal_alb" {
  load_balancer_arn = aws_lb.core_api_internal.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.core_api_internal_alb.arn
  }
}


# Outputs
output "alb_dns_name" {
  value = aws_lb.main.dns_name
}
output "alb_zone_id" {
  value = aws_lb.main.zone_id
}
output "web_frontend_tg_arn" {
  value = aws_lb_target_group.web_frontend.arn
}

