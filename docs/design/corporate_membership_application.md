# 法人入会申し込み設計書

## 概要

個人・法人オプション追加と実質的支配者申告フォームの設計

## データ構造

### 申込者タイプ

```typescript
enum ApplicantType {
  INDIVIDUAL = 'INDIVIDUAL',  // 個人
  CORPORATE = 'CORPORATE'     // 法人
}
```

### 個人申し込みリクエスト

```typescript
interface IndividualApplicationRequest {
  applicantType: 'INDIVIDUAL';
  token: string;
  
  // 個人情報
  firstName: string;
  lastName: string;
  firstNameKana: string;
  lastNameKana: string;
  birthYear: number;
  birthMonth: number;
  birthDay: number;
  
  // 共通情報
  postalCode: string;
  prefecture: string;
  address: string;
  apartment?: string;
  phoneNumber: string;
}
```

### 法人申し込みリクエスト

```typescript
interface CorporateApplicationRequest {
  applicantType: 'CORPORATE';
  token: string;
  
  // 法人情報
  corporateName: string;
  corporateNameKana: string;
  representativeName: string;
  representativeNameKana: string;
  representativePosition: string;
  corporateNumber: string; // 13桁の法人番号
  establishedYear: number;
  establishedMonth: number;
  establishedDay: number;
  
  // 共通情報
  postalCode: string;
  prefecture: string;
  address: string;
  apartment?: string;
  phoneNumber: string;
  
  // 実質的支配者申告
  beneficialOwnerDeclarations: BeneficialOwnerDeclarationRequest[];
}
```

### 実質的支配者申告

```typescript
interface BeneficialOwnerDeclarationRequest {
  // 実質的支配者情報
  beneficialOwnerName: string;
  beneficialOwnerBirthYear: number;
  beneficialOwnerBirthMonth: number;
  beneficialOwnerBirthDay: number;
  beneficialOwnerPostalCode: string;
  beneficialOwnerPrefecture: string;
  beneficialOwnerAddress: string;
  beneficialOwnerApartment?: string;
  
  // 申告者情報
  declarantName: string;
  declarantPosition: string;
  
  // 確認
  isConfirmed: boolean; // 必須：true
}
```

## バリデーション

### 個人申し込み

```typescript
const individualSchema = z.object({
  applicantType: z.literal('INDIVIDUAL'),
  token: z.string().min(1),
  firstName: z.string().min(1, '名は必須です'),
  lastName: z.string().min(1, '姓は必須です'),
  firstNameKana: z.string().min(1, '名（フリガナ）は必須です'),
  lastNameKana: z.string().min(1, '姓（フリガナ）は必須です'),
  birthYear: z.number().int().min(1900).max(new Date().getFullYear()),
  birthMonth: z.number().int().min(1).max(12),
  birthDay: z.number().int().min(1).max(31),
  postalCode: z.string().regex(/^\d{3}-\d{4}$/, '郵便番号の形式が正しくありません'),
  prefecture: z.string().min(1, '都道府県は必須です'),
  address: z.string().min(1, '住所は必須です'),
  apartment: z.string().optional(),
  phoneNumber: z.string().min(1, '電話番号は必須です'),
});
```

### 法人申し込み

```typescript
const corporateSchema = z.object({
  applicantType: z.literal('CORPORATE'),
  token: z.string().min(1),
  corporateName: z.string().min(1, '法人名は必須です'),
  corporateNameKana: z.string().min(1, '法人名（フリガナ）は必須です'),
  representativeName: z.string().min(1, '代表者名は必須です'),
  representativeNameKana: z.string().min(1, '代表者名（フリガナ）は必須です'),
  representativePosition: z.string().min(1, '代表者役職は必須です'),
  corporateNumber: z.string().regex(/^\d{13}$/, '法人番号は13桁で入力してください'),
  establishedYear: z.number().int().min(1800).max(new Date().getFullYear()),
  establishedMonth: z.number().int().min(1).max(12),
  establishedDay: z.number().int().min(1).max(31),
  postalCode: z.string().regex(/^\d{3}-\d{4}$/, '郵便番号の形式が正しくありません'),
  prefecture: z.string().min(1, '都道府県は必須です'),
  address: z.string().min(1, '住所は必須です'),
  apartment: z.string().optional(),
  phoneNumber: z.string().min(1, '電話番号は必須です'),
  beneficialOwnerDeclarations: z.array(beneficialOwnerDeclarationSchema).min(1, '実質的支配者の申告は必須です'),
});
```

### 実質的支配者申告

```typescript
const beneficialOwnerDeclarationSchema = z.object({
  beneficialOwnerName: z.string().min(1, '実質的支配者氏名は必須です'),
  beneficialOwnerBirthYear: z.number().int().min(1900).max(new Date().getFullYear()),
  beneficialOwnerBirthMonth: z.number().int().min(1).max(12),
  beneficialOwnerBirthDay: z.number().int().min(1).max(31),
  beneficialOwnerPostalCode: z.string().regex(/^\d{3}-\d{4}$/, '郵便番号の形式が正しくありません'),
  beneficialOwnerPrefecture: z.string().min(1, '都道府県は必須です'),
  beneficialOwnerAddress: z.string().min(1, '住所は必須です'),
  beneficialOwnerApartment: z.string().optional(),
  declarantName: z.string().min(1, '申告者氏名は必須です'),
  declarantPosition: z.string().min(1, '申告者役職は必須です'),
  isConfirmed: z.literal(true, { errorMap: () => ({ message: '申告内容の確認が必要です' }) }),
});
```

## フロントエンド設計

### 画面フロー

1. **申込者タイプ選択画面** (`/join/application/type`)
   - 個人 / 法人 の選択

2. **個人申し込みフォーム** (`/join/application/individual`)
   - 既存フォームを流用

3. **法人申し込みフォーム** (`/join/application/corporate`)
   - 法人基本情報入力
   - 実質的支配者申告フォーム

4. **確認画面** (`/join/application/confirm`)
   - 申込者タイプに応じた表示内容

### 実質的支配者申告フォーム要件

- 複数の実質的支配者を登録可能
- 各実質的支配者について以下を入力：
  - 氏名、生年月日、住所
- 申告者情報（代表者等）：
  - 氏名、役職
- 最終確認：
  - 「私は当該法人の代表者等であり、入力した内容に相違ないことを確認します。」チェックボックス
  - チェック後でなければ送信不可

## 実装順序

1. データベーススキーマ変更（マイグレーション）
2. API層の変更（バリデーション、リポジトリ）
3. フロントエンド変更（申込者タイプ選択、法人フォーム）
4. 実質的支配者申告フォーム実装
5. テスト追加
6. 管理画面対応

## 考慮事項

- 既存の個人申し込みデータは `INDIVIDUAL` として扱う
- 法人番号の妥当性チェック（チェックデジット検証）
- 実質的支配者は複数登録可能（通常1-3名程度）
- 本人確認書類：法人の場合は登記簿謄本等の追加書類が必要
- 会員テーブル（Member）も同様の変更が必要
