## 募集馬詳細（Offered Horse Detail）ドキュメント

### 概要

募集馬詳細は、一般公開中の募集馬の詳細情報を表示するフロントエンド機能です。URL の募集年・募集番号をキーに Web API からデータを取得し、画像・プロフィール・血統情報・募集条件（価格/口数/ステータス）等を表示します。公開条件は「プロフィールの publishStatus が PUBLISHED」であることです。

---

### 画面/ルーティング

- パス: `/offered_horses/[recruitmentYear]/[recruitmentNo]`
- 実装: `apps/web/frontend/app/(home)/offered_horses/[recruitmentYear]/[recruitmentNo]/page.tsx`
- 404: 該当データが無い/非公開の場合 `notFound()` を返します
- 参考（別機能）: 会員向けの保有馬詳細は `/horses/[recruitmentYear]/[recruitmentNo]` に実装されています

---

### データフロー

1. URL パラメータ（`recruitmentYear`, `recruitmentNo`）を取得
2. フロントエンドの API クライアントから `GetRecruitmentHorse` RPC を呼び出し
3. Web API ハンドラがビジネス層→リポジトリ→DB を経由して情報を取得
4. 画像 URL はサーバ側で署名 URL（Signed URL）に変換
5. スキーマ `RecruitmentHorseDetail` へマッピングしてレスポンス
6. フロントでローディング/エラー/404 を制御して描画

---

### API（Web API）

- ハンドラ: `apps/web/api/src/routes/frontend/recruitment_horse/get_recruitment_horse.ts`
- リポジトリ: `apps/web/api/src/repositories/recruitment_horse_repository.ts`
- スキーマ: `packages/web-api-schema/gen/recruitment_horse_service_pb.ts`

主な I/O:

- Request: `{ recruitmentYear: number; recruitmentNo: number }`
- Response: `{ horse?: RecruitmentHorseDetail }`

RecruitmentHorseDetail（主要フィールド）

- `basicInfo: RecruitmentHorseItem`（一覧アイテム相当 + 詳細表示に必要な基本情報）
  - 例: `imageUrls: string[]`, `horseName`, `horseNameEn`, `gender`, `coatColor`, `birthYear/Month/Day`,
    `sireName`, `damName`, `broodmareSireName`, `breederName`, `affiliation`, `stableName`, `trainingFarm`,
    `amountTotal`, `sharesTotal`, `recruitmentStatus`, `recruitmentYear`, `recruitmentNo`, `recruitmentName` など
- `nameOrigin: string`（馬名の由来）
- `comment: string`（募集コメント）
- `noteContent: string`（備考）

エラーハンドリング（ハンドラ）

- バリデーションエラー: `Code.InvalidArgument`
- 該当データなし: `Code.NotFound`
- DB/内部エラー: `Code.Internal`

公開条件（リポジトリ）

- `profile.publishStatus = PUBLISHED` の馬のみ返却
- `deletedAt = null` を条件に含む
- 画像は `HorseImage` テーブルから取得

---

### フロントエンド構成

エントリ: `apps/web/frontend/app/(home)/offered_horses/[recruitmentYear]/[recruitmentNo]/page.tsx`

主な子コンポーネント:

- `HorseImageSection`: 画像ギャラリー（サムネイル/拡大表示）
- `HorseDetailsSection`: 概要（父・母・母父、生産、厩舎/所属）、血統表/ブラックタイプダウンロード UI、馬名の由来、コメント
- `HorseSidebar`: 募集条件（価格・募集口数・販売状況）、募集中バッジ、申込ボタン

UI 表示のポイント:

- 一口出資額 = `amountTotal / sharesTotal / 10,000`（万円、`Math.round` で丸め）
- 募集状態が `ACTIVE` のときのみ「募集中」バッジと申込ボタンを表示
- 性別/毛色は共通フォーマッタで日本語表記に変換
- 生年月日表示: `M月D日生`

### 画面表示内容

#### ページ構成

- パンくずリスト: 「募集馬 / {馬名}」
- 馬の画像ギャラリー
- 馬の基本情報・募集条件（サイドバー）
- 詳細情報（血統・馬名の由来・コメント）

#### 馬の画像

- メイン画像: 登録された馬の写真を表示
- サムネイル: 複数画像がある場合、下部にサムネイル一覧を表示
- 拡大表示: 画像クリックで拡大モーダルを表示、左右ボタンで画像切り替え可能
- 画像未登録時: 「No Image」プレースホルダを表示

#### 基本情報・募集条件（サイドバー）

- 馬名: 大きく表示
- 募集ステータス: 募集中の場合「募集中」バッジを表示
- 基本属性:
  - 性別（牡/牝/セ）
  - 毛色
  - 生年月日（月日のみ）
- 募集条件:
  - 一口出資額（万円単位）
  - 募集口数
  - 募集総額（万円単位）
- 出資申込ボタン: 募集中の場合のみ表示、クリックで出資申込画面へ遷移

#### 詳細情報

- 血統情報:
  - 父馬名
  - 母馬名
  - 母父馬名
  - 生産者名
  - 厩舎・所属情報
- 血統表・ブラックタイプ: ダウンロードボタン（将来実装予定）
- 馬名の由来: 登録されている場合のみ表示
  - 馬名（日本語）
  - 英名
  - 由来の説明
- コメント: 募集に関するコメントが登録されている場合のみ表示

#### 表示ルール

- 金額: 万円単位で表示、3桁区切りあり
- 未登録項目: 「-」で表示
- 厩舎情報: 所属と厩舎名を「・」で連結（例：「美浦・○○厩舎」）

---

### セキュリティ/公開条件

- 認証無しで参照可能（公開情報）
- 公開条件はリポジトリで強制（`PublishStatus.PUBLISHED`）
- 画像 URL はサーバ側で署名付き URL に変換して返却（直リンク防止）

---

### テスト

- リポジトリテスト: `apps/web/api/src/repositories/recruitment_horse_repository.test.ts`
  - 公開中の募集馬詳細が取得できることを検証
- 実行方法: プロジェクトルートで `make test`

---

### スキーマ変更・機能拡張の手順

1. スキーマ定義（`packages/web-api-schema`）を変更
2. `make routes` で RPC コード生成
3. `make install` を実行
4. リポジトリ/ハンドラで取得・マッピングを更新
5. フロントの型/UI を更新
6. リポジトリ・ハンドラ・フロントのテストを追加/更新し `make test`

注意:

- 画像など追加データが必要な場合、署名 URL 生成のビジネス処理を拡張
- API クライアント生成スクリプトは通常 `withAuthErrorHandling` でラップされますが、本機能は公開 API として利用されます

---

### よくある質問/注意点

- 404 になる
  - `recruitmentYear/recruitmentNo` が存在しない、または `publishStatus ≠ PUBLISHED`
- 画像が表示されない
  - 署名 URL 生成の失敗、または画像未登録の可能性
- 価格表示の丸め
  - 一口出資額は万円単位で四捨五入（`Math.round`）。要件変更は `HorseSidebar` の計算ロジックを更新

---

### 関連ファイル一覧（主要）

- API ハンドラ: `apps/web/api/src/routes/frontend/recruitment_horse/get_recruitment_horse.ts`
- リポジトリ: `apps/web/api/src/repositories/recruitment_horse_repository.ts`
- スキーマ型: `packages/web-api-schema/gen/recruitment_horse_service_pb.ts`
- フロントページ: `apps/web/frontend/app/(home)/offered_horses/[recruitmentYear]/[recruitmentNo]/page.tsx`
- フロント構成:
  - `components/HorseImageSection.tsx`
  - `components/HorseDetailsSection.tsx`
  - `components/HorseSidebar.tsx`
- テスト: `apps/web/api/src/repositories/recruitment_horse_repository.test.ts`
