# 法人向け本人確認書類アップロード仕様（ドラフト）

最終更新: 2025-09-08

## 目的
- 申込主体が「法人」の場合、本人確認として以下のいずれか1点の提出を必須化する。
  - 印鑑証明書
  - 履歴事項全部証明書（現在事項証明書を含む）

## 対象範囲
- 会員側フロント: 入会申込フローの本人確認（KYC）ステップ
- サーバー/API: 本人確認書類登録、DocumentType/FileType 拡張、完了条件検証
- 管理画面: 審査UIへの表示・プレビュー（PDF対応）

## 用語/前提
- applicantType: 申込主体区分。`INDIVIDUAL`（個人）/ `CORPORATE`（法人）を持つ（新規追加）。
- 法人必須書類: 上記2種のいずれか1点。
- 既存仕様:
  - 個人: `IDENTITY_FRONT` / `IDENTITY_BACK` の2点（両方必須）
  - S3アップロードは署名URL方式を利用
  - DocumentGroup は `identity_verification` を使用

## UI仕様（会員側）
- 申込主体選択: 「個人 / 法人」を必須選択（初期値は個人）。
- 個人 UI: 従来通り「本人確認書（表/裏）」の2ファイル必須。
- 法人 UI:
  - セクション: 「会社の本人確認書類（どちらか1点）」必須
  - 選択肢（任意表示）: 「印鑑証明書」/「履歴事項（全部/現在）証明書」
  - ファイルアップロード: 1ファイル（PDF/JPG/PNG、最大10MB）
- 送信ボタン: 必須条件を満たすまで非活性。エラーは項目直下に表示。
- 注意文: 有効期限/鮮明さ/改ざん不可/対応形式・容量を明示。

## ファイル仕様
- 形式: PDF、JPG、PNG
- 容量: 最大 10MB
- S3: 署名付きPUT URLで直接アップロード、サーバー側で `HeadObject` による形式/サイズの緩検証。

## データモデル/サーバー変更
- Prisma: `membershipApplication.applicantType` を追加（ENUM: `INDIVIDUAL` | `CORPORATE`、既存は既定 `INDIVIDUAL`）。
- FileType/DocumentType 拡張:
  - 追加: `CORP_SEAL_CERT`（印鑑証明書）、`CORP_REGISTRY_CERT`（履歴事項証明書）
  - `get_membership_application` の `FileType -> DocumentType` マッピングに上記を追加。
- 書類登録: 既存 `createMembershipApplicationIdentityDocument(...)` を流用。`fileType` に新種別を指定して保存。
- 完了条件（DocumentGroup 完了）:
  - 個人: `IDENTITY_FRONT` と `IDENTITY_BACK` の2点必須。
  - 法人: `CORP_SEAL_CERT` または `CORP_REGISTRY_CERT` のいずれか1点以上で可。
  - 実装: `updateDocumentGroupCompletion` 前後の検証、もしくはUseCase層での条件判定を追加。
- PDF閲覧: 署名付きDL URLをそのままIFrame/別タブで表示（画像専用UIには「PDFを開く」ボタンを追加）。

## 管理画面（審査UI）
- 書類リストのラベル:
  - 個人: 「本人確認書 表面/裏面」
  - 法人: 「印鑑証明書」/「履歴事項（全部/現在）証明書」
- PDF: 「プレビュー/ダウンロード」導線を追加。画像とPDFで表示分岐。
- 差し戻しテンプレート例（任意）: 有効期限切れ/不鮮明/内容不一致 などの定型文。

## API変更（概要）
- 会員側（新規/拡張）
  - 署名付きアップロードURL取得（既存ヘルパー利用）。key 例: `identity/{applicationId}/{uuid}.pdf`
  - 書類登録API: `fileKey` と `fileType`（新種別対応）を受け取り保存。
- 管理側（既存拡張）
  - `get_membership_application`: 新 `DocumentType` を返却。
  - 審査APIは基本互換。コメント運用で法人不備にも対応。

## 移行/互換性
- 既存申込は `applicantType = INDIVIDUAL` で移行。
- 既存のDocumentGroup/ログ/審査フローへの影響はなし。

## 監査/セキュリティ
- 申込主体区分の変更・書類登録は既存の監査基盤（作成者/時刻/キー）で記録。
- 署名URLの期限は既存デフォルト（アップロード 5分/ダウンロード 1時間）。

## i18n/アクセシビリティ
- ラベル/エラーは日本語で簡潔に。PDFボタンに代替テキストを付与。

## テスト観点
- モデル: applicantType 既定値/保存、FileType拡張の保存・取得。
- 完了条件: 個人=2点必須、法人=1点で完了。
- API: `get_membership_application` が新 `DocumentType` を含む。`getIdentityDocumentImageUrl` がPDFにも対応。
- UI: applicantType 切替、必須制御、PDFプレビュー導線、エラー表示。

## 影響ファイル（目安）
- サーバー/モデル
  - Prisma スキーマ: `packages/prisma/**`
  - 変換/取得: `apps/core/api/src/routes/admin/membership_application/get_membership_application.ts`
  - リポジトリ: `apps/core/api/src/repositories/membership_application_repository.ts`
  - スキーマ生成物: `packages/*-api-schema/gen/**`
- 管理UI
  - 一覧/詳細・画像表示: `apps/core/admin/app/(authed)/membership-application/[id]/**`
- 会員側フロント
  - 申込フォーム（applicantType追加）/KYC画面（必須制御・アップロード）

## 未決事項（任意）
- 法人書類の有効期限や発行日チェックの自動化要否（OCR/外部連携）。
- 追加の法人書類（登記事項証明の種類区別、取締役の本人確認など）の拡張計画。

