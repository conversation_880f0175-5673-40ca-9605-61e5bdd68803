## 所属馬一覧（Owned Horse List）ドキュメント

### 概要
所属馬一覧は、会員が自分のクラブに所属する馬の一覧を確認する画面です。馬名から詳細ページへ遷移でき、性別・年齢・所属・厩舎・父/母などの基本情報を一覧で把握できます。

---

### 対象/前提
- 対象利用者: 会員
- 前提: 認証が必要です。未認証時はログインに誘導される運用です。

---

### ルーティング
- 所属馬一覧: `/horses`
- 所属馬詳細: `/horses/[recruitmentYear]/[recruitmentNo]`

---

### 画面表示内容

#### 一覧テーブルの項目
- 馬名（リンク）: 詳細ページへ遷移できるリンク。馬名が未登録時は募集名を表示
- 性（牡/牝 等）
- 齢（年齢）
- 所属（美浦・栗東等）
- 厩舎名
- 父馬名
- 母馬名

#### 操作
- 馬名クリックで当該馬の詳細ページへ遷移

#### 表示ルール
- 空欄となる項目（例: 所属・厩舎など）は未登録時は空白のまま
- 性別はコード値を「牡/牝」等の表記に変換

---

### 検索/絞り込み（将来的な拡張含む）
- 検索: 馬名での検索（オプション）
- 生年フィルタ: 生年での絞り込み（オプション）

備考:
- 現状のUIでは検索/フィルタのコントロール表示は省略状態ですが、API側は `search`（馬名）と `birthYear`（生年）をサポートしています。

---

### API とスキーマ（ビジネス理解向けの最小）
- RPC: ListOwnedHorses
- リクエスト（任意）:
  - `search`: 馬名の部分一致検索
  - `birthYear`: 生年による絞り込み
- レスポンス（OwnedHorseListItem の配列）主要項目:
  - 馬ID、募集年、募集番号、募集名、馬名
  - 性別、年齢
  - 所属、厩舎名
  - 父馬名、母馬名

---

### 関連/補足
- 所属馬詳細（docs/owned_horse_detail.md）と併せて利用する想定です。
- 公開向けの募集馬一覧/詳細とは対象と情報が異なります（会員向け）。

