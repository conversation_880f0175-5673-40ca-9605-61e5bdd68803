## 所属馬詳細（Owned Horse Detail）ドキュメント

### 概要
所属馬詳細は、会員が自分のクラブに所属する馬の詳細情報と最新の近況を閲覧するための画面です。募集年・募集番号をキーに所属馬の基本情報を表示し、直近2件の近況を併せて確認できます。全件の近況は専用ページから参照できます。

---

### 対象/前提
- 対象利用者: 会員
- 前提: 認証が必要です。未認証時はログインに誘導される運用です。

---

### ルーティング
- 所属馬詳細: `/horses/[recruitmentYear]/[recruitmentNo]`
- 近況一覧（全件）: `/horses/[recruitmentYear]/[recruitmentNo]/reports`

---

### 画面表示内容

#### ページ構成
- 馬の画像（代表画像）
- 基本情報（馬名・性別・毛色・生年月日 ほか）
- 血統・生産/厩舎/育成などのプロフィール
- 募集情報（募集総額・募集口数・一口出資額）
- 近況（最新2件）と「近況を全部見る」リンク

#### 馬の画像
- 代表画像を表示します（登録がある場合）。

#### 基本情報
- 馬名（日本語名）
- 性別（牡/牝 など）
- 毛色
- 生年月日（年-月-日）
- クラス（該当する場合）

#### プロフィール（血統・生産・厩舎等）
- 父馬名 / 母馬名 / 母父馬名
- 生産地 / 生産者
- 所属（美浦・栗東等） / 厩舎名
- 育成牧場
- 血統表・ブラックタイプのリンク（将来の連携/提供を想定）
- 馬名の由来（登録がある場合）
  - 馬名（日本語）
  - 英名
  - 由来の説明

#### 募集情報（参考表示）
- 募集総額（円）
- 募集口数（口）
- 一口出資額（円）

#### 近況
- 最新の近況を2件まで表示します。
  - 日付（年-月-日）
  - 場所（例: 美浦・○○厩舎 等）
  - 近況本文
  - 近況画像（複数ある場合はサムネイル的に並列表示）
- 「近況を全部見る」リンクから、当該馬の全件近況一覧ページへ移動できます。

#### 表示ルール
- 金額は円単位で表示（3桁区切り）
- 一口出資額は「募集総額 ÷ 募集口数」の目安額を表示
- 未登録項目は空欄または「-」など適切に非表示相当の扱い
- 画像や近況が未登録の場合は、その旨のメッセージや画像無しの状態を表示

---

### 近況一覧ページ（概要）
- パス: `/horses/[recruitmentYear]/[recruitmentNo]/reports`
- 内容:
  - 当該馬の近況を時系列で一覧表示
  - 各近況の「日付」「場所」「本文」「画像」
  - 所属馬詳細から遷移可能

---

### API とスキーマ（ビジネス理解向けの最小）
- RPC:
  - 所属馬詳細取得: GetOwnedHorse
  - 近況一覧取得: ListHorseReports
- リクエストキー: `recruitmentYear`, `recruitmentNo`
- 代表的なレスポンス項目（OwnedHorseDetail）
  - 基本情報: 馬ID、募集年/募集番号、募集名、馬名、英名、馬名の由来
  - 生年月日: 年・月・日
  - 特徴: 性別、毛色、クラス
  - 血統: 父、母、母の父
  - 生産/育成: 生産地、生産者、育成牧場
  - 厩舎: 厩舎名、所属（美浦・栗東等）
  - 募集情報: 募集口数、募集総額
  - 画像: 馬画像URLの配列

備考:
- 近況（HorseReport）は、日付、場所、本文、画像URLを含みます。
- 認証がない場合は所属馬詳細の取得ができず、また近況取得も行われません（表示できる範囲が制限されます）。

---

### 関連/補足
- 募集馬詳細（公開情報）とは異なり、本画面は会員向けで近況表示を前提としています。
- 募集馬詳細では募集状態や申込ボタンが表示されますが、所属馬詳細では出資申込ボタンは表示しません。

