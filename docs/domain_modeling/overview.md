# 全体ドメインモデル（叩き台）

以下は現状の proto スキーマおよび docs/domain_modeling の各ドキュメントから集約した、粗めの全体ドメインモデル図（Mermaid ER）です。目的は「俯瞰できる叩き台」を共有することです。詳細属性や列挙値は簡略化しています。

```mermaid
erDiagram
  %% 会員・管理者
  AdminUser {
    string admin_user_id PK
    string email
    string name
  }

  %% 一般ユーザー
  User {
    int user_id PK
    string email
    bool must_change_password
  }

  UserSession {
    string user_session_id PK
    int user_id FK, UK
    string session_token UK
    string session_type "NORMAL/PASSWORD_CHANGE_REQUIRED"
    datetime expires_at
  }


  Member {
    int member_id PK
    int member_number
    string email
    string name
    int membership_application_id FK
    datetime approved_at
  }

  %% 入会申込・本人確認
  MembershipApplication {
    int membership_application_id PK
    string status         "NOT_REVIEWED/APPROVE/REJECT/REMAND"
    datetime created_at
    datetime updated_at
  }

  DocumentGroup {
    int document_group_id PK
    string upload_token
    string group_key
    bool is_completed
    datetime created_at
  }

  IdentityDocument {
    int identity_document_id PK
    string file_key
    string document_type  "IDENTITY_FRONT/BACK/SELFIE"
    datetime uploaded_at
  }

  ReviewLog {
    int review_log_id PK
    int membership_application_id FK
    string review_type    "APPROVE/REJECT/REMAND"
    string reviewer_admin_user_id FK
    string note
    datetime created_at
  }

  %% お知らせ
  Announcement {
    int announcement_id PK
    string title
    string status       "DRAFT/PUBLISHED/STOPPED"
    bool is_headline
    datetime published_at
    datetime created_at
  }

  AnnouncementAttachment {
    int announcement_attachment_id PK
    int announcement_id FK
    string file_path
    string mime_type
    int file_size
    int display_order
  }

  %% お問い合わせ
  ContactInquiry {
    int contact_inquiry_id PK
    int member_id FK "任意"
    string status     "NEW/IN_PROGRESS/RESOLVED/CLOSED"
    string priority   "LOW/MEDIUM/HIGH/URGENT"
    datetime submitted_at
  }

  ContactAttachment {
    int contact_attachment_id PK
    int contact_inquiry_id FK
    string file_path
    string mime_type
    int file_size
  }

  %% 会員向けメッセージ
  Message {
    int message_id PK
    string status     "DRAFT/SENDING/SENT/FAILED"
    datetime created_at
  }

  MessageAttachment {
    int message_attachment_id PK
    int message_id FK
    string file_path
    string mime_type
    int display_order
  }

  MessageRecipient {
    int message_recipient_id PK
    int message_id FK
    int member_id FK
    string delivery_status "PENDING/DELIVERED/READ/FAILED"
    datetime delivered_at
    datetime read_at
  }

  %% 馬・レポート・賞金
  Horse {
    int horse_id PK
    string name
  }

  HorseReport {
    int horse_report_id PK
    int horse_id FK
    int report_year
    int report_month
    int report_day
    string status    "DRAFT/PUBLISHED"
    datetime created_at
  }

  HorseReportMedia {
    int horse_report_media_id PK
    int horse_report_id FK
    string file_key
    string media_type "IMAGE/VIDEO"
    int display_order
  }

  HorseIncomePrize {
    int horse_income_prize_id PK
    int horse_id FK
    int income_year_month
    string race_place
    string race_name
    string race_result
    string organizer  "JRA/OTHER"
    int total_prize_amount
    int income_amount
    bool closing
    datetime created_at
  }

  %% 出資（Member ↔ Horse の関係を仲介）
  InvestmentApplication {
    int investment_application_id PK
    int member_id FK
    int horse_id FK
    int requested_number
    int allocated_number "nullable"
    bool rejected
    datetime applied_at
    datetime created_at
  }

  InvestmentContract {
    int investment_contract_id PK
    int member_id FK
    int horse_id FK
    int shares_number
    int investment_amount
    datetime contracted_at "nullable"
    datetime contract_end_at "nullable"
    string contract_status "STARTED/CONTRACTING/PENDING/COMPLETED"
    datetime created_at
  }

  HorseInvestmentShares {
    int horse_id PK, FK
    int member_investment_shares
    datetime created_at
    datetime updated_at
  }

  %% リレーション
  Member ||--|| MembershipApplication : "applies_for / created_from"
  MembershipApplication ||--o{ DocumentGroup : "has"
  DocumentGroup ||--o{ IdentityDocument : "contains"
  AdminUser ||--o{ ReviewLog : "writes"
  MembershipApplication ||--o{ ReviewLog : "has"

  Announcement ||--o{ AnnouncementAttachment : "has"

  Member ||--o{ ContactInquiry : "submits"
  ContactInquiry ||--o{ ContactAttachment : "has"


  %% ユーザー関連リレーション
  User ||--o| UserSession : "1対0..1"
  User ||--o| Member : "1対0..1"

  Message ||--o{ MessageAttachment : "has"
  Message ||--o{ MessageRecipient : "to"
  Member ||--o{ MessageRecipient : "receives"

  Horse ||--o{ HorseReport : "has"
  HorseReport ||--o{ HorseReportMedia : "includes"
  Horse ||--o{ HorseIncomePrize : "earns"

  %% 出資関連（Member ↔ Horse 多対多）
  Member ||--o{ InvestmentApplication : "applies"
  Horse  ||--o{ InvestmentApplication : "receives"

  Member ||--o{ InvestmentContract : "holds"
  Horse  ||--o{ InvestmentContract : "is_invested"

  Horse  ||--o| HorseInvestmentShares : "aggregates"
```

## 注記

- 叩き台として主要エンティティと関連のみを記載しています。属性・列挙は必要最小限です。
- Horse と Member の関係は出資系（InvestmentApplication/InvestmentContract）で多対多となる前提で記載しています。
- 今後の用途に応じて、ドメイン境界ごとの詳細図（入会申込・お知らせ・お問い合わせ・メッセージ・馬管理/レポート/賞金・出資）に分割して整備可能です。
