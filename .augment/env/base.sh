#!/bin/bash
set -e

echo "🚀 Setting up Hami development environment with SMTP support..."

# Change to workspace directory
cd /mnt/persist/workspace

# Update system packages
sudo apt-get update

# Install system dependencies
sudo apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    python3 \
    python3-pip \
    openssl \
    ca-certificates \
    postgresql-client \
    postgresql \
    postgresql-contrib \
    unzip

# Start PostgreSQL service
sudo service postgresql start

# Configure PostgreSQL
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'postgres';" || true
sudo -u postgres createdb hami || true
sudo -u postgres createdb hami_test || true

# Install Node.js 20 (if not already installed)
if ! command -v node &> /dev/null || [[ "$(node --version)" != v20* ]]; then
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Install pnpm globally with sudo to avoid permission issues
sudo npm install -g pnpm@10.8.0

# Install turbo globally
sudo npm install -g turbo

# Install Mailpit directly
echo "📧 Installing Mailpit SMTP server..."
MAILPIT_VERSION="v1.20.5"
MAILPIT_URL="https://github.com/axllent/mailpit/releases/download/${MAILPIT_VERSION}/mailpit-linux-amd64.tar.gz"

# Download and install Mailpit
cd /tmp
wget -O mailpit.tar.gz "$MAILPIT_URL"
tar -xzf mailpit.tar.gz
sudo mv mailpit /usr/local/bin/
sudo chmod +x /usr/local/bin/mailpit

# Return to workspace
cd /mnt/persist/workspace

# Create mailpit data directory
mkdir -p $HOME/.mailpit

# Start Mailpit in background with correct flags
echo "🚀 Starting Mailpit SMTP server..."
nohup /usr/local/bin/mailpit --smtp=0.0.0.0:1025 --listen=0.0.0.0:8025 --database=$HOME/.mailpit/mailpit.db > $HOME/.mailpit/mailpit.log 2>&1 &
MAILPIT_PID=$!

# Wait for Mailpit to start
echo "⏳ Waiting for Mailpit to start..."
sleep 5

# Check if Mailpit is running
if kill -0 $MAILPIT_PID 2>/dev/null; then
    echo "✅ Mailpit is running successfully (PID: $MAILPIT_PID)"
    echo $MAILPIT_PID > $HOME/.mailpit/mailpit.pid
else
    echo "⚠️ Mailpit failed to start, checking log..."
    cat $HOME/.mailpit/mailpit.log || echo "No log file found"
fi

# Test SMTP connection
echo "🧪 Testing SMTP connection..."
timeout 5 bash -c 'cat < /dev/null > /dev/tcp/localhost/1025' && echo "✅ SMTP server is accessible on port 1025" || echo "⚠️ SMTP server connection test failed"

# Set up environment variables for testing
export NODE_ENV=test
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/hami_test"
export FRONTEND_ENDPOINT="https://example.com"
export SMTP_HOST="localhost"
export SMTP_PORT="1025"
export MAILPIT_URL="http://localhost:8025"
export JWT_SECRET="dev-secret"
export AWS_ACCESS_KEY_ID="minioadmin"
export AWS_SECRET_ACCESS_KEY="minioadmin"
export AWS_REGION="ap-northeast-1"
export S3_BUCKET_NAME="test-bucket"
export S3_ENDPOINT="http://localhost:9000"

# Add environment variables to profile for persistence
cat >> $HOME/.profile << 'EOF'
export NODE_ENV=test
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/hami_test"
export FRONTEND_ENDPOINT="https://example.com"
export SMTP_HOST="localhost"
export SMTP_PORT="1025"
export MAILPIT_URL="http://localhost:8025"
export JWT_SECRET="dev-secret"
export AWS_ACCESS_KEY_ID="minioadmin"
export AWS_SECRET_ACCESS_KEY="minioadmin"
export AWS_REGION="ap-northeast-1"
export S3_BUCKET_NAME="test-bucket"
export S3_ENDPOINT="http://localhost:9000"
EOF

# Install project dependencies
echo "📦 Installing project dependencies..."
pnpm install

# Generate Prisma client
echo "🔧 Generating Prisma client..."
pnpm --filter @hami/prisma exec prisma generate

# Set up database schema for test database
echo "🗄️ Setting up test database schema..."
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/hami_test" pnpm --filter @hami/prisma exec prisma db push

# Generate API schemas
echo "🔗 Generating API schemas..."
pnpm --filter @hami/web-api-schema generate || echo "⚠️ web-api-schema generation failed, continuing..."
pnpm --filter @hami/admin-api-schema generate || echo "⚠️ admin-api-schema generation failed, continuing..."
pnpm --filter @hami/core-admin-api-schema generate || echo "⚠️ core-admin-api-schema generation failed, continuing..."

# Build packages that tests depend on
echo "🏗️ Building required packages..."
pnpm --filter @hami/prisma build || echo "⚠️ Prisma build failed, continuing..."

# Final SMTP connection test
echo "🧪 Final SMTP connection test..."
timeout 5 bash -c 'cat < /dev/null > /dev/tcp/localhost/1025' && echo "✅ SMTP server is ready for tests" || echo "⚠️ SMTP server not accessible"

# Check Mailpit web interface
echo "🌐 Testing Mailpit web interface..."
timeout 5 bash -c 'cat < /dev/null > /dev/tcp/localhost/8025' && echo "✅ Mailpit web interface is accessible on port 8025" || echo "⚠️ Mailpit web interface not accessible"

echo "✅ Setup complete! Environment ready for testing."
echo "📧 Mailpit SMTP server is running on localhost:1025"
echo "🌐 Mailpit Web UI is available at http://localhost:8025"
echo "🔍 You can view sent emails in the Mailpit web interface"
echo "📝 Mailpit logs are available at: $HOME/.mailpit/mailpit.log"