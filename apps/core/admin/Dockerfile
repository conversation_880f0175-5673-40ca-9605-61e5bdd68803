FROM node:20-alpine AS base

WORKDIR /app

RUN npm install -g pnpm turbo


FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY . .

RUN turbo prune @hami/core-admin --docker


FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY . .

COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=builder /app/packages/core-admin-api-schema ./packages/core-admin-api-schema
RUN pnpm install

COPY --from=builder /app/out/full/ .

RUN pnpm --filter @hami/prisma exec prisma generate
RUN pnpm turbo run build --filter=@hami/core-admin


FROM base AS runner
WORKDIR /app
ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=installer --chown=nextjs:nodejs /app/apps/core/admin/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/core/admin/.next/static ./apps/core/admin/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/core/admin/public ./apps/core/admin/public
COPY --from=installer --chown=nextjs:nodejs /app/packages/core-admin-api-schema ./packages/core-admin-api-schema

USER nextjs

EXPOSE 3000

ENV PORT=3000

CMD ["sh", "-c", "HOSTNAME=0.0.0.0 exec node apps/core/admin/server.js"]
