/**
 * セッションストレージにデータを保存
 */
export const saveToSession = <T>(key: string, data: T): void => {
  try {
    sessionStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error('セッションデータの保存に失敗しました:', error);
  }
};

/**
 * セッションストレージからデータを取得
 */
export const loadFromSession = <T>(key: string): T | null => {
  try {
    const data = sessionStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('セッションデータの復元に失敗しました:', error);
    return null;
  }
};

/**
 * セッションストレージからデータを削除
 */
export const removeFromSession = (key: string): void => {
  try {
    sessionStorage.removeItem(key);
  } catch (error) {
    console.error('セッションデータの削除に失敗しました:', error);
  }
};

 