import type { DescService } from '@bufbuild/protobuf';
import type { Interceptor } from '@connectrpc/connect';
import { createClient } from '@connectrpc/connect';
import { createConnectTransport } from '@connectrpc/connect-node';
import { cookies } from 'next/headers';

const logger: Interceptor = (next) => async (req) => {
  console.log(`sending message to ${req.url}`);
  return await next(req);
};

const authInterceptor: Interceptor = (next) => async (req) => {
  const cookieStore = await cookies();
  const sessionToken = cookieStore.get('sessionToken')?.value;
  if (sessionToken) {
    req.header.set('sessionToken', sessionToken);
  }
  return await next(req);
};

export const getClient = <T extends DescService>(service: T) => {
  const client = createClient(
    service,
    createConnectTransport({
      httpVersion: '1.1',
      baseUrl: process.env.CORE_API_ENDPOINT ?? 'http://core-api.hami.orb.local/',
      interceptors: [authInterceptor, logger],
    })
  );

  return client;
};
