import { describe, it, expect } from 'vitest';
import {
  formatTimestamp,
  formatDate,
  formatDateTime,
  maskAccountNumber,
  formatBankBranchCode,
  formatBankBranchName,
} from './bank_account_utils';

describe('bank_account_utils', () => {
  describe('formatTimestamp', () => {
    it('通常の数値secondsを正しくフォーマットする', () => {
      const timestamp = { seconds: ********** }; // 2022-01-01 00:00:00 UTC
      const result = formatTimestamp(timestamp);
      expect(result).toBe('2022/01/01');
    });

    it('BigInt型のsecondsを正しくフォーマットする', () => {
      const timestamp = { seconds: BigInt(**********) }; // 2022-01-01 00:00:00 UTC
      const result = formatTimestamp(timestamp);
      expect(result).toBe('2022/01/01');
    });

    it('nullまたはundefinedの場合は"-"を返す', () => {
      expect(formatTimestamp(null)).toBe('-');
      expect(formatTimestamp(undefined)).toBe('-');
    });

    it('secondsが存在しない場合は"-"を返す', () => {
      const timestamp = {} as any;
      expect(formatTimestamp(timestamp)).toBe('-');
    });

    it('secondsがNaNの場合は"-"を返す', () => {
      const timestamp = { seconds: NaN };
      expect(formatTimestamp(timestamp)).toBe('-');
    });

    it('無効な日付の場合は"-"を返す', () => {
      const timestamp = { seconds: 'invalid' };
      expect(formatTimestamp(timestamp)).toBe('-');
    });

    it('カスタムオプションでフォーマットする', () => {
      const timestamp = { seconds: ********** }; // 2022-01-01 00:00:00 UTC
      const options = {
        year: 'numeric' as const,
        month: 'long' as const,
        day: 'numeric' as const,
      };
      const result = formatTimestamp(timestamp, options);
      expect(result).toContain('2022');
      expect(result).toContain('1月');
    });
  });

  describe('formatDate', () => {
    it('日付のみをフォーマットする', () => {
      const timestamp = { seconds: ********** }; // 2022-01-01 00:00:00 UTC
      const result = formatDate(timestamp);
      expect(result).toBe('2022/01/01');
    });
  });

  describe('formatDateTime', () => {
    it('日時をフォーマットする', () => {
      const timestamp = { seconds: ********** }; // 2022-01-01 00:00:00 UTC
      const result = formatDateTime(timestamp);
      expect(result).toMatch(/2022\/01\/01\s+\d{2}:\d{2}/);
    });
  });

  describe('maskAccountNumber', () => {
    it('通常の口座番号を正しくマスキングする', () => {
      const accountNumber = '**********';
      const result = maskAccountNumber(accountNumber);
      expect(result).toBe('******7890');
    });

    it('4桁以下の口座番号はマスキングしない', () => {
      expect(maskAccountNumber('1234')).toBe('1234');
      expect(maskAccountNumber('123')).toBe('123');
      expect(maskAccountNumber('12')).toBe('12');
      expect(maskAccountNumber('1')).toBe('1');
    });

    it('空文字列の場合は空文字列を返す', () => {
      expect(maskAccountNumber('')).toBe('');
    });

    it('nullまたはundefinedの場合は空文字列を返す', () => {
      expect(maskAccountNumber(null as any)).toBe('');
      expect(maskAccountNumber(undefined as any)).toBe('');
    });

    it('5桁の口座番号を正しくマスキングする', () => {
      const accountNumber = '12345';
      const result = maskAccountNumber(accountNumber);
      expect(result).toBe('*2345');
    });

    it('長い口座番号を正しくマスキングする', () => {
      const accountNumber = '***************';
      const result = maskAccountNumber(accountNumber);
      expect(result).toBe('***********2345');
    });
  });

  describe('formatBankBranchCode', () => {
    it('銀行コードと支店コードを正しくフォーマットする', () => {
      const result = formatBankBranchCode('0001', '001');
      expect(result).toBe('0001-001');
    });

    it('銀行コードが空の場合は空文字列を返す', () => {
      const result = formatBankBranchCode('', '001');
      expect(result).toBe('');
    });

    it('支店コードが空の場合は空文字列を返す', () => {
      const result = formatBankBranchCode('0001', '');
      expect(result).toBe('');
    });

    it('両方が空の場合は空文字列を返す', () => {
      const result = formatBankBranchCode('', '');
      expect(result).toBe('');
    });

    it('nullまたはundefinedの場合は空文字列を返す', () => {
      expect(formatBankBranchCode(null as any, '001')).toBe('');
      expect(formatBankBranchCode('0001', null as any)).toBe('');
      expect(formatBankBranchCode(undefined as any, undefined as any)).toBe('');
    });
  });

  describe('formatBankBranchName', () => {
    it('銀行名と支店名を正しくフォーマットする', () => {
      const result = formatBankBranchName('三菱UFJ銀行', '新宿支店');
      expect(result).toBe('三菱UFJ銀行 新宿支店');
    });

    it('銀行名のみの場合は銀行名を返す', () => {
      const result = formatBankBranchName('三菱UFJ銀行', '');
      expect(result).toBe('三菱UFJ銀行');
    });

    it('支店名のみの場合は支店名を返す', () => {
      const result = formatBankBranchName('', '新宿支店');
      expect(result).toBe('新宿支店');
    });

    it('両方が空の場合は空文字列を返す', () => {
      const result = formatBankBranchName('', '');
      expect(result).toBe('');
    });

    it('nullまたはundefinedの場合は適切に処理する', () => {
      expect(formatBankBranchName(null as any, '新宿支店')).toBe('新宿支店');
      expect(formatBankBranchName('三菱UFJ銀行', null as any)).toBe('三菱UFJ銀行');
      expect(formatBankBranchName(null as any, null as any)).toBe('');
    });
  });
});
