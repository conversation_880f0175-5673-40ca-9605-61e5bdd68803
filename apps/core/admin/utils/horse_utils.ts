import { useState, useEffect } from 'react';
import { getHorse } from '@core-admin/api_clients/horse_client';

/**
 * 馬の名前を取得するカスタムフック
 */
export const useHorseName = (horseId: number) => {
  const [horseName, setHorseName] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    
    const fetchHorseName = async () => {
      try {
        setIsLoading(true);
        const horseResponse = await getHorse({ horseId });
        if (mounted) {
          const displayName = horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '';
          setHorseName(displayName);
          setError(null);
        }
      } catch (error) {
        console.error('Failed to fetch horse name:', error);
        if (mounted) {
          setHorseName('');
          setError('馬の名前の取得に失敗しました');
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    if (horseId) {
      fetchHorseName();
    }

    return () => {
      mounted = false;
    };
  }, [horseId]);

  return { horseName, isLoading, error };
};

/**
 * 馬の名前を取得する関数（Server Component用）
 */
export const fetchHorseName = async (horseId: number): Promise<string> => {
  try {
    const horseResponse = await getHorse({ horseId });
    return horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '';
  } catch (error) {
    console.error('Failed to fetch horse name:', error);
    return '';
  }
}; 