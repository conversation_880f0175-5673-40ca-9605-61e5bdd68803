'use server';

import { Code, ConnectError } from '@connectrpc/connect';
import { redirect } from 'next/navigation';

/**
 * 認証エラーをハンドリングするラッパー関数
 * API呼び出しが認証エラー（Code.Unauthenticated）を返した場合、
 * 自動的にログインページにリダイレクトします
 *
 * @param fn 実行する非同期関数
 * @returns 元の関数の戻り値
 */
export async function withAuthErrorHandling<T>(fn: () => Promise<T>): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (error instanceof ConnectError && error.code === Code.Unauthenticated) {
      redirect('/login');
    }
    throw error;
  }
}
