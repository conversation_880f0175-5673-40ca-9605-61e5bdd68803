import { describe, it, expect } from 'vitest';
import {
  calculateOtherIncomeAmounts,
  calculatePrizeIncomeAmounts,
  calculateTotalAllowances,
  formatCurrency,
  formatCurrencyWithUnit,
  calculateExpenditureAmount,
} from './income_utils';

describe('income_utils', () => {
  describe('calculateTotalAllowances', () => {
    it('手当の合計金額を正しく計算する', () => {
      const allowances = [{ amount: 100000 }, { amount: 50000 }, { amount: 25000 }];

      const result = calculateTotalAllowances(allowances);

      expect(result).toBe(175000);
    });

    it('空の配列の場合は0を返す', () => {
      const result = calculateTotalAllowances([]);
      expect(result).toBe(0);
    });

    it('単一の手当の場合は正しく計算する', () => {
      const allowances = [{ amount: 100000 }];
      const result = calculateTotalAllowances(allowances);
      expect(result).toBe(100000);
    });
  });

  describe('calculateOtherIncomeAmounts', () => {
    it('その他収入の税額と収入金額を正確に計算する（Decimal型使用）', () => {
      const amount = 500000;
      const salesCommission = 25000;
      const otherFeeAmount = 10000;
      const taxRate = 10; // 10%

      const result = calculateOtherIncomeAmounts(amount, salesCommission, otherFeeAmount, taxRate);

      // 税込み基準額 = 500000 - 25000 - 10000 = 465000
      // 税額 = 465000 - (465000 / (1 + 10/100)) = 465000 - 422727.27... ≈ 42272.73
      // 収入金額 = 500000 - 25000 - 10000 - 42272.73 ≈ 422727.27

      expect(result.taxAmount).toBeCloseTo(42272.*********, 6);
      expect(result.incomeAmount).toBeCloseTo(422727.*********, 6);
    });

    it('税率0%の場合は税額が0になる', () => {
      const amount = 500000;
      const salesCommission = 25000;
      const otherFeeAmount = 10000;
      const taxRate = 0;

      const result = calculateOtherIncomeAmounts(amount, salesCommission, otherFeeAmount, taxRate);

      expect(result.taxAmount).toBe(0);
      expect(result.incomeAmount).toBe(465000); // 500000 - 25000 - 10000
    });

    it('浮動小数点数の精度問題を回避できる', () => {
      // 浮動小数点数で問題が起きやすい値を使用
      const amount = 1000001;
      const salesCommission = 333333;
      const otherFeeAmount = 111111;
      const taxRate = 8.888; // 複雑な小数

      const result = calculateOtherIncomeAmounts(amount, salesCommission, otherFeeAmount, taxRate);

      // Decimal型を使用しているため、正確な計算結果が得られる
      expect(result.taxAmount).toBeTypeOf('number');
      expect(result.incomeAmount).toBeTypeOf('number');
      expect(result.taxAmount + result.incomeAmount).toBeCloseTo(555557, 6); // amount - salesCommission - otherFeeAmount
    });
  });

  describe('calculatePrizeIncomeAmounts', () => {
    it('賞金収入の各金額を正確に計算する（Decimal型使用）', () => {
      const mainPrizeAmount = 1000000;
      const totalAllowances = 100000;
      const appearanceFee = 50000;
      const clubFeeRate = 3.0; // 3%
      const taxRate = 10.0; // 10%
      const commissionAmount = 50000;
      const withholdingTax = 100000;

      const result = calculatePrizeIncomeAmounts(
        mainPrizeAmount,
        totalAllowances,
        appearanceFee,
        clubFeeRate,
        taxRate,
        commissionAmount,
        withholdingTax
      );

      // 賞金合計 = 1000000 + 100000 + 50000 = 1150000
      expect(result.totalPrizeAmount).toBe(1150000);

      // クラブ法人手数料 = (1000000 + 100000) * 3 / 100 = 33000
      expect(result.clubFeeAmount).toBe(33000);

      // 税額 = (1150000 - 33000 - 50000) * 10 / (100 + 10) = 1067000 * 10 / 110 ≈ 97000
      expect(result.taxAmount).toBeCloseTo(97000, 6);

      // 収入金額 = 1150000 - 100000 - 33000 - 50000 - 97000 = 870000
      expect(result.incomeAmount).toBeCloseTo(870000, 6);
    });

    it('手数料率0%の場合は手数料が0になる', () => {
      const result = calculatePrizeIncomeAmounts(
        1000000, // mainPrizeAmount
        100000, // totalAllowances
        50000, // appearanceFee
        0, // clubFeeRate = 0%
        10, // taxRate
        50000, // commissionAmount
        100000 // withholdingTax
      );

      expect(result.clubFeeAmount).toBe(0);
      expect(result.totalPrizeAmount).toBe(1150000);
    });

    it('複雑な小数での計算でも正確な結果を得られる', () => {
      // 浮動小数点数で問題が起きやすい値
      const result = calculatePrizeIncomeAmounts(
        1000001, // mainPrizeAmount
        333333, // totalAllowances
        50000, // appearanceFee
        3.333, // clubFeeRate - 浮動小数点数で問題が起きやすい
        8.888, // taxRate - 複雑な小数
        77777, // commissionAmount
        111111 // withholdingTax
      );

      // Decimal型を使用しているため、正確な計算結果が得られる
      expect(result.totalPrizeAmount).toBe(1383334);
      expect(result.clubFeeAmount).toBeCloseTo(44440.02222, 5); // 実際の計算結果に合わせて修正
      expect(result.taxAmount).toBeTypeOf('number');
      expect(result.incomeAmount).toBeTypeOf('number');
    });
  });

  describe('calculateExpenditureAmount', () => {
    it('支出金額を正しく計算する', () => {
      const billingAmount = 500000;
      const subsidyAmount = 100000;

      const result = calculateExpenditureAmount(billingAmount, subsidyAmount);

      expect(result).toBe(400000);
    });

    it('補助金額が0の場合は請求金額と同じになる', () => {
      const billingAmount = 500000;
      const subsidyAmount = 0;

      const result = calculateExpenditureAmount(billingAmount, subsidyAmount);

      expect(result).toBe(500000);
    });
  });

  describe('formatCurrency', () => {
    it('数値を通貨形式でフォーマットする（円マークなし）', () => {
      expect(formatCurrency(1000000)).toBe('1,000,000');
      expect(formatCurrency(123456)).toBe('123,456');
      expect(formatCurrency(0)).toBe('0');
    });
  });

  describe('formatCurrencyWithUnit', () => {
    it('数値を通貨形式でフォーマットする（円マーク付き）', () => {
      expect(formatCurrencyWithUnit(1000000)).toBe('1,000,000円');
      expect(formatCurrencyWithUnit(123456)).toBe('123,456円');
      expect(formatCurrencyWithUnit(0)).toBe('0円');
    });
  });
});
