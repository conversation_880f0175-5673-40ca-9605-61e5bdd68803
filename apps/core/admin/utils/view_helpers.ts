import { HorseBillingItemType } from '@hami/core-admin-api-schema/horse_billing_service_pb';

export const getItemTypeString = (itemType: HorseBillingItemType, itemTypeOther?: string) => {
  switch (itemType) {
    case HorseBillingItemType.ENTRUST:
      return '預託料';
    case HorseBillingItemType.INSURANCE:
      return '保険料';
    case HorseBillingItemType.OTHER:
      return itemTypeOther || 'その他';
    default:
      return '不明';
  }
};

export const formatDate = (year: number, month: number, day?: number) => {
  if (day) {
    return `${year}年${month}月${day}日`;
  }
  return `${year}年${month}月`;
};

export const formatYearMonth = (yearMonth: number) => {
  const s = String(yearMonth);
  if (s.length !== 6) {
    return s;
  }
  return `${s.substring(0, 4)}年${s.substring(4, 6)}月`;
}; 