import { IncomeType, HorseIncomePrizeOrganizer, HorseIncomeOtherName } from '@hami/core-admin-api-schema/horse_income_service_pb';
import Decimal from 'decimal.js';

/**
 * その他収入の名目オプションを取得
 */
export const getOtherIncomeNameOptions = () => [
  { value: 'SALES_DIVIDEND', label: '賞品売却分配金' },
  { value: 'INSURANCE', label: '保険金' },
  { value: 'SYMPATHY', label: '見舞金' },
  { value: 'GRANT', label: '抹消給付' },
  { value: 'OTHER', label: 'その他' },
];

/**
 * 賞金収入主催者のオプションを取得
 */
export const getPrizeOrganizerOptions = () => [
  { value: 'JRA', label: '中央' },
  { value: 'OTHER', label: 'その他' },
];

/**
 * その他収入の名目（enum値）を日本語ラベルに変換
 */
export const getOtherIncomeNameLabel = (name: string): string => {
  return getOtherIncomeNameOptions().find((option) => option.value === name)?.label || name;
};

/**
 * その他収入の名目（enum数値）を文字列値に変換
 */
export const getOtherIncomeNameValueFromEnum = (enumValue: HorseIncomeOtherName): string => {
  const nameOptions = [
    { enum: HorseIncomeOtherName.SALES_DIVIDEND, value: 'SALES_DIVIDEND' },
    { enum: HorseIncomeOtherName.INSURANCE, value: 'INSURANCE' },
    { enum: HorseIncomeOtherName.SYMPATHY, value: 'SYMPATHY' },
    { enum: HorseIncomeOtherName.GRANT, value: 'GRANT' },
    { enum: HorseIncomeOtherName.OTHER, value: 'OTHER' },
  ];
  return nameOptions.find((option) => option.enum === enumValue)?.value || 'OTHER';
};

/**
 * その他収入の名目（文字列値）をenum数値に変換
 */
export const getOtherIncomeNameEnumFromValue = (value: string): number => {
  const nameEnumMap = {
    SALES_DIVIDEND: HorseIncomeOtherName.SALES_DIVIDEND,
    INSURANCE: HorseIncomeOtherName.INSURANCE,
    SYMPATHY: HorseIncomeOtherName.SYMPATHY,
    GRANT: HorseIncomeOtherName.GRANT,
    OTHER: HorseIncomeOtherName.OTHER,
  };
  return nameEnumMap[value as keyof typeof nameEnumMap] || HorseIncomeOtherName.OTHER;
};

/**
 * 賞金収入主催者（enum数値）を表示用ラベルに変換
 */
export const getPrizeOrganizerDisplayLabel = (organizer: HorseIncomePrizeOrganizer): string => {
  return organizer === HorseIncomePrizeOrganizer.JRA ? '中央' : 'その他';
};

/**
 * 収入種別を日本語ラベルに変換
 */
export const getIncomeTypeName = (type: IncomeType): string => {
  switch (type) {
    case IncomeType.PRIZE:
      return '賞金';
    case IncomeType.OTHER:
      return 'その他';
    default:
      return '不明';
  }
};

/**
 * 日付をフォーマット（YYYY/MM/DD）
 */
export const formatDate = (year: number, month: number, day: number): string => {
  return `${year}/${String(month).padStart(2, '0')}/${String(day).padStart(2, '0')}`;
};

/**
 * 年月をフォーマット（YYYY / MM）
 */
export const formatYearMonth = (yearMonth: number): string => {
  const year = Math.floor(yearMonth / 100);
  const month = yearMonth % 100;
  return `${year} / ${String(month).padStart(2, '0')}`;
};

/**
 * 年月を日本語フォーマット（YYYY年M月）
 */
export const formatYearMonthJapanese = (yearMonth: number): string => {
  const year = Math.floor(yearMonth / 100);
  const month = yearMonth % 100;
  return `${year}年${month}月`;
};

/**
 * 数値または文字列をフォーマット（3桁区切り）
 */
export const formatNumber = (value: any): string => {
  const num = typeof value === 'number' ? value : parseFloat(value);
  return isNaN(num) ? '0' : num.toLocaleString();
};

/**
 * その他収入の計算ロジック（Decimal型を使用した正確な計算）
 * 税込み価格から消費税額を計算する方式
 */
export const calculateOtherIncomeAmounts = (amount: number, salesCommission: number, otherFeeAmount: number, taxRate: number) => {
  const decimalAmount = new Decimal(amount);
  const decimalSalesCommission = new Decimal(salesCommission);
  const decimalOtherFeeAmount = new Decimal(otherFeeAmount);
  const decimalTaxRate = new Decimal(taxRate);

  const taxBase = decimalAmount.sub(decimalSalesCommission).sub(decimalOtherFeeAmount);
  const taxAmount = taxBase.sub(taxBase.div(new Decimal(1).add(decimalTaxRate.div(100))));
  const incomeAmount = decimalAmount.sub(decimalSalesCommission).sub(decimalOtherFeeAmount).sub(taxAmount);

  return {
    taxAmount: taxAmount.toNumber(),
    incomeAmount: incomeAmount.toNumber(),
  };
};

/**
 * 支出金額を計算する (請求金額 - 補助金額)
 */
export const calculateExpenditureAmount = (billingAmount: number, subsidyAmount: number): number => {
  const decimalBillingAmount = new Decimal(billingAmount);
  const decimalSubsidyAmount = new Decimal(subsidyAmount);
  return decimalBillingAmount.sub(decimalSubsidyAmount).toNumber();
};

/**
 * 数値を通貨形式でフォーマットする（円マークなし）
 */
export const formatCurrency = (value: number): string => {
  return value.toLocaleString();
};

/**
 * 数値を通貨形式でフォーマットする（円マーク付き）
 */
export const formatCurrencyWithUnit = (value: number): string => {
  return `${value.toLocaleString()}円`;
};

/**
 * 手当の合計金額を計算する
 */
export const calculateTotalAllowances = (allowances: Array<{ amount: number }>): number => {
  const amounts = allowances.map((allowance) => allowance.amount);
  return amounts.reduce((sum, amount) => {
    const decimalSum = new Decimal(sum);
    const decimalAmount = new Decimal(amount);
    return decimalSum.add(decimalAmount).toNumber();
  }, 0);
};

/**
 * 賞金収入の計算ロジック（Decimal型を使用した正確な計算）
 */
export const calculatePrizeIncomeAmounts = (
  mainPrizeAmount: number,
  totalAllowances: number,
  appearanceFee: number,
  clubFeeRate: number,
  taxRate: number,
  commissionAmount: number,
  withholdingTax: number
) => {
  const decimalMainPrize = new Decimal(mainPrizeAmount);
  const decimalTotalAllowances = new Decimal(totalAllowances);
  const decimalAppearanceFee = new Decimal(appearanceFee);
  const decimalClubFeeRate = new Decimal(clubFeeRate);
  const decimalTaxRate = new Decimal(taxRate);
  const decimalCommissionAmount = new Decimal(commissionAmount);
  const decimalWithholdingTax = new Decimal(withholdingTax);

  // 賞金合計 = 本賞金 + 手当合計 + 出走手当
  const totalPrizeAmount = decimalMainPrize.add(decimalTotalAllowances).add(decimalAppearanceFee);

  // クラブ法人手数料 = (本賞金 + 手当合計) * クラブ手数料率 / 100
  const clubFeeAmount = Math.floor(decimalMainPrize.add(decimalTotalAllowances).mul(decimalClubFeeRate.div(100)).toNumber());

  // 税額 = (賞金合計 - クラブ法人手数料 - 委託手数料) * 税率 / (100 + 税率)
  const taxAmount = Math.floor(totalPrizeAmount
    .sub(clubFeeAmount)
    .sub(decimalCommissionAmount)
    .mul(decimalTaxRate.div(new Decimal(100).add(decimalTaxRate))).toNumber());

  // 収入金額 = 賞金合計 - 源泉徴収税 - クラブ法人手数料 - 委託手数料 - 税額
  const incomeAmount = totalPrizeAmount.sub(decimalWithholdingTax).sub(clubFeeAmount).sub(decimalCommissionAmount).sub(taxAmount);

  return {
    totalPrizeAmount: totalPrizeAmount.toNumber(),
    clubFeeAmount: clubFeeAmount,
    taxAmount: taxAmount,
    incomeAmount: incomeAmount.toNumber(),
  };
};
