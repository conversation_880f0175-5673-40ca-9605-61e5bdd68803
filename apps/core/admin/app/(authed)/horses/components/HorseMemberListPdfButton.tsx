'use client';

import { useState } from 'react';
import { generateHorseMemberListPdfAction } from '../actions/generate-pdf-action';

export function HorseMemberListPdfButton() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGeneratePdf = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const result = await generateHorseMemberListPdfAction();

      if (result.success && result.data) {
        // PDFが生成されたら自動的にダウンロードを開始
        if (result.data.downloadUrl) {
          const link = document.createElement('a');
          link.href = result.data.downloadUrl;
          link.download = `クラブ所有馬別会員一覧_${new Date().toISOString().split('T')[0]}.pdf`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      } else {
        setError(result.error || 'PDF生成に失敗しました。');
      }
    } catch (err) {
      console.error('PDF generation failed:', err);
      setError('PDF生成に失敗しました。しばらく時間をおいてから再度お試しください。');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex flex-col items-start gap-2">
      <button onClick={handleGeneratePdf} disabled={isGenerating} className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed">
        {isGenerating ? 'PDF生成中...' : 'クラブ所有馬別会員一覧PDF生成'}
      </button>

      {error && <div className="text-error text-sm bg-red-50 border border-red-200 rounded px-3 py-2">{error}</div>}
    </div>
  );
}
