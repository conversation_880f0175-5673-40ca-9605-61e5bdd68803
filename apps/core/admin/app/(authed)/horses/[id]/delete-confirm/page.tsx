'use client';

import { useRouter } from 'next/navigation';
import { useState, useEffect, use } from 'react';
import { getHorse, deleteHorse } from '@core-admin/api_clients/horse_client';
import HorseConfirmDisplay from '../../_components/HorseConfirmDisplay';
import { HorseFormData } from '../../_components/HorseForm';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function DeleteHorseConfirmPage({ params }: PageProps) {
  const router = useRouter();
  const [horseData, setHorseData] = useState<HorseFormData | null>(null);
  const [_isLoading, setIsLoading] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const resolvedParams = use(params);
  const horseId = parseInt(resolvedParams.id);

  useEffect(() => {
    if (isNaN(horseId)) {
      setError('無効な馬IDです');
      setIsDataLoading(false);
      return;
    }

    const fetchHorseData = async () => {
      try {
        const response = await getHorse({ horseId });
        const horse = response.horse;
        
        if (!horse) {
          setError('馬が見つかりません');
          return;
        }

        setHorseData({
          recruitmentYear: horse.recruitmentYear,
          recruitmentNo: horse.recruitmentNo,
          recruitmentName: horse.recruitmentName,
          horseName: horse.horseName,
          birthYear: horse.birthYear,
          birthMonth: horse.birthMonth,
          birthDay: horse.birthDay,
          sharesTotal: horse.sharesTotal,
          amountTotal: horse.amountTotal,
          note: horse.note,
          fundStartYear: horse.fundStartYear,
          fundStartMonth: horse.fundStartMonth,
          fundStartDay: horse.fundStartDay,
          conflictOfInterest: horse.conflictOfInterest,
        });
      } catch (error) {
        console.error('Error fetching horse data:', error);
        setError('馬の情報を取得できませんでした');
      } finally {
        setIsDataLoading(false);
      }
    };

    fetchHorseData();
  }, [horseId]);

  const handleConfirm = async () => {
    if (!horseData) return;

    setIsLoading(true);
    try {
      await deleteHorse({ horseId });
      alert('馬を削除しました');
      router.push('/horses');
    } catch (error) {
      console.error('Error deleting horse:', error);
      alert('馬の削除に失敗しました');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/horses/${horseId}`);
  };

  if (error) {
    return (
      <main className="max-w-4xl mx-auto p-6">
        <div className="bg-gray-200 text-center py-4 mb-6">
          <h1 className="text-xl font-bold">馬削除確認</h1>
        </div>
        <div className="text-center text-red-500">
          <p>{error}</p>
          <button
            onClick={() => router.push('/horses')}
            className="mt-4 px-6 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          >
            馬一覧に戻る
          </button>
        </div>
      </main>
    );
  }

  if (isDataLoading) {
    return (
      <main className="max-w-4xl mx-auto p-6">
        <div className="bg-gray-200 text-center py-4 mb-6">
          <h1 className="text-xl font-bold">馬削除確認</h1>
        </div>
        <div className="text-center">Loading...</div>
      </main>
    );
  }

  if (!horseData) {
    return null;
  }

  return (
    <HorseConfirmDisplay
      data={horseData}
      title="馬削除確認"
      message="この馬を削除します。"
      onConfirm={handleConfirm}
      onCancel={handleCancel}
      confirmButtonText="削除する"
      confirmButtonColor="danger"
      horseId={horseId}
    />
  );
}
