import { listHorseInvestors } from '@core-admin/api_clients/horse_client';
import { getInvestmentApplicationHorseDetail } from '@core-admin/api_clients/investment_application_client';
import Link from 'next/link';
import { notFound } from 'next/navigation';

export default async function HorseInvestorsPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const horseId = Number(resolvedParams.id);
  if (isNaN(horseId)) return notFound();

  // 馬の出資状況サマリー取得
  const horseDetail = await getInvestmentApplicationHorseDetail({ horseId });
  if (!horseDetail) return notFound();

  // 出資者一覧取得
  const { investors } = await listHorseInvestors({ horseId });

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6">
        {/* タイトル・馬名 */}
        <div className="mb-6">
          <h1 className="text-xl font-bold text-primary mb-2">
            {horseDetail.horseName || horseDetail.recruitmentName}出資者管理
          </h1>
        </div>

        {/* サマリー */}
        <div className="mb-8 flex justify-center">
          <div className="bg-surface-card border border-stroke-separator overflow-hidden inline-block">
            <table>
              <tbody>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold text-center">
                    <span className="table-cell-text">出資口数</span>
                  </td>
                  <td className="table-cell text-center">
                    <span className="table-cell-text text-lg">{horseDetail.contractedShares}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold text-center">
                    <span className="table-cell-text">総口数</span>
                  </td>
                  <td className="table-cell text-center">
                    <span className="table-cell-text text-lg">{horseDetail.recruitmentShares}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 出資者一覧テーブル */}
        <div className="mb-8">
          <h2 className="text-lg font-bold text-primary mb-4">出資者一覧</h2>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="table-header">
                  <th>会員番号</th>
                  <th>出資者名</th>
                  <th>出資口数</th>
                  <th>出資金額</th>
                </tr>
              </thead>
              <tbody>
                {investors.length === 0 ? (
                  <tr className="table-row">
                    <td colSpan={4} className="table-cell text-center">
                      <span className="table-cell-text text-muted">出資者がいません</span>
                    </td>
                  </tr>
                ) : (
                  investors.map((inv) => (
                    <tr key={inv.memberId} className="table-row">
                      <td className="table-cell">
                        <span className="table-cell-text">{inv.memberNumber}</span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">{inv.memberName}{inv.retirementDate ? `（${new Date(Number(inv.retirementDate.seconds) * 1000).toLocaleDateString()}退会）` : ''}</span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">{inv.sharesNumber}</span>
                      </td>
                      <td className="table-cell text-right">
                        <span className="table-cell-text text-right">{inv.investmentAmount.toLocaleString()}</span>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* 戻るボタン */}
        <div className="flex justify-center mt-8">
          <Link href={`/horses/${horseId}`} className="btn-secondary">
            馬詳細へ戻る
          </Link>
        </div>
      </div>
    </main>
  );
}
