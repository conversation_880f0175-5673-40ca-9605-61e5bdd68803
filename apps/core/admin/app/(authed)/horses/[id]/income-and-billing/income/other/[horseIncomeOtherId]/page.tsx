'use client';

import { useState, useEffect, use } from 'react';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { getHorseIncomeOther } from '@core-admin/api_clients/horse_income_client';
import { HorseIncomeOtherName } from '@hami/core-admin-api-schema/horse_income_service_pb';
import Link from 'next/link';

interface HorseIncomeOtherDetailPageProps {
  params: Promise<{
    id: string;
    horseIncomeOtherId: string;
  }>;
}

interface OtherData {
  id: number;
  incomeYearMonth: string;
  occurredYear: number;
  occurredMonth: number;
  occurredDay: number;
  name: string;
  nameOther: string;
  amount: number;
  salesCommission: number;
  otherFeeName: string;
  otherFeeAmount: number;
  taxRate: string;
  taxAmount: number;
  incomeAmount: number;
  note: string;
  closing: boolean;
}

export default function HorseIncomeOtherDetailPage({ params }: HorseIncomeOtherDetailPageProps) {
  const { id, horseIncomeOtherId } = use(params);
  const horseId = parseInt(id);
  const otherId = parseInt(horseIncomeOtherId);
  const [horseName, setHorseName] = useState<string>('');
  const [otherData, setOtherData] = useState<OtherData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let mounted = true;

    const fetchData = async () => {
      try {
        // 馬の情報を取得
        const horseResponse = await getHorse({ horseId });
        if (mounted) {
          setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');
        }

        // その他収入詳細を取得
        const otherResponse = await getHorseIncomeOther({ id: otherId });
        const other = otherResponse.horseIncomeOther;

        if (other && mounted) {
          // 分配対象月のフォーマット
          const year = Math.floor(other.incomeYearMonth / 100);
          const month = other.incomeYearMonth % 100;
          const incomeYearMonth = `${year}年${month}月`;

          // 名目の変換
          const nameMap: { [key: number]: string } = {
            [HorseIncomeOtherName.SALES_DIVIDEND]: 'セール配当金',
            [HorseIncomeOtherName.INSURANCE]: '保険金',
            [HorseIncomeOtherName.SYMPATHY]: '見舞金',
            [HorseIncomeOtherName.GRANT]: '奨励金',
            [HorseIncomeOtherName.OTHER]: 'その他',
          };

          setOtherData({
            id: other.horseIncomeOtherId,
            incomeYearMonth,
            occurredYear: other.occurredYear,
            occurredMonth: other.occurredMonth,
            occurredDay: other.occurredDay,
            name: nameMap[other.name] || 'その他',
            nameOther: other.nameOther,
            amount: other.amount,
            salesCommission: other.salesCommission,
            otherFeeName: other.otherFeeName,
            otherFeeAmount: other.otherFeeAmount,
            taxRate: other.taxRate,
            taxAmount: other.taxAmount,
            incomeAmount: other.incomeAmount,
            note: other.note,
            closing: other.closing,
          });
        }
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      mounted = false;
    };
  }, [horseId, otherId]);

  if (isLoading) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">データを読み込み中...</div>
      </div>
    );
  }

  if (!otherData) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">データが見つかりません</div>
      </div>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} その他収入詳細</h1>

        {/* 基本情報 */}
        <div>
          <h3 className="text-lg font-medium text-primary mb-4">基本情報</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">分配対象月</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{otherData.incomeYearMonth}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">発生日</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {otherData.occurredYear}年{otherData.occurredMonth}月{otherData.occurredDay}日
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">名目</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{otherData.name}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{otherData.amount.toLocaleString()}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">賞品売却手数料</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{otherData.salesCommission.toLocaleString()}円</span>
                  </td>
                </tr>
                {otherData.otherFeeAmount > 0 && (
                  <tr className="table-row">
                    <th className="table-header">
                      <span className="table-cell-text">{otherData.otherFeeName}</span>
                    </th>
                    <td className="table-cell text-right">
                      <span className="table-cell-text">{otherData.otherFeeAmount.toLocaleString()}円</span>
                    </td>
                  </tr>
                )}
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税率</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{otherData.taxRate}%</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 備考 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">備考</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">備考</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{otherData.note || 'なし'}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 計算結果 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">計算結果</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{otherData.taxAmount.toLocaleString()}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">収入金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg text-primary">{otherData.incomeAmount.toLocaleString()}円</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* アクションボタン */}
        <div className="flex justify-center gap-4 mt-8">
          <Link href={`/horses/${horseId}/income-and-billing/income`} className="btn-secondary">
            一覧に戻る
          </Link>

          {!otherData.closing && (
            <>
              <Link href={`/horses/${horseId}/income-and-billing/income/other/${otherId}/edit?new=true`} className="btn-primary">
                編集
              </Link>
              <Link href={`/horses/${horseId}/income-and-billing/income/other/${otherId}/delete-confirm`} className="btn-danger">
                削除
              </Link>
            </>
          )}
        </div>
      </div>
    </main>
  );
}
