import { listHorseBillings } from '@core-admin/api_clients/horse_billing_client';
import { listBillers } from '@core-admin/api_clients/biller_client';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { type HorseBillingItem } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { type Biller } from '@hami/core-admin-api-schema/biller_service_pb';
import Link from 'next/link';
import HorseBillingListTable from '@core-admin/components/horse-billing/HorseBillingListTable';

type HorseBillingsPageProps = {
  params: Promise<{ id: string }>;
};

const HorseBillingsPage = async ({ params }: HorseBillingsPageProps) => {
  const resolvedParams = await params;
  const horseId = parseInt(resolvedParams.id, 10);

  if (isNaN(horseId)) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-error">無効な馬IDです。</div>
        </div>
      </main>
    );
  }

  let billings: HorseBillingItem[] = [];
  let billers: Biller[] = [];
  let horseName = '';
  let error: Error | null = null;

  try {
    const [billingsRes, billersRes, horseRes] = await Promise.all([
      listHorseBillings({ horseId, pageSize: 100 }), // Add pagination later
      listBillers({ pageSize: 1000 }), // Assuming not too many billers
      getHorse({ horseId }),
    ]);
    billings = billingsRes.billings;
    billers = billersRes.billers;
    horseName = horseRes.horse?.horseName || horseRes.horse?.recruitmentName || '';
  } catch (e) {
    error = e instanceof Error ? e : new Error('An unknown error occurred');
  }

  if (error) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-error">データの取得に失敗しました: {error.message}</div>
        </div>
      </main>
    );
  }
  
  const billerMap = new Map(billers.map((b) => [b.id, b.name]));

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold text-primary">{horseName} 支出管理</h1>
          <Link href={`/horses/${horseId}/income-and-billing/billing/create`}>
            <button className="btn-primary">
              新規登録
            </button>
          </Link>
        </div>

        <HorseBillingListTable 
          billings={billings} 
          billerMap={billerMap} 
          horseId={horseId} 
        />
      </div>
    </main>
  );
}

export default HorseBillingsPage; 