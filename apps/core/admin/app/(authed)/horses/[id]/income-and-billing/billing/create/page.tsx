'use client';

import { listBillers } from '@core-admin/api_clients/biller_client';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { HorseBillingItemType } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import type { Biller } from '@hami/core-admin-api-schema/biller_service_pb';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import HorseBillingForm, { type HorseBillingFormData } from '@core-admin/components/horse-billing/HorseBillingForm';

const SESSION_STORAGE_KEY = 'horseBillingCreateForm';

export default function CreateHorseBillingPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const horseId = parseInt(params.id as string, 10);

  const [billers, setBillers] = useState<Biller[]>([]);
  const [horseName, setHorseName] = useState<string>('');
  const [formData, setFormData] = useState<HorseBillingFormData>({
    billingYearMonth: '',
    occurredYear: '',
    occurredMonth: '',
    occurredDay: '',
    billerId: '',
    itemType: HorseBillingItemType.UNSPECIFIED,
    itemTypeOther: '',
    billingAmount: '',
    taxAmount: '',
    subsidyAmount: '',
    note: '',
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Reset form state when `?reset=1` is present
    if (searchParams.get('reset') === '1') {
      sessionStorage.removeItem(SESSION_STORAGE_KEY);
      // Clean URL without reloading
      window.history.replaceState(null, '', window.location.pathname);
    }

    Promise.all([
      listBillers({ pageSize: 1000 }),
      getHorse({ horseId }),
    ]).then(([billersRes, horseRes]) => {
      setBillers(billersRes.billers);
      setHorseName(horseRes.horse?.horseName ?? '');
    }).catch(e => setError(e instanceof Error ? e.message : 'データの取得に失敗しました'));

    const savedData = sessionStorage.getItem(SESSION_STORAGE_KEY);
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, [searchParams, horseId]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    let finalValue: string | number = value;
    if (type === 'select-one' && name === 'itemType') {
      finalValue = parseInt(value, 10);
    }
    setFormData((prev) => ({ ...prev, [name]: finalValue }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Add validation
    sessionStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(formData));
    router.push(`/horses/${horseId}/income-and-billing/billing/create/confirm`);
  };

  return (
    <HorseBillingForm
      formData={formData}
      billers={billers}
      horseName={horseName}
      error={error}
      onSubmit={handleSubmit}
      onChange={handleChange}
      submitButtonText="確認画面へ"
      cancelHref={`/horses/${horseId}/income-and-billing/billing`}
      cancelButtonText="キャンセル"
    />
  );
} 