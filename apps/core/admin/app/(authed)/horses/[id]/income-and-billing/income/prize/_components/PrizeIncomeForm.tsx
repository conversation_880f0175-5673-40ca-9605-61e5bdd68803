'use client';

import { useEffect, useState } from 'react';
import { listHorseIncomePrizeAllowanceNames } from '@core-admin/api_clients/horse_income_prize_allowance_client';

interface AllowanceItem {
  name: string;
  amount: string | number;
}

interface FormData {
  incomeYear: string;
  incomeMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  racePlace: string;
  raceName: string;
  raceResult: string;
  organizer: string;
  mainPrizeAmount: string | number;
  allowances: AllowanceItem[];
  appearanceFee: string | number;
  withholdingTax: string | number;
  commissionAmount: string | number;
  clubFeeRate: string;
  taxRate: string;
  note: string;
}

interface PrizeIncomeFormProps {
  formData: FormData;
  onFormDataChange: (data: FormData) => void;
  calculatedValues: {
    totalPrizeAmount: number;
    clubFeeAmount: number;
    taxAmount: number;
    incomeAmount: number;
  };
  isEditMode?: boolean;
}

export default function PrizeIncomeForm({
  formData,
  onFormDataChange,
  calculatedValues,
  isEditMode = false,
}: PrizeIncomeFormProps) {
  const [allowanceNames, setAllowanceNames] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState<{ [key: number]: boolean }>({});
  const [filteredSuggestions, setFilteredSuggestions] = useState<{ [key: number]: string[] }>({});

  useEffect(() => {
    const fetchAllowanceNames = async () => {
      try {
        const response = await listHorseIncomePrizeAllowanceNames({});
        const names = response.horseIncomePrizeAllowanceNames?.map(item => item.name) || [];
        setAllowanceNames(names);
      } catch (error) {
        console.error('手当名の取得に失敗しました:', error);
      }
    };
    fetchAllowanceNames();
  }, []);
  const handleInputChange = (field: keyof FormData, value: string | number) => {
    const newFormData = {
      ...formData,
      [field]: value,
    };
    onFormDataChange(newFormData);
  };

  const handleAllowanceChange = (index: number, field: 'name' | 'amount', value: string | number) => {
    const newAllowances = [...formData.allowances];
    newAllowances[index] = {
      ...newAllowances[index],
      [field]: value,
    };
    const newFormData = {
      ...formData,
      allowances: newAllowances,
    };
    onFormDataChange(newFormData);

    // 手当名が変更された場合、候補をフィルタリング
    if (field === 'name') {
      const inputValue = value.toString().toLowerCase();
      const filtered = allowanceNames.filter(name => 
        name.toLowerCase().includes(inputValue)
      );
      setFilteredSuggestions(prev => ({
        ...prev,
        [index]: filtered
      }));
      setShowSuggestions(prev => ({
        ...prev,
        [index]: filtered.length > 0
      }));
    }
  };

  const handleAllowanceNameFocus = (index: number) => {
    const currentValue = formData.allowances[index].name.toLowerCase();
    // フォーカス時は全ての候補を表示
    const filtered = currentValue.length > 0 
      ? allowanceNames.filter(name => name.toLowerCase().includes(currentValue))
      : allowanceNames;
    setFilteredSuggestions(prev => ({
      ...prev,
      [index]: filtered
    }));
    setShowSuggestions(prev => ({
      ...prev,
      [index]: filtered.length > 0
    }));
  };

  const handleAllowanceNameBlur = (index: number) => {
    // 少し遅延させて、クリックイベントが処理されるようにする
    setTimeout(() => {
      setShowSuggestions(prev => ({
        ...prev,
        [index]: false
      }));
    }, 200);
  };

  const selectAllowanceName = (index: number, name: string) => {
    const newAllowances = [...formData.allowances];
    newAllowances[index] = {
      ...newAllowances[index],
      name: name,
    };
    const newFormData = {
      ...formData,
      allowances: newAllowances,
    };
    onFormDataChange(newFormData);
    setShowSuggestions(prev => ({
      ...prev,
      [index]: false
    }));
  };

  const addAllowance = () => {
    const newFormData = {
      ...formData,
      allowances: [...formData.allowances, { name: '', amount: isEditMode ? 0 : '' }],
    };
    onFormDataChange(newFormData);
  };

  const removeAllowance = (index: number) => {
    const newAllowances = formData.allowances.filter((_, i) => i !== index);
    const newFormData = {
      ...formData,
      allowances: newAllowances,
    };
    onFormDataChange(newFormData);
  };

  const getInputType = (field: keyof Omit<FormData, 'allowances'>) => {
    if (isEditMode) {
      switch (field) {
        case 'mainPrizeAmount':
        case 'appearanceFee':
        case 'withholdingTax':
        case 'commissionAmount':
          return 'number';
        default:
          return 'text';
      }
    }
    return 'text';
  };

  const getInputValue = (field: keyof Omit<FormData, 'allowances'>) => {
    const value = formData[field];
    if (isEditMode && typeof value === 'number') {
      return value.toString();
    }
    return value;
  };

  const handleNumberInputChange = (field: keyof FormData, value: string) => {
    if (isEditMode) {
      const numValue = parseInt(value) || 0;
      handleInputChange(field, numValue);
    } else {
      handleInputChange(field, value);
    }
  };

  return (
    <form className="space-y-8">
      {/* 基本情報 */}
      <div>
        <h3 className="text-lg font-medium text-primary mb-4">基本情報</h3>
        <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
          {/* 分配対象月 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">分配対象月</label>
            <div className="md:col-span-6 flex items-center gap-2">
              <select
                value={formData.incomeYear}
                onChange={(e) => handleInputChange('incomeYear', e.target.value)}
                className="input-base w-32"
              >
                <option value="">年を選択</option>
                {Array.from({ length: 12 }, (_, i) => {
                  const year = 2015 + i;
                  return (
                    <option key={year} value={year.toString()}>
                      {year}年
                    </option>
                  );
                })}
              </select>
              <select
                value={formData.incomeMonth}
                onChange={(e) => handleInputChange('incomeMonth', e.target.value)}
                className="input-base w-28"
              >
                <option value="">月を選択</option>
                {Array.from({ length: 12 }, (_, i) => {
                  const month = i + 1;
                  return (
                    <option key={month} value={month.toString()}>
                      {month}月
                    </option>
                  );
                })}
              </select>
            </div>
          </div>

          {/* 開催年月日 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">開催年月日</label>
            <div className="md:col-span-6 flex items-center gap-2">
              <input
                type="number"
                value={formData.occurredYear}
                onChange={(e) => handleInputChange('occurredYear', e.target.value)}
                className="input-base w-20 text-center"
              />
              <span className="text-sm text-secondary">年</span>
              <input
                type="number"
                value={formData.occurredMonth}
                onChange={(e) => handleInputChange('occurredMonth', e.target.value)}
                className="input-base w-16 text-center"
              />
              <span className="text-sm text-secondary">月</span>
              <input
                type="number"
                value={formData.occurredDay}
                onChange={(e) => handleInputChange('occurredDay', e.target.value)}
                className="input-base w-16 text-center"
              />
              <span className="text-sm text-secondary">日</span>
            </div>
          </div>

          {/* 開催競馬場 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">開催競馬場</label>
            <div className="md:col-span-4">
              <input
                type="text"
                value={formData.racePlace}
                onChange={(e) => handleInputChange('racePlace', e.target.value)}
                className="input-base w-full"
              />
            </div>
          </div>

          {/* レース名 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">レース名</label>
            <div className="md:col-span-6">
              <input
                type="text"
                value={formData.raceName}
                onChange={(e) => handleInputChange('raceName', e.target.value)}
                className="input-base w-full"
              />
            </div>
          </div>

          {/* 着順など */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">着順など</label>
            <div className="md:col-span-3">
              <input
                type="text"
                value={formData.raceResult}
                onChange={(e) => handleInputChange('raceResult', e.target.value)}
                className="input-base w-full"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 賞金・手当 */}
      <div>
        <h3 className="text-lg font-medium text-primary mb-4">賞金・手当</h3>
        <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
          {/* 中央・その他 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">中央・その他</label>
            <div className="md:col-span-4">
              <select
                value={formData.organizer}
                onChange={(e) => handleInputChange('organizer', e.target.value)}
                className="input-base w-full"
              >
                <option value="中央">中央</option>
                <option value="その他">その他</option>
              </select>
            </div>
          </div>

          {/* 本賞金 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">本賞金</label>
            <div className="md:col-span-4 flex items-center gap-2">
              <input
                type={getInputType('mainPrizeAmount')}
                value={getInputValue('mainPrizeAmount')}
                onChange={(e) => handleNumberInputChange('mainPrizeAmount', e.target.value)}
                className="input-base w-full text-right"
                placeholder="0"
              />
              <span className="text-sm text-secondary">円</span>
            </div>
          </div>

          {/* 手当 */}
          {formData.allowances.map((allowance, index) => (
            <div key={index} className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
              <div className="md:col-span-2"></div>
              <div className="md:col-span-4 relative">
                <input
                  type="text"
                  value={allowance.name}
                  onChange={(e) => handleAllowanceChange(index, 'name', e.target.value)}
                  onFocus={() => handleAllowanceNameFocus(index)}
                  onBlur={() => handleAllowanceNameBlur(index)}
                  className="input-base w-full"
                  placeholder="手当名"
                />
                {showSuggestions[index] && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-stroke-separator rounded-md shadow-lg max-h-48 overflow-y-auto">
                    {filteredSuggestions[index]?.map((suggestion, suggestionIndex) => (
                      <div
                        key={suggestionIndex}
                        className="px-3 py-2 hover:bg-surface cursor-pointer text-sm"
                        onClick={() => selectAllowanceName(index, suggestion)}
                      >
                        {suggestion}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <div className="md:col-span-3 flex items-center gap-2">
                <input
                  type={isEditMode ? 'number' : 'text'}
                  value={allowance.amount}
                  onChange={(e) => handleAllowanceChange(index, 'amount', isEditMode ? (parseInt(e.target.value) || 0) : e.target.value)}
                  className="input-base w-full text-right"
                  placeholder="0"
                />
                <span className="text-sm text-secondary">円</span>
              </div>
              <div className="md:col-span-1">
                <button type="button" onClick={() => removeAllowance(index)} className="text-error hover:text-red-800 text-xl">
                  ×
                </button>
              </div>
            </div>
          ))}

          <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
            <div className="md:col-span-2"></div>
            <div className="md:col-span-4">
              <button type="button" onClick={addAllowance} className="flex items-center gap-2 text-primary hover:text-primary-dark">
                <span className="text-lg">+</span>
                手当などを追加する
              </button>
            </div>
          </div>

          {/* 特別出走手当 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">（特別）出走手当</label>
            <div className="md:col-span-4 flex items-center gap-2">
              <input
                type={getInputType('appearanceFee')}
                value={getInputValue('appearanceFee')}
                onChange={(e) => handleNumberInputChange('appearanceFee', e.target.value)}
                className="input-base w-full text-right"
                placeholder="0"
              />
              <span className="text-sm text-secondary">円</span>
            </div>
          </div>
        </div>
      </div>

      {/* 控除 */}
      <div>
        <h3 className="text-lg font-medium text-primary mb-4">控除</h3>
        <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
          {/* 源泉所得税 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">源泉所得税</label>
            <div className="md:col-span-4 flex items-center gap-2">
              <input
                type={getInputType('withholdingTax')}
                value={getInputValue('withholdingTax')}
                onChange={(e) => handleNumberInputChange('withholdingTax', e.target.value)}
                className="input-base w-full text-right"
                placeholder="0"
              />
              <span className="text-sm text-secondary">円</span>
            </div>
          </div>

          {/* 進上金 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">進上金</label>
            <div className="md:col-span-4 flex items-center gap-2">
              <input
                type={getInputType('commissionAmount')}
                value={getInputValue('commissionAmount')}
                onChange={(e) => handleNumberInputChange('commissionAmount', e.target.value)}
                className="input-base w-full text-right"
                placeholder="0"
              />
              <span className="text-sm text-secondary">円</span>
            </div>
          </div>
        </div>
      </div>

      {/* 設定 */}
      <div>
        <h3 className="text-lg font-medium text-primary mb-4">設定</h3>
        <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
          {/* クラブ法人手数料率 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">クラブ法人手数料率（%）</label>
            <div className="md:col-span-3">
              <input
                type="text"
                value={formData.clubFeeRate}
                onChange={(e) => handleInputChange('clubFeeRate', e.target.value)}
                className="input-base w-full text-right"
                placeholder={isEditMode ? "3.0" : "3"}
              />
            </div>
          </div>

          {/* 消費税率 */}
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
            <label className="md:col-span-2 text-sm font-medium text-primary">消費税率（%）</label>
            <div className="md:col-span-3">
              <input
                type="text"
                value={formData.taxRate}
                onChange={(e) => handleInputChange('taxRate', e.target.value)}
                className="input-base w-full text-right"
                placeholder={isEditMode ? "10.0" : "10"}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 備考 */}
      <div>
        <h3 className="text-lg font-medium text-primary mb-4">備考</h3>
        <div className="bg-surface-card border border-stroke-separator p-6">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
            <label className="md:col-span-2 text-sm font-medium text-primary">備考</label>
            <div className="md:col-span-8">
              <textarea
                value={formData.note}
                onChange={(e) => handleInputChange('note', e.target.value)}
                rows={4}
                className="input-base w-full"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 計算結果 */}
      <div>
        <h3 className="text-lg font-medium text-primary mb-4">計算結果</h3>
        <div className="bg-surface-card border border-stroke-separator overflow-hidden">
          <table className="w-full">
            <tbody>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">賞金合計</span>
                </th>
                <td className="table-cell text-right">
                  <span className="table-cell-text font-bold text-lg">{calculatedValues.totalPrizeAmount.toLocaleString()}円</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">クラブ法人手数料</span>
                </th>
                <td className="table-cell text-right">
                  <span className="table-cell-text font-bold text-lg">{calculatedValues.clubFeeAmount.toLocaleString()}円</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">消費税</span>
                </th>
                <td className="table-cell text-right">
                  <span className="table-cell-text font-bold text-lg">{calculatedValues.taxAmount.toLocaleString()}円</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">収入金額</span>
                </th>
                <td className="table-cell text-right">
                  <span className="table-cell-text font-bold text-lg text-primary">
                    {calculatedValues.incomeAmount.toLocaleString()}円
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </form>
  );
}
