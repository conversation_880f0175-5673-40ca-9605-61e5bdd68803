'use client';

import { useRouter } from 'next/navigation';
import { useState, useEffect, use } from 'react';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { listHorseBillings } from '@core-admin/api_clients/horse_billing_client';
import { listBillers } from '@core-admin/api_clients/biller_client';
import { listHorseIncomes } from '@core-admin/api_clients/horse_income_client';
import { type HorseBillingItem } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { type Biller } from '@hami/core-admin-api-schema/biller_service_pb';
import { IncomeType } from '@hami/core-admin-api-schema/horse_income_service_pb';
import Link from 'next/link';
import { formatDate, formatYearMonth, getItemTypeString } from '@core-admin/utils/view_helpers';
import { 
  getOtherIncomeNameLabel, 
  getIncomeTypeName, 
  formatDate as formatIncomeDate, 
  formatYearMonth as formatIncomeYearMonth,
  calculateExpenditureAmount,
  formatCurrency
} from '@core-admin/utils/income_utils';

interface HorseIncome {
  id: number;
  incomeYearMonth: number;
  occurredYear: number;
  occurredMonth: number;
  occurredDay: number;
  incomeType: IncomeType;
  name: string;
  name2: string;
  amount: number;
  closing: boolean;
}

type IncomeAndBillingPageProps = {
  params: Promise<{ id: string }>;
};

const IncomeAndBillingPage = ({ params }: IncomeAndBillingPageProps) => {
  const { id } = use(params);
  const horseId = parseInt(id, 10);
  const router = useRouter();

  const [horseName, setHorseName] = useState<string>('');
  const [billings, setBillings] = useState<HorseBillingItem[]>([]);
  const [billers, setBillers] = useState<Biller[]>([]);
  const [incomes, setIncomes] = useState<HorseIncome[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // データを取得
  useEffect(() => {
    let mounted = true;

    const fetchData = async () => {
      if (isNaN(horseId)) {
        setError(new Error('Invalid horse ID'));
        setIsLoading(false);
        return;
      }

      try {
        const [horseRes, billingsRes, billersRes, incomesRes] = await Promise.all([
          getHorse({ horseId: horseId }),
          listHorseBillings({ horseId, pageSize: 5, page: 1 }), // Preview of latest 5
          listBillers({ pageSize: 1000 }),
          listHorseIncomes({
            horseId: horseId,
            limit: 3, // 最大3件まで表示
            offset: 0,
          }),
        ]);

        if (mounted) {
          setHorseName(horseRes.horse?.horseName || horseRes.horse?.recruitmentName || '');
          setBillings(billingsRes.billings);
          setBillers(billersRes.billers);
          setIncomes(incomesRes.horseIncomes || []);
        }
      } catch (e) {
        if (mounted) {
          setError(e instanceof Error ? e : new Error('データの取得中に不明なエラーが発生しました'));
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      mounted = false;
    };
  }, [horseId]);

  if (isLoading) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-muted">Loading...</div>
        </div>
      </main>
    );
  }

  if (isNaN(horseId)) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-error">無効な馬IDです。</div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-error">データの取得に失敗しました: {error.message}</div>
        </div>
      </main>
    );
  }

  const billerMap = new Map(billers.map((b) => [b.id, b.name]));

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} 出資と分配管理</h1>

        {/* 支出セクション */}
        <section className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-primary">支出</h2>
            <Link href={`/horses/${horseId}/income-and-billing/billing/create`}>
              <button className="btn-primary">
                支出を追加する
              </button>
            </Link>
          </div>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="table-header">
                  <th>締め済</th>
                  <th>出資対象月</th>
                  <th>発生日</th>
                  <th>請求元</th>
                  <th>名目</th>
                  <th>金額</th>
                  <th>詳細</th>
                </tr>
              </thead>
              <tbody>
                {billings.length === 0 ? (
                  <tr className="table-row">
                    <td colSpan={7} className="table-cell text-center">
                      <span className="table-cell-text text-muted">データがありません。</span>
                    </td>
                  </tr>
                ) : (
                  billings.map((billing) => {
                    const expenditure = calculateExpenditureAmount(billing.billingAmount, billing.subsidyAmount);
                    const isClosed = billing.closing;
                    return (
                      <tr key={billing.id} className="table-row">
                        <td className="table-cell text-center">
                          <input type="checkbox" checked={isClosed} readOnly className="h-4 w-4 text-primary border-stroke-border rounded" />
                        </td>
                        <td className="table-cell">
                          <span className="table-cell-text">{formatYearMonth(billing.billingYearMonth)}</span>
                        </td>
                        <td className="table-cell">
                          <span className="table-cell-text">{formatDate(billing.occurredYear, billing.occurredMonth, billing.occurredDay)}</span>
                        </td>
                        <td className="table-cell">
                          <span className="table-cell-text">{billerMap.get(billing.billerId) || '不明'}</span>
                        </td>
                        <td className="table-cell">
                          <span className="table-cell-text">{getItemTypeString(billing.itemType, billing.itemTypeOther)}</span>
                        </td>
                        <td className="table-cell text-right">
                          <span className="table-cell-text text-right">{formatCurrency(expenditure)}</span>
                        </td>
                        <td className="table-cell text-center">
                          <Link href={`/horses/${horseId}/income-and-billing/billing/${billing.id}`}>
                            <button className="btn-secondary text-sm">詳細</button>
                          </Link>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
          <div className="text-right mt-2">
            <Link href={`/horses/${horseId}/income-and-billing/billing`} className="text-primary hover:underline text-sm">
              すべて見る
            </Link>
          </div>
        </section>

        {/* 収入セクション */}
        <section className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-primary">収入</h2>
            <div className="space-x-2">
              <button
                onClick={() => router.push(`/horses/${horseId}/income-and-billing/income/prize/create?new=true`)}
                className="btn-primary"
              >
                賞金収入を追加する
              </button>
              <button
                onClick={() => router.push(`/horses/${horseId}/income-and-billing/income/other/create?new=true`)}
                className="btn-primary"
              >
                その他の収入を追加する
              </button>
            </div>
          </div>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="table-header">
                  <th>締め済</th>
                  <th>分配対象月</th>
                  <th>開催年月日</th>
                  <th>種別</th>
                  <th>名目</th>
                  <th>収入金額</th>
                  <th>詳細</th>
                </tr>
              </thead>
              <tbody>
                {incomes.length === 0 ? (
                  <tr className="table-row">
                    <td colSpan={7} className="table-cell text-center">
                      <span className="table-cell-text text-muted">収入データがありません。</span>
                    </td>
                  </tr>
                ) : (
                  incomes.map((income) => (
                    <tr key={`${income.incomeType}-${income.id}`} className="table-row">
                      <td className="table-cell text-center">
                        <input type="checkbox" checked={income.closing} readOnly className="h-4 w-4 text-primary border-stroke-border rounded" />
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">{formatIncomeYearMonth(income.incomeYearMonth)}</span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">{formatIncomeDate(income.occurredYear, income.occurredMonth, income.occurredDay)}</span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">{getIncomeTypeName(income.incomeType)}</span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">
                          {income.incomeType === IncomeType.OTHER ? getOtherIncomeNameLabel(income.name) : income.name}
                          {income.name2 && <div className="text-sm text-secondary">{income.name2}</div>}
                        </span>
                      </td>
                      <td className="table-cell text-right">
                        <span className="table-cell-text text-right">{formatCurrency(income.amount)}</span>
                      </td>
                      <td className="table-cell text-center">
                        <Link href={`/horses/${horseId}/income-and-billing/income/${income.incomeType === IncomeType.PRIZE ? 'prize' : 'other'}/${income.id}`}>
                          <button className="btn-secondary text-sm">詳細</button>
                        </Link>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          <div className="text-right mt-2">
            <button
              onClick={() => router.push(`/horses/${horseId}/income-and-billing/income`)}
              className="text-primary hover:underline text-sm"
            >
              すべて見る
            </button>
          </div>
        </section>
      </div>
    </main>
  );
};

export default IncomeAndBillingPage;
