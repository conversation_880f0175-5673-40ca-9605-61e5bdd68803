'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { createHorseIncomeOther } from '@core-admin/api_clients/horse_income_client';
import { getHorse } from '@core-admin/api_clients/horse_client';
import {
  getOtherIncomeNameLabel,
  calculateOtherIncomeAmounts,
  formatNumber,
  getOtherIncomeNameEnumFromValue,
} from '@core-admin/utils/income_utils';
import { getOtherIncomeCreateSessionKey, restoreSessionData, clearSessionData } from '../../../../_utils/session_utils';
import { parseNumber } from '@core-admin/utils/form_utils';

interface HorseIncomeOtherCreateConfirmPageProps {
  params: Promise<{
    id: string;
  }>;
}

interface FormData {
  incomeYear: string;
  incomeMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  name: string;
  nameOther: string;
  amount: string;
  salesCommission: string;
  otherFeeName: string;
  otherFeeAmount: string;
  taxRate: string;
  isNonTaxable: boolean;
  note: string;
}

export default function HorseIncomeOtherCreateConfirmPage({ params }: HorseIncomeOtherCreateConfirmPageProps) {
  const { id } = use(params);
  const router = useRouter();
  const horseId = id;
  const [formData, setFormData] = useState<FormData | null>(null);
  const [horseName, setHorseName] = useState<string>('');
  const [calculatedValues, setCalculatedValues] = useState({
    taxAmount: 0,
    incomeAmount: 0,
  });

  // セッションからデータを復元
  useEffect(() => {
    const sessionKey = getOtherIncomeCreateSessionKey(parseInt(horseId));
    const savedData = restoreSessionData<FormData>(sessionKey);
    if (savedData) {
      setFormData(savedData);
    } else {
      router.push(`/horses/${horseId}/income-and-billing/income/other/create`);
    }
  }, [horseId, router]);

  // 馬名を取得
  useEffect(() => {
    const fetchHorseName = async () => {
      try {
        const response = await getHorse({ horseId: parseInt(horseId) });
        setHorseName(response.horse?.horseName || response.horse?.recruitmentName || '');
      } catch (error) {
        console.error('Failed to fetch horse name:', error);
        setHorseName('');
      }
    };

    fetchHorseName();
  }, [horseId]);

  // 計算処理
  useEffect(() => {
    if (formData) {
      const amount = parseNumber(formData.amount);
      const salesCommission = parseNumber(formData.salesCommission);
      const otherFeeAmount = parseNumber(formData.otherFeeAmount);
      const taxRate = parseNumber(formData.taxRate);

      if (formData.isNonTaxable) {
        // 非課税の場合も高精度計算を使用
        const { incomeAmount } = calculateOtherIncomeAmounts(amount, salesCommission, otherFeeAmount, 0);
        setCalculatedValues({
          taxAmount: 0,
          incomeAmount,
        });
      } else {
        const { taxAmount, incomeAmount } = calculateOtherIncomeAmounts(amount, salesCommission, otherFeeAmount, taxRate);
        setCalculatedValues({
          taxAmount,
          incomeAmount,
        });
      }
    }
  }, [formData]);

  const handleBack = () => {
    router.push(`/horses/${horseId}/income-and-billing/income/other/create?from=confirm`);
  };

  const handleSubmit = async () => {
    try {
      if (!formData) return;

      // フォームデータをAPI仕様に合わせて変換
      const amount = parseFloat(formData.amount) || 0;
      const salesCommission = parseFloat(formData.salesCommission) || 0;
      const otherFeeAmount = parseFloat(formData.otherFeeAmount) || 0;
      const incomeYear = parseInt(formData.incomeYear);
      const incomeMonth = parseInt(formData.incomeMonth);
      const incomeYearMonth = incomeYear * 100 + incomeMonth;

      // 名目をenum値に変換
      const nameEnum = getOtherIncomeNameEnumFromValue(formData.name);

      // API呼び出し用のデータを準備
      const createData = {
        horseId: parseInt(horseId),
        incomeYearMonth: incomeYearMonth,
        occurredYear: parseInt(formData.occurredYear),
        occurredMonth: parseInt(formData.occurredMonth),
        occurredDay: parseInt(formData.occurredDay),
        name: nameEnum,
        nameOther: formData.nameOther,
        amount: amount,
        salesCommission: salesCommission,
        otherFeeName: formData.otherFeeName,
        otherFeeAmount: otherFeeAmount,
        taxRate: formData.taxRate,
        taxAmount: calculatedValues.taxAmount,
        incomeAmount: calculatedValues.incomeAmount,
        note: formData.note,
      };

      // 実際のAPI呼び出しを行う
      await createHorseIncomeOther(createData);

      // セッションデータをクリア
      const sessionKey = getOtherIncomeCreateSessionKey(parseInt(horseId));
      clearSessionData(sessionKey);

      // 成功メッセージ
      alert('その他収入を追加しました。');

      // 一覧画面に戻る
      router.push(`/horses/${horseId}/income-and-billing/income`);
    } catch (error) {
      console.error('保存に失敗しました:', error);
      alert('保存に失敗しました。もう一度お試しください。');
    }
  };

  if (!formData) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">読み込み中...</div>
      </div>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} その他収入作成確認</h1>

        {/* 基本情報 */}
        <div>
          <h3 className="text-lg font-medium text-primary mb-4">基本情報</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">分配対象月</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {formData.incomeYear}年{formData.incomeMonth}月
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">発生日</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {formData.occurredYear}年{formData.occurredMonth}月{formData.occurredDay}日
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">名目</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {getOtherIncomeNameLabel(formData.name)}
                      {formData.name === 'OTHER' && formData.nameOther && (
                        <span className="ml-2 text-secondary">（{formData.nameOther}）</span>
                      )}
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{formatNumber(parseFloat(formData.amount) || 0)}円</span>
                  </td>
                </tr>
                {/* 賞品売却手数料（0円の場合は非表示） */}
                {parseFloat(formData.salesCommission) > 0 && (
                  <tr className="table-row">
                    <th className="table-header">
                      <span className="table-cell-text">賞品売却手数料</span>
                    </th>
                    <td className="table-cell text-right">
                      <span className="table-cell-text">{formatNumber(parseFloat(formData.salesCommission) || 0)}円</span>
                    </td>
                  </tr>
                )}
                {/* その他手数料（0円の場合は非表示） */}
                {parseFloat(formData.otherFeeAmount) > 0 && (
                  <tr className="table-row">
                    <th className="table-header">
                      <span className="table-cell-text">{formData.otherFeeName}</span>
                    </th>
                    <td className="table-cell text-right">
                      <span className="table-cell-text">{formatNumber(parseFloat(formData.otherFeeAmount) || 0)}円</span>
                    </td>
                  </tr>
                )}
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税率</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{formData.isNonTaxable ? '非課税' : `${formData.taxRate}%`}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 備考 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">備考</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">備考</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{formData.note || 'なし'}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 計算結果 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">計算結果</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{formatNumber(calculatedValues.taxAmount)}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">収入金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg text-primary">{formatNumber(calculatedValues.incomeAmount)}円</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* ボタン */}
        <div className="flex justify-center gap-4 mt-8">
          <button onClick={handleBack} className="btn-secondary">
            戻る
          </button>
          <button onClick={handleSubmit} className="btn-primary">
            追加する
          </button>
        </div>
      </div>
    </main>
  );
}
