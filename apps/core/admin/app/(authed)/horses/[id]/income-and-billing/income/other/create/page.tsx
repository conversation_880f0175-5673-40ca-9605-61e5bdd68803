'use client';

import { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { getOtherIncomeNameOptions, formatCurrency } from '@core-admin/utils/income_utils';
import { parseNumber } from '@core-admin/utils/form_utils';
import { calculateOtherIncomeAmounts } from '@core-admin/utils/income_utils';
import { DEFAULT_TAX_RATE, NON_TAXABLE_RATE } from '../../../_utils/const';
import {
  getOtherIncomeCreateSessionKey,
  saveSessionData,
  restoreSessionData,
  clearSessionData,
  setNewEntryFlag,
  checkAndClearNewEntryFlag,
} from '../../../_utils/session_utils';

interface HorseIncomeOtherCreatePageProps {
  params: Promise<{
    id: string;
  }>;
}

interface FormData {
  incomeYear: string;
  incomeMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  name: string;
  nameOther: string;
  amount: string;
  salesCommission: string;
  otherFeeName: string;
  otherFeeAmount: string;
  taxRate: string;
  isNonTaxable: boolean;
  note: string;
}

export default function HorseIncomeOtherCreatePage({ params }: HorseIncomeOtherCreatePageProps) {
  const { id } = use(params);
  const router = useRouter();
  const searchParams = useSearchParams();
  const horseId = parseInt(id);
  const [horseName, setHorseName] = useState<string>('');
  const [isInitialized, setIsInitialized] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    incomeYear: '',
    incomeMonth: '',
    occurredYear: '',
    occurredMonth: '',
    occurredDay: '',
    name: 'SALES_DIVIDEND',
    nameOther: '',
    amount: '',
    salesCommission: '',
    otherFeeName: '',
    otherFeeAmount: '',
    taxRate: DEFAULT_TAX_RATE.toString(),
    isNonTaxable: false,
    note: '',
  });

  const [calculatedValues, setCalculatedValues] = useState({
    taxAmount: 0,
    incomeAmount: 0,
  });

  // セッションからデータを復元
  useEffect(() => {
    const sessionKey = getOtherIncomeCreateSessionKey(horseId);
    const isNew = searchParams.get('new') === 'true';
    const fromConfirm = searchParams.get('from') === 'confirm';

    if (isNew) {
      // 新規作成の場合はセッションをクリアして新規フラグを設定
      clearSessionData(sessionKey);
      setNewEntryFlag(sessionKey);
      setIsInitialized(true);
    } else if (fromConfirm) {
      // 確認画面からの戻りの場合はセッションデータを復元
      const savedData = restoreSessionData<FormData>(sessionKey);
      if (savedData) {
        setFormData(savedData);
      }
      setIsInitialized(true);
    } else {
      // 通常の場合もセッションデータを復元
      const savedData = restoreSessionData<FormData>(sessionKey);
      if (savedData) {
        setFormData(savedData);
      }
      setIsInitialized(true);
    }
  }, [horseId, searchParams]);

  // フォームデータをセッションに保存
  useEffect(() => {
    if (!isInitialized) return; // 初期化が完了してからセッション保存を開始

    const sessionKey = getOtherIncomeCreateSessionKey(horseId);
    // 新規作成フラグがある場合は初回保存をスキップ
    if (!checkAndClearNewEntryFlag(sessionKey)) {
      saveSessionData(sessionKey, formData);
    }
  }, [formData, horseId, isInitialized]);

  // 計算処理
  useEffect(() => {
    const amount = parseNumber(formData.amount);
    const salesCommission = parseNumber(formData.salesCommission);
    const otherFeeAmount = parseNumber(formData.otherFeeAmount);
    const taxRate = parseNumber(formData.taxRate);

    if (formData.isNonTaxable) {
      // 非課税の場合も高精度計算を使用
      const { incomeAmount } = calculateOtherIncomeAmounts(amount, salesCommission, otherFeeAmount, 0);
      setCalculatedValues({
        taxAmount: 0,
        incomeAmount,
      });
    } else {
      // 課税の場合：Decimal型を使用した正確な計算
      const { taxAmount, incomeAmount } = calculateOtherIncomeAmounts(amount, salesCommission, otherFeeAmount, taxRate);
      setCalculatedValues({
        taxAmount,
        incomeAmount,
      });
    }
  }, [formData]);

  useEffect(() => {
    let mounted = true;

    const fetchHorseName = async () => {
      try {
        const horseResponse = await getHorse({ horseId });
        if (mounted) {
          setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');
        }
      } catch (error) {
        console.error('Failed to fetch horse name:', error);
        if (mounted) {
          setHorseName('');
        }
      }
    };

    if (horseId) {
      fetchHorseName();
    }

    return () => {
      mounted = false;
    };
  }, [horseId]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNonTaxableChange = (checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      isNonTaxable: checked,
      taxRate: checked ? NON_TAXABLE_RATE.toString() : DEFAULT_TAX_RATE.toString(),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 確認画面に遷移
    router.push(`/horses/${horseId}/income-and-billing/income/other/create/confirm`);
  };

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} その他収入作成</h1>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* 基本情報 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">基本情報</h3>
            <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
              {/* 分配対象月 */}
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <label className="md:col-span-2 text-sm font-medium text-primary">分配対象月</label>
                <div className="md:col-span-6 flex items-center gap-2">
                  <select
                    value={formData.incomeYear}
                    onChange={(e) => handleInputChange('incomeYear', e.target.value)}
                    className="input-base w-32"
                  >
                    <option value="">年を選択</option>
                    {Array.from({ length: 12 }, (_, i) => {
                      const year = 2015 + i;
                      return (
                        <option key={year} value={year.toString()}>
                          {year}年
                        </option>
                      );
                    })}
                  </select>
                  <select
                    value={formData.incomeMonth}
                    onChange={(e) => handleInputChange('incomeMonth', e.target.value)}
                    className="input-base w-28"
                  >
                    <option value="">月を選択</option>
                    {Array.from({ length: 12 }, (_, i) => {
                      const month = i + 1;
                      return (
                        <option key={month} value={month.toString()}>
                          {month}月
                        </option>
                      );
                    })}
                  </select>
                </div>
              </div>

              {/* 発生日 */}
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <label className="md:col-span-2 text-sm font-medium text-primary">発生日</label>
                <div className="md:col-span-6 flex items-center gap-2">
                  <input
                    type="text"
                    value={formData.occurredYear}
                    onChange={(e) => handleInputChange('occurredYear', e.target.value)}
                    placeholder="2024"
                    className="input-base w-20 text-center"
                  />
                  <span className="text-sm text-secondary">年</span>
                  <input
                    type="text"
                    value={formData.occurredMonth}
                    onChange={(e) => handleInputChange('occurredMonth', e.target.value)}
                    placeholder="12"
                    className="input-base w-16 text-center"
                  />
                  <span className="text-sm text-secondary">月</span>
                  <input
                    type="text"
                    value={formData.occurredDay}
                    onChange={(e) => handleInputChange('occurredDay', e.target.value)}
                    placeholder="31"
                    className="input-base w-16 text-center"
                  />
                  <span className="text-sm text-secondary">日</span>
                </div>
              </div>
            </div>
          </div>

          {/* 名目 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">名目</h3>
            <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
              {/* リストから選択 */}
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <label className="md:col-span-2 text-sm font-medium text-primary">リストから選択</label>
                <div className="md:col-span-6">
                  <select value={formData.name} onChange={(e) => handleInputChange('name', e.target.value)} className="input-base w-full">
                    {getOtherIncomeNameOptions().map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* その他の場合 */}
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <label className="md:col-span-2 text-sm font-medium text-primary">その他の場合</label>
                <div className="md:col-span-6">
                  <input
                    type="text"
                    value={formData.nameOther}
                    onChange={(e) => handleInputChange('nameOther', e.target.value)}
                    className="input-base w-full"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 金額 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">金額</h3>
            <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <label className="md:col-span-2 text-sm font-medium text-primary">金額</label>
                <div className="md:col-span-4 flex items-center gap-2">
                  <input
                    type="text"
                    value={formData.amount}
                    onChange={(e) => handleInputChange('amount', e.target.value)}
                    placeholder="0"
                    className="input-base w-full text-right"
                  />
                  <span className="text-sm text-secondary">円</span>
                </div>
              </div>

              {/* 賞品売却手数料 */}
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <label className="md:col-span-2 text-sm font-medium text-primary">賞品売却手数料</label>
                <div className="md:col-span-4 flex items-center gap-2">
                  <input
                    type="text"
                    value={formData.salesCommission}
                    onChange={(e) => handleInputChange('salesCommission', e.target.value)}
                    placeholder="0"
                    className="input-base w-full text-right"
                  />
                  <span className="text-sm text-secondary">円</span>
                </div>
              </div>
            </div>
          </div>

          {/* その他手数料 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">その他手数料</h3>
            <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
              {/* 名目 */}
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <label className="md:col-span-2 text-sm font-medium text-primary">名目</label>
                <div className="md:col-span-6">
                  <input
                    type="text"
                    value={formData.otherFeeName}
                    onChange={(e) => handleInputChange('otherFeeName', e.target.value)}
                    className="input-base w-full"
                  />
                </div>
              </div>

              {/* 手数料 */}
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <label className="md:col-span-2 text-sm font-medium text-primary">手数料</label>
                <div className="md:col-span-4 flex items-center gap-2">
                  <input
                    type="text"
                    value={formData.otherFeeAmount}
                    onChange={(e) => handleInputChange('otherFeeAmount', e.target.value)}
                    placeholder="0"
                    className="input-base w-full text-right"
                  />
                  <span className="text-sm text-secondary">円</span>
                </div>
              </div>
            </div>
          </div>

          {/* 設定 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">設定</h3>
            <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
              {/* 消費税率 */}
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                <label className="md:col-span-2 text-sm font-medium text-primary">消費税率（%）</label>
                <div className="md:col-span-6 flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="isNonTaxable"
                      checked={formData.isNonTaxable}
                      onChange={(e) => handleNonTaxableChange(e.target.checked)}
                      className="h-4 w-4 text-primary focus:ring-primary border-stroke-border rounded"
                    />
                    <label htmlFor="isNonTaxable" className="text-sm text-primary">
                      非課税
                    </label>
                  </div>
                  <div className="flex-1">
                    <input
                      type="text"
                      value={formData.taxRate}
                      onChange={(e) => handleInputChange('taxRate', e.target.value)}
                      placeholder="10"
                      disabled={formData.isNonTaxable}
                      className={`input-base w-full text-right ${formData.isNonTaxable ? 'bg-surface text-muted' : ''}`}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 備考 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">備考</h3>
            <div className="bg-surface-card border border-stroke-separator p-6">
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                <label className="md:col-span-2 text-sm font-medium text-primary">備考</label>
                <div className="md:col-span-8">
                  <textarea
                    value={formData.note}
                    onChange={(e) => handleInputChange('note', e.target.value)}
                    rows={4}
                    className="input-base w-full"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 計算結果 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">計算結果</h3>
            <div className="bg-surface-card border border-stroke-separator overflow-hidden">
              <table className="w-full">
                <tbody>
                  <tr className="table-row">
                    <th className="table-header">
                      <span className="table-cell-text">消費税</span>
                    </th>
                    <td className="table-cell text-right">
                      <span className="table-cell-text font-bold text-lg">{formatCurrency(calculatedValues.taxAmount)}円</span>
                    </td>
                  </tr>
                  <tr className="table-row">
                    <th className="table-header">
                      <span className="table-cell-text">収入金額</span>
                    </th>
                    <td className="table-cell text-right">
                      <span className="table-cell-text font-bold text-lg text-primary">
                        {formatCurrency(calculatedValues.incomeAmount)}円
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* 送信ボタン */}
          <div className="flex justify-center">
            <button type="submit" className="btn-primary">
              登録内容を確認する
            </button>
          </div>
        </form>
      </div>
    </main>
  );
}
