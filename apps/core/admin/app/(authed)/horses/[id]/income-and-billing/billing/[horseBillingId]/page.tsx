import { getHorseBillingDetail } from '@core-admin/api_clients/horse_billing_client';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { getBiller } from '@core-admin/api_clients/biller_client';
import { notFound } from 'next/navigation';
import HorseBillingDetailTable from '@core-admin/components/horse-billing/HorseBillingDetailTable';
import HorseBillingActionButtons from '@core-admin/components/horse-billing/HorseBillingActionButtons';

type PageProps = {
  params: Promise<{
    id: string; // horseId
    horseBillingId: string;
  }>;
};

export default async function HorseBillingDetailPage({ params }: PageProps) {
  const resolvedParams = await params;
  const horseId = parseInt(resolvedParams.id, 10);
  const horseBillingId = parseInt(resolvedParams.horseBillingId, 10);

  if (isNaN(horseId) || isNaN(horseBillingId)) {
    notFound();
  }

  try {
    const billingRes = await getHorseBillingDetail({ id: horseBillingId });
    const billing = billingRes.billing;

    if (!billing) {
      notFound();
    }
    
    // Fetch horse and biller names in parallel
    const [horseRes, billerRes] = await Promise.all([
        getHorse({ horseId }),
        getBiller({ id: billing.billerId })
    ]);

    const horseName = horseRes.horse?.horseName || horseRes.horse?.recruitmentName || '';
    const billerName = billerRes.biller?.name || '不明';
    const isClosed = billing.closing;

    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <h1 className="text-2xl font-bold text-primary mb-8">{horseName} 支出詳細</h1>

          <HorseBillingDetailTable billing={billing} billerName={billerName} />

          <HorseBillingActionButtons 
            horseId={horseId} 
            billingId={horseBillingId} 
            isClosed={isClosed} 
          />
        </div>
      </main>
    );
  } catch (error) {
    console.error(error);
    notFound();
  }
} 