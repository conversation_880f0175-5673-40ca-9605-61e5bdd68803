'use client';

import { createHorseBilling } from '@core-admin/api_clients/horse_billing_client';
import { listBillers } from '@core-admin/api_clients/biller_client';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import HorseBillingConfirmTable from '@core-admin/components/horse-billing/HorseBillingConfirmTable';
import HorseBillingConfirmButtons from '@core-admin/components/horse-billing/HorseBillingConfirmButtons';

const SESSION_STORAGE_KEY = 'horseBillingCreateForm';

type HorseBillingFormData = {
  billingYearMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  billerId: string;
  itemType: string;
  itemTypeOther: string;
  billingAmount: string;
  taxAmount: string;
  subsidyAmount: string;
  note: string;
};

export default function ConfirmCreateHorseBillingPage() {
  const router = useRouter();
  const params = useParams();
  const horseId = parseInt(params.id as string, 10);

  const [formData, setFormData] = useState<HorseBillingFormData | null>(null);
  const [billerName, setBillerName] = useState('');
  const [horseName, setHorseName] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const savedData = sessionStorage.getItem(SESSION_STORAGE_KEY);
    if (!savedData) {
      // Redirect if no data, maybe the user refreshed the page
      router.replace(`/horses/${horseId}/income-and-billing/billing/create?reset=1`);
      return;
    }
    const data = JSON.parse(savedData) as HorseBillingFormData;
    setFormData(data);

    Promise.all([
      getHorse({ horseId }),
      data.billerId ? listBillers({ pageSize: 1000 }) : Promise.resolve(null),
    ]).then(([horseRes, billersRes]) => {
      setHorseName(horseRes.horse?.horseName ?? '');
      if (billersRes) {
        const biller = billersRes.billers.find(b => b.id === parseInt(data.billerId, 10));
        setBillerName(biller?.name || '不明');
      }
    }).catch(e => setError(e instanceof Error ? e.message : 'データの取得に失敗しました'));
  }, [router, horseId]);

  const handleSubmit = async () => {
    if (!formData) return;
    setIsSubmitting(true);
    setError(null);
    
    try {
      const billingAmount = parseInt(formData.billingAmount, 10);
      const taxAmount = parseInt(formData.taxAmount, 10);
      const totalAmount = billingAmount + taxAmount;

      await createHorseBilling({
        horseId,
        billingYearMonth: parseInt(formData.billingYearMonth, 10),
        occurredYear: parseInt(formData.occurredYear, 10),
        occurredMonth: parseInt(formData.occurredMonth, 10),
        occurredDay: parseInt(formData.occurredDay, 10),
        billerId: parseInt(formData.billerId, 10),
        itemType: Number(formData.itemType),
        itemTypeOther: formData.itemTypeOther,
        billingAmount,
        taxAmount,
        subsidyAmount: parseInt(formData.subsidyAmount, 10),
        totalAmount,
        note: formData.note,
      });

      sessionStorage.removeItem(SESSION_STORAGE_KEY);
      router.push(`/horses/${horseId}/income-and-billing/billing`);
      // Consider adding a success toast notification here
    } catch (e) {
      setError(e instanceof Error ? e.message : '登録に失敗しました。');
      setIsSubmitting(false);
    }
  };

  if (!formData) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">読み込み中...</div>
      </div>
    );
  }

  const expenditureAmount = (parseInt(formData.billingAmount, 10) || 0) - (parseInt(formData.subsidyAmount, 10) || 0);

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} 登録内容の確認</h1>
        
        {error && (
          <div className="bg-error border border-error px-4 py-3 rounded mb-6">
            <span className="text-error">{error}</span>
          </div>
        )}
        
        <HorseBillingConfirmTable formData={formData} billerName={billerName} />

        <h2 className="text-xl font-bold text-primary mb-4">計算結果</h2>
        <div className="bg-surface-card border border-stroke-separator overflow-hidden max-w-4xl mb-8">
          <table className="w-full">
            <tbody>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">支出金額</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text font-bold">{expenditureAmount.toLocaleString()} 円</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <HorseBillingConfirmButtons
          isSubmitting={isSubmitting}
          onBack={() => router.back()}
          onSubmit={handleSubmit}
          backButtonText="修正する"
          submitButtonText="登録する"
        />
      </div>
    </main>
  );
} 