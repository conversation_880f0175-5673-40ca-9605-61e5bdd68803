'use client';

import { updateHorseBilling } from '@core-admin/api_clients/horse_billing_client';
import { listBillers } from '@core-admin/api_clients/biller_client';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import HorseBillingConfirmTable from '@core-admin/components/horse-billing/HorseBillingConfirmTable';
import HorseBillingConfirmButtons from '@core-admin/components/horse-billing/HorseBillingConfirmButtons';

type HorseBillingFormData = {
  billingYearMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  billerId: string;
  itemType: string;
  itemTypeOther: string;
  billingAmount: string;
  taxAmount: string;
  subsidyAmount: string;
  note: string;
};

export default function ConfirmEditHorseBillingPage() {
  const router = useRouter();
  const params = useParams();
  const horseId = parseInt(params.id as string, 10);
  const billingId = parseInt(params.horseBillingId as string, 10);
  const SESSION_STORAGE_KEY = `horseBillingEditForm_${billingId}`;

  const [formData, setFormData] = useState<HorseBillingFormData | null>(null);
  const [billerName, setBillerName] = useState('');
  const [horseName, setHorseName] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const savedData = sessionStorage.getItem(SESSION_STORAGE_KEY);
    if (!savedData) {
      router.replace(`/horses/${horseId}/income-and-billing/billing/${billingId}/edit?reset=1`);
      return;
    }
    const data = JSON.parse(savedData) as HorseBillingFormData;
    setFormData(data);

    Promise.all([
      getHorse({ horseId }),
      data.billerId ? listBillers({ pageSize: 1000 }) : Promise.resolve(null),
    ]).then(([horseRes, billersRes]) => {
      setHorseName(horseRes.horse?.horseName ?? '');
      if (billersRes) {
        const biller = billersRes.billers.find(b => b.id === parseInt(data.billerId, 10));
        setBillerName(biller?.name || '不明');
      }
    }).catch(e => setError(e instanceof Error ? e.message : 'データの取得に失敗しました'));
  }, [router, horseId, billingId, SESSION_STORAGE_KEY]);

  const handleSubmit = async () => {
    if (!formData) return;
    setIsSubmitting(true);
    setError(null);
    
    try {
      const billingAmount = parseInt(formData.billingAmount, 10);
      const taxAmount = parseInt(formData.taxAmount, 10);
      const totalAmount = billingAmount + taxAmount;

      await updateHorseBilling({
        id: billingId,
        billingYearMonth: parseInt(formData.billingYearMonth, 10),
        occurredYear: parseInt(formData.occurredYear, 10),
        occurredMonth: parseInt(formData.occurredMonth, 10),
        occurredDay: parseInt(formData.occurredDay, 10),
        billerId: parseInt(formData.billerId, 10),
        itemType: Number(formData.itemType),
        itemTypeOther: formData.itemTypeOther,
        billingAmount,
        taxAmount,
        subsidyAmount: parseInt(formData.subsidyAmount, 10),
        totalAmount,
        note: formData.note,
      });

      sessionStorage.removeItem(SESSION_STORAGE_KEY);
      router.push(`/horses/${horseId}/income-and-billing/billing`);
    } catch (e) {
      setError(e instanceof Error ? e.message : '更新に失敗しました。');
      setIsSubmitting(false);
    }
  };

  if (!formData) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-muted">読み込み中...</div>
        </div>
      </main>
    );
  }
  
  const expenditureAmount = (parseInt(formData.billingAmount, 10) || 0) - (parseInt(formData.subsidyAmount, 10) || 0);
  
  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} 更新内容の確認</h1>
        
        {error && (
          <div className="text-center text-error mb-8">{error}</div>
        )}
        
        <HorseBillingConfirmTable formData={formData} billerName={billerName} />

        <h2 className="text-xl font-bold text-primary mb-4">計算結果</h2>
        <div className="bg-surface-card border border-stroke-separator overflow-hidden max-w-4xl mb-8">
          <table className="w-full">
            <tbody>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">支出金額</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{expenditureAmount.toLocaleString()} 円</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <HorseBillingConfirmButtons
          isSubmitting={isSubmitting}
          onBack={() => router.back()}
          onSubmit={handleSubmit}
          backButtonText="修正する"
          submitButtonText="更新する"
        />
      </div>
    </main>
  );
} 