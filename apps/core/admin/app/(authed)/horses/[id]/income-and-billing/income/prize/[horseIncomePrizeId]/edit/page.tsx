'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { getHorseIncomePrize } from '@core-admin/api_clients/horse_income_client';
import {
  getPrizeIncomeEditSessionKey,
  saveSessionData,
  restoreSessionData,
  clearSessionData,
  setNewEntryFlag,
  checkAndClearNewEntryFlag,
} from '../../../../_utils/session_utils';
import { parseNumber } from '@core-admin/utils/form_utils';
import {
  calculateTotalAllowances,
  calculatePrizeIncomeAmounts,
  getPrizeOrganizerDisplayLabel,
} from '@core-admin/utils/income_utils';
import PrizeIncomeForm from '@core-admin/app/(authed)/horses/[id]/income-and-billing/income/prize/_components/PrizeIncomeForm';

interface HorseIncomePrizeEditPageProps {
  params: Promise<{
    id: string;
    horseIncomePrizeId: string;
  }>;
}

interface AllowanceItem {
  name: string;
  amount: string | number;
}

interface FormData {
  incomeYear: string;
  incomeMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  racePlace: string;
  raceName: string;
  raceResult: string;
  organizer: string;
  mainPrizeAmount: string | number;
  allowances: AllowanceItem[];
  appearanceFee: string | number;
  withholdingTax: string | number;
  commissionAmount: string | number;
  clubFeeRate: string;
  taxRate: string;
  note: string;
}

export default function HorseIncomePrizeEditPage({ params }: HorseIncomePrizeEditPageProps) {
  const { id, horseIncomePrizeId } = use(params);
  const horseId = parseInt(id);
  const prizeId = parseInt(horseIncomePrizeId);
  const router = useRouter();
  const [formData, setFormData] = useState<FormData>({
    incomeYear: '',
    incomeMonth: '',
    occurredYear: '',
    occurredMonth: '',
    occurredDay: '',
    racePlace: '',
    raceName: '',
    raceResult: '',
    organizer: '',
    mainPrizeAmount: 0,
    allowances: [],
    appearanceFee: 0,
    withholdingTax: 0,
    commissionAmount: 0,
    clubFeeRate: '0',
    taxRate: '0',
    note: '',
  });

  const [calculatedValues, setCalculatedValues] = useState({
    totalPrizeAmount: 0,
    clubFeeAmount: 0,
    taxAmount: 0,
    incomeAmount: 0,
  });

  const [horseName, setHorseName] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // 既存データの読み込み
  useEffect(() => {
    let mounted = true;

    const fetchData = async () => {
      try {
        const [horseResponse, prizeResponse] = await Promise.all([getHorse({ horseId }), getHorseIncomePrize({ id: prizeId })]);

        if (!mounted) return;

        setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');

        if (prizeResponse && prizeResponse.horseIncomePrize) {
          const prize = prizeResponse.horseIncomePrize;
          const yearMonth = prize.incomeYearMonth.toString();
          const year = yearMonth.substring(0, 4);
          const month = yearMonth.substring(4, 6);

          const originalData = {
            incomeYear: year,
            incomeMonth: month,
            occurredYear: prize.occurredYear.toString(),
            occurredMonth: prize.occurredMonth.toString(),
            occurredDay: prize.occurredDay.toString(),
            racePlace: prize.racePlace,
            raceName: prize.raceName,
            raceResult: prize.raceResult,
            organizer: getPrizeOrganizerDisplayLabel(prize.organizer),
            mainPrizeAmount: prize.mainPrizeAmount,
            allowances: prize.allowances.map((allowance) => ({
              name: allowance.name,
              amount: allowance.amount,
            })),
            appearanceFee: prize.appearanceFee,
            withholdingTax: prize.withholdingTax,
            commissionAmount: prize.commissionAmount,
            clubFeeRate: prize.clubFeeRate,
            taxRate: prize.taxRate,
            note: prize.note,
          };

          // 新規編集か確認画面からの戻りかを判定
          const sessionKey = getPrizeIncomeEditSessionKey(horseId, prizeId);
          const urlParams = new URLSearchParams(window.location.search);
          const isFromConfirm = urlParams.get('from') === 'confirm';
          const isNewEntry = urlParams.get('new') === 'true';

          if (isNewEntry || (!isFromConfirm && !restoreSessionData(sessionKey))) {
            // 新規編集の場合、またはセッションデータがない場合は元データをセット
            setFormData(originalData);
            // セッションをクリアして新規フラグを設定
            clearSessionData(sessionKey);
            setNewEntryFlag(sessionKey);
          } else if (isFromConfirm) {
            // 確認画面からの戻りの場合はセッションデータを復元
            const savedData = restoreSessionData<FormData>(sessionKey);
            if (savedData) {
              setFormData(savedData);
            } else {
              // セッションデータがない場合は元データを使用
              setFormData(originalData);
            }
          } else {
            // その他の場合は元データを使用
            setFormData(originalData);
          }
        }
      } catch (error) {
        console.error('データの取得に失敗しました:', error);
        if (mounted) {
          setHorseName('');
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
          setIsInitialized(true);
        }
      }
    };

    fetchData();

    return () => {
      mounted = false;
    };
  }, [horseId, prizeId]);

  // フォームデータをセッションに保存（初期化完了後のみ）
  useEffect(() => {
    if (!isLoading && isInitialized) {
      const sessionKey = getPrizeIncomeEditSessionKey(horseId, prizeId);
      // 新規編集フラグがある場合は初回保存をスキップ
      if (!checkAndClearNewEntryFlag(sessionKey)) {
        saveSessionData(sessionKey, formData);
      }
    }
  }, [formData, horseId, prizeId, isLoading, isInitialized]);

  // 計算処理（Decimal型を使用した正確な計算）
  useEffect(() => {
    const allowancesWithNumbers = formData.allowances.map(allowance => ({
      ...allowance,
      amount: typeof allowance.amount === 'string' ? parseNumber(allowance.amount) : allowance.amount
    }));
    const totalAllowances = calculateTotalAllowances(allowancesWithNumbers);

    // Decimal型を使用した正確な計算
    const calculatedAmounts = calculatePrizeIncomeAmounts(
      typeof formData.mainPrizeAmount === 'string' ? parseNumber(formData.mainPrizeAmount) : formData.mainPrizeAmount,
      totalAllowances,
      typeof formData.appearanceFee === 'string' ? parseNumber(formData.appearanceFee) : formData.appearanceFee,
      parseFloat(formData.clubFeeRate) || 0,
      parseFloat(formData.taxRate) || 0,
      typeof formData.commissionAmount === 'string' ? parseNumber(formData.commissionAmount) : formData.commissionAmount,
      typeof formData.withholdingTax === 'string' ? parseNumber(formData.withholdingTax) : formData.withholdingTax
    );

    setCalculatedValues(calculatedAmounts);
  }, [formData]);

  const handleFormDataChange = (newFormData: FormData) => {
    setFormData(newFormData);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // セッションにデータを保存
    const sessionKey = getPrizeIncomeEditSessionKey(horseId, prizeId);
    saveSessionData(sessionKey, formData);

    // 確認画面に遷移
    router.push(`/horses/${horseId}/income-and-billing/income/prize/${prizeId}/edit/confirm`);
  };

  if (isLoading) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-muted">データを読み込み中...</div>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} 賞金収入編集</h1>

        <form onSubmit={handleSubmit}>
          <PrizeIncomeForm
            formData={formData}
            onFormDataChange={handleFormDataChange}
            calculatedValues={calculatedValues}
            isEditMode={true}
          />
          
          {/* 送信ボタン */}
          <div className="flex justify-center mt-8">
            <button type="submit" className="btn-primary">
              編集内容を確認する
            </button>
          </div>
        </form>
      </div>
    </main>
  );
}
