'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { createHorseIncomePrize } from '@core-admin/api_clients/horse_income_client';
import { HorseIncomePrizeOrganizer } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { parseNumber } from '@core-admin/utils/form_utils';
import { calculateTotalAllowances, calculatePrizeIncomeAmounts } from '@core-admin/utils/income_utils';
import { getPrizeIncomeCreateSessionKey, restoreSessionData, clearSessionData } from '../../../../_utils/session_utils';
import PrizeIncomeConfirmLayout from '@core-admin/components/common/PrizeIncomeConfirmLayout';

interface HorseIncomePrizeCreateConfirmPageProps {
  params: Promise<{
    id: string;
  }>;
}

interface AllowanceItem {
  name: string;
  amount: string;
}

interface FormData {
  incomeYear: string;
  incomeMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  racePlace: string;
  raceName: string;
  raceResult: string;
  organizer: string;
  mainPrizeAmount: string;
  allowances: AllowanceItem[];
  appearanceFee: string;
  withholdingTax: string;
  commissionAmount: string;
  clubFeeRate: string;
  taxRate: string;
  note: string;
}

export default function HorseIncomePrizeCreateConfirmPage({ params }: HorseIncomePrizeCreateConfirmPageProps) {
  const { id } = use(params);
  const horseId = parseInt(id);
  const router = useRouter();
  const [horseName, setHorseName] = useState<string>('');
  const [formData, setFormData] = useState<FormData | null>(null);
  const [calculatedValues, setCalculatedValues] = useState({
    totalPrizeAmount: 0,
    clubFeeAmount: 0,
    taxAmount: 0,
    incomeAmount: 0,
  });

  useEffect(() => {
    let mounted = true;

    const fetchHorseName = async () => {
      try {
        const horseResponse = await getHorse({ horseId });
        if (mounted) {
          setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');
        }
      } catch (error) {
        console.error('Failed to fetch horse name:', error);
        if (mounted) {
          setHorseName('');
        }
      }
    };

    if (horseId) {
      fetchHorseName();
    }

    return () => {
      mounted = false;
    };
  }, [horseId]);

  // セッションからデータを復元
  useEffect(() => {
    const sessionKey = getPrizeIncomeCreateSessionKey(horseId);
    const savedData = restoreSessionData<FormData>(sessionKey);
    if (savedData) {
      setFormData(savedData);
    } else {
      router.push(`/horses/${horseId}/income-and-billing/income/prize/create`);
    }
  }, [horseId, router]);

  // 計算処理
  useEffect(() => {
    if (formData) {
      const mainPrizeAmount = parseNumber(formData.mainPrizeAmount);
      const appearanceFee = parseNumber(formData.appearanceFee);
      const withholdingTax = parseNumber(formData.withholdingTax);
      const commissionAmount = parseNumber(formData.commissionAmount);
      const clubFeeRate = parseNumber(formData.clubFeeRate);
      const taxRate = parseNumber(formData.taxRate);

      const totalAllowances = calculateTotalAllowances(formData.allowances.map((allowance) => ({ amount: parseNumber(allowance.amount) })));

      // Decimal型を使用した正確な計算
      const calculatedAmounts = calculatePrizeIncomeAmounts(
        mainPrizeAmount,
        totalAllowances,
        appearanceFee,
        clubFeeRate,
        taxRate,
        commissionAmount,
        withholdingTax
      );

      setCalculatedValues(calculatedAmounts);
    }
  }, [formData]);

  const handleBack = () => {
    router.push(`/horses/${horseId}/income-and-billing/income/prize/create?from=confirm`);
  };

  const handleSubmit = async () => {
    if (!formData) return;

    try {
      // 主催者の変換
      const organizer = formData.organizer === '中央' ? HorseIncomePrizeOrganizer.JRA : HorseIncomePrizeOrganizer.OTHER;

      // 分配対象月の計算（年と月から数値に変換）
      const year = parseInt(formData.incomeYear);
      const month = parseInt(formData.incomeMonth);

      // 必須フィールドの検証
      if (!year || year <= 0) {
        alert('分配対象年を正しく入力してください。');
        return;
      }
      if (!month || month < 1 || month > 12) {
        alert('分配対象月を正しく入力してください。');
        return;
      }

      const occurredYear = parseInt(formData.occurredYear);
      const occurredMonth = parseInt(formData.occurredMonth);
      const occurredDay = parseInt(formData.occurredDay);

      if (!occurredYear || occurredYear <= 0) {
        alert('開催年を正しく入力してください。');
        return;
      }
      if (!occurredMonth || occurredMonth < 1 || occurredMonth > 12) {
        alert('開催月を正しく入力してください。');
        return;
      }
      if (!occurredDay || occurredDay < 1 || occurredDay > 31) {
        alert('開催日を正しく入力してください。');
        return;
      }

      if (!formData.racePlace || formData.racePlace.trim() === '') {
        alert('開催競馬場を入力してください。');
        return;
      }
      if (!formData.raceName || formData.raceName.trim() === '') {
        alert('レース名を入力してください。');
        return;
      }
      if (!formData.raceResult || formData.raceResult.trim() === '') {
        alert('着順などを入力してください。');
        return;
      }

      const incomeYearMonth = year * 100 + month;

      // API呼び出し用のデータを準備
      const createData = {
        horseId: horseId,
        incomeYearMonth: incomeYearMonth,
        occurredYear: occurredYear,
        occurredMonth: occurredMonth,
        occurredDay: occurredDay,
        racePlace: formData.racePlace.trim(),
        raceName: formData.raceName.trim(),
        raceResult: formData.raceResult.trim(),
        organizer: organizer,
        mainPrizeAmount: Math.max(0, parseNumber(formData.mainPrizeAmount)),
        appearanceFee: Math.max(0, parseNumber(formData.appearanceFee)),
        withholdingTax: Math.max(0, parseNumber(formData.withholdingTax)),
        commissionAmount: Math.max(0, parseNumber(formData.commissionAmount)),
        clubFeeRate: formData.clubFeeRate,
        taxRate: formData.taxRate,
        totalPrizeAmount: Math.max(0, calculatedValues.totalPrizeAmount),
        clubFeeAmount: Math.max(0, calculatedValues.clubFeeAmount),
        taxAmount: Math.max(0, calculatedValues.taxAmount),
        incomeAmount: calculatedValues.incomeAmount, // これは負の値もありうる
        note: formData.note || '',
        allowances: formData.allowances
          .filter((allowance) => allowance.name.trim() !== '') // 空の手当は除外
          .map((allowance) => ({
            name: allowance.name.trim(),
            amount: Math.max(0, parseNumber(allowance.amount)),
          })),
      };

      // データ検証
      const validationErrors = [];
      if (createData.horseId <= 0) validationErrors.push('馬ID');
      if (createData.incomeYearMonth <= 0) validationErrors.push('分配対象月');
      if (createData.occurredYear <= 0) validationErrors.push('開催年');
      if (createData.occurredMonth <= 0) validationErrors.push('開催月');
      if (createData.occurredDay <= 0) validationErrors.push('開催日');

      if (validationErrors.length > 0) {
        alert(`以下のフィールドに無効な値が設定されています: ${validationErrors.join(', ')}`);
        return;
      }

      // 実際のAPI呼び出しを行う
      await createHorseIncomePrize(createData);

      // セッションデータをクリア
      const sessionKey = getPrizeIncomeCreateSessionKey(horseId);
      clearSessionData(sessionKey);

      // 成功メッセージを表示
      alert('賞金収入データを保存しました。');

      // 一覧画面に戻る
      router.push(`/horses/${horseId}/income-and-billing/income`);
    } catch (error) {
      console.error('保存に失敗しました:', error);
      alert('保存に失敗しました。もう一度お試しください。');
    }
  };

  if (!formData) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">読み込み中...</div>
      </div>
    );
  }

  // 分配対象月の表示用フォーマット
  const incomeYearMonthDisplay =
    formData.incomeYear && formData.incomeMonth ? `${formData.incomeYear}年${formData.incomeMonth}月` : '未設定';

  return (
    <PrizeIncomeConfirmLayout
      title="賞金収入作成確認"
      horseName={horseName}
      basicInfo={{
        incomeYearMonth: incomeYearMonthDisplay,
        occurredYear: formData.occurredYear,
        occurredMonth: formData.occurredMonth,
        occurredDay: formData.occurredDay,
        racePlace: formData.racePlace,
        raceName: formData.raceName,
        raceResult: formData.raceResult,
      }}
      prizeInfo={{
        organizer: formData.organizer,
        mainPrizeAmount: formData.mainPrizeAmount,
        allowances: formData.allowances,
        appearanceFee: formData.appearanceFee,
        withholdingTax: formData.withholdingTax,
        commissionAmount: formData.commissionAmount,
      }}
      settings={{
        clubFeeRate: formData.clubFeeRate,
        taxRate: formData.taxRate,
      }}
      note={formData.note}
      calculatedValues={calculatedValues}
      onBack={handleBack}
      onSubmit={handleSubmit}
      submitButtonText="追加する"
      submitButtonVariant="primary"
    />
  );
}
