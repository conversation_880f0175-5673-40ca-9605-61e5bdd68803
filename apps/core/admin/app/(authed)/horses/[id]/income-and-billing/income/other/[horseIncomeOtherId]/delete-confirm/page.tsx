'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { getHorseIncomeOther, deleteHorseIncomeOther } from '@core-admin/api_clients/horse_income_client';
import { getOtherIncomeNameLabel, getOtherIncomeNameValueFromEnum } from '@core-admin/utils/income_utils';

interface HorseIncomeOtherDeleteConfirmPageProps {
  params: Promise<{
    id: string;
    horseIncomeOtherId: string;
  }>;
}

interface HorseIncomeOtherData {
  horseIncomeOtherId: number;
  incomeYearMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  name: string;
  nameOther: string;
  amount: number;
  salesCommission: number;
  otherFeeName: string;
  otherFeeAmount: number;
  taxRate: string;
  taxAmount: number;
  incomeAmount: number;
  note: string;
  closing: boolean;
}

export default function HorseIncomeOtherDeleteConfirmPage({ params }: HorseIncomeOtherDeleteConfirmPageProps) {
  const { id, horseIncomeOtherId } = use(params);
  const router = useRouter();
  const horseId = parseInt(id);
  const otherId = horseIncomeOtherId;
  const [otherData, setOtherData] = useState<HorseIncomeOtherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [horseName, setHorseName] = useState<string>('');

  // その他収入データの取得
  useEffect(() => {
    const fetchOtherData = async () => {
      try {
        const response = await getHorseIncomeOther({ id: parseInt(otherId) });
        const apiData = response.horseIncomeOther;

        if (apiData) {
          // APIデータをコンポーネント用の形式に変換
          const formattedData: HorseIncomeOtherData = {
            horseIncomeOtherId: apiData.horseIncomeOtherId,
            incomeYearMonth: `${Math.floor(apiData.incomeYearMonth / 100)}年${apiData.incomeYearMonth % 100}月`,
            occurredYear: apiData.occurredYear.toString(),
            occurredMonth: apiData.occurredMonth.toString(),
            occurredDay: apiData.occurredDay.toString(),
            name: getOtherIncomeNameValueFromEnum(apiData.name),
            nameOther: apiData.nameOther,
            amount: apiData.amount,
            salesCommission: apiData.salesCommission,
            otherFeeName: apiData.otherFeeName,
            otherFeeAmount: apiData.otherFeeAmount,
            taxRate: apiData.taxRate,
            taxAmount: apiData.taxAmount,
            incomeAmount: apiData.incomeAmount,
            note: apiData.note,
            closing: apiData.closing,
          };

          setOtherData(formattedData);
        }
      } catch (error) {
        console.error('データの取得に失敗しました:', error);
        alert('データの取得に失敗しました。');
        router.push(`/horses/${horseId}/income-and-billing/income`);
      } finally {
        setLoading(false);
      }
    };

    fetchOtherData();
  }, [otherId, horseId, router]);

  useEffect(() => {
    let mounted = true;

    const fetchHorseName = async () => {
      try {
        const horseResponse = await getHorse({ horseId });
        if (mounted) {
          setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');
        }
      } catch (error) {
        console.error('Failed to fetch horse name:', error);
        if (mounted) {
          setHorseName('');
        }
      }
    };

    if (horseId) {
      fetchHorseName();
    }

    return () => {
      mounted = false;
    };
  }, [horseId]);

  const handleBack = () => {
    router.push(`/horses/${horseId}/income-and-billing/income/other/${otherId}`);
  };

  const handleDelete = async () => {
    try {
      // 実際のAPI呼び出しを行う
      await deleteHorseIncomeOther({ id: parseInt(otherId) });

      // 成功メッセージ
      alert('その他収入を削除しました。');

      // 収入一覧画面に戻る
      router.push(`/horses/${horseId}/income-and-billing/income`);
    } catch (error) {
      console.error('削除に失敗しました:', error);
      alert('削除に失敗しました。もう一度お試しください。');
    }
  };

  if (loading) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">読み込み中...</div>
      </div>
    );
  }

  if (!otherData) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">データが見つかりません。</div>
      </div>
    );
  }

  // Closingがtrueの場合は削除不可
  if (otherData.closing) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="bg-error border border-error px-4 py-3 rounded">
            <strong className="text-error">エラー:</strong> この収入は締め設定されているため削除できません。
          </div>
          <div className="mt-4">
            <button onClick={handleBack} className="btn-secondary">
              戻る
            </button>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} その他収入削除確認</h1>

        {/* 削除警告 */}
        <div className="bg-error border border-error px-4 py-3 rounded mb-8">
          <strong className="text-error">警告:</strong> この操作は元に戻すことができません。本当に削除してもよろしいですか？
        </div>

        {/* 基本情報 */}
        <div>
          <h3 className="text-lg font-medium text-primary mb-4">基本情報</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">分配対象月</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{otherData.incomeYearMonth}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">発生年月日</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {otherData.occurredYear}年{otherData.occurredMonth}月{otherData.occurredDay}日
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">名目</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{getOtherIncomeNameLabel(otherData.name)}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">名目（その他）</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{otherData.nameOther || 'なし'}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 金額情報 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">金額情報</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{otherData.amount.toLocaleString()}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">賞品売却手数料</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{otherData.salesCommission.toLocaleString()}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">その他手数料名</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{otherData.otherFeeName || 'なし'}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">その他手数料金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{otherData.otherFeeAmount.toLocaleString()}円</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 設定 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">設定</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税率</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{otherData.taxRate}%</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 備考 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">備考</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">備考</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{otherData.note || 'なし'}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 計算結果 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">計算結果</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{otherData.taxAmount.toLocaleString()}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">収入金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg text-primary">{otherData.incomeAmount.toLocaleString()}円</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* ボタン */}
        <div className="flex justify-center gap-4 mt-8">
          <button onClick={handleBack} className="btn-secondary">
            キャンセル
          </button>
          <button onClick={handleDelete} className="btn-danger">
            削除する
          </button>
        </div>
      </div>
    </main>
  );
}
