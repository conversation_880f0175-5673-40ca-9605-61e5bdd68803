'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { parseNumber } from '@core-admin/utils/form_utils';
import {
  saveSessionData,
  restoreSessionData,
  clearSessionData,
  setNewEntryFlag,
  checkAndClearNewEntryFlag,
  getPrizeIncomeCreateSessionKey,
} from '../../../_utils/session_utils';
import { DEFAULT_CLUB_FEE_RATE, DEFAULT_TAX_RATE } from '../../../_utils/const';
import { calculatePrizeIncomeAmounts } from '@core-admin/utils/income_utils';
import PrizeIncomeForm from '@core-admin/app/(authed)/horses/[id]/income-and-billing/income/prize/_components/PrizeIncomeForm';

interface HorseIncomePrizeCreatePageProps {
  params: Promise<{
    id: string;
  }>;
}

interface AllowanceItem {
  name: string;
  amount: string | number;
}

interface FormData {
  incomeYear: string;
  incomeMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  racePlace: string;
  raceName: string;
  raceResult: string;
  organizer: string;
  mainPrizeAmount: string | number;
  allowances: AllowanceItem[];
  appearanceFee: string | number;
  withholdingTax: string | number;
  commissionAmount: string | number;
  clubFeeRate: string;
  taxRate: string;
  note: string;
}

export default function HorseIncomePrizeCreatePage({ params }: HorseIncomePrizeCreatePageProps) {
  const { id } = use(params);
  const horseId = parseInt(id);
  const router = useRouter();
  const [horseName, setHorseName] = useState<string>('');
  const [formData, setFormData] = useState<FormData>({
    incomeYear: '',
    incomeMonth: '',
    occurredYear: '',
    occurredMonth: '',
    occurredDay: '',
    racePlace: '',
    raceName: '',
    raceResult: '',
    organizer: '中央',
    mainPrizeAmount: '',
    allowances: [],
    appearanceFee: '',
    withholdingTax: '',
    commissionAmount: '',
    clubFeeRate: DEFAULT_CLUB_FEE_RATE.toString(),
    taxRate: DEFAULT_TAX_RATE.toString(),
    note: '',
  });

  const [calculatedValues, setCalculatedValues] = useState({
    totalPrizeAmount: 0,
    clubFeeAmount: 0,
    taxAmount: 0,
    incomeAmount: 0,
  });

  const [isInitialized, setIsInitialized] = useState(false);

  // 初期化時にページアクセス方法を判定
  useEffect(() => {
    const sessionKey = getPrizeIncomeCreateSessionKey(horseId);

    // URLのクエリパラメータを確認
    const urlParams = new URLSearchParams(window.location.search);
    const isFromConfirm = urlParams.get('from') === 'confirm';
    const isNewEntry = urlParams.get('new') === 'true';

    if (isNewEntry || (!isFromConfirm && !restoreSessionData(sessionKey))) {
      // 新規作成の場合、またはセッションデータがない場合は前のセッションをクリア
      clearSessionData(sessionKey);
      setNewEntryFlag(sessionKey);
      setIsInitialized(true);
    } else if (isFromConfirm) {
      // 確認画面からの戻りの場合はセッションからデータを復元
      const savedData = restoreSessionData<FormData>(sessionKey);
      if (savedData) {
        setFormData(savedData);
      }
      setIsInitialized(true);
    } else {
      setIsInitialized(true);
    }
  }, [horseId]);

  // フォームデータをセッションに保存（初期化完了後のみ）
  useEffect(() => {
    if (!isInitialized) return;

    const sessionKey = getPrizeIncomeCreateSessionKey(horseId);
    // 新規作成フラグがある場合は初回保存をスキップ
    if (!checkAndClearNewEntryFlag(sessionKey)) {
      saveSessionData(sessionKey, formData);
    }
  }, [formData, horseId, isInitialized]);

  // 計算処理
  useEffect(() => {
    const totalAllowances = formData.allowances.reduce((sum, allowance) => sum + parseNumber(allowance.amount), 0);
    const mainPrizeAmount = parseNumber(formData.mainPrizeAmount);
    const appearanceFee = parseNumber(formData.appearanceFee);
    const withholdingTax = parseNumber(formData.withholdingTax);
    const commissionAmount = parseNumber(formData.commissionAmount);
    const clubFeeRate = parseNumber(formData.clubFeeRate);
    const taxRate = parseNumber(formData.taxRate);

    // Decimal型を使用した正確な計算
    const calculatedAmounts = calculatePrizeIncomeAmounts(
      mainPrizeAmount,
      totalAllowances,
      appearanceFee,
      clubFeeRate,
      taxRate,
      commissionAmount,
      withholdingTax
    );

    setCalculatedValues(calculatedAmounts);
  }, [formData]);

  useEffect(() => {
    let mounted = true;

    const fetchHorseName = async () => {
      try {
        const horseResponse = await getHorse({ horseId });
        if (mounted) {
          setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');
        }
      } catch (error) {
        console.error('Failed to fetch horse name:', error);
        if (mounted) {
          setHorseName('');
        }
      }
    };

    if (horseId) {
      fetchHorseName();
    }

    return () => {
      mounted = false;
    };
  }, [horseId]);

  const handleFormDataChange = (newFormData: FormData) => {
    setFormData(newFormData);
  };

  const handleSubmit = () => {
    const sessionKey = getPrizeIncomeCreateSessionKey(horseId);
    saveSessionData(sessionKey, formData);
    router.push(`/horses/${horseId}/income-and-billing/income/prize/create/confirm`);
  };

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} 賞金収入作成</h1>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
        >
          <PrizeIncomeForm
            formData={formData}
            onFormDataChange={handleFormDataChange}
            calculatedValues={calculatedValues}
            isEditMode={false}
          />
          
          {/* 送信ボタン */}
          <div className="flex justify-center mt-8">
            <button type="submit" className="btn-primary">
              登録内容を確認する
            </button>
          </div>
        </form>
      </div>
    </main>
  );
}
