interface DateSelectorProps {
  yearValue: string;
  monthValue: string;
  dayValue: string;
  onYearChange: (year: string) => void;
  onMonthChange: (month: string) => void;
  onDayChange: (day: string) => void;
}

export default function DateSelector({
  yearValue,
  monthValue,
  dayValue,
  onYearChange,
  onMonthChange,
  onDayChange
}: DateSelectorProps) {
  return (
    <div className="flex items-center gap-2">
      <input
        type="number"
        value={yearValue}
        onChange={(e) => onYearChange(e.target.value)}
        className="w-20 border border-gray-300 rounded px-3 py-2 text-center"
        placeholder="YYYY"
      />
      <span>年</span>
      <input
        type="number"
        value={monthValue}
        onChange={(e) => onMonthChange(e.target.value)}
        className="w-16 border border-gray-300 rounded px-3 py-2 text-center"
        placeholder="MM"
        min="1"
        max="12"
      />
      <span>月</span>
      <input
        type="number"
        value={dayValue}
        onChange={(e) => onDayChange(e.target.value)}
        className="w-16 border border-gray-300 rounded px-3 py-2 text-center"
        placeholder="DD"
        min="1"
        max="31"
      />
      <span>日</span>
    </div>
  );
} 