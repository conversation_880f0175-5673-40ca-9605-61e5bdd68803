import { saveToSession, loadFromSession, removeFromSession } from '@core-admin/utils/session_utils';

/**
 * 賞金収入作成用のセッションキーを生成
 */
export const getPrizeIncomeCreateSessionKey = (horseId: number): string => {
  return `horse-income-prize-create-${horseId}`;
};

/**
 * その他収入作成用のセッションキーを生成
 */
export const getOtherIncomeCreateSessionKey = (horseId: number): string => {
  return `horse-income-other-create-${horseId}`;
};

/**
 * 賞金収入編集用のセッションキーを生成
 */
export const getPrizeIncomeEditSessionKey = (horseId: number, incomeId: number): string => {
  return `horse-income-prize-edit-${horseId}-${incomeId}`;
};

/**
 * その他収入編集用のセッションキーを生成
 */
export const getOtherIncomeEditSessionKey = (horseId: number, incomeId: number): string => {
  return `horse-income-other-edit-${horseId}-${incomeId}`;
};

/**
 * セッションデータをクリアする
 */
export const clearSessionData = (sessionKey: string): void => {
  sessionStorage.removeItem(sessionKey);
};

/**
 * セッションデータを保存する
 */
export const saveSessionData = <T>(sessionKey: string, data: T): void => {
  try {
    sessionStorage.setItem(sessionKey, JSON.stringify(data));
  } catch (error) {
    console.error('セッションデータの保存に失敗しました:', error);
  }
};

/**
 * セッションデータを復元する
 */
export const restoreSessionData = <T>(sessionKey: string): T | null => {
  try {
    const savedData = sessionStorage.getItem(sessionKey);
    if (savedData) {
      return JSON.parse(savedData) as T;
    }
  } catch (error) {
    console.error('セッションデータの復元に失敗しました:', error);
  }
  return null;
};

/**
 * 新規作成・編集開始時にセッションデータをクリアするフラグを設定
 */
export const setNewEntryFlag = (sessionKey: string): void => {
  sessionStorage.setItem(`${sessionKey}-new-entry`, 'true');
};

/**
 * 新規作成・編集開始フラグをチェックして削除
 */
export const checkAndClearNewEntryFlag = (sessionKey: string): boolean => {
  const isNewEntry = sessionStorage.getItem(`${sessionKey}-new-entry`) === 'true';
  if (isNewEntry) {
    sessionStorage.removeItem(`${sessionKey}-new-entry`);
  }
  return isNewEntry;
};

// 便利なラッパー関数

/**
 * 賞金収入作成セッションデータを保存
 */
export const savePrizeIncomeCreateSession = <T>(horseId: number, data: T): void => {
  const sessionKey = getPrizeIncomeCreateSessionKey(horseId);
  saveToSession(sessionKey, data);
};

/**
 * 賞金収入作成セッションデータを復元
 */
export const loadPrizeIncomeCreateSession = <T>(horseId: number): T | null => {
  const sessionKey = getPrizeIncomeCreateSessionKey(horseId);
  return loadFromSession<T>(sessionKey);
};

/**
 * 賞金収入作成セッションデータを削除
 */
export const removePrizeIncomeCreateSession = (horseId: number): void => {
  const sessionKey = getPrizeIncomeCreateSessionKey(horseId);
  removeFromSession(sessionKey);
};

/**
 * 賞金収入編集セッションデータを保存
 */
export const savePrizeIncomeEditSession = <T>(horseId: number, incomeId: number, data: T): void => {
  const sessionKey = getPrizeIncomeEditSessionKey(horseId, incomeId);
  saveToSession(sessionKey, data);
};

/**
 * 賞金収入編集セッションデータを復元
 */
export const loadPrizeIncomeEditSession = <T>(horseId: number, incomeId: number): T | null => {
  const sessionKey = getPrizeIncomeEditSessionKey(horseId, incomeId);
  return loadFromSession<T>(sessionKey);
};

/**
 * 賞金収入編集セッションデータを削除
 */
export const removePrizeIncomeEditSession = (horseId: number, incomeId: number): void => {
  const sessionKey = getPrizeIncomeEditSessionKey(horseId, incomeId);
  removeFromSession(sessionKey);
};

/**
 * その他収入作成セッションデータを保存
 */
export const saveOtherIncomeCreateSession = <T>(horseId: number, data: T): void => {
  const sessionKey = getOtherIncomeCreateSessionKey(horseId);
  saveToSession(sessionKey, data);
};

/**
 * その他収入作成セッションデータを復元
 */
export const loadOtherIncomeCreateSession = <T>(horseId: number): T | null => {
  const sessionKey = getOtherIncomeCreateSessionKey(horseId);
  return loadFromSession<T>(sessionKey);
};

/**
 * その他収入作成セッションデータを削除
 */  
export const removeOtherIncomeCreateSession = (horseId: number): void => {
  const sessionKey = getOtherIncomeCreateSessionKey(horseId);
  removeFromSession(sessionKey);
};

/**
 * その他収入編集セッションデータを保存
 */
export const saveOtherIncomeEditSession = <T>(horseId: number, incomeId: number, data: T): void => {
  const sessionKey = getOtherIncomeEditSessionKey(horseId, incomeId);
  saveToSession(sessionKey, data);
};

/**
 * その他収入編集セッションデータを復元
 */
export const loadOtherIncomeEditSession = <T>(horseId: number, incomeId: number): T | null => {
  const sessionKey = getOtherIncomeEditSessionKey(horseId, incomeId);
  return loadFromSession<T>(sessionKey);
};

/**
 * その他収入編集セッションデータを削除
 */
export const removeOtherIncomeEditSession = (horseId: number, incomeId: number): void => {
  const sessionKey = getOtherIncomeEditSessionKey(horseId, incomeId);
  removeFromSession(sessionKey);
}; 