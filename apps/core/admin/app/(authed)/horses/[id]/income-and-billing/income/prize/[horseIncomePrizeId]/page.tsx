'use client';

import { useState, useEffect, use } from 'react';
import Link from 'next/link';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { getHorseIncomePrize } from '@core-admin/api_clients/horse_income_client';
import { getPrizeOrganizerDisplayLabel, formatCurrencyWithUnit } from '@core-admin/utils/income_utils';

interface HorseIncomePrizeDetailPageProps {
  params: Promise<{
    id: string;
    horseIncomePrizeId: string;
  }>;
}

interface PrizeData {
  id: number;
  incomeYearMonth: string;
  occurredYear: number;
  occurredMonth: number;
  occurredDay: number;
  racePlace: string;
  raceName: string;
  raceResult: string;
  organizer: string;
  mainPrizeAmount: number;
  allowances: Array<{ name: string; amount: number }>;
  appearanceFee: number;
  withholdingTax: number;
  commissionAmount: number;
  clubFeeRate: string;
  taxRate: string;
  totalPrizeAmount: number;
  clubFeeAmount: number;
  taxAmount: number;
  incomeAmount: number;
  note: string;
  closing: boolean;
}

export default function HorseIncomePrizeDetailPage({ params }: HorseIncomePrizeDetailPageProps) {
  const { id, horseIncomePrizeId } = use(params);
  const horseId = parseInt(id);
  const prizeId = parseInt(horseIncomePrizeId);
  const [prizeData, setPrizeData] = useState<PrizeData | null>(null);
  const [horseName, setHorseName] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let mounted = true;

    const fetchData = async () => {
      try {
        // 馬の情報を取得
        const horseResponse = await getHorse({ horseId });
        if (mounted) {
          setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');
        }

        // 賞金収入詳細を取得
        const prizeResponse = await getHorseIncomePrize({ id: prizeId });
        const prize = prizeResponse.horseIncomePrize;

        if (prize && mounted) {
          // 分配対象月のフォーマット
          const year = Math.floor(prize.incomeYearMonth / 100);
          const month = prize.incomeYearMonth % 100;
          const incomeYearMonth = `${year}年${month}月`;

          setPrizeData({
            id: prize.horseIncomePrizeId,
            incomeYearMonth,
            occurredYear: prize.occurredYear,
            occurredMonth: prize.occurredMonth,
            occurredDay: prize.occurredDay,
            racePlace: prize.racePlace,
            raceName: prize.raceName,
            raceResult: prize.raceResult,
            organizer: getPrizeOrganizerDisplayLabel(prize.organizer),
            mainPrizeAmount: prize.mainPrizeAmount,
            allowances:
              prize.allowances?.map((allowance) => ({
                name: allowance.name,
                amount: allowance.amount,
              })) || [],
            appearanceFee: prize.appearanceFee,
            withholdingTax: prize.withholdingTax,
            commissionAmount: prize.commissionAmount,
            clubFeeRate: prize.clubFeeRate,
            taxRate: prize.taxRate,
            totalPrizeAmount: prize.totalPrizeAmount,
            clubFeeAmount: prize.clubFeeAmount,
            taxAmount: prize.taxAmount,
            incomeAmount: prize.incomeAmount,
            note: prize.note,
            closing: prize.closing,
          });
        }
      } catch (error) {
        console.error('Failed to fetch data:', error);
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      mounted = false;
    };
  }, [horseId, prizeId]);

  if (isLoading) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-muted">データを読み込み中...</div>
        </div>
      </main>
    );
  }

  if (!prizeData) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-muted">データが見つかりません</div>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} 賞金収入詳細</h1>

        {/* 基本情報 */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-primary mb-4">基本情報</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">分配対象月</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{prizeData.incomeYearMonth}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">開催年月日</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {prizeData.occurredYear}年{prizeData.occurredMonth}月{prizeData.occurredDay}日
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">開催競馬場</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{prizeData.racePlace}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">レース名</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{prizeData.raceName}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">着順など</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{prizeData.raceResult}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 賞金・手当 */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-primary mb-4">賞金・手当</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">中央・その他</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{prizeData.organizer}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">本賞金</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{formatCurrencyWithUnit(prizeData.mainPrizeAmount)}</span>
                  </td>
                </tr>
                {prizeData.allowances.map((allowance, index) => (
                  <tr key={index} className="table-row">
                    <th className="table-header">
                      <span className="table-cell-text">{allowance.name}</span>
                    </th>
                    <td className="table-cell text-right">
                      <span className="table-cell-text">{formatCurrencyWithUnit(allowance.amount)}</span>
                    </td>
                  </tr>
                ))}
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">特別出走手当</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{formatCurrencyWithUnit(prizeData.appearanceFee)}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">JRA・地方競馬源泉所得税</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{formatCurrencyWithUnit(prizeData.withholdingTax)}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">進上金</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{formatCurrencyWithUnit(prizeData.commissionAmount)}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 設定 */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-primary mb-4">設定</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">クラブ法人手数料率</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{prizeData.clubFeeRate}%</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税率</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{prizeData.taxRate}%</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 備考 */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-primary mb-4">備考</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">備考</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{prizeData.note || 'なし'}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 計算結果 */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-primary mb-4">計算結果</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">賞金合計</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{formatCurrencyWithUnit(prizeData.totalPrizeAmount)}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">クラブ法人手数料</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{formatCurrencyWithUnit(prizeData.clubFeeAmount)}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{formatCurrencyWithUnit(prizeData.taxAmount)}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">収入金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg text-primary">{formatCurrencyWithUnit(prizeData.incomeAmount)}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* アクションボタン */}
        <div className="flex justify-center gap-4">
          <Link href={`/horses/${horseId}/income-and-billing/income`} className="btn-secondary">
            一覧に戻る
          </Link>

          {!prizeData.closing && (
            <>
              <Link href={`/horses/${horseId}/income-and-billing/income/prize/${prizeId}/edit?new=true`} className="btn-primary">
                編集
              </Link>
              <Link href={`/horses/${horseId}/income-and-billing/income/prize/${prizeId}/delete-confirm`} className="btn-danger">
                削除
              </Link>
            </>
          )}
        </div>
      </div>
    </main>
  );
}
