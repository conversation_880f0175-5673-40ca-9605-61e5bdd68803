import { ReactNode } from 'react';

interface FormFieldProps {
  label: string;
  children: ReactNode;
  required?: boolean;
  className?: string;
}

export default function FormField({ 
  label, 
  children, 
  required = false,
  className = ""
}: FormFieldProps) {
  return (
    <div className={`grid grid-cols-12 gap-4 items-center ${className}`}>
      <label className="col-span-2 text-sm font-medium">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <div className="col-span-6">
        {children}
      </div>
    </div>
  );
} 