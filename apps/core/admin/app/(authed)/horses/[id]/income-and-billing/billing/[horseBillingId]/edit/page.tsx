'use client';

import { getHorseBillingDetail } from '@core-admin/api_clients/horse_billing_client';
import { listBillers } from '@core-admin/api_clients/biller_client';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { HorseBillingItemType } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import type { Biller } from '@hami/core-admin-api-schema/biller_service_pb';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import HorseBillingForm, { type HorseBillingFormData } from '@core-admin/components/horse-billing/HorseBillingForm';

export default function EditHorseBillingPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const horseId = parseInt(params.id as string, 10);
  const billingId = parseInt(params.horseBillingId as string, 10);
  const SESSION_STORAGE_KEY = `horseBillingEditForm_${billingId}`;

  const [billers, setBillers] = useState<Biller[]>([]);
  const [horseName, setHorseName] = useState<string>('');
  const [formData, setFormData] = useState<HorseBillingFormData>({
    billingYearMonth: '',
    occurredYear: '',
    occurredMonth: '',
    occurredDay: '',
    billerId: '',
    itemType: HorseBillingItemType.UNSPECIFIED,
    itemTypeOther: '',
    billingAmount: '',
    taxAmount: '',
    subsidyAmount: '',
    note: '',
  });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (searchParams.get('reset') === '1') {
      sessionStorage.removeItem(SESSION_STORAGE_KEY);
      window.history.replaceState(null, '', window.location.pathname);
    }

    const savedData = sessionStorage.getItem(SESSION_STORAGE_KEY);

    Promise.all([
        listBillers({ pageSize: 1000 }),
        !savedData ? getHorseBillingDetail({ id: billingId }) : Promise.resolve(null),
        getHorse({ horseId }),
    ]).then(([billersRes, billingRes, horseRes]) => {
        setBillers(billersRes.billers);
        setHorseName(horseRes.horse?.horseName ?? '');
        if (savedData) {
            setFormData(JSON.parse(savedData));
        } else if (billingRes) {
            const b = billingRes.billing;
            if (!b) {
                setError('請求情報の取得に失敗しました。');
                return;
            }
            setFormData({
                billingYearMonth: b.billingYearMonth.toString(),
                occurredYear: b.occurredYear.toString(),
                occurredMonth: b.occurredMonth.toString(),
                occurredDay: b.occurredDay.toString(),
                billerId: b.billerId.toString(),
                itemType: b.itemType,
                itemTypeOther: b.itemTypeOther,
                billingAmount: b.billingAmount.toString(),
                taxAmount: b.taxAmount.toString(),
                subsidyAmount: b.subsidyAmount.toString(),
                note: b.note,
            });
        }
    }).catch(e => {
        setError(e instanceof Error ? e.message : 'データの読み込みに失敗しました');
    }).finally(() => {
        setIsLoading(false);
    });

  }, [horseId, billingId, searchParams, SESSION_STORAGE_KEY]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    let finalValue: string | number = value;
    if (type === 'select-one' && name === 'itemType') {
      finalValue = parseInt(value, 10);
    }
    setFormData((prev) => ({ ...prev, [name]: finalValue }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sessionStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(formData));
    router.push(`/horses/${horseId}/income-and-billing/billing/${billingId}/edit/confirm`);
  };

  if (isLoading) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-muted">読み込み中...</div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-error">{error}</div>
        </div>
      </main>
    );
  }

  return (
    <HorseBillingForm
      formData={formData}
      billers={billers}
      horseName={horseName}
      error={error}
      onSubmit={handleSubmit}
      onChange={handleChange}
      submitButtonText="確認画面へ"
      cancelHref={`/horses/${horseId}/income-and-billing/billing/${billingId}`}
      cancelButtonText="キャンセル"
    />
  );
} 