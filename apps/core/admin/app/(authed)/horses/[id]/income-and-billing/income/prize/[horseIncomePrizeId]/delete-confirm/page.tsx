'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { getHorseIncomePrize, deleteHorseIncomePrize } from '@core-admin/api_clients/horse_income_client';
import PrizeIncomeConfirmLayout from '@core-admin/components/common/PrizeIncomeConfirmLayout';

interface HorseIncomePrizeDeleteConfirmPageProps {
  params: Promise<{
    id: string;
    horseIncomePrizeId: string;
  }>;
}

interface AllowanceItem {
  name: string;
  amount: number;
}

interface HorseIncomePrizeData {
  horseIncomePrizeId: number;
  incomeYearMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  racePlace: string;
  raceName: string;
  raceResult: string;
  organizer: string;
  mainPrizeAmount: number;
  allowances: AllowanceItem[];
  appearanceFee: number;
  withholdingTax: number;
  commissionAmount: number;
  clubFeeRate: string;
  taxRate: string;
  totalPrizeAmount: number;
  clubFeeAmount: number;
  taxAmount: number;
  incomeAmount: number;
  note: string;
  closing: boolean;
}

export default function HorseIncomePrizeDeleteConfirmPage({ params }: HorseIncomePrizeDeleteConfirmPageProps) {
  const { id, horseIncomePrizeId } = use(params);
  const horseId = parseInt(id);
  const prizeId = horseIncomePrizeId;
  const router = useRouter();
  const [prizeData, setPrizeData] = useState<HorseIncomePrizeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [horseName, setHorseName] = useState<string>('');

  // 賞金収入データの取得
  useEffect(() => {
    const fetchPrizeData = async () => {
      try {
        const response = await getHorseIncomePrize({ id: parseInt(prizeId) });
        const apiData = response.horseIncomePrize;

        if (apiData) {
          // APIデータをコンポーネント用の形式に変換
          const formattedData: HorseIncomePrizeData = {
            horseIncomePrizeId: apiData.horseIncomePrizeId,
            incomeYearMonth: `${Math.floor(apiData.incomeYearMonth / 100)}年${apiData.incomeYearMonth % 100}月`,
            occurredYear: apiData.occurredYear.toString(),
            occurredMonth: apiData.occurredMonth.toString(),
            occurredDay: apiData.occurredDay.toString(),
            racePlace: apiData.racePlace,
            raceName: apiData.raceName,
            raceResult: apiData.raceResult,
            organizer: apiData.organizer === 1 ? 'JRA' : 'その他',
            mainPrizeAmount: apiData.mainPrizeAmount,
            allowances: apiData.allowances.map((allowance) => ({
              name: allowance.name,
              amount: allowance.amount,
            })),
            appearanceFee: apiData.appearanceFee,
            withholdingTax: apiData.withholdingTax,
            commissionAmount: apiData.commissionAmount,
            clubFeeRate: apiData.clubFeeRate,
            taxRate: apiData.taxRate,
            totalPrizeAmount: apiData.totalPrizeAmount,
            clubFeeAmount: apiData.clubFeeAmount,
            taxAmount: apiData.taxAmount,
            incomeAmount: apiData.incomeAmount,
            note: apiData.note,
            closing: apiData.closing,
          };

          setPrizeData(formattedData);
        }
      } catch (error) {
        console.error('データの取得に失敗しました:', error);
        alert('データの取得に失敗しました。');
        router.push(`/horses/${horseId}/income-and-billing/income`);
      } finally {
        setLoading(false);
      }
    };

    fetchPrizeData();
  }, [prizeId, horseId, router]);

  useEffect(() => {
    let mounted = true;

    const fetchHorseName = async () => {
      try {
        const horseResponse = await getHorse({ horseId });
        if (mounted) {
          setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');
        }
      } catch (error) {
        console.error('Failed to fetch horse name:', error);
        if (mounted) {
          setHorseName('');
        }
      }
    };

    if (horseId) {
      fetchHorseName();
    }

    return () => {
      mounted = false;
    };
  }, [horseId]);

  const handleBack = () => {
    router.push(`/horses/${horseId}/income-and-billing/income/prize/${prizeId}`);
  };

  const handleDelete = async () => {
    try {
      // 実際のAPI呼び出しを行う
      await deleteHorseIncomePrize({ id: parseInt(prizeId) });

      // 削除成功後、収入一覧画面に戻る
      router.push(`/horses/${horseId}/income-and-billing/income`);
    } catch (error) {
      console.error('削除に失敗しました:', error);
      alert('削除に失敗しました。もう一度お試しください。');
    }
  };

  if (loading) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">読み込み中...</div>
      </div>
    );
  }

  if (!prizeData) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">データが見つかりません。</div>
      </div>
    );
  }

  // Closingがtrueの場合は削除不可
  if (prizeData.closing) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="bg-error border border-error px-4 py-3 rounded">
            <strong className="text-error">エラー:</strong> この収入は締め設定されているため削除できません。
          </div>
          <div className="mt-4">
            <button onClick={handleBack} className="btn-secondary">
              戻る
            </button>
          </div>
        </div>
      </main>
    );
  }

  return (
    <PrizeIncomeConfirmLayout
      title="賞金収入削除確認"
      horseName={horseName}
      showWarning={true}
      warningMessage="この操作は元に戻すことができません。本当に削除してもよろしいですか？"
      basicInfo={{
        incomeYearMonth: prizeData.incomeYearMonth,
        occurredYear: prizeData.occurredYear,
        occurredMonth: prizeData.occurredMonth,
        occurredDay: prizeData.occurredDay,
        racePlace: prizeData.racePlace,
        raceName: prizeData.raceName,
        raceResult: prizeData.raceResult,
      }}
      prizeInfo={{
        organizer: prizeData.organizer,
        mainPrizeAmount: prizeData.mainPrizeAmount,
        allowances: prizeData.allowances,
        appearanceFee: prizeData.appearanceFee,
        withholdingTax: prizeData.withholdingTax,
        commissionAmount: prizeData.commissionAmount,
      }}
      settings={{
        clubFeeRate: prizeData.clubFeeRate,
        taxRate: prizeData.taxRate,
      }}
      note={prizeData.note}
      calculatedValues={{
        totalPrizeAmount: prizeData.totalPrizeAmount,
        clubFeeAmount: prizeData.clubFeeAmount,
        taxAmount: prizeData.taxAmount,
        incomeAmount: prizeData.incomeAmount,
      }}
      onBack={handleBack}
      onSubmit={handleDelete}
      submitButtonText="削除する"
      submitButtonVariant="danger"
    />
  );
}
