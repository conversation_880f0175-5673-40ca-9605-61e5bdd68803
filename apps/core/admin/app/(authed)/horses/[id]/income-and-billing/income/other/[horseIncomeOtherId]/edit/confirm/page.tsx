'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { getHorse } from '@core-admin/api_clients/horse_client';
import {
  getOtherIncomeNameLabel,
  calculateOtherIncomeAmounts,
  formatNumber as utilFormatNumber,
  getOtherIncomeNameEnumFromValue,
} from '@core-admin/utils/income_utils';
import { updateHorseIncomeOther } from '@core-admin/api_clients/horse_income_client';
import { getOtherIncomeEditSessionKey, restoreSessionData, clearSessionData } from '../../../../../_utils/session_utils';
import { parseNumber } from '@core-admin/utils/form_utils';

interface HorseIncomeOtherEditConfirmPageProps {
  params: Promise<{
    id: string;
    horseIncomeOtherId: string;
  }>;
}

interface FormData {
  incomeYear: string;
  incomeMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  name: string;
  nameOther: string;
  amount: string;
  salesCommission: string;
  otherFeeName: string;
  otherFeeAmount: string;
  taxRate: string;
  note: string;
}

export default function HorseIncomeOtherEditConfirmPage({ params }: HorseIncomeOtherEditConfirmPageProps) {
  const { id, horseIncomeOtherId } = use(params);
  const router = useRouter();
  const horseId = parseInt(id);
  const otherId = horseIncomeOtherId;
  const [formData, setFormData] = useState<FormData | null>(null);
  const [horseName, setHorseName] = useState<string>('');
  const [calculatedValues, setCalculatedValues] = useState({
    taxAmount: 0,
    incomeAmount: 0,
  });

  // セッションからデータを復元
  useEffect(() => {
    const sessionKey = getOtherIncomeEditSessionKey(horseId, parseInt(otherId));
    const savedData = restoreSessionData<FormData>(sessionKey);
    if (savedData) {
      setFormData(savedData);
    } else {
      router.push(`/horses/${horseId}/income-and-billing/income/other/${otherId}/edit`);
    }
  }, [horseId, otherId, router]);

  useEffect(() => {
    let mounted = true;

    const fetchHorseName = async () => {
      try {
        const horseResponse = await getHorse({ horseId });
        if (mounted) {
          setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');
        }
      } catch (error) {
        console.error('Failed to fetch horse name:', error);
        if (mounted) {
          setHorseName('');
        }
      }
    };

    if (horseId) {
      fetchHorseName();
    }

    return () => {
      mounted = false;
    };
  }, [horseId]);

  // 計算処理
  useEffect(() => {
    if (formData) {
      const amount = parseNumber(formData.amount);
      const salesCommission = parseNumber(formData.salesCommission);
      const otherFeeAmount = parseNumber(formData.otherFeeAmount);
      const taxRate = parseNumber(formData.taxRate);

      const { taxAmount, incomeAmount } = calculateOtherIncomeAmounts(amount, salesCommission, otherFeeAmount, taxRate);

      setCalculatedValues({
        taxAmount,
        incomeAmount,
      });
    }
  }, [formData]);

  const handleBack = () => {
    router.push(`/horses/${horseId}/income-and-billing/income/other/${otherId}/edit?from=confirm`);
  };

  const handleSubmit = async () => {
    try {
      if (!formData) return;

      // フォームデータをAPI仕様に合わせて変換
      const amount = parseFloat(formData.amount) || 0;
      const salesCommission = parseFloat(formData.salesCommission) || 0;
      const otherFeeAmount = parseFloat(formData.otherFeeAmount) || 0;

      // 年月を組み合わせて数値に変換
      const incomeYear = parseInt(formData.incomeYear);
      const incomeMonth = parseInt(formData.incomeMonth);

      if (!incomeYear || !incomeMonth || incomeMonth < 1 || incomeMonth > 12) {
        alert('分配対象月が正しく設定されていません。');
        return;
      }

      const incomeYearMonth = incomeYear * 100 + incomeMonth;

      // 名目をenum値に変換
      const nameEnum = getOtherIncomeNameEnumFromValue(formData.name);

      // API呼び出し用のデータを準備
      const updateData = {
        horseIncomeOtherId: parseInt(otherId),
        horseId: horseId,
        incomeYearMonth: incomeYearMonth,
        occurredYear: parseInt(formData.occurredYear),
        occurredMonth: parseInt(formData.occurredMonth),
        occurredDay: parseInt(formData.occurredDay),
        name: nameEnum,
        nameOther: formData.nameOther,
        amount: amount,
        salesCommission: salesCommission,
        otherFeeName: formData.otherFeeName,
        otherFeeAmount: otherFeeAmount,
        taxRate: formData.taxRate,
        taxAmount: calculatedValues.taxAmount,
        incomeAmount: calculatedValues.incomeAmount,
        note: formData.note,
      };

      // 実際のAPI呼び出しを行う
      await updateHorseIncomeOther(updateData);

      // セッションデータをクリア
      const sessionKey = getOtherIncomeEditSessionKey(horseId, parseInt(otherId));
      clearSessionData(sessionKey);

      // 成功メッセージ
      alert('その他収入を更新しました。');

      // 詳細画面に戻る
      router.push(`/horses/${horseId}/income-and-billing/income/other/${otherId}`);
    } catch (error) {
      console.error('更新に失敗しました:', error);
      alert('更新に失敗しました。もう一度お試しください。');
    }
  };

  if (!formData) {
    return (
      <div className="bg-white min-h-screen flex items-center justify-center">
        <div className="text-muted">読み込み中...</div>
      </div>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} その他収入編集確認</h1>

        {/* 基本情報 */}
        <div>
          <h3 className="text-lg font-medium text-primary mb-4">基本情報</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">分配対象月</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {formData.incomeYear}年{formData.incomeMonth}月
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">発生年月日</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {formData.occurredYear}年{formData.occurredMonth}月{formData.occurredDay}日
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">名目</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{getOtherIncomeNameLabel(formData.name)}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">名目（その他）</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{formData.nameOther || 'なし'}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 金額情報 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">金額情報</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{utilFormatNumber(parseFloat(formData.amount) || 0)}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">賞品売却手数料</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{utilFormatNumber(parseFloat(formData.salesCommission) || 0)}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">その他手数料名</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{formData.otherFeeName || 'なし'}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">その他手数料金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{utilFormatNumber(parseFloat(formData.otherFeeAmount) || 0)}円</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 設定 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">設定</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税率</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{formData.taxRate}%</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 備考 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">備考</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">備考</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{formData.note || 'なし'}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 計算結果 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">計算結果</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{utilFormatNumber(calculatedValues.taxAmount)}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">収入金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg text-primary">
                      {utilFormatNumber(calculatedValues.incomeAmount)}円
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* ボタン */}
        <div className="flex justify-center gap-4 mt-8">
          <button onClick={handleBack} className="btn-secondary">
            戻る
          </button>
          <button onClick={handleSubmit} className="btn-primary">
            編集する
          </button>
        </div>
      </div>
    </main>
  );
}
