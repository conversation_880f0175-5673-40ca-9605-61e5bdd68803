interface CalculationResultProps {
  title?: string;
  results: Array<{
    label: string;
    value: number;
    isHighlighted?: boolean;
  }>;
}

export default function CalculationResult({ 
  title = "計算結果", 
  results 
}: CalculationResultProps) {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded p-4">
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      <div className="space-y-4">
        {results.map((result, index) => (
          <div key={index} className="flex justify-between items-center">
            <span className={`text-lg ${result.isHighlighted ? 'font-bold' : 'font-medium'}`}>
              {result.label}
            </span>
            <span className={`text-xl ${result.isHighlighted ? 'font-bold text-blue-600' : 'font-bold'}`}>
              {result.value.toLocaleString()} 円
            </span>
          </div>
        ))}
      </div>
    </div>
  );
} 