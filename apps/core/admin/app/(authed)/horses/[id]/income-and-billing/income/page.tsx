'use client';

import { useState, useEffect, useCallback, use, useRef } from 'react';
import Link from 'next/link';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { listHorseIncomes } from '@core-admin/api_clients/horse_income_client';
import { type HorseIncome } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { getOtherIncomeNameLabel } from '@core-admin/utils/income_utils';

interface HorseIncomePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function HorseIncomePage({ params }: HorseIncomePageProps) {
  const { id } = use(params);
  const horseId = parseInt(id);
  const [horseName, setHorseName] = useState<string>('');
  const [incomes, setIncomes] = useState<HorseIncome[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filterYear, setFilterYear] = useState<string>('');
  const [filterMonth, setFilterMonth] = useState<string>('');
  const allIncomes = useRef<HorseIncome[]>([]);

  // データ取得
  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // 馬の情報を取得
      const horseResponse = await getHorse({ horseId });
      setHorseName(horseResponse?.horse?.horseName || horseResponse?.horse?.recruitmentName || '');

      // 収入データを取得
      const incomesResponse = await listHorseIncomes({ 
        horseId,
        limit: 100,
        offset: 0 
      });
      
      const allIncomesData = incomesResponse.horseIncomes || [];
      allIncomes.current = allIncomesData;
      setIncomes(allIncomesData);
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [horseId]);

  // 初回データ取得
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 絞り込み実行
  const handleFilter = () => {
    // 元のデータを保持するための状態を追加
    if (!allIncomes.current) {
      return;
    }
    
    const filteredIncomes = allIncomes.current.filter(income => {
      const year = Math.floor(income.incomeYearMonth / 100);
      const month = income.incomeYearMonth % 100;
      
      let matches = true;
      if (filterYear) {
        matches = matches && year === parseInt(filterYear);
      }
      if (filterMonth) {
        matches = matches && month === parseInt(filterMonth);
      }
      return matches;
    });
    
    setIncomes(filteredIncomes);
  };

  // 絞り込みクリア
  const handleClear = () => {
    setFilterYear('');
    setFilterMonth('');
    
    // 全データを再取得
    fetchData();
  };

  if (isLoading) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-muted">データを読み込み中...</div>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        {/* ヘッダー */}
        <div className="bg-surface-card border border-stroke-separator mb-8">
          <div className="p-6 border-b border-stroke-separator">
            <h1 className="text-2xl font-bold text-primary">
              {horseName} 収入一覧
            </h1>
          </div>
          
          {/* フィルター */}
          <div className="p-6 flex items-center gap-4">
            <div className="flex items-center gap-2">
              <input
                type="number"
                value={filterYear}
                onChange={(e) => setFilterYear(e.target.value)}
                className="input-base w-20 text-center"
                placeholder="年"
              />
              <span className="text-sm text-secondary">年</span>
              <input
                type="number"
                value={filterMonth}
                onChange={(e) => setFilterMonth(e.target.value)}
                className="input-base w-16 text-center"
                placeholder="月"
                min="1"
                max="12"
              />
              <span className="text-sm text-secondary">月</span>
            </div>
            <button 
              onClick={handleFilter}
              className="btn-primary"
            >
              絞り込み
            </button>
            <button 
              onClick={handleClear}
              className="btn-secondary"
            >
              クリア
            </button>
          </div>
        </div>

        {/* アクションボタン */}
        <div className="flex gap-4 mb-8">
          <Link
            href={`/horses/${horseId}/income-and-billing/income/prize/create?new=true`}
            className="btn-primary"
          >
            賞金収入を追加する
          </Link>
          <Link
            href={`/horses/${horseId}/income-and-billing/income/other/create?new=true`}
            className="btn-primary"
          >
            その他の収入を追加する
          </Link>
        </div>

        {/* 収入一覧テーブル */}
        <div className="bg-surface-card border border-stroke-separator overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="table-header">
                <th>締め済</th>
                <th>分配対象月</th>
                <th>開催年月日</th>
                <th>種別</th>
                <th>名目</th>
                <th>収入金額</th>
                <th>詳細</th>
              </tr>
            </thead>
            <tbody>
              {incomes.length === 0 ? (
                <tr className="table-row">
                  <td colSpan={7} className="table-cell text-center">
                    <span className="table-cell-text text-muted">
                      {filterYear || filterMonth ? '指定した条件に該当する収入データがありません' : '収入データがありません'}
                    </span>
                  </td>
                </tr>
              ) : (
                incomes.map((income) => {
                  const incomeType = income.incomeType === 1 ? 'prize' : 'other';
                  const categoryName = income.incomeType === 1 ? '賞金' : 'その他';
                  const occurredDate = `${income.occurredYear}/${income.occurredMonth.toString().padStart(2, '0')}/${income.occurredDay.toString().padStart(2, '0')}`;
                  
                  // 名目の表示名を決定
                  let displayName = '';
                  if (incomeType === 'prize') {
                    // 賞金収入の場合：nameにraceName、name2にraceResultが入る
                    displayName = income.name || '';
                  } else {
                    // その他収入の場合
                    if (income.name2 && income.name2.trim() !== '') {
                      // nameOtherが設定されている場合はそれを使用
                      displayName = income.name2;
                    } else {
                      // nameOtherが空の場合はenum値を日本語ラベルに変換
                      displayName = getOtherIncomeNameLabel(income.name || '');
                    }
                  }
                  
                  return (
                    <tr key={`${income.incomeType}-${income.id}`} className="table-row">
                      <td className="table-cell text-center">
                        {income.closing && (
                          <div className="w-4 h-4 bg-primary rounded-full mx-auto"></div>
                        )}
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">
                          {income.incomeYearMonth ? `${Math.floor(income.incomeYearMonth / 100)} / ${income.incomeYearMonth % 100}` : ''}
                        </span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">{occurredDate}</span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">{categoryName}</span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">{displayName}</span>
                      </td>
                      <td className="table-cell text-right">
                        <span className="table-cell-text text-right">
                          {income.amount > 0 ? income.amount.toLocaleString() : ''}
                        </span>
                      </td>
                      <td className="table-cell text-center">
                        <Link
                          href={
                            incomeType === 'prize'
                              ? `/horses/${horseId}/income-and-billing/income/prize/${income.id}`
                              : `/horses/${horseId}/income-and-billing/income/other/${income.id}`
                          }
                        >
                          <button className="btn-secondary text-sm">詳細</button>
                        </Link>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>
    </main>
  );
} 