'use client';

import { deleteHorseBilling, getHorseBillingDetail } from '@core-admin/api_clients/horse_billing_client';
import { getHorse } from '@core-admin/api_clients/horse_client';
import { getBiller } from '@core-admin/api_clients/biller_client';
import { useRouter, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { type HorseBillingItem } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import HorseBillingDeleteConfirm from '@core-admin/components/horse-billing/HorseBillingDeleteConfirm';

export default function DeleteHorseBillingPage() {
  const router = useRouter();
  const params = useParams();
  const horseId = parseInt(params.id as string, 10);
  const billingId = parseInt(params.horseBillingId as string, 10);

  const [billing, setBilling] = useState<HorseBillingItem | null>(null);
  const [billerName, setBillerName] = useState('');
  const [_horseName, setHorseName] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    getHorseBillingDetail({ id: billingId })
        .then(async (billingRes) => {
            const billingData = billingRes.billing;
            if (!billingData) {
                setError('請求情報の取得に失敗しました。');
                setIsLoading(false);
                return;
            }
            setBilling(billingData);

            const [horseRes, billerRes] = await Promise.all([
                getHorse({ horseId }),
                getBiller({ id: billingData.billerId })
            ]);

            setHorseName(horseRes.horse?.horseName ?? '');
            setBillerName(billerRes.biller?.name ?? '不明');
            setIsLoading(false);
        })
        .catch(e => {
            setError(e instanceof Error ? e.message : 'エラーが発生しました');
            setIsLoading(false);
        });
  }, [billingId, horseId]);

  const handleDelete = async () => {
    setIsSubmitting(true);
    setError(null);
    try {
      await deleteHorseBilling({ id: billingId });
      router.push(`/horses/${horseId}/income-and-billing/billing`);
    } catch (e) {
      setError(e instanceof Error ? e.message : '削除に失敗しました。');
      setIsSubmitting(false);
    }
  };
  
  if (isLoading) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-muted">読み込み中...</div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-error">{error}</div>
        </div>
      </main>
    );
  }

  if (!billing) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center text-error">請求情報が見つかりません。</div>
        </div>
      </main>
    );
  }

  return (
    <HorseBillingDeleteConfirm
      billing={billing}
      billerName={billerName}
      isSubmitting={isSubmitting}
      onCancel={() => router.push(`/horses/${horseId}/income-and-billing/billing/${billingId}`)}
      onDelete={handleDelete}
    />
  );
} 