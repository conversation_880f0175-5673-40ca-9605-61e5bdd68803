import { getHorse } from '@core-admin/api_clients/horse_client';
import { getCombinedStatusLabel } from '../_utils/statusUtils';
import Link from 'next/link';
import { notFound } from 'next/navigation';

export const metadata = {
  robots: 'noindex, nofollow',
};

export const dynamic = 'force-dynamic';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function HorseDetailPage({ params }: PageProps) {
  const resolvedParams = await params;
  const horseId = parseInt(resolvedParams.id);

  if (isNaN(horseId)) {
    notFound();
  }

  try {
    const response = await getHorse({ horseId });
    const horse = response.horse;

    if (!horse) {
      notFound();
    }

    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6">
          <div className="flex justify-center gap-4 my-8">
            <Link
              href={`/horses/${horseId}/investors`}
              className="btn-primary"
            >
              出資者管理
            </Link>
            <Link
              href={`/horses/${horseId}/income-and-billing`}
              className="btn-primary"
            >
              収入と支出管理
            </Link>
            <Link
              href={`/horses/${horseId}/investment-and-return`}
              className="btn-primary"
            >
              出資・分配金作成履歴
            </Link>
          </div>

          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">馬ID</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{horse.horseId}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">募集年</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{horse.recruitmentYear}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">募集番号</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{horse.recruitmentNo.toString().padStart(3, '0')}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">募集馬名</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{horse.recruitmentName}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">募集口数</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{horse.sharesTotal}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">募集総額（円）</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{horse.amountTotal.toLocaleString()}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">馬名</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{horse.horseName || '未設定'}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">生年月日</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {horse.birthYear}年{horse.birthMonth}月{horse.birthDay}日
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">ファンド開始年月日</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {horse.fundStartYear}年{horse.fundStartMonth}月{horse.fundStartDay}日
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">利益相反該当馬</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{horse.conflictOfInterest ? 'はい' : 'いいえ'}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">備考</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text whitespace-pre-wrap">{horse.note}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <td className="table-cell bg-shark-100 text-secondary font-bold">
                    <span className="table-cell-text">運用ステータス</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{getCombinedStatusLabel(horse.publishStatus, horse.recruitmentStatus)}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div className="flex justify-center gap-4 mt-8">
            <Link
              href="/horses"
              className="btn-secondary"
            >
              馬管理へ戻る
            </Link>
            <Link
              href={`/horses/${horseId}/edit`}
              className="btn-primary"
            >
              編集する
            </Link>
            <Link
              href={`/horses/${horseId}/delete-confirm`}
              className="btn-danger"
            >
              削除する
            </Link>
          </div>
        </div>
      </main>
    );
  } catch (error) {
    console.error('Error fetching horse details:', error);
    notFound();
  }
}
