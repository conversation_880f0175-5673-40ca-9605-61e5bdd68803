'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState, use } from 'react';
import { updateHorse } from '@core-admin/api_clients/horse_client';
import HorseConfirmDisplay from '../../../_components/HorseConfirmDisplay';
import { HorseFormData } from '../../../_components/HorseForm';

interface EditHorseData extends HorseFormData {
  horseId: number;
}

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditHorseConfirmPage({ params }: PageProps) {
  const router = useRouter();
  const [editData, setEditData] = useState<EditHorseData | null>(null);
  const [_isLoading, setIsLoading] = useState(false);
  const resolvedParams = use(params);
  const horseId = parseInt(resolvedParams.id);

  useEffect(() => {
    const savedData = sessionStorage.getItem('editHorseData');
    if (savedData) {
      const data = JSON.parse(savedData) as EditHorseData;
      // horseIdが一致することを確認
      if (data.horseId === horseId) {
        setEditData(data);
      } else {
        router.push(`/horses/${horseId}/edit`);
      }
    } else {
      router.push(`/horses/${horseId}/edit`);
    }
  }, [router, horseId]);

  const handleConfirm = async () => {
    if (!editData) return;

    setIsLoading(true);
    try {
      await updateHorse({
        horseId: editData.horseId,
        recruitmentName: editData.recruitmentName,
        horseName: editData.horseName,
        birthYear: editData.birthYear || new Date().getFullYear() - 3,
        birthMonth: editData.birthMonth || 1,
        birthDay: editData.birthDay || 1,
        sharesTotal: editData.sharesTotal,
        amountTotal: editData.amountTotal,
        note: editData.note,
        fundStartYear: editData.fundStartYear || new Date().getFullYear(),
        fundStartMonth: editData.fundStartMonth || 1,
        fundStartDay: editData.fundStartDay || 1,
        conflictOfInterest: editData.conflictOfInterest,
      });
      
      sessionStorage.removeItem('editHorseData');
      alert('馬の情報を更新しました');
      router.push(`/horses/${horseId}`);
    } catch (error) {
      console.error('Error updating horse:', error);
      alert('馬の更新に失敗しました');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/horses/${horseId}/edit`);
  };

  if (!editData) {
    return <div>Loading...</div>;
  }

  return (
    <HorseConfirmDisplay
      data={editData}
      title="馬編集確認"
      message="この内容で編集します。"
      onConfirm={handleConfirm}
      onCancel={handleCancel}
      confirmButtonText="編集する"
      confirmButtonColor="secondary"
      horseId={editData.horseId}
    />
  );
}
