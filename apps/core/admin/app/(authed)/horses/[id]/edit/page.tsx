'use client';

import { useRouter } from 'next/navigation';
import { useState, useEffect, use } from 'react';
import { getHorse } from '@core-admin/api_clients/horse_client';
import HorseForm, { HorseFormData } from '../../_components/HorseForm';
import { saveToSession, loadFromSession, removeFromSession } from '@core-admin/utils/session_utils';

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditHorsePage({ params }: PageProps) {
  const router = useRouter();
  const [_isLoading, setIsLoading] = useState(false);
  const [initialData, setInitialData] = useState<HorseFormData | null>(null);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const resolvedParams = use(params);
  const horseId = parseInt(resolvedParams.id);

  useEffect(() => {
    if (isNaN(horseId)) {
      setError('無効な馬IDです');
      setIsDataLoading(false);
      return;
    }

    const fetchHorseData = async () => {
      try {
        // まずsessionStorageから保存されたデータがあるかチェック
        const savedData = loadFromSession<HorseFormData & {horseId: number}>('editHorseData');
        if (savedData) {
          // horseIdが一致することを確認
          if (savedData.horseId === horseId) {
            // sessionStorageのデータを使用（horseIdを除去）
            const { horseId: _, ...formData } = savedData;
            setInitialData(formData);
            setIsDataLoading(false);
            return;
          }
        }

        // sessionStorageにデータがない場合は通常通りAPIから取得
        const response = await getHorse({ horseId });
        const horse = response.horse;
        
        if (!horse) {
          setError('馬が見つかりません');
          return;
        }

        setInitialData({
          recruitmentYear: horse.recruitmentYear,
          recruitmentNo: horse.recruitmentNo,
          recruitmentName: horse.recruitmentName,
          horseName: horse.horseName,
          birthYear: horse.birthYear,
          birthMonth: horse.birthMonth,
          birthDay: horse.birthDay,
          sharesTotal: horse.sharesTotal,
          amountTotal: horse.amountTotal,
          fundStartYear: horse.fundStartYear,
          fundStartMonth: horse.fundStartMonth,
          fundStartDay: horse.fundStartDay,
          note: horse.note,
          conflictOfInterest: horse.conflictOfInterest,
        });
      } catch (error) {
        console.error('Error fetching horse data:', error);
        setError('馬の情報を取得できませんでした');
      } finally {
        setIsDataLoading(false);
      }
    };

    fetchHorseData();
  }, [horseId]);

  const handleSubmit = async (data: HorseFormData) => {
    setIsLoading(true);
    try {
      // フォームデータとhorseIdをセッションストレージに保存して確認画面へ遷移
      saveToSession('editHorseData', { ...data, horseId });
      router.push(`/horses/${horseId}/edit/confirm`);
    } catch (error) {
      console.error('Error saving form data:', error);
      alert('エラーが発生しました');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // キャンセル時はsessionStorageをクリア
    removeFromSession('editHorseData');
    router.push(`/horses/${horseId}`);
  };

  if (error) {
    return (
      <main className="max-w-4xl mx-auto p-6">
        <div className="bg-gray-200 text-center py-4 mb-6">
          <h1 className="text-xl font-bold">馬編集</h1>
        </div>
        <div className="text-center text-red-500">
          <p>{error}</p>
          <button
            onClick={() => router.push('/horses')}
            className="mt-4 px-6 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          >
            馬一覧に戻る
          </button>
        </div>
      </main>
    );
  }

  if (isDataLoading) {
    return (
      <main className="max-w-4xl mx-auto p-6">
        <div className="bg-gray-200 text-center py-4 mb-6">
          <h1 className="text-xl font-bold">馬編集</h1>
        </div>
        <div className="text-center">Loading...</div>
      </main>
    );
  }

  if (!initialData) {
    return null;
  }

  return (
    <main className="max-w-4xl mx-auto p-6">
      <div className="bg-gray-200 text-center py-4 mb-6">
        <h1 className="text-xl font-bold">馬編集</h1>
      </div>

      <div className="mb-4">
        <span className="text-lg font-medium">馬ID: {horseId}</span>
      </div>

      <HorseForm
        initialData={initialData}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        submitButtonText="内容を確認する"
        isEdit={true}
      />
    </main>
  );
}
