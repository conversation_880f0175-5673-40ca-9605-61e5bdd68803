"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { getHorse } from "@core-admin/api_clients/horse_client";
import { getCreatedInvestmentAndReturnSharesSumByHorseId, createInvestmentAndReturn } from "@core-admin/api_clients/investment_and_return_client";
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';

export default function InvestmentAndReturnSharesSumPage() {
  const { id } = useParams();
  const idStr = Array.isArray(id) ? id[0] : id;
  const horseId = idStr ? Number(idStr) : undefined;
  const [horseName, setHorseName] = useState("");
  const [sharesList, setSharesList] = useState<{ createdDate: string; sharesSum: number }[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [yearlyReturnTargetFlag, setYearlyReturnTargetFlag] = useState(false);
  const [retirementFlag, setRetirementFlag] = useState(false);
  const [targetYear, setTargetYear] = useState(new Date().getFullYear());
  const [targetMonth, setTargetMonth] = useState(new Date().getMonth() + 1);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const horseRes = await getHorse({ horseId });
        setHorseName(horseRes.horse?.horseName || horseRes.horse?.recruitmentName || "");
        if (horseId === undefined) throw new Error('馬IDが不正です');
        const res = await getCreatedInvestmentAndReturnSharesSumByHorseId({ horseId });
        setSharesList(res.list || []);
      } catch (_e) {
        setError('データの取得に失敗しました');
      } finally {
        setLoading(false);
      }
    })();
  }, [horseId]);

  const breadcrumbItems = [
    { label: '馬管理', href: '/horses' },
    { label: horseName ? `${horseName}` : '馬詳細', href: `/horses/${horseId}` },
    { label: '出資・分配金作成履歴', isCurrent: true }
  ];

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-error text-center mt-8">{error}</div>;

  return (
    <main className="p-6 bg-white">
      <Breadcrumb items={breadcrumbItems} />
      <div className="flex flex-col items-end gap-4 mb-6">
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">対象年:</label>
            <select
              value={targetYear}
              onChange={(e) => setTargetYear(Number(e.target.value))}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {Array.from({ length: 15 }, (_, i) => new Date().getFullYear() - 10 + i).map(year => (
                <option key={year} value={year}>{year}年</option>
              ))}
            </select>
          </div>
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">対象月:</label>
            <select
              value={targetMonth}
              onChange={(e) => setTargetMonth(Number(e.target.value))}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                <option key={month} value={month}>{month}月</option>
              ))}
            </select>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-1 text-primary">
            <input
              type="checkbox"
              checked={yearlyReturnTargetFlag}
              onChange={e => setYearlyReturnTargetFlag(e.target.checked)}
            />
            年次分配対象
          </label>
          <label className="flex items-center gap-1 text-primary">
            <input
              type="checkbox"
              checked={retirementFlag}
              onChange={e => setRetirementFlag(e.target.checked)}
            />
            引退精算
          </label>
        </div>
        <button
          className="btn-primary"
          onClick={async () => {
            try {
              if (!horseId) throw new Error('馬IDが不正です');
              await createInvestmentAndReturn({ 
                horseId, 
                targetYear, 
                targetMonth, 
                yearlyReturnTargetFlag, 
                retirementFlag 
              });
              alert('作成しました');
              window.location.reload();
            } catch (_e) {
              alert('作成に失敗しました');
            }
          }}
        >
          出資・分配金作成
        </button>
      </div>
      <h1 className="text-2xl font-bold text-primary text-center mb-6">出資・分配金作成履歴</h1>
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm mb-8 max-w-3xl mx-auto">
        <table className="w-full">
          <thead>
            <tr className="table-header">
              <th className="text-center">作成日</th>
              <th className="text-center">合計口数</th>
            </tr>
          </thead>
          <tbody>
            {sharesList.length === 0 ? (
              <tr className="table-row">
                <td className="table-cell text-center" colSpan={2}>
                  <span className="table-cell-text text-muted">データがありません</span>
                </td>
              </tr>
            ) : (
              sharesList.map((item) => {
                const date = new Date(item.createdDate);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                
                return (
                  <tr key={item.createdDate} className="table-row">
                    <td className="table-cell text-center">
                      <span className="table-cell-text">{date.toLocaleDateString('ja-JP', { year: 'numeric', month: '2-digit', day: '2-digit' })}</span>
                    </td>
                    <td className="table-cell text-center">
                      <Link 
                        href={`/horses/${horseId}/investment-and-return/${year}/${month}/${day}`}
                        className="table-cell-text text-primary hover:text-primary-dark underline"
                      >
                        {item.sharesSum}
                      </Link>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </main>
  );
}
