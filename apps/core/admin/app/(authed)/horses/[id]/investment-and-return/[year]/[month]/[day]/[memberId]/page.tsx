"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { getHorse } from "@core-admin/api_clients/horse_client";
import { getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId } from "@core-admin/api_clients/investment_and_return_client";
import { getMember } from "@core-admin/api_clients/member_client";
import { Member } from '@hami/core-admin-api-schema/member_service_pb';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { formatCurrency } from '@core-admin/utils/income_utils';

type InvestmentAndReturn = {
  investmentAndReturnId: number;
  horseId: number;
  memberId: number;
  createdDateYear: number;
  createdDateMonth: number;
  createdDateDay: number;
  progressedMonth: number;
  yearlyReturnTargetFlag: boolean;
  sharesNumber: number;
  billingAmount: number;
  paymentAmount: number;
  investment?: {
    investmentAndReturnInvestmentId: number;
    investmentAndReturnId: number;
    racehorseInvestmentEquivalent: number;
    discountAllocation: number;
    racehorseInvestment: number;
    runningCost: number;
    subsidy: number;
    retroactiveRunningCost: number;
    runningCostInvestment: number;
    insuranceInvestment: number;
    otherInvestment: number;
    currentMonthInvestmentTotal: number;
  };
  returns: Array<{
    investmentAndReturnReturnId: number;
    investmentAndReturnId: number;
    returnCategory: string;
    investmentRefundPaidUpToLastMonth?: number;
    refundableInvestmentAmount: number;
    distributionTargetAmount: number;
    distributionTargetAmountRefundable: number;
    distributionTargetAmountProfit: number;
    distributionTargetAmountWithholdingTax: number;
    distributionAmount: number;
    refundableInvestmentAmountCarriedForward: number;
  }>;
};

export default function InvestmentAndReturnMemberDetailPage() {
  const { id, year, month, day, memberId } = useParams();
  const idStr = Array.isArray(id) ? id[0] : id;
  const yearStr = Array.isArray(year) ? year[0] : year;
  const monthStr = Array.isArray(month) ? month[0] : month;
  const dayStr = Array.isArray(day) ? day[0] : day;
  const memberIdStr = Array.isArray(memberId) ? memberId[0] : memberId;
  
  const horseId = idStr ? Number(idStr) : undefined;
  const createdDateYear = yearStr ? Number(yearStr) : undefined;
  const createdDateMonth = monthStr ? Number(monthStr) : undefined;
  const createdDateDay = dayStr ? Number(dayStr) : undefined;
  const memberIdNum = memberIdStr ? Number(memberIdStr) : undefined;
  
  const [horseName, setHorseName] = useState("");
  const [member, setMember] = useState<Member | null>(null);
  const [investmentAndReturn, setInvestmentAndReturn] = useState<InvestmentAndReturn | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        if (!horseId || !createdDateYear || !createdDateMonth || !createdDateDay || !memberIdNum) {
          throw new Error('パラメータが不正です');
        }

        const [horseRes, memberRes, investmentRes] = await Promise.all([
          getHorse({ horseId }),
          getMember({ memberId: memberIdNum }),
          getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId({ 
            horseId, 
            createdDateYear, 
            createdDateMonth, 
            createdDateDay,
            memberId: memberIdNum
          })
        ]);
        
        setHorseName(horseRes.horse?.horseName || horseRes.horse?.recruitmentName || "");
        setMember(memberRes.member || null);
        setInvestmentAndReturn(investmentRes.investmentAndReturn || null);
      } catch (_e) {
        setError('データの取得に失敗しました');
      } finally {
        setLoading(false);
      }
    })();
  }, [horseId, createdDateYear, createdDateMonth, createdDateDay, memberIdNum]);

  const breadcrumbItems = [
    { label: '馬管理', href: '/horses' },
    { label: horseName ? `${horseName}` : '馬詳細', href: `/horses/${horseId}` },
    { label: '出資・分配金作成履歴', href: `/horses/${horseId}/investment-and-return` },
    { label: `${createdDateYear}/${createdDateMonth}/${createdDateDay}`, href: `/horses/${horseId}/investment-and-return/${createdDateYear}/${createdDateMonth}/${createdDateDay}` },
    { label: `${member?.lastName} ${member?.firstName}`, isCurrent: true }
  ];

  const formatReturnCategory = (category: string): string => {
    switch (category) {
      case 'CLUB_TO_FUND_MONTHLY':
        return 'クラブ法人から愛馬会法人への分配金（月次）';
      case 'FUND_TO_MEMBER_MONTHLY':
        return '愛馬会法人から会員への分配金（月次）';
      case 'FUND_TO_MEMBER_YEARLY_ORGANIZER':
        return '愛馬会法人から会員への分配金（主催者源泉税）';
      case 'FUND_TO_MEMBER_YEARLY_CLUB':
        return '愛馬会法人から会員への分配金（クラブ法人源泉税）';
      default:
        return category;
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-error text-center mt-8">{error}</div>;
  if (!investmentAndReturn) return <div className="text-error text-center mt-8">データが見つかりません</div>;

  return (
    <main className="p-6 bg-white">
      <Breadcrumb items={breadcrumbItems} />
      
      {/* 会員情報 */}
      {member && (
        <h1 className="text-2xl font-bold text-primary text-center mb-6">
        馬ごと出資・分配金詳細　／　{member.lastName} {member.firstName}（{member.memberNumber}）
        </h1>
      )}
      
      {/* 基本情報 */}
      <div className="bg-surface-card border border-stroke-separator rounded-lg p-6 mb-8 max-w-4xl mx-auto">
        <h2 className="text-lg font-medium text-primary mb-4">基本情報</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-sm font-medium text-secondary">作成日:</span>
            <span className="ml-2">{investmentAndReturn.createdDateYear}/{investmentAndReturn.createdDateMonth}/{investmentAndReturn.createdDateDay}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-secondary">出資口数</span>
            <span className="ml-2">{investmentAndReturn.sharesNumber}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-secondary">進捗月:</span>
            <span className="ml-2">{investmentAndReturn.progressedMonth}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-secondary">年次分配対象:</span>
            <span className="ml-2">{investmentAndReturn.yearlyReturnTargetFlag ? '○' : '×'}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-secondary">請求金額:</span>
            <span className="ml-2 font-medium">{formatCurrency(investmentAndReturn.billingAmount)}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-secondary">支払金額:</span>
            <span className="ml-2 font-medium">{formatCurrency(investmentAndReturn.paymentAmount)}</span>
          </div>
        </div>
      </div>

      {/* 投資詳細 */}
      {investmentAndReturn.investment && (
        <div className="bg-surface-card border border-stroke-separator rounded-lg p-6 mb-8 max-w-4xl mx-auto">
          <h2 className="text-lg font-medium text-primary mb-4">出資金請求金額の計算</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-secondary">競走馬出資金相当額:</span>
              <span className="ml-2">{formatCurrency(investmentAndReturn.investment.racehorseInvestmentEquivalent)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-secondary">割引充当額:</span>
              <span className="ml-2">{formatCurrency(investmentAndReturn.investment.discountAllocation)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-secondary">競走馬出資金（税込）:</span>
              <span className="ml-2">{formatCurrency(investmentAndReturn.investment.racehorseInvestment)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-secondary">維持費:</span>
              <span className="ml-2">{formatCurrency(investmentAndReturn.investment.runningCost)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-secondary">補助金等:</span>
              <span className="ml-2">{formatCurrency(investmentAndReturn.investment.subsidy)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-secondary">維持費（遡り）:</span>
              <span className="ml-2">{formatCurrency(investmentAndReturn.investment.retroactiveRunningCost)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-secondary">維持費出資金:</span>
              <span className="ml-2">{formatCurrency(investmentAndReturn.investment.runningCostInvestment)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-secondary">保険料出資金:</span>
              <span className="ml-2">{formatCurrency(investmentAndReturn.investment.insuranceInvestment)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-secondary">その他出資金:</span>
              <span className="ml-2">{formatCurrency(investmentAndReturn.investment.otherInvestment)}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-secondary">当月出資金合計額:</span>
              <span className="ml-2 font-medium">{formatCurrency(investmentAndReturn.investment.currentMonthInvestmentTotal)}</span>
            </div>
          </div>
        </div>
      )}

      {/* 分配金詳細 */}
      {investmentAndReturn.returns.length > 0 && (
        <div className="bg-surface-card border border-stroke-separator rounded-lg p-6 mb-8 max-w-4xl mx-auto">
          <h2 className="text-lg font-medium text-primary mb-4">分配金支払金額の計算</h2>
          <div className="space-y-4">
            {investmentAndReturn.returns.map((returnItem, _index) => (
              <div key={returnItem.investmentAndReturnReturnId} className="border border-stroke-separator rounded p-4">
                <h3 className="font-medium text-primary mb-2">分配金- {formatReturnCategory(returnItem.returnCategory)}</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-secondary">前月までの出資返戻済金額:</span>
                    <span className="ml-2">{returnItem.investmentRefundPaidUpToLastMonth ? formatCurrency(returnItem.investmentRefundPaidUpToLastMonth) : 'なし'}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-secondary">出資返戻可能額:</span>
                    <span className="ml-2">{formatCurrency(returnItem.refundableInvestmentAmount)}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-sm font-medium text-secondary">分配対象額:</span>
                    <span className="ml-2">{formatCurrency(returnItem.distributionTargetAmount)}</span>
                    <div className="ml-4 mt-2 space-y-1">
                      <div className="text-sm">
                        <span className="text-secondary">うち出資返戻可能額:</span>
                        <span className="ml-2">{formatCurrency(returnItem.distributionTargetAmountRefundable)}</span>
                      </div>
                      <div className="text-sm">
                        <span className="text-secondary">うち利益分配額:</span>
                        <span className="ml-2">{formatCurrency(returnItem.distributionTargetAmountProfit)}</span>
                      </div>
                      <div className="text-sm">
                        <span className="text-secondary">源泉徴収税:</span>
                        <span className="ml-2">{formatCurrency(returnItem.distributionTargetAmountWithholdingTax)}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-secondary">分配額:</span>
                    <span className="ml-2 font-medium">{formatCurrency(returnItem.distributionAmount)}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-secondary">出資返戻可能額（繰越）:</span>
                    <span className="ml-2">{formatCurrency(returnItem.refundableInvestmentAmountCarriedForward)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 戻るボタン */}
      <div className="text-center">
        <Link
          href={`/horses/${horseId}/investment-and-return/${createdDateYear}/${createdDateMonth}/${createdDateDay}`}
          className="btn-secondary"
        >
          一覧に戻る
        </Link>
      </div>
    </main>
  );
}
