"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { getHorse } from "@core-admin/api_clients/horse_client";
import { getInvestmentAndReturnListByHorseIdAndCreatedDate } from "@core-admin/api_clients/investment_and_return_client";
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';

type InvestmentAndReturnListItem = {
  investmentAndReturnId: number;
  createdDateYear: number;
  createdDateMonth: number;
  createdDateDay: number;
  progressedMonth: number;
  yearlyReturnTargetFlag: boolean;
  billingAmount: number;
  paymentAmount: number;
  memberId: number;
  memberName: string;
};

export default function InvestmentAndReturnDetailPage() {
  const { id, year, month, day } = useParams();
  const idStr = Array.isArray(id) ? id[0] : id;
  const yearStr = Array.isArray(year) ? year[0] : year;
  const monthStr = Array.isArray(month) ? month[0] : month;
  const dayStr = Array.isArray(day) ? day[0] : day;
  
  const horseId = idStr ? Number(idStr) : undefined;
  const createdDateYear = yearStr ? Number(yearStr) : undefined;
  const createdDateMonth = monthStr ? Number(monthStr) : undefined;
  const createdDateDay = dayStr ? Number(dayStr) : undefined;
  
  const [horseName, setHorseName] = useState("");
  const [investmentList, setInvestmentList] = useState<InvestmentAndReturnListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        if (!horseId || !createdDateYear || !createdDateMonth || !createdDateDay) {
          throw new Error('パラメータが不正です');
        }

        const horseRes = await getHorse({ horseId });
        setHorseName(horseRes.horse?.horseName || horseRes.horse?.recruitmentName || "");
        
        const res = await getInvestmentAndReturnListByHorseIdAndCreatedDate({ 
          horseId, 
          createdDateYear, 
          createdDateMonth, 
          createdDateDay 
        });
        setInvestmentList(res.list || []);
      } catch (_e) {
        setError('データの取得に失敗しました');
      } finally {
        setLoading(false);
      }
    })();
  }, [horseId, createdDateYear, createdDateMonth, createdDateDay]);

  const breadcrumbItems = [
    { label: '馬管理', href: '/horses' },
    { label: horseName ? `${horseName}` : '馬詳細', href: `/horses/${horseId}` },
    { label: '出資・分配金作成履歴', href: `/horses/${horseId}/investment-and-return` },
    { label: `${createdDateYear}/${createdDateMonth}/${createdDateDay}`, isCurrent: true }
  ];

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-error text-center mt-8">{error}</div>;

  return (
    <main className="p-6 bg-white">
      <Breadcrumb items={breadcrumbItems} />
      <h1 className="text-2xl font-bold text-primary text-center mb-6">
        出資・分配金詳細 - {createdDateYear}/{createdDateMonth}/{createdDateDay}
      </h1>
      
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm mb-8 max-w-6xl mx-auto">
        <table className="w-full">
          <thead>
            <tr className="table-header">
              <th className="text-center">会員ID</th>
              <th className="text-center">会員名</th>
              <th className="text-center">進捗月</th>
              <th className="text-center">年次分配対象</th>
              <th className="text-center">請求金額</th>
              <th className="text-center">支払金額</th>
            </tr>
          </thead>
          <tbody>
            {investmentList.length === 0 ? (
              <tr className="table-row">
                <td className="table-cell text-center" colSpan={6}>
                  <span className="table-cell-text text-muted">データがありません</span>
                </td>
              </tr>
            ) : (
              investmentList.map((item) => (
                <tr key={item.investmentAndReturnId} className="table-row">
                  <td className="table-cell text-center">
                    <Link
                      href={`/horses/${horseId}/investment-and-return/${createdDateYear}/${createdDateMonth}/${createdDateDay}/${item.memberId}`}
                      className="table-cell-text text-primary hover:text-primary-dark underline"
                    >
                      {item.memberId}
                    </Link>
                  </td>
                  <td className="table-cell text-center">
                    <span className="table-cell-text">{item.memberName}</span>
                  </td>
                  <td className="table-cell text-center">
                    <span className="table-cell-text">{item.progressedMonth}ヶ月</span>
                  </td>
                  <td className="table-cell text-center">
                    <span className="table-cell-text">
                      {item.yearlyReturnTargetFlag ? '○' : '×'}
                    </span>
                  </td>
                  <td className="table-cell text-center">
                    <span className="table-cell-text">
                      {item.billingAmount.toLocaleString()}円
                    </span>
                  </td>
                  <td className="table-cell text-center">
                    <span className="table-cell-text">
                      {item.paymentAmount.toLocaleString()}円
                    </span>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <div className="text-center">
        <Link 
          href={`/horses/${horseId}/investment-and-return`}
          className="btn-secondary"
        >
          一覧に戻る
        </Link>
      </div>
    </main>
  );
}
