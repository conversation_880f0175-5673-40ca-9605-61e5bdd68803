import { listHorses } from '@core-admin/api_clients/horse_client';
import { getCombinedStatusLabel } from './_utils/statusUtils';
import Link from 'next/link';
import { HorseGender } from '@hami/core-admin-api-schema/common_enums_pb';
import { HorseMemberListPdfButton } from './components/HorseMemberListPdfButton';

export const metadata = {
  robots: 'noindex, nofollow',
};

export const dynamic = 'force-dynamic';

interface SearchParams {
  search?: string;
  page?: string;
  age?: string;
}

export default async function HorsesPage({ searchParams }: { searchParams: Promise<SearchParams> }) {
  const resolvedSearchParams = await searchParams;
  const page = parseInt(resolvedSearchParams.page || '1');
  const search = resolvedSearchParams.search || '';
  const age = resolvedSearchParams.age ? parseInt(resolvedSearchParams.age) : undefined;

  try {
    const {
      horses,
      totalPages,
      page: currentPage,
    } = await listHorses({
      page,
      limit: 20,
      search: search || undefined,
      birthYear: age ? new Date().getFullYear() - age : undefined,
    });

    return (
      <main className="bg-white min-h-screen">
        <div className="bg-surface-elevated text-center py-6 mb-6">
          <h1 className="text-xl font-bold text-primary">馬管理</h1>
        </div>

        <div className="container mx-auto px-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex gap-4 items-center">
              <form method="GET" className="flex gap-2">
                <input type="text" name="search" defaultValue={search} placeholder="馬名" className="input-base" />
                <select name="age" defaultValue={age?.toString()} className="input-base">
                  <option value="">年齢を選択</option>
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((age) => (
                    <option key={age} value={age}>
                      {age}歳
                    </option>
                  ))}
                </select>
                <button type="submit" className="btn-primary">
                  検索する
                </button>
                {(search || age) && (
                  <Link href="/horses" className="btn-secondary">
                    クリア
                  </Link>
                )}
              </form>
            </div>
            <div className="flex gap-4 items-center">
              <HorseMemberListPdfButton />
              <Link href="/horses/create" className="btn-primary">
                馬を追加する
              </Link>
            </div>
          </div>

          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="table-header">
                  <th>募集年</th>
                  <th>募集番号</th>
                  <th>馬名/募集馬名</th>
                  <th>性齢</th>
                  <th>状態</th>
                  <th>出資と分配</th>
                  <th className="w-[190px]"></th>
                </tr>
              </thead>
              <tbody>
                {horses.map((horse) => (
                  <tr key={horse.horseId} className="table-row">
                    <td className="table-cell">
                      <span className="table-cell-text">{horse.recruitmentYear}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{horse.recruitmentNo.toString().padStart(3, '0')}</span>
                    </td>
                    <td className="table-cell">
                      <Link href={`/horses/${horse.horseId}`} className="text-primary hover:underline">
                        {horse.horseName || horse.recruitmentName}
                      </Link>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">
                        {horse.gender === undefined || horse.gender === null || horse.gender === HorseGender.HORSE_GENDER_UNSPECIFIED
                          ? '未登録-'
                          : horse.gender === HorseGender.STALLION
                          ? '牡'
                          : horse.gender === HorseGender.MARE
                          ? '牝'
                          : '騸'}
                        {new Date().getFullYear() - horse.birthYear}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{getCombinedStatusLabel(horse.publishStatus, horse.recruitmentStatus)}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">2025/04まで</span>
                    </td>
                    <td className="table-cell">
                      <Link href={`/horses/${horse.horseId}`} className="text-sm btn-ghost">
                        詳細
                      </Link>
                      <Link href={`/horses/${horse.horseId}/edit`} className="btn-ghost text-sm">
                        編集
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {totalPages > 1 && (
            <div className="mt-6 flex justify-center gap-2">
              {currentPage > 1 && (
                <Link href={`?page=${currentPage - 1}${search ? `&search=${encodeURIComponent(search)}` : ''}`} className="btn-secondary">
                  前へ
                </Link>
              )}

              <span className="px-3 py-2 text-secondary">
                {currentPage} / {totalPages} ページ
              </span>

              {currentPage < totalPages && (
                <Link href={`?page=${currentPage + 1}${search ? `&search=${encodeURIComponent(search)}` : ''}`} className="btn-secondary">
                  次へ
                </Link>
              )}
            </div>
          )}

          {horses.length === 0 && (
            <div className="text-center py-8 text-muted">
              {search ? '検索条件に一致する馬が見つかりませんでした' : '馬が登録されていません'}
            </div>
          )}
        </div>
      </main>
    );
  } catch (error) {
    console.error('Error fetching horses:', error);

    return (
      <main className="bg-white min-h-screen">
        <div className="bg-surface-elevated text-center py-6 mb-6">
          <h1 className="text-xl font-bold text-primary">馬管理</h1>
        </div>

        <div className="container mx-auto px-6">
          <div className="text-center py-8">
            <div className="text-error mb-4">
              <p className="text-lg font-medium">エラーが発生しました</p>
              <p className="text-sm mt-2">馬の一覧を取得できませんでした。</p>
              <p className="text-sm">しばらく時間をおいてから再度お試しください。</p>
            </div>

            <Link href="/horses/create" className="btn-primary">
              馬を追加する
            </Link>
          </div>
        </div>
      </main>
    );
  }
}
