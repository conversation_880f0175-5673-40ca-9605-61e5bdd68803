'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { createHorse, checkHorseExists } from '@core-admin/api_clients/horse_client';
import HorseConfirmDisplay from '../../_components/HorseConfirmDisplay';
import { HorseFormData } from '../../_components/HorseForm';

export default function CreateHorseConfirmPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<HorseFormData | null>(null);
  const [_isLoading, setIsLoading] = useState(false);
  const [isCheckingDuplicate, setIsCheckingDuplicate] = useState(false);
  const [isDuplicate, setIsDuplicate] = useState(false);
  const [duplicateCheckError, setDuplicateCheckError] = useState<string | null>(null);

  useEffect(() => {
    const savedData = sessionStorage.getItem('createHorseData');
    if (savedData) {
      const data = JSON.parse(savedData);
      setFormData(data);
      
      // 重複チェックを実行
      checkForDuplicate(data);
    } else {
      router.push('/horses/create');
    }
  }, [router]);

  const checkForDuplicate = async (data: HorseFormData) => {
    if (!data.recruitmentYear || !data.recruitmentNo) return;

    setIsCheckingDuplicate(true);
    setDuplicateCheckError(null);
    
    try {
      const result = await checkHorseExists({
        recruitmentYear: data.recruitmentYear,
        recruitmentNo: data.recruitmentNo,
      });
      
      setIsDuplicate(result.exists);
    } catch (error) {
      console.error('Error checking horse duplicate:', error);
      setDuplicateCheckError('重複チェックに失敗しました。ネットワーク接続を確認してください。');
    } finally {
      setIsCheckingDuplicate(false);
    }
  };

  const handleConfirm = async () => {
    if (!formData || isDuplicate) return;

    setIsLoading(true);
    try {
      await createHorse({
        recruitmentYear: formData.recruitmentYear,
        recruitmentNo: formData.recruitmentNo,
        recruitmentName: formData.recruitmentName,
        horseName: formData.horseName,
        birthYear: formData.birthYear || new Date().getFullYear() - 3,
        birthMonth: formData.birthMonth || 1,
        birthDay: formData.birthDay || 1,
        sharesTotal: formData.sharesTotal,
        amountTotal: formData.amountTotal,
        note: formData.note,
        fundStartYear: formData.fundStartYear || new Date().getFullYear(),
        fundStartMonth: formData.fundStartMonth || 1,
        fundStartDay: formData.fundStartDay || 1,
        conflictOfInterest: formData.conflictOfInterest,
      });
      
      sessionStorage.removeItem('createHorseData');
      alert('馬を追加しました');
      router.push('/horses');
    } catch (error) {
      console.error('Error creating horse:', error);
      alert('馬の追加に失敗しました');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/horses/create');
  };

  if (!formData) {
    return <div>Loading...</div>;
  }

  // 重複チェック中の表示
  if (isCheckingDuplicate) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>重複チェック中...</p>
        </div>
      </div>
    );
  }

  // 重複エラーまたはチェックエラーの表示
  const errorMessage = isDuplicate 
    ? `募集年度 ${formData.recruitmentYear} の募集番号 ${formData.recruitmentNo} は既に登録されています。入力内容を確認してください。`
    : duplicateCheckError;

  return (
    <>
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {isDuplicate ? '重複エラー' : 'チェックエラー'}
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{errorMessage}</p>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <HorseConfirmDisplay
        data={formData}
        title="馬追加確認"
        message={isDuplicate ? "この内容では追加できません。" : "この内容で追加します。"}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        confirmButtonText="馬を追加する"
        confirmButtonColor="secondary"
        confirmButtonDisabled={isDuplicate || !!duplicateCheckError}
      />
    </>
  );
}
