'use client';

import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import HorseForm, { HorseFormData } from '../_components/HorseForm';
import { saveToSession, loadFromSession, removeFromSession } from '@core-admin/utils/session_utils';

export default function CreateHorsePage() {
  const router = useRouter();
  const [_isLoading, setIsLoading] = useState(false);
  const [initialData, setInitialData] = useState<Partial<HorseFormData>>({});

  useEffect(() => {
    // sessionStorageから保存されたデータを読み込む
    const savedData = loadFromSession<HorseFormData>('createHorseData');
    if (savedData) {
      setInitialData(savedData);
    }
  }, []);

  const handleSubmit = async (data: HorseFormData) => {
    setIsLoading(true);
    try {
      // フォームデータをセッションストレージに保存して確認画面へ遷移
      saveToSession('createHorseData', data);
      router.push('/horses/create/confirm');
    } catch (error) {
      console.error('Error saving form data:', error);
      alert('エラーが発生しました');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // キャンセル時はsessionStorageをクリア
    removeFromSession('createHorseData');
    router.push('/horses');
  };

  return (
    <main className="max-w-4xl mx-auto p-6">
      <div className="bg-gray-200 text-center py-4 mb-6">
        <h1 className="text-xl font-bold">馬追加</h1>
      </div>

      <HorseForm
        initialData={initialData}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        submitButtonText="内容を確認する"
      />
    </main>
  );
}
