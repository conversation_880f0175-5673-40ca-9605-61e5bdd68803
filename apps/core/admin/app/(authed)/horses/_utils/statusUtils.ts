import { PublishStatus, RecruitmentStatus } from '@hami/core-admin-api-schema/horse_service_pb';

export const getPublishStatusLabel = (status: PublishStatus): string => {
  switch (status) {
    case PublishStatus.DRAFT:
      return '下書き';
    case PublishStatus.PUBLISHED:
      return '公開';
    case PublishStatus.ARCHIVED:
      return '削除';
    default:
      return '不明';
  }
};

export const getRecruitmentStatusLabel = (status: RecruitmentStatus): string => {
  switch (status) {
    case RecruitmentStatus.UPCOMING:
      return '開始前';
    case RecruitmentStatus.ACTIVE:
      return '運用中';
    case RecruitmentStatus.FULL:
      return '満口';
    case RecruitmentStatus.CLOSED:
      return '解散';
    default:
      return '不明';
  }
};

export const getCombinedStatusLabel = (publishStatus?: PublishStatus, recruitmentStatus?: RecruitmentStatus): string => {
  if (publishStatus === PublishStatus.PUBLISHED && recruitmentStatus) {
    return getRecruitmentStatusLabel(recruitmentStatus);
  }
  if (publishStatus) {
    return getPublishStatusLabel(publishStatus);
  }
  return '不明';
}; 