'use server';

import { generateHorseMemberListPdf } from '@core-admin/api_clients/horse_member_report_client';

export async function generateHorseMemberListPdfAction() {
  try {
    const response = await generateHorseMemberListPdf({});
    return {
      success: true,
      data: {
        pdfFileKey: response.pdfFileKey,
        downloadUrl: response.downloadUrl,
        totalHorses: response.totalHorses,
        totalMembers: response.totalMembers,
        generatedAt: response.generatedAt,
      }
    };
  } catch (error) {
    console.error('PDF generation failed:', error);
    return {
      success: false,
      error: 'PDF生成に失敗しました。しばらく時間をおいてから再度お試しください。'
    };
  }
}
