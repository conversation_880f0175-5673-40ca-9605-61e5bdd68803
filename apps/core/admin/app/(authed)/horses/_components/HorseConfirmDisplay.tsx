import { HorseFormData } from './HorseForm';

interface HorseConfirmDisplayProps {
  data: HorseFormData;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmButtonText: string;
  confirmButtonColor?: 'primary' | 'danger' | 'secondary';
  confirmButtonDisabled?: boolean;
  horseId?: number;
}

export default function HorseConfirmDisplay({
  data,
  title,
  message,
  onConfirm,
  onCancel,
  confirmButtonText,
  confirmButtonColor = 'primary',
  confirmButtonDisabled = false,
  horseId,
}: HorseConfirmDisplayProps) {
  const getButtonClass = () => {
    if (confirmButtonDisabled) {
      return 'disabled';
    }

    switch (confirmButtonColor) {
      case 'danger':
        return 'btn-danger';
      case 'secondary':
        return 'btn-secondary';
      default:
        return 'btn-primary';
    }
  };

  return (
    <div className="container mx-auto px-6">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">{title}</h1>
      </div>

      <div className="text-center mb-8">
        <p className="text-lg mb-2 text-primary">{message}</p>
        <p className="text-lg text-primary">よろしいですか？</p>
      </div>

      <div className="bg-surface-card border border-stroke-separator overflow-hidden">
        <table className="w-full">
          <tbody>
            {horseId && (
              <tr className="table-row">
                <td className="table-cell bg-shark-100 text-secondary font-bold">
                  <span className="table-cell-text">馬ID</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{horseId}</span>
                </td>
              </tr>
            )}
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">募集年</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text">{data.recruitmentYear}</span>
              </td>
            </tr>
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">募集番号</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text">{data.recruitmentNo.toString().padStart(3, '0')}</span>
              </td>
            </tr>
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">募集馬名</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text">{data.recruitmentName}</span>
              </td>
            </tr>
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">馬名</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text">{data.horseName}</span>
              </td>
            </tr>
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">生年月日</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text">
                  {data.birthYear}年{data.birthMonth}月{data.birthDay}日
                </span>
              </td>
            </tr>
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">募集口数</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text">{data.sharesTotal}</span>
              </td>
            </tr>
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">募集総額（円）</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text">{data.amountTotal}</span>
              </td>
            </tr>
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">ファンド開始年月日</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text">
                  {data.fundStartYear}年{data.fundStartMonth}月{data.fundStartDay}日
                </span>
              </td>
            </tr>
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">利益相反該当馬</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text">{data.conflictOfInterest ? 'はい' : 'いいえ'}</span>
              </td>
            </tr>
            <tr className="table-row">
              <td className="table-cell bg-shark-100 text-secondary font-bold">
                <span className="table-cell-text">備考</span>
              </td>
              <td className="table-cell">
                <span className="table-cell-text whitespace-pre-wrap">{data.note}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="flex gap-4 justify-center mt-8">
        <button
          onClick={onCancel}
          className="btn-secondary"
        >
          内容を修正する
        </button>
        <button
          onClick={confirmButtonDisabled ? undefined : onConfirm}
          disabled={confirmButtonDisabled}
          className={getButtonClass()}
        >
          {confirmButtonText}
        </button>
      </div>
    </div>
  );
}