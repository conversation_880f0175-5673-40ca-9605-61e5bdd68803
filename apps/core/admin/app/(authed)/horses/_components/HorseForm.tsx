'use client';

import { useState, useEffect } from 'react';

export interface HorseFormData {
  recruitmentYear: number;
  recruitmentNo: number;
  recruitmentName: string;
  horseName: string;
  birthYear: number | null;
  birthMonth: number | null;
  birthDay: number | null;
  sharesTotal: number;
  amountTotal: number;
  fundStartYear: number | null;
  fundStartMonth: number | null;
  fundStartDay: number | null;
  note: string;
  conflictOfInterest: boolean;
}

interface HorseFormProps {
  initialData?: Partial<HorseFormData>;
  onSubmit: (data: HorseFormData) => void;
  onCancel: () => void;
  submitButtonText: string;
  isEdit?: boolean;
}

export default function HorseForm({
  initialData = {},
  onSubmit,
  onCancel,
  submitButtonText,
  isEdit = false,
}: HorseFormProps) {
  const [formData, setFormData] = useState<HorseFormData>({
    recruitmentYear: initialData.recruitmentYear || new Date().getFullYear(),
    recruitmentNo: initialData.recruitmentNo || 1,
    recruitmentName: initialData.recruitmentName || '',
    horseName: initialData.horseName || '',
    birthYear: initialData.birthYear || new Date().getFullYear() - 3,
    birthMonth: initialData.birthMonth || null,
    birthDay: initialData.birthDay || null,
    sharesTotal: initialData.sharesTotal || 500,
    amountTotal: initialData.amountTotal || 40000000,
    fundStartYear: initialData.fundStartYear || new Date().getFullYear(),
    fundStartMonth: initialData.fundStartMonth || null,
    fundStartDay: initialData.fundStartDay || null,
    note: initialData.note || '',
    conflictOfInterest: initialData.conflictOfInterest || false,
  });

  useEffect(() => {
    setFormData({
      recruitmentYear: initialData.recruitmentYear || new Date().getFullYear(),
      recruitmentNo: initialData.recruitmentNo || 1,
      recruitmentName: initialData.recruitmentName || '',
      horseName: initialData.horseName || '',
      birthYear: initialData.birthYear || new Date().getFullYear() - 3,
      birthMonth: initialData.birthMonth || null,
      birthDay: initialData.birthDay || null,
      sharesTotal: initialData.sharesTotal || 500,
      amountTotal: initialData.amountTotal || 40000000,
      fundStartYear: initialData.fundStartYear || new Date().getFullYear(),
      fundStartMonth: initialData.fundStartMonth || null,
      fundStartDay: initialData.fundStartDay || null,
      note: initialData.note || '',
      conflictOfInterest: initialData.conflictOfInterest || false,
    });
  }, [initialData]);

  const handleInputChange = (field: keyof HorseFormData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNumberInputChange = (field: keyof HorseFormData, value: string) => {
    if (value === '') {
      return setFormData(prev => ({
        ...prev,
        [field]: null,
      }));
    }

    const numValue = parseInt(value);
    if (!isNaN(numValue)) {
      setFormData(prev => ({
        ...prev,
        [field]: numValue,
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-primary mb-2">
            募集年度
          </label>
          <input
            type="number"
            value={formData.recruitmentYear}
            onChange={(e) => handleNumberInputChange('recruitmentYear', e.target.value)}
            className="input-base w-full"
            required
            disabled={isEdit}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-primary mb-2">
            募集番号
          </label>
          <input
            type="number"
            value={formData.recruitmentNo}
            onChange={(e) => handleNumberInputChange('recruitmentNo', e.target.value)}
            className="input-base w-full"
            required
            disabled={isEdit}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-primary mb-2">
            募集馬名
          </label>
          <input
            type="text"
            value={formData.recruitmentName}
            onChange={(e) => handleInputChange('recruitmentName', e.target.value)}
            className="input-base w-full"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-primary mb-2">
            馬名
          </label>
          <input
            type="text"
            value={formData.horseName}
            onChange={(e) => handleInputChange('horseName', e.target.value)}
            className="input-base w-full"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-primary mb-2">
          生年月日
        </label>
        <div className="grid grid-cols-3 gap-2">
          <div className="flex items-center">
            <input
              type="number"
              value={formData.birthYear || ''}
              onChange={(e) => handleNumberInputChange('birthYear', e.target.value)}
              className="input-base w-full"
              placeholder="年"
              required
            />
            <span className="text-sm text-secondary ml-2">年</span>
          </div>
          <div className="flex items-center">
            <input
              type="number"
              min="1"
              max="12"
              value={formData.birthMonth || ''}
              onChange={(e) => handleNumberInputChange('birthMonth', e.target.value)}
              className="input-base w-full"
              placeholder="月"
              required
            />
            <span className="text-sm text-secondary ml-2">月</span>
          </div>
          <div className="flex items-center">
            <input
              type="number"
              min="1"
              max="31"
              value={formData.birthDay || ''}
              onChange={(e) => handleNumberInputChange('birthDay', e.target.value)}
              className="input-base w-full"
              placeholder="日"
              required
            />
            <span className="text-sm text-secondary ml-2">日</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-primary mb-2">
            募集口数
          </label>
          <input
            type="number"
            value={formData.sharesTotal}
            onChange={(e) => handleNumberInputChange('sharesTotal', e.target.value)}
            className="input-base w-full"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-primary mb-2">
            募集総額（円）
          </label>
          <input
            type="number"
            value={formData.amountTotal}
            onChange={(e) => handleNumberInputChange('amountTotal', e.target.value)}
            className="input-base w-full"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-primary mb-2">
          ファンド開始年月日
        </label>
        <div className="grid grid-cols-3 gap-2">
          <div className="flex items-center">
            <input
              type="number"
              value={formData.fundStartYear || ''}
              onChange={(e) => handleNumberInputChange('fundStartYear', e.target.value)}
              className="input-base w-full"
              placeholder="年"
              required
            />
            <span className="text-sm text-secondary ml-2">年</span>
          </div>
          <div className="flex items-center">
            <input
              type="number"
              value={formData.fundStartMonth || ''}
              onChange={(e) => handleNumberInputChange('fundStartMonth', e.target.value)}
              className="input-base w-full"
              min="1"
              max="12"
              placeholder="月"
              required
            />
            <span className="text-sm text-secondary ml-2">月</span>
          </div>
          <div className="flex items-center">
            <input
              type="number"
              value={formData.fundStartDay || ''}
              onChange={(e) => handleNumberInputChange('fundStartDay', e.target.value)}
              className="input-base w-full"
              min="1"
              max="31"
              placeholder="日"
              required
            />
            <span className="text-sm text-secondary ml-2">日</span>
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-primary mb-2">
          備考
        </label>
        <textarea
          value={formData.note}
          onChange={(e) => handleInputChange('note', e.target.value)}
          rows={4}
          className="input-base w-full"
        />
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="conflictOfInterest"
          checked={formData.conflictOfInterest}
          onChange={(e) => handleInputChange('conflictOfInterest', e.target.checked)}
          className="h-4 w-4 text-primary focus:ring-primary border-stroke-border rounded mr-2"
        />
        <label htmlFor="conflictOfInterest" className="text-sm font-medium text-primary">
          利益相反該当馬
        </label>
      </div>

      <div className="flex gap-4 justify-center">
        <button
          type="button"
          onClick={onCancel}
          className="btn-secondary"
        >
          戻る
        </button>
        <button
          type="submit"
          className="btn-primary"
        >
          {submitButtonText}
        </button>
      </div>
    </form>
  );
}