import { useState, useEffect } from 'react';
import { BankAccountType } from '@hami/core-admin-api-schema/biller_service_pb';

export interface BillerFormData {
  name: string;
  address: string;
  bank_name: string;
  bank_branch: string;
  bank_account: string;
  bank_account_type: BankAccountType;
  bank_account_name: string;
}

interface BillerFormProps {
  defaultValues?: BillerFormData;
  onSubmit: (data: BillerFormData) => void;
  submitLabel?: string;
  onChange?: (data: BillerFormData) => void;
}

export function BillerForm({ defaultValues, onSubmit, submitLabel = '内容を確認する', onChange }: BillerFormProps) {
  const [form, setForm] = useState<BillerFormData>(
    defaultValues || {
      name: '',
      address: '',
      bank_name: '',
      bank_branch: '',
      bank_account: '',
      bank_account_type: BankAccountType.SAVINGS,
      bank_account_name: '',
    }
  );
  const [errors, setErrors] = useState<Partial<Record<keyof BillerFormData, string>>>({});

  useEffect(() => {
    if (onChange) onChange(form);
  }, [form, onChange]);

  const handleChange = (field: keyof BillerFormData, value: string | BankAccountType) => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const validate = (): boolean => {
    const newErrors: Partial<Record<keyof BillerFormData, string>> = {};
    if (!form.name) newErrors.name = '名称を入力してください';
    if (!form.address) newErrors.address = '所在地を入力してください';
    if (!form.bank_name) newErrors.bank_name = '銀行名を入力してください';
    if (!form.bank_branch) newErrors.bank_branch = '支店名を入力してください';
    if (!form.bank_account) newErrors.bank_account = '口座番号を入力してください';
    if (!form.bank_account_name) newErrors.bank_account_name = '口座名義を入力してください';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validate()) {
      onSubmit(form);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-secondary">名称</label>
        <input
          type="text"
          value={form.name}
          onChange={e => handleChange('name', e.target.value)}
          className="mt-1 block w-full input-base"
        />
        {errors.name && <p className="mt-1 text-sm text-error">{errors.name}</p>}
      </div>
      <div>
        <label className="block text-sm font-medium text-secondary">所在地</label>
        <input
          type="text"
          value={form.address}
          onChange={e => handleChange('address', e.target.value)}
          className="mt-1 block w-full input-base"
        />
        {errors.address && <p className="mt-1 text-sm text-error">{errors.address}</p>}
      </div>
      <div>
        <label className="block text-sm font-medium text-secondary">銀行口座（銀行名）</label>
        <input
          type="text"
          value={form.bank_name}
          onChange={e => handleChange('bank_name', e.target.value)}
          className="mt-1 block w-full input-base"
        />
        {errors.bank_name && <p className="mt-1 text-sm text-error">{errors.bank_name}</p>}
      </div>
      <div>
        <label className="block text-sm font-medium text-secondary">銀行口座（支店名）</label>
        <input
          type="text"
          value={form.bank_branch}
          onChange={e => handleChange('bank_branch', e.target.value)}
          className="mt-1 block w-full input-base"
        />
        {errors.bank_branch && <p className="mt-1 text-sm text-error">{errors.bank_branch}</p>}
      </div>
      <div>
        <label className="block text-sm font-medium text-secondary">口座種別</label>
        <div className="mt-2 space-x-4">
          <label className="inline-flex items-center">
            <input
              type="radio"
              checked={form.bank_account_type === BankAccountType.SAVINGS}
              onChange={() => handleChange('bank_account_type', BankAccountType.SAVINGS)}
              className="form-radio h-4 w-4 text-primary focus:ring-stroke-focus"
            />
            <span className="ml-2 text-primary">普通</span>
          </label>
          <label className="inline-flex items-center">
            <input
              type="radio"
              checked={form.bank_account_type === BankAccountType.CHECKING}
              onChange={() => handleChange('bank_account_type', BankAccountType.CHECKING)}
              className="form-radio h-4 w-4 text-primary focus:ring-stroke-focus"
            />
            <span className="ml-2 text-primary">当座</span>
          </label>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-secondary">銀行口座（口座番号）</label>
        <input
          type="text"
          value={form.bank_account}
          onChange={e => handleChange('bank_account', e.target.value)}
          className="mt-1 block w-full input-base"
        />
        {errors.bank_account && <p className="mt-1 text-sm text-error">{errors.bank_account}</p>}
      </div>
      <div>
        <label className="block text-sm font-medium text-secondary">銀行口座名義</label>
        <input
          type="text"
          value={form.bank_account_name}
          onChange={e => handleChange('bank_account_name', e.target.value)}
          className="mt-1 block w-full input-base"
        />
        {errors.bank_account_name && <p className="mt-1 text-sm text-error">{errors.bank_account_name}</p>}
      </div>
      <div className="flex justify-center space-x-4">
        <button
          type="submit"
          className="btn-primary"
        >
          {submitLabel}
        </button>
      </div>
    </form>
  );
} 