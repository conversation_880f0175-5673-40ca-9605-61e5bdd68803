import { BankAccountType, Biller } from '@hami/core-admin-api-schema/biller_service_pb';

interface BillerDisplayProps {
  biller: Biller;
}

export function BillerDisplay({ biller }: BillerDisplayProps) {
  const bankAccountTypeText = biller.bankAccountType === BankAccountType.SAVINGS ? '普通' : '当座';

  return (
    <div className="bg-surface-card border border-stroke-separator overflow-hidden">
      <table className="w-full">
        <tbody>
          <tr className="table-row">
            <th className="table-cell w-1/3">
              <span className="table-cell-text font-medium text-secondary">名称</span>
            </th>
            <td className="table-cell w-2/3">
              <span className="table-cell-text">{biller.name}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-cell w-1/3">
              <span className="table-cell-text font-medium text-secondary">所在地</span>
            </th>
            <td className="table-cell w-2/3">
              <span className="table-cell-text">{biller.address}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-cell w-1/3">
              <span className="table-cell-text font-medium text-secondary">銀行口座（銀行名）</span>
            </th>
            <td className="table-cell w-2/3">
              <span className="table-cell-text">{biller.bankName}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-cell w-1/3">
              <span className="table-cell-text font-medium text-secondary">銀行口座（支店名）</span>
            </th>
            <td className="table-cell w-2/3">
              <span className="table-cell-text">{biller.bankBranch}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-cell w-1/3">
              <span className="table-cell-text font-medium text-secondary">口座種別</span>
            </th>
            <td className="table-cell w-2/3">
              <span className="table-cell-text">{bankAccountTypeText}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-cell w-1/3">
              <span className="table-cell-text font-medium text-secondary">銀行口座（口座番号）</span>
            </th>
            <td className="table-cell w-2/3">
              <span className="table-cell-text">{biller.bankAccount}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-cell w-1/3">
              <span className="table-cell-text font-medium text-secondary">銀行口座名義</span>
            </th>
            <td className="table-cell w-2/3">
              <span className="table-cell-text">{biller.bankAccountName}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
} 