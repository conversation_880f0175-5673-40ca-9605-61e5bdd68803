'use client';

import { use } from 'react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { BillerDisplay } from '../_components/BillerDisplay';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { Biller } from '@hami/core-admin-api-schema/biller_service_pb';
import { getBiller } from '@core-admin/api_clients/biller_client';

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export default function BillerDetailPage({ params }: Props) {
  const router = useRouter();
  const resolvedParams = use(params);
  const id = resolvedParams.id;
  const [biller, setBiller] = useState<Biller | null>(null);

  const fetchBiller = useCallback(async () => {
    const response = await getBiller({ id: Number(id) });
    setBiller(response.biller ?? null);
  }, [id]);

  useEffect(() => {
    fetchBiller();
  }, [fetchBiller]);

  if (!biller) {
    return null;
  }

  const breadcrumbItems = [
    { label: '請求元一覧', href: '/biller' },
    { label: biller.name, isCurrent: true },
  ];

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Breadcrumb items={breadcrumbItems} />
      <div className="flex justify-end mb-8">
        <button
          type="button"
          onClick={() => router.push(`/biller/${id}/edit?reset=1`)}
          className="btn-primary"
        >
          編集する
        </button>
      </div>
      <BillerDisplay biller={biller} />
    </div>
  );
}
