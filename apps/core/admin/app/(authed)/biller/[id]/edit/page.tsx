'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState, use } from 'react';
import { BillerForm, type BillerFormData } from '../../_components/BillerForm';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { Biller } from '@hami/core-admin-api-schema/biller_service_pb';
import { getBiller } from '@core-admin/api_clients/biller_client';

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export default function BillerEditPage({ params }: Props) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const resolvedParams = use(params);
  const id = resolvedParams.id;
  const [biller, setBiller] = useState<Biller | null>(null);
  const [defaultValues, setDefaultValues] = useState<BillerFormData | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && searchParams.get('reset') === '1') {
      sessionStorage.removeItem(`billerEditForm_${id}`);
      const url = new URL(window.location.href);
      url.searchParams.delete('reset');
      window.history.replaceState({}, '', url.pathname + url.search);
    }
  }, [id, searchParams]);

  const fetchBiller = useCallback(async () => {
    const response = await getBiller({ id: Number(id) });
    setBiller(response.biller ?? null);
    if (response.biller) {
      const key = `billerEditForm_${id}`;
      if (typeof window !== 'undefined') {
        const saved = sessionStorage.getItem(key);
        if (saved) {
          setDefaultValues(JSON.parse(saved));
        } else {
          setDefaultValues({
            name: response.biller.name,
            address: response.biller.address,
            bank_name: response.biller.bankName,
            bank_branch: response.biller.bankBranch,
            bank_account: response.biller.bankAccount,
            bank_account_type: response.biller.bankAccountType,
            bank_account_name: response.biller.bankAccountName,
          });
        }
      }
    }
  }, [id]);

  useEffect(() => {
    fetchBiller();
  }, [fetchBiller]);

  const handleChange = (data: BillerFormData) => {
    const key = `billerEditForm_${id}`;
    setDefaultValues(data);
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(key, JSON.stringify(data));
    }
  };

  const handleSubmit = (data: BillerFormData) => {
    const key = `billerEditForm_${id}`;
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(key, JSON.stringify(data));
    }
    router.push(`/biller/${id}/edit/confirm`);
  };

  if (!biller || !defaultValues) {
    return null;
  }

  const breadcrumbItems = [
    { label: '請求元一覧', href: '/biller' },
    { label: biller.name, href: `/biller/${id}` },
    { label: '請求元編集', isCurrent: true },
  ];

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Breadcrumb items={breadcrumbItems} />
      <BillerForm defaultValues={defaultValues} onSubmit={handleSubmit} onChange={handleChange} />
    </div>
  );
}
