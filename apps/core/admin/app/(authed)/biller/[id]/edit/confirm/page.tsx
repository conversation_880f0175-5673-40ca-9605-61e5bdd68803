'use client';

import { useEffect, useState } from 'react';
import { use } from 'react';
import { useRouter } from 'next/navigation';
import { BillerDisplay } from '../../../_components/BillerDisplay';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { BillerSchema, type Biller } from '@hami/core-admin-api-schema/biller_service_pb';
import { updateBiller } from '@core-admin/api_clients/biller_client';
import { create } from '@bufbuild/protobuf';

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export default function BillerEditConfirmPage({ params }: Props) {
  const resolvedParams = use(params);
  const id = resolvedParams.id;
  const router = useRouter();
  const [biller, setBiller] = useState<Biller | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = sessionStorage.getItem(`billerEditForm_${id}`);
      if (saved) {
        const data = JSON.parse(saved);
        setBiller(create(BillerSchema, {
          id: Number(id),
          name: data.name,
          address: data.address,
          bankName: data.bank_name,
          bankBranch: data.bank_branch,
          bankAccount: data.bank_account,
          bankAccountType: data.bank_account_type,
          bankAccountName: data.bank_account_name,
        }));
      }
    }
  }, [id]);

  const handleSubmit = async () => {
    if (!biller) return;
    await updateBiller({
      id: biller.id,
      name: biller.name,
      address: biller.address,
      bankName: biller.bankName,
      bankBranch: biller.bankBranch,
      bankAccount: biller.bankAccount,
      bankAccountType: biller.bankAccountType,
      bankAccountName: biller.bankAccountName,
    });
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(`billerEditForm_${id}`);
    }
    router.push('/biller');
  };

  if (!biller) return null;

  const breadcrumbItems = [
    { label: '請求元一覧', href: '/biller' },
    { label: biller.name, href: `/biller/${id}` },
    { label: '請求元編集', href: `/biller/${id}/edit` },
    { label: '請求元編集確認', isCurrent: true },
  ];

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Breadcrumb items={breadcrumbItems} />
      <BillerDisplay biller={biller} />
      <div className="mt-6 flex justify-center space-x-4">
        <button
          type="button"
          onClick={() => router.back()}
          className="btn-secondary"
        >
          戻る
        </button>
        <button
          type="button"
          onClick={handleSubmit}
          className="btn-primary"
        >
          更新する
        </button>
      </div>
    </div>
  );
}
