'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { BillerForm, type BillerFormData } from '../_components/BillerForm';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { BankAccountType } from '@hami/core-admin-api-schema/biller_service_pb';
import { saveToSession, loadFromSession, removeFromSession } from '@core-admin/utils/session_utils';

export default function BillerCreatePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [defaultValues, setDefaultValues] = useState<BillerFormData>(() => {
    if (typeof window !== 'undefined') {
      const saved = loadFromSession<BillerFormData>('billerCreateForm');
      if (saved) return saved;
    }
    return {
      name: '',
      address: '',
      bank_name: '',
      bank_branch: '',
      bank_account: '',
      bank_account_type: BankAccountType.SAVINGS,
      bank_account_name: '',
    };
  });

  useEffect(() => {
    if (typeof window !== 'undefined' && searchParams.get('reset') === '1') {
      removeFromSession('billerCreateForm');
    }
  }, [searchParams]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = loadFromSession<BillerFormData>('billerCreateForm');
      if (saved) setDefaultValues(saved);
    }
  }, []);

  const handleChange = (data: BillerFormData) => {
    setDefaultValues(data);
    if (typeof window !== 'undefined') {
      saveToSession('billerCreateForm', data);
    }
  };

  const handleSubmit = (data: BillerFormData) => {
    if (typeof window !== 'undefined') {
      saveToSession('billerCreateForm', data);
    }
    router.push('/biller/create/confirm');
  };

  const breadcrumbItems = [
    { label: '請求元一覧', href: '/biller' },
    { label: '請求元追加', isCurrent: true },
  ];

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Breadcrumb items={breadcrumbItems} />
      <BillerForm defaultValues={defaultValues} onSubmit={handleSubmit} onChange={handleChange} />
    </div>
  );
}
