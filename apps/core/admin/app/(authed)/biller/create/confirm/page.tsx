'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { BillerDisplay } from '../../_components/BillerDisplay';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { BillerSchema, type Biller } from '@hami/core-admin-api-schema/biller_service_pb';
import { createBiller } from '@core-admin/api_clients/biller_client';
import { create } from '@bufbuild/protobuf';

export default function BillerCreateConfirmPage() {
  const router = useRouter();
  const [biller, setBiller] = useState<Biller | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = sessionStorage.getItem('billerCreateForm');
      if (saved) {
        const data = JSON.parse(saved);
        setBiller(create(BillerSchema, {
          id: 0,
          name: data.name,
          address: data.address,
          bankName: data.bank_name,
          bankBranch: data.bank_branch,
          bankAccount: data.bank_account,
          bankAccountType: data.bank_account_type,
          bankAccountName: data.bank_account_name,
        }));
      }
    }
  }, []);

  const handleSubmit = async () => {
    if (!biller) return;
    await createBiller({
      name: biller.name,
      address: biller.address,
      bankName: biller.bankName,
      bankBranch: biller.bankBranch,
      bankAccount: biller.bankAccount,
      bankAccountType: biller.bankAccountType,
      bankAccountName: biller.bankAccountName,
    });
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem('billerCreateForm');
    }
    router.push('/biller');
  };

  if (!biller) return null;

  const breadcrumbItems = [
    { label: '請求元一覧', href: '/biller' },
    { label: '請求元追加', href: '/biller/create' },
    { label: '請求元追加確認', isCurrent: true },
  ];

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Breadcrumb items={breadcrumbItems} />
      <BillerDisplay biller={biller} />
      <div className="mt-6 flex justify-center space-x-4">
        <button
          type="button"
          onClick={() => router.back()}
          className="btn-secondary"
        >
          戻る
        </button>
        <button
          type="button"
          onClick={handleSubmit}
          className="btn-primary"
        >
          追加する
        </button>
      </div>
    </div>
  );
}
