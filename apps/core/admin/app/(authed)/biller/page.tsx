'use client';

import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { Biller } from '@hami/core-admin-api-schema/biller_service_pb';
import { listBillers } from '@core-admin/api_clients/biller_client';

export default function BillerListPage() {
  const router = useRouter();
  const [billers, setBillers] = useState<Biller[]>([]);

  const fetchBillers = useCallback(async () => {
    const response = await listBillers({ pageSize: 100 });
    setBillers(response.billers);
  }, []);

  useEffect(() => {
    fetchBillers();
  }, [fetchBillers]);

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">請求元管理</h1>
      </div>

      <div className="container mx-auto px-6">
        <div className="flex justify-end mb-6">
          <button type="button" onClick={() => router.push('/biller/create?reset=1')} className="btn-primary">
            請求元を追加する
          </button>
        </div>

        <div className="bg-surface-card border border-stroke-separator overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="table-header">
                <th>請求元ID</th>
                <th>名称</th>
                <th className="text-right">操作</th>
              </tr>
            </thead>
            <tbody>
              {billers.map((biller) => (
                <tr key={biller.id} className="table-row">
                  <td className="table-cell">
                    <span className="table-cell-text font-medium">{biller.id}</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{biller.name}</span>
                  </td>
                  <td className="table-cell text-right">
                    <button onClick={() => router.push(`/biller/${biller.id}`)} className="btn-ghost text-sm">
                      詳細
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </main>
  );
}
