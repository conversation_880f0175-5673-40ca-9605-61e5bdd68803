'use client';
import { MemberClaimAndPayItem } from '@hami/core-admin-api-schema/member_claim_and_pay_service_pb';
import { listMemberClaimAndPays } from '@core-admin/api_clients/member_claim_and_pay_client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';

export default function Page() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // 検索フォームの状態
  const [year, setYear] = useState(new Date().getFullYear().toString());
  const [month, setMonth] = useState((new Date().getMonth() + 1).toString().padStart(2, '0'));
  const [memberClaimAndPays, setMemberClaimAndPays] = useState<MemberClaimAndPayItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // 検索実行
  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const params = new URLSearchParams();
    if (year) params.set('year', year);
    if (month) params.set('month', month);
    router.push(`/claim-and-pay?${params.toString()}`);
  };

  // クエリパラメータが変わるたびにAPI呼び出し・フォーム同期
  useEffect(() => {
    setError(null);
    const yearParam = searchParams.get('year') || new Date().getFullYear().toString();
    const monthParam = searchParams.get('month') || (new Date().getMonth() + 1).toString().padStart(2, '0');

    setYear(yearParam);
    setMonth(monthParam);

    // 年と月が設定されている場合のみAPI呼び出し
    if (yearParam && monthParam) {
      setIsLoading(true);
      const day = 10; // 固定で10日

      listMemberClaimAndPays({
        year: parseInt(yearParam),
        month: parseInt(monthParam),
        day: day,
      })
        .then((result) => {
          setMemberClaimAndPays(result.memberClaimAndPays);
          setIsLoading(false);
        })
        .catch(() => {
          setError('会員請求・支払い情報の取得中にエラーが発生しました。しばらくしてから再度お試しください。');
          setIsLoading(false);
        });
    }
  }, [searchParams]);

  // 年と月の選択肢を生成
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 15 }, (_, i) => currentYear - 12 + i);
  const months = Array.from({ length: 12 }, (_, i) => i + 1);

  // 金額をフォーマットする関数
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ja-JP').format(amount);
  };

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">会員請求・支払い</h1>
        <div className="text-sm text-muted">
          {year}年{month}月10日
        </div>
      </div>

      <div className="container mx-auto px-6">
        {/* 上部行: 左=検索フォーム, 右=アクション（空） */}
        <div className="flex justify-between items-center mb-6">
          <form className="flex flex-wrap gap-4 items-end" onSubmit={handleSearch}>
            <div className="flex items-center gap-2">
              <label className="text-sm text-muted whitespace-nowrap">年</label>
              <select value={year} onChange={(e) => setYear(e.target.value)} className="text-search w-24">
                {years.map((y) => (
                  <option key={y} value={y}>
                    {y}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm text-muted whitespace-nowrap">月</label>
              <select value={month} onChange={(e) => setMonth(e.target.value)} className="text-search w-20">
                {months.map((m) => (
                  <option key={m} value={m.toString().padStart(2, '0')}>
                    {m}
                  </option>
                ))}
              </select>
            </div>
            <button type="submit" className="btn-primary" disabled={isLoading}>
              {isLoading ? '読み込み中...' : '検索'}
            </button>
          </form>
          <div className="flex gap-4 items-center" />
        </div>

        {error && <div className="text-error font-semibold mb-4">{error}</div>}

        <div className="bg-surface-card border border-stroke-separator overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="table-header">
                <th>会員番号</th>
                <th>氏名</th>
                <th>発生日</th>
                <th>請求金額</th>
                <th>支払金額</th>
              </tr>
            </thead>
            <tbody>
              {isLoading ? (
                <tr>
                  <td className="table-cell text-center" colSpan={5}>
                    <span className="table-cell-text text-muted">読み込み中...</span>
                  </td>
                </tr>
              ) : !memberClaimAndPays || memberClaimAndPays.length === 0 ? (
                <tr>
                  <td className="table-cell text-center" colSpan={5}>
                    <span className="table-cell-text text-muted">会員請求・支払い情報が見つかりません。</span>
                  </td>
                </tr>
              ) : (
                memberClaimAndPays.map((item) => (
                  <tr key={item.memberClaimAndPayId} className="table-row">
                    <td className="table-cell">
                      <span className="table-cell-text">{item.memberNumber}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">
                        {item.lastName} {item.firstName}（{item.lastNameKana} {item.firstNameKana}）
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{item.occurredDate}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text text-right">{formatCurrency(item.claimAmount)}円</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text text-right">{formatCurrency(item.payAmount)}円</span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* 合計表示 */}
        {memberClaimAndPays && memberClaimAndPays.length > 0 && (
          <div className="mt-6 p-4 bg-surface-elevated border border-stroke-separator rounded">
            <div className="flex justify-between items-center">
              <span className="font-semibold">合計</span>
              <div className="flex gap-8">
                <div>
                  <span className="text-sm text-muted">請求金額: </span>
                  <span className="font-semibold">
                    {formatCurrency(memberClaimAndPays.reduce((sum, item) => sum + item.claimAmount, 0))}円
                  </span>
                </div>
                <div>
                  <span className="text-sm text-muted">支払金額: </span>
                  <span className="font-semibold">
                    {formatCurrency(memberClaimAndPays.reduce((sum, item) => sum + item.payAmount, 0))}円
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </main>
  );
}
