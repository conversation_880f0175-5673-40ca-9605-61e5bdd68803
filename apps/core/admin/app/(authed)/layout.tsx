import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import AppLayout from '@core-admin/components/layout/AppLayout';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'ダッシュボード',
  robots: 'noindex, nofollow',
};

export default async function AuthedLayout({ children }: { children: React.ReactNode }) {
  const cookieStore = await cookies();
  const sessionToken = cookieStore.get('sessionToken')?.value;

  if (!sessionToken) {
    redirect('/login');
  }

  return <AppLayout>{children}</AppLayout>;
}
