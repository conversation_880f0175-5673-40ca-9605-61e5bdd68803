import { PageHeader } from '@core-admin/components/layout/PageHeader';
import PasswordChangeForm from './_components/PasswordChangeForm';

export default function PasswordSettingsPage() {
  return (
    <div className="px-6 py-8">
      <div className="max-w-3xl mx-auto">
        <PageHeader
          title="パスワード変更"
          breadcrumbItems={[{ label: 'ホーム', href: '/' }, { label: 'システム設定', href: '/settings' }, { label: 'パスワード変更' }]}
        />
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <PasswordChangeForm />
        </div>
      </div>
    </div>
  );
}
