'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { changePassword } from '@core-admin/api_clients/admin_user_client';

export default function PasswordChangeForm() {
  const router = useRouter();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const getSessionTokenFromCookie = () => {
    const cookies = document.cookie.split('; ');
    const sessionCookie = cookies.find((cookie) => cookie.startsWith('sessionToken='));
    return sessionCookie?.split('=')[1] ?? '';
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!currentPassword || !newPassword) return;

    setLoading(true);
    try {
      const sessionToken = getSessionTokenFromCookie();
      await changePassword({ sessionToken, currentPassword, newPassword });
      alert('パスワードを変更しました');
      setCurrentPassword('');
      setNewPassword('');
      router.refresh();
    } catch (err: unknown) {
      console.error(err);
      alert('パスワードの変更に失敗しました');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">現在のパスワード</label>
        <input
          type="password"
          value={currentPassword}
          onChange={(e) => setCurrentPassword(e.target.value)}
          required
          className="w-full px-3 py-2 border border-hami-stroke-border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-hami-bush-800 focus:border-hami-bush-800 bg-hami-surface-elevated text-hami-glyph-base"
          placeholder="現在のパスワード"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">新しいパスワード</label>
        <input
          type="password"
          value={newPassword}
          onChange={(e) => setNewPassword(e.target.value)}
          required
          className="w-full px-3 py-2 border border-hami-stroke-border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-hami-bush-800 focus:border-hami-bush-800 bg-hami-surface-elevated text-hami-glyph-base"
          placeholder="新しいパスワード"
        />
      </div>
      <div className="flex gap-3">
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 bg-bush-950 text-bush-50 border border-bush-950 rounded-sm font-semibold cursor-pointer transition duration-200 hover:bg-bush-900 disabled:opacity-50"
        >
          {loading ? '変更中...' : '変更する'}
        </button>
      </div>
    </form>
  );
}
