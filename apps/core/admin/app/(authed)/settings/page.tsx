import Link from 'next/link';
import { PageHeader } from '@core-admin/components/layout/PageHeader';

export default function SettingsTopPage() {
  return (
    <div className="px-6 py-8">
      <div className="max-w-3xl mx-auto">
        <PageHeader
          title="システム設定"
          breadcrumbItems={[{ label: 'ホーム', href: '/' }, { label: 'システム設定' }]}
        />
        <div className="bg-white rounded-lg shadow-sm border p-6 space-y-4">
          <div>
            <h2 className="text-lg font-semibold mb-2">アカウント</h2>
            <ul className="list-disc list-inside">
              <li>
                <Link href="/settings/password" className="text-blue-600 hover:underline">
                  パスワード変更
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}

