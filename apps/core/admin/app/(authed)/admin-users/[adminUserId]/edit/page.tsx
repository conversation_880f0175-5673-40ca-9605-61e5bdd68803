'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { updateAdminUser, listAdminUsers } from '@core-admin/api_clients/admin_user_client';
import { AdminUser } from '@hami/core-admin-api-schema/admin_user_service_pb';
import Link from 'next/link';

interface EditAdminUserPageProps {
  params: Promise<{
    adminUserId: string;
  }>;
}

export default function EditAdminUserPage({ params }: EditAdminUserPageProps) {
  const router = useRouter();
  const [adminUserId, setAdminUserId] = useState<string>('');
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadParams = async () => {
      const resolvedParams = await params;
      setAdminUserId(resolvedParams.adminUserId);
    };
    loadParams();
  }, [params]);

  useEffect(() => {
    if (!adminUserId) return;

    const fetchAdminUser = async () => {
      try {
        const result = await listAdminUsers({});
        const user = result.adminUsers.find((u) => u.adminUserId === adminUserId);

        if (!user) {
          setError('管理ユーザーが見つかりません。');
          return;
        }

        setAdminUser(user);
        setFormData({
          name: user.name,
          email: user.email,
          password: '',
        });
      } catch (err) {
        setError('管理ユーザー情報の取得に失敗しました。');
        console.error('Failed to fetch admin user:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAdminUser();
  }, [adminUserId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const updateData: {
        adminUserId: string;
        name?: string;
        email?: string;
        password?: string;
      } = {
        adminUserId,
      };

      if (formData.name !== adminUser?.name) {
        updateData.name = formData.name;
      }
      if (formData.email !== adminUser?.email) {
        updateData.email = formData.email;
      }
      if (formData.password.trim() !== '') {
        updateData.password = formData.password;
      }

      await updateAdminUser(updateData);
      router.push('/admin-users');
    } catch (err) {
      setError('管理ユーザーの更新に失敗しました。');
      console.error('Failed to update admin user:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  if (isLoading) {
    return (
      <div className="px-6 py-8">
        <div className="max-w-[800px] w-full flex flex-col gap-8">
          <div className="text-[16px] text-hami-glyph-subtle">読み込み中...</div>
        </div>
      </div>
    );
  }

  if (error && !adminUser) {
    return (
      <div className="px-6 py-8">
        <div className="max-w-[800px] w-full flex flex-col gap-8">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{error}</div>
          <Link href="/admin-users" className="text-hami-brand-primary hover:text-hami-brand-primary-hover underline">
            管理ユーザー一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="px-6 py-8">
      <div className="max-w-[800px] w-full flex flex-col gap-8">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2">
          <Link href="/admin-users" className="text-[16px] text-hami-brand-primary hover:text-hami-brand-primary-hover underline">
            管理ユーザー一覧
          </Link>
          <span className="text-[16px] text-hami-glyph-subtle">›</span>
          <span className="text-[16px] text-hami-glyph-base">編集</span>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-[24px] font-bold text-hami-glyph-base">管理ユーザー編集</h1>
        </div>

        {/* Error Message */}
        {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{error}</div>}

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-hami-surface-elevated border border-hami-stroke-border rounded-lg p-6">
          <div className="flex flex-col gap-6">
            <div>
              <label htmlFor="name" className="block text-[16px] font-medium text-hami-glyph-base mb-2">
                名前 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-hami-stroke-border rounded-lg text-[16px] text-hami-glyph-base bg-white focus:outline-none focus:ring-2 focus:ring-hami-brand-primary focus:border-transparent"
                placeholder="管理ユーザーの名前を入力してください"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-[16px] font-medium text-hami-glyph-base mb-2">
                メールアドレス <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-hami-stroke-border rounded-lg text-[16px] text-hami-glyph-base bg-white focus:outline-none focus:ring-2 focus:ring-hami-brand-primary focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-[16px] font-medium text-hami-glyph-base mb-2">
                パスワード
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-hami-stroke-border rounded-lg text-[16px] text-hami-glyph-base bg-white focus:outline-none focus:ring-2 focus:ring-hami-brand-primary focus:border-transparent"
                placeholder="変更する場合のみ入力してください"
              />
              <p className="text-[14px] text-hami-glyph-subtle mt-1">パスワードを変更しない場合は空欄のままにしてください</p>
            </div>

            <div className="flex gap-4 pt-4">
              <button type="submit" disabled={isSubmitting} className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed">
                {isSubmitting ? '更新中...' : '更新'}
              </button>
              <Link href="/admin-users" className="btn-secondary">
                キャンセル
              </Link>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
