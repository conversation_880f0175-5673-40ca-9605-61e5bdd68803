'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createAdminUser } from '@core-admin/api_clients/admin_user_client';
import Link from 'next/link';

export default function CreateAdminUserPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      await createAdminUser(formData);
      router.push('/admin-users');
    } catch (err) {
      setError('管理ユーザーの作成に失敗しました。');
      console.error('Failed to create admin user:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div className="px-6 py-8">
      <div className="max-w-[800px] w-full flex flex-col gap-8">
        {/* Breadcrumb */}
        <div className="flex items-center gap-2">
          <Link href="/admin-users" className="text-[16px] text-hami-brand-primary hover:text-hami-brand-primary-hover underline">
            管理ユーザー一覧
          </Link>
          <span className="text-[16px] text-hami-glyph-subtle">›</span>
          <span className="text-[16px] text-hami-glyph-base">新規作成</span>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-[24px] font-bold text-hami-glyph-base">管理ユーザー新規作成</h1>
        </div>

        {/* Error Message */}
        {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{error}</div>}

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-hami-surface-elevated border border-hami-stroke-border rounded-lg p-6">
          <div className="flex flex-col gap-6">
            <div>
              <label htmlFor="name" className="block text-[16px] font-medium text-hami-glyph-base mb-2">
                名前 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-hami-stroke-border rounded-lg text-[16px] text-hami-glyph-base bg-white focus:outline-none focus:ring-2 focus:ring-hami-brand-primary focus:border-transparent"
                placeholder="管理ユーザーの名前を入力してください"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-[16px] font-medium text-hami-glyph-base mb-2">
                メールアドレス <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-hami-stroke-border rounded-lg text-[16px] text-hami-glyph-base bg-white focus:outline-none focus:ring-2 focus:ring-hami-brand-primary focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="flex gap-4 pt-4">
              <button type="submit" disabled={isSubmitting} className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed">
                {isSubmitting ? '作成中...' : '作成'}
              </button>
              <Link href="/admin-users" className="btn-secondary">
                キャンセル
              </Link>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
