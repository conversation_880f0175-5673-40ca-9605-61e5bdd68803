'use client';
import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import {
  getMemberRetirementApplication,
  approveMemberRetirementApplication,
  rejectMemberRetirementApplication,
} from '@core-admin/api_clients/member_retirement_application_client';
import { getMember } from '@core-admin/api_clients/member_client';
import type { MemberRetirementApplication } from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import type { Member } from '@hami/core-admin-api-schema/member_service_pb';
import Link from 'next/link';

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [application, setApplication] = useState<MemberRetirementApplication | undefined>(undefined);
  const [member, setMember] = useState<Member | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const id = Number((await params).id);
        const { application } = await getMemberRetirementApplication({ memberRetirementApplicationId: id });
        setApplication(application);
        if (application) {
          const { member } = await getMember({ memberId: application.memberId });
          setMember(member);
        }
      } catch (_e) {
        setError('データ取得に失敗しました');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [params]);

  const handleApprove = useCallback(async () => {
    if (!application) return;
    setProcessing(true);
    try {
      await approveMemberRetirementApplication({ memberRetirementApplicationId: application.memberRetirementApplicationId });
      router.push('/member-retirement');
    } catch {
      setError('承認に失敗しました');
    } finally {
      setProcessing(false);
    }
  }, [application, router]);

  const handleReject = useCallback(async () => {
    if (!application) return;
    setProcessing(true);
    try {
      await rejectMemberRetirementApplication({ memberRetirementApplicationId: application.memberRetirementApplicationId });
      router.push('/member-retirement');
    } catch {
      setError('却下に失敗しました');
    } finally {
      setProcessing(false);
    }
  }, [application, router]);

  if (loading) {
    return (
      <main className="flex min-h-screen flex-col items-center justify-start p-24">
        <div className="w-full max-w-4xl text-center">
          <p className="text-muted">読み込み中...</p>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="flex min-h-screen flex-col items-center justify-start p-24">
        <div className="w-full max-w-4xl">
          <p className="text-error font-semibold">{error}</p>
        </div>
      </main>
    );
  }

  if (!application || !member) return null;

  return (
    <main className="flex min-h-screen flex-col items-center justify-start p-24">
      {/* ページタイトル */}
      <h1 className="text-xl font-bold mb-6 text-primary">退会申請詳細</h1>

      {/* 申請情報セクション */}
      <div className="w-full max-w-4xl mb-8">
        <h2 className="text-lg font-bold mb-4 text-primary">申請情報</h2>
        <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded-lg shadow-sm">
          <table className="w-full">
            <tbody>
              <tr className="table-row">
                <th className="table-header-cell w-1/3 bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">申込日時</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {new Date(application.applicationDate).toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' })}
                  </span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">最終更新者</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">申請者</span>
                </td>
              </tr>
              {Boolean(application.approvedAt) && (
                <tr className="table-row">
                  <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                    <span className="table-cell-text">承認日時</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {new Date(application.approvedAt).toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' })}
                    </span>
                  </td>
                </tr>
              )}
              {Boolean(application.rejectedAt) && (
                <tr className="table-row">
                  <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                    <span className="table-cell-text">却下日時</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {new Date(application.rejectedAt).toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' })}
                    </span>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 会員情報セクション */}
      <div className="w-full max-w-4xl mb-8">
        <h2 className="text-lg font-bold mb-4 text-primary">会員情報</h2>
        <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded-lg shadow-sm">
          <table className="w-full">
            <tbody>
              <tr className="table-row">
                <th className="table-header-cell w-1/3 bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">会員氏名</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {member.lastName} {member.firstName}
                  </span>
                  <div className="mt-2">
                    <Link
                      href={`/members/${member.memberId}`}
                      className="text-info underline hover:text-primary"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      会員詳細を見る
                    </Link>
                  </div>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">会員氏名（フリガナ）</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {member.lastNameKana} {member.firstNameKana}
                  </span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">郵便番号</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{member.postalCode}</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">住所</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {member.prefecture}
                    {member.address}
                  </span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">住所（マンション名など）</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{member.apartment || '-'}</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">メールアドレス</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{member.email}</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">連絡先電話番号</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{member.phoneNumber}</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header-cell bg-shark-100 text-secondary font-bold text-left p-3" scope="row">
                  <span className="table-cell-text">生年月日</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {member.birthYear}年{member.birthMonth}月{member.birthDay}日
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* アクションボタン */}
      <div className="w-full max-w-4xl">
        <div className="flex gap-4">
          <button
            onClick={handleApprove}
            disabled={processing || Boolean(application.approvedAt) || Boolean(application.rejectedAt)}
            className="btn-destruction"
            type="button"
          >
            退会を承認する
          </button>
          <button
            onClick={handleReject}
            disabled={processing || Boolean(application.approvedAt) || Boolean(application.rejectedAt)}
            className="btn-secondary disabled:opacity-50"
          >
            退会を却下する
          </button>
          <Link href="/member-retirement" className="btn-ghost">
            一覧に戻る
          </Link>
        </div>
      </div>
    </main>
  );
}
