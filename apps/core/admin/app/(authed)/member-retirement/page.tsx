'use client';
import { useEffect, useState } from 'react';
import { listMemberRetirementApplications } from '@core-admin/api_clients/member_retirement_application_client';
import Link from 'next/link';
import type { MemberRetirementApplication } from '@hami/core-admin-api-schema/member_retirement_application_service_pb';

export default function Page() {
  const [includeApproved, setIncludeApproved] = useState(false);
  const [includeRejected, setIncludeRejected] = useState(false);
  const [applications, setApplications] = useState<MemberRetirementApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    setLoading(true);
    listMemberRetirementApplications({ includeApproved, includeRejected })
      .then((res) => setApplications(res.applications))
      .catch(() => setError('取得に失敗しました'))
      .finally(() => setLoading(false));
  }, [includeApproved, includeRejected]);

  // ステータスに応じたバッジクラスを取得
  const getStatusBadgeClass = (app: MemberRetirementApplication) => {
    if (Boolean(app.approvedAt)) {
      return 'badge badge-success';
    } else if (Boolean(app.rejectedAt)) {
      return 'badge badge-error';
    } else {
      return 'badge badge-warning';
    }
  };

  // ステータステキストを取得
  const getStatusText = (app: MemberRetirementApplication) => {
    if (Boolean(app.approvedAt)) {
      return '退会完了';
    } else if (Boolean(app.rejectedAt)) {
      return '却下';
    } else {
      return '申込中';
    }
  };

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">退会申込一覧</h1>
      </div>
      <div className="container mx-auto px-6">
        {/* フィルター設定 */}
        <div className="w-full max-w-6xl mb-6">
          <div className="flex gap-8 mb-4">
            <label className="flex items-center gap-2">
              <input type="checkbox" checked={includeApproved} onChange={(e) => setIncludeApproved(e.target.checked)} className="mr-2" />
              <span className="text-secondary">受理済の申請を表示する</span>
            </label>
            <label className="flex items-center gap-2">
              <input type="checkbox" checked={includeRejected} onChange={(e) => setIncludeRejected(e.target.checked)} className="mr-2" />
              <span className="text-secondary">却下済の申請を表示する</span>
            </label>
          </div>
        </div>

        {/* ローディング状態 */}
        {loading && (
          <div className="w-full max-w-6xl text-center">
            <p className="text-muted">読み込み中...</p>
          </div>
        )}

        {/* エラー状態 */}
        {error && (
          <div className="w-full max-w-6xl mb-6">
            <p className="text-error font-semibold">{error}</p>
          </div>
        )}

        {/* 申請一覧テーブル */}
        {!loading && !error && (
          <div className="w-full max-w-6xl">
            {applications.length > 0 ? (
              <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded-lg shadow-sm">
                <table className="w-full">
                  <thead>
                    <tr className="table-header">
                      <th>申請日時</th>
                      <th>氏名</th>
                      <th className="text-center">ステータス</th>
                      <th className="text-center">詳細</th>
                    </tr>
                  </thead>
                  <tbody>
                    {applications.map((app) => (
                      <tr key={app.memberRetirementApplicationId} className="table-row">
                        <td className="table-cell">
                          <span className="table-cell-text font-semibold">
                            {new Date(app.applicationDate).toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' })}
                          </span>
                        </td>
                        <td className="table-cell">
                          <span className="table-cell-text">{app.memberName}</span>
                        </td>
                        <td className="table-cell text-center">
                          <span className={getStatusBadgeClass(app)}>{getStatusText(app)}</span>
                        </td>
                        <td className="table-cell text-center">
                          <Link href={`/member-retirement/${app.memberRetirementApplicationId}`} className="btn-ghost text-sm">
                            詳細
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center p-8">
                <p className="text-muted">退会申請はありません</p>
              </div>
            )}
          </div>
        )}
      </div>
    </main>
  );
}
