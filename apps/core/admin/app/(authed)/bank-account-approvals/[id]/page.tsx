'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { BankAccountApprovalDetail, BankAccountApprovalStatus } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { getBankAccountRegistrationDetail } from '@core-admin/api_clients/bank_account_approval_client';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { LoadingSpinner } from '@core-admin/components/common/LoadingSpinner';
import { ErrorMessage } from '@core-admin/components/common/ErrorMessage';
import { DetailView } from '@core-admin/components/bank-account-approval/DetailView';
import { ApprovalForm } from '@core-admin/components/bank-account-approval/ApprovalForm';
import { ApprovalHistory } from '@core-admin/components/bank-account-approval/ApprovalHistory';

export default function BankAccountApprovalDetailPage() {
  const params = useParams();
  const id = parseInt(params.id as string, 10);

  const [detail, setDetail] = useState<BankAccountApprovalDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getBankAccountRegistrationDetail({
        bankAccountRegistrationId: id,
      });

      setDetail(response.detail ?? null);
    } catch (err) {
      console.error('Failed to load bank account approval detail:', err);
      setError('口座登録詳細の取得に失敗しました。');
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      loadData();
    }
  }, [id, loadData]);

  const handleApprovalComplete = () => {
    // 承認・却下完了後にデータを再読み込み
    loadData();
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  if (!detail) {
    return <ErrorMessage message="口座登録詳細が見つかりません。" />;
  }

  const renderApprovalSection = () => {
    // 承認待ちの場合のみ承認フォームを表示
    if (detail.approvalStatus === BankAccountApprovalStatus.PENDING) {
      return <ApprovalForm bankAccountRegistrationId={detail.bankAccountRegistrationId} onApprovalComplete={handleApprovalComplete} />;
    }

    // 承認済み・却下済みの場合は完了メッセージを表示
    const statusText = detail.approvalStatus === BankAccountApprovalStatus.APPROVED ? '承認済み' : '却下済み';
    const statusColor = detail.approvalStatus === BankAccountApprovalStatus.APPROVED ? 'text-success' : 'text-error';

    return (
      <div className="card bg-gray-50">
        <h2 className="text-xl font-bold text-primary mb-4">承認状況</h2>
        <p className={`font-medium ${statusColor}`}>この口座登録は{statusText}です。</p>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Breadcrumb
        items={[
          { label: 'ホーム', href: '/' },
          { label: '口座登録承認管理', href: '/bank-account-approvals' },
          { label: '詳細', href: `/bank-account-approvals/${id}` },
        ]}
      />

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-primary">口座登録承認詳細</h1>
      </div>

      {/* 詳細情報表示 */}
      <DetailView detail={detail} />

      {/* 承認処理セクション */}
      {renderApprovalSection()}

      {/* 承認履歴 */}
      <ApprovalHistory histories={detail.approvalHistories} />
    </div>
  );
}
