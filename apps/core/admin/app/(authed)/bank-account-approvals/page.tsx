'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { BankAccountRegistrationItem, BankAccountApprovalStatus } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { listAllBankAccountRegistrations } from '@core-admin/api_clients/bank_account_approval_client';

import { LoadingSpinner } from '@core-admin/components/common/LoadingSpinner';
import { ErrorMessage } from '@core-admin/components/common/ErrorMessage';
import { formatDate, formatDateTime } from '@core-admin/utils/bank_account_utils';

const PAGE_SIZE = 20;

export default function BankAccountApprovalsPage() {
  const [items, setItems] = useState<BankAccountRegistrationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const [statusFilter, setStatusFilter] = useState<BankAccountApprovalStatus | undefined>(undefined);

  const loadData = useCallback(
    async (page: number, filterStatus?: BankAccountApprovalStatus | undefined) => {
      try {
        setLoading(true);
        setError(null);

        // filterStatusが渡された場合はそれを使用、そうでなければ現在のstatusFilterを使用
        const actualFilter = arguments.length > 1 ? filterStatus : statusFilter;

        const response = await listAllBankAccountRegistrations({
          page,
          pageSize: PAGE_SIZE,
          statusFilter: actualFilter,
        });

        setItems(response.items);
        setTotalCount(response.totalCount);
        setCurrentPage(response.page);
      } catch (err) {
        console.error('Failed to load bank account approvals:', err);
        setError('口座登録一覧の取得に失敗しました。');
      } finally {
        setLoading(false);
      }
    },
    [statusFilter]
  );

  useEffect(() => {
    loadData(1);
  }, [loadData]);

  const handlePageChange = (page: number) => {
    loadData(page);
  };

  const handleStatusFilterChange = (status: BankAccountApprovalStatus | undefined) => {
    setStatusFilter(status);
    setCurrentPage(1);
    // 新しいフィルタ状態でデータを再読み込み
    loadData(1, status);
  };

  const totalPages = Math.ceil(totalCount / PAGE_SIZE);

  const getStatusBadge = (status: BankAccountApprovalStatus) => {
    switch (status) {
      case BankAccountApprovalStatus.PENDING:
        return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">承認待ち</span>;
      case BankAccountApprovalStatus.APPROVED:
        return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">承認済み</span>;
      case BankAccountApprovalStatus.REJECTED:
        return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">却下済み</span>;
      default:
        return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">不明</span>;
    }
  };

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">会員口座登録承認</h1>
      </div>
      <div className="container mx-auto px-6">
        {/* フィルタフォーム */}
        <div className="bg-surface-card border border-stroke-separator rounded-lg p-6 mb-6">
          <div className="space-y-4">
            {/* ステータスフィルタ */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">ステータスで絞り込み</label>
              <div className="flex gap-2 flex-wrap">
                <button
                  type="button"
                  onClick={() => handleStatusFilterChange(undefined)}
                  className={`px-3 py-1 text-sm rounded ${
                    statusFilter === undefined ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  全て
                </button>
                <button
                  type="button"
                  onClick={() => handleStatusFilterChange(BankAccountApprovalStatus.PENDING)}
                  className={`px-3 py-1 text-sm rounded ${
                    statusFilter === BankAccountApprovalStatus.PENDING
                      ? 'bg-yellow-600 text-white'
                      : 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                  }`}
                >
                  承認待ち
                </button>
                <button
                  type="button"
                  onClick={() => handleStatusFilterChange(BankAccountApprovalStatus.APPROVED)}
                  className={`px-3 py-1 text-sm rounded ${
                    statusFilter === BankAccountApprovalStatus.APPROVED
                      ? 'bg-green-600 text-white'
                      : 'bg-green-100 text-green-800 hover:bg-green-200'
                  }`}
                >
                  承認済み
                </button>
                <button
                  type="button"
                  onClick={() => handleStatusFilterChange(BankAccountApprovalStatus.REJECTED)}
                  className={`px-3 py-1 text-sm rounded ${
                    statusFilter === BankAccountApprovalStatus.REJECTED
                      ? 'bg-red-600 text-white'
                      : 'bg-red-100 text-red-800 hover:bg-red-200'
                  }`}
                >
                  却下済み
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* エラー表示 */}
        {error && <ErrorMessage message={error} />}

        {/* ローディング表示 */}
        {loading && <LoadingSpinner />}

        {/* 一覧表示 */}
        {!loading && !error && (
          <div className="bg-surface-card border border-stroke-separator rounded-lg">
            <div className="p-6 border-b border-gray-200">
              <p className="text-sm text-gray-600">
                全 {totalCount} 件中 {(currentPage - 1) * PAGE_SIZE + 1} - {Math.min(currentPage * PAGE_SIZE, totalCount)} 件を表示
              </p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left py-4 px-6 font-semibold text-gray-700 border-b border-gray-200">会員ID</th>
                    <th className="text-left py-4 px-6 font-semibold text-gray-700 border-b border-gray-200">会員氏名</th>
                    <th className="text-left py-4 px-6 font-semibold text-gray-700 border-b border-gray-200">口座名義</th>
                    <th className="text-left py-4 px-6 font-semibold text-gray-700 border-b border-gray-200">登録日</th>
                    <th className="text-left py-4 px-6 font-semibold text-gray-700 border-b border-gray-200">ステータス</th>
                    <th className="text-left py-4 px-6 font-semibold text-gray-700 border-b border-gray-200">承認者・日時</th>
                    <th className="text-left py-4 px-6 font-semibold text-gray-700 border-b border-gray-200">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {items.length === 0 ? (
                    <tr className="table-header">
                      <td colSpan={7} className="text-center py-12 text-gray-500">
                        口座登録はありません
                      </td>
                    </tr>
                  ) : (
                    items.map((item) => (
                      <tr key={item.bankAccountRegistrationId} className="table-row">
                        <td className="py-4 px-6 text-gray-900">{item.memberId}</td>
                        <td className="py-4 px-6">
                          <div>
                            <div className="font-medium text-gray-900">{item.memberName}</div>
                            <div className="text-sm text-gray-500">{item.memberNameKana}</div>
                          </div>
                        </td>
                        <td className="py-4 px-6 font-medium text-blue-600">{item.resultAccountName}</td>
                        <td className="py-4 px-6 text-gray-700">{formatDate(item.completedAt)}</td>
                        <td className="py-4 px-6">{getStatusBadge(item.approvalStatus || BankAccountApprovalStatus.PENDING)}</td>
                        <td className="py-4 px-6">
                          {item.adminUserName ? (
                            <div className="text-sm">
                              <div className="font-medium text-gray-900">{item.adminUserName}</div>
                              <div className="text-gray-500">{formatDateTime(item.approvedAt)}</div>
                            </div>
                          ) : (
                            <span className="text-gray-400 text-sm">-</span>
                          )}
                        </td>
                        <td className="py-4 px-6">
                          <Link href={`/bank-account-approvals/${item.bankAccountRegistrationId}`} className="btn-ghost text-sm">
                            詳細
                          </Link>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {/* ページネーション */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div className="flex justify-center items-center gap-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage <= 1}
                    className="btn-ghost text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    前へ
                  </button>

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                    return (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          page === currentPage
                            ? 'bg-blue-600 text-white border border-blue-600'
                            : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages}
                    className="btn-ghost text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    次へ
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </main>
  );
}
