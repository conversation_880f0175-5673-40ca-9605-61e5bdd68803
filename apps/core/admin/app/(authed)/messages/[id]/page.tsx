import Link from 'next/link';
import { getMessage } from '@core-admin/api_clients/admin_message_client';
import { MessageDetail } from '@core-admin/components/messages/MessageDetail';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';

export default async function MessageDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const messageId = resolvedParams.id;

  let message;
  let error: string | null = null;

  try {
    const result = await getMessage({ messageId });
    message = result.message;
  } catch (err) {
    console.error('Failed to fetch message:', err);
    error = 'メッセージの取得中にエラーが発生しました。';
  }

  if (error || !message) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          {/* ヘッダー */}
          <div className="mb-8">
            <Breadcrumb
              items={[
                { label: 'メッセージ管理', href: '/messages' },
                { label: 'メッセージ詳細', isCurrent: true }
              ]}
            />
          </div>

          {/* エラー表示 */}
          <div className="bg-surface-card border border-stroke-separator p-8 text-center rounded">
            <div className="text-error font-semibold mb-4">{error || 'メッセージが見つかりません'}</div>
            <Link
              href="/messages"
              className="btn-secondary"
            >
              メッセージ一覧に戻る
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        {/* ヘッダー */}
        <div className="mb-8">
            <Breadcrumb
              items={[
                { label: 'メッセージ管理', href: '/messages' },
                { label: 'メッセージ詳細', isCurrent: true }
              ]}
            />
        </div>

        {/* メッセージ詳細 */}
        <MessageDetail message={message} />

        {/* フッター */}
        <div className="mt-8 flex justify-between">
          <Link
            href="/messages"
            className="btn-secondary"
          >
            ← メッセージ一覧に戻る
          </Link>
          <Link
            href={`/messages/${message.messageId}/delivery`}
            className="btn-primary"
          >
            配信状況を確認 →
          </Link>
        </div>
      </div>
    </main>
  );
}
