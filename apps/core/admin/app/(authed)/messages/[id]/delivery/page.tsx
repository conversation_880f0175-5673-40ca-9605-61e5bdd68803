'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { Message, MessageRecipientStatus, RecipientStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { getMessage, getDeliveryStatus } from '@core-admin/api_clients/admin_message_client';
import { DeliveryStatusOverview } from '@core-admin/components/messages/DeliveryStatusOverview';
import { DeliveryStats } from '@core-admin/components/messages/DeliveryStats';
import { DeliveryStatusTable } from '@core-admin/components/messages/DeliveryStatusTable';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';

export default function DeliveryStatusPage({ params }: { params: Promise<{ id: string }> }) {
  const [messageId, setMessageId] = useState<string>('');
  const [message, setMessage] = useState<Message | null>(null);
  const [recipients, setRecipients] = useState<MessageRecipientStatus[]>([]);
  const [allRecipients, setAllRecipients] = useState<MessageRecipientStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // ページネーション状態
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 20;

  // フィルター状態
  const [statusFilter, setStatusFilter] = useState<RecipientStatus | undefined>();

  // パラメータの解決
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setMessageId(resolvedParams.id);
    };
    resolveParams();
  }, [params]);

  const fetchMessage = useCallback(async () => {
    if (!messageId) return;

    try {
      const result = await getMessage({ messageId });
      setMessage(result.message || null);
    } catch (err) {
      setError('メッセージの取得中にエラーが発生しました。');
      console.error('Failed to fetch message:', err);
    }
  }, [messageId]);

  const fetchDeliveryStatus = useCallback(async () => {
    if (!messageId) return;

    setLoading(true);
    setError(null);

    try {
      const result = await getDeliveryStatus({
        messageId,
        page: currentPage,
        pageSize,
        statusFilter,
      });

      setRecipients(result.recipients);
      setTotalCount(result.totalCount);
      setTotalPages(result.totalPages);

      // 統計用に全受信者データを取得（フィルターなし）
      if (!statusFilter) {
        setAllRecipients(result.recipients);
      } else if (allRecipients.length === 0) {
        // フィルターありの場合、統計用に全データを別途取得
        const allResult = await getDeliveryStatus({
          messageId,
          page: 1,
          pageSize: 1000, // 大きな値で全データを取得
        });
        setAllRecipients(allResult.recipients);
      }
    } catch (err) {
      setError('配信状況の取得中にエラーが発生しました。');
      console.error('Failed to fetch delivery status:', err);
    } finally {
      setLoading(false);
    }
  }, [messageId, currentPage, statusFilter, allRecipients.length]);

  useEffect(() => {
    if (messageId) {
      fetchMessage();
      fetchDeliveryStatus();
    }
  }, [messageId, fetchMessage, fetchDeliveryStatus]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleStatusFilterChange = (status?: RecipientStatus) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  if (!messageId) {
    return <div>Loading...</div>;
  }

  if (error && !message) {
    return (
      <main className="bg-white min-h-screen">
        <div className="container mx-auto px-6 py-8">
          <div className="bg-surface-card border border-stroke-separator p-8 text-center rounded">
            <div className="text-error font-semibold mb-4">{error}</div>
            <Link
              href="/messages"
              className="btn-secondary"
            >
              メッセージ一覧に戻る
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        {/* ヘッダー */}
        <div className="mb-8">
          <Breadcrumb
            items={[
              { label: 'メッセージ管理', href: '/messages' },
              { label: 'メッセージ詳細', href: `/messages/${messageId}` },
              { label: '配信状況', isCurrent: true }
            ]}
          />
        </div>

        <div className="space-y-8">
          {/* メッセージ概要 */}
          {message && <DeliveryStatusOverview message={message} />}

          {/* 配信統計 */}
          <DeliveryStats recipients={allRecipients} />

          {/* 配信状況テーブル */}
          <DeliveryStatusTable
            recipients={recipients}
            loading={loading}
            error={error}
            currentPage={currentPage}
            totalPages={totalPages}
            totalCount={totalCount}
            onPageChange={handlePageChange}
            statusFilter={statusFilter}
            onStatusFilterChange={handleStatusFilterChange}
          />
        </div>

        {/* フッター */}
        <div className="mt-8 flex justify-between">
          <Link
            href={`/messages/${messageId}`}
            className="btn-secondary"
          >
            ← メッセージ詳細に戻る
          </Link>
          <Link
            href="/messages"
            className="btn-primary"
          >
            メッセージ一覧に戻る
          </Link>
        </div>
      </div>
    </main>
  );
}
