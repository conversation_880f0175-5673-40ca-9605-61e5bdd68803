'use client';

import { useState, useEffect, useCallback } from 'react';
import { Message, MessageType, MessageStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { listMessages } from '@core-admin/api_clients/admin_message_client';
import { MessageList } from '@core-admin/components/messages/MessageList';
import { MessageFilters } from '@core-admin/components/messages/MessageFilters';

export default function MessagesPage() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // フィルター状態
  const [messageType, setMessageType] = useState<MessageType | undefined>();
  const [status, setStatus] = useState<MessageStatus | undefined>();
  const [searchQuery, setSearchQuery] = useState('');

  // ページネーション状態
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 20;

  // ソート状態
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const fetchMessages = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await listMessages({
        messageType,
        status,
        searchQuery: searchQuery || undefined,
        page: currentPage,
        pageSize,
        sortBy,
        sortOrder,
      });

      setMessages(result.messages);
      setTotalCount(result.totalCount);
      setTotalPages(result.totalPages);
    } catch (err) {
      setError('メッセージの取得中にエラーが発生しました。しばらくしてから再度お試しください。');
      console.error('Failed to fetch messages:', err);
    } finally {
      setLoading(false);
    }
  }, [messageType, status, searchQuery, currentPage, sortBy, sortOrder]);

  useEffect(() => {
    fetchMessages();
  }, [fetchMessages]);

  const handleFilterReset = () => {
    setMessageType(undefined);
    setStatus(undefined);
    setSearchQuery('');
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setCurrentPage(1);
  };

  // フィルター変更時にページを1に戻す
  useEffect(() => {
    setCurrentPage(1);
  }, [messageType, status, searchQuery]);

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">メッセージ管理</h1>
      </div>
      <div className="container mx-auto px-6">
        {/* フィルター */}
        <MessageFilters
          messageType={messageType}
          status={status}
          searchQuery={searchQuery}
          onMessageTypeChange={setMessageType}
          onStatusChange={setStatus}
          onSearchQueryChange={setSearchQuery}
          onReset={handleFilterReset}
        />

        {/* メッセージ一覧 */}
        <MessageList
          messages={messages}
          loading={loading}
          error={error}
          currentPage={currentPage}
          totalPages={totalPages}
          totalCount={totalCount}
          onPageChange={handlePageChange}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={handleSortChange}
        />
      </div>
    </main>
  );
}
