import Link from 'next/link';
import { notFound } from 'next/navigation';
import { getMemberInformationChangeApplication } from '@core-admin/api_clients/member_information_change_client';
import { MemberInformationChangeStatus } from '@hami/core-admin-api-schema/member_information_change_service_pb';
import { MemberInformationChangeDetail } from '../components/member_information_change_detail';
import { MemberInformationChangeComparison } from '../components/member_information_change_comparison';
import { ReviewForm } from '../components/review_form';
import { ReviewHistory } from '../components/review_history';

interface PageProps {
  params: Promise<{ id: string }>;
}

const statusLabels = {
  [MemberInformationChangeStatus.UNSPECIFIED]: '未指定',
  [MemberInformationChangeStatus.PENDING]: '審査中',
  [MemberInformationChangeStatus.APPROVED]: '承認済み',
  [MemberInformationChangeStatus.REJECTED]: '拒否',
  [MemberInformationChangeStatus.CANCELLED]: 'キャンセル',
} as const;

const statusBadges = {
  [MemberInformationChangeStatus.UNSPECIFIED]: 'badge badge-info',
  [MemberInformationChangeStatus.PENDING]: 'badge badge-warning',
  [MemberInformationChangeStatus.APPROVED]: 'badge badge-success',
  [MemberInformationChangeStatus.REJECTED]: 'badge badge-error',
  [MemberInformationChangeStatus.CANCELLED]: 'badge badge-info',
} as const;

export default async function MemberInformationChangeDetailPage({ params }: PageProps) {
  const { id } = await params;
  const applicationId = parseInt(id, 10);

  if (isNaN(applicationId)) {
    notFound();
  }

  const response = await getMemberInformationChangeApplication({
    memberInformationChangeApplicationId: applicationId,
  });

  if (!response.application) {
    notFound();
  }

  const { application, reviewLogs } = response;
  const canReview = application.status === MemberInformationChangeStatus.PENDING;

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">登録情報変更申請詳細</h1>
      </div>

      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between mb-4">
          <div></div>
          <Link href="/member-information-change" className="btn-secondary">
            一覧に戻る
          </Link>
        </div>

        {/* 申請基本情報 */}
        <div className="mb-6 bg-surface-card border border-stroke-separator rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">申請基本情報</h2>
            <span className={statusBadges[application.status]}>{statusLabels[application.status]}</span>
          </div>

          <MemberInformationChangeDetail application={application} />
        </div>

        {/* 変更内容比較 */}
        <div className="mb-6 bg-surface-card border border-stroke-separator rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">変更内容</h2>
          <MemberInformationChangeComparison application={application} />
        </div>

        {/* 審査フォーム */}
        {canReview && (
          <div className="mb-6 bg-surface-card border border-stroke-separator rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-4">審査</h2>
            <ReviewForm applicationId={applicationId} />
          </div>
        )}

        {/* 審査履歴 */}
        {reviewLogs.length > 0 && (
          <div className="bg-surface-card border border-stroke-separator rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-4">審査履歴</h2>
            <ReviewHistory reviewLogs={reviewLogs} />
          </div>
        )}
      </div>
    </main>
  );
}
