import { MemberInformationChangeApplication } from '@hami/core-admin-api-schema/member_information_change_service_pb';

interface Props {
  application: MemberInformationChangeApplication;
}

export function MemberInformationChangeDetail({ application }: Props) {
  return (
    <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-2">
      <dt className="font-semibold">申請ID</dt>
      <dd>{application.memberInformationChangeApplicationId}</dd>
      
      <dt className="font-semibold">申請日時</dt>
      <dd>{new Date(Number(application.createdAt?.seconds) * 1000).toLocaleString('ja-JP')}</dd>
      
      <dt className="font-semibold">更新日時</dt>
      <dd>{new Date(Number(application.updatedAt?.seconds) * 1000).toLocaleString('ja-JP')}</dd>
      
      <dt className="font-semibold">会員ID</dt>
      <dd>{application.memberId}</dd>
      
      <dt className="font-semibold">会員番号</dt>
      <dd>{application.memberNumber}</dd>
      
      <dt className="font-semibold">会員名</dt>
      <dd>{application.memberName}</dd>
      
      <dt className="font-semibold">メールアドレス</dt>
      <dd>{application.memberEmail}</dd>
      
      <dt className="font-semibold">変更希望日</dt>
      <dd>{new Date(Number(application.requestedChangeDate?.seconds) * 1000).toLocaleDateString('ja-JP')}</dd>
      
      {application.reason && (
        <>
          <dt className="font-semibold">変更理由</dt>
          <dd className="whitespace-pre-wrap">{application.reason}</dd>
        </>
      )}
    </dl>
  );
}
