import { MemberInformationChangeApplication } from '@hami/core-admin-api-schema/member_information_change_service_pb';

interface Props {
  application: MemberInformationChangeApplication;
}

interface ComparisonRowProps {
  label: string;
  currentValue: string;
  newValue?: string;
}

function ComparisonRow({ label, currentValue, newValue }: ComparisonRowProps) {
  const hasChange = newValue !== undefined && newValue !== currentValue;

  return (
    <tr className={hasChange ? 'bg-yellow-50' : ''}>
      <td className="border px-4 py-2 font-semibold bg-gray-50">{label}</td>
      <td className="border px-4 py-2">{currentValue || '-'}</td>
      <td className={`border px-4 py-2 ${hasChange ? 'bg-yellow-100 font-semibold' : ''}`}>
        {newValue !== undefined ? newValue || '-' : '変更なし'}
      </td>
    </tr>
  );
}

export function MemberInformationChangeComparison({ application }: Props) {
  return (
    <div className="overflow-x-auto">
      <table className="table-auto border-collapse border border-gray-300 w-full">
        <thead>
          <tr className="bg-gray-100">
            <th className="border px-4 py-2 text-left">項目</th>
            <th className="border px-4 py-2 text-left">現在の情報</th>
            <th className="border px-4 py-2 text-left">変更後の情報</th>
          </tr>
        </thead>
        <tbody>
          <ComparisonRow label="郵便番号" currentValue={application.currentPostalCode} newValue={application.newPostalCode} />
          <ComparisonRow label="都道府県" currentValue={application.currentPrefecture} newValue={application.newPrefecture} />
          <ComparisonRow label="住所" currentValue={application.currentAddress} newValue={application.newAddress} />
          <ComparisonRow label="建物名・部屋番号" currentValue={application.currentApartment || ''} newValue={application.newApartment} />
          <ComparisonRow label="電話番号" currentValue={application.currentPhoneNumber} newValue={application.newPhoneNumber} />
        </tbody>
      </table>

      <div className="mt-4 text-sm text-gray-600">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-yellow-100 border"></div>
          <span>変更箇所</span>
        </div>
      </div>
    </div>
  );
}
