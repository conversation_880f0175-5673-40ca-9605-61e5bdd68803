'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { reviewMemberInformationChangeApplication } from '@core-admin/api_clients/member_information_change_client';
import { MemberInformationChangeReviewType } from '@hami/core-admin-api-schema/member_information_change_service_pb';

interface Props {
  applicationId: number;
}

export function ReviewForm({ applicationId }: Props) {
  const router = useRouter();
  const [reviewType, setReviewType] = useState<MemberInformationChangeReviewType | null>(null);
  const [comment, setComment] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!reviewType) {
      setError('審査結果を選択してください');
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      await reviewMemberInformationChangeApplication({
        memberInformationChangeApplicationId: applicationId,
        reviewType,
        comment: comment.trim() || undefined,
      });

      // 審査完了後、ページをリロードして最新状態を表示
      router.refresh();
    } catch (err) {
      setError(err instanceof Error ? err.message : '審査の実行に失敗しました');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">{error}</div>}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          審査結果 <span className="text-red-500">*</span>
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="reviewType"
              value={MemberInformationChangeReviewType.APPROVE}
              checked={reviewType === MemberInformationChangeReviewType.APPROVE}
              onChange={(e) => setReviewType(parseInt(e.target.value, 10) as MemberInformationChangeReviewType)}
              className="mr-2"
              disabled={submitting}
            />
            <span className="text-green-700 font-medium">承認</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="reviewType"
              value={MemberInformationChangeReviewType.REJECT}
              checked={reviewType === MemberInformationChangeReviewType.REJECT}
              onChange={(e) => setReviewType(parseInt(e.target.value, 10) as MemberInformationChangeReviewType)}
              className="mr-2"
              disabled={submitting}
            />
            <span className="text-red-700 font-medium">拒否</span>
          </label>
        </div>
      </div>

      <div>
        <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-2">
          審査コメント
        </label>
        <textarea
          id="comment"
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="審査に関するコメントがあれば入力してください（任意）"
          disabled={submitting}
        />
      </div>

      <div className="flex gap-4">
        <button
          type="submit"
          disabled={submitting || !reviewType}
          className={`px-6 py-2 rounded font-medium ${
            submitting || !reviewType
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : reviewType === MemberInformationChangeReviewType.APPROVE
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-red-600 text-white hover:bg-red-700'
          }`}
        >
          {submitting ? '処理中...' : '審査を実行'}
        </button>
      </div>
    </form>
  );
}
