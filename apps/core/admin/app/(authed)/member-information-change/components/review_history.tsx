import {
  MemberInformationChangeReviewLog,
  MemberInformationChangeReviewType,
} from '@hami/core-admin-api-schema/member_information_change_service_pb';

interface Props {
  reviewLogs: MemberInformationChangeReviewLog[];
}

const reviewTypeLabels = {
  [MemberInformationChangeReviewType.UNKNOWN]: '不明',
  [MemberInformationChangeReviewType.APPROVE]: '承認',
  [MemberInformationChangeReviewType.REJECT]: '拒否',
} as const;

const reviewTypeColors = {
  [MemberInformationChangeReviewType.UNKNOWN]: 'bg-gray-100 text-gray-800',
  [MemberInformationChangeReviewType.APPROVE]: 'bg-green-100 text-green-800',
  [MemberInformationChangeReviewType.REJECT]: 'bg-red-100 text-red-800',
} as const;

export function ReviewHistory({ reviewLogs }: Props) {
  // 審査ログを日時の降順でソート（最新が上）
  const sortedLogs = [...reviewLogs].sort((a, b) => {
    const aTime = Number(a.timestamp?.seconds) || 0;
    const bTime = Number(b.timestamp?.seconds) || 0;
    return bTime - aTime;
  });

  return (
    <div className="space-y-4">
      {sortedLogs.map((log) => (
        <div key={log.memberInformationChangeReviewLogId} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-3">
              <span className={`px-2 py-1 rounded text-xs font-medium ${reviewTypeColors[log.reviewType]}`}>
                {reviewTypeLabels[log.reviewType]}
              </span>
              <span className="text-sm font-medium text-gray-700">審査者: {log.reviewer}</span>
            </div>
            <span className="text-sm text-gray-500">{new Date(Number(log.timestamp?.seconds) * 1000).toLocaleString('ja-JP')}</span>
          </div>

          {log.comment && (
            <div className="mt-2">
              <p className="text-sm text-gray-700 whitespace-pre-wrap">{log.comment}</p>
            </div>
          )}
        </div>
      ))}

      {sortedLogs.length === 0 && <p className="text-gray-500 text-center py-4">審査履歴はありません</p>}
    </div>
  );
}
