import Link from 'next/link';
import { getMemberInformationChangeApplications } from '@core-admin/api_clients/member_information_change_client';
import {
  MemberInformationChangeStatus,
  MemberInformationChangeSortField,
} from '@hami/core-admin-api-schema/member_information_change_service_pb';
import { MemberInformationChangeApplicationsTable } from './components/member_information_change_applications_table';

interface PageProps {
  searchParams: Promise<{
    page?: string;
    limit?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: string;
  }>;
}

const statusLabels = {
  [MemberInformationChangeStatus.UNSPECIFIED]: '未指定',
  [MemberInformationChangeStatus.PENDING]: '審査中',
  [MemberInformationChangeStatus.APPROVED]: '承認済み',
  [MemberInformationChangeStatus.REJECTED]: '拒否',
  [MemberInformationChangeStatus.CANCELLED]: 'キャンセル',
} as const;

export default async function MemberInformationChangePage({ searchParams }: PageProps) {
  const params = await searchParams;
  const page = parseInt(params.page || '1', 10);
  const limit = parseInt(params.limit || '20', 10);
  const status = params.status ? (parseInt(params.status, 10) as MemberInformationChangeStatus) : undefined;
  const sortBy = params.sortBy
    ? (parseInt(params.sortBy, 10) as MemberInformationChangeSortField)
    : MemberInformationChangeSortField.CREATED_AT;
  const sortOrder: 'asc' | 'desc' = params.sortOrder === 'asc' ? 'asc' : params.sortOrder === 'desc' ? 'desc' : 'desc';

  const response = await getMemberInformationChangeApplications({
    page,
    limit,
    status,
    sortBy,
    sortOrder,
  });

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">登録情報変更申請</h1>
      </div>

      <div className="container mx-auto px-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex gap-2 items-center">
            <Link href="/member-information-change" className={`${!status ? 'btn-primary text-sm' : 'btn-ghost text-sm'}`}>
              全て
            </Link>
            {Object.entries(statusLabels).map(([statusValue, label]) => (
              <Link
                key={statusValue}
                href={`/member-information-change?status=${statusValue}`}
                className={`${status === parseInt(statusValue, 10) ? 'btn-primary text-sm' : 'btn-ghost text-sm'}`}
              >
                {label}
              </Link>
            ))}
          </div>
          <div className="flex gap-4 items-center"></div>
        </div>

        <MemberInformationChangeApplicationsTable
          applications={response.applications}
          totalCount={response.totalCount}
          currentPage={page}
          limit={limit}
          currentStatus={status}
          currentSortBy={sortBy}
          currentSortOrder={sortOrder}
        />
      </div>
    </main>
  );
}
