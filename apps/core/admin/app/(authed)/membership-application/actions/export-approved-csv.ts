'use server';

import { exportApprovedMembershipApplicationsCsv } from '@core-admin/api_clients/membership_application_client';

export async function exportApprovedCsvAction() {
  try {
    const res = await exportApprovedMembershipApplicationsCsv({});
    return {
      success: true,
      data: {
        csvFileKey: res.csvFileKey,
        downloadUrl: res.downloadUrl,
        totalApplications: res.totalApplications,
      },
    } as const;
  } catch (error) {
    console.error('CSV export failed:', error);
    return { success: false, error: 'CSV出力に失敗しました。時間をおいて再度お試しください。' } as const;
  }
}


