'use client';

import { useState } from 'react';
import { exportApprovedCsvAction } from '../actions/export-approved-csv';

export function ExportApprovedCsvButton() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onClick = async () => {
    setLoading(true);
    setError(null);
    const res = await exportApprovedCsvAction();
    setLoading(false);
    if (res.success && res.data?.downloadUrl) {
      window.open(res.data.downloadUrl, '_blank');
    } else if (!res.success) {
      setError(res.error);
    }
  };

  return (
    <div className="flex items-center gap-3">
      <button onClick={onClick} className="btn-primary" disabled={loading}>
        {loading ? 'エクスポート中...' : '承認済みCSVを出力'}
      </button>
      {error && <span className="text-error text-sm">{error}</span>}
    </div>
  );
}


