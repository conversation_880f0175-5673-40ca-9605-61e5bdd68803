import { MembershipApplicationDocumentGroupReviewLog, ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { formatDate } from '@core-admin/utils/date_formatter';

function labelReviewType(t: ReviewType): string {
  switch (t) {
    case ReviewType.APPROVE:
      return '承認';
    case ReviewType.REJECT:
      return '否認';
    case ReviewType.REMAND:
      return '差し戻し';
    default:
      return '未審査';
  }
}

export default function DocumentGroupReviewLogs({
  logs,
  title = '書類審査ログ',
}: {
  logs: MembershipApplicationDocumentGroupReviewLog[];
  title?: string;
}) {
  return (
    <div className="card mt-4">
      <h3 className="text-lg font-semibold mb-3">{title}</h3>
      {(!logs || logs.length === 0) && <p className="text-sm text-muted">審査履歴はまだありません。</p>}
      <ul className="space-y-3">
        {logs?.map((log) => (
          <li key={log.membershipApplicationDocumentGroupReviewLogId} className="border-b pb-2 last:border-b-0">
            <div className="text-sm">
              <span className="font-medium">{formatDate(Number(log.createdAt))}</span>
              <span className="mx-2">/</span>
              <span className="text-muted">{log.reviewer}</span>
              <span className="mx-2">/</span>
              <span className="font-medium">{labelReviewType(log.reviewType)}</span>
            </div>
            {log.remandReason && <div className="text-sm text-muted mt-1">{log.remandReason}</div>}
          </li>
        ))}
      </ul>
    </div>
  );
}
