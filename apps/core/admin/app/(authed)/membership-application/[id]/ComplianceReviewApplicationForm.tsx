'use client';
import { reviewComplianceMembershipApplication } from '@core-admin/api_clients/membership_application_client';
import { useReviewForm } from './useReviewForm';
import { ReviewFormBase } from './ReviewFormBase';

interface ComplianceReviewApplicationFormProps {
  membershipApplicationId: number;
  onReviewed?: () => void;
}

export default function ComplianceReviewApplicationForm({ membershipApplicationId, onReviewed }: ComplianceReviewApplicationFormProps) {
  const form = useReviewForm({ membershipApplicationId, onReviewed, apiCall: reviewComplianceMembershipApplication });
  return <ReviewFormBase title="コンプライアンス審査（申込）" submitButtonText="審査を送信" {...form} />;
}
