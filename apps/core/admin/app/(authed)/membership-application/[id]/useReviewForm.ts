import { useState } from 'react';
import { ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';

interface UseReviewFormOptions {
  membershipApplicationId: number;
  onReviewed?: () => void;
  apiCall: (params: { membershipApplicationId: number; reviewType: ReviewType; remandReason: string }) => Promise<unknown>;
}

function labelReviewType(type: ReviewType): string {
  switch (type) {
    case ReviewType.APPROVE:
      return '承認';
    case ReviewType.REJECT:
      return '否認';
    case ReviewType.REMAND:
      return '差し戻し';
    default:
      return '不明';
  }
}

export function useReviewForm({ membershipApplicationId, onReviewed, apiCall }: UseReviewFormOptions) {
  const [reviewType, setReviewType] = useState<ReviewType | null>(null);
  const [note, setNote] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (reviewType === null) {
      setError('審査結果を選択してください。');
      return;
    }
    if (reviewType === ReviewType.REMAND && note.trim() === '') {
      setError('差し戻しの場合は差し戻し事由を入力してください。');
      return;
    }
    const actionLabel = labelReviewType(reviewType);
    const message =
      reviewType === ReviewType.REMAND && note.trim() !== ''
        ? `この申込を「${actionLabel}」として送信します。\n差し戻し事由: ${note}\nよろしいですか？`
        : `この申込を「${actionLabel}」として送信します。よろしいですか？`;

    // ブラウザ標準の確認ダイアログ。
    const confirmed = window.confirm(message);
    if (!confirmed) return;

    setLoading(true);
    setError(null);
    setSuccess(false);
    try {
      await apiCall({ membershipApplicationId, reviewType, remandReason: note });
      setSuccess(true);
      if (onReviewed) onReviewed();
      window.location.reload();
    } catch (_err) {
      setError('審査に失敗しました。');
    } finally {
      setLoading(false);
    }
  };

  return {
    reviewType,
    setReviewType,
    note,
    setNote,
    loading,
    error,
    success,
    handleSubmit,
  };
}
