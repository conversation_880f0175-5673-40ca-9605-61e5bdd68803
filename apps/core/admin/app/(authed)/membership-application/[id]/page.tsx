import { ReviewType, MembershipApplication, ApplicantType } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { getMembershipApplication } from '@core-admin/api_clients/membership_application_client';
import { notFound } from 'next/navigation';
import ReviewApplicationForm from './ReviewApplicationForm';
import DocumentReviewForm from './DocumentReviewForm';
import ApplicationReviewLogs from './ApplicationReviewLogs';
import DocumentGroupReviewLogs from './DocumentGroupReviewLogs';

import IdentityDocumentsList from './IdentityDocumentsList';

import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';

import { MembershipApplicationDetails } from './MembershipApplicationDetails';

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const id = Number(resolvedParams.id);
  if (isNaN(id)) return notFound();

  const {
    membershipApplication: application,
    applicationReviewLogs = [],
    documentGroupReviewLogs = [],
  } = await getMembershipApplication({
    membershipApplicationId: id,
  });
  if (!application) return notFound();

  // 申込者タイプに応じて表示名を決定
  const displayName =
    application.applicantType === ApplicantType.CORPORATE
      ? `${application.corporateName}（${application.representativeName}）`
      : `${application.lastName} ${application.firstName}`;

  const breadcrumbItems = [
    { label: '入会申込一覧', href: '/membership-application' },
    { label: `申込詳細（${displayName}）`, isCurrent: true },
  ];

  return (
    <main className="container mx-auto px-4 py-8">
      <Breadcrumb items={breadcrumbItems} />

      <MembershipApplicationDetails application={application} />

      <div className="mt-6">
        <IdentityDocumentsList documents={application.identityDocuments} />
      </div>

      <div className="mt-6">{renderReviewSection(application)}</div>
      <div className="mt-6">
        <ApplicationReviewLogs logs={applicationReviewLogs} />
      </div>
      <div className="mt-6">
        <DocumentGroupReviewLogs logs={documentGroupReviewLogs} />
      </div>
      <div aria-hidden className="h-24 md:h-32" />
    </main>
  );
}

function renderReviewSection(application: MembershipApplication) {
  // 入会申込審査フォーム - 未審査の場合のみ表示
  if (application.applicationReviewStatus === ReviewType.NOT_REVIEWED) {
    return <ReviewApplicationForm membershipApplicationId={application.membershipApplicationId} />;
  }

  // 入会申込が承認済みで、本人確認が未審査の場合は本人確認審査フォームを表示
  if (application.applicationReviewStatus === ReviewType.APPROVE && application.documentGroupReviewStatus === ReviewType.NOT_REVIEWED) {
    return <DocumentReviewForm membershipApplication={application} />;
  }

  // 本人確認書類が差し戻しの場合は明示的に表示
  if (application.documentGroupReviewStatus === ReviewType.REMAND) {
    return (
      <div className="card bg-warning mt-6">
        <h2 className="text-xl font-bold text-primary mb-4">審査状況</h2>
        <p className="text-warning">本人確認書類は差し戻し済みです。再提出をお待ちください。</p>
      </div>
    );
  }

  // 一次審査が完了している場合は、コンプライアンス審査の案内のみ表示（同ページでは実施しない）
  if (
    application.applicationReviewStatus === ReviewType.APPROVE &&
    application.documentGroupReviewStatus === ReviewType.APPROVE &&
    (application.complianceApplicationReviewStatus === ReviewType.NOT_REVIEWED ||
      application.complianceDocumentGroupReviewStatus === ReviewType.NOT_REVIEWED)
  ) {
    return (
      <div className="card bg-info mt-6">
        <h2 className="text-xl font-bold text-primary mb-4">審査状況</h2>
        <p className="text-info">一次審査は完了しました。</p>
      </div>
    );
  }

  // その他の場合は審査完了メッセージを表示
  return (
    <div className="card bg-success">
      <h2 className="text-xl font-bold text-primary mb-4">審査状況</h2>
      <p className="text-success">この申込の審査は完了しています。</p>
    </div>
  );
}
