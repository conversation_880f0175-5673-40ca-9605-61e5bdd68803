'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { getIdentityDocumentImageUrl } from '@core-admin/api_clients/membership_application_client';

interface IdentityDocumentImageProps {
  identityDocumentId: number;
  displayLabel?: string;
  fileName?: string;
}

export default function IdentityDocumentImage({ identityDocumentId, displayLabel, fileName }: IdentityDocumentImageProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchImageUrl = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await getIdentityDocumentImageUrl({
          identityDocumentId,
        });

        setImageUrl(response.imageUrl);
      } catch (err) {
        console.error('Failed to fetch image URL:', err);
        setError('画像の読み込みに失敗しました');
      } finally {
        setLoading(false);
      }
    };

    fetchImageUrl();
  }, [identityDocumentId]);

  const heading = displayLabel || '本人確認書類';
  const isPdf = (fileName ?? '').toLowerCase().endsWith('.pdf');

  if (loading) {
    return (
      <div className="border rounded-lg p-4 bg-gray-50">
        <h3 className="font-semibold mb-2">{heading}</h3>
        <div className="w-full h-48 bg-gray-200 rounded flex items-center justify-center">
          <div className="animate-pulse">
            <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
          </div>
        </div>
        <p className="text-sm text-gray-500 mt-2">読み込み中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="border rounded-lg p-4 bg-red-50 border-red-200">
        <h3 className="font-semibold mb-2 text-red-800">{heading}</h3>
        <div className="w-full h-48 bg-red-100 rounded flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-500 text-2xl mb-2">⚠️</div>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        </div>
        {fileName && <p className="text-sm text-gray-600 mt-2">ファイル名: {fileName}</p>}
      </div>
    );
  }

  if (!imageUrl) {
    return (
      <div className="border rounded-lg p-4 bg-gray-50">
        <h3 className="font-semibold mb-2">{heading}</h3>
        <div className="w-full h-48 bg-gray-200 rounded flex items-center justify-center">
          <p className="text-gray-500">画像が見つかりません</p>
        </div>
        {fileName && <p className="text-sm text-gray-600 mt-2">ファイル名: {fileName}</p>}
      </div>
    );
  }

  if (isPdf) {
    return (
      <div className="border rounded-lg p-4 bg-gray-50">
        <h3 className="font-semibold mb-2">{heading}</h3>
        <a
          href={imageUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center gap-2 px-3 py-2 rounded bg-primary text-white hover:bg-primary/90"
        >
          PDFを別タブで開く
        </a>
        {fileName && <p className="text-sm text-gray-600 mt-2">ファイル名: {fileName}</p>}
      </div>
    );
  }

  return (
    <Image
      src={imageUrl}
      alt={heading}
      width={800}
      height={600}
      className="w-full h-auto max-h-96 object-contain rounded border cursor-pointer hover:opacity-90 transition-opacity"
      onClick={() => window.open(imageUrl, '_blank', 'noopener,noreferrer')}
      onError={() => {
        setError('画像の表示に失敗しました');
        setImageUrl(null);
      }}
    />
  );
}
