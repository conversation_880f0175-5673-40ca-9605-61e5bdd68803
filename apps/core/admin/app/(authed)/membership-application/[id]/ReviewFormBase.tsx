import { ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';
import React from 'react';

interface ReviewFormBaseProps {
  title: string;
  reviewType: ReviewType | null;
  setReviewType: (type: ReviewType) => void;
  note: string;
  setNote: (note: string) => void;
  loading: boolean;
  error: string | null;
  success: boolean;
  handleSubmit: (e: React.FormEvent) => void;
  submitButtonText?: string;
}

export function ReviewFormBase({
  title,
  reviewType,
  setReviewType,
  note,
  setNote,
  loading,
  error,
  success,
  handleSubmit,
  submitButtonText = '審査を送信',
}: ReviewFormBaseProps) {
  const { labelText, placeholderText } = (() => {
    switch (reviewType) {
      case ReviewType.REJECT:
        return { labelText: '拒否理由（任意）', placeholderText: '拒否理由があれば入力してください' };
      case ReviewType.REMAND:
        return { labelText: '差し戻し事由（必須）', placeholderText: '差し戻し事由を入力してください ※入会申込者に送信されます' };
      default:
        return { labelText: '備考（任意）', placeholderText: '審査に関する補足があれば入力してください' };
    }
  })();

  return (
    <form onSubmit={handleSubmit} className="card">
      <h2 className="text-xl font-bold text-primary mb-6">{title}</h2>

      <div className="mb-6">
        <label className="block font-semibold text-secondary mb-3">審査結果</label>
        <div className="flex gap-4">
          <button
            type="button"
            className={`btn-secondary ${reviewType === ReviewType.APPROVE ? 'bg-success text-white' : ''}`}
            onClick={() => setReviewType(ReviewType.APPROVE)}
            disabled={loading}
          >
            承認
          </button>
          <button
            type="button"
            className={`btn-secondary ${reviewType === ReviewType.REJECT ? 'bg-error text-white' : ''}`}
            onClick={() => setReviewType(ReviewType.REJECT)}
            disabled={loading}
          >
            拒否
          </button>
          <button
            type="button"
            className={`btn-secondary ${reviewType === ReviewType.REMAND ? 'bg-warning text-white' : ''}`}
            onClick={() => setReviewType(ReviewType.REMAND)}
            disabled={loading}
          >
            差し戻し
          </button>
        </div>
      </div>

      <div className="mb-6">
        <label className="block font-semibold text-secondary mb-2">{labelText}</label>
        <textarea
          className="input-base w-full"
          rows={3}
          value={note}
          onChange={(e) => setNote(e.target.value)}
          disabled={loading}
          placeholder={placeholderText}
        />
      </div>

      {error && <div className="text-error mb-4">{error}</div>}
      {success && <div className="text-success mb-4">送信が完了しました。</div>}

      <button type="submit" className="btn-primary disabled:opacity-50" disabled={loading}>
        {loading ? '送信中...' : submitButtonText}
      </button>
    </form>
  );
}
