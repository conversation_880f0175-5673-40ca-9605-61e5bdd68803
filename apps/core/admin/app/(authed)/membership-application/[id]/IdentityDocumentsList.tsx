'use client';

import { IdentityDocument } from '@hami/core-admin-api-schema/membership_application_service_pb';
import IdentityDocumentImage from './IdentityDocumentImage';

interface IdentityDocumentsListProps {
  documents: IdentityDocument[];
}

export default function IdentityDocumentsList({ documents }: IdentityDocumentsListProps) {
  if (!documents || documents.length === 0) {
    return (
      <div className="mt-8 p-4 border rounded bg-gray-50 max-w-4xl w-full">
        <h2 className="text-lg font-bold mb-4">身分証明書</h2>
        <p className="text-gray-500">アップロードされた身分証明書がありません。</p>
      </div>
    );
  }

  return (
    <div className="mt-8 p-4 border rounded bg-gray-50 max-w-4xl w-full">
      <h2 className="text-lg font-bold mb-4">身分証明書</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {documents.map((document, index) => {
          const fileName = document.fileKey.split('/').pop();
          const ordinal = document.personalIndex && document.personalIndex > 0 ? document.personalIndex : index + 1;
          const displayLabel = `本人確認書類（${ordinal}枚目）`;
          return (
            <IdentityDocumentImage
              key={document.identityDocumentId}
              identityDocumentId={document.identityDocumentId}
              displayLabel={displayLabel}
              fileName={fileName}
            />
          );
        })}
      </div>
      <div className="mt-4 text-sm text-gray-600">
        <p>合計 {documents.length} 件の身分証明書がアップロードされています。</p>
        <p className="mt-1">画像やPDFをクリックすると別タブで開きます（画像は拡大表示、PDFはビューワで表示）。</p>
      </div>
    </div>
  );
}
