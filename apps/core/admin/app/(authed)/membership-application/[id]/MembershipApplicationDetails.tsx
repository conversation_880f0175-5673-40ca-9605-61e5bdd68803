import { ApplicantType, MembershipApplication } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { formatDate } from '@core-admin/utils/date_formatter';

export function MembershipApplicationDetails({ application }: { application: MembershipApplication }) {
  return (
    <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded-lg">
      <table className="w-full">
        <tbody>
          <tr className="table-row">
            <td className="table-cell w-1/3">
              <span className="table-cell-text font-semibold text-secondary">申込ID</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{application.membershipApplicationId}</span>
            </td>
          </tr>
          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">申込日時</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{formatDate(Number(application.appliedAt))}</span>
            </td>
          </tr>
          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">更新日時</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{formatDate(Number(application.updatedAt))}</span>
            </td>
          </tr>
          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">メールアドレス</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{application.email}</span>
            </td>
          </tr>
          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">申込者タイプ</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{application.applicantType === ApplicantType.CORPORATE ? '法人' : '個人'}</span>
            </td>
          </tr>

          {application.applicantType === ApplicantType.INDIVIDUAL && (
            <>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">氏名</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {application.lastName} {application.firstName}
                  </span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">氏名（カナ）</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {application.lastNameKana} {application.firstNameKana}
                  </span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">生年月日</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {application.birthYear && application.birthMonth && application.birthDay
                      ? `${application.birthYear}年${application.birthMonth}月${application.birthDay}日`
                      : '-'}
                  </span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">年収</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.annualIncome || '-'}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">預金</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.depositAmount || '-'}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">資産</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.financialAssets || '-'}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">職業</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.occupation || '-'}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">勤務先名</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.companyName || '-'}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">勤務先住所</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.companyAddress || '-'}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">勤務先電話番号</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.companyPhoneNumber || '-'}</span>
                </td>
              </tr>
            </>
          )}

          {application.applicantType === ApplicantType.CORPORATE && (
            <>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">法人名</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.corporateName}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">法人名（カナ）</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.corporateNameKana}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">法人番号</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.corporateNumber}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">設立年月日</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {application.establishedYear && application.establishedMonth && application.establishedDay
                      ? `${application.establishedYear}年${application.establishedMonth}月${application.establishedDay}日`
                      : '-'}
                  </span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">代表者名</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.representativeName}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">代表者名（カナ）</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.representativeNameKana}</span>
                </td>
              </tr>
              <tr className="table-row">
                <td className="table-cell">
                  <span className="table-cell-text font-semibold text-secondary">代表者役職</span>
                </td>
                <td className="table-cell">
                  <span className="table-cell-text">{application.representativePosition}</span>
                </td>
              </tr>
            </>
          )}

          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">郵便番号</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{application.postalCode}</span>
            </td>
          </tr>
          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">都道府県</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{application.prefecture}</span>
            </td>
          </tr>
          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">住所</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{application.address}</span>
            </td>
          </tr>
          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">建物名・部屋番号</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{application.apartment || '-'}</span>
            </td>
          </tr>
          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">電話番号</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{application.phoneNumber}</span>
            </td>
          </tr>
          <tr className="table-row">
            <td className="table-cell">
              <span className="table-cell-text font-semibold text-secondary">個人情報保護方針への同意</span>
            </td>
            <td className="table-cell">
              <span className="table-cell-text">{application.privacyPolicyAgreement ? '同意済み' : '未同意'}</span>
            </td>
          </tr>

          {application.applicantType === ApplicantType.CORPORATE &&
            application.beneficialOwnerDeclarations &&
            application.beneficialOwnerDeclarations.length > 0 && (
              <>
                <tr className="table-row">
                  <td className="table-cell" colSpan={2}>
                    <span className="table-cell-text font-semibold text-secondary">実質的支配者申告</span>
                  </td>
                </tr>
                {application.beneficialOwnerDeclarations.map((declaration, index) => (
                  <tr key={index} className="table-row">
                    <td className="table-cell">
                      <span className="table-cell-text font-semibold text-secondary">実質的支配者{index + 1}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">
                        {declaration.beneficialOwnerName}
                        <br />
                        申告者: {declaration.declarantName}（{declaration.declarantPosition}）
                      </span>
                    </td>
                  </tr>
                ))}
              </>
            )}
        </tbody>
      </table>
    </div>
  );
}
