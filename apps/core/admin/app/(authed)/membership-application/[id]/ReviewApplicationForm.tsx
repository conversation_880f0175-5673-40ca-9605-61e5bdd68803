'use client';
import { reviewMembershipApplication } from '@core-admin/api_clients/membership_application_client';
import { useReviewForm } from './useReviewForm';
import { ReviewFormBase } from './ReviewFormBase';

interface ReviewApplicationFormProps {
  membershipApplicationId: number;
  onReviewed?: () => void;
}

export default function ReviewApplicationForm({ membershipApplicationId, onReviewed }: ReviewApplicationFormProps) {
  const form = useReviewForm({
    membershipApplicationId,
    onReviewed,
    apiCall: reviewMembershipApplication,
  });

  return (
    <div className="space-y-6">
      <ReviewFormBase title="審査" submitButtonText="審査を送信" {...form} />
    </div>
  );
}
