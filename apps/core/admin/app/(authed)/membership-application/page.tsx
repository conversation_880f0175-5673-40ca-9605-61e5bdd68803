'use client';

import { MembershipApplication, ReviewType, ApplicantType, MembershipApplicationListStatus } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { listMembershipApplications } from '@core-admin/api_clients/membership_application_client';
import Link from 'next/link';
import { ExportApprovedCsvButton } from './components/ExportApprovedCsvButton';
import { useState, useEffect } from 'react';

import { formatDate } from '@core-admin/utils/date_formatter';

export default function Page() {
  const [membershipApplications, setMembershipApplications] = useState<MembershipApplication[]>([]);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [statusFilter, setStatusFilter] = useState<MembershipApplicationListStatus>(
    MembershipApplicationListStatus.UNSPECIFIED
  );

  const fetchApplications = async (pageNum: number) => {
    setLoading(true);
    try {
      const { membershipApplications: apps, hasNextPage: hasNext } = await listMembershipApplications({
        pageSize: 30,
        page: pageNum,
        statusFilter,
      });
      setMembershipApplications(apps || []);
      setHasNextPage(hasNext);
    } catch (error) {
      console.error('Failed to fetch applications:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApplications(page);
  }, [page, statusFilter]);

  const handleNextPage = () => {
    if (hasNextPage && !loading) {
      setPage((prev) => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (page > 1 && !loading) {
      setPage((prev) => prev - 1);
    }
  };

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">入会申込者の管理</h1>
      </div>

      <div className="container mx-auto px-6">
        {/* タブ（フィルタ） */}
        <div className="flex gap-2 mb-4">
          {[
            { label: '全て', value: MembershipApplicationListStatus.UNSPECIFIED },
            { label: '要対応', value: MembershipApplicationListStatus.ACTION_REQUIRED },
            { label: 'コンプラ審査待ち', value: MembershipApplicationListStatus.COMPLIANCE_PENDING },
            { label: '承認済み', value: MembershipApplicationListStatus.APPROVED },
            { label: '送付済み', value: MembershipApplicationListStatus.SENT },
          ].map((tab) => (
            <button
              key={tab.value}
              className={
                tab.value === statusFilter
                  ? 'btn-primary'
                  : 'btn-ghost'
              }
              onClick={() => {
                setStatusFilter(tab.value);
                setPage(1);
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>
        {statusFilter === MembershipApplicationListStatus.APPROVED && (
          <div className="flex justify-end mb-3">
            <ExportApprovedCsvButton />
          </div>
        )}
        <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded-lg">
          <table className="w-full">
            <thead>
              <tr className="table-header">
                <th>申込ID</th>
                <th>申込日時</th>
                <th>申込者タイプ</th>
                <th>氏名・法人名</th>
                <th>本人情報 / 法人情報</th>
                <th>本人確認書類</th>
                <th>詳細</th>
              </tr>
            </thead>
            <tbody>
              {membershipApplications?.map((app) => {
                // 申込者タイプに応じて表示名を決定
                const displayName =
                  app.applicantType === ApplicantType.CORPORATE
                    ? `${app.corporateName}（${app.representativeName}）`
                    : `${app.lastName} ${app.firstName}（${app.lastNameKana} ${app.firstNameKana}）`;

                return (
                  <tr key={app.membershipApplicationId} className="table-row">
                    <td className="table-cell">
                      <span className="table-cell-text">{app.membershipApplicationId}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{formatDate(Number(app.appliedAt))}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{app.applicantType === ApplicantType.CORPORATE ? '法人' : '個人'}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{displayName}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">
                        <span className={`badge badge-${getStatusBadgeType(app.applicationReviewStatus, app.isResubmittedAfterRemand)}`}>
                          {mapReviewType(app.applicationReviewStatus, app.isResubmittedAfterRemand)}
                        </span>
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">
                        {(() => {
                          const isDocumentNotSubmitted = (app.identityDocuments?.length ?? 0) === 0;
                          const badgeClass = isDocumentNotSubmitted
                            ? 'badge badge-neutral'
                            : `badge badge-${getStatusBadgeType(app.documentGroupReviewStatus)}`;
                          const label = isDocumentNotSubmitted
                            ? '未提出'
                            : mapReviewType(app.documentGroupReviewStatus);
                          return <span className={badgeClass}>{label}</span>;
                        })()}
                      </span>
                    </td>
                    <td className="table-cell">
                      <Link href={`/membership-application/${app.membershipApplicationId}`} className="btn-ghost text-sm">
                        詳細
                      </Link>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        <div className="mt-6 flex gap-4 items-center justify-center">
          <button onClick={handlePrevPage} disabled={page <= 1 || loading} className="btn-secondary disabled:opacity-50">
            前のページ
          </button>
          <span className="text-secondary">ページ {page}</span>
          <button onClick={handleNextPage} disabled={!hasNextPage || loading} className="btn-primary disabled:opacity-50">
            次のページ
          </button>
        </div>
      </div>
    </main>
  );
}

const mapReviewType = (type: ReviewType, isResubmittedAfterRemand?: boolean): string => {
  // 差し戻し後の再提出の場合
  if (type === ReviewType.NOT_REVIEWED && isResubmittedAfterRemand) {
    return '再提出';
  }

  switch (type) {
    case ReviewType.APPROVE:
      return '承認';
    case ReviewType.REJECT:
      return '拒否';
    case ReviewType.REMAND:
      return '差し戻し';
    case ReviewType.NOT_REVIEWED:
      return '未承認';
    default:
      return '不明';
  }
};

const getStatusBadgeType = (type: ReviewType, isResubmittedAfterRemand?: boolean): string => {
  // 差し戻し後の再提出の場合は警告色
  if (type === ReviewType.NOT_REVIEWED && isResubmittedAfterRemand) {
    return 'warning';
  }

  switch (type) {
    case ReviewType.APPROVE:
      return 'success';
    case ReviewType.REJECT:
      return 'error';
    case ReviewType.REMAND:
      return 'warning';
    case ReviewType.NOT_REVIEWED:
      return 'info';
    default:
      return 'info';
  }
};
