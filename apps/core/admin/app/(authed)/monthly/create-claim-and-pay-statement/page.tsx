'use client';

import { useState } from 'react';
import { generateInvestmentAndReturnPdf } from '@core-admin/api_clients/member_claim_and_pay_client';
import { Breadcrumb } from '../../../../components/common/Breadcrumb';

export default function CreateClaimAndPayStatementPage() {
  const [year, setYear] = useState(new Date().getFullYear());
  const [month, setMonth] = useState(new Date().getMonth() + 1);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const breadcrumbItems = [
    { label: '月締め処理', href: '/monthly' },
    { label: '出資と分配明細書作成', isCurrent: true }
  ];

  const handleGeneratePdf = async () => {
    setIsLoading(true);
    setMessage(null);
    
    try {
      // 日は10日を固定で使用
      const day = 10;
      
      await generateInvestmentAndReturnPdf({
        occurredDateYear: year,
        occurredDateMonth: month,
        occurredDateDay: day,
      });
      
      setMessage({
        type: 'success',
        text: `${year}年${month}月${day}日付の出資と分配明細書の生成が完了しました。`
      });
    } catch (error) {
      console.error('PDF生成エラー:', error);
      setMessage({
        type: 'error',
        text: 'PDF生成に失敗しました: ' + (error instanceof Error ? error.message : '不明なエラーが発生しました')
      });
    } finally {
      setIsLoading(false);
    }
  };

  const validateInput = () => {
    if (year < 1900 || year > 2100) return false;
    if (month < 1 || month > 12) return false;
    return true;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* ヘッダー */}
        <div className="mb-8">
          <Breadcrumb items={breadcrumbItems} />
          <h1 className="text-3xl font-bold text-gray-900 mt-4">出資と分配明細書作成</h1>
          <p className="mt-2 text-gray-600">
            指定した年月の出資と分配明細書PDFを生成します。日付は10日固定で処理されます。
          </p>
        </div>

        {/* メインコンテンツ */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="space-y-6">
            {/* 年月選択 */}
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4">対象年月の指定</h2>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-2">
                    年
                  </label>
                  <select
                    id="year"
                    value={year}
                    onChange={(e) => setYear(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Array.from({ length: 21 }, (_, i) => 2010 + i).map((y) => (
                      <option key={y} value={y}>{y}年</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label htmlFor="month" className="block text-sm font-medium text-gray-700 mb-2">
                    月
                  </label>
                  <select
                    id="month"
                    value={month}
                    onChange={(e) => setMonth(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Array.from({ length: 12 }, (_, i) => i + 1).map((m) => (
                      <option key={m} value={m}>{m}月</option>
                    ))}
                  </select>
                </div>
              </div>
              <p className="mt-2 text-sm text-gray-500">
                日付は10日固定で処理されます
              </p>
            </div>

            {/* 実行ボタン */}
            <div>
              <button
                onClick={handleGeneratePdf}
                disabled={isLoading || !validateInput()}
                className={`w-full px-4 py-3 text-lg font-medium rounded-md transition-colors ${
                  isLoading || !validateInput()
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
                }`}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    処理中...
                  </div>
                ) : (
                  `${year}年${month}月10日付の出資と分配明細書を生成`
                )}
              </button>
            </div>

            {/* メッセージ表示 */}
            {message && (
              <div className={`p-4 rounded-md ${
                message.type === 'success' 
                  ? 'bg-green-50 border border-green-200 text-green-800' 
                  : 'bg-red-50 border border-red-200 text-red-800'
              }`}>
                <div className="flex items-center">
                  {message.type === 'success' ? (
                    <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  )}
                  <span className="font-medium">{message.text}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 補足情報 */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">ご利用上の注意</h3>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p>指定した年月の10日付で出資と分配明細書が生成されます。</p>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p>PDF生成には時間がかかる場合があります。処理中はボタンを押さないでください。</p>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p>生成された明細書は、会員の出資と分配明細書ページでダウンロードできます。</p>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p>ご不明な点がございましたら、システム管理者にお問い合わせください。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
