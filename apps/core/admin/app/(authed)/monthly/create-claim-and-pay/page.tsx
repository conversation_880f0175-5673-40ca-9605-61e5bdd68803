'use client';

import { useState } from 'react';
import { createMemberClaimAndPay } from '@core-admin/api_clients/investment_and_return_client';
import { Breadcrumb } from '../../../../components/common/Breadcrumb';

export default function CreateClaimAndPayPage() {
  const [year, setYear] = useState(new Date().getFullYear());
  const [month, setMonth] = useState(new Date().getMonth() + 1);
  const [isLoading, setIsLoading] = useState(false);

  const breadcrumbItems = [
    { label: '月締め処理', href: '/monthly' },
    { label: '請求・支払いデータ作成', isCurrent: true }
  ];

  const handleCreateMemberClaimAndPay = async () => {
    setIsLoading(true);
    try {
      // 指定された年・月の10日を設定（UTCで正しい日付になるように修正）
      const createdDate = new Date(Date.UTC(year, month - 1, 10));
      
      const request = {
        createdDate: createdDate.toISOString(),
        membershipDues: 3000, // 月会費（固定値）
        taxRate: 10, // 税率10%（固定値）
      };

      await createMemberClaimAndPay(request);
      
      alert('月締め処理が完了しました');
    } catch (error) {
      console.error('月締め処理エラー:', error);
      alert('月締め処理に失敗しました: ' + (error instanceof Error ? error.message : '不明なエラーが発生しました'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Breadcrumb items={breadcrumbItems} />
      
      <div className="mb-8">
        <p className="text-muted-foreground mt-2">
          指定した年・月の請求・支払いデータを作成します
        </p>
      </div>

      <div className="grid gap-6">
        <div className="bg-surface-card border border-stroke-separator rounded-lg p-6">
          <div className="mb-4">
            <h2 className="text-xl font-semibold">請求・支払いデータ作成</h2>
            <p className="text-sm text-muted-foreground">
              指定した年・月の10日を基準として、会員の請求・支払いデータを作成します
            </p>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="year" className="block text-sm font-medium mb-2">
                  年
                </label>
                <input
                  id="year"
                  type="number"
                  value={year}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setYear(parseInt(e.target.value))}
                  min={2015}
                  max={2030}
                  className="w-full px-3 py-2 border border-stroke-border rounded-md focus:outline-none focus:ring-2 focus:ring-stroke-focus"
                />
              </div>
              <div>
                <label htmlFor="month" className="block text-sm font-medium mb-2">
                  月
                </label>
                <select
                  id="month"
                  value={month}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setMonth(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-stroke-border rounded-md focus:outline-none focus:ring-2 focus:ring-stroke-focus"
                >
                  {Array.from({ length: 12 }, (_, i) => i + 1).map((m) => (
                    <option key={m} value={m}>
                      {m}月
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">処理日</label>
              <p className="text-sm text-muted-foreground">
                {year}年{month}月10日
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">設定値</label>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">月会費:</span> 3,000円
                </div>
                <div>
                  <span className="font-medium">税率:</span> 10%
                </div>
              </div>
            </div>

            <button 
              onClick={handleCreateMemberClaimAndPay}
              disabled={isLoading}
              className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '処理中...' : '請求・支払いデータを作成'}
            </button>
          </div>
        </div>

        <div className="bg-surface-card border border-stroke-separator rounded-lg p-6">
          <div className="mb-4">
            <h2 className="text-xl font-semibold">注意事項</h2>
          </div>
          <ul className="list-disc list-inside space-y-2 text-sm text-muted-foreground">
            <li>この処理は指定した年・月の10日を基準として実行されます</li>
            <li>既存のデータがある場合は上書きされる可能性があります</li>
            <li>処理には時間がかかる場合があります</li>
            <li>処理中はページを離れないでください</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
