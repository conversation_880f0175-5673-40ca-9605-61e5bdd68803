'use client';

import Link from 'next/link';

export default function MonthlyPage() {
  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">月締め</h1>
      </div>

      <div className="container mx-auto px-6">
        <div className="grid gap-6">
          <div className="bg-surface-card border border-stroke-separator rounded-lg p-6">
            <div className="mb-4">
              <h2 className="text-xl font-semibold">請求・支払いデータ作成</h2>
              <p className="text-sm text-muted-foreground mb-4">指定した年・月の10日を基準として、会員の請求・支払いデータを作成します</p>
              <Link href="/monthly/create-claim-and-pay" className="inline-block btn-primary">
                請求・支払いデータ作成ページへ
              </Link>
            </div>
          </div>

          <div className="bg-surface-card border border-stroke-separator rounded-lg p-6">
            <div className="mb-4">
              <h2 className="text-xl font-semibold">出資と分配明細書作成</h2>
              <p className="text-sm text-muted-foreground mb-4">
                指定した年・月の10日付で出資と分配明細書PDFを生成します
              </p>
              <Link 
                href="/monthly/create-claim-and-pay-statement"
                className="inline-block btn-primary"
              >
                出資と分配明細書作成ページへ
              </Link>
            </div>
          </div>

          <div className="bg-surface-card border border-stroke-separator rounded-lg p-6">
            <div className="mb-4">
              <h2 className="text-xl font-semibold">月締め処理一覧</h2>
            </div>
            <ul className="list-disc list-inside space-y-2 text-sm text-muted-foreground">
              <li>請求・支払いデータ作成 - 会員の月会費請求と支払いデータを作成</li>
              <li>出資と分配明細書作成 - 指定年月の出資と分配明細書PDFを生成</li>
              <li>その他の月締め処理は今後追加予定</li>
            </ul>
          </div>
        </div>
      </div>
    </main>
  );
}
