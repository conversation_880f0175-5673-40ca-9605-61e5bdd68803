'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';
import { listAllHorseBillings } from '@core-admin/api_clients/horse_billing_client';
import { HorseBillingItem } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { formatCurrency, formatDate } from '@core-admin/utils/income_utils';

export default function BillingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [billings, setBillings] = useState<HorseBillingItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // URLパラメータから初期値を取得
  const getInitialYear = (param: string | null, defaultValue: number) => {
    if (param) {
      const year = parseInt(param, 10);
      return isNaN(year) ? defaultValue : year;
    }
    return defaultValue;
  };

  const getInitialMonth = (param: string | null, defaultValue: number) => {
    if (param) {
      const month = parseInt(param, 10);
      return isNaN(month) || month < 1 || month > 12 ? defaultValue : month;
    }
    return defaultValue;
  };

  const getInitialDay = (param: string | null, defaultValue: number) => {
    if (param) {
      const day = parseInt(param, 10);
      return isNaN(day) || day < 1 || day > 31 ? defaultValue : day;
    }
    return defaultValue;
  };

  // 日付範囲の状態
  const [startYear, setStartYear] = useState(getInitialYear(searchParams.get('startYear'), new Date().getFullYear() - 1));
  const [startMonth, setStartMonth] = useState(getInitialMonth(searchParams.get('startMonth'), 1));
  const [startDay, setStartDay] = useState(getInitialDay(searchParams.get('startDay'), 1));
  const [endYear, setEndYear] = useState(getInitialYear(searchParams.get('endYear'), new Date().getFullYear()));
  const [endMonth, setEndMonth] = useState(getInitialMonth(searchParams.get('endMonth'), 12));
  const [endDay, setEndDay] = useState(getInitialDay(searchParams.get('endDay'), 31));

  const fetchBillings = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await listAllHorseBillings({
        startYear,
        startMonth,
        startDay,
        endYear,
        endMonth,
        endDay,
      });

      setBillings(result.billings);
    } catch (err) {
      console.error('支出データの取得に失敗しました:', err);
      setError('支出データの取得に失敗しました。しばらくしてから再度お試しください。');
    } finally {
      setLoading(false);
    }
  }, [startYear, startMonth, startDay, endYear, endMonth, endDay]);

  // URLパラメータを更新する関数
  const updateUrlParams = useCallback(() => {
    const params = new URLSearchParams();
    params.set('startYear', startYear.toString());
    params.set('startMonth', startMonth.toString());
    params.set('startDay', startDay.toString());
    params.set('endYear', endYear.toString());
    params.set('endMonth', endMonth.toString());
    params.set('endDay', endDay.toString());

    // URLを更新（履歴に追加しない）
    router.replace(`/income-and-billing/billing?${params.toString()}`);
  }, [startYear, startMonth, startDay, endYear, endMonth, endDay, router]);

  // URLパラメータの変更を監視してデータを取得
  useEffect(() => {
    fetchBillings();
  }, [fetchBillings]);

  // 状態変更時にURLパラメータを更新
  useEffect(() => {
    // 初期化時はスキップ
    if (!searchParams.get('startYear') && !searchParams.get('endYear')) {
      return;
    }
    updateUrlParams();
  }, [updateUrlParams, searchParams]);

  // 初期データ取得（初回のみ）
  useEffect(() => {
    if (searchParams.get('startYear') || searchParams.get('endYear')) {
      // URLパラメータがある場合は既に上記のuseEffectで処理される
      return;
    }
    fetchBillings();
  }, [fetchBillings, searchParams]);

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">支出一覧</h1>
      </div>
      <div className="container mx-auto px-6">
        {/* 検索フォーム */}
        <div className="bg-surface-card border border-stroke-separator rounded p-4 mb-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-primary">期間:</span>
              <select value={startYear} onChange={(e) => setStartYear(parseInt(e.target.value))} className="input-base w-24">
                {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - 5 + i).map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">年</span>
              <select value={startMonth} onChange={(e) => setStartMonth(parseInt(e.target.value))} className="input-base w-20">
                {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                  <option key={month} value={month}>
                    {month}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">月</span>
              <select value={startDay} onChange={(e) => setStartDay(parseInt(e.target.value))} className="input-base w-20">
                {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                  <option key={day} value={day}>
                    {day}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">日</span>
              <span className="text-sm text-muted">〜</span>
              <select value={endYear} onChange={(e) => setEndYear(parseInt(e.target.value))} className="input-base w-24">
                {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - 5 + i).map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">年</span>
              <select value={endMonth} onChange={(e) => setEndMonth(parseInt(e.target.value))} className="input-base w-20">
                {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                  <option key={month} value={month}>
                    {month}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">月</span>
              <select value={endDay} onChange={(e) => setEndDay(parseInt(e.target.value))} className="input-base w-20">
                {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                  <option key={day} value={day}>
                    {day}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">日</span>
            </div>
            {loading && <div className="ml-auto text-sm text-muted">検索中...</div>}
          </div>
        </div>

        {/* エラー表示 */}
        {error && (
          <div className="bg-error border border-error px-4 py-3 rounded mb-8">
            <strong className="text-error">エラー:</strong> {error}
          </div>
        )}

        {/* 支出一覧 */}
        <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded">
          {/* ヘッダー */}
          <div className="px-6 py-4 border-b border-stroke-separator">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-primary">支出一覧</h2>
              <div className="text-sm text-muted">{billings.length}件の支出</div>
            </div>
          </div>

          {/* テーブル */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="table-header">
                  <th className="table-header">
                    <span className="table-cell-text">請求年月</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">発生日</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">馬名</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">請求元</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">合計金額</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">操作</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr className="table-row">
                    <td colSpan={6} className="table-cell text-center">
                      <span className="table-cell-text text-muted">読み込み中...</span>
                    </td>
                  </tr>
                ) : billings.length === 0 ? (
                  <tr className="table-row">
                    <td colSpan={6} className="table-cell text-center">
                      <span className="table-cell-text text-muted">支出データが見つかりません</span>
                    </td>
                  </tr>
                ) : (
                  billings.map((billing) => (
                    <tr key={billing.id} className="table-row">
                      <td className="table-cell">
                        <span className="table-cell-text">{billing.billingYearMonth}</span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">
                          {formatDate(billing.occurredYear, billing.occurredMonth, billing.occurredDay)}
                        </span>
                      </td>
                      <td className="table-cell">
                        <Link
                          href={`/horses/${billing.horseId}`}
                          className="table-cell-text font-medium text-primary hover:text-primary-dark underline"
                        >
                          {billing.horseName}
                        </Link>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">{billing.billerName}</span>
                      </td>
                      <td className="table-cell text-right">
                        <span className="table-cell-text font-medium">{formatCurrency(billing.totalAmount)}</span>
                      </td>
                      <td className="table-cell">
                        <div className="flex flex-col space-y-1">
                          <Link href={`/horses/${billing.horseId}/income-and-billing/billing/${billing.id}`} className="btn-ghost text-sm">
                            詳細
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </main>
  );
}
