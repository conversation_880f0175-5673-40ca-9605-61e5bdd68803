'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';
import { listAllHorseIncomes } from '@core-admin/api_clients/horse_income_client';
import { HorseIncome, IncomeType } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { formatCurrency, formatDate, getOtherIncomeNameOptions } from '@core-admin/utils/income_utils';

export default function IncomeAndBillingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [incomes, setIncomes] = useState<HorseIncome[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // URLパラメータから初期値を取得
  const getInitialYear = (param: string | null, defaultValue: number) => {
    if (param) {
      const year = parseInt(param, 10);
      return isNaN(year) ? defaultValue : year;
    }
    return defaultValue;
  };

  const getInitialMonth = (param: string | null, defaultValue: number) => {
    if (param) {
      const month = parseInt(param, 10);
      return isNaN(month) || month < 1 || month > 12 ? defaultValue : month;
    }
    return defaultValue;
  };

  const getInitialDay = (param: string | null, defaultValue: number) => {
    if (param) {
      const day = parseInt(param, 10);
      return isNaN(day) || day < 1 || day > 31 ? defaultValue : day;
    }
    return defaultValue;
  };

  // 日付範囲の状態
  const [startYear, setStartYear] = useState(getInitialYear(searchParams.get('startYear'), new Date().getFullYear() - 1));
  const [startMonth, setStartMonth] = useState(getInitialMonth(searchParams.get('startMonth'), 1));
  const [startDay, setStartDay] = useState(getInitialDay(searchParams.get('startDay'), 1));
  const [endYear, setEndYear] = useState(getInitialYear(searchParams.get('endYear'), new Date().getFullYear()));
  const [endMonth, setEndMonth] = useState(getInitialMonth(searchParams.get('endMonth'), 12));
  const [endDay, setEndDay] = useState(getInitialDay(searchParams.get('endDay'), 31));

  const fetchIncomes = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await listAllHorseIncomes({
        startYear,
        startMonth,
        startDay,
        endYear,
        endMonth,
        endDay,
      });

      setIncomes(result.horseIncomes);
    } catch (err) {
      console.error('収入データの取得に失敗しました:', err);
      setError('収入データの取得に失敗しました。しばらくしてから再度お試しください。');
    } finally {
      setLoading(false);
    }
  }, [startYear, startMonth, startDay, endYear, endMonth, endDay]);

  // URLパラメータを更新する関数
  const updateUrlParams = useCallback(() => {
    const params = new URLSearchParams();
    params.set('startYear', startYear.toString());
    params.set('startMonth', startMonth.toString());
    params.set('startDay', startDay.toString());
    params.set('endYear', endYear.toString());
    params.set('endMonth', endMonth.toString());
    params.set('endDay', endDay.toString());

    // URLを更新（履歴に追加しない）
    router.replace(`/income-and-billing/income?${params.toString()}`);
  }, [startYear, startMonth, startDay, endYear, endMonth, endDay, router]);

  // URLパラメータの変更を監視してデータを取得
  useEffect(() => {
    fetchIncomes();
  }, [fetchIncomes]);

  // 状態変更時にURLパラメータを更新
  useEffect(() => {
    // 初期化時はスキップ
    if (!searchParams.get('startYear') && !searchParams.get('endYear')) {
      return;
    }
    updateUrlParams();
  }, [updateUrlParams, searchParams]);

  // 初期データ取得（初回のみ）
  useEffect(() => {
    if (searchParams.get('startYear') || searchParams.get('endYear')) {
      // URLパラメータがある場合は既に上記のuseEffectで処理される
      return;
    }
    fetchIncomes();
  }, [fetchIncomes, searchParams]);

  const getIncomeTypeLabel = (type: IncomeType) => {
    switch (type) {
      case IncomeType.PRIZE:
        return '賞金収入';
      case IncomeType.OTHER:
        return 'その他収入';
      default:
        return '不明';
    }
  };

  const getIncomeTypeBadgeClass = (type: IncomeType) => {
    switch (type) {
      case IncomeType.PRIZE:
        return 'bg-primary text-white';
      case IncomeType.OTHER:
        return 'bg-secondary text-white';
      default:
        return 'bg-muted text-primary';
    }
  };

  // その他収入名目の英語を日本語に変換する関数
  const getOtherIncomeNameLabel = (name: string) => {
    const options = getOtherIncomeNameOptions();
    const option = options.find((opt) => opt.value === name);
    return option ? option.label : name;
  };

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">収入一覧</h1>
      </div>
      <div className="container mx-auto px-6">
        {/* 検索フォーム */}
        <div className="bg-surface-card border border-stroke-separator rounded p-4 mb-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-primary">期間:</span>
              <select value={startYear} onChange={(e) => setStartYear(parseInt(e.target.value))} className="input-base w-24">
                {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - 5 + i).map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">年</span>
              <select value={startMonth} onChange={(e) => setStartMonth(parseInt(e.target.value))} className="input-base w-20">
                {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                  <option key={month} value={month}>
                    {month}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">月</span>
              <select value={startDay} onChange={(e) => setStartDay(parseInt(e.target.value))} className="input-base w-20">
                {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                  <option key={day} value={day}>
                    {day}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">日</span>
              <span className="text-sm text-muted">〜</span>
              <select value={endYear} onChange={(e) => setEndYear(parseInt(e.target.value))} className="input-base w-24">
                {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - 5 + i).map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">年</span>
              <select value={endMonth} onChange={(e) => setEndMonth(parseInt(e.target.value))} className="input-base w-20">
                {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                  <option key={month} value={month}>
                    {month}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">月</span>
              <select value={endDay} onChange={(e) => setEndDay(parseInt(e.target.value))} className="input-base w-20">
                {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                  <option key={day} value={day}>
                    {day}
                  </option>
                ))}
              </select>
              <span className="text-sm text-muted">日</span>
            </div>
            {loading && <div className="ml-auto text-sm text-muted">検索中...</div>}
          </div>
        </div>

        {/* エラー表示 */}
        {error && (
          <div className="bg-error border border-error px-4 py-3 rounded mb-8">
            <strong className="text-error">エラー:</strong> {error}
          </div>
        )}

        {/* 収入一覧 */}
        <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded">
          {/* ヘッダー */}
          <div className="px-6 py-4 border-b border-stroke-separator">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-primary">収入一覧</h2>
              <div className="text-sm text-muted">{incomes.length}件の収入</div>
            </div>
          </div>

          {/* テーブル */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="table-header">
                  <th className="table-header">
                    <span className="table-cell-text">開催日</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">馬名</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">種別</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">名称</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">詳細</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">金額</span>
                  </th>
                  <th className="table-header">
                    <span className="table-cell-text">操作</span>
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr className="table-row">
                    <td colSpan={7} className="table-cell text-center">
                      <span className="table-cell-text text-muted">読み込み中...</span>
                    </td>
                  </tr>
                ) : incomes.length === 0 ? (
                  <tr className="table-row">
                    <td colSpan={7} className="table-cell text-center">
                      <span className="table-cell-text text-muted">収入データが見つかりません</span>
                    </td>
                  </tr>
                ) : (
                  incomes.map((income) => (
                    <tr key={income.id} className="table-row">
                      <td className="table-cell">
                        <span className="table-cell-text">{formatDate(income.occurredYear, income.occurredMonth, income.occurredDay)}</span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text font-medium">{income.horseName}</span>
                      </td>
                      <td className="table-cell">
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${getIncomeTypeBadgeClass(income.incomeType)}`}
                        >
                          {getIncomeTypeLabel(income.incomeType)}
                        </span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text">
                          {income.incomeType === IncomeType.OTHER ? getOtherIncomeNameLabel(income.name) : income.name}
                        </span>
                      </td>
                      <td className="table-cell">
                        <span className="table-cell-text text-muted">{income.name2}</span>
                      </td>
                      <td className="table-cell text-right">
                        <span className="table-cell-text font-medium">{formatCurrency(income.amount)}</span>
                      </td>
                      <td className="table-cell">
                        <div className="flex flex-col space-y-1">
                          <Link
                            href={`/horses/${income.horseId}/income-and-billing/income/${income.incomeType === IncomeType.PRIZE ? 'prize' : 'other'}/${income.id}`}
                            className="btn-ghost text-sm"
                          >
                            詳細
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </main>
  );
}
