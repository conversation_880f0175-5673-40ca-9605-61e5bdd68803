import { listAnnualBundleInvestmentApplications, listInvestmentApplicationHorses } from '@core-admin/api_clients/investment_application_client';
import React from 'react';

import { Link } from '@core-admin/components/common/Link';

export default async function InvestmentApplicationHorsesPage() {
  // APIからデータ取得
  const [{ applications: annualBundleApplications }, { horses }] = await Promise.all([
    listAnnualBundleInvestmentApplications({}),
    listInvestmentApplicationHorses({}),
  ]);

  // 年度バンドル申込をバンドル単位で集計
  type AggregatedBundle = {
    annualBundleId: number;
    fiscalYear: number;
    bundleName: string;
    totalApplicationShares: number;
  };

  const bundlesMap = new Map<number, AggregatedBundle>();
  for (const app of annualBundleApplications ?? []) {
    const detail = app.annualBundleDetail;
    if (!detail) continue;
    const id = detail.annualBundleId;
    const prev = bundlesMap.get(id);
    const requested = app.requestedShares ?? 0;
    if (prev) {
      prev.totalApplicationShares += requested;
    } else {
      bundlesMap.set(id, {
        annualBundleId: id,
        fiscalYear: detail.fiscalYear,
        bundleName: detail.bundleName,
        totalApplicationShares: requested,
      });
    }
  }

  // 馬と年度バンドルを統合した一覧データを作成
  const items = [
    // 馬
    ...horses.map((horse) => ({
      key: `horse-${horse.horseId}`,
      type: 'horse' as const,
      recruitmentYear: horse.recruitmentYear,
      recruitmentNo: horse.recruitmentNo,
      name: horse.recruitmentName,
      totalApplicationShares: horse.totalApplicationShares,
      statusText: `${horse.contractedShares}/${horse.recruitmentShares}`,
      link: `/investment-application/${horse.horseId}`,
    })),
    // 年度バンドル
    ...Array.from(bundlesMap.values()).map((b) => ({
      key: `bundle-${b.annualBundleId}`,
      type: 'bundle' as const,
      recruitmentYear: b.fiscalYear,
      recruitmentNo: null as number | null,
      name: b.bundleName,
      totalApplicationShares: b.totalApplicationShares,
      statusText: '-',
      link: `/investment-application/annual-bundle/${b.annualBundleId}`,
    })),
  ];

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">出資申込の管理</h1>
      </div>

      <div className="container mx-auto px-6">
        <div className="bg-surface-card border border-stroke-separator rounded-lg overflow-hidden shadow-sm">
          <table className="w-full">
            <thead>
              <tr className="table-header">
                <th>募集年</th>
                <th>募集番号</th>
                <th>馬名 / パッケージ名</th>
                <th>申込口数</th>
                <th>出資状況</th>
              </tr>
            </thead>
            <tbody>
              {items.length === 0 ? (
                <tr className="table-row">
                  <td colSpan={5} className="table-cell">
                    <span className="table-cell-text text-center block py-8 text-muted">申込のある馬がありません</span>
                  </td>
                </tr>
              ) : (
                items.map((item) => (
                  <tr key={item.key} className="table-row">
                    <td className="table-cell">
                      <span className="table-cell-text">{item.recruitmentYear}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{item.recruitmentNo === null ? '-' : item.recruitmentNo.toString().padStart(3, '0')}</span>
                    </td>
                    <td className="table-cell">
                      <Link href={item.link} className="text-primary hover:underline">
                        {item.name}
                      </Link>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{item.totalApplicationShares}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{item.statusText}</span>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </main>
  );
}
