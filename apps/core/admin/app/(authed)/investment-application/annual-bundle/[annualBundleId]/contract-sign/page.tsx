"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { getAnnualBundle } from "@core-admin/api_clients/annual_bundle_client";
import type { ContractItem } from '@hami/core-admin-api-schema/investment_application_service_pb';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { Link } from '@core-admin/components/common/Link';

export default function AnnualBundleContractSignPage() {
  const { annualBundleId } = useParams();
  const [bundleName, setBundleName] = useState("");
  const [contracts, setContracts] = useState<ContractItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const bundleRes = await getAnnualBundle({ annualBundleId: Number(annualBundleId) });
        setBundleName(bundleRes.bundle?.name || "");
        
        // localStorageから契約データ取得
        const contractsStr = typeof window !== 'undefined' ? localStorage.getItem(`contracts_annual_bundle_${annualBundleId}`) : null;
        const contracts = contractsStr ? JSON.parse(contractsStr) : [];
        setContracts(contracts);
      } catch (_e) {
        setError('データの取得に失敗しました');
      } finally {
        setLoading(false);
      }
    })();
  }, [annualBundleId]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-error text-center mt-8">{error}</div>;

  const breadcrumbItems = [
    { label: '出資申込管理', href: '/investment-application' },
    { label: `${bundleName} 出資状況`, href: `/investment-application/annual-bundle/${annualBundleId}` },
    { label: '契約締結', isCurrent: true }
  ];

  return (
    <main className="p-6 bg-white">
      <Breadcrumb items={breadcrumbItems} />
      
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-primary mb-4">
          {bundleName} 出資契約締結完了
        </h1>
        <p className="text-primary">
          出資契約の締結が完了しました。
        </p>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm mb-8 max-w-3xl mx-auto">
        <table className="w-full">
          <thead>
            <tr className="table-header">
              <th className="text-center w-1/2">契約者</th>
              <th className="text-center w-1/2">口数</th>
            </tr>
          </thead>
          <tbody>
            {contracts.length === 0 ? (
              <tr className="table-row">
                <td colSpan={2} className="table-cell text-center text-muted py-8">
                  契約データがありません
                </td>
              </tr>
            ) : (
              contracts.map((contract, index) => {
                return (
                  <tr key={index} className="table-row">
                    <td className="table-cell text-center">
                      <span className="table-cell-text">{contract.memberName}</span>
                    </td>
                    <td className="table-cell text-center">
                      <span className="table-cell-text">{contract.contractedShares}</span>
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      <div className="flex justify-center">
        <Link
          href="/investment-application"
          className="btn-primary text-lg px-8 py-3"
        >
          出資申込管理に戻る
        </Link>
      </div>
    </main>
  );
}
