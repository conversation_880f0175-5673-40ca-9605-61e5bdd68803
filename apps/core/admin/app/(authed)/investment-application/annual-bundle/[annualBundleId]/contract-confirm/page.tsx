"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { listAnnualBundleInvestmentApplications } from "@core-admin/api_clients/investment_application_client";
import { getAnnualBundle } from "@core-admin/api_clients/annual_bundle_client";
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';

type ContractTargetApplicationItem = {
  investmentApplicationId: number;
  memberName: string;
  allocatedNumber: number;
};

export default function AnnualBundleContractConfirmPage() {
  const router = useRouter();
  const { annualBundleId } = useParams();
  const [bundleName, setBundleName] = useState("");
  const [applications, setApplications] = useState<ContractTargetApplicationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const bundleRes = await getAnnualBundle({ annualBundleId: Number(annualBundleId) });
        setBundleName(bundleRes.bundle?.name || "");
        
        const res = await listAnnualBundleInvestmentApplications({ 
          annualBundleIds: [Number(annualBundleId)] 
        });
        
        // 受入口数が0より大きい申し込みのみをフィルタリング
        const contractTargetApplications = (res.applications || [])
          .filter(app => (app.allocatedShares ?? 0) > 0)
          .map(app => ({
            investmentApplicationId: app.investmentApplicationId!,
            memberName: app.member?.memberName || "",
            allocatedNumber: app.allocatedShares ?? 0,
          }));
        
        setApplications(contractTargetApplications);
      } catch (_e) {
        setError('データの取得に失敗しました');
      } finally {
        setLoading(false);
      }
    })();
  }, [annualBundleId]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-error text-center mt-8">{error}</div>;

  const investmentApplicationIds = applications.map(app => app.investmentApplicationId);

  const breadcrumbItems = [
    { label: '出資申込管理', href: '/investment-application' },
    { label: `${bundleName} 出資状況`, href: `/investment-application/annual-bundle/${annualBundleId}` },
    { label: '契約締結確認', isCurrent: true }
  ];

  return (
    <main className="p-6 bg-white">
      <Breadcrumb items={breadcrumbItems} />
      
      <p className="text-primary text-center mb-4">
          {bundleName}について、下記の内容で出資契約を結びます。<br />
          よろしいですか？
        </p>

      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm mb-8 max-w-3xl mx-auto">
        <table className="w-full">
          <thead>
            <tr className="table-header">
              <th className="text-center w-1/2">申込者</th>
              <th className="text-center w-1/2">口数</th>
            </tr>
          </thead>
          <tbody>
            {applications.map((app) => (
              <tr key={app.investmentApplicationId} className="table-row">
                <td className="table-cell text-center">
                  <span className="table-cell-text">{app.memberName}</span>
                </td>
                <td className="table-cell text-center">
                  <span className="table-cell-text">{app.allocatedNumber}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-center gap-6">
        <button
          className="btn-secondary text-lg px-8 py-3"
          onClick={() => router.back()}
        >
          戻る
        </button>
        <button
          className="btn-primary text-lg px-8 py-3"
          onClick={async () => {
            const annualBundleIdNum = Number(annualBundleId);
            
            if (!annualBundleIdNum || annualBundleIdNum < 0 || investmentApplicationIds.length === 0) {
              alert('年度バンドルIDまたは申込IDが不正です');
              return;
            }
            
            try {
              const res = await fetch(`/investment-application/annual-bundle/${annualBundleId}/contract-sign/api`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ annualBundleId: annualBundleIdNum, investmentApplicationIds }),
              });
              
              const data = await res.json();
              
              if (!res.ok) {
                console.error('API error:', data);
                alert(`エラーが発生しました: ${data.error || 'Unknown error'}`);
                return;
              }
              
              // エラーメッセージがある場合は表示
              if (data.errors && data.errors.length > 0) {
                alert(`契約締結エラー: ${data.errors.join(', ')}`);
                return;
              }
              
              if (typeof window !== 'undefined') {
                localStorage.setItem(`contracts_annual_bundle_${annualBundleId}`, JSON.stringify(data.contracts));
              }
              router.push(`/investment-application/annual-bundle/${annualBundleId}/contract-sign`);
            } catch  {
              alert('契約締結中にエラーが発生しました');
            }
          }}
        >
          契約を結ぶ
        </button>
      </div>
    </main>
  );
}
