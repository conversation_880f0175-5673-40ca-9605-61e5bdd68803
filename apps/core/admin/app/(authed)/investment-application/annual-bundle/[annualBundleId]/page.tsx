"use client";

import React, { useEffect, useMemo, useState } from 'react';
import { useParams } from 'next/navigation';
import { Link } from '@core-admin/components/common/Link';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import {
  listAnnualBundleInvestmentApplications,
  acceptAnnualBundleInvestmentApplications,
  getInvestmentApplicationAnnualBundleDetail,
} from '@core-admin/api_clients/investment_application_client';
import type { InvestmentApplicationSummary, GetInvestmentApplicationAnnualBundleDetailResponse } from '@hami/core-admin-api-schema/investment_application_service_pb';
import { InvestmentApplicationStatus } from '@hami/core-admin-api-schema/investment_application_service_pb';

export default function AnnualBundleInvestmentApplicationsPage() {
  const params = useParams();
  const idStr = Array.isArray(params.annualBundleId) ? params.annualBundleId[0] : params.annualBundleId;
  const annualBundleId = idStr ? Number(idStr) : undefined;

  const [applications, setApplications] = useState<InvestmentApplicationSummary[]>([]);
  const [bundleDetail, setBundleDetail] = useState<GetInvestmentApplicationAnnualBundleDetailResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialog, setDialog] = useState<{ open: boolean; app?: InvestmentApplicationSummary | InvestmentApplicationSummary[]; action?: 'accept' | 'reject'; allocated?: number }>({ open: false });
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [allocations, setAllocations] = useState<Record<number, number>>({});
  const [exclusions, setExclusions] = useState<Record<number, boolean>>({});

  const bundleName = useMemo(() => bundleDetail?.bundleName ?? '', [bundleDetail]);
  const fiscalYear = useMemo(() => bundleDetail?.fiscalYear ?? undefined, [bundleDetail]);

  // APIから取得した詳細情報を使用
  const contractedShares = useMemo(() => bundleDetail?.contractedShares ?? 0, [bundleDetail]);
  const recruitmentShares = useMemo(() => bundleDetail?.recruitmentShares ?? 0, [bundleDetail]);
  const recruitmentTotalAmount = useMemo(() => bundleDetail?.recruitmentTotalAmount ?? 0, [bundleDetail]);

  // 表示用の申込リスト（契約済みを除外）
  const displayApplications = useMemo(() => {
    return applications.filter(app => 
      app.status !== InvestmentApplicationStatus.CONTRACT_COMPLETED
    );
  }, [applications]);

  const applyApplicationAllocatedShares = useMemo(() => {
    return applications.reduce((sum, app) => sum + (app.allocatedShares ?? 0), 0);
  }, [applications]);

  const fetchList = async () => {
    if (annualBundleId === undefined) return;
    setLoading(true);
    setError(null);
    try {
      const [applicationsRes, detailRes] = await Promise.all([
        listAnnualBundleInvestmentApplications({ annualBundleIds: [annualBundleId] }),
        getInvestmentApplicationAnnualBundleDetail({ annualBundleId }),
      ]);
      
      // すべての申込を取得（契約済みも含む）
      const allApplications = applicationsRes.applications || [];
      setApplications(allApplications);
      setBundleDetail(detailRes);
    } catch (_e) {
      setError('データの取得に失敗しました');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [annualBundleId]);

  const openAccept = (app: InvestmentApplicationSummary) => {
    const defaultAlloc = Math.max(0, Math.min(app.requestedShares ?? 0, Number(app.allocatedShares ?? app.requestedShares ?? 0)));
    const initial = app.rejectPartialAllocation ? (app.requestedShares ?? 0) : defaultAlloc;
    setAllocations({ [app.investmentApplicationId]: initial });
    setExclusions({});
    setDialog({ open: true, app, action: 'accept', allocated: initial });
  };

  const openReject = (app: InvestmentApplicationSummary) => {
    setDialog({ open: true, app, action: 'reject' });
  };

  const handleBatch = (action: 'accept' | 'reject') => {
    const selectedApps = displayApplications.filter(app => selectedIds.includes(app.investmentApplicationId));
    if (action === 'accept') {
      // 既存のロジックを初期値として、残口数の範囲で自動割当（あとでモーダルで編集可能）
      let remainingShares = 1000; // 仮の残口数（実際のAPIから取得する必要がある）
      const initial: Record<number, number> = {};
      const initialEx: Record<number, boolean> = {};
      selectedApps.forEach((app) => {
        if (app.rejectPartialAllocation && remainingShares < (app.requestedShares ?? 0)) {
          initial[app.investmentApplicationId] = 0; // 初期値は受入不可（残口不足）
          initialEx[app.investmentApplicationId] = true; // 非許容はデフォルト除外
          return;
        }
        const allocated = Math.max(0, Math.min(app.requestedShares ?? 0, remainingShares));
        initial[app.investmentApplicationId] = allocated;
        remainingShares -= allocated;
        initialEx[app.investmentApplicationId] = app.rejectPartialAllocation === true;
      });
      setAllocations(initial);
      setExclusions(initialEx);
    }
    setDialog({ open: true, app: selectedApps, action });
  };

  const confirmDialog = async () => {
    if (!dialog.open || !dialog.app || !dialog.action) return;
    setLoading(true);
    
    if (Array.isArray(dialog.app)) {
      // まとめて処理
      if (dialog.action === 'accept') {
        const apps = dialog.app as InvestmentApplicationSummary[];
        const included = apps.filter((a) => exclusions[a.investmentApplicationId] !== true);
        const acceptances = included
          .map((app: InvestmentApplicationSummary) => {
            const base = allocations[app.investmentApplicationId] ?? 0;
            const clamped = Math.max(0, Math.min(app.requestedShares ?? 0, Math.floor(base)));
            const chosen = app.rejectPartialAllocation ? (app.requestedShares ?? 0) : clamped;
            return {
              investmentApplicationId: app.investmentApplicationId,
              annualBundleId: app.annualBundleDetail?.annualBundleId,
              allocatedShares: chosen,
            };
          })
          .filter(item => item.allocatedShares > 0 && item.annualBundleId);
        
        if (acceptances.length > 0) {
          await acceptAnnualBundleInvestmentApplications({
            commands: acceptances,
          });
        }
      } else {
        const rejections = dialog.app.map((app: InvestmentApplicationSummary) => ({
          investmentApplicationId: app.investmentApplicationId,
          annualBundleId: app.annualBundleDetail?.annualBundleId,
          allocatedShares: 0,
        })).filter(item => item.annualBundleId);
        
        if (rejections.length > 0) {
          await acceptAnnualBundleInvestmentApplications({
            commands: rejections,
          });
        }
      }
    } else {
      // 単体処理
      const app = dialog.app as InvestmentApplicationSummary;
    const annualBundleIdVal = app.annualBundleDetail?.annualBundleId;
    const applicationIdVal = app.investmentApplicationId;
      
    if (!annualBundleIdVal || !applicationIdVal) {
      setDialog({ open: false });
        setLoading(false);
      return;
    }
      
    const chosen = dialog.action === 'reject' ? 0 : Math.max(0, Math.min(app.requestedShares ?? 0, Math.floor(dialog.allocated ?? 0)));
    // rejectPartialAllocation の場合は requestedShares か 0
    const allocatedShares = app.rejectPartialAllocation ? ((chosen >= (app.requestedShares ?? 0)) ? (app.requestedShares ?? 0) : 0) : chosen;
      
    try {
      await acceptAnnualBundleInvestmentApplications({
        commands: [{ investmentApplicationId: applicationIdVal, annualBundleId: annualBundleIdVal, allocatedShares }],
      });
      } catch (_) {
        // noop
      }
    }
    
    try {
      await fetchList();
    } catch (_) {
      // noop
    } finally {
      setLoading(false);
      setDialog({ open: false });
      setSelectedIds([]);
    }
  };


  const totalApplicants = displayApplications.length;
  const totalRequestedShares = displayApplications.reduce((sum, a) => sum + (a.requestedShares ?? 0), 0);
  const rejectedApplicants = displayApplications.filter((a) => (a.allocatedShares ?? undefined) === 0).length;
  const rejectedRequestedShares = displayApplications
    .filter((a) => (a.allocatedShares ?? undefined) === 0)
    .reduce((sum, a) => sum + (a.requestedShares ?? 0), 0);

  const formatTimestamp = (ts: unknown): string => {
    if (!ts) return '-';
    // 文字列/数値/Date で来た場合
    if (typeof ts === 'string' || typeof ts === 'number' || ts instanceof Date) {
      const d = new Date(ts);
      return isNaN(d.getTime()) ? '-' : d.toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' });
    }
    // { seconds, nanos } の形で来た場合（Buf Timestampのシリアライズ形）
    if (typeof ts === 'object' && ts !== null && 'seconds' in ts && 'nanos' in ts) {
      const seconds = ts.seconds;
      const nanos = ts.nanos ?? 0;
      const secNum = typeof seconds === 'bigint' ? Number(seconds) : Number(seconds);
      if (!isFinite(secNum)) return '-';
      const ms = secNum * 1000 + Math.floor(Number(nanos) / 1_000_000);
      const d = new Date(ms);
      return isNaN(d.getTime()) ? '-' : d.toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' });
    }
    return '-';
  };

  const breadcrumbItems = [
    { label: '出資申込管理', href: '/investment-application' },
    { label: bundleName ? `${bundleName} 出資状況` : '年度バンドル 出資状況', isCurrent: true },
  ];

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-error">{error}</div>;

  return (
    <main className="p-6 bg-white">
      <Breadcrumb items={breadcrumbItems} />
      
      <div className="mb-6">
        <table className="w-full">
          <tbody>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">年度</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">{fiscalYear ?? '-'}</span>
              </td>
            </tr>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">パッケージ名</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">{bundleName || '-'}</span>
              </td>
            </tr>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">出資口数/総口数(残口数)</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">
                  {bundleDetail ? `${contractedShares}/${recruitmentShares}（${recruitmentShares - contractedShares}）` : '-'}
                </span>
              </td>
            </tr>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">募集総額(万円)</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">
                  {bundleDetail ? Math.floor(recruitmentTotalAmount / 10000) : '-'}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6 shadow-sm">
        <div className="flex justify-center gap-8">
          <span className="text-primary font-bold">
            残口数：<span className="text-error">{bundleDetail ? recruitmentShares - contractedShares : '-'}</span>
          </span>
          <span className="text-primary font-bold">
            出資申込受入口数：<span className="text-error">{applyApplicationAllocatedShares}</span>
          </span>
            </div>
          </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6 shadow-sm">
        <div className="flex justify-center gap-8">
          <span className="text-primary font-bold">
            申込件数：<span className="text-focus">{totalApplicants}</span>
            <span className="text-secondary">（却下 {rejectedApplicants}）</span>
          </span>
          <span className="text-primary font-bold">
            申込口数合計：<span className="text-focus">{totalRequestedShares}</span>
            <span className="text-secondary">（却下 {rejectedRequestedShares}）</span>
          </span>
        </div>
          </div>

      <div className="mb-6 flex gap-4 items-center">
        <button
          className="btn-primary"
          disabled={selectedIds.length === 0 || loading}
          onClick={() => handleBatch('accept')}
        >
          まとめて受入
        </button>
        <button
          className="btn-secondary"
          disabled={selectedIds.length === 0 || loading}
          onClick={() => handleBatch('reject')}
        >
          まとめて却下
        </button>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
          <table className="w-full">
            <thead>
              <tr className="table-header">
              <th className="text-center">
                <input
                  type="checkbox"
                  checked={selectedIds.length > 0 && selectedIds.length === displayApplications.length}
                  onChange={e => {
                    if (e.target.checked) setSelectedIds(displayApplications.map(app => app.investmentApplicationId));
                    else setSelectedIds([]);
                  }}
                />
              </th>
                <th className="text-center">申込日時</th>
                <th className="text-center">申込者</th>
                <th className="text-center">会員番号</th>
                <th className="text-center">申込口数</th>
                <th className="text-center">希望口数未満</th>
                <th className="text-center">出資受入口数</th>
                <th className="text-center">操作</th>
              </tr>
            </thead>
            <tbody>
              {displayApplications.length === 0 ? (
                <tr className="table-row">
                <td colSpan={8} className="table-cell text-center text-muted py-8">申込がありません</td>
                </tr>
              ) : (
              displayApplications.map((app) => {
                const isRejected = (app.allocatedShares ?? undefined) === 0;
                return (
                  <tr key={app.investmentApplicationId} className={isRejected ? 'bg-gray-50 text-gray-500' : 'table-row text-primary'}>
                    <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                      <input
                        type="checkbox"
                        disabled={isRejected}
                        checked={selectedIds.includes(app.investmentApplicationId)}
                        onChange={e => {
                          if (e.target.checked) setSelectedIds([...selectedIds, app.investmentApplicationId]);
                          else setSelectedIds(selectedIds.filter(id => id !== app.investmentApplicationId));
                        }}
                      />
                    </td>
                    <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejected ? 'text-gray-500' : 'text-primary'}`}>
                      <span className="text-sm">{formatTimestamp(app.applicationDate)}</span>
                    </td>
                    <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejected ? 'text-gray-500' : 'text-primary'}`}>
                      <Link href={`/members/${app.member?.memberId}`} className="text-sm hover:text-focus underline hover:no-underline transition-colors">
                        {app.member?.memberName || '-'}
                      </Link>
                    </td>
                    <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejected ? 'text-gray-500' : 'text-primary'}`}>
                      <span className="text-sm">{app.member?.memberNumber ?? '-'}</span>
                    </td>
                    <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejected ? 'text-gray-500' : 'text-primary'}`}>
                      <span className="text-sm">{app.requestedShares ?? 0}</span>
                    </td>
                    <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejected ? 'text-gray-500' : 'text-primary'}`}>
                      <span className="text-sm">{app.rejectPartialAllocation ? '×' : '◯'}</span>
                    </td>
                    <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejected ? 'text-gray-500' : 'text-primary'}`}>
                      <span className="text-sm">{app.allocatedShares ?? '-'}</span>
                    </td>
                    <td className="px-3 py-2 text-center border-b border-gray-200">
                      {isRejected ? (
                        <span className="text-sm">却下済</span>
                      ) : (
                      <div className="flex justify-center gap-2">
                        <button
                          onClick={() => openAccept(app)}
                            className="btn-primary text-sm"
                        >受入</button>
                        <button
                          onClick={() => openReject(app)}
                            className="btn-secondary text-sm"
                        >却下</button>
                      </div>
                      )}
                    </td>
                  </tr>
                );
              })
              )}
            </tbody>
          </table>
        </div>

        {/* 契約締結確認への導線（受入口数>0 のみ） */}
        {displayApplications.some((a) => (a.allocatedShares ?? 0) > 0) && (
          <div className="mt-8 flex justify-center">
            <Link
              href={`/investment-application/annual-bundle/${annualBundleId}/contract-confirm`}
              className="btn-primary text-lg px-8 py-4"
            >
              上記内容での契約締結を確認する
            </Link>
          </div>
        )}

        {/* 確認モーダル */}
      {dialog.open && (
        <div className="fixed inset-0 flex items-center justify-center bg-muted bg-opacity-60 backdrop-blur-sm z-50">
          <div className="bg-white border border-stroke-separator rounded-lg p-6 shadow-lg w-full max-w-4xl mx-4">
            <div className="mb-6">
              {Array.isArray(dialog.app) ? (
                <>
                  <div className="mb-4 text-primary font-medium">
                    {dialog.action === 'accept'
                      ? `まとめて受入（${dialog.app.length}件）`
                      : `まとめて却下します（${dialog.app.length}件）`}
                  </div>
                  {dialog.action === 'accept' ? (
                    <>
                      <div className="mb-4 text-secondary">
                        選択合計: <span className="text-focus">{dialog.app.reduce((sum, app) => sum + (allocations[app.investmentApplicationId] ?? 0), 0)}</span>
                      </div>
                      <div className="max-h-80 overflow-auto border border-gray-200 rounded">
                        <table className="w-full">
                          <thead>
                            <tr className="table-header">
                              <th className="text-left px-3">会員</th>
                              <th className="text-center">申込口数</th>
                              <th className="text-center">希望口数未満</th>
                              <th className="text-center">受入口数</th>
                              <th className="text-center">除外</th>
                            </tr>
                          </thead>
                          <tbody>
                            {(dialog.app as InvestmentApplicationSummary[]).map((app: InvestmentApplicationSummary) => {
                              const current = allocations[app.investmentApplicationId] ?? 0;
                              const max = app.requestedShares ?? 0;
                              const isNoPartial = app.rejectPartialAllocation === true;
                              const excluded = exclusions[app.investmentApplicationId] === true;
                              return (
                                <tr key={app.investmentApplicationId} className={`table-row ${excluded ? 'bg-gray-50 text-gray-400' : 'text-primary'}`}>
                                  <td className="px-3 py-2 border-b border-r border-gray-200">
                                    <span className="text-sm font-medium">{app.member?.memberName ?? '-'}</span>
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                                    <span className="text-sm">{app.requestedShares ?? 0}</span>
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                                    <span className="text-sm">{isNoPartial ? '×' : '◯'}</span>
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                                    {(excluded) ? (
                                      <div className="flex items-center justify-center gap-2">
                                        <input
                                          type="number"
                                          className="input-base w-24"
                                          value={0}
                                          min={0}
                                          max={max}
                                          readOnly
                                        />
                                      </div>
                                    ) : isNoPartial ? (
                                      <div className="flex items-center justify-center gap-2">
                                        <input
                                          type="number"
                                          className="input-base w-24"
                                          value={app.requestedShares ?? 0}
                                          min={app.requestedShares ?? 0}
                                          max={app.requestedShares ?? 0}
                                          readOnly
                                        />
                                      </div>
                                    ) : (
                                      <input
                                        type="number"
                                        className="input-base w-24"
                                        value={String(current).replace(/^0+(\d)/, '$1')}
                                        min={0}
                                        max={max}
                                        onChange={(e) => {
                                          const normalized = e.target.value === '' ? 0 : Number(e.target.value);
                                          const clamped = Math.max(0, Math.min(max, isNaN(normalized) ? 0 : Math.floor(normalized)));
                                          setAllocations(prev => ({ ...prev, [app.investmentApplicationId]: clamped }));
                                        }}
                                      />
                                    )}
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-gray-200">
                                    <input
                                      type="checkbox"
                                      checked={excluded}
                                      onChange={(e) => {
                                        const next = e.target.checked;
                                        setExclusions(prev => ({ ...prev, [app.investmentApplicationId]: next }));
                                      }}
                                      disabled={isNoPartial}
                                    />
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </>
                  ) : (
                    <ul className="mb-4 text-secondary list-disc list-inside space-y-2">
                      {dialog.app.map((app: InvestmentApplicationSummary) => (
                        <li key={app.investmentApplicationId}>
                          <span className="font-medium text-primary">{app.member?.memberName ?? '-'}</span> 様：{app.requestedShares ?? 0}口
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              ) : dialog.app && (
                <>
              {dialog.action === 'accept' ? (
                    <div className="text-primary space-y-4">
                      <div className="mb-4 text-secondary">
                        選択合計: <span className="text-focus">{dialog.allocated ?? 0}</span>
                      </div>
                      <div className="max-h-80 overflow-auto border border-gray-200 rounded">
                  <table className="w-full">
                    <thead>
                      <tr className="table-header">
                              <th className="text-left px-3">会員</th>
                              <th className="text-center">申込口数</th>
                              <th className="text-center">希望口数未満</th>
                              <th className="text-center">受入口数</th>
                      </tr>
                    </thead>
                    <tbody>
                            <tr className="table-row text-primary">
                              <td className="px-3 py-2 border-b border-r border-gray-200">
                          <span className="text-sm font-medium">{dialog.app.member?.memberName ?? '-'}</span>
                        </td>
                              <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                          <span className="text-sm">{dialog.app.requestedShares ?? 0}</span>
                        </td>
                              <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                          <span className="text-sm">{dialog.app.rejectPartialAllocation ? '×' : '◯'}</span>
                        </td>
                              <td className="px-3 py-2 text-center border-b border-gray-200">
                          {dialog.app.rejectPartialAllocation ? (
                                  <div className="flex items-center justify-center gap-2">
                            <input
                              type="number"
                              className="input-base w-24"
                              value={dialog.app.requestedShares ?? 0}
                                      min={0}
                              max={dialog.app.requestedShares ?? 0}
                              readOnly
                            />
                                  </div>
                          ) : (
                            <input
                              type="number"
                                    className="input-base w-24"
                              value={String(dialog.allocated ?? 0).replace(/^0+(\d)/, '$1')}
                              min={0}
                              max={dialog.app.requestedShares ?? 0}
                              onChange={(e) => {
                                const normalized = e.target.value === '' ? 0 : Number(e.target.value);
                                      const app = dialog.app as InvestmentApplicationSummary;
                                      const clamped = Math.max(0, Math.min(app?.requestedShares ?? 0, isNaN(normalized) ? 0 : Math.floor(normalized)));
                                setDialog(prev => ({ ...prev, allocated: clamped }));
                              }}
                            />
                          )}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  </div>
                </div>
              ) : (
                  <div className="text-primary">
                    <span className="font-medium">{dialog.app.member?.memberName ?? '-'}</span> 様の申込：
                    <span className="font-medium">{bundleName || '年度バンドル'}</span> に
                    <span className="font-medium"> {dialog.app.requestedShares ?? 0}口</span>
                    {' を却下しますか？'}
                  </div>
                  )}
                </>
              )}
            </div>
            <div className="flex gap-4 justify-end">
                    <button className="btn-ghost" onClick={() => setDialog({ open: false })}>キャンセル</button>
                    <button className="btn-primary" onClick={confirmDialog} disabled={loading}>OK</button>
                  </div>
            </div>
          </div>
        )}
    </main>
  );
}


