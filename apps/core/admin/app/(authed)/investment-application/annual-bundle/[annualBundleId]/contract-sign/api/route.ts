import { NextRequest, NextResponse } from 'next/server';
import { completeAnnualBundleInvestmentApplications } from '@core-admin/api_clients/investment_application_client';

export async function POST(req: NextRequest) {
  const { annualBundleId, investmentApplicationIds } = await req.json();
  
  const commands = investmentApplicationIds.map((id: number) => ({
    investmentApplicationId: id,
    annualBundleId: annualBundleId,
  }));
  
  const res = await completeAnnualBundleInvestmentApplications({ commands });
  
  // 年度バンドルの結果を馬のAPIと同じ形式に変換
  const contracts = res.results
    ?.filter(r => r.success)
    ?.map(r => ({
      investmentApplicationId: r.investmentApplicationId,
      memberNumber: r.memberNumber || 0,
      memberName: r.memberName || '',
      contractedShares: r.contractedShares || 0,
    })) || [];
  
  
  // エラーがある場合は、エラーメッセージも返す
  const errors = res.results?.filter(r => !r.success) || [];
  if (errors.length > 0) {
    return NextResponse.json({ 
      contracts, 
      errors: errors.map(e => e.errorMessage) 
    });
  }
  
  return NextResponse.json({ contracts });
}
