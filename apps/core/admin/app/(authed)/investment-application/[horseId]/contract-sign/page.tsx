"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { getHorse } from "@core-admin/api_clients/horse_client";
import type { ContractItem } from '@hami/core-admin-api-schema/investment_application_service_pb';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { Link } from '@core-admin/components/common/Link';

export default function ContractSignPage() {
  const { horseId } = useParams();
  const [horseName, setHorseName] = useState("");
  const [contracts, setContracts] = useState<ContractItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const horseRes = await getHorse({ horseId: Number(horseId) });
        setHorseName(horseRes.horse?.horseName || horseRes.horse?.recruitmentName || "");
        // localStorageから契約データ取得
        const contractsStr = typeof window !== 'undefined' ? localStorage.getItem(`contracts_${horseId}`) : null;
        setContracts(contractsStr ? JSON.parse(contractsStr) : []);
      } catch (_e) {
        setError('データの取得に失敗しました');
      } finally {
        setLoading(false);
      }
    })();
  }, [horseId]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-error text-center mt-8">{error}</div>;

  const breadcrumbItems = [
    { label: '出資申込管理', href: '/investment-application' },
    { label: `${horseName} 出資状況`, href: `/investment-application/${horseId}` },
    { label: '契約締結', isCurrent: true }
  ];

  return (
    <main className="p-6 bg-white">
      <Breadcrumb items={breadcrumbItems} />
      
      <p className="text-primary text-center">
          {horseName}について、下記の内容で出資契約を締結しました。
      </p>

      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm mb-8 max-w-3xl mx-auto">
        <table className="w-full">
          <thead>
            <tr className="table-header">
              <th className="text-center w-1/2">申込者</th>
              <th className="text-center w-1/2">口数</th>
            </tr>
          </thead>
          <tbody>
            {contracts.map((contract) => (
              <tr key={contract.investmentApplicationId} className="table-row">
                <td className="table-cell text-center">
                  <span className="table-cell-text">{contract.memberName}</span>
                </td>
                <td className="table-cell text-center">
                  <span className="table-cell-text">{contract.contractedShares}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-center">
        <Link
          href={`/investment-application/${horseId}`}
          className="btn-primary text-lg px-8 py-3"
        >
          出資状況へ戻る
        </Link>
      </div>
    </main>
  );
}
