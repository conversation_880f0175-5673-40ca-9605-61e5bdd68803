"use client";
import { useState, useEffect, use } from 'react';
import { getInvestmentApplicationHorseDetail, listInvestmentApplicationsByHorse, acceptInvestmentApplications } from '@core-admin/api_clients/investment_application_client';
import { notFound } from 'next/navigation';
import type {
  GetInvestmentApplicationHorseDetailResponse,
  InvestmentApplicationItem,
} from '@hami/core-admin-api-schema/investment_application_service_pb';
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';
import { Link } from '@core-admin/components/common/Link';
import { SortOrder, InvestmentApplicationSortField } from '@hami/core-admin-api-schema/investment_application_service_pb';

type DialogState = {
  open: boolean;
  app?: InvestmentApplicationItem | InvestmentApplicationItem[];
  action?: 'accept' | 'reject';
};

const shuffleWithFisherYates = <T,>(array: T[]): T[] => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
};

export default function InvestmentApplicationHorseDetailPage({ params }: { params: Promise<{ horseId: string }> }) {
  const { horseId } = use(params);
  const horseIdNum = Number(horseId);
  const [horseDetail, setHorseDetail] = useState<GetInvestmentApplicationHorseDetailResponse | null>(null);
  const [applications, setApplications] = useState<InvestmentApplicationItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialog, setDialog] = useState<DialogState>({ open: false });
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [availableShares, setAvailableShares] = useState<number>(0);
  const [allocations, setAllocations] = useState<Record<number, number>>({});
  const [exclusions, setExclusions] = useState<Record<number, boolean>>({});
  // 抽選・先着・カナ
  const [sortMode, setSortMode] = useState<'lottery' | 'first-come' | 'kana'>('first-come');

  const resolveSortEnums = (mode: 'lottery' | 'first-come' | 'kana') => {
    if (mode === 'kana') {
      return {
        sortFieldEnum: InvestmentApplicationSortField.SORT_FIELD_MEMBER_NAME_KANA as const,
        sortOrderEnum: SortOrder.ASC as const,
      };
    }
    return {
      sortFieldEnum: InvestmentApplicationSortField.SORT_FIELD_APPLIED_AT as const,
      sortOrderEnum: SortOrder.ASC as const,
    };
  };

  useEffect(() => {
    (async () => {
      const detail = await getInvestmentApplicationHorseDetail({ horseId: horseIdNum });
      if (!detail) return notFound();
      setHorseDetail(detail);

      // API並べ替え指定（先着=作成日時昇順、カナ=昇順、抽選=作成日時昇順で取得→後でクライアント側でシャッフル）
      const { sortFieldEnum, sortOrderEnum } = resolveSortEnums(sortMode);

      const { applications } = await listInvestmentApplicationsByHorse({
        horseId: horseIdNum,
        sortField: sortFieldEnum,
        sortOrder: sortOrderEnum,
      });
      setApplications(applications);
      setAvailableShares(detail.recruitmentShares - detail.contractedShares - detail.totalApplicationShares);
    })();
  }, [horseIdNum, sortMode]);

  const handleAccept = (app: InvestmentApplicationItem) => {
    const defaultAllocation = Math.min(app.requestedShares, availableShares);
    setAllocations({ [app.investmentApplicationId]: defaultAllocation });
    setExclusions({});
    setDialog({ open: true, app, action: 'accept' });
  };
  const handleReject = (app: InvestmentApplicationItem) => {
    setDialog({ open: true, app, action: 'reject' });
  };
  const handleBatch = (action: 'accept' | 'reject') => {
    const selectedApps = sortedApplications.filter(app => selectedIds.includes(app.investmentApplicationId));
    if (action === 'accept') {
      // 既存のロジックを初期値として、残口数の範囲で自動割当（あとでモーダルで編集可能）
      let remainingShares = availableShares;
      const initial: Record<number, number> = {};
      const initialEx: Record<number, boolean> = {};
      selectedApps.forEach((app) => {
        if (app.rejectPartialAllocation && remainingShares < app.requestedShares) {
          initial[app.investmentApplicationId] = 0; // 初期値は受入不可（残口不足）
          initialEx[app.investmentApplicationId] = true; // 非許容はデフォルト除外
          return;
        }
        const allocated = Math.max(0, Math.min(app.requestedShares, remainingShares));
        initial[app.investmentApplicationId] = allocated;
        remainingShares -= allocated;
        initialEx[app.investmentApplicationId] = app.rejectPartialAllocation === true;
      });
      // 0も許容するため、最低1口ルールは撤廃
      setAllocations(initial);
      setExclusions(initialEx);
    }
    setDialog({ open: true, app: selectedApps, action });
  };
  const handleDialogOk = async () => {
    if (!dialog.app || !dialog.action) return;
    setLoading(true);
    if (Array.isArray(dialog.app)) {
      // まとめて
      if (dialog.action === 'accept') {
        const apps = dialog.app as InvestmentApplicationItem[];
        const included = apps.filter((a) => exclusions[a.investmentApplicationId] !== true);
        const acceptances = included
          .map((app: InvestmentApplicationItem) => {
            const base = allocations[app.investmentApplicationId] ?? 0;
            const clamped = Math.max(0, Math.min(app.requestedShares, Math.floor(base)));
            const chosen = app.rejectPartialAllocation ? app.requestedShares : clamped;
            return {
              horseId: horseIdNum,
              investmentApplicationId: app.investmentApplicationId,
              allocatedShares: chosen,
            };
          })
          .filter(item => item.allocatedShares > 0);
        await acceptInvestmentApplications({
          acceptances,
        });
      } else {
        await acceptInvestmentApplications({
          acceptances: dialog.app.map((app: InvestmentApplicationItem) => ({
            horseId: horseIdNum,
            investmentApplicationId: app.investmentApplicationId,
            allocatedShares: 0,
          })),
        });
      }
    } else {
      // 単体
      if (dialog.action === 'accept') {
        const singleApp = dialog.app as InvestmentApplicationItem;
        const base = allocations[singleApp.investmentApplicationId] ?? 0;
        const clamped = Math.max(0, Math.min(Math.min(singleApp.requestedShares, availableShares), Math.floor(base)));
        const chosen = singleApp.rejectPartialAllocation ? (availableShares < singleApp.requestedShares ? 0 : singleApp.requestedShares) : clamped;
        if (chosen > 0) {
          await acceptInvestmentApplications({
            acceptances: [{
              horseId: horseIdNum,
              investmentApplicationId: singleApp.investmentApplicationId,
              allocatedShares: chosen,
            }],
          });
        }
      } else {
        await acceptInvestmentApplications({
          acceptances: [{
            horseId: horseIdNum,
            investmentApplicationId: (dialog.app as InvestmentApplicationItem).investmentApplicationId,
            allocatedShares: 0,
          }],
        });
      }
    }
    // 再取得（現在のsortModeでAPIへ）
    const detail = await getInvestmentApplicationHorseDetail({ horseId: horseIdNum });
    setHorseDetail(detail);
    setAvailableShares(detail.recruitmentShares - detail.contractedShares - detail.totalApplicationShares);

    const { sortFieldEnum, sortOrderEnum } = resolveSortEnums(sortMode);
    const { applications } = await listInvestmentApplicationsByHorse({
      horseId: horseIdNum,
      sortField: sortFieldEnum,
      sortOrder: sortOrderEnum,
    });
    setApplications(applications);
    setLoading(false);
    setDialog({ open: false });
    setSelectedIds([]);
  };
  const handleDialogCancel = () => {
    setDialog({ open: false });
  };

  // 並べ替え順（抽選モード時のみシャッフル）
  let sortedApplications = [...applications];
  if (sortMode === 'lottery') {
    sortedApplications = shuffleWithFisherYates(sortedApplications);
  }
  // 却下（allocatedSharesが0または未定義）は一番下
  const isRejected = (app: InvestmentApplicationItem): boolean =>
    typeof app.allocatedShares === 'number' && app.allocatedShares === 0;
  sortedApplications = [
    ...sortedApplications.filter(app => !isRejected(app)),
    ...sortedApplications.filter(app => isRejected(app)),
  ];

  if (!horseDetail) return <div>Loading...</div>;

  // 申込合計（人数＝件数、口数＝requestedShares合計）
  const totalApplicants = applications.length;
  const totalRequestedShares = applications.reduce((sum, app) => sum + app.requestedShares, 0);
  const rejectedApplicants = applications.filter(app => isRejected(app)).length;
  const rejectedRequestedShares = applications
    .filter(app => isRejected(app))
    .reduce((sum, app) => sum + app.requestedShares, 0);

  const breadcrumbItems = [
    { label: '出資申込管理', href: '/investment-application' },
    { label: `${horseDetail.horseName || horseDetail.recruitmentName} 出資状況`, isCurrent: true }
  ];

  return (
    <main className="p-6 bg-white">
      <Breadcrumb items={breadcrumbItems} />
      
      <div className="mb-6">
        <table className="w-full">
          <tbody>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">馬名／申込馬名</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">{horseDetail.horseName || horseDetail.recruitmentName}</span>
              </td>
            </tr>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">生年月日</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">{horseDetail.birthYear}年{horseDetail.birthMonth}月{horseDetail.birthDay}日</span>
              </td>
            </tr>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">募集年</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">{horseDetail.recruitmentYear}</span>
              </td>
            </tr>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">募集番号</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">{horseDetail.recruitmentNo}</span>
              </td>
            </tr>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">出資口数/総口数(残口数)</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">{horseDetail.contractedShares}/{horseDetail.recruitmentShares}（{horseDetail.recruitmentShares - horseDetail.contractedShares}）</span>
              </td>
            </tr>
            <tr className="table-row">
              <th className="table-cell text-right w-1/3">
                <span className="table-cell-text font-medium">募集総額(万円)</span>
              </th>
              <td className="table-cell w-2/3">
                <span className="table-cell-text">{Math.floor(horseDetail.recruitmentTotalAmount / 10000)}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6 shadow-sm">
        <div className="flex justify-center gap-8">
          <span className="text-primary font-bold">
            残口数：<span className="text-error">{horseDetail.recruitmentShares - horseDetail.contractedShares}</span>
          </span>
          <span className="text-primary font-bold">
            出資申込受入口数：<span className="text-error">{horseDetail.totalApplicationShares}</span>
          </span>
        </div>
      </div>

      <div className="mb-6 flex gap-4 items-center">
        <button
          className="btn-primary"
          disabled={selectedIds.length === 0 || loading}
          onClick={() => handleBatch('accept')}
        >
          まとめて受入
        </button>
        <button
          className="btn-secondary"
          disabled={selectedIds.length === 0 || loading}
          onClick={() => handleBatch('reject')}
        >
          まとめて却下
        </button>
      </div>

      <div className="mb-6 flex gap-4 items-center">
        <label className="text-primary">現在の並べ替え順：</label>
        <select
          className="input-base"
          value={sortMode}
          onChange={e => setSortMode(e.target.value as 'lottery' | 'first-come' | 'kana')}
        >
          <option value="first-come">先着</option>
          <option value="kana">カナ</option>
          <option value="lottery">抽選</option>
        </select>
      </div>

      {/* 申込合計のサマリー（表の直前に表示） */}
      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-4 shadow-sm">
        <div className="flex justify-between gap-8">
          <div>
            <span className="text-primary font-bold">
              出資申込情報
            </span>
          </div>
          <div className="flex gap-8 justify-center">
          <span className="text-primary font-bold">
            合計人数：<span className="text-focus">{totalApplicants}</span>
            <span className="text-secondary">（却下 {rejectedApplicants}）</span>
          </span>
          <span className="text-primary font-bold">
            合計口数：<span className="text-focus">{totalRequestedShares}</span>
            <span className="text-secondary">（却下 {rejectedRequestedShares}）</span>
          </span>

          </div>
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        <table className="w-full">
          <thead>
            <tr className="table-header">
              <th className="text-center">
                <input
                  type="checkbox"
                  checked={selectedIds.length > 0 && selectedIds.length === sortedApplications.length}
                  onChange={e => {
                    if (e.target.checked) setSelectedIds(sortedApplications.filter(app => !isRejected(app)).map(app => app.investmentApplicationId));
                    else setSelectedIds([]);
                  }}
                />
              </th>
              <th className="text-center">申込日時</th>
              <th className="text-center">申込者</th>
              <th className="text-center">会員番号</th>
              <th className="text-center">申込口数</th>
              <th className="text-center">希望口数未満</th>
              <th className="text-center">出資受入口数</th>
              <th className="text-center">操作</th>
            </tr>
          </thead>
          <tbody>
            {sortedApplications.map(app => {
              const isRejectedRow = isRejected(app);
              const appDate = app.applicationDate && 'seconds' in app.applicationDate ? new Date(Number(app.applicationDate.seconds) * 1000).toLocaleString() : '';
              return (
                <tr key={app.investmentApplicationId} className={isRejectedRow ? 'bg-gray-50 text-gray-500' : 'table-row text-primary'}>
                  <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                    <input
                      type="checkbox"
                      disabled={isRejectedRow}
                      checked={selectedIds.includes(app.investmentApplicationId)}
                      onChange={e => {
                        if (e.target.checked) setSelectedIds([...selectedIds, app.investmentApplicationId]);
                        else setSelectedIds(selectedIds.filter(id => id !== app.investmentApplicationId));
                      }}
                    />
                  </td>
                  <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejectedRow ? 'text-gray-500' : 'text-primary'}`}>
                    <span className="text-sm">{appDate}</span>
                  </td>
                  <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejectedRow ? 'text-gray-500' : 'text-primary'}`}>
                    <Link href={`/members/${app.memberId}`} className="text-sm hover:text-focus underline hover:no-underline transition-colors">
                      {app.memberName}{app.memberNameKana ? `(${app.memberNameKana})` : ''}
                    </Link>
                  </td>
                  <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejectedRow ? 'text-gray-500' : 'text-primary'}`}>
                    <span className="text-sm">{app.memberNumber}</span>
                  </td>
                  <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejectedRow ? 'text-gray-500' : 'text-primary'}`}>
                    <span className="text-sm">{app.requestedShares}</span>
                  </td>
                  <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejectedRow ? 'text-gray-500' : 'text-primary'}`}>
                    <span className="text-sm">{app.rejectPartialAllocation ? '×' : '◯'}</span>
                  </td>
                  <td className={`px-3 py-2 text-center border-b border-r border-gray-200 ${isRejectedRow ? 'text-gray-500' : 'text-primary'}`}>
                    <span className="text-sm">{app.allocatedShares ?? '-'}</span>
                  </td>
                  <td className="px-3 py-2 text-center border-b border-gray-200">
                    {
                      isRejected(app) ? (
                        <span className="text-sm">
                          却下済
                        </span>
                      ) : (
                        <>
                          <button
                            className="btn-primary text-sm mr-2"
                            disabled={
                              loading ||
                              (app.rejectPartialAllocation && availableShares < app.requestedShares)
                            }
                            onClick={() => handleAccept(app)}
                          >
                            受入
                          </button>
                          <button 
                            className="btn-secondary text-sm" 
                            disabled={loading} 
                            onClick={() => handleReject(app)}
                          >
                            却下
                          </button>
                        </>
                      )
                    }
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* 確認ダイアログ */}
      {dialog.open && (
        <div className="fixed inset-0 flex items-center justify-center bg-muted bg-opacity-60 backdrop-blur-sm z-50">
          <div className="bg-white border border-stroke-separator rounded-lg p-6 shadow-lg w-full max-w-4xl mx-4">
            <div className="mb-6">
              {Array.isArray(dialog.app) ? (
                <>
                  <div className="mb-4 text-primary font-medium">
                    {dialog.action === 'accept'
                      ? `まとめて受入（${dialog.app.length}件）`
                      : `まとめて却下します（${dialog.app.length}件）`}
                  </div>
                  {dialog.action === 'accept' ? (
                    <>
                      <div className="mb-3 text-secondary">
                        残口数: <span className="text-focus">{availableShares}</span>
                      </div>
                      {(() => {
                        const apps = dialog.app as InvestmentApplicationItem[];
                        const included = apps.filter((a) => exclusions[a.investmentApplicationId] !== true);
                        const effectiveSum = included.reduce((acc, app) => acc + Math.max(0, Math.min(app.requestedShares, allocations[app.investmentApplicationId] ?? 0)), 0);
                        return (
                          <div className="mb-4 text-secondary">
                            選択合計: <span className={`font-medium ${effectiveSum > availableShares ? 'text-error' : 'text-focus'}`}>{effectiveSum}</span>
                            <span className="ml-2">（残り {Math.max(0, availableShares - effectiveSum)}）</span>
                          </div>
                        );
                      })()}
                      <div className="max-h-80 overflow-auto border border-gray-200 rounded">
                        <table className="w-full">
                          <thead>
                            <tr className="table-header">
                              <th className="text-left px-3">会員</th>
                              <th className="text-center">申込口数</th>
                              <th className="text-center">希望口数未満</th>
                              <th className="text-center">受入口数</th>
                              <th className="text-center">除外</th>
                            </tr>
                          </thead>
                          <tbody>
                            {(dialog.app as InvestmentApplicationItem[]).map((app: InvestmentApplicationItem) => {
                              const current = allocations[app.investmentApplicationId] ?? 0;
                              const max = app.requestedShares;
                              const isNoPartial = app.rejectPartialAllocation === true;
                              const excluded = exclusions[app.investmentApplicationId] === true;
                              return (
                                <tr key={app.investmentApplicationId} className={`table-row ${excluded ? 'bg-gray-50 text-gray-400' : 'text-primary'}`}>
                                  <td className="px-3 py-2 border-b border-r border-gray-200">
                                    <span className="text-sm font-medium">{app.memberName}</span>
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                                    <span className="text-sm">{app.requestedShares}</span>
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                                    <span className="text-sm">{isNoPartial ? '×' : '◯'}</span>
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-gray-200">
                                    {(excluded) ? (
                                      <div className="flex items-center justify-center gap-2">
                                        <input
                                          type="number"
                                          className="input-base w-24"
                                          value={0}
                                          min={0}
                                          max={max}
                                          readOnly
                                        />
                                      </div>
                                    ) : isNoPartial ? (
                                      <div className="flex items-center justify-center gap-2">
                                        <input
                                          type="number"
                                          className="input-base w-24"
                                          value={app.requestedShares}
                                          min={app.requestedShares}
                                          max={app.requestedShares}
                                          readOnly
                                        />
                                      </div>
                                    ) : (
                                      <input
                                        type="number"
                                        className="input-base w-24"
                                        value={String(current).replace(/^0+(\d)/, '$1')}
                                        min={0}
                                        max={max}
                                        onChange={(e) => {
                                          const normalized = e.target.value === '' ? 0 : Number(e.target.value);
                                          const clamped = Math.max(0, Math.min(max, isNaN(normalized) ? 0 : Math.floor(normalized)));
                                          setAllocations(prev => ({ ...prev, [app.investmentApplicationId]: clamped }));
                                        }}
                                      />
                                    )}
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-gray-200">
                                    <input
                                      type="checkbox"
                                      checked={excluded}
                                      onChange={(e) => {
                                        const next = e.target.checked;
                                        setExclusions(prev => ({ ...prev, [app.investmentApplicationId]: next }));
                                      }}
                                      disabled={isNoPartial}
                                    />
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </>
                  ) : (
                    <ul className="mb-4 text-secondary list-disc list-inside space-y-2">
                      {dialog.app.map((app: InvestmentApplicationItem) => (
                        <li key={app.investmentApplicationId}>
                          <span className="font-medium text-primary">{app.memberName}</span> 様：{app.requestedShares}口
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              ) : dialog.app && (
                <>
                  {dialog.action === 'accept' ? (
                    (() => {
                      const singleApp = dialog.app as InvestmentApplicationItem;
                      const current = allocations[singleApp.investmentApplicationId] ?? 0;
                      const excluded = false;
                      const maxAllowed = Math.min(singleApp.requestedShares, availableShares);
                      const sumSelected = Math.max(0, Math.min(maxAllowed, Number(current) || 0));
                      return (
                        <div className="text-primary space-y-4">
                          <div className="mb-3 text-secondary">
                            残口数: <span className="text-focus">{availableShares}</span>
                          </div>
                          <div className="mb-4 text-secondary">
                            選択合計: <span className={`font-medium ${sumSelected > availableShares ? 'text-error' : 'text-focus'}`}>{sumSelected}</span>
                            <span className="ml-2">（残り {Math.max(0, availableShares - sumSelected)}）</span>
                          </div>
                          <div className="max-h-80 overflow-auto border border-gray-200 rounded">
                            <table className="w-full">
                              <thead>
                                <tr className="table-header">
                                  <th className="text-left px-3">会員</th>
                                  <th className="text-center">申込口数</th>
                                  <th className="text-center">希望口数未満</th>
                                  <th className="text-center">受入口数</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr className={`table-row ${excluded ? 'bg-gray-50 text-gray-400' : 'text-primary'}`}>
                                  <td className="px-3 py-2 border-b border-r border-gray-200">
                                    <span className="text-sm font-medium">{singleApp.memberName}</span>
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                                    <span className="text-sm">{singleApp.requestedShares}</span>
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-r border-gray-200">
                                    <span className="text-sm">{singleApp.rejectPartialAllocation ? '×' : '◯'}</span>
                                  </td>
                                  <td className="px-3 py-2 text-center border-b border-gray-200">
                                    {singleApp.rejectPartialAllocation ? (
                                      <div className="flex items-center justify-center gap-2">
                                        <input
                                          type="number"
                                          className="input-base w-24"
                                          value={availableShares < singleApp.requestedShares ? 0 : singleApp.requestedShares}
                                          min={0}
                                          max={singleApp.requestedShares}
                                          readOnly
                                        />
                                      </div>
                                    ) : (
                                      <input
                                        type="number"
                                        className="input-base w-24"
                                        value={String(current).replace(/^0+(\d)/, '$1')}
                                        min={0}
                                        max={Math.min(singleApp.requestedShares, availableShares)}
                                        onChange={(e) => {
                                          const normalized = e.target.value === '' ? 0 : Number(e.target.value);
                                          const clamped = Math.max(0, Math.min(Math.min(singleApp.requestedShares, availableShares), isNaN(normalized) ? 0 : Math.floor(normalized)));
                                          setAllocations(prev => ({ ...prev, [singleApp.investmentApplicationId]: clamped }));
                                        }}
                                      />
                                    )}
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      );
                    })()
                  ) : (
                    <div className="text-primary">
                      <span className="font-medium">{dialog.app.memberName}</span> 様の申込：
                      <span className="font-medium">{horseDetail.horseName || horseDetail.recruitmentName}</span> に
                      <span className="font-medium"> {dialog.app.requestedShares}口</span>
                      {' 却下しますか？'}
                    </div>
                  )}
                </>
              )}{}
            </div>
            <div className="flex gap-4 justify-end">
              <button className="btn-ghost" onClick={handleDialogCancel}>キャンセル</button>
              {(() => {
                // 新ルール: 0許容、超過時は部分配分不可は0固定。OKは「強制値適用後の合計<=残口」で有効。
                let okDisabled = loading;
                if (dialog.action === 'accept') {
                  if (Array.isArray(dialog.app)) {
                    const apps = dialog.app as InvestmentApplicationItem[];
                    const included = apps.filter(a => exclusions[a.investmentApplicationId] !== true);
                    const effectiveSum = included.reduce((acc, app) => acc + Math.max(0, Math.min(app.requestedShares, allocations[app.investmentApplicationId] ?? 0)), 0);
                    if (effectiveSum > availableShares) okDisabled = true;
                    if (included.length === 0) okDisabled = true;
                    if (effectiveSum < 1) okDisabled = true;
                  } else {
                    const app = dialog.app as InvestmentApplicationItem;
                    const base = allocations[app.investmentApplicationId] ?? 0;
                    const clamped = Math.max(0, Math.min(Math.min(app.requestedShares, availableShares), base));
                    const effective = clamped;
                    if (effective > availableShares) okDisabled = true; // 実質ありえないが整合のため
                    if (effective < 1) okDisabled = true;
                  }
                }
                return (
                  <button className="btn-primary" onClick={handleDialogOk} disabled={okDisabled}>OK</button>
                );
              })()}
            </div>
          </div>
        </div>
      )}

      {/* 契約締結確認への導線 */}
      <div className="mt-8 flex justify-center">
        <Link
          href={`/investment-application/${horseId}/contract-confirm`}
          className="btn-primary text-lg px-8 py-4"
        >
          上記内容での契約締結を確認する
        </Link>
      </div>
    </main>
  );
}
