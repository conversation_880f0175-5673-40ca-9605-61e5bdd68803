"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { listContractTargetApplications } from "@core-admin/api_clients/investment_application_client";
import { getHorse } from "@core-admin/api_clients/horse_client";
import { Breadcrumb } from '@core-admin/components/common/Breadcrumb';

type ContractTargetApplicationItem = {
  investmentApplicationId: number;
  memberName: string;
  allocatedNumber: number;
};

export default function ContractConfirmPage() {
  const router = useRouter();
  const { horseId } = useParams();
  const [horseName, setHorseName] = useState("");
  const [applications, setApplications] = useState<ContractTargetApplicationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const horseRes = await getHorse({ horseId: Number(horseId) });
        setHorseName(horseRes.horse?.horseName || horseRes.horse?.recruitmentName || "");
        const res = await listContractTargetApplications({ horseId: Number(horseId) });
        setApplications(res.applications || []);
      } catch (_e) {
        setError('データの取得に失敗しました');
      } finally {
        setLoading(false);
      }
    })();
  }, [horseId]);

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="text-error text-center mt-8">{error}</div>;

  const investmentApplicationIds = applications.map(app => app.investmentApplicationId);

  const breadcrumbItems = [
    { label: '出資申込管理', href: '/investment-application' },
    { label: `${horseName} 出資状況`, href: `/investment-application/${horseId}` },
    { label: '契約締結確認', isCurrent: true }
  ];

  return (
    <main className="p-6 bg-white">
      <Breadcrumb items={breadcrumbItems} />
      
      <p className="text-primary text-center mb-4">
          {horseName}について、下記の内容で出資契約を結びます。<br />
          よろしいですか？
        </p>

      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm mb-8 max-w-3xl mx-auto">
        <table className="w-full">
          <thead>
            <tr className="table-header">
              <th className="text-center w-1/2">申込者</th>
              <th className="text-center w-1/2">口数</th>
            </tr>
          </thead>
          <tbody>
            {applications.map((app) => (
              <tr key={app.investmentApplicationId} className="table-row">
                <td className="table-cell text-center">
                  <span className="table-cell-text">{app.memberName}</span>
                </td>
                <td className="table-cell text-center">
                  <span className="table-cell-text">{app.allocatedNumber}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-center gap-6">
        <button
          className="btn-secondary text-lg px-8 py-3"
          onClick={() => router.back()}
        >
          戻る
        </button>
        <button
          className="btn-primary text-lg px-8 py-3"
          onClick={async () => {
            const horseIdNum = Number(horseId);
            if (!horseIdNum || horseIdNum < 0 || investmentApplicationIds.length === 0) {
              alert('馬IDまたは申込IDが不正です');
              return;
            }
            const res = await fetch(`/investment-application/${horseId}/contract-sign/api`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ horseId: horseIdNum, investmentApplicationIds }),
            });
            const data = await res.json();
            if (typeof window !== 'undefined') {
              localStorage.setItem(`contracts_${horseId}`, JSON.stringify(data.contracts));
            }
            router.push(`/investment-application/${horseId}/contract-sign`);
          }}
        >
          契約を結ぶ
        </button>
      </div>
    </main>
  );
}