import Link from 'next/link';
import { listMembershipApplications } from '@core-admin/api_clients/membership_application_client';
import { ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { formatDate } from '@core-admin/utils/date_formatter';

export default async function Page() {
  const { membershipApplications } = await listMembershipApplications({});

  const targets = membershipApplications.filter((app) => {
    const isPrimaryApproved = app.applicationReviewStatus === ReviewType.APPROVE && app.documentGroupReviewStatus === ReviewType.APPROVE;
    const isCompliancePending =
      app.complianceApplicationReviewStatus === ReviewType.NOT_REVIEWED ||
      app.complianceDocumentGroupReviewStatus === ReviewType.NOT_REVIEWED;
    return isPrimaryApproved && isCompliancePending;
  });

  // 並び順: 更新日時降順
  targets.sort((a, b) => Number(b.updatedAt) - Number(a.updatedAt));

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">コンプライアンス審査待ち一覧</h1>
      </div>

      <div className="container mx-auto px-6">
        <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded-lg">
          <table className="w-full">
            <thead>
              <tr className="table-header">
                <th>申込ID</th>
                <th>氏名</th>
                <th>メール</th>
                <th>申込日時</th>
                <th>一次審査通過日時</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {targets.map((app) => (
                <tr key={app.membershipApplicationId} className="table-row">
                  <td className="table-cell">
                    <span className="table-cell-text">{app.membershipApplicationId}</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {app.lastName} {app.firstName}
                    </span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{app.email}</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{formatDate(Number(app.appliedAt))}</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{app.primaryApprovedAt ? formatDate(Number(app.primaryApprovedAt)) : '-'}</span>
                  </td>
                  <td className="table-cell">
                    <Link className="btn-ghost text-sm" href={`/compliance/membership-applications/${app.membershipApplicationId}`}>
                      審査へ
                    </Link>
                  </td>
                </tr>
              ))}
              {targets.length === 0 && (
                <tr>
                  <td className="table-cell text-secondary" colSpan={6}>
                    <span className="table-cell-text">審査待ちの申込はありません。</span>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </main>
  );
}
