import { notFound } from 'next/navigation';
import { getMembershipApplication } from '@core-admin/api_clients/membership_application_client';
import { ReviewType, MembershipApplication } from '@hami/core-admin-api-schema/membership_application_service_pb';

import ComplianceReviewApplicationForm from '../../../membership-application/[id]/ComplianceReviewApplicationForm';
import ComplianceDocumentReviewForm from '../../../membership-application/[id]/ComplianceDocumentReviewForm';
import { MembershipApplicationDetails } from '../../../membership-application/[id]/MembershipApplicationDetails';
import IdentityDocumentsList from '../../../membership-application/[id]/IdentityDocumentsList';
import ApplicationReviewLogs from '../../../membership-application/[id]/ApplicationReviewLogs';
import DocumentGroupReviewLogs from '../../../membership-application/[id]/DocumentGroupReviewLogs';

import { Breadcrumb } from '../../../../../components/common/Breadcrumb';

export default async function Page({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const id = Number(resolvedParams.id);
  if (isNaN(id)) return notFound();

  const {
    membershipApplication: application,
    complianceApplicationReviewLogs = [],
    complianceDocumentGroupReviewLogs = [],
  } = await getMembershipApplication({ membershipApplicationId: id });
  if (!application) return notFound();

  // 一次審査が完了していないケースは、この画面の対象外
  const isPrimaryApproved =
    application.applicationReviewStatus === ReviewType.APPROVE && application.documentGroupReviewStatus === ReviewType.APPROVE;
  if (!isPrimaryApproved) return notFound();

  return (
    <main className="container mx-auto px-4 py-8">
      <Breadcrumb
        items={[
          { label: 'コンプライアンス審査待ち一覧', href: '/compliance/membership-applications' },
          { label: `申込詳細（ID: ${application.membershipApplicationId}）`, isCurrent: true },
        ]}
      />

      <MembershipApplicationDetails application={application} />

      <div className="mt-6">
        <IdentityDocumentsList documents={application.identityDocuments} />
      </div>

      <div className="mt-6">{renderComplianceSection(application)}</div>

      <div className="mt-6">
        <ApplicationReviewLogs title="コンプライアンス審査ログ" logs={complianceApplicationReviewLogs} />
      </div>
      <div className="mt-6">
        <DocumentGroupReviewLogs title="コンプライアンス書類審査ログ" logs={complianceDocumentGroupReviewLogs} />
      </div>
    </main>
  );
}

function renderComplianceSection(application: MembershipApplication) {
  if (application.complianceApplicationReviewStatus === ReviewType.NOT_REVIEWED) {
    return <ComplianceReviewApplicationForm membershipApplicationId={application.membershipApplicationId} />;
  }
  if (
    application.complianceApplicationReviewStatus === ReviewType.APPROVE &&
    application.complianceDocumentGroupReviewStatus === ReviewType.NOT_REVIEWED
  ) {
    return <ComplianceDocumentReviewForm membershipApplication={application} />;
  }
  return (
    <div className="card bg-success mt-6">
      <h2 className="text-xl font-bold text-primary mb-4">審査状況</h2>
      <p className="text-success">この申込のコンプライアンス審査は完了しています。</p>
    </div>
  );
}
