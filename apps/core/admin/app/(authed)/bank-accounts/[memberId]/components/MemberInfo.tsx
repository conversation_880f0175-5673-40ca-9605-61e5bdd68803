import { MemberInfo as MemberInfoType } from '@hami/core-admin-api-schema/bank_account_service_pb';

interface MemberInfoProps {
  member: MemberInfoType;
}

export function MemberInfo({ member }: MemberInfoProps) {
  return (
    <div className="mb-8 bg-surface-card border border-stroke-separator rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4">会員情報</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">
            会員ID
          </label>
          <div className="text-sm">
            {member.memberId}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">
            会員番号
          </label>
          <div className="text-sm">
            {member.memberNumber}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">
            会員名
          </label>
          <div className="text-sm">
            {member.memberName}
          </div>
        </div>
        
        <div className="md:col-span-2 lg:col-span-3">
          <label className="block text-sm font-medium mb-1">
            メールアドレス
          </label>
          <div className="text-sm">
            {member.email}
          </div>
        </div>
      </div>
    </div>
  );
}
