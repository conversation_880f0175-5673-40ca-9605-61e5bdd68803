import { getBankAccountDetail } from '@core-admin/api_clients/bank_account_client';
import { BankAccountRegistrationDetail, MemberInfo as MemberInfoType } from '@hami/core-admin-api-schema/bank_account_service_pb';
import Link from 'next/link';
import { BankAccountDetail } from './components/BankAccountDetail';
import { MemberInfo } from './components/MemberInfo';
import { Breadcrumb } from '../../../../components/common/Breadcrumb';

interface PageProps {
  params: Promise<{
    memberId: string;
  }>;
}

export default async function Page({ params }: PageProps) {
  const { memberId: memberIdStr } = await params;
  const memberId = parseInt(memberIdStr);

  const breadcrumbItems = [
    { label: '口座登録管理', href: '/bank-accounts' },
    { label: '口座登録詳細', isCurrent: true }
  ];

  if (isNaN(memberId)) {
    return (
      <div className="px-4 sm:px-6 lg:px-8">
        <Breadcrumb items={breadcrumbItems} />
        
        <div className="mb-8">
          <p className="text-muted-foreground mt-2">
            会員の銀行口座登録詳細を確認します
          </p>
        </div>

        <div className="bg-surface-card border border-stroke-separator rounded-lg p-6">
          <div className="text-error font-semibold">無効な会員IDです。</div>
          <Link href="/bank-accounts" className="mt-4 inline-block text-primary underline">
            口座登録一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  let memberInfo: MemberInfoType | null = null;
  let registrations: BankAccountRegistrationDetail[] = [];
  let activeRegistration: BankAccountRegistrationDetail | null = null;
  let error: string | null = null;

  try {
    const result = await getBankAccountDetail({ memberId });
    memberInfo = result.member ?? null;
    registrations = result.registrations ?? [];
    activeRegistration = result.activeRegistration ?? null;
  } catch (_e) {
    error = '口座登録詳細の取得中にエラーが発生しました。しばらくしてから再度お試しください。';
  }

  if (error) {
    return (
      <div className="px-4 sm:px-6 lg:px-8">
        <Breadcrumb items={breadcrumbItems} />
        
        <div className="mb-8">
          <p className="text-muted-foreground mt-2">
            会員の銀行口座登録詳細を確認します
          </p>
        </div>

        <div className="bg-surface-card border border-stroke-separator rounded-lg p-6">
          <div className="text-error font-semibold">{error}</div>
          <Link href="/bank-accounts" className="mt-4 inline-block text-primary underline">
            口座登録一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Breadcrumb items={breadcrumbItems} />
      
      <div className="w-full max-w-6xl">

        {/* 会員情報 */}
        {memberInfo && <MemberInfo member={memberInfo} />}

        {/* アクティブな口座登録 */}
        {activeRegistration && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4 text-success">現在有効な口座登録</h2>
            <BankAccountDetail registration={activeRegistration} isActive={true} />
          </div>
        )}

        {/* 口座登録履歴 */}
        <div>
          <h2 className="text-xl font-semibold mb-4">口座登録履歴</h2>
          {registrations.length === 0 ? (
            <div className="bg-surface-card border border-stroke-separator rounded-lg p-6 text-center text-muted">
              口座登録履歴がありません。
            </div>
          ) : (
            <div className="space-y-4">
              {registrations.map((registration, index) => (
                <BankAccountDetail
                  key={registration.bankAccountRegistrationId}
                  registration={registration}
                  isActive={registration.isActive}
                  isLatest={index === 0}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
