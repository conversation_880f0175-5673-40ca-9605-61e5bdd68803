import { BankAccountRegistrationSummary, BankAccountRegistrationStatus } from '@hami/core-admin-api-schema/bank_account_service_pb';
import Link from 'next/link';
import { Pagination } from './Pagination';

interface BankAccountListProps {
  registrations: BankAccountRegistrationSummary[];
  currentPage: number;
  totalPages: number;
  totalCount: number;
  searchParams: Record<string, string | undefined>;
}

function getStatusLabel(status: BankAccountRegistrationStatus): string {
  switch (status) {
    case BankAccountRegistrationStatus.ENTRY:
      return '登録済み';
    case BankAccountRegistrationStatus.START:
      return '金融機関画面遷移';
    case BankAccountRegistrationStatus.TERM:
      return '結果確認';
    case BankAccountRegistrationStatus.SUCCESS:
      return '申込成功';
    case BankAccountRegistrationStatus.FAIL:
      return '申込失敗';
    case BankAccountRegistrationStatus.UNPROCESSED:
      return '申込失敗（未処理）';
    default:
      return '不明';
  }
}

function getStatusBadgeClass(status: BankAccountRegistrationStatus): string {
  switch (status) {
    case BankAccountRegistrationStatus.SUCCESS:
      return 'badge badge-success';
    case BankAccountRegistrationStatus.FAIL:
    case BankAccountRegistrationStatus.UNPROCESSED:
      return 'badge badge-error';
    case BankAccountRegistrationStatus.ENTRY:
    case BankAccountRegistrationStatus.START:
    case BankAccountRegistrationStatus.TERM:
      return 'badge badge-warning';
    default:
      return 'badge badge-info';
  }
}

export function BankAccountList({ 
  registrations, 
  currentPage, 
  totalPages, 
  totalCount,
  searchParams 
}: BankAccountListProps) {
  return (
    <div className="w-full max-w-7xl">
      {/* ページネーション（上部） */}
      <div className="mb-4">
        <Pagination 
          currentPage={currentPage}
          totalPages={totalPages}
          totalCount={totalCount}
          searchParams={searchParams}
        />
      </div>

      {/* テーブル */}
      <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded-lg">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="table-header">
                <th>会員ID</th>
                <th>会員番号</th>
                <th>会員名</th>
                <th>ステータス</th>
                <th>銀行コード</th>
                <th>口座番号</th>
                <th>作成日時</th>
                <th>更新日時</th>
                <th>詳細</th>
              </tr>
            </thead>
            <tbody>
              {!registrations || registrations.length === 0 ? (
                <tr className="table-row">
                  <td className="table-cell" colSpan={9}>
                    <span className="table-cell-text text-center">口座登録が見つかりません。</span>
                  </td>
                </tr>
              ) : (
                registrations.map((registration) => (
                  <tr key={registration.bankAccountRegistrationId} className="table-row">
                    <td className="table-cell">
                      <span className="table-cell-text">{registration.memberId}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{registration.memberNumber}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{registration.memberName}</span>
                    </td>
                    <td className="table-cell">
                      <span className={getStatusBadgeClass(registration.registrationStatus)}>
                        {getStatusLabel(registration.registrationStatus)}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{registration.bankCode}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">{registration.resultAccountNumber || '-'}</span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">
                        {new Date(Number(registration.createdAt)).toLocaleString('ja-JP')}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="table-cell-text">
                        {new Date(Number(registration.updatedAt)).toLocaleString('ja-JP')}
                      </span>
                    </td>
                    <td className="table-cell">
                      <Link 
                        href={`/bank-accounts/${registration.memberId}`} 
                        className="btn-secondary text-sm"
                      >
                        詳細 &gt;
                      </Link>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* ページネーション（下部） */}
      <div className="mt-4">
        <Pagination 
          currentPage={currentPage}
          totalPages={totalPages}
          totalCount={totalCount}
          searchParams={searchParams}
        />
      </div>
    </div>
  );
}
