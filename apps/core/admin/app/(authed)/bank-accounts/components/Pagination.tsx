import Link from 'next/link';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  searchParams: Record<string, string | undefined>;
}

export function Pagination({ currentPage, totalPages, totalCount, searchParams }: PaginationProps) {
  const createPageUrl = (page: number) => {
    const params = new URLSearchParams();

    // 既存の検索パラメータを保持
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value && key !== 'page') {
        params.set(key, value);
      }
    });

    params.set('page', page.toString());
    return `/bank-accounts?${params.toString()}`;
  };

  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // 総ページ数が少ない場合は全て表示
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 現在のページを中心に表示
      const start = Math.max(1, currentPage - 2);
      const end = Math.min(totalPages, currentPage + 2);

      if (start > 1) {
        pages.push(1);
        if (start > 2) {
          pages.push('...');
        }
      }

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (end < totalPages) {
        if (end < totalPages - 1) {
          pages.push('...');
        }
        pages.push(totalPages);
      }
    }

    return pages;
  };

  if (totalPages <= 1) {
    return (
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted">全 {totalCount} 件</div>
      </div>
    );
  }

  return (
    <div className="flex justify-between items-center">
      <div className="text-sm text-muted">
        全 {totalCount} 件 (ページ {currentPage} / {totalPages})
      </div>

      <div className="flex items-center space-x-1">
        {/* 前のページ */}
        {currentPage > 1 ? (
          <Link
            href={createPageUrl(currentPage - 1)}
            className="btn-ghost text-sm"
          >
            前へ
          </Link>
        ) : (
          <span className="btn-ghost text-sm disabled">前へ</span>
        )}

        {/* ページ番号 */}
        {getPageNumbers().map((page, index) => (
          <span key={`${page}-${index}`}>
            {typeof page === 'number' ? (
              page === currentPage ? (
                <span className="btn-primary text-sm">{page}</span>
              ) : (
                <Link href={createPageUrl(page)} className="btn-ghost text-sm">
                  {page}
                </Link>
              )
            ) : (
              <span className="text-sm text-muted px-3 py-2">{page}</span>
            )}
          </span>
        ))}

        {/* 次のページ */}
        {currentPage < totalPages ? (
          <Link
            href={createPageUrl(currentPage + 1)}
            className="btn-ghost text-sm"
          >
            次へ
          </Link>
        ) : (
          <span className="btn-ghost text-sm disabled">次へ</span>
        )}
      </div>
    </div>
  );
}
