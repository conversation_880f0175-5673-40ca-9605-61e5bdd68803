'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';

interface SearchFormProps {
  initialSearch: string;
  initialStatus?: string;
  initialSortBy: string;
  initialSortOrder: string;
}

const statusOptions = [
  { value: '', label: '全て' },
  { value: 'ENTRY', label: '登録済み' },
  { value: 'START', label: '金融機関画面遷移' },
  { value: 'TERM', label: '結果確認' },
  { value: 'SUCCESS', label: '申込成功' },
  { value: 'FAIL', label: '申込失敗' },
  { value: 'UNPROCESSED', label: '申込失敗（未処理）' },
];

const sortOptions = [
  { value: 'created_at', label: '作成日時' },
  { value: 'updated_at', label: '更新日時' },
  { value: 'member_name', label: '会員名' },
];

export function SearchForm({ initialSearch, initialStatus, initialSortBy, initialSortOrder }: SearchFormProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [search, setSearch] = useState(initialSearch);
  const [status, setStatus] = useState(initialStatus || '');
  const [sortBy, setSortBy] = useState(initialSortBy);
  const [sortOrder, setSortOrder] = useState(initialSortOrder);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const params = new URLSearchParams(searchParams);

    // 検索条件を設定
    if (search) {
      params.set('search', search);
    } else {
      params.delete('search');
    }

    if (status) {
      params.set('status', status);
    } else {
      params.delete('status');
    }

    params.set('sortBy', sortBy);
    params.set('sortOrder', sortOrder);

    // ページを1にリセット
    params.set('page', '1');

    router.push(`/bank-accounts?${params.toString()}`);
  };

  const handleReset = () => {
    setSearch('');
    setStatus('');
    setSortBy('created_at');
    setSortOrder('desc');
    router.push('/bank-accounts');
  };

  return (
    <div className="mb-6 bg-surface-card border border-stroke-separator rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4">検索・フィルター</h2>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* 検索クエリ */}
          <div>
            <label htmlFor="search" className="block text-sm font-medium mb-2">
              会員名・会員番号
            </label>
            <input
              type="text"
              id="search"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              placeholder="田中 または 12345"
              className="w-full px-3 py-2 border border-stroke-border rounded-md focus:outline-none focus:ring-2 focus:ring-stroke-focus"
            />
          </div>

          {/* ステータスフィルター */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium mb-2">
              ステータス
            </label>
            <select
              id="status"
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full px-3 py-2 border border-stroke-border rounded-md focus:outline-none focus:ring-2 focus:ring-stroke-focus"
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* ソート項目 */}
          <div>
            <label htmlFor="sortBy" className="block text-sm font-medium mb-2">
              ソート項目
            </label>
            <select
              id="sortBy"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-3 py-2 border border-stroke-border rounded-md focus:outline-none focus:ring-2 focus:ring-stroke-focus"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* ソート順 */}
          <div>
            <label htmlFor="sortOrder" className="block text-sm font-medium mb-2">
              ソート順
            </label>
            <select
              id="sortOrder"
              value={sortOrder}
              onChange={(e) => setSortOrder(e.target.value)}
              className="w-full px-3 py-2 border border-stroke-border rounded-md focus:outline-none focus:ring-2 focus:ring-stroke-focus"
            >
              <option value="desc">降順</option>
              <option value="asc">昇順</option>
            </select>
          </div>
        </div>

        {/* ボタン */}
        <div className="flex gap-2">
          <button
            type="submit"
            className="btn-primary"
          >
            検索
          </button>
          <button
            type="button"
            onClick={handleReset}
            className="btn-secondary"
          >
            リセット
          </button>
        </div>
      </form>
    </div>
  );
}
