import { BankAccountRegistrationSummary, BankAccountRegistrationStatus } from '@hami/core-admin-api-schema/bank_account_service_pb';
import { listBankAccountRegistrations } from '@core-admin/api_clients/bank_account_client';
import { BankAccountList } from './components/BankAccountList';
import { SearchForm } from './components/SearchForm';

interface SearchParams extends Record<string, string | undefined> {
  page?: string;
  limit?: string;
  status?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: string;
}

export default async function Page({ searchParams }: { searchParams: Promise<SearchParams> }) {
  const params = await searchParams;
  const page = parseInt(params.page || '1');
  const limit = parseInt(params.limit || '20');
  const statusFilter = params.status ? [params.status as keyof typeof BankAccountRegistrationStatus] : [];
  const searchQuery = params.search || '';
  const sortBy = (params.sortBy as 'created_at' | 'updated_at' | 'member_name') || 'created_at';
  const sortOrder = (params.sortOrder as 'asc' | 'desc') || 'desc';

  let registrations: BankAccountRegistrationSummary[] = [];
  let totalCount = 0;
  let currentPage = 1;
  let totalPages = 1;
  let error: string | null = null;

  try {
    const result = await listBankAccountRegistrations({
      page,
      limit,
      statusFilter: statusFilter.map((status) => BankAccountRegistrationStatus[status]),
      searchQuery: searchQuery || undefined,
      sortBy,
      sortOrder,
    });

    registrations = result.registrations;
    totalCount = result.totalCount;
    currentPage = result.currentPage;
    totalPages = result.totalPages;
  } catch (_e) {
    error = '口座登録情報の取得中にエラーが発生しました。しばらくしてから再度お試しください。';
  }

  if (error) {
    return (
      <main className="bg-white min-h-screen">
        <div className="bg-surface-elevated text-center py-6 mb-6">
          <h1 className="text-xl font-bold text-primary">会員銀行口座管理</h1>
        </div>

        <div className="container mx-auto px-6">
          <div className="bg-surface-card border border-stroke-separator rounded-lg p-6">
            <div className="text-error font-semibold">{error}</div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">会員銀行口座管理</h1>
      </div>

      <div className="container mx-auto px-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <SearchForm initialSearch={searchQuery} initialStatus={params.status} initialSortBy={sortBy} initialSortOrder={sortOrder} />
          </div>
          <div className="flex gap-4 items-center" />
        </div>

        <div className="mb-6 bg-surface-card border border-stroke-separator rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">統計情報</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">総件数:</span> {totalCount}件
            </div>
            <div>
              <span className="font-medium">成功:</span>{' '}
              {registrations.filter((r) => r.registrationStatus === BankAccountRegistrationStatus.SUCCESS).length}件
            </div>
            <div>
              <span className="font-medium">失敗:</span>{' '}
              {
                registrations.filter((r) =>
                  [BankAccountRegistrationStatus.FAIL, BankAccountRegistrationStatus.UNPROCESSED].includes(r.registrationStatus)
                ).length
              }
              件
            </div>
            <div>
              <span className="font-medium">処理中:</span>{' '}
              {
                registrations.filter((r) =>
                  [BankAccountRegistrationStatus.ENTRY, BankAccountRegistrationStatus.START, BankAccountRegistrationStatus.TERM].includes(
                    r.registrationStatus
                  )
                ).length
              }
              件
            </div>
          </div>
        </div>

        <BankAccountList
          registrations={registrations}
          currentPage={currentPage}
          totalPages={totalPages}
          totalCount={totalCount}
          searchParams={params}
        />
      </div>
    </main>
  );
}
