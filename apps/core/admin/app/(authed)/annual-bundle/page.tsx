import Link from 'next/link';
import { listAnnualBundles } from '@core-admin/api_clients/annual_bundle_client';
import { AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@hami/core-admin-api-schema/annual_bundle_service_pb';

export const metadata = {
  robots: 'noindex, nofollow',
};

export const dynamic = 'force-dynamic';

interface SearchParams {
  page?: string;
  fiscalYear?: string;
}

function publishStatusLabel(status: AnnualBundlePublishStatus) {
  return status === AnnualBundlePublishStatus.AB_PUBLIC ? '公開' : '非公開';
}

function recruitmentStatusLabel(status: AnnualBundleRecruitmentStatus) {
  switch (status) {
    case AnnualBundleRecruitmentStatus.AB_UPCOMING:
      return '募集前';
    case AnnualBundleRecruitmentStatus.AB_ACTIVE:
      return '募集中';
    case AnnualBundleRecruitmentStatus.AB_CLOSED:
      return '募集終了';
    case AnnualBundleRecruitmentStatus.AB_FULL:
      return '満口';
    default:
      return '-';
  }
}

export default async function AnnualBundleListPage({ searchParams }: { searchParams: Promise<SearchParams> }) {
  const resolved = await searchParams;
  const page = parseInt(resolved.page || '1');
  const fiscalYear = resolved.fiscalYear ? parseInt(resolved.fiscalYear) : undefined;

  const { bundles, totalPages, page: currentPage } = await listAnnualBundles({ page, limit: 20, fiscalYear });

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">一括出資パッケージ管理</h1>
      </div>

      <div className="container mx-auto px-6">
        <div className="flex justify-between items-center mb-6">
          <form method="GET" className="flex gap-2 items-center">
            <input type="number" name="fiscalYear" placeholder="年度" defaultValue={fiscalYear?.toString()} className="input-base w-32" />
            <button type="submit" className="btn-primary">検索する</button>
            {fiscalYear && (
              <Link href="/annual-bundle" className="btn-secondary">クリア</Link>
            )}
          </form>

          <Link href="/annual-bundle/new" className="btn-primary">一括出資パッケージを作成</Link>
        </div>

        <div className="bg-surface-card border border-stroke-separator overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="table-header">
                <th>年度</th>
                <th>名称</th>
                <th>口数</th>
                <th>公開</th>
                <th>募集</th>
                <th className="w-[190px]"></th>
              </tr>
            </thead>
            <tbody>
              {bundles.map((b) => (
                <tr key={b.annualBundleId} className="table-row">
                  <td className="table-cell"><span className="table-cell-text">{b.fiscalYear}</span></td>
                  <td className="table-cell"><Link href={`/annual-bundle/${b.annualBundleId}`} className="text-primary hover:underline">{b.name}</Link></td>
                  <td className="table-cell"><span className="table-cell-text">{b.shares}</span></td>
                  <td className="table-cell"><span className="table-cell-text">{publishStatusLabel(b.publishStatus)}</span></td>
                  <td className="table-cell"><span className="table-cell-text">{recruitmentStatusLabel(b.recruitmentStatus)}</span></td>
                  <td className="table-cell">
                    <Link href={`/annual-bundle/${b.annualBundleId}`} className="text-sm btn-ghost">詳細</Link>
                    <Link href={`/annual-bundle/${b.annualBundleId}/edit`} className="text-sm btn-ghost">編集</Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {totalPages > 1 && (
          <div className="mt-6 flex justify-center gap-2">
            {currentPage > 1 && (
              <Link href={`?page=${currentPage - 1}${fiscalYear ? `&fiscalYear=${fiscalYear}` : ''}`} className="btn-secondary">前へ</Link>
            )}
            <span className="px-3 py-2 text-secondary">{currentPage} / {totalPages} ページ</span>
            {currentPage < totalPages && (
              <Link href={`?page=${currentPage + 1}${fiscalYear ? `&fiscalYear=${fiscalYear}` : ''}`} className="btn-secondary">次へ</Link>
            )}
          </div>
        )}

        {bundles.length === 0 && (
          <div className="text-center py-8 text-muted">データがありません</div>
        )}
      </div>
    </main>
  );
}
