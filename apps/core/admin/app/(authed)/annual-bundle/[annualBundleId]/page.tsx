import Link from 'next/link';
import { getAnnualBundle } from '@core-admin/api_clients/annual_bundle_client';
import { AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@hami/core-admin-api-schema/annual_bundle_service_pb';

export const metadata = { robots: 'noindex, nofollow' };

function publishStatusLabel(status: AnnualBundlePublishStatus) {
  return status === AnnualBundlePublishStatus.AB_PUBLIC ? '公開' : '非公開';
}

function recruitmentStatusLabel(status: AnnualBundleRecruitmentStatus) {
  switch (status) {
    case AnnualBundleRecruitmentStatus.AB_UPCOMING:
      return '募集前';
    case AnnualBundleRecruitmentStatus.AB_ACTIVE:
      return '募集中';
    case AnnualBundleRecruitmentStatus.AB_CLOSED:
      return '募集終了';
    case AnnualBundleRecruitmentStatus.AB_FULL:
      return '満口';
    default:
      return '-';
  }
}

export default async function AnnualBundleDetailPage({ params }: { params: Promise<{ annualBundleId: string }> }) {
  const { annualBundleId } = await params;
  const { bundle } = await getAnnualBundle({ annualBundleId: parseInt(annualBundleId) });

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">一括出資パッケージ詳細</h1>
      </div>

      <div className="container mx-auto px-6">
        <div className="mb-6 flex justify-between">
          <Link href="/annual-bundle" className="btn-secondary">一覧へ戻る</Link>
          <div className="flex gap-2">
            <Link href={`/annual-bundle/${bundle!.annualBundleId}/horses`} className="btn-secondary">紐付け馬を選ぶ</Link>
            <Link href={`/annual-bundle/${bundle!.annualBundleId}/edit`} className="btn-primary">編集</Link>
          </div>
        </div>

        <div className="bg-surface-card border border-stroke-separator p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div><div className="text-secondary text-sm">年度</div><div className="text-base">{bundle!.fiscalYear}</div></div>
            <div><div className="text-secondary text-sm">名称</div><div className="text-base">{bundle!.name}</div></div>
            <div>
              <div className="text-secondary text-sm">口数</div>
              <div className="text-base">{bundle!.shares}</div>
              {bundle!.horses.length > 0 && (() => {
                const minShares = Math.min(...bundle!.horses.map((h) => h.sharesTotal || 0));
                const maxAllowed = Math.floor(minShares / 2);
                const isExceeded = bundle!.shares > maxAllowed;
                return isExceeded ? (
                  <div className="mt-1 text-sm text-red-600">パッケージの口数が上限({maxAllowed} 口)を超えています。</div>
                ) : null;
              })()}
            </div>
            <div><div className="text-secondary text-sm">公開</div><div className="text-base">{publishStatusLabel(bundle!.publishStatus!)}</div></div>
            <div><div className="text-secondary text-sm">募集</div><div className="text-base">{recruitmentStatusLabel(bundle!.recruitmentStatus!)}</div></div>
          </div>
        </div>

        <div className="bg-surface-card border border-stroke-separator p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-bold">紐付け馬</h2>
            <Link href={`/annual-bundle/${bundle!.annualBundleId}/horses`} className="btn-secondary">変更</Link>
          </div>
          {bundle!.horses.length === 0 ? (
            <div className="text-muted">未設定</div>
          ) : (
            <table className="w-full">
              <thead>
                <tr className="table-header">
                  <th className="w-24">募集番号</th>
                  <th>馬名</th>
                  <th className="w-24">口数</th>
                </tr>
              </thead>
              <tbody>
                {bundle!.horses.sort((a, b) => a.recruitmentNo - b.recruitmentNo).map((h) => (
                  <tr key={h.horseId} className="table-row">
                    <td className="table-cell"><span className="table-cell-text">{String(h.recruitmentNo ?? '').padStart(3, '0')}</span></td>
                    <td className="table-cell">
                      <Link href={`/horses/${h.horseId}`} className="text-primary hover:underline">
                        {h.horseName || h.recruitmentName || `馬ID: ${h.horseId}`}
                      </Link>
                    </td>
                    <td className="table-cell"><span className="table-cell-text">{h.sharesTotal}</span></td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </main>
  );
}
