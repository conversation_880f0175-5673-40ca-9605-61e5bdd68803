import Link from 'next/link';
import { redirect } from 'next/navigation';
import { listAnnualBundleHorsesCandidates, getAnnualBundle, updateAnnualBundle } from '@core-admin/api_clients/annual_bundle_client';

export const metadata = { robots: 'noindex, nofollow' };
export const dynamic = 'force-dynamic';

export default async function BundleHorsesPage({ params, searchParams }: { params: Promise<{ annualBundleId: string }>; searchParams: Promise<{ page?: string; search?: string }> }) {
  const { annualBundleId } = await params;
  const { page, search } = await searchParams;
  const id = parseInt(annualBundleId);
  const pageNum = parseInt(page || '1');

  const [{ bundle }, { horses, totalPages, page: currentPage }] = await Promise.all([
    getAnnualBundle({ annualBundleId: id }),
    listAnnualBundleHorsesCandidates({ annualBundleId: id, page: pageNum, limit: 20, search: search || undefined }),
  ]);

  async function saveSelection(formData: FormData) {
    'use server';
    const ids = formData.getAll('horseId').map((v) => parseInt(v as string)).filter((n) => !Number.isNaN(n));
    await updateAnnualBundle({ annualBundleId: id, horseIds: ids });
    redirect(`/annual-bundle/${id}`);
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">紐付け馬の選択</h1>
        <div className="text-secondary mt-1">年度: {bundle!.fiscalYear} / {bundle!.name}</div>
      </div>

      <div className="container mx-auto px-6">
        <div className="flex justify-between items-center mb-6">
          <Link href={`/annual-bundle/${id}`} className="btn-secondary">戻る</Link>
          <form className="flex gap-2" method="GET">
            <input className="input-base" name="search" defaultValue={search} placeholder="馬名/募集名" />
            <button className="btn-primary" type="submit">検索</button>
            {search && <Link href={`/annual-bundle/${id}/horses`} className="btn-secondary">クリア</Link>}
          </form>
        </div>

        <form action={saveSelection} className="bg-surface-card border border-stroke-separator p-4">
          <table className="w-full">
            <thead>
              <tr className="table-header">
                <th className="w-16"></th>
                <th className="w-24">年度</th>
                <th className="w-24">募集ID</th>
                <th>募集名</th>
                <th>馬名</th>
                <th className="w-24">募集口数</th>
              </tr>
            </thead>
            <tbody>
              {horses.sort((a, b) => a.recruitmentNo - b.recruitmentNo).map((h) => (
                <tr key={h.horseId} className="table-row">
                  <td className="table-cell"><input type="checkbox" name="horseId" value={h.horseId} defaultChecked={h.selected} /></td>
                  <td className="table-cell"><span className="table-cell-text">{h.recruitmentYear}</span></td>
                  <td className="table-cell"><span className="table-cell-text">{String(h.recruitmentNo ?? '').padStart(3, '0')}</span></td>
                  <td className="table-cell"><span className="table-cell-text">{h.recruitmentName}</span></td>
                  <td className="table-cell"><span className="table-cell-text">{h.horseName}</span></td>
                  <td className="table-cell"><span className="table-cell-text">{h.sharesTotal}</span></td>
                </tr>
              ))}
            </tbody>
          </table>

          <div className="mt-4 flex gap-3">
            <button className="btn-primary" type="submit">選択を保存</button>
            <Link href={`/annual-bundle/${id}`} className="btn-secondary">キャンセル</Link>
          </div>
        </form>

        {totalPages > 1 && (
          <div className="mt-6 flex justify-center gap-2">
            {currentPage > 1 && (
              <Link href={`?page=${currentPage - 1}${search ? `&search=${encodeURIComponent(search)}` : ''}`} className="btn-secondary">前へ</Link>
            )}
            <span className="px-3 py-2 text-secondary">{currentPage} / {totalPages} ページ</span>
            {currentPage < totalPages && (
              <Link href={`?page=${currentPage + 1}${search ? `&search=${encodeURIComponent(search)}` : ''}`} className="btn-secondary">次へ</Link>
            )}
          </div>
        )}
      </div>
    </main>
  );
}
