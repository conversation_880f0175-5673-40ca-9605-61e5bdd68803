'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createAnnualBundle } from '@core-admin/api_clients/annual_bundle_client';
import { AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@hami/core-admin-api-schema/annual_bundle_service_pb';

export default function CreateAnnualBundlePage() {
  const router = useRouter();
  const [fiscalYear, setFiscalYear] = useState<number | ''>('');
  const [name, setName] = useState('');
  const [shares, setShares] = useState<number | ''>('');
  const [publishStatus, setPublishStatus] = useState<AnnualBundlePublishStatus>(AnnualBundlePublishStatus.AB_PRIVATE);
  const [recruitmentStatus, setRecruitmentStatus] = useState<AnnualBundleRecruitmentStatus>(AnnualBundleRecruitmentStatus.AB_UPCOMING);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (fiscalYear === '' || shares === '' || !name) return;
    setIsLoading(true);
    try {
      const res = await createAnnualBundle({
        fiscalYear: fiscalYear as number,
        name,
        shares: shares as number,
        publishStatus,
        recruitmentStatus,
        horseIds: [],
      });
      router.replace(`/annual-bundle/${res.annualBundleId}`);
    } catch (_e) {
      alert('作成に失敗しました');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main className="max-w-3xl mx-auto p-6">
      <div className="bg-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">一括出資パッケージ作成</h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
          <div className='flex flex-col justify-between gap-2'>
            <label className="label">年度</label>
            <input className="input-base w-40" type="number" value={fiscalYear} onChange={(e) => setFiscalYear(e.target.value ? parseInt(e.target.value) : '')} required />
          </div>
          <div className='flex flex-col justify-between gap-2'>
            <label className="label">パッケージ名</label>
            <input className="input-base w-full" value={name} onChange={(e) => setName(e.target.value)} required />
          </div>
          <div className='flex flex-col justify-between gap-2'>
            <label className="label">口数</label>
            <input className="input-base w-40" type="number" value={shares} onChange={(e) => setShares(e.target.value ? parseInt(e.target.value) : '')} required />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="label">公開ステータス</label>
              <select className="input-base w-40" value={publishStatus} onChange={(e) => setPublishStatus(parseInt(e.target.value) as AnnualBundlePublishStatus)}>
                <option value={AnnualBundlePublishStatus.AB_PRIVATE}>非公開</option>
                <option value={AnnualBundlePublishStatus.AB_PUBLIC}>公開</option>
              </select>
            </div>
            <div>
              <label className="label">募集ステータス</label>
              <select className="input-base w-40" value={recruitmentStatus} onChange={(e) => setRecruitmentStatus(parseInt(e.target.value) as AnnualBundleRecruitmentStatus)}>
                <option value={AnnualBundleRecruitmentStatus.AB_UPCOMING}>募集前</option>
                <option value={AnnualBundleRecruitmentStatus.AB_ACTIVE}>募集中</option>
                <option value={AnnualBundleRecruitmentStatus.AB_CLOSED}>募集終了</option>
                <option value={AnnualBundleRecruitmentStatus.AB_FULL}>満口</option>
              </select>
            </div>
          </div>
        </div>
        <div className="flex gap-3">
          <button className="btn-primary" type="submit" disabled={isLoading}>{isLoading ? '作成中...' : '作成する'}</button>
          <button className="btn-secondary" type="button" onClick={() => router.back()} disabled={isLoading}>キャンセル</button>
        </div>
      </form>
    </main>
  );
}
