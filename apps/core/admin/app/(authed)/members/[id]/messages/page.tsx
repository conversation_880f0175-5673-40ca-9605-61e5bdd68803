'use client';

import { useState, useEffect, useCallback } from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Member } from '@hami/core-admin-api-schema/member_service_pb';
import { MemberMessage, MessageType, RecipientStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { getMember } from '@core-admin/api_clients/member_client';
import { listMemberMessages } from '@core-admin/api_clients/admin_message_client';
import { MemberMessageList } from '@core-admin/components/messages/MemberMessageList';

export default function MemberMessagesPage({ params }: { params: Promise<{ id: string }> }) {
  const [memberId, setMemberId] = useState<number>(0);
  const [member, setMember] = useState<Member | null>(null);
  const [messages, setMessages] = useState<MemberMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // フィルター状態
  const [messageTypeFilter, setMessageTypeFilter] = useState<MessageType | undefined>();
  const [statusFilter, setStatusFilter] = useState<RecipientStatus | undefined>();

  // ページネーション状態
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 20;

  // ソート状態
  const [sortBy, setSortBy] = useState('sentAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // パラメータの解決
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      const id = Number(resolvedParams.id);
      if (isNaN(id)) {
        notFound();
        return;
      }
      setMemberId(id);
    };
    resolveParams();
  }, [params]);

  const fetchMember = useCallback(async () => {
    if (!memberId) return;

    try {
      const result = await getMember({ memberId });
      if (!result.member) {
        notFound();
        return;
      }
      setMember(result.member);
    } catch (err) {
      setError('会員情報の取得中にエラーが発生しました。');
      console.error('Failed to fetch member:', err);
    }
  }, [memberId]);

  const fetchMemberMessages = useCallback(async () => {
    if (!memberId) return;

    setLoading(true);
    setError(null);

    try {
      const result = await listMemberMessages({
        memberId,
        messageType: messageTypeFilter,
        status: statusFilter,
        page: currentPage,
        pageSize,
        sortBy,
        sortOrder,
      });

      setMessages(result.messages);
      setTotalCount(result.totalCount);
      setTotalPages(result.totalPages);
    } catch (err) {
      setError('メッセージの取得中にエラーが発生しました。');
      console.error('Failed to fetch member messages:', err);
    } finally {
      setLoading(false);
    }
  }, [memberId, messageTypeFilter, statusFilter, currentPage, sortBy, sortOrder]);

  useEffect(() => {
    if (memberId) {
      fetchMember();
      fetchMemberMessages();
    }
  }, [memberId, fetchMember, fetchMemberMessages]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setCurrentPage(1);
  };

  const handleMessageTypeFilterChange = (messageType?: MessageType) => {
    setMessageTypeFilter(messageType);
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (status?: RecipientStatus) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  if (!memberId) {
    return <div>Loading...</div>;
  }

  if (error && !member) {
    return (
      <main className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <div className="text-red-600 font-semibold mb-4">{error}</div>
            <Link
              href="/members"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              会員一覧に戻る
            </Link>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* ヘッダー */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-2xl font-bold text-gray-900">会員別メッセージ</h1>
            <div className="flex gap-4">
              <Link href="/members" className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                会員一覧
              </Link>
              <Link href="/messages" className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                メッセージ一覧
              </Link>
              <Link href="/" className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                ダッシュボード
              </Link>
            </div>
          </div>

          <nav className="text-sm text-gray-500">
            <Link href="/" className="hover:text-gray-700">
              ダッシュボード
            </Link>
            <span className="mx-2">/</span>
            <Link href="/members" className="hover:text-gray-700">
              会員一覧
            </Link>
            <span className="mx-2">/</span>
            <Link href={`/members/${memberId}`} className="hover:text-gray-700">
              会員詳細
            </Link>
            <span className="mx-2">/</span>
            <span className="text-gray-900">メッセージ</span>
          </nav>
        </div>

        {/* 会員情報 */}
        {member && (
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">会員情報</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <dt className="text-sm font-medium text-gray-500">氏名</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {member.lastName} {member.firstName}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">会員番号</dt>
                <dd className="mt-1 text-sm text-gray-900">{member.memberNumber}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">メールアドレス</dt>
                <dd className="mt-1 text-sm text-gray-900">{member.email}</dd>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <Link href={`/members/${memberId}`} className="text-blue-600 hover:text-blue-900 text-sm">
                会員詳細を表示 →
              </Link>
            </div>
          </div>
        )}

        {/* メッセージ一覧 */}
        <MemberMessageList
          messages={messages}
          loading={loading}
          error={error}
          currentPage={currentPage}
          totalPages={totalPages}
          totalCount={totalCount}
          onPageChange={handlePageChange}
          messageTypeFilter={messageTypeFilter}
          statusFilter={statusFilter}
          onMessageTypeFilterChange={handleMessageTypeFilterChange}
          onStatusFilterChange={handleStatusFilterChange}
          sortBy={sortBy}
          sortOrder={sortOrder}
          onSortChange={handleSortChange}
        />

        {/* フッター */}
        <div className="mt-6 flex justify-between">
          <Link
            href={`/members/${memberId}`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            ← 会員詳細に戻る
          </Link>
          <Link
            href="/members"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
          >
            会員一覧に戻る
          </Link>
        </div>
      </div>
    </main>
  );
}
