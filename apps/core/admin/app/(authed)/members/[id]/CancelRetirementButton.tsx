'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { cancelRetirement } from '@core-admin/api_clients/member_client';

interface CancelRetirementButtonProps {
  memberId: number;
}

export function CancelRetirementButton({ memberId }: CancelRetirementButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleCancelRetirement = async () => {
    if (!confirm('退会予定をキャンセルしますか？')) {
      return;
    }

    setIsLoading(true);
    try {
      await cancelRetirement({ memberId });
      alert('退会予定をキャンセルしました');
      router.refresh(); // ページをリロードして最新の状態を表示
    } catch (_e) {
      alert('退会キャンセルに失敗しました');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="border p-4 rounded shadow-md bg-yellow-50 border-yellow-200">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-yellow-800">退会予定キャンセル</h3>
          <p className="text-sm text-yellow-700">この会員は退会予定です。退会予定をキャンセルする場合は下のボタンをクリックしてください。</p>
        </div>
        <button
          onClick={handleCancelRetirement}
          disabled={isLoading}
          className="px-6 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isLoading ? '処理中...' : '退会予定をキャンセル'}
        </button>
      </div>
    </div>
  );
} 