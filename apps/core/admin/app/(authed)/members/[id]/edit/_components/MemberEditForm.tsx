'use client';

import { useState, useEffect } from 'react';
import type { Member } from '@hami/core-admin-api-schema/member_service_pb';

export interface MemberEditFormData {
  postalCode: string;
  prefecture: string;
  address: string;
  apartment: string;
  phoneNumber: string;
}

interface MemberEditFormProps {
  member: Member;
  onSubmit: (data: MemberEditFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export default function MemberEditForm({ member, onSubmit, onCancel, isLoading = false }: MemberEditFormProps) {
  const [formData, setFormData] = useState<MemberEditFormData>({
    postalCode: member.postalCode,
    prefecture: member.prefecture,
    address: member.address,
    apartment: member.apartment || '',
    phoneNumber: member.phoneNumber,
  });

  const [errors, setErrors] = useState<Partial<Record<keyof MemberEditFormData, string>>>({});

  useEffect(() => {
    setFormData({
      postalCode: member.postalCode,
      prefecture: member.prefecture,
      address: member.address,
      apartment: member.apartment || '',
      phoneNumber: member.phoneNumber,
    });
  }, [member]);

  const handleInputChange = (field: keyof MemberEditFormData, value: string | number | boolean) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    // エラーをクリア
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof MemberEditFormData, string>> = {};

    // 必須フィールドのバリデーション
    if (!formData.postalCode.trim()) {
      newErrors.postalCode = '郵便番号は必須です';
    }
    if (!formData.prefecture.trim()) {
      newErrors.prefecture = '都道府県は必須です';
    }
    if (!formData.address.trim()) {
      newErrors.address = '住所は必須です';
    }
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = '電話番号は必須です';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 基本情報セクション */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">基本情報</h3>

        {/* 郵便番号 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            郵便番号 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.postalCode}
            onChange={(e) => handleInputChange('postalCode', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.postalCode ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="例: 100-0001"
            disabled={isLoading}
          />
          {errors.postalCode && <p className="mt-1 text-sm text-red-600">{errors.postalCode}</p>}
        </div>

        {/* 都道府県 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            都道府県 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.prefecture}
            onChange={(e) => handleInputChange('prefecture', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.prefecture ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="例: 東京都"
            disabled={isLoading}
          />
          {errors.prefecture && <p className="mt-1 text-sm text-red-600">{errors.prefecture}</p>}
        </div>

        {/* 住所 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            住所 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.address ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="例: 千代田区千代田1-1"
            disabled={isLoading}
          />
          {errors.address && <p className="mt-1 text-sm text-red-600">{errors.address}</p>}
        </div>

        {/* 建物名・部屋番号 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">建物名・部屋番号</label>
          <input
            type="text"
            value={formData.apartment}
            onChange={(e) => handleInputChange('apartment', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="例: ○○マンション101号室"
            disabled={isLoading}
          />
        </div>

        {/* 電話番号 */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            電話番号 <span className="text-red-500">*</span>
          </label>
          <input
            type="tel"
            value={formData.phoneNumber}
            onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.phoneNumber ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="例: 03-1234-5678"
            disabled={isLoading}
          />
          {errors.phoneNumber && <p className="mt-1 text-sm text-red-600">{errors.phoneNumber}</p>}
        </div>
      </div>

      {/* ボタン */}
      <div className="flex gap-4 justify-end">
        <button
          type="button"
          onClick={onCancel}
          className="btn-ghost"
          disabled={isLoading}
        >
          キャンセル
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? '保存中...' : '保存'}
        </button>
      </div>
    </form>
  );
}
