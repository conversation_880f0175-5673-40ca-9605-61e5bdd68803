'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getMember, updateMember } from '@core-admin/api_clients/member_client';
import MemberEditForm, { type MemberEditFormData } from './_components/MemberEditForm';
import type { Member } from '@hami/core-admin-api-schema/member_service_pb';
import Link from 'next/link';

export default function MemberEditPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [resolvedParams, setResolvedParams] = useState<{ id: string } | null>(null);
  const [member, setMember] = useState<Member | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // paramsを解決
  useEffect(() => {
    params.then(setResolvedParams);
  }, [params]);

  // 会員データを取得
  useEffect(() => {
    if (!resolvedParams) return;

    const id = Number(resolvedParams.id);
    if (isNaN(id)) {
      setError('無効な会員IDです');
      setIsLoading(false);
      return;
    }

    const fetchMember = async () => {
      try {
        setIsLoading(true);
        const response = await getMember({ memberId: id });
        if (response.member) {
          setMember(response.member);
        } else {
          setError('会員が見つかりません');
        }
      } catch (err) {
        console.error('Error fetching member:', err);
        setError('会員情報の取得に失敗しました');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMember();
  }, [resolvedParams]);

  const handleSubmit = async (formData: MemberEditFormData) => {
    if (!member) return;

    setIsSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      // 空文字列をundefinedに変換（optionalフィールドとして扱う）
      const updateData = {
        memberId: member.memberId,
        postalCode: formData.postalCode || undefined,
        prefecture: formData.prefecture || undefined,
        address: formData.address || undefined,
        apartment: formData.apartment || undefined,
        phoneNumber: formData.phoneNumber || undefined,
      };

      const response = await updateMember(updateData);

      if (response.member) {
        setMember(response.member);
        setSuccess(true);
        // 即座に詳細ページにリダイレクト
        router.push(`/members/${member.memberId}`);
      }
    } catch (err) {
      console.error('Error updating member:', err);
      setError('会員情報の更新に失敗しました');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (member) {
      router.push(`/members/${member.memberId}`);
    } else {
      router.push('/members');
    }
  };

  if (isLoading) {
    return (
      <main className="max-w-4xl mx-auto p-6">
        <div className="bg-gray-200 text-center py-4 mb-6">
          <h1 className="text-xl font-bold">会員情報編集</h1>
        </div>
        <div className="text-center py-8">
          <p>読み込み中...</p>
        </div>
      </main>
    );
  }

  if (error && !member) {
    return (
      <main className="max-w-4xl mx-auto p-6">
        <div className="bg-gray-200 text-center py-4 mb-6">
          <h1 className="text-xl font-bold">会員情報編集</h1>
        </div>
        <div className="text-center py-8">
          <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded mb-4">{error}</div>
          <Link href="/members" className="text-blue-600 underline">
            会員一覧に戻る
          </Link>
        </div>
      </main>
    );
  }

  if (!member) {
    return (
      <main className="max-w-4xl mx-auto p-6">
        <div className="bg-surface-elevated text-center py-6 mb-6">
          <h1 className="text-xl font-bold text-primary">会員情報編集</h1>
        </div>
        <div className="text-center py-8">
          <p>会員が見つかりません</p>
          <Link href="/members" className="text-blue-600 underline">
            会員一覧に戻る
          </Link>
        </div>
      </main>
    );
  }

  return (
    <main className="max-w-4xl mx-auto p-6">
      <div className="bg-gray-200 text-center py-4 mb-6">
        <h1 className="text-xl font-bold">会員情報編集</h1>
      </div>

      {/* 会員基本情報表示 */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-lg font-semibold mb-4 text-gray-800">編集対象会員</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-600">会員ID:</span> {member.memberId}
          </div>
          <div>
            <span className="font-medium text-gray-600">会員番号:</span> {member.memberNumber}
          </div>
          <div>
            <span className="font-medium text-gray-600">氏名:</span> {member.lastName} {member.firstName}
          </div>
          <div>
            <span className="font-medium text-gray-600">メールアドレス:</span> {member.email}
          </div>
        </div>
      </div>

      {/* 成功メッセージ */}
      {success && (
        <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded">会員情報を正常に更新しました。</div>
      )}

      {/* エラーメッセージ */}
      {error && <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">{error}</div>}

      {/* 編集フォーム */}
      <MemberEditForm member={member} onSubmit={handleSubmit} onCancel={handleCancel} isLoading={isSubmitting} />
    </main>
  );
}
