'use client';

import { login } from '@core-admin/api_clients/admin_user_client';
import { redirect } from 'next/navigation';

export default function Login() {
  const submitHandler = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    const res = await login({
      email,
      password,
    });

    if (res.sessionToken) {
      if (typeof window !== 'undefined') {
        const TWENTY_YEARS = 60 * 60 * 24 * 365 * 20;
        document.cookie = `sessionToken=${res.sessionToken}; path=/; max-age=${TWENTY_YEARS}`;
      }
      redirect('/');
    }
  };

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-6 md:p-24 bg-gradient-to-b from-shark-50 to-shark-100">
      <div className="w-full max-w-lg mx-auto bg-surface-card rounded-xl shadow-md p-8 border border-stroke-border min-w-[400px]">
        <h1 className="text-2xl font-bold text-primary mb-6 text-center">管理者ログイン</h1>
        <form onSubmit={submitHandler} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-primary mb-2">
              メールアドレス
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              className="w-full px-3 py-2 border border-hami-stroke-border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-hami-bush-800 focus:border-hami-bush-800 bg-hami-surface-elevated text-hami-glyph-base"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-primary mb-2">
              パスワード
            </label>
            <input
              type="password"
              id="password"
              name="password"
              required
              className="w-full px-3 py-2 border border-hami-stroke-border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-hami-bush-800 focus:border-hami-bush-800 bg-hami-surface-elevated text-hami-glyph-base"
              placeholder="パスワードを入力"
            />
          </div>
          <button
            type="submit"
            className="w-full py-2 px-4 bg-bush-950 text-bush-50 border border-bush-950 rounded-sm font-semibold cursor-pointer transition duration-200 hover:bg-bush-900"
          >
            ログイン
          </button>
        </form>
      </div>
    </main>
  );
}
