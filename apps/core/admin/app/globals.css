@import 'tailwindcss';

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Surface Colors - White Palette */
  --color-hami-white-50: #ffffff;
  --color-hami-white-100: #efefef;
  --color-hami-white-200: #dcdcdc;
  --color-hami-white-300: #bdbdbd;
  --color-hami-white-400: #989898;
  --color-hami-white-500: #7c7c7c;
  --color-hami-white-600: #656565;
  --color-hami-white-700: #525252;
  --color-hami-white-800: #464646;
  --color-hami-white-900: #3d3d3d;
  --color-hami-white-950: #292929;

  /* Surface Colors */
  --color-hami-surface-base: var(--color-hami-white-100);
  --color-hami-surface-elevated: var(--color-hami-white-50);
  --color-hami-surface-card: var(--color-hami-white-50);
  --color-hami-surface-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors - Shark Palette */
  --color-hami-shark-50: #f6f5f5;
  --color-hami-shark-100: #e7e6e6;
  --color-hami-shark-200: #d1d0d0;
  --color-hami-shark-300: #b1afb0;
  --color-hami-shark-400: #8a8687;
  --color-hami-shark-500: #6f6b6b;
  --color-hami-shark-600: #5f5b5b;
  --color-hami-shark-700: #504e4f;
  --color-hami-shark-800: #464445;
  --color-hami-shark-900: #3d3c3c;
  --color-hami-shark-950: #201f1f;

  --color-hami-glyph-base: #201f1f;
  --color-hami-glyph-base-1: var(--color-hami-shark-900);
  --color-hami-glyph-subtle: var(--color-hami-shark-500);
  --color-hami-glyph-modest: #b1afb0;
  --color-hami-glyph-disabled: var(--color-hami-shark-300);

  /* Border & Stroke Colors */
  --color-hami-stroke-border: #e7e6e6;
  --color-hami-stroke-separator: var(--color-hami-shark-200);
  --color-hami-stroke-border-alt: var(--color-hami-shark-200);
  --color-hami-stroke-focus: var(--color-hami-bush-800);

  /* Brand Colors - Bush Palette (Green) */
  --color-hami-bush-50: #f2fbf5;
  --color-hami-bush-100: #e1f7ea;
  --color-hami-bush-200: #c4eed5;
  --color-hami-bush-300: #96dfb3;
  --color-hami-bush-400: #61c78a;
  --color-hami-bush-500: #3bac6a;
  --color-hami-bush-600: #2c8d54;
  --color-hami-bush-700: #266f44;
  --color-hami-bush-800: #225939;
  --color-hami-bush-900: #1e4931;
  --color-hami-bush-950: #0e311e;

  /* Accent Colors - Goldenrod Palette (Yellow/Orange) */
  --color-hami-goldenrod-50: #fffbeb;
  --color-hami-goldenrod-100: #fdf2c8;
  --color-hami-goldenrod-200: #fae58d;
  --color-hami-goldenrod-300: #f9d96d;
  --color-hami-goldenrod-400: #f6be29;
  --color-hami-goldenrod-500: #ef9e11;
  --color-hami-goldenrod-600: #d4780b;
  --color-hami-goldenrod-700: #b0550d;
  --color-hami-goldenrod-800: #8f4211;
  --color-hami-goldenrod-900: #763711;
  --color-hami-goldenrod-950: #431a05;

  /* Shiraz Palette (ブランドカラー - 赤系) */
  --color-hami-shiraz-50: #fef3f2;
  --color-hami-shiraz-100: #fee6e5;
  --color-hami-shiraz-200: #fccfcf;
  --color-hami-shiraz-300: #f9a9a8;
  --color-hami-shiraz-400: #f4787a;
  --color-hami-shiraz-500: #eb484f;
  --color-hami-shiraz-600: #d72736;
  --color-hami-shiraz-700: #b61a2b;
  --color-hami-shiraz-800: #a51b2f;
  --color-hami-shiraz-900: #82192b;
  --color-hami-shiraz-950: #490812;

  /* Status Colors */
  --color-hami-success: #10b981;
  --color-hami-warning: var(--color-hami-goldenrod-500);
  --color-hami-error: var(--color-hami-shiraz-600);
  --color-hami-info: #3b82f6;

  /* Fonts */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 数値入力フィールドのスピンボタンを非表示 */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

/* ========================================
   HAMI Design System Utility Classes
   ======================================== */

/* ===== Button Components ===== */
.btn-primary {
  background-color: var(--color-hami-bush-950);
  color: var(--color-hami-bush-50);
  border: 1px solid var(--color-hami-bush-950);
  border-radius: 2px;
  padding: 8px 22px;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-primary:hover {
  background-color: var(--color-hami-bush-950);
  border-color: var(--color-hami-bush-950);
  opacity: 0.2;
}

.btn-primary:focus {
  outline: 2px solid var(--color-hami-bush-800);
  outline-offset: 2px;
}

.btn-primary:disabled {
  background-color: var(--color-hami-bush-950);
  border-color: var(--color-hami-bush-950);
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--color-hami-shark-100);
  color: var(--color-hami-glyph-base);
  border: 1px solid var(--color-hami-stroke-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-secondary:hover {
  background-color: var(--color-hami-shark-200);
  border-color: var(--color-hami-shark-200);
}

.btn-secondary:focus {
  outline: 2px solid var(--color-hami-stroke-focus);
  outline-offset: 2px;
}

.btn-danger {
  background-color: var(--color-hami-error);
  color: white;
  border: 1px solid var(--color-hami-error);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-danger:hover {
  background-color: var(--color-hami-shiraz-700);
  border-color: var(--color-hami-shiraz-700);
}

.btn-danger:focus {
  outline: 2px solid var(--color-hami-error);
  outline-offset: 2px;
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-hami-glyph-base);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-ghost:hover {
  background-color: var(--color-hami-white-100);
  border-color: var(--color-hami-shark-100);
}

.btn-ghost:focus {
  outline: 2px solid var(--color-hami-stroke-focus);
  outline-offset: 2px;
}

/* distruction button (破壊的アクション用ボタン) */
.btn-destruction {
  background-color: #fff;
  color: var(--color-hami-shiraz-800);
  border: 1px solid var(--color-hami-shiraz-800);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 600;
  font-size: 18px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-destruction:hover {
  background-color: #fff;
  border-color: var(--color-hami-shiraz-800);
  color: var(--color-hami-shiraz-400);
}

.btn-destruction:focus {
  outline: 2px solid var(--color-hami-shiraz-800);
  outline-offset: 2px;
}

.btn-destruction:disabled,
.btn-destruction[aria-disabled='true'] {
  background-color: var(--color-hami-white-100);
  border-color: var(--color-hami-shiraz-200);
  color: var(--color-hami-shiraz-300);
  opacity: 0.6;
  cursor: not-allowed;
}

/* ===== Text Utilities ===== */
.text-primary {
  color: var(--color-hami-glyph-base);
}

.text-secondary {
  color: var(--color-hami-glyph-subtle);
}

.text-muted {
  color: var(--color-hami-glyph-modest);
}

.text-disabled {
  color: var(--color-hami-glyph-disabled);
}

.text-success {
  color: var(--color-hami-success);
}

.text-warning {
  color: var(--color-hami-warning);
}

.text-error {
  color: var(--color-hami-error);
}

.text-info {
  color: var(--color-hami-info);
}

/* ===== Background Utilities ===== */
.bg-surface {
  background-color: var(--color-hami-surface-base);
}

.bg-surface-elevated {
  background-color: var(--color-hami-surface-elevated);
}

.bg-surface-card {
  background-color: var(--color-hami-surface-card);
  border: 1px solid var(--color-hami-stroke-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.bg-brand {
  background-color: var(--color-hami-bush-950);
}

.bg-success {
  background-color: var(--color-hami-success);
}

.bg-warning {
  background-color: var(--color-hami-warning);
}

.bg-error {
  background-color: var(--color-hami-error);
}

.bg-info {
  background-color: var(--color-hami-info);
}

/* ===== Border Utilities ===== */
.border-primary {
  border-color: var(--color-hami-stroke-border);
}

.border-secondary {
  border-color: var(--color-hami-stroke-separator);
}

.border-focus {
  border-color: var(--color-hami-stroke-focus);
}

.border-success {
  border-color: var(--color-hami-success);
}

.border-warning {
  border-color: var(--color-hami-warning);
}

.border-error {
  border-color: var(--color-hami-error);
}

/* ===== Form Components ===== */
.input-base {
  background-color: var(--color-hami-surface-elevated);
  border: 1px solid var(--color-hami-stroke-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--color-hami-glyph-base);
  font-size: 1rem;
  transition: all 0.2s ease;
}

.input-base:focus {
  outline: none;
  border-color: var(--color-hami-stroke-focus);
  box-shadow: 0 0 0 3px rgba(165, 27, 47, 0.1);
}

.input-base:disabled {
  background-color: var(--color-hami-surface-base);
  color: var(--color-hami-glyph-disabled);
  cursor: not-allowed;
}

.input-error {
  border-color: var(--color-hami-error);
}

.input-error:focus {
  border-color: var(--color-hami-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* ===== TextSearch Component ===== */
.text-search {
  background-color: #ffffff;
  border: 1px solid var(--color-hami-stroke-border);
  border-radius: 8px;
  padding: 0 12px;
  height: 48px;
  color: var(--color-hami-glyph-base);
  font-size: 1rem;
  transition: all 0.2s ease;
  width: 125px;
}

.text-search:focus {
  outline: none;
  border-color: var(--color-hami-stroke-focus);
  box-shadow: 0 0 0 3px rgba(14, 49, 30, 0.1);
}

.text-search::placeholder {
  color: var(--color-hami-glyph-modest);
}

.text-search:disabled {
  background-color: var(--color-hami-surface-base);
  color: var(--color-hami-glyph-disabled);
  cursor: not-allowed;
}

/* ===== Background Color Utilities ===== */
.bg-shark-100 {
  background-color: var(--color-hami-shark-100);
}

.bg-shark-200 {
  background-color: var(--color-hami-shark-200);
}

/* ===== Table Header Styles ===== */
.table-header {
  background-color: var(--color-hami-shark-100);
  border-bottom: 1px solid var(--color-hami-stroke-separator);
}

.table-header th {
  padding: 16px 12px;
  text-align: left;
  font-family: var(--font-noto-sans-jp), sans-serif;
  font-size: 14px;
  font-weight: 700;
  line-height: 140%;
  letter-spacing: 0%;
  color: var(--color-hami-glyph-subtle);
  border-bottom: 1px solid var(--color-hami-stroke-separator);
  border-right: 1px solid var(--color-hami-stroke-separator);
}

.table-header th:last-child {
  border-right: none;
}

/* ===== Table Cell Styles ===== */
.table-cell {
  background-color: var(--color-hami-white-50);
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--color-hami-stroke-separator);
  border-right: 1px solid var(--color-hami-stroke-separator);
  vertical-align: top;
}

.table-cell:last-child {
  border-right: none;
}

/* テーブルセルでbg-shark-100が適用されるように優先度を上げる */
.table-cell.bg-shark-100 {
  background-color: var(--color-hami-shark-100) !important;
}

.table-cell-text {
  font-family: var(--font-noto-sans-jp), sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  letter-spacing: 0%;
  color: var(--color-hami-glyph-base);
}

/* テーブルセルテキストでtext-rightが適用されるように優先度を上げる */
.table-cell-text.text-right {
  text-align: right !important;
}

/* テーブルセルでtext-rightが適用されるように優先度を上げる */
.table-cell.text-right {
  text-align: right !important;
}

/* テーブルヘッダーでtext-rightが適用されるように優先度を上げる */
.table-header th.text-right {
  text-align: right !important;
}

/* ===== Table Row Styles ===== */
.table-row {
  border-bottom: 1px solid var(--color-hami-stroke-separator);
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: var(--color-hami-shark-50);
}

/* ===== Layout Components ===== */
.card {
  background-color: var(--color-hami-surface-card);
  border: 1px solid var(--color-hami-stroke-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.card-elevated {
  background-color: var(--color-hami-surface-card);
  border: 1px solid var(--color-hami-stroke-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

/* ===== Status Indicators ===== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
}

.badge-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--color-hami-success);
}

.badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--color-hami-warning);
}

.badge-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--color-hami-error);
}

.badge-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--color-hami-info);
}

.badge-neutral {
  background-color: var(--color-hami-shark-100);
  color: var(--color-hami-shark-700);
}

/* ===== Interactive States ===== */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.focus-ring {
  outline: 2px solid var(--color-hami-stroke-focus);
  outline-offset: 2px;
}

/* ===== Responsive Utilities ===== */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .btn-primary,
  .btn-secondary,
  .btn-danger,
  .btn-ghost {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
  }
}
