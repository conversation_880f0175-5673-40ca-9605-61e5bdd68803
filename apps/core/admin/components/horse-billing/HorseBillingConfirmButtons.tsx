type HorseBillingConfirmButtonsProps = {
  isSubmitting: boolean;
  onBack: () => void;
  onSubmit: () => void;
  backButtonText: string;
  submitButtonText: string;
  submitButtonVariant?: 'primary' | 'danger';
};

export default function HorseBillingConfirmButtons({
  isSubmitting,
  onBack,
  onSubmit,
  backButtonText,
  submitButtonText,
  submitButtonVariant = 'primary',
}: HorseBillingConfirmButtonsProps) {
  const submitButtonClass = submitButtonVariant === 'danger' ? 'btn-danger' : 'btn-primary';

  return (
    <div className="flex justify-center gap-4">
      <button 
        onClick={onBack} 
        disabled={isSubmitting} 
        className="btn-secondary disabled:bg-surface disabled:text-muted disabled:cursor-not-allowed"
      >
        {backButtonText}
      </button>
      <button 
        onClick={onSubmit} 
        disabled={isSubmitting} 
        className={`${submitButtonClass} disabled:bg-surface disabled:text-muted disabled:cursor-not-allowed`}
      >
        {isSubmitting ? `${submitButtonText}中...` : submitButtonText}
      </button>
    </div>
  );
}
