import Link from 'next/link';
import { type HorseBillingItem } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { formatDate, getItemTypeString } from '@core-admin/utils/view_helpers';
import { calculateExpenditureAmount, formatCurrency } from '@core-admin/utils/income_utils';

type HorseBillingListTableProps = {
  billings: HorseBillingItem[];
  billerMap: Map<number, string>;
  horseId: number;
};

export default function HorseBillingListTable({ billings, billerMap, horseId }: HorseBillingListTableProps) {
  return (
    <div className="bg-surface-card border border-stroke-separator overflow-hidden">
      <table className="w-full">
        <thead>
          <tr className="table-header">
            <th>発生日</th>
            <th>項目</th>
            <th>請求元</th>
            <th>請求金額（円）</th>
            <th>補助金額（円）</th>
            <th>支出金額（円）</th>
            <th>締め</th>
            <th>詳細</th>
          </tr>
        </thead>
        <tbody>
          {billings.length === 0 ? (
            <tr className="table-row">
              <td colSpan={8} className="table-cell text-center">
                <span className="table-cell-text text-muted">データがありません。</span>
              </td>
            </tr>
          ) : (
            billings.map((billing) => {
              const expenditure = calculateExpenditureAmount(billing.billingAmount, billing.subsidyAmount);
              const isClosed = billing.closing;
              return (
                <tr key={billing.id} className="table-row">
                  <td className="table-cell">
                    <span className="table-cell-text">{formatDate(billing.occurredYear, billing.occurredMonth, billing.occurredDay)}</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{getItemTypeString(billing.itemType)}</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{billerMap.get(billing.billerId) || '不明'}</span>
                  </td>
                  <td className="table-cell text-right">
                    <span className="table-cell-text text-right">{formatCurrency(billing.billingAmount)}</span>
                  </td>
                  <td className="table-cell text-right">
                    <span className="table-cell-text text-right">{formatCurrency(billing.subsidyAmount)}</span>
                  </td>
                  <td className="table-cell text-right">
                    <span className="table-cell-text text-right">{formatCurrency(expenditure)}</span>
                  </td>
                  <td className="table-cell">
                    <span className="table-cell-text">{isClosed ? '済' : '未'}</span>
                  </td>
                  <td className="table-cell text-center">
                    <Link href={`/horses/${horseId}/income-and-billing/billing/${billing.id}`}>
                      <button className="btn-secondary text-sm">詳細</button>
                    </Link>
                  </td>
                </tr>
              );
            })
          )}
        </tbody>
      </table>
    </div>
  );
}
