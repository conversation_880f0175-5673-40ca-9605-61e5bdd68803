import { HorseBillingItemType } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { getItemTypeString, formatDate } from '@core-admin/utils/view_helpers';
import { calculateExpenditureAmount } from '@core-admin/utils/income_utils';

type HorseBillingConfirmTableProps = {
  formData: {
    billingYearMonth: string;
    occurredYear: string;
    occurredMonth: string;
    occurredDay: string;
    billerId: string;
    itemType: string | number;
    itemTypeOther: string;
    billingAmount: string;
    taxAmount: string;
    subsidyAmount: string;
    note: string;
  };
  billerName: string;
  showNote?: boolean;
};

export default function HorseBillingConfirmTable({ formData, billerName, showNote = true }: HorseBillingConfirmTableProps) {
  const _expenditureAmount = calculateExpenditureAmount(
    parseInt(formData.billingAmount, 10) || 0,
    parseInt(formData.subsidyAmount, 10) || 0
  );

  return (
    <div className="bg-surface-card border border-stroke-separator overflow-hidden max-w-4xl mb-8">
      <table className="w-full">
        <tbody>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">請求年月</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{formatDate(Number(String(formData.billingYearMonth).substring(0,4)), Number(String(formData.billingYearMonth).substring(4,6)))}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">発生年月日</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{formatDate(Number(formData.occurredYear), Number(formData.occurredMonth), Number(formData.occurredDay))}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">請求元</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{billerName}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">項目</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">
                {getItemTypeString(Number(formData.itemType))}
                {Number(formData.itemType) === HorseBillingItemType.OTHER && ` (${formData.itemTypeOther})`}
              </span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">請求金額（税込）</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{(parseInt(formData.billingAmount, 10) || 0).toLocaleString()} 円</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">税額</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{(parseInt(formData.taxAmount, 10) || 0).toLocaleString()} 円</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">補助金額</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{(parseInt(formData.subsidyAmount, 10) || 0).toLocaleString()} 円</span>
            </td>
          </tr>
          {showNote && (
            <tr className="table-row">
              <th className="table-header">
                <span className="table-cell-text">備考</span>
              </th>
              <td className="table-cell">
                <span className="table-cell-text whitespace-pre-wrap">{formData.note || 'なし'}</span>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
}
