import Link from 'next/link';

type HorseBillingActionButtonsProps = {
  horseId: number;
  billingId: number;
  isClosed: boolean;
};

export default function HorseBillingActionButtons({ horseId, billingId, isClosed }: HorseBillingActionButtonsProps) {
  return (
    <div className="flex justify-center gap-4">
      <Link href={`/horses/${horseId}/income-and-billing/billing`}>
        <button className="btn-secondary">一覧へ戻る</button>
      </Link>
      <Link href={`/horses/${horseId}/income-and-billing/billing/${billingId}/edit`} className={isClosed ? "pointer-events-none" : ""}>
        <button disabled={isClosed} className="btn-primary disabled:bg-surface disabled:text-muted disabled:cursor-not-allowed">
          編集する
        </button>
      </Link>
      <Link href={`/horses/${horseId}/income-and-billing/billing/${billingId}/delete-confirm`} className={isClosed ? "pointer-events-none" : ""}>
        <button disabled={isClosed} className="btn-danger disabled:bg-surface disabled:text-muted disabled:cursor-not-allowed">
          削除する
        </button>
      </Link>
    </div>
  );
}
