'use client';

import { HorseBillingItemType } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import type { Biller } from '@hami/core-admin-api-schema/biller_service_pb';

export type HorseBillingFormData = {
  billingYearMonth: string;
  occurredYear: string;
  occurredMonth: string;
  occurredDay: string;
  billerId: string;
  itemType: HorseBillingItemType;
  itemTypeOther: string;
  billingAmount: string;
  taxAmount: string;
  subsidyAmount: string;
  note: string;
};

type HorseBillingFormProps = {
  formData: HorseBillingFormData;
  billers: Biller[];
  horseName: string;
  error?: string | null;
  onSubmit: (e: React.FormEvent) => void;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  submitButtonText: string;
  cancelHref: string;
  cancelButtonText: string;
};

export default function HorseBillingForm({
  formData,
  billers,
  horseName,
  error,
  onSubmit,
  onChange,
  submitButtonText,
  cancelHref,
  cancelButtonText,
}: HorseBillingFormProps) {
  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} 支出情報 {submitButtonText}</h1>
        
        {error && (
          <div className="bg-error border border-error px-4 py-3 rounded mb-6">
            <span className="text-error">{error}</span>
          </div>
        )}

        <form onSubmit={onSubmit} className="space-y-8 max-w-4xl">
          {/* 基本情報 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">基本情報</h3>
            <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                <div className="md:col-span-3">
                  <label htmlFor="billingYearMonth" className="block text-sm font-medium text-primary mb-2">請求年月</label>
                  <input 
                    type="text" 
                    name="billingYearMonth" 
                    id="billingYearMonth" 
                    value={formData.billingYearMonth} 
                    onChange={onChange} 
                    placeholder="YYYYMM" 
                    required 
                    className="input-base w-full"
                  />
                </div>
                <div className="md:col-span-1">
                  <label htmlFor="occurredYear" className="block text-sm font-medium text-primary mb-2">発生日（年）</label>
                  <input 
                    type="number" 
                    name="occurredYear" 
                    id="occurredYear" 
                    value={formData.occurredYear} 
                    onChange={onChange} 
                    placeholder="2024" 
                    required 
                    min="1900" 
                    max="2100"
                    className="input-base w-full"
                  />
                </div>
                <div className="md:col-span-1">
                  <label htmlFor="occurredMonth" className="block text-sm font-medium text-primary mb-2">発生日（月）</label>
                  <input 
                    type="number" 
                    name="occurredMonth" 
                    id="occurredMonth" 
                    value={formData.occurredMonth} 
                    onChange={onChange} 
                    placeholder="1" 
                    required 
                    min="1" 
                    max="12"
                    className="input-base w-full"
                  />
                </div>
                <div className="md:col-span-1">
                  <label htmlFor="occurredDay" className="block text-sm font-medium text-primary mb-2">発生日（日）</label>
                  <input 
                    type="number" 
                    name="occurredDay" 
                    id="occurredDay" 
                    value={formData.occurredDay} 
                    onChange={onChange} 
                    placeholder="1" 
                    required 
                    min="1" 
                    max="31"
                    className="input-base w-full"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="billerId" className="block text-sm font-medium text-primary mb-2">請求元</label>
                  <select 
                    name="billerId" 
                    id="billerId" 
                    value={formData.billerId} 
                    onChange={onChange} 
                    required
                    className="input-base w-full"
                  >
                    <option value="">選択してください</option>
                    {billers.map((biller) => (
                      <option key={biller.id} value={biller.id}>
                        {biller.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label htmlFor="itemType" className="block text-sm font-medium text-primary mb-2">項目</label>
                  <select 
                    name="itemType" 
                    id="itemType" 
                    value={formData.itemType} 
                    onChange={onChange} 
                    required
                    className="input-base w-full"
                  >
                    <option value={HorseBillingItemType.UNSPECIFIED}>選択してください</option>
                    <option value={HorseBillingItemType.ENTRUST}>預託料</option>
                    <option value={HorseBillingItemType.INSURANCE}>保険料</option>
                    <option value={HorseBillingItemType.OTHER}>その他</option>
                  </select>
                </div>
              </div>

              {formData.itemType === HorseBillingItemType.OTHER && (
                <div>
                  <label htmlFor="itemTypeOther" className="block text-sm font-medium text-primary mb-2">その他項目名</label>
                  <input 
                    type="text" 
                    name="itemTypeOther" 
                    id="itemTypeOther" 
                    value={formData.itemTypeOther} 
                    onChange={onChange} 
                    placeholder="項目名を入力してください" 
                    required
                    className="input-base w-full"
                  />
                </div>
              )}
            </div>
          </div>

          {/* 金額情報 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">金額情報</h3>
            <div className="bg-surface-card border border-stroke-separator p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="billingAmount" className="block text-sm font-medium text-primary mb-2">請求金額（円）</label>
                  <input 
                    type="number" 
                    name="billingAmount" 
                    id="billingAmount" 
                    value={formData.billingAmount} 
                    onChange={onChange} 
                    placeholder="0" 
                    required 
                    min="0"
                    className="input-base w-full"
                  />
                </div>
                <div>
                  <label htmlFor="taxAmount" className="block text-sm font-medium text-primary mb-2">税額（円）</label>
                  <input 
                    type="number" 
                    name="taxAmount" 
                    id="taxAmount" 
                    value={formData.taxAmount} 
                    onChange={onChange} 
                    placeholder="0" 
                    required 
                    min="0"
                    className="input-base w-full"
                  />
                </div>
                <div>
                  <label htmlFor="subsidyAmount" className="block text-sm font-medium text-primary mb-2">補助金額（円）</label>
                  <input 
                    type="number" 
                    name="subsidyAmount" 
                    id="subsidyAmount" 
                    value={formData.subsidyAmount} 
                    onChange={onChange} 
                    placeholder="0" 
                    required 
                    min="0"
                    className="input-base w-full"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 備考 */}
          <div>
            <h3 className="text-lg font-medium text-primary mb-4">備考</h3>
            <div className="bg-surface-card border border-stroke-separator p-6">
              <label htmlFor="note" className="block text-sm font-medium text-primary mb-2">備考</label>
              <textarea 
                name="note" 
                id="note" 
                value={formData.note} 
                onChange={onChange} 
                placeholder="備考があれば入力してください" 
                rows={4}
                className="input-base w-full"
              />
            </div>
          </div>

          {/* ボタン */}
          <div className="flex justify-center gap-4">
            <a href={cancelHref} className="btn-secondary">
              {cancelButtonText}
            </a>
            <button type="submit" className="btn-primary">
              {submitButtonText}
            </button>
          </div>
        </form>
      </div>
    </main>
  );
}
