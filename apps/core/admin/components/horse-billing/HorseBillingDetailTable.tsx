import { HorseBillingItemType } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { formatDate, getItemTypeString } from '@core-admin/utils/view_helpers';
import { calculateExpenditureAmount, formatCurrencyWithUnit } from '@core-admin/utils/income_utils';

type HorseBillingDetailTableProps = {
  billing: {
    id: number;
    billingYearMonth: number;
    occurredYear: number;
    occurredMonth: number;
    occurredDay: number;
    billerId: number;
    itemType: HorseBillingItemType;
    itemTypeOther: string;
    billingAmount: number;
    taxAmount: number;
    subsidyAmount: number;
    note: string;
    closing: boolean;
  };
  billerName: string;
};

export default function HorseBillingDetailTable({ billing, billerName }: HorseBillingDetailTableProps) {
  const expenditureAmount = calculateExpenditureAmount(billing.billingAmount, billing.subsidyAmount);
  const isClosed = billing.closing;

  return (
    <div className="bg-surface-card border border-stroke-separator overflow-hidden max-w-4xl mb-8">
      <table className="w-full">
        <tbody>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">請求年月</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{formatDate(Number(String(billing.billingYearMonth).substring(0,4)), Number(String(billing.billingYearMonth).substring(4,6)))}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">発生年月日</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{formatDate(billing.occurredYear, billing.occurredMonth, billing.occurredDay)}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">請求元</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{billerName}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">項目</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{getItemTypeString(billing.itemType)}{billing.itemType === HorseBillingItemType.OTHER && ` (${billing.itemTypeOther})`}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">請求金額（税込）</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{formatCurrencyWithUnit(billing.billingAmount)}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">税額</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{formatCurrencyWithUnit(billing.taxAmount)}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">補助金額</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{formatCurrencyWithUnit(billing.subsidyAmount)}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">支出金額</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text font-bold">{formatCurrencyWithUnit(expenditureAmount)}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">備考</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text whitespace-pre-wrap">{billing.note || 'なし'}</span>
            </td>
          </tr>
          <tr className="table-row">
            <th className="table-header">
              <span className="table-cell-text">締め</span>
            </th>
            <td className="table-cell">
              <span className="table-cell-text">{isClosed ? '済' : '未'}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
}
