import { type HorseBillingItem } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { getItemTypeString, formatDate } from '@core-admin/utils/view_helpers';
import { calculateExpenditureAmount } from '@core-admin/utils/income_utils';

type HorseBillingDeleteConfirmProps = {
  billing: HorseBillingItem;
  billerName: string;
  isSubmitting: boolean;
  onCancel: () => void;
  onDelete: () => void;
};

export default function HorseBillingDeleteConfirm({
  billing,
  billerName,
  isSubmitting,
  onCancel,
  onDelete,
}: HorseBillingDeleteConfirmProps) {
  const expenditureAmount = calculateExpenditureAmount(billing.billingAmount, billing.subsidyAmount);

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-error mb-4">支出情報の削除</h1>
        <p className="text-primary mb-8">以下の情報を本当に削除しますか？この操作は元に戻せません。</p>
        
        <div className="bg-surface-card border border-stroke-separator overflow-hidden max-w-4xl mb-8">
          <table className="w-full">
            <tbody>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">請求年月</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{formatDate(Number(String(billing.billingYearMonth).substring(0,4)), Number(String(billing.billingYearMonth).substring(4,6)))}</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">発生年月日</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{formatDate(billing.occurredYear, billing.occurredMonth, billing.occurredDay)}</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">請求元</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{billerName}</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">項目</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">
                    {getItemTypeString(billing.itemType)}
                    {billing.itemTypeOther && ` (${billing.itemTypeOther})`}
                  </span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">請求金額（税込）</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{billing.billingAmount.toLocaleString()} 円</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">税額</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{billing.taxAmount.toLocaleString()} 円</span>
                </td>
              </tr>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">補助金額</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text">{billing.subsidyAmount.toLocaleString()} 円</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <h2 className="text-xl font-bold text-primary mb-4">計算結果</h2>
        <div className="bg-surface-card border border-stroke-separator overflow-hidden max-w-4xl mb-8">
          <table className="w-full">
            <tbody>
              <tr className="table-row">
                <th className="table-header">
                  <span className="table-cell-text">支出金額</span>
                </th>
                <td className="table-cell">
                  <span className="table-cell-text font-bold">{expenditureAmount.toLocaleString()} 円</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div className="flex justify-center gap-4">
          <button 
            onClick={onCancel} 
            disabled={isSubmitting} 
            className="btn-secondary disabled:bg-surface disabled:text-muted disabled:cursor-not-allowed"
          >
            キャンセル
          </button>
          <button 
            onClick={onDelete} 
            disabled={isSubmitting} 
            className="btn-danger disabled:bg-surface disabled:text-muted disabled:cursor-not-allowed"
          >
            {isSubmitting ? '削除中...' : '削除する'}
          </button>
        </div>
      </div>
    </main>
  );
}
