import { BankAccountApprovalHistory, BankAccountApprovalStatus } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { formatDateTime } from '@core-admin/utils/bank_account_utils';

interface ApprovalHistoryProps {
  histories: BankAccountApprovalHistory[];
}

export function ApprovalHistory({ histories }: ApprovalHistoryProps) {
  const getStatusText = (status: BankAccountApprovalStatus) => {
    switch (status) {
      case BankAccountApprovalStatus.PENDING:
        return '申請受付';
      case BankAccountApprovalStatus.APPROVED:
        return '承認';
      case BankAccountApprovalStatus.REJECTED:
        return '却下';
      default:
        return '不明';
    }
  };

  const getStatusColor = (status: BankAccountApprovalStatus) => {
    switch (status) {
      case BankAccountApprovalStatus.PENDING:
        return 'text-yellow-600';
      case BankAccountApprovalStatus.APPROVED:
        return 'text-green-600';
      case BankAccountApprovalStatus.REJECTED:
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: BankAccountApprovalStatus) => {
    switch (status) {
      case BankAccountApprovalStatus.PENDING:
        return <div className="w-3 h-3 bg-yellow-400 rounded-full flex-shrink-0 mt-1"></div>;
      case BankAccountApprovalStatus.APPROVED:
        return <div className="w-3 h-3 bg-green-400 rounded-full flex-shrink-0 mt-1"></div>;
      case BankAccountApprovalStatus.REJECTED:
        return <div className="w-3 h-3 bg-red-400 rounded-full flex-shrink-0 mt-1"></div>;
      default:
        return <div className="w-3 h-3 bg-gray-400 rounded-full flex-shrink-0 mt-1"></div>;
    }
  };

  // 履歴を日時順（新しい順）でソート
  const sortedHistories = [...histories].sort((a, b) => {
    const aTime = a.createdAt?.seconds || 0;
    const bTime = b.createdAt?.seconds || 0;
    return Number(bTime) - Number(aTime);
  });

  return (
    <div className="card">
      <h2 className="text-xl font-bold text-primary mb-6">承認履歴</h2>

      {sortedHistories.length === 0 ? (
        <p className="text-secondary text-center py-8">履歴はありません</p>
      ) : (
        <div className="space-y-4">
          {sortedHistories.map((history, index) => (
            <div key={history.bankAccountApprovalId} className="flex gap-4">
              {/* タイムラインアイコン */}
              <div className="flex flex-col items-center">
                {getStatusIcon(history.approvalStatus)}
                {index < sortedHistories.length - 1 && <div className="w-px h-16 bg-gray-300 mt-2"></div>}
              </div>

              {/* 履歴内容 */}
              <div className="flex-1 pb-6">
                <div className="flex items-center gap-3 mb-2">
                  <span className={`font-medium ${getStatusColor(history.approvalStatus)}`}>{getStatusText(history.approvalStatus)}</span>
                  <span className="text-sm text-secondary">{formatDateTime(history.createdAt)}</span>
                </div>

                {history.adminUserName && <p className="text-sm text-secondary mb-2">処理者: {history.adminUserName}</p>}

                {history.comment && (
                  <div className="bg-gray-50 p-3 rounded border-l-4 border-gray-300">
                    <p className="text-sm text-secondary mb-1">
                      {history.approvalStatus === BankAccountApprovalStatus.APPROVED ? 'コメント' : '却下理由'}:
                    </p>
                    <p className="text-sm">{history.comment}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
