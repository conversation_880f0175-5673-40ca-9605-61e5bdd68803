'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { BankAccountRegistrationItem } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { listPendingBankAccountRegistrations } from '@core-admin/api_clients/bank_account_approval_client';
import { LoadingSpinner } from '@core-admin/components/common/LoadingSpinner';
import { ErrorMessage } from '@core-admin/components/common/ErrorMessage';
import { formatDate, maskAccountNumber } from '@core-admin/utils/bank_account_utils';

interface PendingListProps {
  pageSize?: number;
  onItemClick?: (item: BankAccountRegistrationItem) => void;
}

export function PendingList({ pageSize = 20, onItemClick }: PendingListProps) {
  const [items, setItems] = useState<BankAccountRegistrationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const loadData = useCallback(
    async (page: number) => {
      try {
        setLoading(true);
        setError(null);

        const response = await listPendingBankAccountRegistrations({
          page,
          pageSize,
        });

        setItems(response.items);
        setTotalCount(response.totalCount);
        setCurrentPage(response.page);
      } catch (err) {
        console.error('Failed to load bank account approvals:', err);
        setError('承認待ち口座一覧の取得に失敗しました。');
      } finally {
        setLoading(false);
      }
    },
    [pageSize]
  );

  useEffect(() => {
    loadData(1);
  }, [loadData]);

  const handlePageChange = (page: number) => {
    loadData(page);
  };

  const totalPages = Math.ceil(totalCount / pageSize);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  return (
    <div className="space-y-4">
      {/* 件数表示 */}
      <div className="flex justify-between items-center">
        <p className="text-sm text-secondary">
          全 {totalCount} 件中 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, totalCount)} 件を表示
        </p>
      </div>

      {/* 一覧テーブル */}
      <div className="overflow-x-auto">
        <table className="w-full bg-white rounded-lg shadow">
          <thead>
            <tr className="border-b border-gray-200 bg-gray-50">
              <th className="text-left py-3 px-4 font-semibold text-secondary">会員ID</th>
              <th className="text-left py-3 px-4 font-semibold text-secondary">会員氏名</th>
              <th className="text-left py-3 px-4 font-semibold text-secondary">口座名義</th>
              <th className="text-left py-3 px-4 font-semibold text-secondary">金融機関</th>
              <th className="text-left py-3 px-4 font-semibold text-secondary">口座番号</th>
              <th className="text-left py-3 px-4 font-semibold text-secondary">登録日</th>
              <th className="text-left py-3 px-4 font-semibold text-secondary">操作</th>
            </tr>
          </thead>
          <tbody>
            {items.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center py-8 text-secondary">
                  承認待ちの口座登録はありません
                </td>
              </tr>
            ) : (
              items.map((item) => (
                <tr
                  key={item.bankAccountRegistrationId}
                  className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                  onClick={() => onItemClick?.(item)}
                >
                  <td className="py-3 px-4">{item.memberId}</td>
                  <td className="py-3 px-4">
                    <div>
                      <div className="font-medium">{item.memberName}</div>
                      <div className="text-sm text-secondary">{item.memberNameKana}</div>
                    </div>
                  </td>
                  <td className="py-3 px-4 font-medium text-blue-600">{item.resultAccountName}</td>
                  <td className="py-3 px-4">
                    <div className="text-sm">
                      <div>{item.resultBankCode}</div>
                      <div className="text-secondary">{item.resultBranchCode}</div>
                    </div>
                  </td>
                  <td className="py-3 px-4 font-mono">{maskAccountNumber(item.resultAccountNumber)}</td>
                  <td className="py-3 px-4">{formatDate(item.completedAt)}</td>
                  <td className="py-3 px-4">
                    <Link
                      href={`/bank-account-approvals/${item.bankAccountRegistrationId}`}
                      className="btn-secondary text-sm"
                      onClick={(e) => e.stopPropagation()}
                    >
                      詳細
                    </Link>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* ページネーション */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage <= 1}
            className="btn-secondary disabled:opacity-50"
          >
            前へ
          </button>

          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
            return (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-1 rounded ${
                  page === currentPage ? 'bg-primary text-white' : 'bg-gray-200 text-secondary hover:bg-gray-300'
                }`}
              >
                {page}
              </button>
            );
          })}

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage >= totalPages}
            className="btn-secondary disabled:opacity-50"
          >
            次へ
          </button>
        </div>
      )}
    </div>
  );
}
