'use client';

import { useState } from 'react';
import { approveBankAccountRegistration, rejectBankAccountRegistration } from '@core-admin/api_clients/bank_account_approval_client';

interface ApprovalFormProps {
  bankAccountRegistrationId: number;
  onApprovalComplete?: () => void;
}

type ApprovalAction = 'approve' | 'reject' | null;

export function ApprovalForm({ bankAccountRegistrationId, onApprovalComplete }: ApprovalFormProps) {
  const [action, setAction] = useState<ApprovalAction>(null);
  const [comment, setComment] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!action) {
      setError('承認または却下を選択してください。');
      return;
    }

    if (action === 'reject' && rejectionReason.trim() === '') {
      setError('却下の場合は理由を入力してください。');
      return;
    }

    setShowConfirmDialog(true);
  };

  const executeApproval = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);
      setShowConfirmDialog(false);

      if (action === 'approve') {
        await approveBankAccountRegistration({
          bankAccountRegistrationId,
          comment: comment.trim(),
        });
      } else if (action === 'reject') {
        await rejectBankAccountRegistration({
          bankAccountRegistrationId,
          rejectionReason: rejectionReason.trim(),
        });
      }

      setSuccess(true);
      if (onApprovalComplete) {
        onApprovalComplete();
      }

      // 成功後にフォームをリセット
      setAction(null);
      setComment('');
      setRejectionReason('');
    } catch (err) {
      console.error('Approval failed:', err);
      setError('処理に失敗しました。もう一度お試しください。');
    } finally {
      setLoading(false);
    }
  };

  const cancelConfirmation = () => {
    setShowConfirmDialog(false);
  };

  return (
    <>
      <form onSubmit={handleSubmit} className="card">
        <h2 className="text-xl font-bold text-primary mb-6">承認処理</h2>

        {/* 承認・却下選択 */}
        <div className="mb-6">
          <label className="block font-semibold text-secondary mb-3">処理を選択してください</label>
          <div className="flex gap-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="action"
                value="approve"
                checked={action === 'approve'}
                onChange={(e) => setAction(e.target.value as ApprovalAction)}
                disabled={loading}
                className="mr-2"
              />
              <span className="text-green-600 font-medium">承認</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="action"
                value="reject"
                checked={action === 'reject'}
                onChange={(e) => setAction(e.target.value as ApprovalAction)}
                disabled={loading}
                className="mr-2"
              />
              <span className="text-red-600 font-medium">却下</span>
            </label>
          </div>
        </div>

        {/* 承認コメント */}
        {action === 'approve' && (
          <div className="mb-6">
            <label className="block font-semibold text-secondary mb-2">承認コメント（任意）</label>
            <textarea
              className="input-base w-full"
              rows={3}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              disabled={loading}
              placeholder="承認に関するコメントがあれば入力してください"
            />
          </div>
        )}

        {/* 却下理由 */}
        {action === 'reject' && (
          <div className="mb-6">
            <label className="block font-semibold text-secondary mb-2">
              却下理由 <span className="text-red-500">*</span>
            </label>
            <textarea
              className="input-base w-full"
              rows={4}
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              disabled={loading}
              placeholder="却下理由を詳しく入力してください"
              required
            />
            <p className="text-sm text-secondary mt-1">会員に通知されるため、分かりやすく具体的に記載してください。</p>
          </div>
        )}

        {/* エラー・成功メッセージ */}
        {error && <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-red-700">{error}</div>}

        {success && <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded text-green-700">処理が完了しました。</div>}

        {/* 送信ボタン */}
        <button type="submit" className="btn-primary disabled:opacity-50" disabled={loading || !action}>
          {loading ? '処理中...' : action === 'approve' ? '承認する' : action === 'reject' ? '却下する' : '処理を実行'}
        </button>
      </form>

      {/* 確認ダイアログ */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg shadow-lg min-w-[400px] max-w-[600px] w-auto mx-4">
            <h3 className="text-lg font-bold text-primary mb-4">確認</h3>
            <p className="text-secondary mb-6 whitespace-normal">
              {action === 'approve'
                ? 'この口座登録を承認しますか？承認後は取り消すことができません。'
                : 'この口座登録を却下しますか？却下後は取り消すことができません。'}
            </p>
            <div className="flex gap-3 justify-end">
              <button onClick={cancelConfirmation} className="btn-secondary" disabled={loading}>
                キャンセル
              </button>
              <button
                onClick={executeApproval}
                className={`${action === 'approve' ? 'btn-primary' : 'bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded'} disabled:opacity-50`}
                disabled={loading}
              >
                {loading ? '処理中...' : action === 'approve' ? '承認する' : '却下する'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
