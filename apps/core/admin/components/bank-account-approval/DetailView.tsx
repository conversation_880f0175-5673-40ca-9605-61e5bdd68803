import { BankAccountApprovalDetail, BankAccountApprovalStatus } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { formatDateTime, maskAccountNumber } from '@core-admin/utils/bank_account_utils';

interface DetailViewProps {
  detail: BankAccountApprovalDetail;
}

export function DetailView({ detail }: DetailViewProps) {
  const getStatusBadge = (status: BankAccountApprovalStatus) => {
    switch (status) {
      case BankAccountApprovalStatus.PENDING:
        return <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">承認待ち</span>;
      case BankAccountApprovalStatus.APPROVED:
        return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">承認済み</span>;
      case BankAccountApprovalStatus.REJECTED:
        return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">却下済み</span>;
      default:
        return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">不明</span>;
    }
  };

  return (
    <div className="space-y-6">
      {/* 会員情報 */}
      <div className="card">
        <h2 className="text-xl font-bold text-primary mb-6">会員情報</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">会員ID</label>
            <p className="text-base">{detail.memberId}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">会員番号</label>
            <p className="text-base">{detail.memberNumber}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">氏名</label>
            <p className="text-base font-medium">{detail.memberName}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">氏名（カナ）</label>
            <p className="text-base">{detail.memberNameKana}</p>
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-secondary mb-2">メールアドレス</label>
            <p className="text-base">{detail.memberEmail}</p>
          </div>
        </div>
      </div>

      {/* 口座情報 */}
      <div className="card">
        <h2 className="text-xl font-bold text-primary mb-6">口座情報</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">口座名義（カナ）</label>
            <p className="text-base font-medium text-blue-600">{detail.resultAccountName}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">承認状況</label>
            <div>{getStatusBadge(detail.approvalStatus)}</div>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">金融機関</label>
            <p className="text-base">
              {detail.resultBankName}
              <span className="text-sm text-secondary ml-2">({detail.resultBankCode})</span>
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">支店</label>
            <p className="text-base">
              {detail.resultBranchName}
              <span className="text-sm text-secondary ml-2">({detail.resultBranchCode})</span>
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">口座番号</label>
            <p className="text-base font-mono">{maskAccountNumber(detail.resultAccountNumber)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">登録完了日時</label>
            <p className="text-base">{formatDateTime(detail.completedAt)}</p>
          </div>
        </div>
      </div>

      {/* 名義確認セクション */}
      <div className="card bg-blue-50 border-l-4 border-blue-400">
        <h3 className="text-lg font-bold text-primary mb-4">名義確認</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">会員氏名（カナ）</label>
            <p className="text-lg font-bold text-blue-600 bg-white p-3 rounded border">{detail.memberNameKana}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary mb-2">口座名義（カナ）</label>
            <p className="text-lg font-bold text-blue-600 bg-white p-3 rounded border">{detail.resultAccountName}</p>
          </div>
        </div>
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <p className="text-sm text-yellow-800">
            <strong>確認事項：</strong>
            会員氏名（カナ）と口座名義（カナ）が一致しているかご確認ください。 スペースや文字の違いにもご注意ください。
          </p>
        </div>
      </div>
    </div>
  );
}
