import React from 'react';

interface AllowanceItem {
  name: string;
  amount: number | string;
}

interface PrizeIncomeConfirmLayoutProps {
  title: string;
  horseName: string;
  showWarning?: boolean;
  warningMessage?: string;
  showError?: boolean;
  errorMessage?: string;
  basicInfo: {
    incomeYearMonth: string;
    occurredYear: string;
    occurredMonth: string;
    occurredDay: string;
    racePlace: string;
    raceName: string;
    raceResult: string;
  };
  prizeInfo: {
    organizer: string;
    mainPrizeAmount: number | string;
    allowances: AllowanceItem[];
    appearanceFee: number | string;
    withholdingTax: number | string;
    commissionAmount: number | string;
  };
  settings: {
    clubFeeRate: string;
    taxRate: string;
  };
  note: string;
  calculatedValues: {
    totalPrizeAmount: number;
    clubFeeAmount: number;
    taxAmount: number;
    incomeAmount: number;
  };
  onBack: () => void;
  onSubmit: () => void;
  submitButtonText: string;
  submitButtonVariant?: 'primary' | 'danger';
}

export default function PrizeIncomeConfirmLayout({
  title,
  horseName,
  showWarning = false,
  warningMessage,
  showError = false,
  errorMessage,
  basicInfo,
  prizeInfo,
  settings,
  note,
  calculatedValues,
  onBack,
  onSubmit,
  submitButtonText,
  submitButtonVariant = 'primary',
}: PrizeIncomeConfirmLayoutProps) {
  const formatAmount = (amount: number | string) => {
    if (typeof amount === 'string') {
      return amount;
    }
    return amount.toLocaleString();
  };

  const getSubmitButtonClass = () => {
    return submitButtonVariant === 'danger' ? 'btn-danger' : 'btn-primary';
  };

  return (
    <main className="bg-white min-h-screen">
      <div className="container mx-auto px-6 py-8">
        <h1 className="text-2xl font-bold text-primary mb-8">{horseName} {title}</h1>

        {/* 警告メッセージ */}
        {showWarning && warningMessage && (
          <div className="bg-error border border-error px-4 py-3 rounded mb-8">
            <strong className="text-error">警告:</strong> {warningMessage}
          </div>
        )}

        {/* エラーメッセージ */}
        {showError && errorMessage && (
          <div className="bg-error border border-error px-4 py-3 rounded mb-8">
            <strong className="text-error">エラー:</strong> {errorMessage}
          </div>
        )}

        {/* 基本情報 */}
        <div>
          <h3 className="text-lg font-medium text-primary mb-4">基本情報</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">分配対象月</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{basicInfo.incomeYearMonth}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">開催年月日</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">
                      {basicInfo.occurredYear}年{basicInfo.occurredMonth}月{basicInfo.occurredDay}日
                    </span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">開催競馬場</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{basicInfo.racePlace}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">レース名</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{basicInfo.raceName}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">着順など</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{basicInfo.raceResult}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 賞金・手当 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">賞金・手当</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">中央・その他</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{prizeInfo.organizer}</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">本賞金</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{formatAmount(prizeInfo.mainPrizeAmount)}円</span>
                  </td>
                </tr>
                {prizeInfo.allowances.map((allowance, index) => (
                  <tr key={index} className="table-row">
                    <th className="table-header">
                      <span className="table-cell-text">{allowance.name}</span>
                    </th>
                    <td className="table-cell text-right">
                      <span className="table-cell-text">{formatAmount(allowance.amount)}円</span>
                    </td>
                  </tr>
                ))}
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">特別出走手当</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{formatAmount(prizeInfo.appearanceFee)}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">JRA・地方競馬源泉所得税</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{formatAmount(prizeInfo.withholdingTax)}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">進上金</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text">{formatAmount(prizeInfo.commissionAmount)}円</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 設定 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">設定</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">クラブ法人手数料率</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{settings.clubFeeRate}%</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税率</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{settings.taxRate}%</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 備考 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">備考</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">備考</span>
                  </th>
                  <td className="table-cell">
                    <span className="table-cell-text">{note || 'なし'}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* 計算結果 */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-primary mb-4">計算結果</h3>
          <div className="bg-surface-card border border-stroke-separator overflow-hidden">
            <table className="w-full">
              <tbody>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">賞金合計</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{calculatedValues.totalPrizeAmount.toLocaleString()}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">クラブ法人手数料</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{calculatedValues.clubFeeAmount.toLocaleString()}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">消費税</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg">{calculatedValues.taxAmount.toLocaleString()}円</span>
                  </td>
                </tr>
                <tr className="table-row">
                  <th className="table-header">
                    <span className="table-cell-text">収入金額</span>
                  </th>
                  <td className="table-cell text-right">
                    <span className="table-cell-text font-bold text-lg text-primary">{calculatedValues.incomeAmount.toLocaleString()}円</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* ボタン */}
        <div className="flex justify-center gap-4 mt-8">
          <button onClick={onBack} className="btn-secondary">
            キャンセル
          </button>
          <button onClick={onSubmit} className={getSubmitButtonClass()}>
            {submitButtonText}
          </button>
        </div>
      </div>
    </main>
  );
} 