import Link from 'next/link';
import React from 'react';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  isCurrent?: boolean;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  return (
    <nav className={`mb-6 ${className}`}>
      <ol className="flex items-center space-x-2 text-sm">
        {items.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <li className="text-muted">/</li>
            )}
            <li>
              {item.href && !item.isCurrent ? (
                <Link 
                  href={item.href} 
                  className="text-muted hover:text-primary transition-colors"
                >
                  {item.label}
                </Link>
              ) : (
                <span className={`${item.isCurrent ? 'text-primary font-medium' : 'text-muted'}`}>
                  {item.label}
                </span>
              )}
            </li>
          </React.Fragment>
        ))}
      </ol>
    </nav>
  );
} 