interface ErrorMessageProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  className?: string;
}

export function ErrorMessage({ 
  title = 'エラーが発生しました', 
  message, 
  onRetry, 
  className = '' 
}: ErrorMessageProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border p-8 text-center ${className}`}>
      <div className="text-red-600 font-semibold mb-4">{title}</div>
      <div className="text-gray-600 text-sm mb-4">{message}</div>
      {onRetry && (
        <button
          onClick={onRetry}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          再試行
        </button>
      )}
    </div>
  );
}

interface EmptyStateProps {
  title?: string;
  message: string;
  action?: React.ReactNode;
  className?: string;
}

export function EmptyState({ 
  title = 'データが見つかりません', 
  message, 
  action, 
  className = '' 
}: EmptyStateProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border p-8 text-center ${className}`}>
      <div className="text-gray-900 font-medium mb-2">{title}</div>
      <div className="text-gray-500 text-sm mb-4">{message}</div>
      {action}
    </div>
  );
}
