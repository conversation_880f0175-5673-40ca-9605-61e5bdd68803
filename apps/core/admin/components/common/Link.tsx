import NextLink, { LinkProps as NextLinkProps } from 'next/link';
import React from 'react';

interface LinkProps extends Omit<NextLinkProps, 'className'> {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'subtle';
}

export function Link({ children, className = '', variant = 'default', ...props }: LinkProps) {
  const baseClasses = 'transition-colors';
  
  const variantClasses = {
    default: 'text-primary hover:text-focus underline hover:no-underline',
    subtle: 'text-muted hover:text-primary'
  };

  // btn-で始まるクラスが含まれている場合はボタンスタイルとして扱い、variantClassesを適用しない
  const isButtonStyle = className.includes('btn-');
  
  const finalClasses = isButtonStyle 
    ? `${baseClasses} ${className}`
    : `${baseClasses} ${variantClasses[variant]} ${className}`;

  return (
    <NextLink 
      className={finalClasses}
      {...props}
    >
      {children}
    </NextLink>
  );
} 