import { RecipientStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';

interface RecipientStatusBadgeProps {
  status: RecipientStatus;
  className?: string;
}

export function RecipientStatusBadge({ status, className = '' }: RecipientStatusBadgeProps) {
  const getStatusConfig = (status: RecipientStatus) => {
    switch (status) {
      case RecipientStatus.PENDING:
        return {
          label: '送信待ち',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
        };
      case RecipientStatus.DELIVERED:
        return {
          label: '配信済み',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
        };
      case RecipientStatus.READ:
        return {
          label: '既読',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
        };
      case RecipientStatus.FAILED:
        return {
          label: '配信失敗',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
        };
      default:
        return {
          label: '不明',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor} ${className}`}
    >
      {config.label}
    </span>
  );
}
