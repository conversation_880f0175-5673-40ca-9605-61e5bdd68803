import { Message } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { MessageStatusBadge } from './MessageStatusBadge';
import { MessageTypeBadge } from './MessageTypeBadge';
import { formatDate } from '@core-admin/utils/date_formatter';

interface DeliveryStatusOverviewProps {
  message: Message;
  className?: string;
}

export function DeliveryStatusOverview({ message, className = '' }: DeliveryStatusOverviewProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <h2 className="text-lg font-medium text-gray-900 mb-4">メッセージ概要</h2>

      <div className="space-y-4">
        {/* タイトルと状態 */}
        <div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">{message.title}</h3>
          <div className="flex items-center space-x-3">
            <MessageTypeBadge messageType={message.messageType} />
            <MessageStatusBadge status={message.status} />
          </div>
        </div>

        {/* メッセージ情報 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200">
          <div>
            <dt className="text-sm font-medium text-gray-500">メッセージID</dt>
            <dd className="mt-1 text-sm text-gray-900">{message.messageId}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">送信者ID</dt>
            <dd className="mt-1 text-sm text-gray-900">{message.senderId}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">作成日時</dt>
            <dd className="mt-1 text-sm text-gray-900">{formatDate(message.createdAt)}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">送信日時</dt>
            <dd className="mt-1 text-sm text-gray-900">{formatDate(message.sentAt)}</dd>
          </div>
        </div>

        {/* メッセージ本文（プレビュー） */}
        <div className="pt-4 border-t border-gray-200">
          <dt className="text-sm font-medium text-gray-500 mb-2">メッセージ本文</dt>
          <dd className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
            {message.body.length > 200 ? `${message.body.substring(0, 200)}...` : message.body}
          </dd>
        </div>

        {/* 添付ファイル情報 */}
        {message.attachments.length > 0 && (
          <div className="pt-4 border-t border-gray-200">
            <dt className="text-sm font-medium text-gray-500 mb-2">添付ファイル</dt>
            <dd className="text-sm text-gray-900">{message.attachments.length}件の添付ファイル</dd>
          </div>
        )}
      </div>
    </div>
  );
}
