'use client';

import { useState, useEffect } from 'react';
import { Member } from '@hami/core-admin-api-schema/member_service_pb';
import { listMembers } from '@core-admin/api_clients/member_client';

interface MemberSelectorProps {
  selectedMemberId?: number;
  onMemberSelect: (member: Member | null) => void;
  className?: string;
}

export function MemberSelector({ selectedMemberId, onMemberSelect, className = '' }: MemberSelectorProps) {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    const fetchMembers = async () => {
      setLoading(true);
      setError(null);
      try {
        const result = await listMembers({});
        setMembers(result.members);
      } catch (err) {
        setError('会員一覧の取得に失敗しました');
        console.error('Failed to fetch members:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchMembers();
  }, []);

  const filteredMembers = members.filter((member) => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      member.memberNumber.toString().toLowerCase().includes(query) ||
      `${member.lastName} ${member.firstName}`.toLowerCase().includes(query) ||
      `${member.lastNameKana} ${member.firstNameKana}`.toLowerCase().includes(query) ||
      member.email.toLowerCase().includes(query)
    );
  });

  const selectedMember = members.find((member) => member.memberId === selectedMemberId);

  return (
    <div className={`bg-white p-4 rounded-lg shadow-sm border ${className}`}>
      <h3 className="text-lg font-medium text-gray-900 mb-4">会員選択</h3>

      {/* 検索フィールド */}
      <div className="mb-4">
        <label htmlFor="memberSearch" className="block text-sm font-medium text-gray-700 mb-1">
          会員検索
        </label>
        <input
          type="text"
          id="memberSearch"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="会員番号、氏名、メールアドレスで検索"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      {/* 選択された会員の表示 */}
      {selectedMember && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex justify-between items-center">
            <div>
              <p className="font-medium text-blue-900">
                {selectedMember.lastName} {selectedMember.firstName}
              </p>
              <p className="text-sm text-blue-700">
                会員番号: {selectedMember.memberNumber} | {selectedMember.email}
              </p>
            </div>
            <button onClick={() => onMemberSelect(null)} className="text-blue-600 hover:text-blue-800 text-sm underline">
              選択解除
            </button>
          </div>
        </div>
      )}

      {/* 会員一覧 */}
      {loading ? (
        <div className="text-center py-4">
          <div className="text-gray-500">読み込み中...</div>
        </div>
      ) : error ? (
        <div className="text-center py-4">
          <div className="text-red-600">{error}</div>
        </div>
      ) : (
        <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-md">
          {filteredMembers.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {searchQuery ? '検索条件に一致する会員が見つかりません' : '会員が見つかりません'}
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredMembers.map((member) => (
                <button
                  key={member.memberId}
                  onClick={() => onMemberSelect(member)}
                  className={`w-full p-3 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 ${
                    selectedMemberId === member.memberId ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="font-medium text-gray-900">
                    {member.lastName} {member.firstName}
                  </div>
                  <div className="text-sm text-gray-500">
                    会員番号: {member.memberNumber} | {member.email}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
