import { MessageType } from '@hami/core-admin-api-schema/admin_message_service_pb';

interface MessageTypeBadgeProps {
  messageType: MessageType;
  className?: string;
}

export function MessageTypeBadge({ messageType, className = '' }: MessageTypeBadgeProps) {
  const getTypeConfig = (messageType: MessageType) => {
    switch (messageType) {
      case MessageType.INDIVIDUAL:
        return {
          label: '個別メッセージ',
          bgColor: 'bg-purple-100',
          textColor: 'text-purple-800',
        };
      case MessageType.BROADCAST:
        return {
          label: '一斉送信',
          bgColor: 'bg-orange-100',
          textColor: 'text-orange-800',
        };
      case MessageType.NOTIFICATION:
        return {
          label: '通知メッセージ',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
        };
      case MessageType.REMINDER:
        return {
          label: 'リマインダー',
          bgColor: 'bg-yellow-100',
          textColor: 'text-yellow-800',
        };
      default:
        return {
          label: '不明',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
        };
    }
  };

  const config = getTypeConfig(messageType);

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor} ${className}`}
    >
      {config.label}
    </span>
  );
}
