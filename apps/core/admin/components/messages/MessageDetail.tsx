import Link from 'next/link';
import { Message } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { MessageStatusBadge } from './MessageStatusBadge';
import { MessageTypeBadge } from './MessageTypeBadge';
import { AttachmentList } from './AttachmentList';
import { formatDate } from '@core-admin/utils/date_formatter';

interface MessageDetailProps {
  message: Message;
  className?: string;
}

export function MessageDetail({ message, className = '' }: MessageDetailProps) {
  return (
    <div className={`bg-white rounded-lg shadow-sm border overflow-hidden ${className}`}>
      {/* ヘッダー */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h1 className="text-xl font-semibold text-gray-900 mb-2">{message.title}</h1>
            <div className="flex items-center space-x-4">
              <MessageTypeBadge messageType={message.messageType} />
              <MessageStatusBadge status={message.status} />
            </div>
          </div>
          <div className="flex space-x-2">
            <Link
              href={`/messages/${message.messageId}/delivery`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              配信状況を確認
            </Link>
          </div>
        </div>
      </div>

      {/* メッセージ情報 */}
      <div className="px-6 py-4 border-b border-gray-200">
        <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
          <div>
            <dt className="text-sm font-medium text-gray-500">メッセージID</dt>
            <dd className="mt-1 text-sm text-gray-900">{message.messageId}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">送信者ID</dt>
            <dd className="mt-1 text-sm text-gray-900">{message.senderId}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">作成日時</dt>
            <dd className="mt-1 text-sm text-gray-900">{formatDate(message.createdAt)}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">更新日時</dt>
            <dd className="mt-1 text-sm text-gray-900">{formatDate(message.updatedAt)}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">送信日時</dt>
            <dd className="mt-1 text-sm text-gray-900">{formatDate(message.sentAt)}</dd>
          </div>
        </dl>
      </div>

      {/* メッセージ本文 */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-900 mb-3">メッセージ本文</h3>
        <div className="prose max-w-none">
          <div className="whitespace-pre-wrap text-sm text-gray-700 bg-gray-50 p-4 rounded-lg border">{message.body}</div>
        </div>
      </div>

      {/* 添付ファイル */}
      <div className="px-6 py-4">
        <AttachmentList attachments={message.attachments} />
      </div>
    </div>
  );
}
