'use client';

import Link from 'next/link';
import { Message } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { MessageStatusBadge } from './MessageStatusBadge';
import { MessageTypeBadge } from './MessageTypeBadge';
import { formatDate } from '@core-admin/utils/date_formatter';

interface MessageListProps {
  messages: Message[];
  loading?: boolean;
  error?: string | null;
  currentPage: number;
  totalPages: number;
  totalCount: number;
  onPageChange: (page: number) => void;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
}

export function MessageList({
  messages,
  loading = false,
  error = null,
  currentPage,
  totalPages,
  totalCount,
  onPageChange,
  sortBy,
  sortOrder,
  onSortChange,
}: MessageListProps) {
  const handleSort = (field: string) => {
    if (sortBy === field) {
      onSortChange(field, sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      onSortChange(field, 'desc');
    }
  };

  const getSortIcon = (field: string) => {
    if (sortBy !== field) return '↕️';
    return sortOrder === 'asc' ? '↑' : '↓';
  };

  if (loading) {
    return (
      <div className="bg-surface-card border border-stroke-separator rounded">
        <div className="p-8 text-center">
          <div className="text-muted">読み込み中...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-surface-card border border-stroke-separator rounded">
        <div className="p-8 text-center">
          <div className="text-error">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-surface-card border border-stroke-separator overflow-hidden rounded">
      {/* ヘッダー */}
      <div className="px-6 py-4 border-b border-stroke-separator">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-medium text-primary">メッセージ一覧</h2>
          <div className="text-sm text-muted">
            {totalCount}件中 {Math.min((currentPage - 1) * 20 + 1, totalCount)}-{Math.min(currentPage * 20, totalCount)}件を表示
          </div>
        </div>
      </div>

      {/* テーブル */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="table-header">
              <th
                className="table-header cursor-pointer hover:bg-surface-elevated"
                onClick={() => handleSort('title')}
              >
                <span className="table-cell-text">タイトル {getSortIcon('title')}</span>
              </th>
              <th className="table-header">
                <span className="table-cell-text">種別</span>
              </th>
              <th className="table-header">
                <span className="table-cell-text">状態</span>
              </th>
              <th
                className="table-header cursor-pointer hover:bg-surface-elevated"
                onClick={() => handleSort('sentAt')}
              >
                <span className="table-cell-text">送信日時 {getSortIcon('sentAt')}</span>
              </th>
              <th
                className="table-header cursor-pointer hover:bg-surface-elevated"
                onClick={() => handleSort('createdAt')}
              >
                <span className="table-cell-text">作成日時 {getSortIcon('createdAt')}</span>
              </th>
              <th className="table-header">
                <span className="table-cell-text">添付</span>
              </th>
              <th className="table-header">
                <span className="table-cell-text">操作</span>
              </th>
            </tr>
          </thead>
          <tbody>
            {messages.length === 0 ? (
              <tr className="table-row">
                <td colSpan={7} className="table-cell text-center">
                  <span className="table-cell-text text-muted">メッセージが見つかりません</span>
                </td>
              </tr>
            ) : (
              messages.map((message) => (
                <tr key={message.messageId} className="table-row">
                  <td className="table-cell w-1/6">
                    <div className="space-y-1">
                      <div className="table-cell-text font-medium truncate">{message.title}</div>
                      <div className="table-cell-text text-muted truncate">
                        {message.body.substring(0, 25)}
                        {message.body.length > 25 ? '...' : ''}
                      </div>
                    </div>
                  </td>
                  <td className="table-cell w-16">
                    <MessageTypeBadge messageType={message.messageType} />
                  </td>
                  <td className="table-cell w-16">
                    <MessageStatusBadge status={message.status} />
                  </td>
                  <td className="table-cell w-24">
                    <span className="table-cell-text">{formatDate(message.sentAt)}</span>
                  </td>
                  <td className="table-cell w-24">
                    <span className="table-cell-text">{formatDate(message.createdAt)}</span>
                  </td>
                  <td className="table-cell w-16">
                    {message.attachments.length > 0 ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-info text-white">
                        {message.attachments.length}件
                      </span>
                    ) : (
                      <span className="table-cell-text text-muted">-</span>
                    )}
                  </td>
                  <td className="table-cell w-24">
                    <div className="flex flex-col space-y-1">
                      <Link href={`/messages/${message.messageId}`} className="btn-secondary text-xs px-2 py-1">
                        詳細
                      </Link>
                      <Link href={`/messages/${message.messageId}/delivery`} className="btn-secondary text-xs px-2 py-1">
                        配信状況
                      </Link>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* ページネーション */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-stroke-separator">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted">
              ページ {currentPage} / {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage <= 1}
                className="btn-secondary text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                前へ
              </button>
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                return (
                  <button
                    key={page}
                    onClick={() => onPageChange(page)}
                    className={`px-3 py-1 text-sm border rounded-md ${
                      currentPage === page ? 'btn-primary' : 'btn-secondary'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}
              <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage >= totalPages}
                className="btn-secondary text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                次へ
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
