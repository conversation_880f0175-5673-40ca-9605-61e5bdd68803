import { MessageAttachment, AttachmentType } from '@hami/core-admin-api-schema/admin_message_service_pb';

interface AttachmentListProps {
  attachments: MessageAttachment[];
  className?: string;
}

export function AttachmentList({ attachments, className = '' }: AttachmentListProps) {
  const getAttachmentTypeIcon = (attachmentType: AttachmentType) => {
    switch (attachmentType) {
      case AttachmentType.IMAGE:
        return '🖼️';
      case AttachmentType.DOCUMENT:
        return '📄';
      case AttachmentType.OTHER:
        return '📎';
      default:
        return '📎';
    }
  };

  const getAttachmentTypeLabel = (attachmentType: AttachmentType) => {
    switch (attachmentType) {
      case AttachmentType.IMAGE:
        return '画像';
      case AttachmentType.DOCUMENT:
        return '文書';
      case AttachmentType.OTHER:
        return 'その他';
      default:
        return 'ファイル';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (attachments.length === 0) {
    return (
      <div className={`text-gray-500 text-sm ${className}`}>
        添付ファイルはありません
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <h4 className="text-sm font-medium text-gray-900">添付ファイル ({attachments.length}件)</h4>
      <div className="space-y-2">
        {attachments.map((attachment) => (
          <div
            key={attachment.messageAttachmentId}
            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
          >
            <div className="flex items-center space-x-3">
              <span className="text-2xl">
                {getAttachmentTypeIcon(attachment.attachmentType)}
              </span>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {attachment.fileName}
                </p>
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  <span>{getAttachmentTypeLabel(attachment.attachmentType)}</span>
                  <span>•</span>
                  <span>{formatFileSize(attachment.fileSize)}</span>
                  <span>•</span>
                  <span>{attachment.mimeType}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {attachment.downloadUrl && (
                <a
                  href={attachment.downloadUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  ダウンロード
                </a>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
