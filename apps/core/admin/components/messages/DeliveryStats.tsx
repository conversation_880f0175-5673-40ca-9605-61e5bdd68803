import { MessageRecipientStatus, RecipientStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';

interface DeliveryStatsProps {
  recipients: MessageRecipientStatus[];
  className?: string;
}

export function DeliveryStats({ recipients, className = '' }: DeliveryStatsProps) {
  const stats = recipients.reduce(
    (acc, recipient) => {
      acc.total++;
      switch (recipient.status) {
        case RecipientStatus.PENDING:
          acc.pending++;
          break;
        case RecipientStatus.DELIVERED:
          acc.delivered++;
          break;
        case RecipientStatus.READ:
          acc.read++;
          break;
        case RecipientStatus.FAILED:
          acc.failed++;
          break;
      }
      return acc;
    },
    { total: 0, pending: 0, delivered: 0, read: 0, failed: 0 }
  );

  const getPercentage = (count: number) => {
    if (stats.total === 0) return 0;
    return Math.round((count / stats.total) * 100);
  };

  const statItems = [
    {
      label: '総受信者数',
      value: stats.total,
      percentage: 100,
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-800',
    },
    {
      label: '送信待ち',
      value: stats.pending,
      percentage: getPercentage(stats.pending),
      bgColor: 'bg-gray-100',
      textColor: 'text-gray-800',
    },
    {
      label: '配信済み',
      value: stats.delivered,
      percentage: getPercentage(stats.delivered),
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-800',
    },
    {
      label: '既読',
      value: stats.read,
      percentage: getPercentage(stats.read),
      bgColor: 'bg-green-100',
      textColor: 'text-green-800',
    },
    {
      label: '配信失敗',
      value: stats.failed,
      percentage: getPercentage(stats.failed),
      bgColor: 'bg-red-100',
      textColor: 'text-red-800',
    },
  ];

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      <h3 className="text-lg font-medium text-gray-900 mb-4">配信統計</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {statItems.map((item) => (
          <div
            key={item.label}
            className={`p-4 rounded-lg ${item.bgColor}`}
          >
            <div className="text-center">
              <div className={`text-2xl font-bold ${item.textColor}`}>
                {item.value}
              </div>
              <div className={`text-sm font-medium ${item.textColor}`}>
                {item.label}
              </div>
              {item.label !== '総受信者数' && (
                <div className={`text-xs ${item.textColor} mt-1`}>
                  ({item.percentage}%)
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* プログレスバー */}
      <div className="mt-6">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>配信進捗</span>
          <span>{getPercentage(stats.delivered + stats.read)}% 完了</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div className="flex h-2 rounded-full overflow-hidden">
            <div
              className="bg-blue-500"
              style={{ width: `${getPercentage(stats.delivered)}%` }}
            />
            <div
              className="bg-green-500"
              style={{ width: `${getPercentage(stats.read)}%` }}
            />
          </div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>配信済み: {getPercentage(stats.delivered)}%</span>
          <span>既読: {getPercentage(stats.read)}%</span>
        </div>
      </div>
    </div>
  );
}
