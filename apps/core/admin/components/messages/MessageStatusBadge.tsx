import { MessageStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';

interface MessageStatusBadgeProps {
  status: MessageStatus;
  className?: string;
}

export function MessageStatusBadge({ status, className = '' }: MessageStatusBadgeProps) {
  const getStatusConfig = (status: MessageStatus) => {
    switch (status) {
      case MessageStatus.DRAFT:
        return {
          label: '下書き',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
        };
      case MessageStatus.SENDING:
        return {
          label: '送信中',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-800',
        };
      case MessageStatus.SENT:
        return {
          label: '送信完了',
          bgColor: 'bg-green-100',
          textColor: 'text-green-800',
        };
      case MessageStatus.FAILED:
        return {
          label: '送信失敗',
          bgColor: 'bg-red-100',
          textColor: 'text-red-800',
        };
      default:
        return {
          label: '不明',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-800',
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor} ${className}`}
    >
      {config.label}
    </span>
  );
}
