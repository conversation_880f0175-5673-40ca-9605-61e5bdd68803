'use client';

import { MessageType, MessageStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';

interface MessageFiltersProps {
  messageType?: MessageType;
  status?: MessageStatus;
  searchQuery?: string;
  onMessageTypeChange: (messageType?: MessageType) => void;
  onStatusChange: (status?: MessageStatus) => void;
  onSearchQueryChange: (searchQuery: string) => void;
  onReset: () => void;
}

export function MessageFilters({
  messageType,
  status,
  searchQuery = '',
  onMessageTypeChange,
  onStatusChange,
  onSearchQueryChange,
  onReset,
}: MessageFiltersProps) {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border mb-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* メッセージ種別フィルタ */}
        <div>
          <label htmlFor="messageType" className="block text-sm font-medium text-gray-700 mb-1">
            メッセージ種別
          </label>
          <select
            id="messageType"
            value={messageType || ''}
            onChange={(e) => onMessageTypeChange(e.target.value ? Number(e.target.value) as MessageType : undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">すべて</option>
            <option value={MessageType.INDIVIDUAL}>個別メッセージ</option>
            <option value={MessageType.BROADCAST}>一斉送信</option>
            <option value={MessageType.NOTIFICATION}>通知メッセージ</option>
            <option value={MessageType.REMINDER}>リマインダー</option>
          </select>
        </div>

        {/* ステータスフィルタ */}
        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
            送信状態
          </label>
          <select
            id="status"
            value={status || ''}
            onChange={(e) => onStatusChange(e.target.value ? Number(e.target.value) as MessageStatus : undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">すべて</option>
            <option value={MessageStatus.DRAFT}>下書き</option>
            <option value={MessageStatus.SENDING}>送信中</option>
            <option value={MessageStatus.SENT}>送信完了</option>
            <option value={MessageStatus.FAILED}>送信失敗</option>
          </select>
        </div>

        {/* 検索クエリ */}
        <div>
          <label htmlFor="searchQuery" className="block text-sm font-medium text-gray-700 mb-1">
            検索（タイトル・本文）
          </label>
          <input
            type="text"
            id="searchQuery"
            value={searchQuery}
            onChange={(e) => onSearchQueryChange(e.target.value)}
            placeholder="検索キーワードを入力"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* リセットボタン */}
        <div className="flex items-end">
          <button
            onClick={onReset}
            className="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            リセット
          </button>
        </div>
      </div>
    </div>
  );
}
