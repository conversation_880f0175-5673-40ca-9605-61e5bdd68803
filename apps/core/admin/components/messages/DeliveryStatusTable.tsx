'use client';

import Link from 'next/link';
import { MessageRecipientStatus, RecipientStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { RecipientStatusBadge } from './RecipientStatusBadge';
import { formatDate } from '@core-admin/utils/date_formatter';

interface DeliveryStatusTableProps {
  recipients: MessageRecipientStatus[];
  loading?: boolean;
  error?: string | null;
  currentPage: number;
  totalPages: number;
  totalCount: number;
  onPageChange: (page: number) => void;
  statusFilter?: RecipientStatus;
  onStatusFilterChange: (status?: RecipientStatus) => void;
}

export function DeliveryStatusTable({
  recipients,
  loading = false,
  error = null,
  currentPage,
  totalPages,
  totalCount,
  onPageChange,
  statusFilter,
  onStatusFilterChange,
}: DeliveryStatusTableProps) {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-8 text-center">
          <div className="text-gray-500">読み込み中...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-8 text-center">
          <div className="text-red-600">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
      {/* ヘッダー */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">配信状況詳細</h3>
          <div className="text-sm text-gray-500">
            {totalCount}件中 {Math.min((currentPage - 1) * 20 + 1, totalCount)}-{Math.min(currentPage * 20, totalCount)}件を表示
          </div>
        </div>

        {/* フィルター */}
        <div className="mt-4">
          <label htmlFor="statusFilter" className="block text-sm font-medium text-gray-700 mb-1">
            配信状況でフィルター
          </label>
          <select
            id="statusFilter"
            value={statusFilter || ''}
            onChange={(e) => onStatusFilterChange(e.target.value ? (Number(e.target.value) as RecipientStatus) : undefined)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">すべて</option>
            <option value={RecipientStatus.PENDING}>送信待ち</option>
            <option value={RecipientStatus.DELIVERED}>配信済み</option>
            <option value={RecipientStatus.READ}>既読</option>
            <option value={RecipientStatus.FAILED}>配信失敗</option>
          </select>
        </div>
      </div>

      {/* テーブル */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">会員情報</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">配信状況</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">配信日時</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">既読日時</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {recipients.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                  {statusFilter ? 'フィルター条件に一致する受信者が見つかりません' : '受信者が見つかりません'}
                </td>
              </tr>
            ) : (
              recipients.map((recipient) => (
                <tr key={recipient.memberId} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">{recipient.memberName}</div>
                    <div className="text-sm text-gray-500">会員番号: {recipient.memberNumber}</div>
                  </td>
                  <td className="px-6 py-4">
                    <RecipientStatusBadge status={recipient.status} />
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">{formatDate(recipient.deliveredAt)}</td>
                  <td className="px-6 py-4 text-sm text-gray-900">{formatDate(recipient.readAt)}</td>
                  <td className="px-6 py-4 text-sm font-medium">
                    <Link href={`/members/${recipient.memberId}`} className="text-blue-600 hover:text-blue-900">
                      会員詳細
                    </Link>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* ページネーション */}
      {totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              ページ {currentPage} / {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage <= 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                前へ
              </button>
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                return (
                  <button
                    key={page}
                    onClick={() => onPageChange(page)}
                    className={`px-3 py-1 text-sm border rounded-md ${
                      currentPage === page ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}
              <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage >= totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                次へ
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
