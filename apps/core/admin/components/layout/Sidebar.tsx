import Link from 'next/link';

interface SidebarProps {
  currentPath?: string;
}

interface MenuGroup {
  category: string;
  items: {
    name: string;
    path: string;
  }[];
}

export default function Sidebar({ currentPath }: SidebarProps) {
  const menuGroups: MenuGroup[] = [
    {
      category: '会員',
      items: [
        { name: '会員管理', path: '/members' },
        { name: '入会申込者の管理', path: '/membership-application' },
        { name: 'コンプライアンス室審査待ち', path: '/compliance/membership-applications' },
        { name: '退会申込一覧', path: '/member-retirement' },
        { name: '会員情報変更', path: '/member-information-change' },
        { name: '会員銀行口座管理', path: '/bank-accounts' },
        { name: '会員口座登録承認', path: '/bank-account-approvals' },
      ],
    },
    {
      category: '馬',
      items: [
        { name: '馬管理', path: '/horses' },
        { name: '一括出資パッケージ', path: '/annual-bundle' },
        { name: '出資申込の管理', path: '/investment-application' },
        { name: '収入一覧', path: '/income-and-billing/income' },
        { name: '支出一覧', path: '/income-and-billing/billing' },
      ],
    },
    {
      category: '請求・支払い',
      items: [
        { name: '会員請求・支払い', path: '/claim-and-pay' },
        { name: '月締め', path: '/monthly' },
        { name: '請求元管理', path: '/biller' },
      ],
    },
    {
      category: 'システム管理',
      items: [{ name: '管理ユーザー一覧', path: '/admin-users' }],
    },
    {
      category: 'その他',
      items: [
        { name: 'ダッシュボード', path: '/' },
        { name: 'メッセージ管理', path: '/messages' },
      ],
    },
  ];

  return (
    <div className="fixed left-0 top-[60px] bg-hami-surface-base w-[300px] h-[calc(100vh-60px)] p-4 overflow-y-auto">
      <div className="flex flex-col gap-8 h-full">
        <div className="flex flex-col">
          {menuGroups.map((group, groupIndex) => (
            <div key={group.category} className={groupIndex > 0 ? 'mt-4' : ''}>
              {/* カテゴリーヘッダー */}
              <div className="h-12 rounded-lg px-4 py-3 flex items-end">
                <span className="text-[14px] font-normal text-[#6f6b6b] tracking-[-0.28px] leading-[1.4]">{group.category}</span>
              </div>

              {/* カテゴリー内のアイテム */}
              {group.items.map((item) => {
                const isActive = currentPath === item.path || (item.path === '/' && currentPath === '/');

                return (
                  <Link
                    key={item.name}
                    href={item.path}
                    className={`h-12 rounded-lg px-4 py-2 flex items-center transition-colors hover:bg-hami-surface-elevated ${
                      isActive ? 'bg-hami-surface-elevated border border-hami-stroke-border' : ''
                    }`}
                  >
                    <span className="text-[16px] font-normal text-hami-glyph-base leading-none">{item.name}</span>
                  </Link>
                );
              })}
            </div>
          ))}
        </div>

        <div className="flex-1 flex flex-col justify-end gap-2">
          <Link href="/settings" className="h-12 rounded-lg px-4 py-2 flex items-center transition-colors hover:bg-hami-surface-elevated">
            <span className="text-[16px] font-normal text-hami-glyph-base leading-none">システム設定</span>
          </Link>
          <Link
            href="/settings/password"
            className="h-12 rounded-lg px-4 py-2 flex items-center transition-colors hover:bg-hami-surface-elevated"
          >
            <span className="text-[16px] font-normal text-hami-glyph-base leading-none">パスワード変更</span>
          </Link>
        </div>
      </div>
    </div>
  );
}
