'use client';

import { usePathname } from 'next/navigation';
import Header from './Header';
import Sidebar from './Sidebar';

interface AppLayoutProps {
  children: React.ReactNode;
  showSidebar?: boolean;
}

export default function AppLayout({ 
  children, 
  showSidebar = true
}: AppLayoutProps) {
  const pathname = usePathname();

  return (
    <div className="bg-hami-surface-base min-h-screen">
      <Header />
      <div className="flex pt-[60px]">
        {showSidebar && <Sidebar currentPath={pathname} />}
        <div 
          className={`bg-hami-surface-elevated flex-1 min-h-[calc(100vh-60px)] ${
            showSidebar ? 'ml-[300px]' : ''
          }`}
        >
          {children}
        </div>
      </div>
    </div>
  );
} 