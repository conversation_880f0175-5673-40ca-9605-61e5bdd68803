import Link from 'next/link';

interface AdminNavigationProps {
  className?: string;
}

export function AdminNavigation({ className = '' }: AdminNavigationProps) {
  const navigationItems = [
    {
      label: 'ダッシュボード',
      href: '/',
      bgColor: 'bg-blue-600',
      hoverColor: 'hover:bg-blue-700',
    },
    {
      label: '会員一覧',
      href: '/members',
      bgColor: 'bg-green-600',
      hoverColor: 'hover:bg-green-700',
    },
    {
      label: 'メッセージ管理',
      href: '/messages',
      bgColor: 'bg-purple-600',
      hoverColor: 'hover:bg-purple-700',
    },
    {
      label: '入会申込一覧',
      href: '/membership-application',
      bgColor: 'bg-orange-600',
      hoverColor: 'hover:bg-orange-700',
    },
    {
      label: '口座登録承認',
      href: '/bank-account-approvals',
      bgColor: 'bg-indigo-600',
      hoverColor: 'hover:bg-indigo-700',
    },
  ];

  return (
    <div className={`flex gap-4 ${className}`}>
      {navigationItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={`px-4 py-2 text-white rounded-md text-sm font-medium ${item.bgColor} ${item.hoverColor} transition-colors`}
        >
          {item.label}
        </Link>
      ))}
    </div>
  );
}
