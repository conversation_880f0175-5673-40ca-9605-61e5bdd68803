'use client';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import LogoHorizontal from '@core-admin/icons/logo_horizontal.svg';
import LogoSquare from '@core-admin/icons/logo_square.svg';

const Logo = ({ size = 'large', variant = 'square' }: { size?: 'large' | 'small'; variant?: 'square' | 'horizontal' }) => {
  if (variant === 'horizontal') {
    const logoWidth = size === 'large' ? 164 : 110;
    const logoHeight = size === 'large' ? 48 : 32;

    return (
      <div className="flex items-center justify-center">
        <LogoHorizontal width={logoWidth} height={logoHeight} className="object-contain" aria-label="HAMI Logo" />
      </div>
    );
  }

  // Square variant - Figmaデザインに基づくサイズ: 200x200px (desktop), 60x60px (mobile)
  const logoSize = size === 'large' ? 200 : 60;

  return (
    <div className="flex items-center justify-center">
      <LogoSquare width={logoSize} height={logoSize} className="object-contain" aria-label="HAMI Logo" />
    </div>
  );
};

export default function Header() {
  const [adminName, setAdminName] = useState<string>('Loading...');
  const [lastLoginAt, setLastLoginAt] = useState<string>('');
  const router = useRouter();

  useEffect(() => {
    const fetchAdminInfo = async () => {
      try {
        // セッショントークンをCookieから取得
        const cookies = document.cookie.split('; ');
        const sessionCookie = cookies.find((cookie) => cookie.startsWith('sessionToken='));
        const sessionToken = sessionCookie?.split('=')[1];

        if (sessionToken) {
          const { getMe } = await import('@core-admin/api_clients/admin_user_client');
          const response = await getMe({ sessionToken });
          setAdminName(response.name);
          
          // lastLoginAtをフォーマット
          if (response.lastLoginAt) {
            const timestamp = response.lastLoginAt;
            const date = new Date(Number(timestamp.seconds) * 1000 + Math.floor(timestamp.nanos / 1000000));
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            setLastLoginAt(`${year}/${month}/${day} ${hours}:${minutes}`);
          }
        } else {
          setAdminName('No Session');
        }
      } catch (error) {
        console.error('Failed to fetch admin info:', error);
        setAdminName('Unknown User');
      }
    };

    fetchAdminInfo();
  }, []);

  const handleLogout = async () => {
    try {
      // Cookieからセッショントークンを削除
      document.cookie = 'sessionToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

      // ログインページに遷移
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
      // エラーが発生してもCookieを削除してログインページに遷移
      document.cookie = 'sessionToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      router.push('/login');
    }
  };

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-hami-surface-base border-b border-hami-stroke-separator h-[60px]">
      <div className="flex items-center px-4 py-2 h-full">
        <div className="flex items-center gap-4 flex-1">
          <Link href="/" className="flex items-center">
            <Logo variant="horizontal" size="large" />
          </Link>
          <div className="border border-hami-stroke-border rounded-lg px-2 py-1">
            <span className="text-[13px] font-semibold text-hami-glyph-subtle">基幹システム</span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-[16px] text-hami-glyph-base-1">{adminName}</span>
          <div className="border border-hami-stroke-border rounded px-1.5 py-0">
            <span className="text-[12px] text-hami-glyph-subtle">管理者</span>
          </div>
          <span className="text-[12px] text-hami-glyph-subtle">{lastLoginAt}</span>
          <button
            onClick={handleLogout}
            className="bg-gray-200 rounded px-3 py-2 text-[14px] font-bold text-gray-700
                     hover:bg-gray-600 hover:text-white 
                     active:bg-gray-700 active:scale-95
                     transition-all duration-200 ease-in-out
                     cursor-pointer select-none
                     border border-transparent hover:border-gray-600"
          >
            ログアウト
          </button>
        </div>
      </div>
    </div>
  );
} 