import React from 'react';

export interface Column<T> {
  header: string;
  accessor: keyof T | string;
  width?: string;
  render?: (value: T[keyof T] | string | number | boolean | null | undefined, row: T, index: number) => React.ReactNode;
}

interface DataTableProps<T extends Record<string, unknown>> {
  columns: Column<T>[];
  data: T[];
  className?: string;
}

// ネストしたプロパティにアクセスするためのヘルパー関数
function getNestedValue(obj: Record<string, unknown>, path: string): unknown {
  return path.split('.').reduce((current, key) => {
    return current && typeof current === 'object' && key in current ? (current as Record<string, unknown>)[key] : undefined;
  }, obj as unknown);
}

export default function DataTable<T extends Record<string, unknown>>({ columns, data, className }: DataTableProps<T>) {
  // CSS Grid用のテンプレートカラムを生成
  const gridTemplateColumns = columns
    .map((column) => {
      if (column.width) {
        if (column.width.startsWith('w-')) {
          // Tailwind width classを実際のpx値に変換
          const sizeMap: Record<string, string> = {
            'w-40': '160px',
            'w-32': '128px',
            'w-48': '192px',
            'w-56': '224px',
            'w-64': '256px',
          };
          return sizeMap[column.width] || '160px';
        }
        return column.width;
      }
      return '1fr'; // 残りの幅を均等分割
    })
    .join(' ');

  return (
    <div className={`border border-hami-stroke-border rounded-lg overflow-hidden ${className || ''}`} role="table" aria-label="data">
      {/* Table Container */}
      <div className="grid" style={{ gridTemplateColumns }}>
        {/* Table Header */}
        {columns.map((column, index) => (
          <div
            key={`header-${index}`}
            role="columnheader"
            className={`bg-hami-shark-100 px-3 py-3 flex items-center ${
              index < columns.length - 1 ? 'border-r border-hami-stroke-separator' : ''
            }`}
          >
            <span className="text-[14px] font-bold text-hami-glyph-subtle">{column.header}</span>
          </div>
        ))}

        {/* Table Rows */}
        {data.length === 0 ? (
          <div
            role="cell"
            className="bg-hami-surface-elevated h-12 flex items-center justify-center border-t border-hami-stroke-border text-[16px] text-hami-glyph-subtle col-span-full"
          >
            データがありません
          </div>
        ) : (
          data.flatMap((row, rowIndex) =>
            columns.map((column, columnIndex) => {
              const value = typeof column.accessor === 'string' ? getNestedValue(row, column.accessor) : row[column.accessor];

              return (
                <div
                  key={`${rowIndex}-${columnIndex}`}
                  role="cell"
                  className={`bg-hami-surface-elevated px-3 py-3 flex items-start border-t border-hami-stroke-border ${
                    columnIndex < columns.length - 1 ? 'border-r border-hami-stroke-border' : ''
                  }`}
                >
                  {column.render ? (
                    column.render(value as T[keyof T] | string | number | boolean | null | undefined, row, rowIndex)
                  ) : (
                    <span className="text-[18px] text-hami-glyph-base break-words whitespace-pre-wrap">
                      {value != null ? String(value) : ''}
                    </span>
                  )}
                </div>
              );
            })
          )
        )}
      </div>
    </div>
  );
}
