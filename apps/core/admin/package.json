{"name": "@hami/core-admin", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "gen_api_clients": "tsx ./scripts/gen_api_clients.ts", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@bufbuild/protobuf": "^2.2.5", "@connectrpc/connect": "^2.0.1", "@connectrpc/connect-node": "^2.0.1", "@hami/core-admin-api-schema": "workspace:*", "@hami/core-api": "workspace:*", "@tanstack/react-query": "^5.0.0", "decimal.js": "^10.6.0", "next": "15.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "vercel": "^41.3.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.25.1", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/ui": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.2.1", "fast-glob": "^3.3.3", "jsdom": "^26.1.0", "prisma": "^6.13.0", "tailwindcss": "^4", "tsx": "^4.19.2", "typescript": "^5", "vitest": "^3.2.4"}}