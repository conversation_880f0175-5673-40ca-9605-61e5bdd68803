import { promises as fs } from 'fs';
import path from 'path';
import glob from 'fast-glob';
import { fileURLToPath } from 'url';

// camelCase → snake_case 変換
function camelToSnake(str: string): string {
  return str
    .replace(/([A-Z])/g, '_$1')
    .toLowerCase()
    .replace(/^_/, '');
}
// 先頭大文字化
function upperFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
// 先頭小文字化
function lowerFirst(str: string): string {
  return str.charAt(0).toLowerCase() + str.slice(1);
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename); // このスクリプトファイルがあるディレクトリの絶対パス

(async () => {
  try {
    // globのパターンはスクリプトの場所からの相対パスで指定し、絶対パスに解決
    // globの対象ディレクトリを先に解決
    const targetGlobDir = path.resolve(__dirname, '../../../../packages/core-admin-api-schema/gen');
    // globは対象ディレクトリ内でパターンに一致するファイルを探す
    const filesRelative = await glob('*_service_pb.ts', { cwd: targetGlobDir, absolute: true });

    if (filesRelative.length === 0) {
      return;
    }

    for (const file of filesRelative) {
      try {
        const absolutePathToImport = file; // globが絶対パスを返すのでそのまま使用
        const mod = await import(absolutePathToImport);

        const services = Object.entries(mod).filter(([k, v]) => k.endsWith('Service'));

        if (services.length === 0) {
          continue;
        }

        for (const [serviceName, serviceRaw] of services) {
          const service = serviceRaw as any;

          if (!service.methods || !Array.isArray(service.methods)) {
            console.error(`Service ${serviceName} in ${file} does not have a valid 'methods' array. Skipping.`);
            continue;
          }
          const methods = service.methods as { name: string; I: any }[];

          const importPathSuffix = path.basename(file, '.ts');
          const importPath = `@hami/core-admin-api-schema/${importPathSuffix}`;

          const relName = serviceName.replace(/Service$/, '');
          const destRelativeFromScript = `../api_clients/${camelToSnake(relName)}_client.ts`;
          const destAbsolute = path.resolve(__dirname, destRelativeFromScript);

          const body = [
            `'use server';`,
            ``,
            `import { ${serviceName},`,
            ...methods.map((m) => `  ${upperFirst(m.name)}RequestSchema,`),
            `} from '${importPath}';`,
            // 生成されるクライアントファイル (例: apps/core/admin/api_clients/xxx.ts) から見て
            // apps/core/admin/utils/api_clients.ts を参照するための相対パス
            `import { getClient } from '@core-admin/utils/api_clients';`,
            `import { create } from '@bufbuild/protobuf';`,
            `import type { MessageInitShape } from '@bufbuild/protobuf';`,
            `import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';`,
            ``,
            `const client = getClient(${serviceName});`,
            ``,
            ...methods.map((m) => {
              const reqType = `${upperFirst(m.name)}RequestSchema`;
              return (
                `export const ${lowerFirst(m.name)} = async (req: MessageInitShape<typeof ${reqType}>) => ` +
                `withAuthErrorHandling(() => client.${lowerFirst(m.name)}(create(${reqType}, req)));\n`
              );
            }),
            ``,
          ].join('\n');

          const destDir = path.dirname(destAbsolute);
          await fs.mkdir(destDir, { recursive: true });

          await fs.writeFile(destAbsolute, body, 'utf8');

          try {
            await fs.access(destAbsolute);
          } catch (e) {
            console.error(`File ${destAbsolute} NOT found after writing. Error:`, e);
          }
        }
      } catch (loopError) {
        console.error(`Error processing file ${file}:`, loopError);
      }
    }
    console.log('Script finished processing all files.');
  } catch (error) {
    console.error('An critical error occurred in the script:', error);
  }
})();
