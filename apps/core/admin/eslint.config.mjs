import { defineConfig } from 'eslint/config';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default defineConfig([
  {
    extends: compat.extends('next/core-web-vitals', 'next/typescript'),

    rules: {
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_',
        },
      ],
      // any型の使用を禁止
      '@typescript-eslint/no-explicit-any': 'error',
      // non-null assertionの使用を警告
      '@typescript-eslint/no-non-null-assertion': 'warn',
      // 不安全な型アサーションを警告
      '@typescript-eslint/consistent-type-assertions': [
        'warn',
        {
          assertionStyle: 'as',
          objectLiteralTypeAssertions: 'never',
        },
      ],
    },
  },
  {
    files: ['**/test/**/*.ts', '**/test/**/*.tsx', '**/*.test.ts', '**/*.test.tsx'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off', // テストファイルではany型を許可
      '@typescript-eslint/no-non-null-assertion': 'off', // テストファイルではnon-null assertionを許可
      '@typescript-eslint/consistent-type-assertions': 'off', // テストファイルでは型アサーションを許可
    },
  },
]);
