'use server';

import { BankAccountApprovalService,
  ListPendingBankAccountRegistrationsRequestSchema,
  ListAllBankAccountRegistrationsRequestSchema,
  GetBankAccountRegistrationDetailRequestSchema,
  ApproveBankAccountRegistrationRequestSchema,
  RejectBankAccountRegistrationRequestSchema,
} from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(BankAccountApprovalService);

export const listPendingBankAccountRegistrations = async (req: MessageInitShape<typeof ListPendingBankAccountRegistrationsRequestSchema>) => withAuthErrorHandling(() => client.listPendingBankAccountRegistrations(create(ListPendingBankAccountRegistrationsRequestSchema, req)));

export const listAllBankAccountRegistrations = async (req: MessageInitShape<typeof ListAllBankAccountRegistrationsRequestSchema>) => withAuthErrorHandling(() => client.listAllBankAccountRegistrations(create(ListAllBankAccountRegistrationsRequestSchema, req)));

export const getBankAccountRegistrationDetail = async (req: MessageInitShape<typeof GetBankAccountRegistrationDetailRequestSchema>) => withAuthErrorHandling(() => client.getBankAccountRegistrationDetail(create(GetBankAccountRegistrationDetailRequestSchema, req)));

export const approveBankAccountRegistration = async (req: MessageInitShape<typeof ApproveBankAccountRegistrationRequestSchema>) => withAuthErrorHandling(() => client.approveBankAccountRegistration(create(ApproveBankAccountRegistrationRequestSchema, req)));

export const rejectBankAccountRegistration = async (req: MessageInitShape<typeof RejectBankAccountRegistrationRequestSchema>) => withAuthErrorHandling(() => client.rejectBankAccountRegistration(create(RejectBankAccountRegistrationRequestSchema, req)));

