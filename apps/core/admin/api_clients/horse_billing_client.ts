'use server';

import { HorseBillingService,
  ListHorseBillingsRequestSchema,
  ListAllHorseBillingsRequestSchema,
  GetHorseBillingDetailRequestSchema,
  CreateHorseBillingRequestSchema,
  UpdateHorseBillingRequestSchema,
  DeleteHorseBillingRequestSchema,
} from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(HorseBillingService);

export const listHorseBillings = async (req: MessageInitShape<typeof ListHorseBillingsRequestSchema>) => withAuthErrorHandling(() => client.listHorseBillings(create(ListHorseBillingsRequestSchema, req)));

export const listAllHorseBillings = async (req: MessageInitShape<typeof ListAllHorseBillingsRequestSchema>) => withAuthErrorHandling(() => client.listAllHorseBillings(create(ListAllHorseBillingsRequestSchema, req)));

export const getHorseBillingDetail = async (req: MessageInitShape<typeof GetHorseBillingDetailRequestSchema>) => withAuthErrorHandling(() => client.getHorseBillingDetail(create(GetHorseBillingDetailRequestSchema, req)));

export const createHorseBilling = async (req: MessageInitShape<typeof CreateHorseBillingRequestSchema>) => withAuthErrorHandling(() => client.createHorseBilling(create(CreateHorseBillingRequestSchema, req)));

export const updateHorseBilling = async (req: MessageInitShape<typeof UpdateHorseBillingRequestSchema>) => withAuthErrorHandling(() => client.updateHorseBilling(create(UpdateHorseBillingRequestSchema, req)));

export const deleteHorseBilling = async (req: MessageInitShape<typeof DeleteHorseBillingRequestSchema>) => withAuthErrorHandling(() => client.deleteHorseBilling(create(DeleteHorseBillingRequestSchema, req)));

