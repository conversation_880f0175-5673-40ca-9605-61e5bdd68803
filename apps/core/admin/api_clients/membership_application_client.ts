'use server';

import { MembershipApplicationService,
  ListMembershipApplicationsRequestSchema,
  GetMembershipApplicationRequestSchema,
  ReviewMembershipApplicationRequestSchema,
  ReviewMembershipApplicationDocumentGroupRequestSchema,
  ReviewComplianceMembershipApplicationRequestSchema,
  ReviewComplianceMembershipApplicationDocumentGroupRequestSchema,
  GetIdentityDocumentImageUrlRequestSchema,
  ExportApprovedMembershipApplicationsCsvRequestSchema,
} from '@hami/core-admin-api-schema/membership_application_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(MembershipApplicationService);

export const listMembershipApplications = async (req: MessageInitShape<typeof ListMembershipApplicationsRequestSchema>) => withAuthErrorHandling(() => client.listMembershipApplications(create(ListMembershipApplicationsRequestSchema, req)));

export const getMembershipApplication = async (req: MessageInitShape<typeof GetMembershipApplicationRequestSchema>) => withAuthErrorHandling(() => client.getMembershipApplication(create(GetMembershipApplicationRequestSchema, req)));

export const reviewMembershipApplication = async (req: MessageInitShape<typeof ReviewMembershipApplicationRequestSchema>) => withAuthErrorHandling(() => client.reviewMembershipApplication(create(ReviewMembershipApplicationRequestSchema, req)));

export const reviewMembershipApplicationDocumentGroup = async (req: MessageInitShape<typeof ReviewMembershipApplicationDocumentGroupRequestSchema>) => withAuthErrorHandling(() => client.reviewMembershipApplicationDocumentGroup(create(ReviewMembershipApplicationDocumentGroupRequestSchema, req)));

export const reviewComplianceMembershipApplication = async (req: MessageInitShape<typeof ReviewComplianceMembershipApplicationRequestSchema>) => withAuthErrorHandling(() => client.reviewComplianceMembershipApplication(create(ReviewComplianceMembershipApplicationRequestSchema, req)));

export const reviewComplianceMembershipApplicationDocumentGroup = async (req: MessageInitShape<typeof ReviewComplianceMembershipApplicationDocumentGroupRequestSchema>) => withAuthErrorHandling(() => client.reviewComplianceMembershipApplicationDocumentGroup(create(ReviewComplianceMembershipApplicationDocumentGroupRequestSchema, req)));

export const getIdentityDocumentImageUrl = async (req: MessageInitShape<typeof GetIdentityDocumentImageUrlRequestSchema>) => withAuthErrorHandling(() => client.getIdentityDocumentImageUrl(create(GetIdentityDocumentImageUrlRequestSchema, req)));

export const exportApprovedMembershipApplicationsCsv = async (req: MessageInitShape<typeof ExportApprovedMembershipApplicationsCsvRequestSchema>) => withAuthErrorHandling(() => client.exportApprovedMembershipApplicationsCsv(create(ExportApprovedMembershipApplicationsCsvRequestSchema, req)));

