'use server';

import { HorseIncomeService,
  ListHorseIncomesRequestSchema,
  ListAllHorseIncomesRequestSchema,
  GetHorseIncomePrizeRequestSchema,
  CreateHorseIncomePrizeRequestSchema,
  UpdateHorseIncomePrizeRequestSchema,
  DeleteHorseIncomePrizeRequestSchema,
  GetHorseIncomeOtherRequestSchema,
  CreateHorseIncomeOtherRequestSchema,
  UpdateHorseIncomeOtherRequestSchema,
  DeleteHorseIncomeOtherRequestSchema,
  ListHorseIncomePrizeAllowanceNamesRequestSchema,
} from '@hami/core-admin-api-schema/horse_income_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(HorseIncomeService);

export const listHorseIncomes = async (req: MessageInitShape<typeof ListHorseIncomesRequestSchema>) => withAuthErrorHandling(() => client.listHorseIncomes(create(ListHorseIncomesRequestSchema, req)));

export const listAllHorseIncomes = async (req: MessageInitShape<typeof ListAllHorseIncomesRequestSchema>) => withAuthErrorHandling(() => client.listAllHorseIncomes(create(ListAllHorseIncomesRequestSchema, req)));

export const getHorseIncomePrize = async (req: MessageInitShape<typeof GetHorseIncomePrizeRequestSchema>) => withAuthErrorHandling(() => client.getHorseIncomePrize(create(GetHorseIncomePrizeRequestSchema, req)));

export const createHorseIncomePrize = async (req: MessageInitShape<typeof CreateHorseIncomePrizeRequestSchema>) => withAuthErrorHandling(() => client.createHorseIncomePrize(create(CreateHorseIncomePrizeRequestSchema, req)));

export const updateHorseIncomePrize = async (req: MessageInitShape<typeof UpdateHorseIncomePrizeRequestSchema>) => withAuthErrorHandling(() => client.updateHorseIncomePrize(create(UpdateHorseIncomePrizeRequestSchema, req)));

export const deleteHorseIncomePrize = async (req: MessageInitShape<typeof DeleteHorseIncomePrizeRequestSchema>) => withAuthErrorHandling(() => client.deleteHorseIncomePrize(create(DeleteHorseIncomePrizeRequestSchema, req)));

export const getHorseIncomeOther = async (req: MessageInitShape<typeof GetHorseIncomeOtherRequestSchema>) => withAuthErrorHandling(() => client.getHorseIncomeOther(create(GetHorseIncomeOtherRequestSchema, req)));

export const createHorseIncomeOther = async (req: MessageInitShape<typeof CreateHorseIncomeOtherRequestSchema>) => withAuthErrorHandling(() => client.createHorseIncomeOther(create(CreateHorseIncomeOtherRequestSchema, req)));

export const updateHorseIncomeOther = async (req: MessageInitShape<typeof UpdateHorseIncomeOtherRequestSchema>) => withAuthErrorHandling(() => client.updateHorseIncomeOther(create(UpdateHorseIncomeOtherRequestSchema, req)));

export const deleteHorseIncomeOther = async (req: MessageInitShape<typeof DeleteHorseIncomeOtherRequestSchema>) => withAuthErrorHandling(() => client.deleteHorseIncomeOther(create(DeleteHorseIncomeOtherRequestSchema, req)));

export const listHorseIncomePrizeAllowanceNames = async (req: MessageInitShape<typeof ListHorseIncomePrizeAllowanceNamesRequestSchema>) => withAuthErrorHandling(() => client.listHorseIncomePrizeAllowanceNames(create(ListHorseIncomePrizeAllowanceNamesRequestSchema, req)));

