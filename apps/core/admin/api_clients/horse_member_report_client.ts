'use server';

import { HorseMemberReportService,
  GenerateHorseMemberListPdfRequestSchema,
  GetHorseMemberListPdfDownloadUrlRequestSchema,
} from '@hami/core-admin-api-schema/horse_member_report_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(HorseMemberReportService);

export const generateHorseMemberListPdf = async (req: MessageInitShape<typeof GenerateHorseMemberListPdfRequestSchema>) => withAuthErrorHandling(() => client.generateHorseMemberListPdf(create(GenerateHorseMemberListPdfRequestSchema, req)));

export const getHorseMemberListPdfDownloadUrl = async (req: MessageInitShape<typeof GetHorseMemberListPdfDownloadUrlRequestSchema>) => withAuthErrorHandling(() => client.getHorseMemberListPdfDownloadUrl(create(GetHorseMemberListPdfDownloadUrlRequestSchema, req)));

