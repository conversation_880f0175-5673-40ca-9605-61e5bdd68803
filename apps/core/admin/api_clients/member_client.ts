'use server';

import { MemberService,
  ListMembersRequestSchema,
  GetMemberRequestSchema,
  UpdateMemberRequestSchema,
  CancelRetirementRequestSchema,
  ListMemberInvestmentHorsesRequestSchema,
} from '@hami/core-admin-api-schema/member_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(MemberService);

export const listMembers = async (req: MessageInitShape<typeof ListMembersRequestSchema>) => withAuthErrorHandling(() => client.listMembers(create(ListMembersRequestSchema, req)));

export const getMember = async (req: MessageInitShape<typeof GetMemberRequestSchema>) => withAuthErrorHandling(() => client.getMember(create(GetMemberRequestSchema, req)));

export const updateMember = async (req: MessageInitShape<typeof UpdateMemberRequestSchema>) => withAuthErrorHandling(() => client.updateMember(create(UpdateMemberRequestSchema, req)));

export const cancelRetirement = async (req: MessageInitShape<typeof CancelRetirementRequestSchema>) => withAuthErrorHandling(() => client.cancelRetirement(create(CancelRetirementRequestSchema, req)));

export const listMemberInvestmentHorses = async (req: MessageInitShape<typeof ListMemberInvestmentHorsesRequestSchema>) => withAuthErrorHandling(() => client.listMemberInvestmentHorses(create(ListMemberInvestmentHorsesRequestSchema, req)));

