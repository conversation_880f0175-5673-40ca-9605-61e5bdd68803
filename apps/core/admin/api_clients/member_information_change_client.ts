'use server';

import { MemberInformationChangeService,
  GetMemberInformationChangeApplicationsRequestSchema,
  GetMemberInformationChangeApplicationRequestSchema,
  ReviewMemberInformationChangeApplicationRequestSchema,
} from '@hami/core-admin-api-schema/member_information_change_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(MemberInformationChangeService);

export const getMemberInformationChangeApplications = async (req: MessageInitShape<typeof GetMemberInformationChangeApplicationsRequestSchema>) => withAuthErrorHandling(() => client.getMemberInformationChangeApplications(create(GetMemberInformationChangeApplicationsRequestSchema, req)));

export const getMemberInformationChangeApplication = async (req: MessageInitShape<typeof GetMemberInformationChangeApplicationRequestSchema>) => withAuthErrorHandling(() => client.getMemberInformationChangeApplication(create(GetMemberInformationChangeApplicationRequestSchema, req)));

export const reviewMemberInformationChangeApplication = async (req: MessageInitShape<typeof ReviewMemberInformationChangeApplicationRequestSchema>) => withAuthErrorHandling(() => client.reviewMemberInformationChangeApplication(create(ReviewMemberInformationChangeApplicationRequestSchema, req)));

