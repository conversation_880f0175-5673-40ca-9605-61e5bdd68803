'use server';

import { HorseService,
  ListHorsesRequestSchema,
  GetHorseRequestSchema,
  CreateHorseRequestSchema,
  UpdateHorseRequestSchema,
  DeleteHorseRequestSchema,
  CheckHorseExistsRequestSchema,
  ListHorseInvestorsRequestSchema,
} from '@hami/core-admin-api-schema/horse_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(HorseService);

export const listHorses = async (req: MessageInitShape<typeof ListHorsesRequestSchema>) => withAuthErrorHandling(() => client.listHorses(create(ListHorsesRequestSchema, req)));

export const getHorse = async (req: MessageInitShape<typeof GetHorseRequestSchema>) => withAuthErrorHandling(() => client.getHorse(create(GetHorseRequestSchema, req)));

export const createHorse = async (req: MessageInitShape<typeof CreateHorseRequestSchema>) => withAuthErrorHandling(() => client.createHorse(create(CreateHorseRequestSchema, req)));

export const updateHorse = async (req: MessageInitShape<typeof UpdateHorseRequestSchema>) => withAuthErrorHandling(() => client.updateHorse(create(UpdateHorseRequestSchema, req)));

export const deleteHorse = async (req: MessageInitShape<typeof DeleteHorseRequestSchema>) => withAuthErrorHandling(() => client.deleteHorse(create(DeleteHorseRequestSchema, req)));

export const checkHorseExists = async (req: MessageInitShape<typeof CheckHorseExistsRequestSchema>) => withAuthErrorHandling(() => client.checkHorseExists(create(CheckHorseExistsRequestSchema, req)));

export const listHorseInvestors = async (req: MessageInitShape<typeof ListHorseInvestorsRequestSchema>) => withAuthErrorHandling(() => client.listHorseInvestors(create(ListHorseInvestorsRequestSchema, req)));

