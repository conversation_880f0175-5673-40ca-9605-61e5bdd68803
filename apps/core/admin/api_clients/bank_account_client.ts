'use server';

import { BankAccountService,
  ListBankAccountRegistrationsRequestSchema,
  GetBankAccountDetailRequestSchema,
} from '@hami/core-admin-api-schema/bank_account_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(BankAccountService);

export const listBankAccountRegistrations = async (req: MessageInitShape<typeof ListBankAccountRegistrationsRequestSchema>) => withAuthErrorHandling(() => client.listBankAccountRegistrations(create(ListBankAccountRegistrationsRequestSchema, req)));

export const getBankAccountDetail = async (req: MessageInitShape<typeof GetBankAccountDetailRequestSchema>) => withAuthErrorHandling(() => client.getBankAccountDetail(create(GetBankAccountDetailRequestSchema, req)));

