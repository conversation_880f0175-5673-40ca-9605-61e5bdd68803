'use server';

import { HorseIncomeService, ListHorseIncomePrizeAllowanceNamesRequestSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(HorseIncomeService);

export const listHorseIncomePrizeAllowanceNames = async (req: MessageInitShape<typeof ListHorseIncomePrizeAllowanceNamesRequestSchema>) => withAuthErrorHandling(() => client.listHorseIncomePrizeAllowanceNames(create(ListHorseIncomePrizeAllowanceNamesRequestSchema, req)));
