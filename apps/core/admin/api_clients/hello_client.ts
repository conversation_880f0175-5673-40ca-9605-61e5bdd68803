'use server';

import { HelloService,
  HelloRequestSchema,
} from '@hami/core-admin-api-schema/hello_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';

const client = getClient(HelloService);

export const sayHello = async (req: MessageInitShape<typeof HelloRequestSchema>) => client.sayHello(create(HelloRequestSchema, req));

