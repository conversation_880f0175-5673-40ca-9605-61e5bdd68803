'use server';

import { MemberClaimAndPayService,
  ListMemberClaimAndPaysRequestSchema,
  GenerateInvestmentAndReturnPdfRequestSchema,
} from '@hami/core-admin-api-schema/member_claim_and_pay_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(MemberClaimAndPayService);

export const listMemberClaimAndPays = async (req: MessageInitShape<typeof ListMemberClaimAndPaysRequestSchema>) => withAuthErrorHandling(() => client.listMemberClaimAndPays(create(ListMemberClaimAndPaysRequestSchema, req)));

export const generateInvestmentAndReturnPdf = async (req: MessageInitShape<typeof GenerateInvestmentAndReturnPdfRequestSchema>) => withAuthErrorHandling(() => client.generateInvestmentAndReturnPdf(create(GenerateInvestmentAndReturnPdfRequestSchema, req)));

