'use server';

import { AdminMessageService,
  ListMessagesRequestSchema,
  GetMessageRequestSchema,
  GetDeliveryStatusRequestSchema,
  ListMemberMessagesRequestSchema,
} from '@hami/core-admin-api-schema/admin_message_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(AdminMessageService);

export const listMessages = async (req: MessageInitShape<typeof ListMessagesRequestSchema>) => withAuthErrorHandling(() => client.listMessages(create(ListMessagesRequestSchema, req)));

export const getMessage = async (req: MessageInitShape<typeof GetMessageRequestSchema>) => withAuthErrorHandling(() => client.getMessage(create(GetMessageRequestSchema, req)));

export const getDeliveryStatus = async (req: MessageInitShape<typeof GetDeliveryStatusRequestSchema>) => withAuthErrorHandling(() => client.getDeliveryStatus(create(GetDeliveryStatusRequestSchema, req)));

export const listMemberMessages = async (req: MessageInitShape<typeof ListMemberMessagesRequestSchema>) => withAuthErrorHandling(() => client.listMemberMessages(create(ListMemberMessagesRequestSchema, req)));

