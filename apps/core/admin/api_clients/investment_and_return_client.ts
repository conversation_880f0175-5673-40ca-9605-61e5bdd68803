'use server';

import { InvestmentAndReturnService,
  CreateInvestmentAndReturnRequestSchema,
  CreateMemberClaimAndPayRequestSchema,
  GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema,
  GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema,
  GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema,
} from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(InvestmentAndReturnService);

export const createInvestmentAndReturn = async (req: MessageInitShape<typeof CreateInvestmentAndReturnRequestSchema>) => withAuthErrorHandling(() => client.createInvestmentAndReturn(create(CreateInvestmentAndReturnRequestSchema, req)));

export const createMemberClaimAndPay = async (req: MessageInitShape<typeof CreateMemberClaimAndPayRequestSchema>) => withAuthErrorHandling(() => client.createMemberClaimAndPay(create(CreateMemberClaimAndPayRequestSchema, req)));

export const getCreatedInvestmentAndReturnSharesSumByHorseId = async (req: MessageInitShape<typeof GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema>) => withAuthErrorHandling(() => client.getCreatedInvestmentAndReturnSharesSumByHorseId(create(GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema, req)));

export const getInvestmentAndReturnListByHorseIdAndCreatedDate = async (req: MessageInitShape<typeof GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema>) => withAuthErrorHandling(() => client.getInvestmentAndReturnListByHorseIdAndCreatedDate(create(GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema, req)));

export const getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId = async (req: MessageInitShape<typeof GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema>) => withAuthErrorHandling(() => client.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, req)));

