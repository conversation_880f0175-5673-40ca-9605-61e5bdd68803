'use server';

import { AdminUserService,
  LoginRequestSchema,
  GetMeRequestSchema,
  ListAdminUsersRequestSchema,
  CreateAdminUserRequestSchema,
  UpdateAdminUserRequestSchema,
  ChangePasswordRequestSchema,
} from '@hami/core-admin-api-schema/admin_user_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(AdminUserService);

export const login = async (req: MessageInitShape<typeof LoginRequestSchema>) => withAuthErrorHandling(() => client.login(create(LoginRequestSchema, req)));

export const getMe = async (req: MessageInitShape<typeof GetMeRequestSchema>) => withAuthErrorHandling(() => client.getMe(create(GetMeRequestSchema, req)));

export const listAdminUsers = async (req: MessageInitShape<typeof ListAdminUsersRequestSchema>) => withAuthErrorHandling(() => client.listAdminUsers(create(ListAdminUsersRequestSchema, req)));

export const createAdminUser = async (req: MessageInitShape<typeof CreateAdminUserRequestSchema>) => withAuthErrorHandling(() => client.createAdminUser(create(CreateAdminUserRequestSchema, req)));

export const updateAdminUser = async (req: MessageInitShape<typeof UpdateAdminUserRequestSchema>) => withAuthErrorHandling(() => client.updateAdminUser(create(UpdateAdminUserRequestSchema, req)));

export const changePassword = async (req: MessageInitShape<typeof ChangePasswordRequestSchema>) => withAuthErrorHandling(() => client.changePassword(create(ChangePasswordRequestSchema, req)));

