'use server';

import { InvestmentApplicationService,
  ListInvestmentApplicationHorsesRequestSchema,
  GetInvestmentApplicationHorseDetailRequestSchema,
  ListInvestmentApplicationsByHorseRequestSchema,
  AcceptInvestmentApplicationsRequestSchema,
  ListContractTargetApplicationsRequestSchema,
  CompleteInvestmentContractsRequestSchema,
  ListInvestmentApplicationsRequestSchema,
  ListAnnualBundleInvestmentApplicationsRequestSchema,
  GetInvestmentApplicationAnnualBundleDetailRequestSchema,
  AcceptAnnualBundleInvestmentApplicationsRequestSchema,
  CompleteAnnualBundleInvestmentApplicationsRequestSchema,
} from '@hami/core-admin-api-schema/investment_application_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(InvestmentApplicationService);

export const listInvestmentApplicationHorses = async (req: MessageInitShape<typeof ListInvestmentApplicationHorsesRequestSchema>) => withAuthErrorHandling(() => client.listInvestmentApplicationHorses(create(ListInvestmentApplicationHorsesRequestSchema, req)));

export const getInvestmentApplicationHorseDetail = async (req: MessageInitShape<typeof GetInvestmentApplicationHorseDetailRequestSchema>) => withAuthErrorHandling(() => client.getInvestmentApplicationHorseDetail(create(GetInvestmentApplicationHorseDetailRequestSchema, req)));

export const listInvestmentApplicationsByHorse = async (req: MessageInitShape<typeof ListInvestmentApplicationsByHorseRequestSchema>) => withAuthErrorHandling(() => client.listInvestmentApplicationsByHorse(create(ListInvestmentApplicationsByHorseRequestSchema, req)));

export const acceptInvestmentApplications = async (req: MessageInitShape<typeof AcceptInvestmentApplicationsRequestSchema>) => withAuthErrorHandling(() => client.acceptInvestmentApplications(create(AcceptInvestmentApplicationsRequestSchema, req)));

export const listContractTargetApplications = async (req: MessageInitShape<typeof ListContractTargetApplicationsRequestSchema>) => withAuthErrorHandling(() => client.listContractTargetApplications(create(ListContractTargetApplicationsRequestSchema, req)));

export const completeInvestmentContracts = async (req: MessageInitShape<typeof CompleteInvestmentContractsRequestSchema>) => withAuthErrorHandling(() => client.completeInvestmentContracts(create(CompleteInvestmentContractsRequestSchema, req)));

export const listInvestmentApplications = async (req: MessageInitShape<typeof ListInvestmentApplicationsRequestSchema>) => withAuthErrorHandling(() => client.listInvestmentApplications(create(ListInvestmentApplicationsRequestSchema, req)));

export const listAnnualBundleInvestmentApplications = async (req: MessageInitShape<typeof ListAnnualBundleInvestmentApplicationsRequestSchema>) => withAuthErrorHandling(() => client.listAnnualBundleInvestmentApplications(create(ListAnnualBundleInvestmentApplicationsRequestSchema, req)));

export const getInvestmentApplicationAnnualBundleDetail = async (req: MessageInitShape<typeof GetInvestmentApplicationAnnualBundleDetailRequestSchema>) => withAuthErrorHandling(() => client.getInvestmentApplicationAnnualBundleDetail(create(GetInvestmentApplicationAnnualBundleDetailRequestSchema, req)));

export const acceptAnnualBundleInvestmentApplications = async (req: MessageInitShape<typeof AcceptAnnualBundleInvestmentApplicationsRequestSchema>) => withAuthErrorHandling(() => client.acceptAnnualBundleInvestmentApplications(create(AcceptAnnualBundleInvestmentApplicationsRequestSchema, req)));

export const completeAnnualBundleInvestmentApplications = async (req: MessageInitShape<typeof CompleteAnnualBundleInvestmentApplicationsRequestSchema>) => withAuthErrorHandling(() => client.completeAnnualBundleInvestmentApplications(create(CompleteAnnualBundleInvestmentApplicationsRequestSchema, req)));

