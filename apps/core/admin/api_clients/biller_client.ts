'use server';

import { <PERSON>er<PERSON>er<PERSON>,
  ListBillersRequestSchema,
  GetBillerRequestSchema,
  CreateBillerRequestSchema,
  UpdateBillerRequestSchema,
} from '@hami/core-admin-api-schema/biller_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(BillerService);

export const listBillers = async (req: MessageInitShape<typeof ListBillersRequestSchema>) => withAuthErrorHandling(() => client.listBillers(create(ListBillersRequestSchema, req)));

export const getBiller = async (req: MessageInitShape<typeof GetBillerRequestSchema>) => withAuthErrorHandling(() => client.getBiller(create(GetBillerRequestSchema, req)));

export const createBiller = async (req: MessageInitShape<typeof CreateBillerRequestSchema>) => withAuthErrorHandling(() => client.createBiller(create(CreateBillerRequestSchema, req)));

export const updateBiller = async (req: MessageInitShape<typeof UpdateBillerRequestSchema>) => withAuthErrorHandling(() => client.updateBiller(create(UpdateBillerRequestSchema, req)));

