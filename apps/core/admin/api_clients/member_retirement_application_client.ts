'use server';

import { MemberRetirementApplicationService,
  ListMemberRetirementApplicationsRequestSchema,
  GetMemberRetirementApplicationRequestSchema,
  ApproveMemberRetirementApplicationRequestSchema,
  RejectMemberRetirementApplicationRequestSchema,
} from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(MemberRetirementApplicationService);

export const listMemberRetirementApplications = async (req: MessageInitShape<typeof ListMemberRetirementApplicationsRequestSchema>) => withAuthErrorHandling(() => client.listMemberRetirementApplications(create(ListMemberRetirementApplicationsRequestSchema, req)));

export const getMemberRetirementApplication = async (req: MessageInitShape<typeof GetMemberRetirementApplicationRequestSchema>) => withAuthErrorHandling(() => client.getMemberRetirementApplication(create(GetMemberRetirementApplicationRequestSchema, req)));

export const approveMemberRetirementApplication = async (req: MessageInitShape<typeof ApproveMemberRetirementApplicationRequestSchema>) => withAuthErrorHandling(() => client.approveMemberRetirementApplication(create(ApproveMemberRetirementApplicationRequestSchema, req)));

export const rejectMemberRetirementApplication = async (req: MessageInitShape<typeof RejectMemberRetirementApplicationRequestSchema>) => withAuthErrorHandling(() => client.rejectMemberRetirementApplication(create(RejectMemberRetirementApplicationRequestSchema, req)));

