'use server';

import { AnnualBundleService,
  ListAnnualBundlesRequestSchema,
  GetAnnualBundleRequestSchema,
  CreateAnnualBundleRequestSchema,
  UpdateAnnualBundleRequestSchema,
  ListAnnualBundleHorsesCandidatesRequestSchema,
} from '@hami/core-admin-api-schema/annual_bundle_service_pb';
import { getClient } from '@core-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@core-admin/utils/api_error_handlers';

const client = getClient(AnnualBundleService);

export const listAnnualBundles = async (req: MessageInitShape<typeof ListAnnualBundlesRequestSchema>) => withAuthErrorHandling(() => client.listAnnualBundles(create(ListAnnualBundlesRequestSchema, req)));

export const getAnnualBundle = async (req: MessageInitShape<typeof GetAnnualBundleRequestSchema>) => withAuthErrorHandling(() => client.getAnnualBundle(create(GetAnnualBundleRequestSchema, req)));

export const createAnnualBundle = async (req: MessageInitShape<typeof CreateAnnualBundleRequestSchema>) => withAuthErrorHandling(() => client.createAnnualBundle(create(CreateAnnualBundleRequestSchema, req)));

export const updateAnnualBundle = async (req: MessageInitShape<typeof UpdateAnnualBundleRequestSchema>) => withAuthErrorHandling(() => client.updateAnnualBundle(create(UpdateAnnualBundleRequestSchema, req)));

export const listAnnualBundleHorsesCandidates = async (req: MessageInitShape<typeof ListAnnualBundleHorsesCandidatesRequestSchema>) => withAuthErrorHandling(() => client.listAnnualBundleHorsesCandidates(create(ListAnnualBundleHorsesCandidatesRequestSchema, req)));

