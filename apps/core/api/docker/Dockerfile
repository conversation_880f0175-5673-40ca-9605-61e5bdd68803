FROM node:20-alpine AS base

WORKDIR /app

RUN npm install -g pnpm turbo
RUN apk add --no-cache openssl


FROM base AS builder
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY . .

RUN turbo prune @hami/core-api @hami/prisma --docker


FROM base AS installer
RUN apk update
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY --from=builder /app/out/json/ .
COPY --from=builder /app/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/out/pnpm-lock.yaml ./pnpm-lock.yaml

RUN mkdir -p ./packages/core-admin-api-schema
COPY --from=builder /app/packages/core-admin-api-schema/gen ./packages/core-admin-api-schema/gen
RUN pnpm install --frozen-lockfile

COPY --from=builder /app/out/full/ .
RUN pnpm --filter @hami/core-admin-api-schema run build
RUN pnpm --filter @hami/prisma run generate

RUN pnpm turbo run build --filter=@hami/core-api
RUN pnpm --filter @hami/core-api exec tsc-alias


FROM base AS runner
WORKDIR /app
ENV NODE_ENV=production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nodeapp

COPY --from=installer /app/node_modules ./node_modules
COPY --from=installer /app/apps/core/api/node_modules ./apps/core/api/node_modules
COPY --from=installer /app/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=installer /app/package.json ./package.json
COPY --from=installer /app/apps/core/api/dist ./apps/core/api/dist
COPY --from=installer /app/apps/core/api/package.json ./apps/core/api/package.json
COPY --from=builder /app/apps/core/api/src/assets ./apps/core/api/src/assets
COPY --from=installer /app/packages/prisma ./packages/prisma
COPY --from=installer /app/packages/core-admin-api-schema ./packages/core-admin-api-schema

USER nodeapp
WORKDIR /app/apps/core/api

EXPOSE 8080
CMD ["pnpm", "--filter", "@hami/core-api", "start"]
