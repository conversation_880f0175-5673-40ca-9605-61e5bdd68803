services:
  core-api:
    build:
      context: ../../../
      dockerfile: ./apps/core/api/docker/Dockerfile
      target: installer
    user: root
    volumes:
      - ../../../:/app
      - core_api_node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/hami
      - JWT_SECRET=dev-secret
      - FRONTEND_ENDPOINT=http://localhost:3000
      - SMTP_HOST=mailpit
      - SMTP_PORT=1025
      - SMTP_FROM_ADDRESS=<EMAIL>
      - MAILPIT_URL=http://mailpit:8025
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin
      - AWS_REGION=ap-northeast-1
      - S3_BUCKET_NAME=test-bucket
      - S3_ENDPOINT=http://minio:9000
      - S3_PUBLIC_ENDPOINT=https://minio.hami-${WORKTREE_NAME}.orb.local
      - NODE_TLS_REJECT_UNAUTHORIZED=0
      - GMO_PG_BASE_URL=https://kt01.mul-pay.jp
      - GMO_PG_SHOP_ID=tshop00058016
      - GMO_PG_SHOP_PASS=f5nzdyhf
      - GMO_PG_SITE_ID=tsite00058016
      - GMO_PG_SITE_PASS=f5nzdyhf
      - GMO_PG_TIMEOUT=10000
      - GMO_PG_RETRY_COUNT=3
    command: pnpm --filter @hami/core-api dev
    depends_on:
      - db
      - mailpit
    networks:
      - hami-network

networks:
  hami-network:
    external: true
    name: hami-${WORKTREE_NAME}-network

volumes:
  core_api_node_modules:
    driver: local
    name: core_api_node_modules_${WORKTREE_NAME}
