import { defineMembershipApplicationComplianceReviewLogFactory } from '@hami/prisma';
import { MembershipApplicationFactory } from './membership_application_factory';

export const MembershipApplicationComplianceReviewLogFactory = defineMembershipApplicationComplianceReviewLogFactory({
  defaultData: ({ seq }) => ({
    membershipApplication: MembershipApplicationFactory,
    reviewer: `Compliance Reviewer ${seq}`,
    reviewType: 'APPROVE',
    remandReason: '',
  }),
});


