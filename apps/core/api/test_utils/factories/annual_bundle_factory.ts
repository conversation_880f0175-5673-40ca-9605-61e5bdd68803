import { defineAnnualBundleFactory, AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@hami/prisma';

export const AnnualBundleFactory = defineAnnualBundleFactory({
  defaultData: ({ seq }) => {
    const fiscalYear = 2020 + (seq % 10);
    return {
      fiscalYear,
      name: `年度バンドル${seq}`,
      shares: 1000 + seq,
      publishStatus: AnnualBundlePublishStatus.PRIVATE,
      recruitmentStatus: AnnualBundleRecruitmentStatus.UPCOMING,
    };
  },
});


