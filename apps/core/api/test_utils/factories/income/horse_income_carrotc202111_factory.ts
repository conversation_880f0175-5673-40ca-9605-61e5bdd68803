import * as path from 'path';
import { HorseIncomeOtherName } from '@hami/prisma';
import { createOtherIncome, createHorseIncomePrizeListFromTsv } from './create';

export const createHorseIncomeOtherCarrotc202111 = (carrotc202111Id: number) => [
  // その他収入
  // 保険
  createOtherIncome(carrotc202111Id, 202406, 2024, 5, HorseIncomeOtherName.SYMPATHY, '', 2650000, 0, '見舞金', 0, 0, ''),

  // 賞品売却分配金
  createOtherIncome(
    carrotc202111Id,
    202301,
    2022,
    12,
    HorseIncomeOtherName.GRANT,
    '賞品売却分配金',
    738036,
    22000,
    '',
    0,
    10,
    '賞品売却分配金-1'
  ),
  createOtherIncome(
    carrotc202111Id,
    202305,
    2023,
    5,
    HorseIncomeOtherName.GRANT,
    '賞品売却分配金',
    1106457,
    22000,
    '',
    0,
    10,
    '賞品売却分配金-2'
  ),
  createOtherIncome(
    carrotc202111Id,
    202307,
    2023,
    7,
    HorseIncomeOtherName.GRANT,
    '賞品売却分配金',
    1305872,
    22000,
    '',
    0,
    10,
    '賞品売却分配金-3'
  ),
  createOtherIncome(
    carrotc202111Id,
    202310,
    2023,
    9,
    HorseIncomeOtherName.GRANT,
    '賞品売却分配金',
    1202048,
    22000,
    '',
    0,
    10,
    '賞品売却分配金-4'
  ),
  createOtherIncome(
    carrotc202111Id,
    202311,
    2023,
    10,
    HorseIncomeOtherName.GRANT,
    '賞品売却分配金',
    3553038,
    22000,
    '',
    0,
    10,
    '賞品売却分配金-5'
  ),
];

export const createHorseIncomePrizeCarrotc202111 = (carrotc202111Id: number) => {
  const tsvFilePath = path.join(__dirname, 'csv', 'carrotc202111.tsv');
  return createHorseIncomePrizeListFromTsv(carrotc202111Id, tsvFilePath);
};
