import { defineAnnualBundleHorseFactory, AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@hami/prisma';

export const AnnualBundleHorseFactory = defineAnnualBundleHorseFactory({
  defaultData: ({ seq }) => {
    const fiscalYear = 2020 + (seq % 10);
    return {
      annualBundle: {
        create: {
          fiscalYear,
          name: `年度バンドル${seq}`,
          shares: 1000,
          publishStatus: AnnualBundlePublishStatus.PRIVATE,
          recruitmentStatus: AnnualBundleRecruitmentStatus.UPCOMING,
        },
      },
      horse: {
        create: {
          recruitmentYear: fiscalYear,
          recruitmentNo: 1000 + seq,
          horseName: `テストホース${seq}`,
          birthYear: 2019,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: `テスト馬${seq}`,
          sharesTotal: 100,
          amountTotal: 1000000,
          fundStartYear: fiscalYear,
          fundStartMonth: 1,
          fundStartDay: 1,
          note: '',
          conflictOfInterest: false,
        },
      },
    };
  },
});
