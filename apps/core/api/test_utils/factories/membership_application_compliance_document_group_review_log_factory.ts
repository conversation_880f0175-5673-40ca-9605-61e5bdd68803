import { defineMembershipApplicationComplianceDocumentGroupReviewLogFactory } from '@hami/prisma';
import { MembershipApplicationFactory } from './membership_application_factory';

export const MembershipApplicationComplianceDocumentGroupReviewLogFactory = defineMembershipApplicationComplianceDocumentGroupReviewLogFactory({
  defaultData: ({ seq }) => ({
    membershipApplication: MembershipApplicationFactory,
    reviewer: `Compliance Doc Reviewer ${seq}`,
    reviewType: 'APPROVE',
    remandReason: '',
  }),
});


