/**
 * 環境変数の設定と検証を行うモジュール
 */

export class EnvironmentValidationError extends Error {
  readonly name = 'EnvironmentValidationError';
}

/**
 * 必須環境変数の検証
 */
function validateRequiredEnvVar(name: string, value: string | undefined): string {
  if (!value || value.trim() === '') {
    throw new EnvironmentValidationError(`環境変数 ${name} が設定されていません`);
  }
  return value.trim();
}

/**
 * URL形式の環境変数の検証
 */
function validateUrlEnvVar(name: string, value: string | undefined): string {
  const validatedValue = validateRequiredEnvVar(name, value);

  try {
    new URL(validatedValue);
    return validatedValue;
  } catch {
    throw new EnvironmentValidationError(`環境変数 ${name} は有効なURLである必要があります: ${validatedValue}`);
  }
}

/**
 * 環境変数の設定
 */
export const config = {
  /**
   * JWT設定
   */
  get jwtSecret(): string {
    return validateRequiredEnvVar('JWT_SECRET', process.env.JWT_SECRET);
  },

  /**
   * データベース設定
   */
  get databaseUrl(): string {
    return validateRequiredEnvVar('DATABASE_URL', process.env.DATABASE_URL);
  },

  /**
   * フロントエンド設定
   */
  get frontendEndpoint(): string {
    return validateUrlEnvVar('FRONTEND_ENDPOINT', process.env.FRONTEND_ENDPOINT);
  },

  /**
   * SMTP設定
   */
  smtp: {
    get host(): string {
      return process.env.SMTP_HOST || 'localhost';
    },
    get port(): number {
      return parseInt(process.env.SMTP_PORT || '1025', 10);
    },
    get user(): string | undefined {
      return process.env.SMTP_USER;
    },
    get password(): string | undefined {
      return process.env.SMTP_PASSWORD;
    },
    get secure(): boolean {
      return process.env.SMTP_SECURE?.toLowerCase() === 'true';
    },
    get requireTls(): boolean {
      return process.env.SMTP_REQUIRE_TLS?.toLowerCase() === 'true';
    },
    get fromAddress(): string {
      return process.env.SMTP_FROM_ADDRESS || `no-reply@${this.host}`;
    },
  },

  /**
   * S3設定
   */
  s3: {
    get bucketName(): string {
      if (process.env.NODE_ENV === 'test') {
        return process.env.S3_BUCKET_NAME || 'test-bucket';
      }
      return validateRequiredEnvVar('S3_BUCKET_NAME', process.env.S3_BUCKET_NAME);
    },
    get region(): string {
      if (process.env.NODE_ENV === 'test') {
        return process.env.AWS_REGION || 'ap-northeast-1';
      }
      return validateRequiredEnvVar('AWS_REGION', process.env.AWS_REGION);
    },
    get accessKeyId(): string | undefined {
      if (process.env.NODE_ENV === 'test') {
        return process.env.AWS_ACCESS_KEY_ID || 'minioadmin';
      }
      // ECS環境ではIAMタスクロールを使用するため、環境変数は任意
      return process.env.AWS_ACCESS_KEY_ID;
    },
    get secretAccessKey(): string | undefined {
      if (process.env.NODE_ENV === 'test') {
        return process.env.AWS_SECRET_ACCESS_KEY || 'minioadmin';
      }
      // ECS環境ではIAMタスクロールを使用するため、環境変数は任意
      return process.env.AWS_SECRET_ACCESS_KEY;
    },
    get endpoint(): string | undefined {
      return process.env.S3_ENDPOINT;
    },
    get publicEndpoint(): string | undefined {
      return process.env.S3_PUBLIC_ENDPOINT;
    },
  },

  /**
   * GMO ペイメントゲートウェイ設定
   */
  gmoPg: {
    get baseUrl(): string {
      if (process.env.NODE_ENV === 'test') {
        return process.env.GMO_PG_BASE_URL || 'https://test-pt01.mul-pay.jp';
      }
      return validateRequiredEnvVar('GMO_PG_BASE_URL', process.env.GMO_PG_BASE_URL);
    },
    get shopId(): string {
      if (process.env.NODE_ENV === 'test') {
        return process.env.GMO_PG_SHOP_ID || 'test_shop_id';
      }
      return validateRequiredEnvVar('GMO_PG_SHOP_ID', process.env.GMO_PG_SHOP_ID);
    },
    get shopPass(): string {
      if (process.env.NODE_ENV === 'test') {
        return process.env.GMO_PG_SHOP_PASS || 'test_shop_pass';
      }
      return validateRequiredEnvVar('GMO_PG_SHOP_PASS', process.env.GMO_PG_SHOP_PASS);
    },
    get siteId(): string {
      if (process.env.NODE_ENV === 'test') {
        return process.env.GMO_PG_SITE_ID || 'test_site_id';
      }
      return validateRequiredEnvVar('GMO_PG_SITE_ID', process.env.GMO_PG_SITE_ID);
    },
    get sitePass(): string {
      if (process.env.NODE_ENV === 'test') {
        return process.env.GMO_PG_SITE_PASS || 'test_site_pass';
      }
      return validateRequiredEnvVar('GMO_PG_SITE_PASS', process.env.GMO_PG_SITE_PASS);
    },
    get timeout(): number {
      return parseInt(process.env.GMO_PG_TIMEOUT || '30000', 10);
    },
    get retryCount(): number {
      return parseInt(process.env.GMO_PG_RETRY_COUNT || '3', 10);
    },
  },
} as const;

/**
 * 起動時に必須環境変数をすべて検証する
 */
export function validateEnvironment(): void {
  try {
    // 必須環境変数をすべてアクセスして検証
    config.jwtSecret;
    config.databaseUrl;
    config.frontendEndpoint;

    // S3設定は本番環境でのみ必須
    if (process.env.NODE_ENV !== 'test') {
      config.s3.bucketName;
      config.s3.region;
    }

    // GMO PG設定は本番環境でのみ必須
    if (process.env.NODE_ENV === 'production') {
      config.gmoPg.baseUrl;
      config.gmoPg.shopId;
      config.gmoPg.shopPass;
      config.gmoPg.siteId;
      config.gmoPg.sitePass;
    }

    console.log('✅ 環境変数の検証が完了しました');
  } catch (error) {
    if (error instanceof EnvironmentValidationError) {
      console.error('❌ 環境変数の検証に失敗しました:', error.message);
      throw error;
    }
    throw error;
  }
}
