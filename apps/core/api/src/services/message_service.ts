import { create } from '@bufbuild/protobuf';
import {
  Message,
  MessageSchema,
  MessageAttachment,
  MessageAttachmentSchema,
  MessageRecipientStatus,
  MessageRecipientStatusSchema,
  MemberMessage,
  MemberMessageSchema,
  MessageType,
  MessageStatus,
  RecipientStatus,
  AttachmentType,
} from '@hami/core-admin-api-schema/admin_message_service_pb';
import type { MessageAttachment as PrismaMessageAttachment, RecipientStatus as PrismaRecipientStatus, Prisma } from '@hami/prisma';

// Prismaのincludeオプションを使った場合の型定義
type MessageWithAttachments = Prisma.MessageGetPayload<{
  include: {
    attachments: true;
  };
}>;

// 配信状況データの型定義（会員情報を含む）
type MessageRecipientWithMemberInfo = {
  messageRecipientId: number;
  messageId: number;
  memberId: number;
  recipientStatus: PrismaRecipientStatus;
  deliveredAt: Date | null;
  readAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  memberName: string;
  memberNumber: string;
};

// 会員別メッセージデータの型定義（MessageRecipientとMessageを結合）
type MessageRecipientWithMessage = Prisma.MessageRecipientGetPayload<{
  include: {
    message: {
      include: {
        attachments: true;
      };
    };
  };
}>;
import * as messageRecipientRepository from '@core-api/repositories/message_recipient_repository';
import * as messageRepository from '@core-api/repositories/message_repository';

/**
 * Prismaのenum値をproto enum値にマッピング
 */
const mapMessageType = (type: string): MessageType => {
  switch (type) {
    case 'INDIVIDUAL':
      return MessageType.INDIVIDUAL;
    case 'BROADCAST':
      return MessageType.BROADCAST;
    case 'NOTIFICATION':
      return MessageType.NOTIFICATION;
    case 'REMINDER':
      return MessageType.REMINDER;
    default:
      return MessageType.UNSPECIFIED;
  }
};

const mapMessageStatus = (status: string): MessageStatus => {
  switch (status) {
    case 'DRAFT':
      return MessageStatus.DRAFT;
    case 'SENDING':
      return MessageStatus.SENDING;
    case 'SENT':
      return MessageStatus.SENT;
    case 'FAILED':
      return MessageStatus.FAILED;
    default:
      return MessageStatus.UNSPECIFIED;
  }
};

const mapRecipientStatus = (status: string): RecipientStatus => {
  switch (status) {
    case 'PENDING':
      return RecipientStatus.PENDING;
    case 'DELIVERED':
      return RecipientStatus.DELIVERED;
    case 'READ':
      return RecipientStatus.READ;
    case 'FAILED':
      return RecipientStatus.FAILED;
    default:
      return RecipientStatus.UNSPECIFIED;
  }
};

const mapAttachmentType = (type: string): AttachmentType => {
  switch (type) {
    case 'IMAGE':
      return AttachmentType.IMAGE;
    case 'DOCUMENT':
      return AttachmentType.DOCUMENT;
    case 'OTHER':
      return AttachmentType.OTHER;
    default:
      return AttachmentType.UNSPECIFIED;
  }
};

/**
 * 添付ファイルのダウンロードURL生成
 * TODO: S3の署名付きURLを生成する実装に置き換える
 */
const generateDownloadUrl = (filePath: string): string => {
  // 仮実装: 実際にはS3の署名付きURLを生成する
  return `/api/attachments/download?path=${encodeURIComponent(filePath)}`;
};

/**
 * PrismaのMessageAttachmentをproto MessageAttachmentに変換
 */
const mapToMessageAttachment = (attachment: PrismaMessageAttachment): MessageAttachment => {
  return create(MessageAttachmentSchema, {
    messageAttachmentId: attachment.messageAttachmentId,
    fileName: attachment.fileName,
    mimeType: attachment.mimeType,
    fileSize: attachment.fileSize,
    attachmentType: mapAttachmentType(attachment.attachmentType),
    displayOrder: attachment.displayOrder,
    downloadUrl: generateDownloadUrl(attachment.filePath),
  });
};

/**
 * PrismaのMessageをproto Messageに変換
 */
const mapToMessage = (message: MessageWithAttachments): Message => {
  return create(MessageSchema, {
    messageId: message.publicId,
    title: message.title,
    body: message.body,
    messageType: mapMessageType(message.messageType),
    status: mapMessageStatus(message.status),
    senderId: message.senderId,
    sentAt: message.sentAt
      ? {
          seconds: BigInt(Math.floor(message.sentAt.getTime() / 1000)),
          nanos: (message.sentAt.getTime() % 1000) * 1000000,
        }
      : undefined,
    createdAt: {
      seconds: BigInt(Math.floor(message.createdAt.getTime() / 1000)),
      nanos: (message.createdAt.getTime() % 1000) * 1000000,
    },
    updatedAt: {
      seconds: BigInt(Math.floor(message.updatedAt.getTime() / 1000)),
      nanos: (message.updatedAt.getTime() % 1000) * 1000000,
    },
    attachments: message.attachments?.map(mapToMessageAttachment) || [],
  });
};

/**
 * 配信状況データをproto MessageRecipientStatusに変換
 */
const mapToMessageRecipientStatus = (recipient: MessageRecipientWithMemberInfo): MessageRecipientStatus => {
  return create(MessageRecipientStatusSchema, {
    memberId: recipient.memberId,
    memberName: recipient.memberName || '',
    memberNumber: recipient.memberNumber || '',
    status: mapRecipientStatus(recipient.recipientStatus),
    deliveredAt: recipient.deliveredAt
      ? {
          seconds: BigInt(Math.floor(recipient.deliveredAt.getTime() / 1000)),
          nanos: (recipient.deliveredAt.getTime() % 1000) * 1000000,
        }
      : undefined,
    readAt: recipient.readAt
      ? {
          seconds: BigInt(Math.floor(recipient.readAt.getTime() / 1000)),
          nanos: (recipient.readAt.getTime() % 1000) * 1000000,
        }
      : undefined,
  });
};

/**
 * 会員別メッセージデータをproto MemberMessageに変換
 */
const mapToMemberMessage = (recipient: MessageRecipientWithMessage): MemberMessage => {
  const message = recipient.message;
  return create(MemberMessageSchema, {
    messageId: message.publicId,
    title: message.title,
    body: message.body,
    messageType: mapMessageType(message.messageType),
    status: mapMessageStatus(message.status),
    recipientStatus: mapRecipientStatus(recipient.recipientStatus),
    senderId: message.senderId,
    sentAt: message.sentAt
      ? {
          seconds: BigInt(Math.floor(message.sentAt.getTime() / 1000)),
          nanos: (message.sentAt.getTime() % 1000) * 1000000,
        }
      : undefined,
    deliveredAt: recipient.deliveredAt
      ? {
          seconds: BigInt(Math.floor(recipient.deliveredAt.getTime() / 1000)),
          nanos: (recipient.deliveredAt.getTime() % 1000) * 1000000,
        }
      : undefined,
    readAt: recipient.readAt
      ? {
          seconds: BigInt(Math.floor(recipient.readAt.getTime() / 1000)),
          nanos: (recipient.readAt.getTime() % 1000) * 1000000,
        }
      : undefined,
    createdAt: {
      seconds: BigInt(Math.floor(message.createdAt.getTime() / 1000)),
      nanos: (message.createdAt.getTime() % 1000) * 1000000,
    },
    updatedAt: {
      seconds: BigInt(Math.floor(message.updatedAt.getTime() / 1000)),
      nanos: (message.updatedAt.getTime() % 1000) * 1000000,
    },
    attachments: message.attachments?.map(mapToMessageAttachment) || [],
  });
};

/**
 * メッセージ一覧取得サービス
 */
export const listMessages = (params: messageRepository.ListMessagesParams) => {
  return messageRepository.listMessages(params).map(({ messages, totalCount, totalPages }) => ({
    messages: messages.map(mapToMessage),
    totalCount,
    totalPages,
  }));
};

/**
 * メッセージ詳細取得サービス（public_idで検索）
 */
export const getMessage = (publicId: string) => {
  return messageRepository.getMessageByPublicId(publicId).map(mapToMessage);
};

/**
 * 配信状況取得サービス
 */
export const getDeliveryStatus = (params: messageRecipientRepository.GetDeliveryStatusParams) => {
  return messageRecipientRepository.getDeliveryStatusWithMemberInfo(params).map(({ recipients, totalCount, totalPages }) => ({
    recipients: recipients.map(mapToMessageRecipientStatus),
    totalCount,
    totalPages,
  }));
};

/**
 * 会員別メッセージ一覧取得サービス
 */
export const listMemberMessages = (params: messageRecipientRepository.ListMemberMessagesParams) => {
  return messageRecipientRepository.listMemberMessages(params).map(({ recipients, totalCount, totalPages }) => ({
    messages: recipients.map(mapToMemberMessage),
    totalCount,
    totalPages,
  }));
};
