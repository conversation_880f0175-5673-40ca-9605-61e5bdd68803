import { MessageType, MessageStatus } from '@hami/prisma';
import { listMessages, getMessageById, MessageNotFoundError } from './message_repository';
import { messageFactory } from '../../test_utils/factories/message_factory';

describe('message_repository', () => {
  describe('listMessages', () => {
    it('メッセージ一覧を取得できること', async () => {
      // テストデータ作成
      const message1 = await messageFactory.create({
        title: 'テストメッセージ1',
        messageType: MessageType.INDIVIDUAL,
        status: MessageStatus.SENT,
      });

      const message2 = await messageFactory.create({
        title: 'テストメッセージ2',
        messageType: MessageType.BROADCAST,
        status: MessageStatus.SENT,
      });

      const result = await listMessages({
        page: 1,
        pageSize: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.messages).toHaveLength(2);
        expect(result.value.totalCount).toBe(2);
        expect(result.value.totalPages).toBe(1);

        // 最新のメッセージが最初に来ることを確認
        expect(result.value.messages[0].messageId).toBe(message2.messageId);
        expect(result.value.messages[1].messageId).toBe(message1.messageId);
      }
    });

    it('メッセージタイプでフィルタリングできること', async () => {
      await messageFactory.create({
        title: '個別メッセージ',
        messageType: MessageType.INDIVIDUAL,
      });

      await messageFactory.create({
        title: '一斉送信メッセージ',
        messageType: MessageType.BROADCAST,
      });

      const result = await listMessages({
        page: 1,
        pageSize: 10,
        messageType: 'INDIVIDUAL',
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.messages).toHaveLength(1);
        expect(result.value.messages[0].title).toBe('個別メッセージ');
        expect(result.value.messages[0].messageType).toBe(MessageType.INDIVIDUAL);
      }
    });

    it('ステータスでフィルタリングできること', async () => {
      await messageFactory.create({
        title: '送信済みメッセージ',
        status: MessageStatus.SENT,
      });

      await messageFactory.create({
        title: '下書きメッセージ',
        status: MessageStatus.DRAFT,
      });

      const result = await listMessages({
        page: 1,
        pageSize: 10,
        status: 'DRAFT',
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.messages).toHaveLength(1);
        expect(result.value.messages[0].title).toBe('下書きメッセージ');
        expect(result.value.messages[0].status).toBe(MessageStatus.DRAFT);
      }
    });

    it('検索クエリでフィルタリングできること', async () => {
      await messageFactory.create({
        title: '重要なお知らせ',
        body: 'システムメンテナンスのお知らせです',
      });

      await messageFactory.create({
        title: '一般的なメッセージ',
        body: '通常のメッセージです',
      });

      // タイトルで検索
      const titleResult = await listMessages({
        page: 1,
        pageSize: 10,
        searchQuery: '重要',
      });

      expect(titleResult.isOk()).toBe(true);
      if (titleResult.isOk()) {
        expect(titleResult.value.messages).toHaveLength(1);
        expect(titleResult.value.messages[0].title).toBe('重要なお知らせ');
      }

      // 本文で検索
      const bodyResult = await listMessages({
        page: 1,
        pageSize: 10,
        searchQuery: 'メンテナンス',
      });

      expect(bodyResult.isOk()).toBe(true);
      if (bodyResult.isOk()) {
        expect(bodyResult.value.messages).toHaveLength(1);
        expect(bodyResult.value.messages[0].title).toBe('重要なお知らせ');
      }
    });

    it('ページネーションが正しく動作すること', async () => {
      // 5件のテストデータ作成
      for (let i = 1; i <= 5; i++) {
        await messageFactory.create({
          title: `テストメッセージ${i}`,
        });
      }

      // 1ページ目（2件ずつ）
      const page1Result = await listMessages({
        page: 1,
        pageSize: 2,
      });

      expect(page1Result.isOk()).toBe(true);
      if (page1Result.isOk()) {
        expect(page1Result.value.messages).toHaveLength(2);
        expect(page1Result.value.totalCount).toBe(5);
        expect(page1Result.value.totalPages).toBe(3);
      }

      // 2ページ目
      const page2Result = await listMessages({
        page: 2,
        pageSize: 2,
      });

      expect(page2Result.isOk()).toBe(true);
      if (page2Result.isOk()) {
        expect(page2Result.value.messages).toHaveLength(2);
        expect(page2Result.value.totalCount).toBe(5);
        expect(page2Result.value.totalPages).toBe(3);
      }
    });

    it('添付ファイル付きメッセージを取得できること', async () => {
      const { message } = await messageFactory.createWithAttachments(
        {
          title: '添付ファイル付きメッセージ',
        },
        2
      );

      const result = await listMessages({
        page: 1,
        pageSize: 10,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.messages).toHaveLength(1);
        expect(result.value.messages[0].messageId).toBe(message.messageId);
        expect(result.value.messages[0].attachments).toHaveLength(2);

        // 添付ファイルの順序確認
        expect(result.value.messages[0].attachments[0].displayOrder).toBe(0);
        expect(result.value.messages[0].attachments[1].displayOrder).toBe(1);
      }
    });

    it('メッセージが存在しない場合は空の配列を返すこと', async () => {
      const result = await listMessages({
        page: 1,
        pageSize: 10,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.messages).toHaveLength(0);
        expect(result.value.totalCount).toBe(0);
        expect(result.value.totalPages).toBe(0);
      }
    });
  });

  describe('getMessageById', () => {
    it('メッセージ詳細を取得できること', async () => {
      const { message } = await messageFactory.createWithAttachments(
        {
          title: 'テストメッセージ',
          body: 'テストメッセージの本文',
        },
        1
      );

      const result = await getMessageById(message.messageId);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.messageId).toBe(message.messageId);
        expect(result.value.title).toBe('テストメッセージ');
        expect(result.value.body).toBe('テストメッセージの本文');
        expect(result.value.attachments).toHaveLength(1);
      }
    });

    it('存在しないメッセージIDの場合はエラーを返すこと', async () => {
      const result = await getMessageById(99999);

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(MessageNotFoundError);
        expect(result.error.message).toBe('Message with ID 99999 not found');
      }
    });
  });
});
