import { err, ok, ResultAsync } from 'neverthrow';
import { Prisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class MembershipApplicationDocumentGroupReviewLogNotFoundError extends Error {
  readonly name = 'MembershipApplicationDocumentGroupReviewLogNotFoundError';
}

/**
 * membershipApplicationIdでMembershipApplicationDocumentGroupReviewLogレコードを検索
 * @param membershipApplicationId 検索するmembershipApplicationId
 * @returns 見つかった場合はMembershipApplicationDocumentGroupReviewLogレコード、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const findMembershipApplicationDocumentGroupReviewLogByMembershipApplicationId = ({
  membershipApplicationId,
}: {
  membershipApplicationId: number;
}) => {
  return ResultAsync.fromPromise(
    client.membershipApplicationDocumentGroupReviewLog.findFirst({
      where: {
        membershipApplication: {
          membershipApplicationId,
        },
      },
      include: {
        membershipApplication: true,
      },
    }),
    () => new DatabaseError('Failed to query membership application document group review log')
  ).andThen((record) => {
    if (!record) {
      return err(new MembershipApplicationDocumentGroupReviewLogNotFoundError());
    }
    return ok(record);
  });
};

/**
 * MembershipApplicationDocumentGroupReviewLogレコードを作成
 * @param data 作成するデータ (membershipApplicationIdを含む)
 * @returns 作成されたMembershipApplicationDocumentGroupReviewLog
 */
export const createMembershipApplicationDocumentGroupReviewLog = (
  data: Omit<Prisma.MembershipApplicationDocumentGroupReviewLogCreateInput, 'membershipApplication' | 'timestamp'> & {
    membershipApplicationId: number;
  }
) => {
  const { membershipApplicationId, ...rest } = data;
  return ResultAsync.fromPromise(
    client.membershipApplicationDocumentGroupReviewLog.create({
      data: {
        ...rest,
        timestamp: new Date(),
        membershipApplication: { connect: { membershipApplicationId } },
      },
    }),
    () => new DatabaseError('Failed to create membership application document group review log')
  );
};
