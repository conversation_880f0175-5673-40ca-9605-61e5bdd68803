import { MemberFactory } from '@core-test/factories/member_factory';
import { MemberClaimAndPay } from '@hami/prisma';
import { toNumber } from '@core-api/utils/decimal';
import {
  createMonthlyGmoProcessingBatch,
  getMonthlyGmoProcessingBatchByTargetDate,
  updateMonthlyGmoProcessingBatchStatus,
  createMonthlyGmoProcessingRecord,
  updateMonthlyGmoProcessingRecordStatus,
  createMonthlyGmoProcessingLog,
  listMonthlyGmoProcessingRecordsByBatchId,
  listPendingMonthlyGmoProcessingRecords,
  updateMonthlyGmoProcessingBatchStats,
  incrementMonthlyGmoProcessingRecordRetryCount,
  type MonthlyGmoProcessingRecordWithRelations,
} from './monthly_gmo_processing_repository';

describe('monthly_gmo_processing_repository', () => {
  let member: Awaited<ReturnType<typeof MemberFactory.create>>;
  let memberClaimAndPay: MemberClaimAndPay;
  const targetDate = new Date('2024-07-01');

  beforeEach(async () => {
    // テスト用会員を作成
    member = await MemberFactory.create();

    // テスト用請求・支払いデータを作成
    memberClaimAndPay = await vPrisma.client.memberClaimAndPay.create({
      data: {
        memberId: member.memberId,
        occurredDate: targetDate,
        claimAmount: 10000,
        payAmount: 5000,
      },
    });
  });

  describe('createMonthlyGmoProcessingBatch', () => {
    it('月締めGMO処理バッチが作成できること', async () => {
      const result = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const batch = result.value;
        expect(batch.targetDate).toEqual(targetDate);
        expect(batch.totalTransferCount).toBe(5);
        expect(batch.totalRemittanceCount).toBe(3);
        expect(batch.status).toBe('PENDING');
        expect(batch.completedTransferCount).toBe(0);
        expect(batch.completedRemittanceCount).toBe(0);
        expect(batch.failedTransferCount).toBe(0);
        expect(batch.failedRemittanceCount).toBe(0);
      }
    });

    it('同じ対象日で複数回作成しようとするとエラーになること', async () => {
      // 最初のバッチを作成
      const firstResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(firstResult.isOk()).toBe(true);

      // 同じ日付で再度作成を試行
      const secondResult = await createMonthlyGmoProcessingBatch(targetDate, 2, 1);
      expect(secondResult.isErr()).toBe(true);
    });
  });

  describe('getMonthlyGmoProcessingBatchByTargetDate', () => {
    it('対象日でバッチが取得できること', async () => {
      // バッチを作成
      const createResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(createResult.isOk()).toBe(true);

      // 作成したバッチを取得
      const getResult = await getMonthlyGmoProcessingBatchByTargetDate(targetDate);
      expect(getResult.isOk()).toBe(true);

      if (getResult.isOk() && createResult.isOk()) {
        const batch = getResult.value;
        expect(batch).not.toBeNull();
        expect(batch!.monthlyGmoProcessingBatchId).toBe(createResult.value.monthlyGmoProcessingBatchId);
        expect(batch!.targetDate).toEqual(targetDate);
        expect(batch!.totalTransferCount).toBe(5);
        expect(batch!.totalRemittanceCount).toBe(3);
      }
    });

    it('存在しない対象日の場合はnullが返されること', async () => {
      const nonExistentDate = new Date('2025-12-31');
      const result = await getMonthlyGmoProcessingBatchByTargetDate(nonExistentDate);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBeNull();
      }
    });
  });

  describe('updateMonthlyGmoProcessingBatchStatus', () => {
    it('バッチの状態が更新できること', async () => {
      // バッチを作成
      const createResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(createResult.isOk()).toBe(true);

      if (createResult.isOk()) {
        const batchId = createResult.value.monthlyGmoProcessingBatchId;

        // 状態をRUNNINGに更新
        const updateResult = await updateMonthlyGmoProcessingBatchStatus(batchId, 'RUNNING');
        expect(updateResult.isOk()).toBe(true);

        if (updateResult.isOk()) {
          const updatedBatch = updateResult.value;
          expect(updatedBatch.status).toBe('RUNNING');
          expect(updatedBatch.startedAt).not.toBeNull();
        }
      }
    });

    it('エラーメッセージ付きで状態が更新できること', async () => {
      const createResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(createResult.isOk()).toBe(true);

      if (createResult.isOk()) {
        const batchId = createResult.value.monthlyGmoProcessingBatchId;
        const errorMessage = 'テストエラーメッセージ';

        const updateResult = await updateMonthlyGmoProcessingBatchStatus(batchId, 'FAILED', errorMessage);
        expect(updateResult.isOk()).toBe(true);

        if (updateResult.isOk()) {
          const updatedBatch = updateResult.value;
          expect(updatedBatch.status).toBe('FAILED');
          expect(updatedBatch.errorMessage).toBe(errorMessage);
          expect(updatedBatch.completedAt).not.toBeNull();
        }
      }
    });
  });

  describe('createMonthlyGmoProcessingRecord', () => {
    it('月締めGMO処理記録が作成できること', async () => {
      // バッチを作成
      const batchResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        const batchId = batchResult.value.monthlyGmoProcessingBatchId;

        // 処理記録を作成
        const recordResult = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'BANK_TRANSFER',
          10000
        );

        expect(recordResult.isOk()).toBe(true);
        if (recordResult.isOk()) {
          const record = recordResult.value;
          expect(record.monthlyGmoProcessingBatchId).toBe(batchId);
          expect(record.memberId).toBe(member.memberId);
          expect(record.memberClaimAndPayId).toBe(memberClaimAndPay.memberClaimAndPayId);
          expect(record.processingType).toBe('BANK_TRANSFER');
          expect(toNumber(record.amount)).toBe(10000);
          expect(record.status).toBe('PENDING');
          expect(record.retryCount).toBe(0);
        }
      }
    });
  });

  describe('updateMonthlyGmoProcessingRecordStatus', () => {
    it('処理記録の状態が更新できること', async () => {
      // バッチと処理記録を作成
      const batchResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        const batchId = batchResult.value.monthlyGmoProcessingBatchId;

        const recordResult = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'BANK_TRANSFER',
          10000
        );
        expect(recordResult.isOk()).toBe(true);

        if (recordResult.isOk()) {
          const recordId = recordResult.value.monthlyGmoProcessingRecordId;

          // GMOデータ付きで状態を更新
          const gmoData = {
            gmoOrderId: 'ORDER123456789',
            gmoAccessId: 'ACCESS123',
            gmoAccessPass: 'PASS123',
            gmoTransactionId: 'TRANS123456789',
            targetDate: new Date('2024-07-27'),
          };

          const updateResult = await updateMonthlyGmoProcessingRecordStatus(recordId, 'COMPLETED', gmoData);

          expect(updateResult.isOk()).toBe(true);
          if (updateResult.isOk()) {
            const updatedRecord = updateResult.value;
            expect(updatedRecord.status).toBe('COMPLETED');
            expect(updatedRecord.gmoOrderId).toBe(gmoData.gmoOrderId);
            expect(updatedRecord.gmoAccessId).toBe(gmoData.gmoAccessId);
            expect(updatedRecord.gmoAccessPass).toBe(gmoData.gmoAccessPass);
            expect(updatedRecord.gmoTransactionId).toBe(gmoData.gmoTransactionId);
            expect(updatedRecord.targetDate).toEqual(gmoData.targetDate);
            expect(updatedRecord.processedAt).not.toBeNull();
          }
        }
      }
    });
  });

  describe('incrementMonthlyGmoProcessingRecordRetryCount', () => {
    it('リトライ回数が増加できること', async () => {
      // バッチと処理記録を作成
      const batchResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        const batchId = batchResult.value.monthlyGmoProcessingBatchId;

        const recordResult = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'BANK_TRANSFER',
          10000
        );
        expect(recordResult.isOk()).toBe(true);

        if (recordResult.isOk()) {
          const recordId = recordResult.value.monthlyGmoProcessingRecordId;

          // リトライ回数を増加
          const incrementResult = await incrementMonthlyGmoProcessingRecordRetryCount(recordId);
          expect(incrementResult.isOk()).toBe(true);

          if (incrementResult.isOk()) {
            const updatedRecord = incrementResult.value;
            expect(updatedRecord.retryCount).toBe(1);
          }

          // 再度増加
          const secondIncrementResult = await incrementMonthlyGmoProcessingRecordRetryCount(recordId);
          expect(secondIncrementResult.isOk()).toBe(true);

          if (secondIncrementResult.isOk()) {
            const updatedRecord = secondIncrementResult.value;
            expect(updatedRecord.retryCount).toBe(2);
          }
        }
      }
    });
  });

  describe('createMonthlyGmoProcessingLog', () => {
    it('処理ログが作成できること', async () => {
      // バッチと処理記録を作成
      const batchResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        const batchId = batchResult.value.monthlyGmoProcessingBatchId;

        const recordResult = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'BANK_TRANSFER',
          10000
        );
        expect(recordResult.isOk()).toBe(true);

        if (recordResult.isOk()) {
          const recordId = recordResult.value.monthlyGmoProcessingRecordId;

          // ログを作成
          const logResult = await createMonthlyGmoProcessingLog(
            recordId,
            'INFO',
            'テスト処理が開始されました',
            '/payment/EntryTranBankaccount.idPass',
            '{"OrderID":"ORDER123"}',
            '{"AccessID":"ACCESS123"}',
            'PENDING',
            'ENTRY_REGISTERED'
          );

          expect(logResult.isOk()).toBe(true);
          if (logResult.isOk()) {
            const log = logResult.value;
            expect(log.monthlyGmoProcessingRecordId).toBe(recordId);
            expect(log.logLevel).toBe('INFO');
            expect(log.message).toBe('テスト処理が開始されました');
            expect(log.apiEndpoint).toBe('/payment/EntryTranBankaccount.idPass');
            expect(log.requestData).toBe('{"OrderID":"ORDER123"}');
            expect(log.responseData).toBe('{"AccessID":"ACCESS123"}');
            expect(log.fromStatus).toBe('PENDING');
            expect(log.toStatus).toBe('ENTRY_REGISTERED');
          }
        }
      }
    });
  });

  describe('listMonthlyGmoProcessingRecordsByBatchId', () => {
    it('バッチIDで処理記録一覧が取得できること', async () => {
      // バッチを作成
      const batchResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        const batchId = batchResult.value.monthlyGmoProcessingBatchId;

        // 複数の処理記録を作成
        const record1Result = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'BANK_TRANSFER',
          10000
        );
        expect(record1Result.isOk()).toBe(true);

        const record2Result = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'REMITTANCE',
          5000
        );
        expect(record2Result.isOk()).toBe(true);

        // 記録一覧を取得
        const listResult = await listMonthlyGmoProcessingRecordsByBatchId(batchId);
        expect(listResult.isOk()).toBe(true);

        if (listResult.isOk()) {
          const records: MonthlyGmoProcessingRecordWithRelations[] = listResult.value;
          expect(records).toHaveLength(2);
          expect(records[0].processingType).toBe('BANK_TRANSFER');
          expect(records[1].processingType).toBe('REMITTANCE');
          // リレーションが含まれていることを確認
          expect(records[0].member).toBeDefined();
          expect(records[0].memberClaimAndPay).toBeDefined();
        }
      }
    });

    it('存在しないバッチIDの場合は空配列が返されること', async () => {
      const nonExistentBatchId = 999999;
      const result = await listMonthlyGmoProcessingRecordsByBatchId(nonExistentBatchId);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(0);
      }
    });
  });

  describe('listPendingMonthlyGmoProcessingRecords', () => {
    it('処理待ち状態の記録が取得できること', async () => {
      // バッチを作成
      const batchResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        const batchId = batchResult.value.monthlyGmoProcessingBatchId;

        // PENDING状態の記録を作成
        const pendingRecordResult = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'BANK_TRANSFER',
          10000
        );
        expect(pendingRecordResult.isOk()).toBe(true);

        // COMPLETED状態の記録を作成
        const completedRecordResult = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'REMITTANCE',
          5000
        );
        expect(completedRecordResult.isOk()).toBe(true);

        if (completedRecordResult.isOk()) {
          // COMPLETED状態に更新
          await updateMonthlyGmoProcessingRecordStatus(completedRecordResult.value.monthlyGmoProcessingRecordId, 'COMPLETED');
        }

        // PENDING状態の記録のみを取得
        const pendingResult = await listPendingMonthlyGmoProcessingRecords(batchId);
        expect(pendingResult.isOk()).toBe(true);

        if (pendingResult.isOk()) {
          const pendingRecords = pendingResult.value;
          expect(pendingRecords).toHaveLength(1);
          expect(pendingRecords[0].status).toBe('PENDING');
          expect(pendingRecords[0].processingType).toBe('BANK_TRANSFER');
        }
      }
    });

    it('処理種別でフィルタリングできること', async () => {
      // バッチを作成
      const batchResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        const batchId = batchResult.value.monthlyGmoProcessingBatchId;

        // 振替記録を作成
        const transferRecordResult = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'BANK_TRANSFER',
          10000
        );
        expect(transferRecordResult.isOk()).toBe(true);

        // 送金記録を作成
        const remittanceRecordResult = await createMonthlyGmoProcessingRecord(
          batchId,
          member.memberId,
          memberClaimAndPay.memberClaimAndPayId,
          'REMITTANCE',
          5000
        );
        expect(remittanceRecordResult.isOk()).toBe(true);

        // 振替のみを取得
        const transferResult = await listPendingMonthlyGmoProcessingRecords(batchId, 'BANK_TRANSFER');
        expect(transferResult.isOk()).toBe(true);

        if (transferResult.isOk()) {
          const transferRecords = transferResult.value;
          expect(transferRecords).toHaveLength(1);
          expect(transferRecords[0].processingType).toBe('BANK_TRANSFER');
        }

        // 送金のみを取得
        const remittanceResult = await listPendingMonthlyGmoProcessingRecords(batchId, 'REMITTANCE');
        expect(remittanceResult.isOk()).toBe(true);

        if (remittanceResult.isOk()) {
          const remittanceRecords = remittanceResult.value;
          expect(remittanceRecords).toHaveLength(1);
          expect(remittanceRecords[0].processingType).toBe('REMITTANCE');
        }
      }
    });
  });

  describe('updateMonthlyGmoProcessingBatchStats', () => {
    it('バッチの処理統計が更新できること', async () => {
      // バッチを作成
      const batchResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        const batchId = batchResult.value.monthlyGmoProcessingBatchId;

        // 統計を更新
        const updateResult = await updateMonthlyGmoProcessingBatchStats(
          batchId,
          3, // completedTransferCount
          2, // completedRemittanceCount
          1, // failedTransferCount
          0 // failedRemittanceCount
        );

        expect(updateResult.isOk()).toBe(true);
        if (updateResult.isOk()) {
          const updatedBatch = updateResult.value;
          expect(updatedBatch.completedTransferCount).toBe(3);
          expect(updatedBatch.completedRemittanceCount).toBe(2);
          expect(updatedBatch.failedTransferCount).toBe(1);
          expect(updatedBatch.failedRemittanceCount).toBe(0);
        }
      }
    });

    it('一部の統計のみ更新できること', async () => {
      // バッチを作成
      const batchResult = await createMonthlyGmoProcessingBatch(targetDate, 5, 3);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        const batchId = batchResult.value.monthlyGmoProcessingBatchId;

        // 完了数のみ更新
        const updateResult = await updateMonthlyGmoProcessingBatchStats(
          batchId,
          2, // completedTransferCount
          1 // completedRemittanceCount
          // failedTransferCount, failedRemittanceCountは未指定
        );

        expect(updateResult.isOk()).toBe(true);
        if (updateResult.isOk()) {
          const updatedBatch = updateResult.value;
          expect(updatedBatch.completedTransferCount).toBe(2);
          expect(updatedBatch.completedRemittanceCount).toBe(1);
          expect(updatedBatch.failedTransferCount).toBe(0); // 初期値のまま
          expect(updatedBatch.failedRemittanceCount).toBe(0); // 初期値のまま
        }
      }
    });
  });
});
