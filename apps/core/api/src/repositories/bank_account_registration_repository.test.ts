import { MemberFactory, UserFactory, MembershipApplicationFactory, MailVerificationFactory } from '@core-test/index';
import { listBankAccountRegistrationsForAdmin, getBankAccountRegistrationsByMemberId } from './bank_account_registration_repository';

// vPrisma.clientを使用（setup.tsでモックされているため）
const prisma = vPrisma.client;

describe('bank_account_registration_repository', () => {
  describe('listBankAccountRegistrationsForAdmin', () => {
    it('口座登録一覧を正常に取得できる', async () => {
      // ===== Arrange =====
      // テスト用の会員とユーザーを作成
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      });

      // 口座登録を作成
      const registration1 = await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: '0001',
          registrationStatus: 'SUCCESS',
          isActive: true,
        },
      });

      const registration2 = await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: '0002',
          registrationStatus: 'FAIL',
          isActive: false,
        },
      });

      // ===== Act =====
      const result = await listBankAccountRegistrationsForAdmin({
        page: 1,
        limit: 10,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const data = result.value;
        expect(data.registrations).toHaveLength(2);
        expect(data.totalCount).toBe(2);
        expect(data.currentPage).toBe(1);
        expect(data.totalPages).toBe(1);

        // 作成日時の降順でソートされていることを確認
        expect(data.registrations[0].bankAccountRegistrationId).toBe(registration2.bankAccountRegistrationId);
        expect(data.registrations[1].bankAccountRegistrationId).toBe(registration1.bankAccountRegistrationId);
      }
    });

    it('ステータスフィルターが正常に動作する', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      });

      await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: '0001',
          registrationStatus: 'SUCCESS',
          isActive: true,
        },
      });

      await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: '0002',
          registrationStatus: 'FAIL',
          isActive: false,
        },
      });

      // ===== Act =====
      const result = await listBankAccountRegistrationsForAdmin({
        statusFilter: ['SUCCESS'],
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const data = result.value;
        expect(data.registrations).toHaveLength(1);
        expect(data.registrations[0].registrationStatus).toBe('SUCCESS');
      }
    });

    it('検索クエリが正常に動作する', async () => {
      // ===== Arrange =====
      const mailVerification1 = await MailVerificationFactory.create();
      const application1 = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification1.mailVerificationId } },
      });
      const user1 = await UserFactory.create();
      const member1 = await MemberFactory.create({
        user: { connect: { userId: user1.userId } },
        membershipApplication: { connect: { membershipApplicationId: application1.membershipApplicationId } },
        firstName: '太郎',
        lastName: '田中',
      });

      const mailVerification2 = await MailVerificationFactory.create();
      const application2 = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification2.mailVerificationId } },
      });
      const user2 = await UserFactory.create();
      const member2 = await MemberFactory.create({
        user: { connect: { userId: user2.userId } },
        membershipApplication: { connect: { membershipApplicationId: application2.membershipApplicationId } },
        firstName: '花子',
        lastName: '佐藤',
      });

      await prisma.bankAccountRegistration.create({
        data: {
          memberId: member1.memberId,
          bankCode: '0001',
          registrationStatus: 'SUCCESS',
          isActive: true,
        },
      });

      await prisma.bankAccountRegistration.create({
        data: {
          memberId: member2.memberId,
          bankCode: '0002',
          registrationStatus: 'SUCCESS',
          isActive: true,
        },
      });

      // ===== Act =====
      const result = await listBankAccountRegistrationsForAdmin({
        searchQuery: '田中',
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const data = result.value;
        expect(data.registrations).toHaveLength(1);
        expect(data.registrations[0].member.lastName).toBe('田中');
      }
    });

    it('ページネーションが正常に動作する', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      });

      // 3件の口座登録を作成
      for (let i = 0; i < 3; i++) {
        await prisma.bankAccountRegistration.create({
          data: {
            memberId: member.memberId,
            bankCode: `000${i + 1}`,
            registrationStatus: 'SUCCESS',
            isActive: i === 0, // 最初のもののみアクティブ
          },
        });
      }

      // ===== Act =====
      const result = await listBankAccountRegistrationsForAdmin({
        page: 2,
        limit: 2,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const data = result.value;
        expect(data.registrations).toHaveLength(1); // 3件中2ページ目なので1件
        expect(data.totalCount).toBe(3);
        expect(data.currentPage).toBe(2);
        expect(data.totalPages).toBe(2);
      }
    });
  });

  describe('getBankAccountRegistrationsByMemberId', () => {
    it('会員の口座登録詳細を正常に取得できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      });

      const registration = await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: '0001',
          registrationStatus: 'SUCCESS',
          isActive: true,
        },
      });

      // ログを作成
      await prisma.bankAccountRegistrationLog.create({
        data: {
          bankAccountRegistrationId: registration.bankAccountRegistrationId,
          fromStatus: null,
          toStatus: 'ENTRY',
          triggeredBy: 'api',
        },
      });

      // ===== Act =====
      const result = await getBankAccountRegistrationsByMemberId({
        memberId: member.memberId,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const data = result.value;
        expect(data.member.memberId).toBe(member.memberId);
        expect(data.member.memberName).toBe(`${member.lastName} ${member.firstName}`);
        expect(data.registrations).toHaveLength(1);
        expect(data.activeRegistration).not.toBeNull();
        expect(data.activeRegistration?.isActive).toBe(true);
        // 口座登録が取得されていることを確認
        expect(data.registrations[0].bankAccountRegistrationId).toBe(registration.bankAccountRegistrationId);
      }
    });

    it('存在しない会員IDの場合はエラーを返す', async () => {
      // ===== Act =====
      const result = await getBankAccountRegistrationsByMemberId({
        memberId: 99999,
      });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        // BankAccountRegistrationNotFoundErrorが正しく保持されていることを確認
        expect(result.error.message).toBe('Member not found');
      }
    });
  });
});
