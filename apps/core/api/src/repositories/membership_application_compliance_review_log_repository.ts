import { err, ok, ResultAsync } from 'neverthrow';
import { Prisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class MembershipApplicationComplianceReviewLogNotFoundError extends Error {
  readonly name = 'MembershipApplicationComplianceReviewLogNotFoundError';
}

/**
 * membershipApplicationIdでMembershipApplicationComplianceReviewLogレコードを検索
 */
export const findMembershipApplicationComplianceReviewLogByMembershipApplicationId = ({
  membershipApplicationId,
}: {
  membershipApplicationId: number;
}) => {
  return ResultAsync.fromPromise(
    client.membershipApplicationComplianceReviewLog.findFirst({
      where: {
        membershipApplication: {
          membershipApplicationId,
        },
      },
      include: {
        membershipApplication: true,
      },
    }),
    () => new DatabaseError('Failed to query membership application compliance review log')
  ).andThen((record) => {
    if (!record) {
      return err(new MembershipApplicationComplianceReviewLogNotFoundError());
    }
    return ok(record);
  });
};

/**
 * MembershipApplicationComplianceReviewLogレコードを作成
 */
export const createMembershipApplicationComplianceReviewLog = (
  data: Omit<Prisma.MembershipApplicationComplianceReviewLogCreateInput, 'membershipApplication' | 'timestamp'> & {
    membershipApplicationId: number;
  }
) => {
  const { membershipApplicationId, ...rest } = data;
  return ResultAsync.fromPromise(
    client.membershipApplicationComplianceReviewLog.create({
      data: {
        ...rest,
        timestamp: new Date(),
        membershipApplication: { connect: { membershipApplicationId } },
      },
    }),
    () => new DatabaseError('Failed to create membership application compliance review log')
  );
};

