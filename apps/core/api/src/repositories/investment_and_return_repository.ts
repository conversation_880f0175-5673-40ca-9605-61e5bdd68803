import { ResultAsync } from 'neverthrow';
import { InvestmentAndReturn, InvestmentAndReturnInvestment, InvestmentAndReturnReturn, ReturnCategory, InvestmentContract, Member, Horse } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

// 馬・会員ごとの InvestmentAndReturn を取得
export function listInvestmentAndReturnByHorseIdAndMemberId(horseId: number, memberId: number): ResultAsync<InvestmentAndReturn[], Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturn.findMany({
      where: {
        memberId: memberId,
        horseId: horseId,
      },
      orderBy: {
        createdDate: 'asc',
      },
    }),
    (error) => new DatabaseError(`Failed to list investment and returns: ${error}`)
  );
}

// 馬・会員ごとの InvestmentAndReturnInvestment を取得
// investmentAndReturnIdを参照しているので、結合してhorseId, memberIdを参照する
export function listInvestmentAndReturnInvestmentByHorseIdAndMemberId(
  horseId: number,
  memberId: number
): ResultAsync<InvestmentAndReturnInvestment[], Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturnInvestment.findMany({
      where: { investmentAndReturn: { memberId: memberId, horseId: horseId } },
      include: {
        investmentAndReturn: true,
      },
      orderBy: {
        investmentAndReturn: {
          createdDate: 'asc',
        },
      },
    }),
    (error) => new DatabaseError(`Failed to list investment and return investment: ${error}`)
  );
}

// 馬・会員ごとのInvestmentAndReturnReturnを取得
export function listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(
  horseId: number,
  memberId: number,
  returnCategory: ReturnCategory
): ResultAsync<InvestmentAndReturnReturn[], Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturnReturn.findMany({
      where: { investmentAndReturn: { memberId: memberId, horseId: horseId }, returnCategory: returnCategory },
      include: {
        investmentAndReturn: true,
      },
      orderBy: {
        investmentAndReturn: {
          createdDate: 'asc',
        },
      },
    }),
    (error) => new DatabaseError(`Failed to list investment and return return: ${error}`)
  );
}

// 会員IDと発生日に紐づくInvestmentAndReturnを取得
// InvestmentAndReturnInvestmentとInvestmentAndReturnReturnとhorseを結合して取得
export function listInvestmentAndReturnByMemberIdAndOccurredDate(memberId: number, occurredDate: Date): ResultAsync<(InvestmentAndReturn & {
  horse: Horse;
  investment: InvestmentAndReturnInvestment | null;
  returns: InvestmentAndReturnReturn[];
})[], Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturn.findMany({ where: { memberId: memberId, createdDate: occurredDate }, include: { investment: true, returns: true, horse: true } }),
    (error) => new DatabaseError(`Failed to list investment and return: ${error}`)
  );
}
// investmentContractIdに関して、指定日以前の競走馬出資金（racehorseInvestment）の合計額を返却する
export function getTotalRacehorseInvestmentByHorseIdAndMemberIdAndDate(
  horseId: number,
  memberId: number,
  lteDate: Date
): ResultAsync<number, Error> {
  // investmentAndReturnInvestmentはinvestmentAndReturnのidを参照しているので、結合してhorseId, memberId, createdDateを参照する
  return ResultAsync.fromPromise(
    client.investmentAndReturnInvestment.aggregate({
      where: { investmentAndReturn: { memberId: memberId, horseId: horseId, createdDate: { lte: lteDate } } },
      _sum: { racehorseInvestment: true },
    }),
    (error) => new DatabaseError(`Failed to get total racehorse investment equivalent: ${error}`)
  ).map((result) => result?._sum?.racehorseInvestment || 0);
}

// 馬・会員に関して、指定日以前の保険料出資金（insuranceInvestment）の合計額を返却する
export function getTotalInsuranceInvestmentByHorseIdAndMemberIdAndDate(
  horseId: number,
  memberId: number,
  lteDate: Date
): ResultAsync<number, Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturnInvestment.aggregate({
      where: { investmentAndReturn: { memberId: memberId, horseId: horseId, createdDate: { lte: lteDate } } },
      _sum: { insuranceInvestment: true },
    }),
    (error) => new DatabaseError(`Failed to get total insurance investment: ${error}`)
  ).map((result) => result?._sum?.insuranceInvestment || 0);
}

// 馬・会員に関して、指定日以前の競走馬出資金（otherInvestment）の合計額を返却する
export function getTotalOtherInvestmentByHorseIdAndMemberIdAndDate(
  horseId: number,
  memberId: number,
  lteDate: Date
): ResultAsync<number, Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturnInvestment.aggregate({
      where: { investmentAndReturn: { memberId: memberId, horseId: horseId, createdDate: { lte: lteDate } } },
      _sum: { otherInvestment: true },
    }),
    (error) => new DatabaseError(`Failed to get total other investment: ${error}`)
  ).map((result) => result?._sum?.otherInvestment || 0);
}

// 馬・会員に関して、指定日以前の「分配対象額／うち出資返戻可能額」（distributionTargetAmountRefundable）の合計額を返却する
export function getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate(
  horseId: number,
  memberId: number,
  lteDate: Date,
  returnCategory: ReturnCategory
): ResultAsync<number, Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturnReturn.aggregate({
      where: {
        investmentAndReturn: { memberId: memberId, horseId: horseId, createdDate: { lte: lteDate } },
        returnCategory: returnCategory,
      },
      _sum: { distributionTargetAmountRefundable: true },
    }),
    (error) => new DatabaseError(`Failed to get total distribution target amount refundable: ${error}`)
  ).map((result) => result?._sum?.distributionTargetAmountRefundable || 0);
}

// 馬・会員に関して、ReturnCategoryに合致する分配金のリストを取得する
export function getInvestmentAndReturnReturnListByHorseIdAndMemberIdAndReturnCategory(
  horseId: number,
  memberId: number,
  returnCategory: ReturnCategory
): ResultAsync<InvestmentAndReturnReturn[], Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturnReturn.findMany({
      where: { investmentAndReturn: { memberId: memberId, horseId: horseId }, returnCategory: returnCategory },
      include: {
        investmentAndReturn: true,
      },
      orderBy: {
        investmentAndReturn: {
          createdDate: 'asc',
        },
      },
    }),
    (error) => new DatabaseError(`Failed to get investment and return return list: ${error}`)
  );
}

// 出資金・分配金を作成
export function createInvestmentAndReturn(
  horseId: number,
  memberId: number,
  createdDate: Date,
  progressedMonth: number,
  yearlyReturnTargetFlag: boolean,
  sharesNumber: number,
  runningCostInvestmentTotal: number,
  insuranceInvestmentTotal: number,
  otherInvestmentTotal: number,
  investmentTotal: number,
  racehorseBookValueEndOfLastMonth: number,
  investmentRefundPaidUpToLastMonth: number,
  organizerWithholdingTaxTotal: number,
  organizerWithholdingTaxCurrentMonthAddition: number,
  clubWithholdingTaxTotal: number,
  clubWithholdingTaxCurrentMonthAddition: number,
  billingAmount: number,
  paymentAmount: number
): ResultAsync<InvestmentAndReturn, Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturn.create({
      data: {
        horseId: horseId,
        memberId: memberId,
        createdDate: createdDate,
        progressedMonth: progressedMonth,
        yearlyReturnTargetFlag: yearlyReturnTargetFlag,
        sharesNumber: sharesNumber,
        runningCostInvestmentTotal: runningCostInvestmentTotal,
        insuranceInvestmentTotal: insuranceInvestmentTotal,
        otherInvestmentTotal: otherInvestmentTotal,
        investmentTotal: investmentTotal,
        racehorseBookValueEndOfLastMonth: racehorseBookValueEndOfLastMonth,
        investmentRefundPaidUpToLastMonth: investmentRefundPaidUpToLastMonth,
        organizerWithholdingTaxTotal: organizerWithholdingTaxTotal,
        organizerWithholdingTaxCurrentMonthAddition: organizerWithholdingTaxCurrentMonthAddition,
        clubWithholdingTaxTotal: clubWithholdingTaxTotal,
        clubWithholdingTaxCurrentMonthAddition: clubWithholdingTaxCurrentMonthAddition,
        billingAmount: billingAmount,
        paymentAmount: paymentAmount,
      },
    }),
    (error) => new DatabaseError(`Failed to create investment and return: ${error}`)
  );
}

// paymentAmountを更新
export function updatePaymentAmount(investmentAndReturnId: number, paymentAmount: number): ResultAsync<InvestmentAndReturn, Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturn.update({ where: { investmentAndReturnId: investmentAndReturnId }, data: { paymentAmount: paymentAmount } }),
    (error) => new DatabaseError(`Failed to update payment amount: ${error}`)
  );
}

// 出資金を作成
export function createInvestmentAndReturnInvestment(
  investmentAndReturnId: number,
  racehorseInvestment: number,
  runningCost: number,
  subsidy: number,
  insuranceInvestment: number,
  otherInvestment: number,
  discountAllocation: number,
  retroactiveRunningCost: number,
  runningCostInvestment: number,
  currentMonthInvestmentTotal: number,
  racehorseInvestmentEquivalent: number
): ResultAsync<InvestmentAndReturnInvestment, Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturnInvestment.create({
      data: {
        investmentAndReturnId: investmentAndReturnId,
        racehorseInvestment: racehorseInvestment,
        runningCost: runningCost,
        subsidy: subsidy,
        insuranceInvestment: insuranceInvestment,
        otherInvestment: otherInvestment,
        discountAllocation: discountAllocation,
        retroactiveRunningCost: retroactiveRunningCost,
        runningCostInvestment: runningCostInvestment,
        currentMonthInvestmentTotal: currentMonthInvestmentTotal,
        racehorseInvestmentEquivalent: racehorseInvestmentEquivalent,
      },
    }),
    (error) => new DatabaseError(`Failed to create investment and return investment: ${error}`)
  );
}

// 分配金を作成
export function createInvestmentAndReturnReturn(
  investmentAndReturnId: number,
  returnCategory: ReturnCategory,
  investmentRefundPaidUpToLastMonth: number | null,
  refundableInvestmentAmount: number,
  distributionTargetAmount: number,
  distributionTargetAmountRefundable: number,
  distributionTargetAmountProfit: number,
  distributionTargetAmountWithholdingTax: number,
  distributionAmount: number,
  refundableInvestmentAmountCarriedForward: number
): ResultAsync<InvestmentAndReturnReturn, Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturnReturn.create({
      data: {
        investmentAndReturnId: investmentAndReturnId,
        returnCategory: returnCategory,
        investmentRefundPaidUpToLastMonth: investmentRefundPaidUpToLastMonth,
        refundableInvestmentAmount: refundableInvestmentAmount,
        distributionTargetAmount: distributionTargetAmount,
        distributionTargetAmountRefundable: distributionTargetAmountRefundable,
        distributionTargetAmountProfit: distributionTargetAmountProfit,
        distributionTargetAmountWithholdingTax: distributionTargetAmountWithholdingTax,
        distributionAmount: distributionAmount,
        refundableInvestmentAmountCarriedForward: refundableInvestmentAmountCarriedForward,
      },
    }),
    (error) => new DatabaseError(`Failed to create investment and return return: ${error}`)
  );
}


// 馬ごとの出資と分配の作成状況を取得
export function getCreatedInvestmentAndReturnSharesSumByHorseId(horseId: number): ResultAsync<{ createdDate: Date, sharesSum: number }[], Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturn.findMany({
      where: {
        horseId: horseId,
      },
      include: {
        horse: true,
      },
      orderBy: {
        createdDate: 'asc',
      },
    }),
    (error) => new DatabaseError(`Failed to sum sharesNumber by createdDate: ${error}`)
  ).map((records) => {
    // createdDateごとにグループ化してsharesNumber合計
    const map = new Map<string, { createdDate: Date, sharesSum: number }>();
    records.forEach((record) => {
      const key = record.createdDate.toISOString();
      if (!record.horse) return;
      if (!map.has(key)) {
        map.set(key, { createdDate: record.createdDate, sharesSum: 0 });
      }
      const entry = map.get(key);
      if (entry) {
        entry.sharesSum += 1;
      }
    });
    return Array.from(map.values());
  });
}

// 馬ごと・作成日ごとの出資金・分配金のリストを取得
// 会員情報を紐づけておく
export function getInvestmentAndReturnListByHorseIdAndCreatedDate(horseId: number, createdDate: Date): ResultAsync<(InvestmentAndReturn & {
  member: Member;
  horse: Horse & {
    investmentContracts: InvestmentContract[];
  };
})[], Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturn.findMany({
      where: {
        horseId: horseId,
        createdDate: createdDate,
      },
      include: {
        member: true,
        horse: {
          include: {
            investmentContracts: true,
          },
        },
      },
    }),
    (error) => new DatabaseError(`Failed to get investment and return list: ${error}`)
  );
}

// 馬・作成日・会員で特定して出資金・分配金を取得
// リレーションしているinvestmentAndReturnInvestmentとinvestmentAndReturnReturnを取得
export function getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(horseId: number, createdDate: Date, memberId: number): ResultAsync<(InvestmentAndReturn & {
  investment: InvestmentAndReturnInvestment | null;
  returns: InvestmentAndReturnReturn[];
}) | null, Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturn.findFirst({
      where: { horseId: horseId, createdDate: createdDate, memberId: memberId },
      include: {
        investment: true,
        returns: true,
      },
    }),
    (error) => new DatabaseError(`Failed to get investment and return by horse id and created date and member id: ${error}`)
  );
}

// 指定のcreatedDateについてInvestmentAndReturnからデータを取得し、memberとhorseのリレーションを含める
export function listInvestmentAndReturnByCreatedDate(createdDate: Date): ResultAsync<(InvestmentAndReturn & {
  member: Member;
  horse: Horse;
})[], Error> {
  return ResultAsync.fromPromise(
    client.investmentAndReturn.findMany({
      where: { createdDate: createdDate },
      include: {
        member: true,
        horse: true,
      },
    }),
    (error) => new DatabaseError(`Failed to list investment and return with member and horse: ${error}`)
  );
}