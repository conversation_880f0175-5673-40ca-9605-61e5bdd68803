import { HorseBillingItemType } from '@hami/prisma';
import {
  listHorseBillings,
  getHorseBillingDetail,
  createHorseBilling,
  updateHorseBilling,
  deleteHorseBilling,
  getBillingTotalBeforeTax,
} from './horse_billing_repository';
import { listAllHorseBillings } from './horse_billing_repository';

describe('horse_billing_repository', () => {
  beforeEach(async () => {
    await vPrisma.client.horseBilling.deleteMany();
    await vPrisma.client.horse.deleteMany();
    await vPrisma.client.biller.deleteMany();
  });

  describe('listHorseBillings', () => {
    it('馬の支出一覧を取得できる', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing1 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出1',
          closing: false,
        },
      });
      const billing2 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 2,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 5000,
          taxAmount: 500,
          subsidyAmount: 0,
          totalAmount: 5500,
          note: 'テスト支出2',
          closing: false,
        },
      });

      // Act
      const result = await listHorseBillings({
        horseId: horse.horseId,
      });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.billings).toHaveLength(2);
        expect(result.value.totalCount).toBe(2);
        expect(result.value.page).toBe(1);
        expect(result.value.limit).toBe(2);
        expect(result.value.totalPages).toBe(1);

        // 新しい順で取得されることを確認
        expect(result.value.billings[0].id).toBe(billing2.id);
        expect(result.value.billings[1].id).toBe(billing1.id);

        // 1件目の詳細確認
        const firstBilling = result.value.billings[0];
        expect(firstBilling.horseId).toBe(horse.horseId);
        expect(firstBilling.billerId).toBe(biller.id);
        expect(firstBilling.billingYearMonth).toBe(202412);
        expect(firstBilling.occurredYear).toBe(2024);
        expect(firstBilling.occurredMonth).toBe(12);
        expect(firstBilling.occurredDay).toBe(2);
        expect(firstBilling.itemType).toBe(HorseBillingItemType.INSURANCE);
        expect(firstBilling.itemTypeOther).toBe('');
        expect(firstBilling.billingAmount).toBe(5000);
        expect(firstBilling.taxAmount).toBe(500);
        expect(firstBilling.subsidyAmount).toBe(0);
        expect(firstBilling.totalAmount).toBe(5500);
        expect(firstBilling.note).toBe('テスト支出2');
        expect(firstBilling.closing).toBe(false);

        // 2件目の詳細確認
        const secondBilling = result.value.billings[1];
        expect(secondBilling.horseId).toBe(horse.horseId);
        expect(secondBilling.billerId).toBe(biller.id);
        expect(secondBilling.billingYearMonth).toBe(202412);
        expect(secondBilling.occurredYear).toBe(2024);
        expect(secondBilling.occurredMonth).toBe(12);
        expect(secondBilling.occurredDay).toBe(1);
        expect(secondBilling.itemType).toBe(HorseBillingItemType.ENTRUST);
        expect(secondBilling.itemTypeOther).toBe('');
        expect(secondBilling.billingAmount).toBe(10000);
        expect(secondBilling.taxAmount).toBe(1000);
        expect(secondBilling.subsidyAmount).toBe(0);
        expect(secondBilling.totalAmount).toBe(11000);
        expect(secondBilling.note).toBe('テスト支出1');
        expect(secondBilling.closing).toBe(false);
      }
    });

    it('検索条件で絞り込みができる', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing1 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出1',
          closing: false,
        },
      });
      const billing2 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202411,
          occurredYear: 2024,
          occurredMonth: 11,
          occurredDay: 1,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 5000,
          taxAmount: 500,
          subsidyAmount: 0,
          totalAmount: 5500,
          note: 'テスト支出2',
          closing: false,
        },
      });

      // Act
      const result = await listHorseBillings({
        horseId: horse.horseId,
        billingYearMonth: 202412,
        itemType: HorseBillingItemType.ENTRUST,
      });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.billings).toHaveLength(1);
        expect(result.value.totalCount).toBe(1);
        expect(result.value.page).toBe(1);
        expect(result.value.limit).toBe(1);
        expect(result.value.totalPages).toBe(1);

        const filteredBilling = result.value.billings[0];
        expect(filteredBilling.id).toBe(billing1.id);
        expect(filteredBilling.billingYearMonth).toBe(202412);
        expect(filteredBilling.itemType).toBe(HorseBillingItemType.ENTRUST);
        expect(filteredBilling.horseId).toBe(horse.horseId);
        expect(filteredBilling.billerId).toBe(biller.id);
        expect(filteredBilling.occurredYear).toBe(2024);
        expect(filteredBilling.occurredMonth).toBe(12);
        expect(filteredBilling.occurredDay).toBe(1);
        expect(filteredBilling.itemTypeOther).toBe('');
        expect(filteredBilling.billingAmount).toBe(10000);
        expect(filteredBilling.taxAmount).toBe(1000);
        expect(filteredBilling.subsidyAmount).toBe(0);
        expect(filteredBilling.totalAmount).toBe(11000);
        expect(filteredBilling.note).toBe('テスト支出1');
        expect(filteredBilling.closing).toBe(false);
      }
    });

    it('ページネーションが動作する', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const createdBillings: Array<{
        id: number;
        occurredDay: number;
        billingAmount: number;
        taxAmount: number;
        totalAmount: number;
        note: string;
      }> = [];
      for (let i = 1; i <= 25; i++) {
        const billing = await vPrisma.client.horseBilling.create({
          data: {
            horseId: horse.horseId,
            billerId: biller.id,
            billingYearMonth: 202412,
            occurredYear: 2024,
            occurredMonth: 12,
            occurredDay: i,
            itemType: HorseBillingItemType.ENTRUST,
            itemTypeOther: '',
            billingAmount: 10000 + i * 100,
            taxAmount: 1000 + i * 10,
            subsidyAmount: 0,
            totalAmount: 11000 + i * 110,
            note: `テスト支出${i}`,
            closing: false,
          },
        });
        createdBillings.push(billing);
      }

      // Act
      const result = await listHorseBillings({
        horseId: horse.horseId,
        page: 2,
        pageSize: 10,
      });

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.billings).toHaveLength(10);
        expect(result.value.totalCount).toBe(25);
        expect(result.value.page).toBe(2);
        expect(result.value.limit).toBe(10);
        expect(result.value.totalPages).toBe(3);

        // 2ページ目は11-20件目が取得されることを確認（新しい順なので、IDの大きい順）
        // 新しい順でソートされているので、IDの大きい順に取得される
        const sortedBillings = [...createdBillings].sort((a, b) => b.id - a.id);
        const expectedIds = sortedBillings.slice(10, 20).map((b) => b.id);
        const actualIds = result.value.billings.map((b) => b.id);
        expect(actualIds).toEqual(expectedIds);

        // 各レコードの詳細確認
        result.value.billings.forEach((billing, index) => {
          const expectedBilling = sortedBillings[10 + index]; // 2ページ目なので10から開始
          expect(billing.id).toBe(expectedBilling.id);
          expect(billing.horseId).toBe(horse.horseId);
          expect(billing.billerId).toBe(biller.id);
          expect(billing.billingYearMonth).toBe(202412);
          expect(billing.occurredYear).toBe(2024);
          expect(billing.occurredMonth).toBe(12);
          expect(billing.occurredDay).toBe(expectedBilling.occurredDay);
          expect(billing.itemType).toBe(HorseBillingItemType.ENTRUST);
          expect(billing.itemTypeOther).toBe('');
          expect(billing.billingAmount).toBe(expectedBilling.billingAmount);
          expect(billing.taxAmount).toBe(expectedBilling.taxAmount);
          expect(billing.subsidyAmount).toBe(0);
          expect(billing.totalAmount).toBe(expectedBilling.totalAmount);
          expect(billing.note).toBe(expectedBilling.note);
          expect(billing.closing).toBe(false);
        });
      }
    });

    it('closingフィルターが動作する', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const openBilling = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: '未締切支出',
          closing: false,
        },
      });
      const closedBilling = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 2,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 5000,
          taxAmount: 500,
          subsidyAmount: 0,
          totalAmount: 5500,
          note: '締切済み支出',
          closing: true,
        },
      });

      // Act - closing=falseで検索
      const openResult = await listHorseBillings({
        horseId: horse.horseId,
        closing: false,
      });

      // Assert - closing=falseの結果
      expect(openResult.isOk()).toBe(true);
      if (openResult.isOk()) {
        expect(openResult.value.billings).toHaveLength(1);
        expect(openResult.value.totalCount).toBe(1);
        expect(openResult.value.billings[0].id).toBe(openBilling.id);
        expect(openResult.value.billings[0].closing).toBe(false);
        expect(openResult.value.billings[0].note).toBe('未締切支出');
      }

      // Act - closing=trueで検索
      const closedResult = await listHorseBillings({
        horseId: horse.horseId,
        closing: true,
      });

      // Assert - closing=trueの結果
      expect(closedResult.isOk()).toBe(true);
      if (closedResult.isOk()) {
        expect(closedResult.value.billings).toHaveLength(1);
        expect(closedResult.value.totalCount).toBe(1);
        expect(closedResult.value.billings[0].id).toBe(closedBilling.id);
        expect(closedResult.value.billings[0].closing).toBe(true);
        expect(closedResult.value.billings[0].note).toBe('締切済み支出');
      }

      // Act - closing未指定で検索
      const allResult = await listHorseBillings({
        horseId: horse.horseId,
      });

      // Assert - 全件取得される
      expect(allResult.isOk()).toBe(true);
      if (allResult.isOk()) {
        expect(allResult.value.billings).toHaveLength(2);
        expect(allResult.value.totalCount).toBe(2);
        const allIds = allResult.value.billings.map((b) => b.id);
        expect(allIds).toContain(openBilling.id);
        expect(allIds).toContain(closedBilling.id);
      }
    });
  });

  describe('getHorseBillingDetail', () => {
    it('支出詳細を取得できる', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出',
          closing: false,
        },
      });

      // Act
      const result = await getHorseBillingDetail(billing.id);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.id).toBe(billing.id);
        expect(result.value.horseId).toBe(horse.horseId);
        expect(result.value.billerId).toBe(biller.id);
        expect(result.value.billingYearMonth).toBe(202412);
        expect(result.value.occurredYear).toBe(2024);
        expect(result.value.occurredMonth).toBe(12);
        expect(result.value.occurredDay).toBe(1);
        expect(result.value.itemType).toBe(HorseBillingItemType.ENTRUST);
        expect(result.value.itemTypeOther).toBe('');
        expect(result.value.billingAmount).toBe(10000);
        expect(result.value.taxAmount).toBe(1000);
        expect(result.value.subsidyAmount).toBe(0);
        expect(result.value.totalAmount).toBe(11000);
        expect(result.value.note).toBe('テスト支出');
        expect(result.value.closing).toBe(false);
      }
    });

    it('存在しないIDの場合はエラーになる', async () => {
      // Act & Assert
      const result = await getHorseBillingDetail(999);
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('Horse billing not found');
      }
    });
  });

  describe('createHorseBilling', () => {
    it('支出を作成できる', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billingData = {
        horseId: horse.horseId,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: biller.id,
        itemType: HorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: 10000,
        taxAmount: 1000,
        subsidyAmount: 0,
        totalAmount: 11000,
        note: 'テスト支出',
      };

      // Act
      const result = await createHorseBilling(billingData);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.horseId).toBe(horse.horseId);
        expect(result.value.billerId).toBe(biller.id);
        expect(result.value.billingYearMonth).toBe(202412);
        expect(result.value.occurredYear).toBe(2024);
        expect(result.value.occurredMonth).toBe(12);
        expect(result.value.occurredDay).toBe(1);
        expect(result.value.itemType).toBe(HorseBillingItemType.ENTRUST);
        expect(result.value.itemTypeOther).toBe('');
        expect(result.value.billingAmount).toBe(10000);
        expect(result.value.taxAmount).toBe(1000);
        expect(result.value.subsidyAmount).toBe(0);
        expect(result.value.totalAmount).toBe(11000);
        expect(result.value.note).toBe('テスト支出');
        expect(result.value.closing).toBe(false); // 作成時は常にfalse
        expect(result.value.id).toBeGreaterThan(0);

        // データベースに実際に保存されていることを確認
        const savedBilling = await vPrisma.client.horseBilling.findUnique({
          where: { id: result.value.id },
        });
        expect(savedBilling).not.toBeNull();
        expect(savedBilling?.horseId).toBe(horse.horseId);
        expect(savedBilling?.billerId).toBe(biller.id);
        expect(savedBilling?.billingYearMonth).toBe(202412);
        expect(savedBilling?.closing).toBe(false);
      }
    });

    it('存在しない馬IDの場合はエラーになる', async () => {
      // Arrange
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billingData = {
        horseId: 999,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: biller.id,
        itemType: HorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: 10000,
        taxAmount: 1000,
        subsidyAmount: 0,
        totalAmount: 11000,
        note: 'テスト支出',
      };

      // Act & Assert
      const result = await createHorseBilling(billingData);
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('Horse not found');
      }
    });

    it('存在しない請求者IDの場合はエラーになる', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const billingData = {
        horseId: horse.horseId,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: 999,
        itemType: HorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: 10000,
        taxAmount: 1000,
        subsidyAmount: 0,
        totalAmount: 11000,
        note: 'テスト支出',
      };

      // Act & Assert
      const result = await createHorseBilling(billingData);
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('Biller not found');
      }
    });
  });

  describe('updateHorseBilling', () => {
    it('支出を更新できる', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出',
          closing: false,
        },
      });

      const updateData = {
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: biller.id,
        itemType: HorseBillingItemType.INSURANCE,
        itemTypeOther: '保険料詳細',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 500,
        totalAmount: 17000,
        note: '更新後の支出',
      };

      // Act
      const result = await updateHorseBilling(billing.id, updateData);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.id).toBe(billing.id);
        expect(result.value.horseId).toBe(horse.horseId);
        expect(result.value.billerId).toBe(biller.id);
        expect(result.value.billingYearMonth).toBe(202411);
        expect(result.value.occurredYear).toBe(2024);
        expect(result.value.occurredMonth).toBe(11);
        expect(result.value.occurredDay).toBe(15);
        expect(result.value.itemType).toBe(HorseBillingItemType.INSURANCE);
        expect(result.value.itemTypeOther).toBe('保険料詳細');
        expect(result.value.billingAmount).toBe(15000);
        expect(result.value.taxAmount).toBe(1500);
        expect(result.value.subsidyAmount).toBe(500);
        expect(result.value.totalAmount).toBe(17000);
        expect(result.value.note).toBe('更新後の支出');
        expect(result.value.closing).toBe(false);

        // データベースに実際に更新されていることを確認
        const updatedBilling = await vPrisma.client.horseBilling.findUnique({
          where: { id: billing.id },
        });
        expect(updatedBilling).not.toBeNull();
        expect(updatedBilling?.billingYearMonth).toBe(202411);
        expect(updatedBilling?.itemType).toBe(HorseBillingItemType.INSURANCE);
        expect(updatedBilling?.billingAmount).toBe(15000);
        expect(updatedBilling?.note).toBe('更新後の支出');
      }
    });

    it('存在しないIDの場合はエラーになる', async () => {
      // Arrange
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const updateData = {
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: biller.id,
        itemType: HorseBillingItemType.INSURANCE,
        itemTypeOther: '',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 0,
        totalAmount: 16500,
        note: '更新後の支出',
      };

      // Act & Assert
      const result = await updateHorseBilling(999, updateData);
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('Horse billing not found');
      }
    });

    it('closingがtrueの場合は更新できない', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出',
          closing: true, // 締切済み
        },
      });

      const updateData = {
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: biller.id,
        itemType: HorseBillingItemType.INSURANCE,
        itemTypeOther: '',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 0,
        totalAmount: 16500,
        note: '更新後の支出',
      };

      // Act & Assert
      const result = await updateHorseBilling(billing.id, updateData);
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('Cannot update closed horse billing');
      }
    });

    it('存在しない請求者IDの場合はエラーになる', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出',
          closing: false,
        },
      });

      const updateData = {
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: 999, // 存在しない請求者ID
        itemType: HorseBillingItemType.INSURANCE,
        itemTypeOther: '',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 0,
        totalAmount: 16500,
        note: '更新後の支出',
      };

      // Act & Assert
      const result = await updateHorseBilling(billing.id, updateData);
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('Biller not found');
      }
    });
  });

  describe('deleteHorseBilling', () => {
    it('支出を削除できる', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出',
          closing: false,
        },
      });

      // Act
      const result = await deleteHorseBilling(billing.id);

      // Assert
      expect(result.isOk()).toBe(true);

      const deletedBilling = await vPrisma.client.horseBilling.findUnique({
        where: { id: billing.id },
      });
      expect(deletedBilling).toBeNull();

      // 他のレコードは影響を受けないことを確認
      const otherBillings = await vPrisma.client.horseBilling.findMany({
        where: { horseId: horse.horseId },
      });
      expect(otherBillings).toHaveLength(0);
    });

    it('存在しないIDの場合はエラーになる', async () => {
      // Act & Assert
      const result = await deleteHorseBilling(999);
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('Horse billing not found');
      }
    });

    it('closingがtrueの場合は削除できない', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出',
          closing: true, // 締切済み
        },
      });

      // Act & Assert
      const result = await deleteHorseBilling(billing.id);
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('Cannot delete closed horse billing');
      }

      // 削除されていないことを確認
      const notDeletedBilling = await vPrisma.client.horseBilling.findUnique({
        where: { id: billing.id },
      });
      expect(notDeletedBilling).not.toBeNull();
      expect(notDeletedBilling?.id).toBe(billing.id);
      expect(notDeletedBilling?.closing).toBe(true);
    });
  });


  describe('listAllHorseBillings', () => {
    it('指定された日付範囲の全馬の支出一覧を取得できる', async () => {
      // ===== Arrange =====
      const horse1 = await vPrisma.client.horse.create({
        data: {
          horseId: 1001,
          horseName: 'テスト馬1001',
          recruitmentYear: 2020,
          recruitmentNo: 1001,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1001募集',
          sharesTotal: 100,
          amountTotal: ********,
          note: 'テスト馬1001',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const horse2 = await vPrisma.client.horse.create({
        data: {
          horseId: 1002,
          horseName: 'テスト馬1002',
          recruitmentYear: 2020,
          recruitmentNo: 1002,
          birthYear: 2020,
          birthMonth: 2,
          birthDay: 15,
          recruitmentName: 'テスト馬1002募集',
          sharesTotal: 100,
          amountTotal: ********,
          note: 'テスト馬1002',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const biller1 = await vPrisma.client.biller.create({
        data: {
          id: 1,
          name: 'テスト請求者1',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者1',
        },
      });

      const biller2 = await vPrisma.client.biller.create({
        data: {
          id: 2,
          name: 'テスト請求者2',
          address: '東京都新宿区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '7654321',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者2',
        },
      });

      // 指定範囲内の支出
      const billing1 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse1.horseId,
          billingYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          billerId: biller1.id,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 500000,
          taxAmount: 50000,
          subsidyAmount: 0,
          totalAmount: 550000,
          note: 'テスト支出1',
          closing: false,
        },
      });

      const billing2 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse2.horseId,
          billingYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 20,
          billerId: biller2.id,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 300000,
          taxAmount: 30000,
          subsidyAmount: 50000,
          totalAmount: 280000,
          note: 'テスト支出2',
          closing: false,
        },
      });

      // 指定範囲外の支出（取得されないはず）
      const billing3 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse1.horseId,
          billingYearMonth: 202312,
          occurredYear: 2023,
          occurredMonth: 12,
          occurredDay: 31,
          billerId: biller1.id,
          itemType: HorseBillingItemType.OTHER,
          itemTypeOther: '',
          billingAmount: 200000,
          taxAmount: 20000,
          subsidyAmount: 0,
          totalAmount: 220000,
          note: 'テスト支出3（範囲外）',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listAllHorseBillings({
        startYear: 2024,
        startMonth: 1,
        startDay: 1,
        endYear: 2024,
        endMonth: 12,
        endDay: 31,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const billings = result.value.billings;
        expect(billings.length).toBe(2);
        
        // 指定範囲内の支出が含まれていることを確認
        const billingIds = billings.map(billing => billing.id);
        expect(billingIds).toContain(billing1.id);
        expect(billingIds).toContain(billing2.id);
        
        // 指定範囲外の支出は含まれていないことを確認
        expect(billingIds).not.toContain(billing3.id);

        // データの詳細を確認
        const billing1Data = billings.find(billing => billing.id === billing1.id);
        const billing2Data = billings.find(billing => billing.id === billing2.id);

        expect(billing1Data).toBeDefined();
        if (billing1Data) {
          expect(billing1Data.horseId).toBe(horse1.horseId);
          expect(billing1Data.horseName).toBe(horse1.horseName);
          expect(billing1Data.billerName).toBe(biller1.name);
          expect(billing1Data.billingYearMonth).toBe(202401);
          expect(billing1Data.occurredYear).toBe(2024);
          expect(billing1Data.occurredMonth).toBe(1);
          expect(billing1Data.occurredDay).toBe(15);
          expect(billing1Data.billerId).toBe(biller1.id);
          expect(billing1Data.itemType).toBe(HorseBillingItemType.ENTRUST);
          expect(billing1Data.billingAmount).toBe(500000);
          expect(billing1Data.totalAmount).toBe(550000);
          expect(billing1Data.closing).toBe(false);
        }

        expect(billing2Data).toBeDefined();
        if (billing2Data) {
          expect(billing2Data.horseId).toBe(horse2.horseId);
          expect(billing2Data.horseName).toBe(horse2.horseName);
          expect(billing2Data.billerName).toBe(biller2.name);
          expect(billing2Data.billingYearMonth).toBe(202402);
          expect(billing2Data.occurredYear).toBe(2024);
          expect(billing2Data.occurredMonth).toBe(2);
          expect(billing2Data.occurredDay).toBe(20);
          expect(billing2Data.billerId).toBe(biller2.id);
          expect(billing2Data.itemType).toBe(HorseBillingItemType.INSURANCE);
          expect(billing2Data.billingAmount).toBe(300000);
          expect(billing2Data.totalAmount).toBe(280000);
          expect(billing2Data.closing).toBe(false);
        }
      }
    });

    it('日付範囲が狭い場合、該当する支出のみ取得される', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1003,
          horseName: 'テスト馬1003',
          recruitmentYear: 2020,
          recruitmentNo: 1003,
          birthYear: 2020,
          birthMonth: 3,
          birthDay: 30,
          recruitmentName: 'テスト馬1003募集',
          sharesTotal: 100,
          amountTotal: ********,
          note: 'テスト馬1003',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const biller = await vPrisma.client.biller.create({
        data: {
          id: 3,
          name: 'テスト請求者3',
          address: '東京都新宿区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '7654321',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者3',
        },
      });

      // 1月15日の支出
      const billing1 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billingYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          billerId: biller.id,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 500000,
          taxAmount: 50000,
          subsidyAmount: 0,
          totalAmount: 550000,
          note: '1月の支出',
          closing: false,
        },
      });

      // 1月20日の支出
      const billing2 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billingYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 20,
          billerId: biller.id,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 300000,
          taxAmount: 30000,
          subsidyAmount: 50000,
          totalAmount: 280000,
          note: '1月の支出2',
          closing: false,
        },
      });

      // 2月10日の支出（範囲外）
      const billing3 = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billingYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 10,
          billerId: biller.id,
          itemType: HorseBillingItemType.OTHER,
          itemTypeOther: '',
          billingAmount: 200000,
          taxAmount: 20000,
          subsidyAmount: 0,
          totalAmount: 220000,
          note: '2月の支出（範囲外）',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listAllHorseBillings({
        startYear: 2024,
        startMonth: 1,
        startDay: 15,
        endYear: 2024,
        endMonth: 1,
        endDay: 25,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const billings = result.value.billings;
        expect(billings.length).toBe(2);
        
        // 指定範囲内の支出が含まれていることを確認
        const billingIds = billings.map(billing => billing.id);
        expect(billingIds).toContain(billing1.id);
        expect(billingIds).toContain(billing2.id);
        
        // 指定範囲外の支出は含まれていないことを確認
        expect(billingIds).not.toContain(billing3.id);

        // ソート順の確認（発生年月日降順、ID降順）
        expect(billings[0].id).toBe(billing2.id); // 1月20日
        expect(billings[1].id).toBe(billing1.id); // 1月15日
      }
    });

    it('支出が存在しない場合、空の配列を返す', async () => {
      // ===== Act =====
      const result = await listAllHorseBillings({
        startYear: 2024,
        startMonth: 1,
        startDay: 1,
        endYear: 2024,
        endMonth: 12,
        endDay: 31,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.billings).toEqual([]);
      }
    });

    it('複数の支出種別が正しく処理される', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1004,
          horseName: 'テスト馬1004',
          recruitmentYear: 2020,
          recruitmentNo: 1004,
          birthYear: 2020,
          birthMonth: 4,
          birthDay: 15,
          recruitmentName: 'テスト馬1004募集',
          sharesTotal: 100,
          amountTotal: ********,
          note: 'テスト馬1004',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const biller = await vPrisma.client.biller.create({
        data: {
          id: 4,
          name: 'テスト請求者4',
          address: '東京都新宿区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '7654321',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者4',
        },
      });

      // 複数の支出種別を作成
      const trainingBilling = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billingYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 10,
          billerId: biller.id,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 500000,
          taxAmount: 50000,
          subsidyAmount: 0,
          totalAmount: 550000,
          note: '預託料',
          closing: false,
        },
      });

      const veterinaryBilling = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billingYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          billerId: biller.id,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 300000,
          taxAmount: 30000,
          subsidyAmount: 50000,
          totalAmount: 280000,
          note: '保険料',
          closing: false,
        },
      });

      const transportBilling = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billingYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 20,
          billerId: biller.id,
          itemType: HorseBillingItemType.OTHER,
          itemTypeOther: '',
          billingAmount: 200000,
          taxAmount: 20000,
          subsidyAmount: 0,
          totalAmount: 220000,
          note: 'その他費用',
          closing: false,
        },
      });

      const insuranceBilling = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billingYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 25,
          billerId: biller.id,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 150000,
          taxAmount: 15000,
          subsidyAmount: 0,
          totalAmount: 165000,
          note: '保険料2',
          closing: false,
        },
      });

      const entrustBilling = await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billingYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 30,
          billerId: biller.id,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 100000,
          taxAmount: 10000,
          subsidyAmount: 0,
          totalAmount: 110000,
          note: '委託料',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listAllHorseBillings({
        startYear: 2024,
        startMonth: 1,
        startDay: 1,
        endYear: 2024,
        endMonth: 12,
        endDay: 31,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const billings = result.value.billings;
        expect(billings.length).toBe(5);

        // 全ての支出種別が含まれていることを確認
        const itemTypes = billings.map(billing => billing.itemType);
        expect(itemTypes).toContain(HorseBillingItemType.ENTRUST);
        expect(itemTypes).toContain(HorseBillingItemType.INSURANCE);
        expect(itemTypes).toContain(HorseBillingItemType.OTHER);

        // ソート順の確認（発生年月日降順、ID降順）
        expect(billings[0].id).toBe(entrustBilling.id); // 1月30日
        expect(billings[1].id).toBe(insuranceBilling.id); // 1月25日
        expect(billings[2].id).toBe(transportBilling.id); // 1月20日
        expect(billings[3].id).toBe(veterinaryBilling.id); // 1月15日
        expect(billings[4].id).toBe(trainingBilling.id); // 1月10日
      }
    });
  });

  describe('getBillingTotalBeforeTax', () => {
    it('指定された月の前までの預託料の税別合計を取得できる', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });

      // 2024年1月の預託料（対象外）
      await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: '1月の預託料',
          closing: false,
        },
      });

      // 2024年2月の預託料（対象）
      await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 20000,
          taxAmount: 2000,
          subsidyAmount: 1000,
          totalAmount: 21000,
          note: '2月の預託料',
          closing: false,
        },
      });

      // 2024年3月の預託料（対象）
      await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 30000,
          taxAmount: 3000,
          subsidyAmount: 2000,
          totalAmount: 31000,
          note: '3月の預託料',
          closing: false,
        },
      });

      // 2024年4月の預託料（対象外）
      await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202404,
          occurredYear: 2024,
          occurredMonth: 4,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 40000,
          taxAmount: 4000,
          subsidyAmount: 0,
          totalAmount: 44000,
          note: '4月の預託料',
          closing: false,
        },
      });

      // 預託料以外の支出（対象外）
      await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 15,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 50000,
          taxAmount: 5000,
          subsidyAmount: 0,
          totalAmount: 55000,
          note: '保険料',
          closing: false,
        },
      });

      // Act
      const result = await getBillingTotalBeforeTax(horse.horseId, 202404);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        // 1月、2月、3月の預託料の合計（税別）
        // 1月: 10000 - 0 - 1000 = 9000
        // 2月: 20000 - 1000 - 2000 = 17000
        // 3月: 30000 - 2000 - 3000 = 25000
        // 合計: 9000 + 17000 + 25000 = 51000
        expect(result.value.totalBillingAmountBeforeTax).toBe(51000);
      }
    });

    it('預託料が存在しない場合は0を返す', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // Act
      const result = await getBillingTotalBeforeTax(horse.horseId, 202404);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.totalBillingAmountBeforeTax).toBe(0);
      }
    });

    it('指定された月より後の預託料は含まれない', async () => {
      // Arrange
      const horse = await vPrisma.client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await vPrisma.client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });

      // 2024年3月の委託料（対象）
      await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 30000,
          taxAmount: 3000,
          subsidyAmount: 2000,
          totalAmount: 31000,
          note: '3月の委託料',
          closing: false,
        },
      });

      // 2024年4月の委託料（対象外）
      await vPrisma.client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202404,
          occurredYear: 2024,
          occurredMonth: 4,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 40000,
          taxAmount: 4000,
          subsidyAmount: 0,
          totalAmount: 44000,
          note: '4月の委託料',
          closing: false,
        },
      });

      // Act
      const result = await getBillingTotalBeforeTax(horse.horseId, 202404);

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        // 3月の預託料のみ（税別）
        // 30000 - 2000 - 3000 = 25000
        expect(result.value.totalBillingAmountBeforeTax).toBe(25000);
      }
    });
  });
});
