import { RecipientStatus } from '@hami/prisma';
import { getDeliveryStatus, getDeliveryStatusWithMemberInfo } from './message_recipient_repository';
import { MemberFactory } from '../../test_utils/factories/member_factory';
import { messageFactory, messageRecipientFactory } from '../../test_utils/factories/message_factory';

describe('message_recipient_repository', () => {
  describe('getDeliveryStatus', () => {
    it('配信状況一覧を取得できること', async () => {
      // テストメッセージを作成
      const message = await messageFactory.create({
        title: 'テスト配信メッセージ',
      });

      // テスト会員を作成
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 配信状況を作成
      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: member1.memberId,
        recipientStatus: RecipientStatus.DELIVERED,
      });

      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: member2.memberId,
        recipientStatus: RecipientStatus.READ,
      });

      const result = await getDeliveryStatus({
        publicId: message.publicId,
        page: 1,
        pageSize: 10,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.recipients).toHaveLength(2);
        expect(result.value.totalCount).toBe(2);
        expect(result.value.totalPages).toBe(1);

        // 配信状況の確認
        const deliveredRecipient = result.value.recipients.find((r) => r.recipientStatus === RecipientStatus.DELIVERED);
        const readRecipient = result.value.recipients.find((r) => r.recipientStatus === RecipientStatus.READ);

        expect(deliveredRecipient).toBeDefined();
        expect(readRecipient).toBeDefined();
      }
    });

    it('ステータスでフィルタリングできること', async () => {
      // テストメッセージを作成
      const message = await messageFactory.create({
        title: 'フィルタリングテストメッセージ',
      });

      // テスト会員を作成
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 異なるステータスの配信状況を作成
      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: member1.memberId,
        recipientStatus: RecipientStatus.DELIVERED,
      });

      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: member2.memberId,
        recipientStatus: RecipientStatus.READ,
      });

      // DELIVERED ステータスでフィルタリング
      const result = await getDeliveryStatus({
        publicId: message.publicId,
        page: 1,
        pageSize: 10,
        statusFilter: RecipientStatus.DELIVERED,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.recipients).toHaveLength(1);
        expect(result.value.recipients[0].recipientStatus).toBe(RecipientStatus.DELIVERED);
        expect(result.value.recipients[0].memberId).toBe(member1.memberId);
      }
    });

    it('ページネーションが正しく動作すること', async () => {
      // テストメッセージを作成
      const message = await messageFactory.create({
        title: 'ページネーションテストメッセージ',
      });

      // 5人の会員を作成し、配信状況を作成
      const members = [];
      for (let i = 0; i < 5; i++) {
        const member = await MemberFactory.create();
        members.push(member);

        await messageRecipientFactory.create({
          messageId: message.messageId,
          memberId: member.memberId,
          recipientStatus: RecipientStatus.DELIVERED,
        });
      }

      // 1ページ目（2件ずつ）
      const page1Result = await getDeliveryStatus({
        publicId: message.publicId,
        page: 1,
        pageSize: 2,
      });

      expect(page1Result.isOk()).toBe(true);
      if (page1Result.isOk()) {
        expect(page1Result.value.recipients).toHaveLength(2);
        expect(page1Result.value.totalCount).toBe(5);
        expect(page1Result.value.totalPages).toBe(3);
      }

      // 2ページ目
      const page2Result = await getDeliveryStatus({
        publicId: message.publicId,
        page: 2,
        pageSize: 2,
      });

      expect(page2Result.isOk()).toBe(true);
      if (page2Result.isOk()) {
        expect(page2Result.value.recipients).toHaveLength(2);
        expect(page2Result.value.totalCount).toBe(5);
        expect(page2Result.value.totalPages).toBe(3);
      }
    });

    it('配信状況が存在しない場合は空の配列を返すこと', async () => {
      // テストメッセージを作成（配信状況は作成しない）
      const message = await messageFactory.create({
        title: '配信状況なしメッセージ',
      });

      const result = await getDeliveryStatus({
        publicId: message.publicId,
        page: 1,
        pageSize: 10,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.recipients).toHaveLength(0);
        expect(result.value.totalCount).toBe(0);
        expect(result.value.totalPages).toBe(0);
      }
    });
  });

  describe('getDeliveryStatusWithMemberInfo', () => {
    it('会員情報を含む配信状況を取得できること', async () => {
      // テストメッセージを作成
      const message = await messageFactory.create({
        title: '会員情報付き配信状況テスト',
      });

      // テスト会員を作成
      const member = await MemberFactory.create({
        firstName: '太郎',
        lastName: '田中',
        memberNumber: 1001,
      });

      // 配信状況を作成
      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: member.memberId,
        recipientStatus: RecipientStatus.DELIVERED,
      });

      const result = await getDeliveryStatusWithMemberInfo({
        publicId: message.publicId,
        page: 1,
        pageSize: 10,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.recipients).toHaveLength(1);

        const recipient = result.value.recipients[0];
        expect(recipient.memberId).toBe(member.memberId);
        expect(recipient.memberName).toBe('太郎 田中');
        expect(recipient.memberNumber).toBe('1001');
        expect(recipient.recipientStatus).toBe(RecipientStatus.DELIVERED);
      }
    });

    it('会員情報が見つからない場合は空の名前を返すこと', async () => {
      // テストメッセージを作成
      const message = await messageFactory.create({
        title: '会員情報なし配信状況テスト',
      });

      // 存在しない会員IDで配信状況を作成
      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: 99999, // 存在しない会員ID
        recipientStatus: RecipientStatus.DELIVERED,
      });

      const result = await getDeliveryStatusWithMemberInfo({
        publicId: message.publicId,
        page: 1,
        pageSize: 10,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.recipients).toHaveLength(1);

        const recipient = result.value.recipients[0];
        expect(recipient.memberId).toBe(99999);
        expect(recipient.memberName).toBe('');
        expect(recipient.memberNumber).toBe('');
      }
    });

    it('配信状況が存在しない場合は空の配列を返すこと', async () => {
      // テストメッセージを作成（配信状況は作成しない）
      const message = await messageFactory.create({
        title: '配信状況なしメッセージ',
      });

      const result = await getDeliveryStatusWithMemberInfo({
        publicId: message.publicId,
        page: 1,
        pageSize: 10,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.recipients).toHaveLength(0);
        expect(result.value.totalCount).toBe(0);
        expect(result.value.totalPages).toBe(0);
      }
    });

    it('ステータスフィルタリングが正しく動作すること', async () => {
      // テストメッセージを作成
      const message = await messageFactory.create({
        title: 'ステータスフィルタリングテスト',
      });

      // テスト会員を作成
      const member1 = await MemberFactory.create({
        firstName: '太郎',
        lastName: '田中',
      });

      const member2 = await MemberFactory.create({
        firstName: '花子',
        lastName: '佐藤',
      });

      // 異なるステータスの配信状況を作成
      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: member1.memberId,
        recipientStatus: RecipientStatus.DELIVERED,
      });

      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: member2.memberId,
        recipientStatus: RecipientStatus.READ,
      });

      // READ ステータスでフィルタリング
      const result = await getDeliveryStatusWithMemberInfo({
        publicId: message.publicId,
        page: 1,
        pageSize: 10,
        statusFilter: RecipientStatus.READ,
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.recipients).toHaveLength(1);
        expect(result.value.recipients[0].recipientStatus).toBe(RecipientStatus.READ);
        expect(result.value.recipients[0].memberName).toBe('花子 佐藤');
      }
    });
  });
});
