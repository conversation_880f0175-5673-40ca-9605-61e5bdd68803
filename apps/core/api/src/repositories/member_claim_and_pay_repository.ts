import { ResultAsync } from 'neverthrow';
import { MemberClaim, MemberClaimAndPay } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

/**
 * MemberClaimを作成する
 */
export function createMemberClaim(
  memberId: number,
  occurredDate: Date,
  title: string,
  description: string,
  taxRate: number,
  taxAmount: number,
  amount: number
): ResultAsync<MemberClaim, Error> {
  return ResultAsync.fromPromise(
    client.memberClaim.create({
      data: {
        memberId,
        occurredDate,
        title,
        description,
        taxRate,
        taxAmount,
        amount,
      },
    }),
    (error) => new DatabaseError(`Failed to create member claim: ${error}`)
  );
}

export function deleteMemberClaim(memberId: number, occurredDate: Date) {
  return ResultAsync.fromPromise(
    client.memberClaim.deleteMany({
      where: { memberId, occurredDate },
    }),
    (error) => new DatabaseError(`Failed to delete member claim: ${error}`)
  );
}

export function listMemberClaimByOccurredDate(occurredDate: Date): ResultAsync<MemberClaim[], Error> {
  return ResultAsync.fromPromise(
    client.memberClaim.findMany({
      where: { occurredDate },
    }),
    (error) => new DatabaseError(`Failed to list member claim: ${error}`)
  );
}

/**
 * 指定された日付のMemberClaimAndPayをMember情報と一緒に取得する
 */
export function listMemberClaimAndPaysWithMember(occurredDate: Date): ResultAsync<(MemberClaimAndPay & {
  member: {
    memberId: number;
    memberNumber: number;
    lastName: string;
    firstName: string;
    lastNameKana: string;
    firstNameKana: string;
  };
})[], Error> {
  return ResultAsync.fromPromise(
    client.memberClaimAndPay.findMany({
      where: { occurredDate },
      include: {
        member: {
          select: {
            memberId: true,
            memberNumber: true,
            lastName: true,
            firstName: true,
            lastNameKana: true,
            firstNameKana: true,
          },
        },
      },
      orderBy: {
        memberId: 'asc',
      },
    }),
    (error) => new DatabaseError(`Failed to list member claim and pays with member: ${error}`)
  );
}

// 指定のmemberIdとoccurredDateについて、MemberClaimAndPayを取得する
export function listMemberClaimByMemberIdAndOccurredDate(memberId: number, occurredDate: Date): ResultAsync<MemberClaim[], Error> {
  return ResultAsync.fromPromise(
    client.memberClaim.findMany({ where: { memberId, occurredDate } }),
    (error) => new DatabaseError(`Failed to list member claim and pay by member id and occurred date: ${error}`)
  );
}

// 指定のmemberIdについて、MemberClaimを作成する
export function createMemberClaimAndPay(
  memberId: number,
  occurredDate: Date,
  claimAmount: number,
  payAmount: number,
  statementFileKey?: string,
  statementIssuedAt?: Date
): ResultAsync<MemberClaimAndPay, Error> {
  return ResultAsync.fromPromise(
    client.memberClaimAndPay.create({
      data: {
        memberId,
        occurredDate,
        claimAmount,
        payAmount,
        statementFileKey,
        statementIssuedAt,
      },
    }),
    (error) => new DatabaseError(`Failed to create member claim and pay: ${error}`)
  );
}

// memberClaimAndPayのstatementFileKeyとstatementIssuedAtをmemberClaimAndPayIdで更新する
export function updateMemberClaimAndPayStatement(memberClaimAndPayId: number, statementFileKey: string, statementIssuedAt: Date) {
  return ResultAsync.fromPromise(
    client.memberClaimAndPay.update({ where: { memberClaimAndPayId }, data: { statementFileKey, statementIssuedAt } }),
    (error) => new DatabaseError(`Failed to update member claim and pay: ${error}`)
  );
}

export function deleteMemberClaimAndPay(memberId: number, occurredDate: Date) {
  return ResultAsync.fromPromise(
    client.memberClaimAndPay.deleteMany({
      where: { memberId, occurredDate },
    }),
    (error) => new DatabaseError(`Failed to delete member claim and pay: ${error}`)
  );
}
