import { MemberFactory } from '@core-test/factories/member_factory';
import {
  createMemberClaim,
  deleteMemberClaim,
  createMemberClaimAndPay,
  deleteMemberClaimAndPay,
  listMemberClaimByOccurredDate,
  listMemberClaimAndPaysWithMember,
  listMemberClaimByMemberIdAndOccurredDate,
  updateMemberClaimAndPayStatement,
} from './member_claim_and_pay_repository';

describe('member_claim_and_pay_repository', () => {
  let member: Awaited<ReturnType<typeof MemberFactory.create>>;
  const occurredDate = new Date('2024-07-01');

  beforeEach(async () => {
    // テスト用会員を作成
    member = await MemberFactory.create();
  });

  it('createMemberClaimでMemberClaimが作成できる', async () => {
    const result = await createMemberClaim(member.memberId, occurredDate, 'タイトル', '説明', 10, 100, 1100);
    if (result.isErr()) {
      console.error('createMemberClaim failed:', result.error);
    }
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      const claim = await vPrisma.client.memberClaim.findUnique({ where: { memberClaimId: result.value.memberClaimId } });
      expect(claim).not.toBeNull();
      expect(claim!.memberId).toBe(member.memberId);
      expect(claim!.occurredDate.toISOString()).toBe(occurredDate.toISOString());
      expect(claim!.title).toBe('タイトル');
      expect(claim!.description).toBe('説明');
      expect(Number(claim!.taxRate)).toBe(10);
      expect(Number(claim!.taxAmount)).toBe(100);
      expect(Number(claim!.amount)).toBe(1100);
    }
  });

  it('listMemberClaimByOccurredDateで作成したMemberClaimが取得できる', async () => {
    await createMemberClaim(member.memberId, occurredDate, 'タイトル', '説明', 10, 100, 1100);
    const result = await listMemberClaimByOccurredDate(occurredDate);
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      expect(result.value.length).toBe(1);
      expect(result.value[0].memberId).toBe(member.memberId);
      expect(result.value[0].title).toBe('タイトル');
      expect(result.value[0].description).toBe('説明');
      expect(Number(result.value[0].taxRate)).toBe(10);
      expect(Number(result.value[0].taxAmount)).toBe(100);
      expect(Number(result.value[0].amount)).toBe(1100);
    }
  });

  it('deleteMemberClaimでMemberClaimが削除できる', async () => {
    await createMemberClaim(member.memberId, occurredDate, 'タイトル', '説明', 10, 100, 1100);
    const delResult = await deleteMemberClaim(member.memberId, occurredDate);
    expect(delResult.isOk()).toBe(true);
    const claims = await vPrisma.client.memberClaim.findMany({ where: { memberId: member.memberId, occurredDate } });
    expect(claims.length).toBe(0);
  });

  it('createMemberClaimAndPayでMemberClaimAndPayが作成できる', async () => {
    const result = await createMemberClaimAndPay(member.memberId, occurredDate, 2000, 1500);
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      const claimPay = await vPrisma.client.memberClaimAndPay.findUnique({
        where: { memberClaimAndPayId: result.value.memberClaimAndPayId },
      });
      expect(claimPay).not.toBeNull();
      expect(claimPay!.memberId).toBe(member.memberId);
      expect(claimPay!.occurredDate.toISOString()).toBe(occurredDate.toISOString());
      expect(claimPay!.claimAmount).toBe(2000);
      expect(claimPay!.payAmount).toBe(1500);
    }
  });

  it('deleteMemberClaimAndPayでMemberClaimAndPayが削除できる', async () => {
    await createMemberClaimAndPay(member.memberId, occurredDate, 2000, 1500);
    const delResult = await deleteMemberClaimAndPay(member.memberId, occurredDate);
    expect(delResult.isOk()).toBe(true);
    const claimPays = await vPrisma.client.memberClaimAndPay.findMany({ where: { memberId: member.memberId, occurredDate } });
    expect(claimPays.length).toBe(0);
  });

  it('存在しないmemberIdでdeleteMemberClaim/deleteMemberClaimAndPayしてもエラーにならない', async () => {
    const del1 = await deleteMemberClaim(999999, occurredDate);
    expect(del1.isOk()).toBe(true);
    const del2 = await deleteMemberClaimAndPay(999999, occurredDate);
    expect(del2.isOk()).toBe(true);
  });

  it('listMemberClaimAndPaysWithMemberで作成したMemberClaimAndPayがMember情報と一緒に取得できる', async () => {
    await createMemberClaimAndPay(member.memberId, occurredDate, 2000, 1500);
    const result = await listMemberClaimAndPaysWithMember(occurredDate);
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      expect(result.value.length).toBe(1);
      const claimPay = result.value[0];
      expect(claimPay.memberId).toBe(member.memberId);
      expect(claimPay.claimAmount).toBe(2000);
      expect(claimPay.payAmount).toBe(1500);
      expect(claimPay.occurredDate.toISOString()).toBe(occurredDate.toISOString());
      
      // Member情報が含まれていることを確認
      expect(claimPay.member).toBeDefined();
      expect(claimPay.member.memberId).toBe(member.memberId);
      expect(claimPay.member.memberNumber).toBe(member.memberNumber);
      expect(claimPay.member.lastName).toBe(member.lastName);
      expect(claimPay.member.firstName).toBe(member.firstName);
      expect(claimPay.member.lastNameKana).toBe(member.lastNameKana);
      expect(claimPay.member.firstNameKana).toBe(member.firstNameKana);
    }
  });

  it('listMemberClaimAndPaysWithMemberで指定された日付にデータが存在しない場合は空配列を返す', async () => {
    const otherDate = new Date('2024-08-01');
    const result = await listMemberClaimAndPaysWithMember(otherDate);
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      expect(result.value.length).toBe(0);
    }
  });

  it('listMemberClaimAndPaysWithMemberで複数のMemberClaimAndPayが会員ID順で取得できる', async () => {
    // 別の会員を作成
    const member2 = await MemberFactory.create();
    
    // 異なる順序でデータを作成
    await createMemberClaimAndPay(member2.memberId, occurredDate, 3000, 2500);
    await createMemberClaimAndPay(member.memberId, occurredDate, 2000, 1500);
    
    const result = await listMemberClaimAndPaysWithMember(occurredDate);
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      expect(result.value.length).toBe(2);
      
      // 会員ID順でソートされていることを確認
      expect(result.value[0].memberId).toBe(member.memberId);
      expect(result.value[1].memberId).toBe(member2.memberId);
      
      // 各レコードにMember情報が含まれていることを確認
      expect(result.value[0].member.memberId).toBe(member.memberId);
      expect(result.value[1].member.memberId).toBe(member2.memberId);
    }
  });

  describe('listMemberClaimByMemberIdAndOccurredDate', () => {
    it('指定されたmemberIdとoccurredDateのMemberClaimが取得できる', async () => {
      // テストデータを作成
      await createMemberClaim(member.memberId, occurredDate, 'テスト請求1', '説明1', 10, 100, 1100);
      await createMemberClaim(member.memberId, occurredDate, 'テスト請求2', '説明2', 8, 80, 880);
      
      const result = await listMemberClaimByMemberIdAndOccurredDate(member.memberId, occurredDate);
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.length).toBe(2);
        
        // 作成順序で取得されることを確認
        expect(result.value[0].title).toBe('テスト請求1');
        expect(result.value[0].description).toBe('説明1');
        expect(Number(result.value[0].taxRate)).toBe(10);
        expect(Number(result.value[0].taxAmount)).toBe(100);
        expect(Number(result.value[0].amount)).toBe(1100);
        
        expect(result.value[1].title).toBe('テスト請求2');
        expect(result.value[1].description).toBe('説明2');
        expect(Number(result.value[1].taxRate)).toBe(8);
        expect(Number(result.value[1].taxAmount)).toBe(80);
        expect(Number(result.value[1].amount)).toBe(880);
      }
    });

    it('指定されたmemberIdとoccurredDateにデータが存在しない場合は空配列を返す', async () => {
      const otherDate = new Date('2024-08-01');
      const result = await listMemberClaimByMemberIdAndOccurredDate(member.memberId, otherDate);
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.length).toBe(0);
      }
    });

    it('異なるmemberIdのデータは取得されない', async () => {
      // 別の会員を作成
      const member2 = await MemberFactory.create();
      
      // 異なる会員のデータを作成
      await createMemberClaim(member2.memberId, occurredDate, '他会員の請求', '説明', 10, 100, 1100);
      
      // 元の会員のデータを取得
      const result = await listMemberClaimByMemberIdAndOccurredDate(member.memberId, occurredDate);
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.length).toBe(0);
      }
    });

    it('異なる日付のデータは取得されない', async () => {
      const otherDate = new Date('2024-08-01');
      
      // 異なる日付のデータを作成
      await createMemberClaim(member.memberId, otherDate, '他日付の請求', '説明', 10, 100, 1100);
      
      // 元の日付のデータを取得
      const result = await listMemberClaimByMemberIdAndOccurredDate(member.memberId, occurredDate);
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.length).toBe(0);
      }
    });
  });

  describe('updateMemberClaimAndPayStatement', () => {
    it('MemberClaimAndPayのstatementFileKeyとstatementIssuedAtが更新できる', async () => {
      // テストデータを作成
      const claimPayResult = await createMemberClaimAndPay(member.memberId, occurredDate, 2000, 1500);
      expect(claimPayResult.isOk()).toBe(true);
      if (!claimPayResult.isOk()) return;
      
      const memberClaimAndPayId = claimPayResult.value.memberClaimAndPayId;
      const statementFileKey = 'test-statement-file-key.pdf';
      const statementIssuedAt = new Date('2024-07-15');
      
      // 更新を実行
      const result = await updateMemberClaimAndPayStatement(memberClaimAndPayId, statementFileKey, statementIssuedAt);
      expect(result.isOk()).toBe(true);
      
      // データベースで更新を確認
      const updatedClaimPay = await vPrisma.client.memberClaimAndPay.findUnique({
        where: { memberClaimAndPayId },
      });
      expect(updatedClaimPay).not.toBeNull();
      expect(updatedClaimPay!.statementFileKey).toBe(statementFileKey);
      expect(updatedClaimPay!.statementIssuedAt!.toISOString()).toBe(statementIssuedAt.toISOString());
    });

    it('存在しないmemberClaimAndPayIdで更新しようとするとエラーになる', async () => {
      const nonExistentId = 999999;
      const statementFileKey = 'test-statement-file-key.pdf';
      const statementIssuedAt = new Date('2024-07-15');
      
      const result = await updateMemberClaimAndPayStatement(nonExistentId, statementFileKey, statementIssuedAt);
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain('Failed to update member claim and pay');
      }
    });

    it('既存のstatementFileKeyとstatementIssuedAtを上書きできる', async () => {
      // 初期データを作成（statementFileKeyとstatementIssuedAtあり）
      const initialStatementFileKey = 'initial-statement-file-key.pdf';
      const initialStatementIssuedAt = new Date('2024-07-10');
      
      const claimPayResult = await createMemberClaimAndPay(
        member.memberId, 
        occurredDate, 
        2000, 
        1500, 
        initialStatementFileKey, 
        initialStatementIssuedAt
      );
      expect(claimPayResult.isOk()).toBe(true);
      if (!claimPayResult.isOk()) return;
      
      const memberClaimAndPayId = claimPayResult.value.memberClaimAndPayId;
      
      // 新しい値で更新
      const newStatementFileKey = 'new-statement-file-key.pdf';
      const newStatementIssuedAt = new Date('2024-07-20');
      
      const result = await updateMemberClaimAndPayStatement(memberClaimAndPayId, newStatementFileKey, newStatementIssuedAt);
      expect(result.isOk()).toBe(true);
      
      // データベースで更新を確認
      const updatedClaimPay = await vPrisma.client.memberClaimAndPay.findUnique({
        where: { memberClaimAndPayId },
      });
      expect(updatedClaimPay).not.toBeNull();
      expect(updatedClaimPay!.statementFileKey).toBe(newStatementFileKey);
      expect(updatedClaimPay!.statementIssuedAt!.toISOString()).toBe(newStatementIssuedAt.toISOString());
      
      // 古い値が上書きされていることを確認
      expect(updatedClaimPay!.statementFileKey).not.toBe(initialStatementFileKey);
      expect(updatedClaimPay!.statementIssuedAt!.toISOString()).not.toBe(initialStatementIssuedAt.toISOString());
    });
  });
});
