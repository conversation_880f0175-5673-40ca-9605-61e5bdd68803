import { create } from '@bufbuild/protobuf';
import { err, ok, ResultAsync } from 'neverthrow';
import {
  HorseIncomePrize,
  HorseIncomeOther,
  HorseIncomePrizeAllowance,
  HorseIncomePrizeAllowanceName,
  IncomeType,
  HorseIncomePrizeOrganizer,
  HorseIncomeOtherName,
  HorseIncomeSchema,
  HorseIncomePrizeSchema,
  HorseIncomeOtherSchema,
  HorseIncomePrizeAllowanceSchema,
  HorseIncomePrizeAllowanceNameSchema,
} from '@hami/core-admin-api-schema/horse_income_service_pb';

import {
  Prisma,
  HorseIncomePrizeOrganizer as PrismaHorseIncomePrizeOrganizer,
  HorseIncomeOtherName as PrismaHorseIncomeOtherName,
} from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';
import { fromString } from '../utils/decimal';

export class HorseIncomePrizeNotFoundError extends Error {
  readonly name = 'HorseIncomePrizeNotFoundError';
}

export class HorseIncomeOtherNotFoundError extends Error {
  readonly name = 'HorseIncomeOtherNotFoundError';
}

export class HorseIncomePrizeAllowanceNotFoundError extends Error {
  readonly name = 'HorseIncomePrizeAllowanceNotFoundError';
}

export class HorseIncomePrizeAllowanceNameNotFoundError extends Error {
  readonly name = 'HorseIncomePrizeAllowanceNameNotFoundError';
}

// 馬の収入一覧取得（賞金収入とその他収入をマージ）
export const listHorseIncomes = (params: { horseId?: number; limit?: number; offset?: number }) => {
  const { horseId, limit, offset = 0 } = params;

  const whereConditions: { horseId?: number } = {};
  if (horseId) {
    whereConditions.horseId = horseId;
  }

  return ResultAsync.fromPromise(
    Promise.all([
      client.horseIncomeView.findMany({
        where: whereConditions,
        take: limit,
        skip: offset,
        orderBy: [{ occurredYear: 'desc' }, { occurredMonth: 'desc' }, { occurredDay: 'desc' }, { incomeType: 'asc' }, { id: 'desc' }],
      }),
      client.horseIncomeView.count({
        where: whereConditions,
      }),
      client.horse.findMany({
        select: { horseId: true, horseName: true },
      }),
    ]),
    () => new DatabaseError('Failed to query horse incomes')
  ).map(([incomes, totalCount, horses]) => {
    // 馬のマスタ情報をMapに変換
    const horseMap = new Map(horses.map(horse => [horse.horseId, horse.horseName]));
    const totalPages = totalCount > 0 ? Math.ceil(totalCount / (limit ?? totalCount)) : 0;
    return {
      incomes: incomes.map((income) =>
        create(HorseIncomeSchema, {
          id: income.id,
          closing: income.closing,
          incomeYearMonth: income.incomeYearMonth,
          occurredYear: income.occurredYear,
          occurredMonth: income.occurredMonth,
          occurredDay: income.occurredDay,
          horseId: income.horseId,
          horseName: horseMap.get(income.horseId) || '',
          incomeType: income.incomeType === 'prize' ? IncomeType.PRIZE : IncomeType.OTHER,
          name: income.incomeType === 'prize' ? income.raceName || '' : income.name || '',
          name2: income.incomeType === 'prize' ? income.raceResult || '' : income.nameOther || '',
          amount: income.amount,
        })
      ),
      totalCount, totalPages,
    };
  });
};

// 全馬の収入一覧取得（日付範囲指定）
export const listAllHorseIncomes = (params: { 
  startYear: number; 
  startMonth: number; 
  startDay: number; 
  endYear: number; 
  endMonth: number; 
  endDay: number; 
}) => {
  const { startYear, startMonth, startDay, endYear, endMonth, endDay } = params;

  return ResultAsync.fromPromise(
    Promise.all([
      client.horseIncomeView.findMany({
        where: {
          AND: [
            // 開始日以降の条件
            {
              OR: [
                { occurredYear: { gt: startYear } },
                { 
                  occurredYear: startYear,
                  OR: [
                    { occurredMonth: { gt: startMonth } },
                    { occurredMonth: startMonth, occurredDay: { gte: startDay } }
                  ]
                }
              ]
            },
            // 終了日以前の条件
            {
              OR: [
                { occurredYear: { lt: endYear } },
                { 
                  occurredYear: endYear,
                  OR: [
                    { occurredMonth: { lt: endMonth } },
                    { occurredMonth: endMonth, occurredDay: { lte: endDay } }
                  ]
                }
              ]
            }
          ]
        },
        orderBy: [{ occurredYear: 'desc' }, { occurredMonth: 'desc' }, { occurredDay: 'desc' }, { incomeType: 'asc' }, { id: 'desc' }],
      }),
      client.horse.findMany({
        select: { horseId: true, horseName: true },
      }),
    ]),
    () => new DatabaseError('Failed to query all horse incomes')
  ).map(([incomes, horses]) => {
    // 馬のマスタ情報をMapに変換
    const horseMap = new Map(horses.map(horse => [horse.horseId, horse.horseName]));
    
    return {
      incomes: incomes.map((income) =>
        create(HorseIncomeSchema, {
          id: income.id,
          closing: income.closing,
          incomeYearMonth: income.incomeYearMonth,
          occurredYear: income.occurredYear,
          occurredMonth: income.occurredMonth,
          occurredDay: income.occurredDay,
          horseId: income.horseId,
          horseName: horseMap.get(income.horseId) || '',
          incomeType: income.incomeType === 'prize' ? IncomeType.PRIZE : IncomeType.OTHER,
          name: income.incomeType === 'prize' ? income.raceName || '' : income.name || '',
          name2: income.incomeType === 'prize' ? income.raceResult || '' : income.nameOther || '',
          amount: income.amount,
        })
      ),
    };
  });
};

// 馬の賞金収入の一覧を取得
export const listHorseIncomePrize = (params: { horseId: number; incomeYearMonth?: number; limit?: number; offset?: number }) => {
  return ResultAsync.fromPromise(
    client.horseIncomePrize.findMany({
      where: { horseId: params.horseId, incomeYearMonth: params.incomeYearMonth || undefined },
      take: params.limit,
      skip: params.offset,
      orderBy: [{ occurredYear: 'desc' }, { occurredMonth: 'desc' }, { occurredDay: 'desc' }, { horseIncomePrizeId: 'desc' }],
    }),
    () => new DatabaseError('Failed to query horse income prize')
  ).map((incomes) => {
    return incomes.map((income) =>
      create(HorseIncomePrizeSchema, {
        horseIncomePrizeId: income.horseIncomePrizeId,
        horseId: income.horseId,
        incomeYearMonth: income.incomeYearMonth,
        occurredYear: income.occurredYear,
        occurredMonth: income.occurredMonth,
        occurredDay: income.occurredDay,
        racePlace: income.racePlace,
        raceName: income.raceName,
        raceResult: income.raceResult,
        organizer: mapOrganizerFromPrisma(income.organizer),
        mainPrizeAmount: income.mainPrizeAmount,
        appearanceFee: income.appearanceFee,
        withholdingTax: income.withholdingTax,
        commissionAmount: income.commissionAmount,
        clubFeeRate: income.clubFeeRate.toString(),
        taxRate: income.taxRate.toString(),
        totalPrizeAmount: income.totalPrizeAmount,
        clubFeeAmount: income.clubFeeAmount,
        taxAmount: income.taxAmount,
        incomeAmount: income.incomeAmount,
        note: income.note,
        closing: income.closing,
      })
    );
  });
};

// 馬の賞金収入詳細取得
export const getHorseIncomePrize = ({ id }: { id: number }) => {
  return ResultAsync.fromPromise(
    client.horseIncomePrize.findUnique({
      where: { horseIncomePrizeId: id },
      include: { allowances: true },
    }),
    () => new DatabaseError('Failed to query horse income prize')
  ).andThen((prize) => {
    if (!prize) {
      return err(new HorseIncomePrizeNotFoundError());
    }
    return ok(mapToHorseIncomePrize(prize));
  });
};

// 馬の賞金収入作成
export const createHorseIncomePrize = (data: {
  horseId: number;
  incomeYearMonth: number;
  occurredYear: number;
  occurredMonth: number;
  occurredDay: number;
  racePlace: string;
  raceName: string;
  raceResult: string;
  organizer: HorseIncomePrizeOrganizer;
  mainPrizeAmount: number;
  appearanceFee: number;
  withholdingTax: number;
  commissionAmount: number;
  clubFeeRate: string;
  taxRate: string;
  totalPrizeAmount: number;
  clubFeeAmount: number;
  taxAmount: number;
  incomeAmount: number;
  note: string;
  allowances: Array<{ name: string; amount: number }>;
}) => {
  return ResultAsync.fromPromise(
    client.horseIncomePrize.create({
      data: {
        horseId: data.horseId,
        incomeYearMonth: data.incomeYearMonth,
        occurredYear: data.occurredYear,
        occurredMonth: data.occurredMonth,
        occurredDay: data.occurredDay,
        racePlace: data.racePlace,
        raceName: data.raceName,
        raceResult: data.raceResult,
        organizer: mapOrganizerToPrisma(data.organizer),
        mainPrizeAmount: data.mainPrizeAmount,
        appearanceFee: data.appearanceFee,
        withholdingTax: data.withholdingTax,
        commissionAmount: data.commissionAmount,
        clubFeeRate: fromString(data.clubFeeRate),
        taxRate: fromString(data.taxRate),
        totalPrizeAmount: data.totalPrizeAmount,
        clubFeeAmount: data.clubFeeAmount,
        taxAmount: data.taxAmount,
        incomeAmount: data.incomeAmount,
        note: data.note,
        allowances: {
          create: data.allowances.map((allowance) => ({
            name: allowance.name,
            amount: allowance.amount,
          })),
        },
      },
      include: { allowances: true },
    }),
    () => new DatabaseError('Failed to create horse income prize')
  ).map(mapToHorseIncomePrize);
};

// 馬の賞金収入更新
export const updateHorseIncomePrize = (
  id: number,
  data: {
    horseId: number;
    incomeYearMonth: number;
    occurredYear: number;
    occurredMonth: number;
    occurredDay: number;
    racePlace: string;
    raceName: string;
    raceResult: string;
    organizer: HorseIncomePrizeOrganizer;
    mainPrizeAmount: number;
    appearanceFee: number;
    withholdingTax: number;
    commissionAmount: number;
    clubFeeRate: string;
    taxRate: string;
    totalPrizeAmount: number;
    clubFeeAmount: number;
    taxAmount: number;
    incomeAmount: number;
    note: string;
    allowances: Array<{ name: string; amount: number }>;
  }
): ResultAsync<Record<string, never>, DatabaseError | HorseIncomePrizeNotFoundError> => {
  return ResultAsync.fromPromise(
    client.horseIncomePrize.update({
      where: { horseIncomePrizeId: id },
      data: {
        horseId: data.horseId,
        incomeYearMonth: data.incomeYearMonth,
        occurredYear: data.occurredYear,
        occurredMonth: data.occurredMonth,
        occurredDay: data.occurredDay,
        racePlace: data.racePlace,
        raceName: data.raceName,
        raceResult: data.raceResult,
        organizer: mapOrganizerToPrisma(data.organizer),
        mainPrizeAmount: data.mainPrizeAmount,
        appearanceFee: data.appearanceFee,
        withholdingTax: data.withholdingTax,
        commissionAmount: data.commissionAmount,
        clubFeeRate: fromString(data.clubFeeRate),
        taxRate: fromString(data.taxRate),
        totalPrizeAmount: data.totalPrizeAmount,
        clubFeeAmount: data.clubFeeAmount,
        taxAmount: data.taxAmount,
        incomeAmount: data.incomeAmount,
        note: data.note,
        allowances: {
          deleteMany: {},
          create: data.allowances.map((allowance) => ({
            name: allowance.name,
            amount: allowance.amount,
          })),
        },
      },
    }),
    (error) => {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return new HorseIncomePrizeNotFoundError();
      }
      return new DatabaseError('Failed to update horse income prize');
    }
  ).map(() => ({}));
};

// 馬の賞金収入削除
export const deleteHorseIncomePrize = ({ id }: { id: number }) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // まず関連する手当を削除
      await tx.horseIncomePrizeAllowance.deleteMany({
        where: { horseIncomePrizeId: id },
      });

      // その後、賞金収入を削除
      await tx.horseIncomePrize.delete({
        where: { horseIncomePrizeId: id },
      });
    }),
    (error) => {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return new HorseIncomePrizeNotFoundError();
      }
      return new DatabaseError('Failed to delete horse income prize');
    }
  ).map(() => ({}));
};

// 馬のその他収入一覧取得
export const listHorseIncomeOther = (params: { horseId: number; incomeYearMonth?: number; limit?: number; offset?: number }) => {
  return ResultAsync.fromPromise(
    client.horseIncomeOther.findMany({ where: { horseId: params.horseId, incomeYearMonth: params.incomeYearMonth || undefined }, take: params.limit, skip: params.offset, orderBy: [{ occurredYear: 'desc' }, { occurredMonth: 'desc' }, { occurredDay: 'desc' }, { horseIncomeOtherId: 'desc' }], }),
    () => new DatabaseError('Failed to query horse income other')
  ).map((incomes) => {
    return incomes.map((income) => mapToHorseIncomeOther(income));
  });
};

// 馬のその他収入詳細取得
export const getHorseIncomeOther = ({ id }: { id: number }) => {
  return ResultAsync.fromPromise(
    client.horseIncomeOther.findUnique({ where: { horseIncomeOtherId: id } }),
    () => new DatabaseError('Failed to query horse income other')
  ).andThen((other) => {
    if (!other) {
      return err(new HorseIncomeOtherNotFoundError());
    }
    return ok(mapToHorseIncomeOther(other));
  });
};

// 馬のその他収入作成
export const createHorseIncomeOther = (data: {
  horseId: number;
  incomeYearMonth: number;
  occurredYear: number;
  occurredMonth: number;
  occurredDay: number;
  name: HorseIncomeOtherName;
  nameOther: string;
  amount: number;
  salesCommission: number;
  otherFeeName: string;
  otherFeeAmount: number;
  taxRate: string;
  taxAmount: number;
  incomeAmount: number;
  note: string;
}) => {
  return ResultAsync.fromPromise(
    client.horseIncomeOther.create({
      data: {
        horseId: data.horseId,
        incomeYearMonth: data.incomeYearMonth,
        occurredYear: data.occurredYear,
        occurredMonth: data.occurredMonth,
        occurredDay: data.occurredDay,
        name: mapOtherNameToPrisma(data.name),
        nameOther: data.nameOther,
        amount: data.amount,
        salesCommission: data.salesCommission,
        otherFeeName: data.otherFeeName,
        otherFeeAmount: data.otherFeeAmount,
        taxRate: fromString(data.taxRate),
        taxAmount: data.taxAmount,
        incomeAmount: data.incomeAmount,
        note: data.note,
      },
    }),
    () => new DatabaseError('Failed to create horse income other')
  ).map(mapToHorseIncomeOther);
};

// 馬のその他収入更新
export const updateHorseIncomeOther = (
  id: number,
  data: {
    horseId: number;
    incomeYearMonth: number;
    occurredYear: number;
    occurredMonth: number;
    occurredDay: number;
    name: HorseIncomeOtherName;
    nameOther: string;
    amount: number;
    salesCommission: number;
    otherFeeName: string;
    otherFeeAmount: number;
    taxRate: string;
    taxAmount: number;
    incomeAmount: number;
    note: string;
  }
): ResultAsync<Record<string, never>, DatabaseError | HorseIncomeOtherNotFoundError> => {
  return ResultAsync.fromPromise(
    client.horseIncomeOther.update({
      where: { horseIncomeOtherId: id },
      data: {
        horseId: data.horseId,
        incomeYearMonth: data.incomeYearMonth,
        occurredYear: data.occurredYear,
        occurredMonth: data.occurredMonth,
        occurredDay: data.occurredDay,
        name: mapOtherNameToPrisma(data.name),
        nameOther: data.nameOther,
        amount: data.amount,
        salesCommission: data.salesCommission,
        otherFeeName: data.otherFeeName,
        otherFeeAmount: data.otherFeeAmount,
        taxRate: fromString(data.taxRate),
        taxAmount: data.taxAmount,
        incomeAmount: data.incomeAmount,
        note: data.note,
      },
    }),
    (error) => {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return new HorseIncomeOtherNotFoundError();
      }
      return new DatabaseError('Failed to update horse income other');
    }
  ).map(() => ({}));
};

// 馬のその他収入削除
export const deleteHorseIncomeOther = ({ id }: { id: number }) => {
  return ResultAsync.fromPromise(
    client.horseIncomeOther.delete({
      where: { horseIncomeOtherId: id },
    }),
    (error) => {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return new HorseIncomeOtherNotFoundError();
      }
      return new DatabaseError('Failed to delete horse income other');
    }
  ).map(() => ({}));
};

// 賞金収入手当名一覧取得
export const listHorseIncomePrizeAllowanceNames = () => {
  return ResultAsync.fromPromise(
    client.horseIncomePrizeAllowanceName.findMany({
      orderBy: { horseIncomePrizeAllowanceNameId: 'asc' },
    }),
    () => new DatabaseError('Failed to query horse income prize allowance names')
  ).map((names) => names.map(mapToHorseIncomePrizeAllowanceName));
};

/**
 * PrismaのモデルをHorseIncomePrizeに変換
 */
const mapToHorseIncomePrize = (prize: Prisma.HorseIncomePrizeGetPayload<{ include: { allowances: true } }>): HorseIncomePrize => {
  return create(HorseIncomePrizeSchema, {
    horseIncomePrizeId: prize.horseIncomePrizeId,
    horseId: prize.horseId,
    incomeYearMonth: prize.incomeYearMonth,
    occurredYear: prize.occurredYear,
    occurredMonth: prize.occurredMonth,
    occurredDay: prize.occurredDay,
    racePlace: prize.racePlace,
    raceName: prize.raceName,
    raceResult: prize.raceResult,
    organizer: mapOrganizerFromPrisma(prize.organizer),
    mainPrizeAmount: prize.mainPrizeAmount,
    appearanceFee: prize.appearanceFee,
    withholdingTax: prize.withholdingTax,
    commissionAmount: prize.commissionAmount,
    clubFeeRate: prize.clubFeeRate.toString(),
    taxRate: prize.taxRate.toString(),
    totalPrizeAmount: prize.totalPrizeAmount,
    clubFeeAmount: prize.clubFeeAmount,
    taxAmount: prize.taxAmount,
    incomeAmount: prize.incomeAmount,
    note: prize.note,
    closing: prize.closing,
    allowances: prize.allowances.map(mapToHorseIncomePrizeAllowance),
  });
};

/**
 * PrismaのモデルをHorseIncomeOtherに変換
 */
const mapToHorseIncomeOther = (other: Prisma.HorseIncomeOtherGetPayload<{}>): HorseIncomeOther => {
  return create(HorseIncomeOtherSchema, {
    horseIncomeOtherId: other.horseIncomeOtherId,
    horseId: other.horseId,
    incomeYearMonth: other.incomeYearMonth,
    occurredYear: other.occurredYear,
    occurredMonth: other.occurredMonth,
    occurredDay: other.occurredDay,
    name: mapOtherNameFromPrisma(other.name),
    nameOther: other.nameOther,
    amount: other.amount,
    salesCommission: other.salesCommission,
    otherFeeName: other.otherFeeName,
    otherFeeAmount: other.otherFeeAmount,
    taxRate: other.taxRate.toString(),
    taxAmount: other.taxAmount,
    incomeAmount: other.incomeAmount,
    note: other.note,
    closing: other.closing,
  });
};

/**
 * PrismaのモデルをHorseIncomePrizeAllowanceに変換
 */
const mapToHorseIncomePrizeAllowance = (allowance: Prisma.HorseIncomePrizeAllowanceGetPayload<{}>): HorseIncomePrizeAllowance => {
  return create(HorseIncomePrizeAllowanceSchema, {
    horseIncomePrizeAllowanceId: allowance.horseIncomePrizeAllowanceId,
    horseIncomePrizeId: allowance.horseIncomePrizeId,
    name: allowance.name,
    amount: allowance.amount,
  });
};

/**
 * PrismaのモデルをHorseIncomePrizeAllowanceNameに変換
 */
const mapToHorseIncomePrizeAllowanceName = (name: Prisma.HorseIncomePrizeAllowanceNameGetPayload<{}>): HorseIncomePrizeAllowanceName => {
  return create(HorseIncomePrizeAllowanceNameSchema, {
    horseIncomePrizeAllowanceNameId: name.horseIncomePrizeAllowanceNameId,
    name: name.name,
  });
};

/**
 * Prismaの主催者をBillerの主催者に変換
 */
const mapOrganizerFromPrisma = (organizer: PrismaHorseIncomePrizeOrganizer): HorseIncomePrizeOrganizer => {
  switch (organizer) {
    case PrismaHorseIncomePrizeOrganizer.JRA:
      return HorseIncomePrizeOrganizer.JRA;
    case PrismaHorseIncomePrizeOrganizer.OTHER:
      return HorseIncomePrizeOrganizer.OTHER;
    default:
      return HorseIncomePrizeOrganizer.UNSPECIFIED;
  }
};

/**
 * Billerの主催者をPrismaの主催者に変換
 */
const mapOrganizerToPrisma = (organizer: HorseIncomePrizeOrganizer): PrismaHorseIncomePrizeOrganizer => {
  switch (organizer) {
    case HorseIncomePrizeOrganizer.JRA:
      return PrismaHorseIncomePrizeOrganizer.JRA;
    case HorseIncomePrizeOrganizer.OTHER:
      return PrismaHorseIncomePrizeOrganizer.OTHER;
    default:
      return PrismaHorseIncomePrizeOrganizer.OTHER;
  }
};

/**
 * Prismaのその他収入名目をBillerのその他収入名目に変換
 */
const mapOtherNameFromPrisma = (name: PrismaHorseIncomeOtherName): HorseIncomeOtherName => {
  switch (name) {
    case PrismaHorseIncomeOtherName.SALES_DIVIDEND:
      return HorseIncomeOtherName.SALES_DIVIDEND;
    case PrismaHorseIncomeOtherName.INSURANCE:
      return HorseIncomeOtherName.INSURANCE;
    case PrismaHorseIncomeOtherName.SYMPATHY:
      return HorseIncomeOtherName.SYMPATHY;
    case PrismaHorseIncomeOtherName.GRANT:
      return HorseIncomeOtherName.GRANT;
    case PrismaHorseIncomeOtherName.OTHER:
      return HorseIncomeOtherName.OTHER;
    default:
      return HorseIncomeOtherName.UNSPECIFIED;
  }
};

/**
 * Billerのその他収入名目をPrismaのその他収入名目に変換
 */
const mapOtherNameToPrisma = (name: HorseIncomeOtherName): PrismaHorseIncomeOtherName => {
  switch (name) {
    case HorseIncomeOtherName.SALES_DIVIDEND:
      return PrismaHorseIncomeOtherName.SALES_DIVIDEND;
    case HorseIncomeOtherName.INSURANCE:
      return PrismaHorseIncomeOtherName.INSURANCE;
    case HorseIncomeOtherName.SYMPATHY:
      return PrismaHorseIncomeOtherName.SYMPATHY;
    case HorseIncomeOtherName.GRANT:
      return PrismaHorseIncomeOtherName.GRANT;
    case HorseIncomeOtherName.OTHER:
      return PrismaHorseIncomeOtherName.OTHER;
    default:
      return PrismaHorseIncomeOtherName.OTHER;
  }
};
