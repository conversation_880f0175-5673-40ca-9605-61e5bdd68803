import { ResultAsync } from 'neverthrow';
import type {
  MonthlyGmoProcessingBatch,
  MonthlyGmoProcessingRecord,
  MonthlyGmoProcessingLog,
  Member,
  MemberClaimAndPay,
} from '@hami/prisma';
import { MonthlyProcessingStatus, GmoProcessingType, GmoProcessingStatus } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';

/**
 * ログレベルの型定義
 */
export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';

// リレーション付きの型定義
export type MonthlyGmoProcessingRecordWithRelations = MonthlyGmoProcessingRecord & {
  member: Member;
  memberClaimAndPay: MemberClaimAndPay;
};

/**
 * 月締めGMO処理バッチを作成
 */
export async function createMonthlyGmoProcessingBatch(
  targetDate: Date,
  totalTransferCount: number,
  totalRemittanceCount: number
): Promise<ResultAsync<MonthlyGmoProcessingBatch, Error>> {
  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingBatch.create({
      data: {
        targetDate,
        totalTransferCount,
        totalRemittanceCount,
        status: MonthlyProcessingStatus.PENDING,
      },
    }),
    (error) => new Error(`Failed to create monthly GMO processing batch: ${error}`)
  );
}

/**
 * 月締めGMO処理バッチと関連する処理記録を原子的に作成
 * 複数のテーブルを更新するため、トランザクションを使用してデータ整合性を保証
 */
export async function createMonthlyGmoProcessingBatchWithRecords(
  targetDate: Date,
  transferRecords: Array<{
    memberId: number;
    memberClaimAndPayId: number;
    amount: number;
  }>,
  remittanceRecords: Array<{
    memberId: number;
    memberClaimAndPayId: number;
    amount: number;
  }>
): Promise<ResultAsync<MonthlyGmoProcessingBatch, Error>> {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // バッチを作成
      const batch = await tx.monthlyGmoProcessingBatch.create({
        data: {
          targetDate,
          totalTransferCount: transferRecords.length,
          totalRemittanceCount: remittanceRecords.length,
          status: MonthlyProcessingStatus.PENDING,
        },
      });

      // 振替処理記録を作成
      for (const record of transferRecords) {
        await tx.monthlyGmoProcessingRecord.create({
          data: {
            monthlyGmoProcessingBatchId: batch.monthlyGmoProcessingBatchId,
            memberId: record.memberId,
            memberClaimAndPayId: record.memberClaimAndPayId,
            processingType: GmoProcessingType.BANK_TRANSFER,
            amount: record.amount,
            status: GmoProcessingStatus.PENDING,
          },
        });
      }

      // 送金処理記録を作成
      for (const record of remittanceRecords) {
        await tx.monthlyGmoProcessingRecord.create({
          data: {
            monthlyGmoProcessingBatchId: batch.monthlyGmoProcessingBatchId,
            memberId: record.memberId,
            memberClaimAndPayId: record.memberClaimAndPayId,
            processingType: GmoProcessingType.REMITTANCE,
            amount: record.amount,
            status: GmoProcessingStatus.PENDING,
          },
        });
      }

      return batch;
    }),
    (error) => new Error(`Failed to create monthly GMO processing batch with records: ${error}`)
  );
}

/**
 * 月締めGMO処理バッチを取得（対象月で検索）
 */
export async function getMonthlyGmoProcessingBatchByTargetDate(
  targetDate: Date
): Promise<ResultAsync<MonthlyGmoProcessingBatch | null, Error>> {
  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingBatch.findUnique({
      where: {
        targetDate,
      },
    }),
    (error) => new Error(`Failed to get monthly GMO processing batch: ${error}`)
  );
}

/**
 * 月締めGMO処理バッチの状態を更新
 */
export async function updateMonthlyGmoProcessingBatchStatus(
  batchId: number,
  status: MonthlyProcessingStatus,
  errorMessage?: string
): Promise<ResultAsync<MonthlyGmoProcessingBatch, Error>> {
  const updateData: {
    status: MonthlyProcessingStatus;
    updatedAt: Date;
    startedAt?: Date;
    completedAt?: Date;
    errorMessage?: string;
  } = {
    status,
    updatedAt: new Date(),
  };

  if (status === MonthlyProcessingStatus.RUNNING) {
    updateData.startedAt = new Date();
  } else if (status === MonthlyProcessingStatus.COMPLETED || status === MonthlyProcessingStatus.FAILED) {
    updateData.completedAt = new Date();
  }

  if (errorMessage) {
    updateData.errorMessage = errorMessage;
  }

  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingBatch.update({
      where: { monthlyGmoProcessingBatchId: batchId },
      data: updateData,
    }),
    (error) => new Error(`Failed to update monthly GMO processing batch status: ${error}`)
  );
}

/**
 * 月締めGMO処理記録を作成
 */
export async function createMonthlyGmoProcessingRecord(
  batchId: number,
  memberId: number,
  memberClaimAndPayId: number,
  processingType: GmoProcessingType,
  amount: number
): Promise<ResultAsync<MonthlyGmoProcessingRecord, Error>> {
  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingRecord.create({
      data: {
        monthlyGmoProcessingBatchId: batchId,
        memberId,
        memberClaimAndPayId,
        processingType,
        amount: amount,
        status: GmoProcessingStatus.PENDING,
      },
    }),
    (error) => new Error(`Failed to create monthly GMO processing record: ${error}`)
  );
}

/**
 * 月締めGMO処理記録の状態を更新
 */
export async function updateMonthlyGmoProcessingRecordStatus(
  recordId: number,
  status: GmoProcessingStatus,
  gmoData?: {
    gmoOrderId?: string;
    gmoAccessId?: string;
    gmoAccessPass?: string;
    gmoBankId?: string;
    gmoDepositId?: string;
    gmoTransactionId?: string;
    targetDate?: Date;
    errorCode?: string;
    errorDetail?: string;
    errorMessage?: string;
  }
): Promise<ResultAsync<MonthlyGmoProcessingRecord, Error>> {
  const updateData: {
    status: GmoProcessingStatus;
    updatedAt: Date;
    processedAt?: Date;
    gmoOrderId?: string;
    gmoAccessId?: string;
    gmoAccessPass?: string;
    gmoBankId?: string;
    gmoDepositId?: string;
    gmoTransactionId?: string;
    targetDate?: Date;
    errorCode?: string;
    errorDetail?: string;
    errorMessage?: string;
  } = {
    status,
    updatedAt: new Date(),
  };

  if (status === GmoProcessingStatus.COMPLETED || status === GmoProcessingStatus.FAILED) {
    updateData.processedAt = new Date();
  }

  if (gmoData) {
    if (gmoData.gmoOrderId !== undefined) updateData.gmoOrderId = gmoData.gmoOrderId;
    if (gmoData.gmoAccessId !== undefined) updateData.gmoAccessId = gmoData.gmoAccessId;
    if (gmoData.gmoAccessPass !== undefined) updateData.gmoAccessPass = gmoData.gmoAccessPass;
    if (gmoData.gmoBankId !== undefined) updateData.gmoBankId = gmoData.gmoBankId;
    if (gmoData.gmoDepositId !== undefined) updateData.gmoDepositId = gmoData.gmoDepositId;
    if (gmoData.gmoTransactionId !== undefined) updateData.gmoTransactionId = gmoData.gmoTransactionId;
    if (gmoData.targetDate !== undefined) updateData.targetDate = gmoData.targetDate;
    if (gmoData.errorCode !== undefined) updateData.errorCode = gmoData.errorCode;
    if (gmoData.errorDetail !== undefined) updateData.errorDetail = gmoData.errorDetail;
    if (gmoData.errorMessage !== undefined) updateData.errorMessage = gmoData.errorMessage;
  }

  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingRecord.update({
      where: { monthlyGmoProcessingRecordId: recordId },
      data: updateData,
    }),
    (error) => new Error(`Failed to update monthly GMO processing record status: ${error}`)
  );
}

/**
 * 月締めGMO処理記録の状態とバッチ統計、ログを原子的に更新
 * 複数のテーブル（record, batch, log）を同時に更新するため、トランザクションを使用
 */
export async function updateMonthlyGmoProcessingRecordWithBatchStatsAndLog(
  recordId: number,
  batchId: number,
  status: GmoProcessingStatus,
  logData: {
    logLevel: LogLevel;
    message: string;
    apiEndpoint?: string;
    requestData?: string;
    responseData?: string;
    fromStatus?: GmoProcessingStatus;
  },
  gmoData?: {
    gmoOrderId?: string;
    gmoAccessId?: string;
    gmoAccessPass?: string;
    gmoBankId?: string;
    gmoDepositId?: string;
    gmoTransactionId?: string;
    targetDate?: Date;
    errorCode?: string;
    errorDetail?: string;
    errorMessage?: string;
  }
): Promise<ResultAsync<MonthlyGmoProcessingRecord, Error>> {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 記録の状態を更新
      const updateData: {
        status: GmoProcessingStatus;
        updatedAt: Date;
        processedAt?: Date;
        gmoOrderId?: string;
        gmoAccessId?: string;
        gmoAccessPass?: string;
        gmoBankId?: string;
        gmoDepositId?: string;
        gmoTransactionId?: string;
        targetDate?: Date;
        errorCode?: string;
        errorDetail?: string;
        errorMessage?: string;
      } = {
        status,
        updatedAt: new Date(),
      };

      if (status === GmoProcessingStatus.COMPLETED || status === GmoProcessingStatus.FAILED) {
        updateData.processedAt = new Date();
      }

      if (gmoData) {
        if (gmoData.gmoOrderId !== undefined) updateData.gmoOrderId = gmoData.gmoOrderId;
        if (gmoData.gmoAccessId !== undefined) updateData.gmoAccessId = gmoData.gmoAccessId;
        if (gmoData.gmoAccessPass !== undefined) updateData.gmoAccessPass = gmoData.gmoAccessPass;
        if (gmoData.gmoBankId !== undefined) updateData.gmoBankId = gmoData.gmoBankId;
        if (gmoData.gmoDepositId !== undefined) updateData.gmoDepositId = gmoData.gmoDepositId;
        if (gmoData.gmoTransactionId !== undefined) updateData.gmoTransactionId = gmoData.gmoTransactionId;
        if (gmoData.targetDate !== undefined) updateData.targetDate = gmoData.targetDate;
        if (gmoData.errorCode !== undefined) updateData.errorCode = gmoData.errorCode;
        if (gmoData.errorDetail !== undefined) updateData.errorDetail = gmoData.errorDetail;
        if (gmoData.errorMessage !== undefined) updateData.errorMessage = gmoData.errorMessage;
      }

      const updatedRecord = await tx.monthlyGmoProcessingRecord.update({
        where: { monthlyGmoProcessingRecordId: recordId },
        data: updateData,
      });

      // ログエントリを作成
      await tx.monthlyGmoProcessingLog.create({
        data: {
          monthlyGmoProcessingRecordId: recordId,
          logLevel: logData.logLevel,
          message: logData.message,
          apiEndpoint: logData.apiEndpoint,
          requestData: logData.requestData,
          responseData: logData.responseData,
          fromStatus: logData.fromStatus,
          toStatus: status,
        },
      });

      // バッチ統計を更新（COMPLETED や FAILED の場合のみ）
      if (status === GmoProcessingStatus.COMPLETED || status === GmoProcessingStatus.FAILED) {
        // 現在のバッチ統計を取得して更新
        const batch = await tx.monthlyGmoProcessingBatch.findUnique({
          where: { monthlyGmoProcessingBatchId: batchId },
          select: {
            completedTransferCount: true,
            completedRemittanceCount: true,
            failedTransferCount: true,
            failedRemittanceCount: true,
          },
        });

        if (batch) {
          const batchUpdateData: {
            updatedAt: Date;
            completedTransferCount?: number;
            completedRemittanceCount?: number;
            failedTransferCount?: number;
            failedRemittanceCount?: number;
          } = {
            updatedAt: new Date(),
          };

          if (updatedRecord.processingType === GmoProcessingType.BANK_TRANSFER) {
            if (status === GmoProcessingStatus.COMPLETED) {
              batchUpdateData.completedTransferCount = (batch.completedTransferCount || 0) + 1;
            } else if (status === GmoProcessingStatus.FAILED) {
              batchUpdateData.failedTransferCount = (batch.failedTransferCount || 0) + 1;
            }
          } else if (updatedRecord.processingType === GmoProcessingType.REMITTANCE) {
            if (status === GmoProcessingStatus.COMPLETED) {
              batchUpdateData.completedRemittanceCount = (batch.completedRemittanceCount || 0) + 1;
            } else if (status === GmoProcessingStatus.FAILED) {
              batchUpdateData.failedRemittanceCount = (batch.failedRemittanceCount || 0) + 1;
            }
          }

          await tx.monthlyGmoProcessingBatch.update({
            where: { monthlyGmoProcessingBatchId: batchId },
            data: batchUpdateData,
          });
        }
      }

      return updatedRecord;
    }),
    (error) => new Error(`Failed to update monthly GMO processing record with batch stats and log: ${error}`)
  );
}

/**
 * 月締めGMO処理記録のリトライ回数を増加
 */
export async function incrementMonthlyGmoProcessingRecordRetryCount(
  recordId: number
): Promise<ResultAsync<MonthlyGmoProcessingRecord, Error>> {
  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingRecord.update({
      where: { monthlyGmoProcessingRecordId: recordId },
      data: {
        retryCount: {
          increment: 1,
        },
        updatedAt: new Date(),
      },
    }),
    (error) => new Error(`Failed to increment retry count: ${error}`)
  );
}

/**
 * 月締めGMO処理記録のリトライ回数を増加し、ログを原子的に作成
 * リトライ処理では記録の更新とログ作成を同時に行うため、トランザクションを使用
 */
export async function incrementMonthlyGmoProcessingRecordRetryCountWithLog(
  recordId: number,
  logData: {
    logLevel: LogLevel;
    message: string;
    apiEndpoint?: string;
    requestData?: string;
    responseData?: string;
    fromStatus?: GmoProcessingStatus;
    toStatus?: GmoProcessingStatus;
  }
): Promise<ResultAsync<MonthlyGmoProcessingRecord, Error>> {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // リトライ回数を増加
      const updatedRecord = await tx.monthlyGmoProcessingRecord.update({
        where: { monthlyGmoProcessingRecordId: recordId },
        data: {
          retryCount: {
            increment: 1,
          },
          updatedAt: new Date(),
        },
      });

      // ログエントリを作成
      await tx.monthlyGmoProcessingLog.create({
        data: {
          monthlyGmoProcessingRecordId: recordId,
          logLevel: logData.logLevel,
          message: logData.message,
          apiEndpoint: logData.apiEndpoint,
          requestData: logData.requestData,
          responseData: logData.responseData,
          fromStatus: logData.fromStatus,
          toStatus: logData.toStatus,
        },
      });

      return updatedRecord;
    }),
    (error) => new Error(`Failed to increment retry count with log: ${error}`)
  );
}

/**
 * 月締めGMO処理ログを作成
 */
export async function createMonthlyGmoProcessingLog(
  recordId: number,
  logLevel: LogLevel,
  message: string,
  apiEndpoint?: string,
  requestData?: string,
  responseData?: string,
  fromStatus?: GmoProcessingStatus,
  toStatus?: GmoProcessingStatus
): Promise<ResultAsync<MonthlyGmoProcessingLog, Error>> {
  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingLog.create({
      data: {
        monthlyGmoProcessingRecordId: recordId,
        logLevel,
        message,
        apiEndpoint,
        requestData,
        responseData,
        fromStatus,
        toStatus,
      },
    }),
    (error) => new Error(`Failed to create monthly GMO processing log: ${error}`)
  );
}

/**
 * バッチIDで処理記録一覧を取得
 */
export async function listMonthlyGmoProcessingRecordsByBatchId(
  batchId: number
): Promise<ResultAsync<MonthlyGmoProcessingRecordWithRelations[], Error>> {
  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingRecord.findMany({
      where: {
        monthlyGmoProcessingBatchId: batchId,
      },
      include: {
        member: true,
        memberClaimAndPay: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    }),
    (error) => new Error(`Failed to list monthly GMO processing records: ${error}`)
  );
}

/**
 * 処理待ち状態の記録を取得
 */
export async function listPendingMonthlyGmoProcessingRecords(
  batchId: number,
  processingType?: GmoProcessingType
): Promise<ResultAsync<MonthlyGmoProcessingRecordWithRelations[], Error>> {
  const whereCondition: {
    monthlyGmoProcessingBatchId: number;
    status: GmoProcessingStatus;
    processingType?: GmoProcessingType;
  } = {
    monthlyGmoProcessingBatchId: batchId,
    status: GmoProcessingStatus.PENDING,
  };

  if (processingType) {
    whereCondition.processingType = processingType;
  }

  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingRecord.findMany({
      where: whereCondition,
      include: {
        member: true,
        memberClaimAndPay: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    }),
    (error) => new Error(`Failed to list pending monthly GMO processing records: ${error}`)
  );
}

/**
 * バッチの処理統計を更新
 */
export async function updateMonthlyGmoProcessingBatchStats(
  batchId: number,
  completedTransferCount?: number,
  completedRemittanceCount?: number,
  failedTransferCount?: number,
  failedRemittanceCount?: number
): Promise<ResultAsync<MonthlyGmoProcessingBatch, Error>> {
  const updateData: {
    updatedAt: Date;
    completedTransferCount?: number;
    completedRemittanceCount?: number;
    failedTransferCount?: number;
    failedRemittanceCount?: number;
  } = {
    updatedAt: new Date(),
  };

  if (completedTransferCount !== undefined) {
    updateData.completedTransferCount = completedTransferCount;
  }
  if (completedRemittanceCount !== undefined) {
    updateData.completedRemittanceCount = completedRemittanceCount;
  }
  if (failedTransferCount !== undefined) {
    updateData.failedTransferCount = failedTransferCount;
  }
  if (failedRemittanceCount !== undefined) {
    updateData.failedRemittanceCount = failedRemittanceCount;
  }

  return ResultAsync.fromPromise(
    client.monthlyGmoProcessingBatch.update({
      where: { monthlyGmoProcessingBatchId: batchId },
      data: updateData,
    }),
    (error) => new Error(`Failed to update batch stats: ${error}`)
  );
}

/**
 * バッチ統計をDB集計から再計算して更新
 */
export async function recalcMonthlyGmoProcessingBatchStats(batchId: number): Promise<ResultAsync<MonthlyGmoProcessingBatch, Error>> {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      const [completedTransferCount, completedRemittanceCount, failedTransferCount, failedRemittanceCount] = await Promise.all([
        tx.monthlyGmoProcessingRecord.count({
          where: { monthlyGmoProcessingBatchId: batchId, processingType: GmoProcessingType.BANK_TRANSFER, status: 'COMPLETED' },
        }),
        tx.monthlyGmoProcessingRecord.count({
          where: { monthlyGmoProcessingBatchId: batchId, processingType: GmoProcessingType.REMITTANCE, status: 'COMPLETED' },
        }),
        tx.monthlyGmoProcessingRecord.count({
          where: { monthlyGmoProcessingBatchId: batchId, processingType: GmoProcessingType.BANK_TRANSFER, status: 'FAILED' },
        }),
        tx.monthlyGmoProcessingRecord.count({
          where: { monthlyGmoProcessingBatchId: batchId, processingType: GmoProcessingType.REMITTANCE, status: 'FAILED' },
        }),
      ]);

      return tx.monthlyGmoProcessingBatch.update({
        where: { monthlyGmoProcessingBatchId: batchId },
        data: {
          completedTransferCount,
          completedRemittanceCount,
          failedTransferCount,
          failedRemittanceCount,
          updatedAt: new Date(),
        },
      });
    }),
    (error) => new Error(`Failed to recalc batch stats: ${error}`)
  );
}

/**
 * 複数の月締めGMO処理記録を原子的に一括更新
 * 大量の記録を同時に処理する際のデータ整合性を保証するため、トランザクションを使用
 */
export async function bulkUpdateMonthlyGmoProcessingRecords(
  updates: Array<{
    recordId: number;
    status: GmoProcessingStatus;
    gmoData?: {
      gmoOrderId?: string;
      gmoAccessId?: string;
      gmoAccessPass?: string;
      gmoBankId?: string;
      gmoDepositId?: string;
      gmoTransactionId?: string;
      targetDate?: Date;
      errorCode?: string;
      errorDetail?: string;
      errorMessage?: string;
    };
  }>
): Promise<ResultAsync<MonthlyGmoProcessingRecord[], Error>> {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      const updatedRecords: MonthlyGmoProcessingRecord[] = [];

      // 順次更新（デッドロック回避のため、recordIdでソート）
      const sortedUpdates = [...updates].sort((a, b) => a.recordId - b.recordId);

      for (const update of sortedUpdates) {
        const updateData: {
          status: GmoProcessingStatus;
          updatedAt: Date;
          processedAt?: Date;
          gmoOrderId?: string;
          gmoAccessId?: string;
          gmoAccessPass?: string;
          gmoBankId?: string;
          gmoDepositId?: string;
          gmoTransactionId?: string;
          targetDate?: Date;
          errorCode?: string;
          errorDetail?: string;
          errorMessage?: string;
        } = {
          status: update.status,
          updatedAt: new Date(),
        };

        if (update.status === GmoProcessingStatus.COMPLETED || update.status === GmoProcessingStatus.FAILED) {
          updateData.processedAt = new Date();
        }

        if (update.gmoData) {
          const gmoData = update.gmoData;
          if (gmoData.gmoOrderId !== undefined) updateData.gmoOrderId = gmoData.gmoOrderId;
          if (gmoData.gmoAccessId !== undefined) updateData.gmoAccessId = gmoData.gmoAccessId;
          if (gmoData.gmoAccessPass !== undefined) updateData.gmoAccessPass = gmoData.gmoAccessPass;
          if (gmoData.gmoBankId !== undefined) updateData.gmoBankId = gmoData.gmoBankId;
          if (gmoData.gmoDepositId !== undefined) updateData.gmoDepositId = gmoData.gmoDepositId;
          if (gmoData.gmoTransactionId !== undefined) updateData.gmoTransactionId = gmoData.gmoTransactionId;
          if (gmoData.targetDate !== undefined) updateData.targetDate = gmoData.targetDate;
          if (gmoData.errorCode !== undefined) updateData.errorCode = gmoData.errorCode;
          if (gmoData.errorDetail !== undefined) updateData.errorDetail = gmoData.errorDetail;
          if (gmoData.errorMessage !== undefined) updateData.errorMessage = gmoData.errorMessage;
        }

        const updatedRecord = await tx.monthlyGmoProcessingRecord.update({
          where: { monthlyGmoProcessingRecordId: update.recordId },
          data: updateData,
        });

        updatedRecords.push(updatedRecord);
      }

      return updatedRecords;
    }),
    (error) => new Error(`Failed to bulk update monthly GMO processing records: ${error}`)
  );
}
