import { HorseFactory, MemberFactory, InvestmentApplicationFactory, InvestmentContractFactory } from '@core-test/index';
import { InvestmentContractStatus } from '@hami/prisma';
import {
  listInvestmentApplicationHorses,
  getInvestmentApplicationHorseDetail,
  listInvestmentApplicationsByHorse,
  acceptInvestmentApplications,
  listContractTargetApplications,
  getExistingContracts,
  getContractTargetApplications,
} from './investment_application_repository';

describe('InvestmentApplicationRepository', () => {
  describe('listInvestmentApplicationHorses', () => {
    it('出資申込馬一覧を正しく取得できる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentYear: 2024,
        recruitmentNo: 1,
        horseName: 'テスト馬',
        recruitmentName: 'テスト募集馬',
        sharesTotal: 40,
        amountTotal: 4000000,
      });

      const member = await MemberFactory.create();

      // 出資申込を作成（rejected: false, contracted: false）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        isWhole: false,
      });

      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 3,
        rejected: false,
        contracted: false,
        isWhole: false,
      });

      // 契約を作成
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        sharesNumber: 2,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      // ===== Act =====
      const result = await listInvestmentApplicationHorses();

      // ===== Assert =====
      expect(result.length).toBeGreaterThanOrEqual(1);
      const horseItem = result.find((h) => h.horseId === horse.horseId);
      expect(horseItem).toBeDefined();
      if (horseItem) {
        expect(horseItem.horseName).toBe('テスト馬');
        expect(horseItem.recruitmentName).toBe('テスト募集馬');
        expect(horseItem.totalApplicationShares).toBe(8); // 5 + 3
        expect(horseItem.recruitmentShares).toBe(40);
        expect(horseItem.contractedShares).toBe(2);
      }
    });

    it('rejectedまたはcontractedがtrueの申込は集計に含まれない', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentYear: 2024,
        recruitmentNo: 2,
        sharesTotal: 40,
        amountTotal: 4000000,
      });

      const member = await MemberFactory.create();

      // rejected: trueの申込
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 10,
        rejected: true,
        contracted: false,
        isWhole: false,
      });

      // contracted: trueの申込
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 7,
        rejected: false,
        contracted: true,
        isWhole: false,
      });

      // rejected: false, contracted: falseの申込
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        isWhole: false,
      });

      // ===== Act =====
      const result = await listInvestmentApplicationHorses();

      // ===== Assert =====
      const horseItem = result.find((h) => h.horseId === horse.horseId);
      expect(horseItem).toBeDefined();
      if (horseItem) {
        expect(horseItem.totalApplicationShares).toBe(5); // rejected: false かつ contracted: false のみ
      }
    });

    it('rejected=falseかつcontracted=falseの申込がない馬は結果に含まれない', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentYear: 2024,
        recruitmentNo: 3,
        sharesTotal: 40,
        amountTotal: 4000000,
      });

      const member = await MemberFactory.create();

      // rejected: trueの申込のみ
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 10,
        rejected: true,
        contracted: false,
        isWhole: false,
      });

      // contracted: trueの申込のみ
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: true,
        isWhole: false,
      });

      // ===== Act =====
      const result = await listInvestmentApplicationHorses();

      // ===== Assert =====
      const horseItem = result.find((h) => h.horseId === horse.horseId);
      expect(horseItem).toBeUndefined(); // この馬は結果に含まれない
    });

    it('退会済み会員の契約は集計されない', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentYear: 2024,
        recruitmentNo: 4,
        horseName: 'テスト馬4',
        recruitmentName: 'テスト募集馬4',
        sharesTotal: 40,
        amountTotal: 4000000,
      });

      const activeMember = await MemberFactory.create({
        retirementDate: null, // 現役会員
      });

      const retiredMember = await MemberFactory.create({
        retirementDate: new Date('2024-01-01'), // 退会済み会員
      });

      // 出資申込を作成（rejected: false, contracted: false）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: activeMember.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        isWhole: false,
      });

      // 現役会員の契約を作成
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: activeMember.memberId } },
        sharesNumber: 3,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      // 退会済み会員の契約を作成
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: retiredMember.memberId } },
        sharesNumber: 7,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      // ===== Act =====
      const result = await listInvestmentApplicationHorses();

      // ===== Assert =====
      expect(result.length).toBeGreaterThanOrEqual(1);
      const horseItem = result.find((h) => h.horseId === horse.horseId);
      expect(horseItem).toBeDefined();
      if (horseItem) {
        expect(horseItem.horseName).toBe('テスト馬4');
        expect(horseItem.recruitmentName).toBe('テスト募集馬4');
        expect(horseItem.totalApplicationShares).toBe(5); // 申込口数
        expect(horseItem.recruitmentShares).toBe(40);
        expect(horseItem.contractedShares).toBe(3); // 現役会員の契約のみ集計される（退会済み会員の7口は除外）
      }
    });
  });

  describe('getInvestmentApplicationHorseDetail', () => {
    it('馬詳細情報を正しく取得できる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        birthYear: 2020,
        birthMonth: 4,
        birthDay: 15,
        recruitmentYear: 2024,
        recruitmentNo: 1,
        horseName: 'テスト馬',
        recruitmentName: 'テスト募集馬',
        sharesTotal: 40,
        amountTotal: 4000000,
      });

      const member = await MemberFactory.create();

      // rejected=false, contracted=falseの申込
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 15,
        allocatedNumber: 10,
        rejected: false,
        contracted: false,
        isWhole: false,
      });

      // rejected=trueの申込（集計に含まれない）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 5,
        allocatedNumber: 7,
        rejected: true,
        contracted: false,
        isWhole: false,
      });

      // contracted=trueの申込（集計に含まれない）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 3,
        allocatedNumber: 2,
        rejected: false,
        contracted: true,
        isWhole: false,
      });

      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        sharesNumber: 8,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      // ===== Act =====
      const result = await getInvestmentApplicationHorseDetail(horse.horseId);

      // ===== Assert =====
      expect(result.horseId).toBe(horse.horseId);
      expect(result.birthYear).toBe(2020);
      expect(result.birthMonth).toBe(4);
      expect(result.birthDay).toBe(15);
      expect(result.recruitmentYear).toBe(2024);
      expect(result.recruitmentNo).toBe(1);
      expect(result.horseName).toBe('テスト馬');
      expect(result.recruitmentName).toBe('テスト募集馬');
      expect(result.totalApplicationShares).toBe(10); // rejected=false かつ contracted=false のみ
      expect(result.recruitmentShares).toBe(40);
      expect(result.contractedShares).toBe(8);
      expect(result.recruitmentTotalAmount).toBe(4000000);
    });

    it('馬が見つからない場合はエラーを投げる', async () => {
      // ===== Act & Assert =====
      await expect(getInvestmentApplicationHorseDetail(999999)).rejects.toThrow('Horse not found');
    });

    it('退会済み会員の契約は集計されない', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        birthYear: 2020,
        birthMonth: 4,
        birthDay: 15,
        recruitmentYear: 2024,
        recruitmentNo: 2,
        horseName: 'テスト馬詳細',
        recruitmentName: 'テスト募集馬詳細',
        sharesTotal: 40,
        amountTotal: 4000000,
      });

      const activeMember = await MemberFactory.create({
        retirementDate: null, // 現役会員
      });

      const retiredMember = await MemberFactory.create({
        retirementDate: new Date('2024-01-01'), // 退会済み会員
      });

      // rejected=false, contracted=falseの申込（現役会員）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: activeMember.memberId } },
        requestedNumber: 15,
        allocatedNumber: 10,
        rejected: false,
        contracted: false,
        isWhole: false,
      });

      // 現役会員の契約を作成
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: activeMember.memberId } },
        sharesNumber: 8,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      // 退会済み会員の契約を作成
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: retiredMember.memberId } },
        sharesNumber: 12,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      // ===== Act =====
      const result = await getInvestmentApplicationHorseDetail(horse.horseId);

      // ===== Assert =====
      expect(result.horseId).toBe(horse.horseId);
      expect(result.birthYear).toBe(2020);
      expect(result.birthMonth).toBe(4);
      expect(result.birthDay).toBe(15);
      expect(result.recruitmentYear).toBe(2024);
      expect(result.recruitmentNo).toBe(2);
      expect(result.horseName).toBe('テスト馬詳細');
      expect(result.recruitmentName).toBe('テスト募集馬詳細');
      expect(result.totalApplicationShares).toBe(10); // rejected=false かつ contracted=false のみ
      expect(result.recruitmentShares).toBe(40);
      expect(result.contractedShares).toBe(8); // 現役会員の契約のみ集計される（退会済み会員の12口は除外）
      expect(result.recruitmentTotalAmount).toBe(4000000);
    });
  });

  describe('listInvestmentApplicationsByHorse', () => {
    it('馬ごとの出資申込一覧を正しく取得できる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const member1 = await MemberFactory.create({
        lastName: '田中',
        firstName: '太郎',
      });
      const member2 = await MemberFactory.create({
        lastName: '佐藤',
        firstName: '花子',
      });

      const app1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 5,
        rejectPartialAllocation: false,
        allocatedNumber: 3,
        rejected: false,
        contracted: false,
        isWhole: false,
        installmentPayment: false,
      });

      const app2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 10,
        rejectPartialAllocation: true,
        allocatedNumber: null,
        rejected: false,
        contracted: false,
        isWhole: false,
        installmentPayment: true,
      });

      // ===== Act =====
      const result = await listInvestmentApplicationsByHorse({ horseId: horse.horseId, sortBy: 'applied_at', sortOrder: 'asc' });

      // ===== Assert =====
      expect(result.length).toBe(2);

      const app1Result = result.find((r) => r.investmentApplicationId === app1.investmentApplicationId);
      expect(app1Result).toBeDefined();
      if (app1Result) {
        expect(app1Result.memberName).toBe('田中 太郎');
        expect(app1Result.memberId).toBe(member1.memberId);
        expect(app1Result.memberNumber).toBe(member1.memberNumber);
        expect(app1Result.requestedShares).toBe(5);
        expect(app1Result.rejectPartialAllocation).toBe(false);
        expect(app1Result.allocatedShares).toBe(3);
        expect(app1Result.installmentPayment).toBe(false);
      }

      const app2Result = result.find((r) => r.investmentApplicationId === app2.investmentApplicationId);
      expect(app2Result).toBeDefined();
      if (app2Result) {
        expect(app2Result.memberName).toBe('佐藤 花子');
        expect(app2Result.memberId).toBe(member2.memberId);
        expect(app2Result.memberNumber).toBe(member2.memberNumber);
        expect(app2Result.requestedShares).toBe(10);
        expect(app2Result.rejectPartialAllocation).toBe(true);
        expect(app2Result.allocatedShares).toBeNull();
        expect(app2Result.installmentPayment).toBe(true);
      }
    });

    it('rejectedまたはcontractedがtrueの申込は含まれない', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const member = await MemberFactory.create();

      // rejected: true
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: true,
        contracted: false,
        isWhole: false,
      });

      // contracted: true
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: true,
      });

      // 対象となる申込
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: false,
      });

      // ===== Act =====
      const result = await listInvestmentApplicationsByHorse({ horseId: horse.horseId, sortBy: 'applied_at', sortOrder: 'asc' });

      // ===== Assert =====
      expect(result.length).toBe(1);
    });

    it('退会済み会員の申込は含まれない', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const activeMember = await MemberFactory.create({
        lastName: '現役',
        firstName: '太郎',
        retirementDate: null, // 現役会員
      });
      const retiredMember = await MemberFactory.create({
        lastName: '退会',
        firstName: '花子',
        retirementDate: new Date('2024-01-01'), // 退会済み会員
      });

      // 現役会員の申込
      const activeApp = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: activeMember.memberId } },
        requestedNumber: 5,
        rejectPartialAllocation: false,
        allocatedNumber: 3,
        rejected: false,
        contracted: false,
        isWhole: false,
      });

      // 退会済み会員の申込
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: retiredMember.memberId } },
        requestedNumber: 10,
        rejectPartialAllocation: true,
        allocatedNumber: null,
        rejected: false,
        contracted: false,
      });

      // ===== Act =====
      const result = await listInvestmentApplicationsByHorse({ horseId: horse.horseId, sortBy: 'applied_at', sortOrder: 'asc' });

      // ===== Assert =====
      expect(result.length).toBe(1);

      // 現役会員の申込のみが含まれることを確認
      const activeAppResult = result.find((r) => r.investmentApplicationId === activeApp.investmentApplicationId);
      expect(activeAppResult).toBeDefined();
      if (activeAppResult) {
        expect(activeAppResult.memberName).toBe('現役 太郎');
        expect(activeAppResult.memberId).toBe(activeMember.memberId);
        expect(activeAppResult.memberNumber).toBe(activeMember.memberNumber);
        expect(activeAppResult.requestedShares).toBe(5);
        expect(activeAppResult.rejectPartialAllocation).toBe(false);
        expect(activeAppResult.allocatedShares).toBe(3);
      }

      // 退会済み会員の申込が含まれないことを確認
      const retiredAppResults = result.filter((r) => r.memberName === '退会 花子');
      expect(retiredAppResults).toHaveLength(0);
    });

    it('カナ昇順で並び替えできる（lastNameKana -> firstNameKana）', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const memberA = await MemberFactory.create({ lastName: '仮名', firstName: 'ア', lastNameKana: 'ア', firstNameKana: 'ア' });
      await new Promise((r) => setTimeout(r, 5));
      const memberB = await MemberFactory.create({ lastName: '仮名', firstName: 'カ', lastNameKana: 'カ', firstNameKana: 'カ' });

      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: memberB.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        isWhole: false,
      });
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: memberA.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        isWhole: false,
      });

      // ===== Act =====
      const result = await listInvestmentApplicationsByHorse({ horseId: horse.horseId, sortBy: 'member_name_kana', sortOrder: 'asc' });

      // ===== Assert =====
      expect(result.length).toBe(2);
      const names = result.map(r => r.memberName);
      expect(names[0]).toContain('ア');
      expect(names[1]).toContain('カ');
    });
  });

  describe('acceptInvestmentApplications', () => {
    it('出資受入を正しく実行できる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const member = await MemberFactory.create();

      const app1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: null,
        isWhole: false,
      });

      const app2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: null,
        isWhole: false,
      });

      const acceptances = [
        {
          horseId: horse.horseId,
          investmentApplicationId: app1.investmentApplicationId,
          allocatedShares: 3,
        },
        {
          horseId: horse.horseId,
          investmentApplicationId: app2.investmentApplicationId,
          allocatedShares: 5,
        },
      ];

      // ===== Act =====
      await acceptInvestmentApplications(acceptances);

      // ===== Assert =====
      const updatedApp1 = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: app1.investmentApplicationId },
      });
      const updatedApp2 = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: app2.investmentApplicationId },
      });

      expect(updatedApp1?.allocatedNumber).toBe(3);
      expect(updatedApp2?.allocatedNumber).toBe(5);
    });
  });

  describe('listContractTargetApplications', () => {
    it('契約締結対象申込一覧を正しく取得できる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const member = await MemberFactory.create({
        lastName: '田中',
        firstName: '太郎',
        lastNameKana: 'タナカ',
        firstNameKana: 'タロウ',
      });

      const targetApp = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        allocatedNumber: 3,
        isWhole: false,
      });

      // 対象外（allocatedNumber: null）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: null,
        isWhole: false,
      });

      // ===== Act =====
      const result = await listContractTargetApplications(horse.horseId);

      // ===== Assert =====
      expect(result.length).toBe(1);
      expect(result[0].investmentApplicationId).toBe(targetApp.investmentApplicationId);
      expect(result[0].memberName).toBe('田中 太郎');
      expect(result[0].memberNameKana).toBe('タナカ タロウ');
      expect(result[0].requestedShares).toBe(5);
      expect(result[0].allocatedNumber).toBe(3);
    });
  });

  describe('getExistingContracts', () => {
    it('既存の契約を正しく取得できる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 契約済みの申込を作成
      const contractedApp1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: true,
        allocatedNumber: 3,
        isWhole: false,
      });

      const contractedApp2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 10,
        rejected: false,
        contracted: true,
        allocatedNumber: 7,
        isWhole: false,
      });

      // 契約対象外の申込を作成（contracted: false）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 8,
        rejected: false,
        contracted: false,
        allocatedNumber: 4,
        isWhole: false,
      });

      // ===== Act =====
      const result = await getExistingContracts(horse.horseId);

      // ===== Assert =====
      expect(result.length).toBe(2);
      
      const app1Result = result.find(r => r.investmentApplicationId === contractedApp1.investmentApplicationId);
      expect(app1Result).toBeDefined();
      expect(app1Result?.allocatedNumber).toBe(3);

      const app2Result = result.find(r => r.investmentApplicationId === contractedApp2.investmentApplicationId);
      expect(app2Result).toBeDefined();
      expect(app2Result?.allocatedNumber).toBe(7);
    });

    it('契約済みの申込がない場合は空配列を返す', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const member = await MemberFactory.create();

      // 契約対象外の申込のみ作成
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        allocatedNumber: 3,
        isWhole: false,
      });

      // ===== Act =====
      const result = await getExistingContracts(horse.horseId);

      // ===== Assert =====
      expect(result.length).toBe(0);
    });

    it('異なる馬の契約は含まれない', async () => {
      // ===== Arrange =====
      const horse1 = await HorseFactory.create();
      const horse2 = await HorseFactory.create();
      const member = await MemberFactory.create();

      // horse1の契約済み申込
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse1.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: true,
        allocatedNumber: 3,
        isWhole: false,
      });

      // horse2の契約済み申込
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse2.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 10,
        rejected: false,
        contracted: true,
        allocatedNumber: 7,
        isWhole: false,
      });

      // ===== Act =====
      const result = await getExistingContracts(horse1.horseId);

      // ===== Assert =====
      expect(result.length).toBe(1);
      expect(result[0].allocatedNumber).toBe(3);
    });
  });

  describe('getContractTargetApplications', () => {
    it('契約対象の申込を正しく取得できる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const member1 = await MemberFactory.create({
        lastName: '田中',
        firstName: '太郎',
      });
      const member2 = await MemberFactory.create({
        lastName: '佐藤',
        firstName: '花子',
      });

      // 契約対象の申込
      const targetApp1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        allocatedNumber: 3,
        isWhole: false,
        installmentPayment: false,
      });

      const targetApp2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 10,
        rejected: false,
        contracted: false,
        allocatedNumber: 7,
        isWhole: false,
        installmentPayment: true,
      });

      // 対象外の申込（allocatedNumber: null）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 8,
        rejected: false,
        contracted: false,
        allocatedNumber: null,
        isWhole: false,
      });

      // 対象外の申込（rejected: true）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 6,
        rejected: true,
        contracted: false,
        allocatedNumber: 4,
        isWhole: false,
      });

      // 対象外の申込（contracted: true）
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 9,
        rejected: false,
        contracted: true,
        allocatedNumber: 5,
        isWhole: false,
      });

      const applicationIds = [targetApp1.investmentApplicationId, targetApp2.investmentApplicationId];

      // ===== Act =====
      const result = await getContractTargetApplications(horse.horseId, applicationIds);

      // ===== Assert =====
      expect(result.length).toBe(2);

      const app1Result = result.find(r => r.investmentApplicationId === targetApp1.investmentApplicationId);
      expect(app1Result).toBeDefined();
      expect(app1Result?.memberName).toBe('田中 太郎');
      expect(app1Result?.requestedShares).toBe(5);
      expect(app1Result?.allocatedNumber).toBe(3);
      expect(app1Result?.installmentPayment).toBe(false);

      const app2Result = result.find(r => r.investmentApplicationId === targetApp2.investmentApplicationId);
      expect(app2Result).toBeDefined();
      expect(app2Result?.memberName).toBe('佐藤 花子');
      expect(app2Result?.requestedShares).toBe(10);
      expect(app2Result?.allocatedNumber).toBe(7);
      expect(app2Result?.installmentPayment).toBe(true);
    });

    it('指定された申込IDが存在しない場合は空配列を返す', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const nonExistentIds = [999999, 999998];

      // ===== Act =====
      const result = await getContractTargetApplications(horse.horseId, nonExistentIds);

      // ===== Assert =====
      expect(result.length).toBe(0);
    });

    it('異なる馬の申込IDは含まれない', async () => {
      // ===== Arrange =====
      const horse1 = await HorseFactory.create();
      const horse2 = await HorseFactory.create();
      const member = await MemberFactory.create({
        lastName: '田中',
        firstName: '太郎',
      });

      // horse1の契約対象申込
      const horse1App = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse1.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        allocatedNumber: 3,
        isWhole: false,
      });

      // horse2の契約対象申込
      const horse2App = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse2.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 10,
        rejected: false,
        contracted: false,
        allocatedNumber: 7,
        isWhole: false,
      });

      // horse1のIDでhorse2の申込を検索
      const applicationIds = [horse2App.investmentApplicationId];

      // ===== Act =====
      const result = await getContractTargetApplications(horse1.horseId, applicationIds);

      // ===== Assert =====
      expect(result.length).toBe(0);
    });

    it('条件に合わない申込は含まれない', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();
      const member = await MemberFactory.create({
        lastName: '田中',
        firstName: '太郎',
      });

      // 契約対象の申込
      const targetApp = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 5,
        rejected: false,
        contracted: false,
        allocatedNumber: 3,
        isWhole: false,
      });

      // 条件に合わない申込（allocatedNumber: 0）
      const invalidApp = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 8,
        rejected: false,
        contracted: false,
        allocatedNumber: 0,
        isWhole: false,
      });

      const applicationIds = [targetApp.investmentApplicationId, invalidApp.investmentApplicationId];

      // ===== Act =====
      const result = await getContractTargetApplications(horse.horseId, applicationIds);

      // ===== Assert =====
      expect(result.length).toBe(1);
      expect(result[0].investmentApplicationId).toBe(targetApp.investmentApplicationId);
      expect(result[0].allocatedNumber).toBe(3);
    });
  });

});
