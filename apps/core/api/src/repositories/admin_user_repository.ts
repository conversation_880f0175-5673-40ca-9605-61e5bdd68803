import bcrypt from 'bcrypt';
import { err, ok, ResultAsync } from 'neverthrow';
import { Prisma } from '@hami/prisma';
import { AdminUser } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class AdminUserNotFoundError extends Error {
  readonly name = 'AdminUserNotFoundError';
}

export class AdminUserAlreadyExistsError extends Error {
  readonly name = 'AdminUserAlreadyExistsError';
}

export const findAdminUserByEmail = ({ email }: { email: string }) => {
  return ResultAsync.fromPromise(
    client.adminUser.findUnique({ where: { email } }),
    () => new DatabaseError('Failed to query admin user')
  ).andThen((record) => {
    if (!record) {
      return err(new AdminUserNotFoundError());
    }
    return ok(record);
  });
};

export const findAdminUserBySessionToken = ({ sessionToken }: { sessionToken: string }) => {
  return ResultAsync.fromPromise(
    client.adminUserSession.findUnique({
      where: { sessionToken },
      include: { adminUser: true },
    }),
    () => new DatabaseError('Failed to query admin user session')
  ).andThen((session) => {
    if (!session) {
      return err(new AdminUserNotFoundError());
    }
    return ok({ user: session.adminUser, session });
  });
};

export const upsertAdminUserSession = ({ user }: { user: AdminUser }) => {
  return ResultAsync.fromPromise(
    client.adminUserSession.upsert({
      where: { adminUserId: user.adminUserId },
      update: {
        // Extend admin session to ~30 days
        expiresAt: new Date(Date.now() + 30 * 24 * 3600 * 1000),
      },
      create: {
        adminUserId: user.adminUserId,
        sessionToken: crypto.randomUUID(),
        // Extend admin session to ~30 days
        expiresAt: new Date(Date.now() + 30 * 24 * 3600 * 1000),
      },
    }),
    () => new DatabaseError('Failed to upsert admin user session')
  );
};

export const deleteAdminUserSession = ({ sessionToken }: { sessionToken: string }) => {
  return ResultAsync.fromPromise(
    client.adminUserSession.deleteMany({
      where: { sessionToken },
    }),
    () => new DatabaseError('Failed to delete admin user session')
  );
};

/**
 * 運営ユーザー一覧を取得（少人数想定のため全件返却）
 * パスワードハッシュやソルトなどの機微情報はレスポンスで返さない（リポジトリでは取得可）
 */
export const listAdminUsers = () => {
  return ResultAsync.fromPromise(
    client.adminUser.findMany({
      include: { adminUserSession: true },
      orderBy: { createdAt: 'asc' },
    }),
    () => new DatabaseError('Failed to list admin users')
  );
};

export const createAdminUser = ({ name, email, password }: { name: string; email: string; password: string }) => {
  return ResultAsync.fromPromise(
    (async () => {
      const salt = await bcrypt.genSalt(10);
      const passwordHash = await bcrypt.hash(password, salt);
      return { salt, passwordHash };
    })(),
    () => new DatabaseError('Failed to hash password')
  ).andThen(({ salt, passwordHash }) =>
    ResultAsync.fromPromise(
      client.adminUser.create({
        data: { name, email, password_hash: passwordHash, salt },
        include: { adminUserSession: true },
      }),
      (error) => {
        if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
          return new AdminUserAlreadyExistsError('Admin user with this email already exists');
        }
        return new DatabaseError('Failed to create admin user');
      }
    )
  );
};

export const updateAdminUser = ({
  adminUserId,
  data,
}: {
  adminUserId: string;
  data: { name?: string; email?: string; password?: string };
}) => {
  return ResultAsync.fromPromise(
    (async () => {
      const updateData: Record<string, unknown> = {};
      if (data.name !== undefined) updateData.name = data.name;
      if (data.email !== undefined) updateData.email = data.email;
      if (data.password !== undefined) {
        const salt = await bcrypt.genSalt(10);
        const passwordHash = await bcrypt.hash(data.password, salt);
        updateData.password_hash = passwordHash;
        updateData.salt = salt;
      }
      return updateData;
    })(),
    () => new DatabaseError('Failed to prepare update data')
  ).andThen((updateData) =>
    ResultAsync.fromPromise(
      client.adminUser.update({
        where: { adminUserId },
        data: updateData,
        include: { adminUserSession: true },
      }),
      (error) => {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === 'P2002') return new AdminUserAlreadyExistsError('Admin user with this email already exists');
          if (error.code === 'P2025') return new AdminUserNotFoundError();
        }
        return new DatabaseError('Failed to update admin user');
      }
    )
  );
};

export const updateAdminUserPassword = ({ adminUserId, newPassword }: { adminUserId: string; newPassword: string }) => {
  const salt = bcrypt.genSaltSync(10);
  const password_hash = bcrypt.hashSync(newPassword, salt);
  return ResultAsync.fromPromise(
    client.adminUser.update({ where: { adminUserId }, data: { salt, password_hash } }),
    () => new DatabaseError('Failed to update admin user password')
  );
};
