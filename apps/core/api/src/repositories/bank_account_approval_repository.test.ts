import { AdminUserFactory, MemberFactory, UserFactory, MailVerificationFactory, MembershipApplicationFactory } from '@core-test/index';
import { BankAccountRegistrationStatus, ApprovalType } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import {
  listPendingBankAccountRegistrations,
  getBankAccountRegistrationDetail,
  approveBankAccountRegistration,
  rejectBankAccountRegistration,
  BankAccountRegistrationNotFoundError,
  BankAccountRegistrationAlreadyProcessedError,
  AdminUserNotFoundError,
} from './bank_account_approval_repository';

// vPrisma.clientを使用（setup.tsでモックされているため）
const prisma = vPrisma.client;

// テストデータ作成ヘルパー
const createTestMember = async () => {
  const mailVerification = await MailVerificationFactory.create();
  const application = await MembershipApplicationFactory.create({
    mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
  });
  const user = await UserFactory.create();
  const member = await MemberFactory.create({
    user: { connect: { userId: user.userId } },
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
  });
  return { user, member, application, mailVerification };
};

describe('bank_account_approval_repository', () => {
  describe('listPendingBankAccountRegistrations', () => {
    it('承認待ちの口座登録一覧を取得できること', async () => {
      // テストデータ作成
      const { member } = await createTestMember();
      
      // 承認待ち（SUCCESS状態で承認レコードなし）
      const pendingRegistration = await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: '0001',
          registrationStatus: BankAccountRegistrationStatus.SUCCESS,
          isActive: true,
          completedAt: new Date(),
        },
      });

      // テスト実行
      const result = await listPendingBankAccountRegistrations({ page: 1, pageSize: 10 });

      // 検証
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { registrations, totalCount } = result.value;
        expect(totalCount).toBe(1);
        expect(registrations).toHaveLength(1);
        expect(registrations[0].bankAccountRegistrationId).toBe(pendingRegistration.bankAccountRegistrationId);
        expect(registrations[0].registrationStatus).toBe(BankAccountRegistrationStatus.SUCCESS);
        expect(registrations[0].member).toBeDefined();
        expect(registrations[0].member.user).toBeDefined();
      }
    });
  });

  describe('getBankAccountRegistrationDetail', () => {
    it('口座登録詳細を取得できること', async () => {
      // テストデータ作成
      const { member } = await createTestMember();
      const registration = await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: '0001',
          registrationStatus: BankAccountRegistrationStatus.SUCCESS,
          isActive: true,
          completedAt: new Date(),
        },
      });

      // テスト実行
      const result = await getBankAccountRegistrationDetail({
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
      });

      // 検証
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const detail = result.value;
        expect(detail.bankAccountRegistrationId).toBe(registration.bankAccountRegistrationId);
        expect(detail.member).toBeDefined();
        expect(detail.member.user).toBeDefined();
      }
    });

    it('存在しない口座登録IDの場合エラーを返すこと', async () => {
      const result = await getBankAccountRegistrationDetail({
        bankAccountRegistrationId: 99999,
      });

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(BankAccountRegistrationNotFoundError);
      }
    });
  });

  describe('approveBankAccountRegistration', () => {
    it('口座登録を承認できること', async () => {
      // テストデータ作成
      const { member } = await createTestMember();
      const registration = await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: '0001',
          registrationStatus: BankAccountRegistrationStatus.SUCCESS,
          isActive: true,
          completedAt: new Date(),
        },
      });
      const adminUser = await AdminUserFactory.create();

      // テスト実行
      const result = await approveBankAccountRegistration({
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        adminUserId: adminUser.adminUserId,
        comment: 'テスト承認コメント',
      });

      // 検証
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const approval = result.value;
        expect(approval.approvalType).toBe(ApprovalType.APPROVE);
        expect(approval.comment).toBe('テスト承認コメント');
        expect(approval.adminUserId).toBe(adminUser.adminUserId);
        expect(approval.adminUser).toBeDefined();
      }
    });

    it('存在しない口座登録IDの場合エラーを返すこと', async () => {
      const adminUser = await AdminUserFactory.create();

      const result = await approveBankAccountRegistration({
        bankAccountRegistrationId: 99999,
        adminUserId: adminUser.adminUserId,
        comment: 'テスト',
      });

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(BankAccountRegistrationNotFoundError);
      }
    });
  });

  describe('rejectBankAccountRegistration', () => {
    it('口座登録を却下できること', async () => {
      // テストデータ作成
      const { member } = await createTestMember();
      const registration = await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: '0001',
          registrationStatus: BankAccountRegistrationStatus.SUCCESS,
          isActive: true,
          completedAt: new Date(),
        },
      });
      const adminUser = await AdminUserFactory.create();

      // テスト実行
      const result = await rejectBankAccountRegistration({
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        adminUserId: adminUser.adminUserId,
        rejectionReason: '口座名義が一致しません',
      });

      // 検証
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const approval = result.value;
        expect(approval.approvalType).toBe(ApprovalType.REJECT);
        expect(approval.rejectionReason).toBe('口座名義が一致しません');
        expect(approval.adminUserId).toBe(adminUser.adminUserId);
        expect(approval.adminUser).toBeDefined();
      }
    });
  });
});
