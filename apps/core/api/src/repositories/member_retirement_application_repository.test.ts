import { client } from '@core-api/utils/prisma';
import {
  listMemberRetirementApplications,
  getMemberRetirementApplication,
  approveMemberRetirementApplication,
  rejectMemberRetirementApplication,
} from './member_retirement_application_repository';

describe('member_retirement_application_repository', () => {
  describe('listMemberRetirementApplications', () => {
    it('デフォルトでは未処理の退会申請のみを取得できること', async () => {
      // テスト用のユーザーとメンバーを作成
      const user = await client.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'test-hash',
          salt: 'test-salt',
        },
      });

      const mailVerification = await client.mailVerification.create({
        data: {
          email: '<EMAIL>',
          token: 'test-token',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      const membershipApplication = await client.membershipApplication.create({
        data: {
          mailVerificationId: mailVerification.mailVerificationId,
          firstName: 'Test',
          lastName: 'User',
          firstNameKana: 'テスト',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'test-upload-token',
        },
      });

      const member = await client.member.create({
        data: {
          userId: user.userId,
          membershipApplicationId: membershipApplication.membershipApplicationId,
          firstName: 'Test',
          lastName: 'User',
          firstNameKana: 'テスト',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
        },
      });

      const applicationDate = new Date('2024-01-15T10:00:00Z');
      await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate,
        },
      });

      const result = await listMemberRetirementApplications();

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(1);
        expect(result.value[0].memberId).toBe(member.memberId);
        expect(result.value[0].applicationDate).toEqual(applicationDate);
        expect(result.value[0].member).toBeDefined();
        expect(result.value[0].member.firstName).toBe('Test');
        expect(result.value[0].member.lastName).toBe('User');
      }
    });

    it('承認済みを含めるフラグがtrueの場合、未処理と承認済みを取得できること', async () => {
      // テスト用のユーザーとメンバーを作成
      const user = await client.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'test-hash',
          salt: 'test-salt',
        },
      });

      const mailVerification = await client.mailVerification.create({
        data: {
          email: '<EMAIL>',
          token: 'test-token-2',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      const membershipApplication = await client.membershipApplication.create({
        data: {
          mailVerificationId: mailVerification.mailVerificationId,
          firstName: 'Test2',
          lastName: 'User',
          firstNameKana: 'テスト2',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'test-upload-token-2',
        },
      });

      const member = await client.member.create({
        data: {
          userId: user.userId,
          membershipApplicationId: membershipApplication.membershipApplicationId,
          firstName: 'Test2',
          lastName: 'User',
          firstNameKana: 'テスト2',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
        },
      });

      // 未処理の申請
      const pendingApplication = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date('2024-01-15T10:00:00Z'),
        },
      });

      // 承認済みの申請
      const approvedApplication = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date('2024-01-16T10:00:00Z'),
        },
      });

      // 承認済みに更新
      await client.memberRetirementApplication.update({
        where: { memberRetirementApplicationId: approvedApplication.memberRetirementApplicationId },
        data: { approvedAt: new Date() },
      });

      // 拒否済みの申請
      const rejectedApplication = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date('2024-01-17T10:00:00Z'),
        },
      });

      // 拒否済みに更新
      await client.memberRetirementApplication.update({
        where: { memberRetirementApplicationId: rejectedApplication.memberRetirementApplicationId },
        data: { rejectedAt: new Date() },
      });

      const result = await listMemberRetirementApplications(true, false);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(2);
        // 申請日降順でソートされていることを確認
        expect(result.value[0].memberRetirementApplicationId).toBe(approvedApplication.memberRetirementApplicationId);
        expect(result.value[1].memberRetirementApplicationId).toBe(pendingApplication.memberRetirementApplicationId);
        // 拒否済みは含まれていないことを確認
        expect(
          result.value.find((app) => app.memberRetirementApplicationId === rejectedApplication.memberRetirementApplicationId)
        ).toBeUndefined();
      }
    });

    it('拒否済みを含めるフラグがtrueの場合、未処理と拒否済みを取得できること', async () => {
      // テスト用のユーザーとメンバーを作成
      const user = await client.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'test-hash',
          salt: 'test-salt',
        },
      });

      const mailVerification = await client.mailVerification.create({
        data: {
          email: '<EMAIL>',
          token: 'test-token-3',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      const membershipApplication = await client.membershipApplication.create({
        data: {
          mailVerificationId: mailVerification.mailVerificationId,
          firstName: 'Test3',
          lastName: 'User',
          firstNameKana: 'テスト3',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'test-upload-token-3',
        },
      });

      const member = await client.member.create({
        data: {
          userId: user.userId,
          membershipApplicationId: membershipApplication.membershipApplicationId,
          firstName: 'Test3',
          lastName: 'User',
          firstNameKana: 'テスト3',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
        },
      });

      // 未処理の申請
      const pendingApplication = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date('2024-01-15T10:00:00Z'),
        },
      });

      // 承認済みの申請
      const approvedApplication = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date('2024-01-16T10:00:00Z'),
        },
      });

      // 承認済みに更新
      await client.memberRetirementApplication.update({
        where: { memberRetirementApplicationId: approvedApplication.memberRetirementApplicationId },
        data: { approvedAt: new Date() },
      });

      // 拒否済みの申請
      const rejectedApplication = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date('2024-01-17T10:00:00Z'),
        },
      });

      // 拒否済みに更新
      await client.memberRetirementApplication.update({
        where: { memberRetirementApplicationId: rejectedApplication.memberRetirementApplicationId },
        data: { rejectedAt: new Date() },
      });

      const result = await listMemberRetirementApplications(false, true);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(2);
        // 申請日降順でソートされていることを確認
        expect(result.value[0].memberRetirementApplicationId).toBe(rejectedApplication.memberRetirementApplicationId);
        expect(result.value[1].memberRetirementApplicationId).toBe(pendingApplication.memberRetirementApplicationId);
        // 承認済みは含まれていないことを確認
        expect(
          result.value.find((app) => app.memberRetirementApplicationId === approvedApplication.memberRetirementApplicationId)
        ).toBeUndefined();
      }
    });

    it('両方のフラグがtrueの場合、すべての申請を取得できること', async () => {
      // テスト用のユーザーとメンバーを作成
      const user = await client.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'test-hash',
          salt: 'test-salt',
        },
      });

      const mailVerification = await client.mailVerification.create({
        data: {
          email: '<EMAIL>',
          token: 'test-token-4',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      const membershipApplication = await client.membershipApplication.create({
        data: {
          mailVerificationId: mailVerification.mailVerificationId,
          firstName: 'Test4',
          lastName: 'User',
          firstNameKana: 'テスト4',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'test-upload-token-4',
        },
      });

      const member = await client.member.create({
        data: {
          userId: user.userId,
          membershipApplicationId: membershipApplication.membershipApplicationId,
          firstName: 'Test4',
          lastName: 'User',
          firstNameKana: 'テスト4',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
        },
      });

      // 未処理の申請
      const pendingApplication = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date('2024-01-15T10:00:00Z'),
        },
      });

      // 承認済みの申請
      const approvedApplication = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date('2024-01-16T10:00:00Z'),
        },
      });

      // 承認済みに更新
      await client.memberRetirementApplication.update({
        where: { memberRetirementApplicationId: approvedApplication.memberRetirementApplicationId },
        data: { approvedAt: new Date() },
      });

      // 拒否済みの申請
      const rejectedApplication = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date('2024-01-17T10:00:00Z'),
        },
      });

      // 拒否済みに更新
      await client.memberRetirementApplication.update({
        where: { memberRetirementApplicationId: rejectedApplication.memberRetirementApplicationId },
        data: { rejectedAt: new Date() },
      });

      const result = await listMemberRetirementApplications(true, true);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(3);
        // 申請日降順でソートされていることを確認
        expect(result.value[0].memberRetirementApplicationId).toBe(rejectedApplication.memberRetirementApplicationId);
        expect(result.value[1].memberRetirementApplicationId).toBe(approvedApplication.memberRetirementApplicationId);
        expect(result.value[2].memberRetirementApplicationId).toBe(pendingApplication.memberRetirementApplicationId);
      }
    });

    it('退会申請が存在しない場合は空配列を返すこと', async () => {
      const result = await listMemberRetirementApplications();

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(0);
      }
    });
  });

  describe('getMemberRetirementApplication', () => {
    it('指定したIDの退会申請を取得できること', async () => {
      // テスト用のユーザーとメンバーを作成
      const user = await client.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'test-hash',
          salt: 'test-salt',
        },
      });

      const mailVerification = await client.mailVerification.create({
        data: {
          email: '<EMAIL>',
          token: 'test-token',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      const membershipApplication = await client.membershipApplication.create({
        data: {
          mailVerificationId: mailVerification.mailVerificationId,
          firstName: 'Test',
          lastName: 'User',
          firstNameKana: 'テスト',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'test-upload-token',
        },
      });

      const member = await client.member.create({
        data: {
          userId: user.userId,
          membershipApplicationId: membershipApplication.membershipApplicationId,
          firstName: 'Test',
          lastName: 'User',
          firstNameKana: 'テスト',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
        },
      });

      const applicationDate = new Date('2024-01-15T10:00:00Z');
      const application = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate,
        },
      });

      const result = await getMemberRetirementApplication(application.memberRetirementApplicationId);

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value?.memberId).toBe(member.memberId);
        expect(result.value?.applicationDate).toEqual(applicationDate);
        expect(result.value?.member).toBeDefined();
        expect(result.value?.member.firstName).toBe('Test');
        expect(result.value?.member.lastName).toBe('User');
      }
    });

    it('存在しないIDの場合はNOT_FOUNDエラーを返すこと', async () => {
      const result = await getMemberRetirementApplication(99999);

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.type).toBe('NOT_FOUND');
      }
    });
  });

  describe('approveMemberRetirementApplication', () => {
    it('退会申請を承認できること', async () => {
      // テスト用のユーザーとメンバーを作成
      const user = await client.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'test-hash',
          salt: 'test-salt',
        },
      });

      const mailVerification = await client.mailVerification.create({
        data: {
          email: '<EMAIL>',
          token: 'test-token',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      const membershipApplication = await client.membershipApplication.create({
        data: {
          mailVerificationId: mailVerification.mailVerificationId,
          firstName: 'Test',
          lastName: 'User',
          firstNameKana: 'テスト',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'test-upload-token',
        },
      });

      const member = await client.member.create({
        data: {
          userId: user.userId,
          membershipApplicationId: membershipApplication.membershipApplicationId,
          firstName: 'Test',
          lastName: 'User',
          firstNameKana: 'テスト',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
        },
      });

      const applicationDate = new Date('2024-01-15T10:00:00Z');
      const application = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate,
        },
      });

      const result = await approveMemberRetirementApplication(application.memberRetirementApplicationId);

      expect(result.isOk()).toBe(true);

      // 退会申請が承認されていることを確認
      const approvedApplication = await client.memberRetirementApplication.findUnique({
        where: { memberRetirementApplicationId: application.memberRetirementApplicationId },
      });
      expect(approvedApplication?.approvedAt).toBeDefined();
      expect(approvedApplication?.rejectedAt).toBeNull();

      // メンバーの退会日が翌月の末に設定されていることを確認
      const updatedMember = await client.member.findUnique({
        where: { memberId: member.memberId },
      });
      // 2024年1月15日の翌月の末は2024年2月29日（うるう年）
      const expectedRetirementDate = new Date('2024-02-29');
      expect(updatedMember?.retirementDate?.toISOString().split('T')[0]).toEqual(expectedRetirementDate.toISOString().split('T')[0]);
    });

    it('存在しないIDの場合はNOT_FOUNDエラーを返すこと', async () => {
      const result = await approveMemberRetirementApplication(99999);

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.type).toBe('NOT_FOUND');
      }
    });

    it('既に処理済みの場合はALREADY_PROCESSEDエラーを返すこと', async () => {
      // テスト用のユーザーとメンバーを作成
      const user = await client.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'test-hash',
          salt: 'test-salt',
        },
      });

      const mailVerification = await client.mailVerification.create({
        data: {
          email: '<EMAIL>',
          token: 'test-token-2',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      const membershipApplication = await client.membershipApplication.create({
        data: {
          mailVerificationId: mailVerification.mailVerificationId,
          firstName: 'Test2',
          lastName: 'User',
          firstNameKana: 'テスト2',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'test-upload-token-2',
        },
      });

      const member = await client.member.create({
        data: {
          userId: user.userId,
          membershipApplicationId: membershipApplication.membershipApplicationId,
          firstName: 'Test2',
          lastName: 'User',
          firstNameKana: 'テスト2',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
        },
      });

      const application = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date(),
        },
      });

      // 一度承認
      await approveMemberRetirementApplication(application.memberRetirementApplicationId);

      // 再度承認を試行
      const result = await approveMemberRetirementApplication(application.memberRetirementApplicationId);

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.type).toBe('ALREADY_PROCESSED');
      }
    });
  });

  describe('rejectMemberRetirementApplication', () => {
    it('退会申請を却下できること', async () => {
      // テスト用のユーザーとメンバーを作成
      const user = await client.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'test-hash',
          salt: 'test-salt',
        },
      });

      const mailVerification = await client.mailVerification.create({
        data: {
          email: '<EMAIL>',
          token: 'test-token-3',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      const membershipApplication = await client.membershipApplication.create({
        data: {
          mailVerificationId: mailVerification.mailVerificationId,
          firstName: 'Test3',
          lastName: 'User',
          firstNameKana: 'テスト3',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'test-upload-token-3',
        },
      });

      const member = await client.member.create({
        data: {
          userId: user.userId,
          membershipApplicationId: membershipApplication.membershipApplicationId,
          firstName: 'Test3',
          lastName: 'User',
          firstNameKana: 'テスト3',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
        },
      });

      const application = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date(),
        },
      });

      const result = await rejectMemberRetirementApplication(application.memberRetirementApplicationId);

      expect(result.isOk()).toBe(true);

      // 退会申請が却下されていることを確認
      const rejectedApplication = await client.memberRetirementApplication.findUnique({
        where: { memberRetirementApplicationId: application.memberRetirementApplicationId },
      });
      expect(rejectedApplication?.rejectedAt).toBeDefined();
      expect(rejectedApplication?.approvedAt).toBeNull();

      // メンバーの退会日が設定されていないことを確認
      const updatedMember = await client.member.findUnique({
        where: { memberId: member.memberId },
      });
      expect(updatedMember?.retirementDate).toBeNull();
    });

    it('存在しないIDの場合はNOT_FOUNDエラーを返すこと', async () => {
      const result = await rejectMemberRetirementApplication(99999);

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.type).toBe('NOT_FOUND');
      }
    });

    it('既に処理済みの場合はALREADY_PROCESSEDエラーを返すこと', async () => {
      // テスト用のユーザーとメンバーを作成
      const user = await client.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'test-hash',
          salt: 'test-salt',
        },
      });

      const mailVerification = await client.mailVerification.create({
        data: {
          email: '<EMAIL>',
          token: 'test-token-4',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
      });

      const membershipApplication = await client.membershipApplication.create({
        data: {
          mailVerificationId: mailVerification.mailVerificationId,
          firstName: 'Test4',
          lastName: 'User',
          firstNameKana: 'テスト4',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'test-upload-token-4',
        },
      });

      const member = await client.member.create({
        data: {
          userId: user.userId,
          membershipApplicationId: membershipApplication.membershipApplicationId,
          firstName: 'Test4',
          lastName: 'User',
          firstNameKana: 'テスト4',
          lastNameKana: 'ユーザー',
          postalCode: '123-4567',
          prefecture: '東京都',
          address: 'テスト住所',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
        },
      });

      const application = await client.memberRetirementApplication.create({
        data: {
          memberId: member.memberId,
          applicationDate: new Date(),
        },
      });

      // 一度却下
      await rejectMemberRetirementApplication(application.memberRetirementApplicationId);

      // 再度却下を試行
      const result = await rejectMemberRetirementApplication(application.memberRetirementApplicationId);

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.type).toBe('ALREADY_PROCESSED');
      }
    });
  });
});
