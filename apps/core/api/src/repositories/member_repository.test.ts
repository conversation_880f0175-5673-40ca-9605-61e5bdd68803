import { MemberFactory, UserFactory, MembershipApplicationFactory, MailVerificationFactory } from '@core-test/index';
import { InvestmentContractStatus } from '@hami/prisma';
import { createHorse } from './horse_repository';
import {
  listMembers,
  findMemberById,
  createMember,
  updateMember,
  MemberNotFoundError,
  listMemberInvestmentHorses,
  cancelRetirement,
} from './member_repository';

describe('member_repository', () => {
  describe('createMember', () => {
    it('新しい会員を作成できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();

      const memberData = {
        userId: user.userId,
        membershipApplicationId: application.membershipApplicationId,
        firstName: 'テスト',
        lastName: '太郎',
        firstNameKana: 'テスト',
        lastNameKana: 'タロウ',
        postalCode: '123-4567',
        prefecture: '東京都',
        address: '渋谷区1-1-1',
        apartment: 'テストマンション101',
        phoneNumber: '090-1234-5678',
        birthYear: 1990,
        birthMonth: 1,
        birthDay: 1,
        approvedAt: new Date(),
        approvedBy: '<EMAIL>',
      };

      // ===== Act =====
      const result = await createMember(memberData);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const createdMember = result.value;
        expect(createdMember.firstName).toBe('テスト');
        expect(createdMember.lastName).toBe('太郎');
        expect(createdMember.user.userId).toBe(user.userId);
        expect(createdMember.membershipApplication.membershipApplicationId).toBe(application.membershipApplicationId);
        expect(createdMember.memberNumber).toBeGreaterThan(0);
      }
    });

    it('必須フィールドが不足している場合はエラーを返す', async () => {
      // ===== Arrange =====
      const invalidData = {
        userId: 999999, // 存在しないユーザーID
        membershipApplicationId: 999999, // 存在しない申し込みID
        firstName: 'テスト',
        lastName: '太郎',
        firstNameKana: 'テスト',
        lastNameKana: 'タロウ',
        postalCode: '123-4567',
        prefecture: '東京都',
        address: '渋谷区1-1-1',
        phoneNumber: '090-1234-5678',
        birthYear: 1990,
        birthMonth: 1,
        birthDay: 1,
        approvedAt: new Date(),
        approvedBy: '<EMAIL>',
      };

      // ===== Act =====
      const result = await createMember(invalidData);

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
    });
  });

  describe('listMembers', () => {
    it('会員一覧を取得できる', async () => {
      // ===== Arrange =====
      const mailVerification1 = await MailVerificationFactory.create();
      const application1 = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification1.mailVerificationId } },
      });
      const user1 = await UserFactory.create();
      await MemberFactory.create({
        user: { connect: { userId: user1.userId } },
        membershipApplication: { connect: { membershipApplicationId: application1.membershipApplicationId } },
      });

      const mailVerification2 = await MailVerificationFactory.create();
      const application2 = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification2.mailVerificationId } },
      });
      const user2 = await UserFactory.create();
      await MemberFactory.create({
        user: { connect: { userId: user2.userId } },
        membershipApplication: { connect: { membershipApplicationId: application2.membershipApplicationId } },
      });

      // ===== Act =====
      const result = await listMembers();

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.length).toBeGreaterThanOrEqual(2);
        // 各メンバーにuserとmembershipApplicationが含まれていることを確認
        result.value.forEach((member) => {
          expect(member.user).toBeDefined();
          expect(member.membershipApplication).toBeDefined();
          expect(member.membershipApplication.mailVerification).toBeDefined();
        });
      }
    });

    it('会員が存在しない場合は空配列を返す', async () => {
      // ===== Act =====
      const result = await listMembers();

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(Array.isArray(result.value)).toBe(true);
      }
    });

    it('ソート順を指定できる（昇順）', async () => {
      // ===== Arrange =====
      const mailVerification1 = await MailVerificationFactory.create();
      const application1 = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification1.mailVerificationId } },
      });
      const user1 = await UserFactory.create();
      await MemberFactory.create({
        user: { connect: { userId: user1.userId } },
        membershipApplication: { connect: { membershipApplicationId: application1.membershipApplicationId } },
      });

      const mailVerification2 = await MailVerificationFactory.create();
      const application2 = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification2.mailVerificationId } },
      });
      const user2 = await UserFactory.create();
      await MemberFactory.create({
        user: { connect: { userId: user2.userId } },
        membershipApplication: { connect: { membershipApplicationId: application2.membershipApplicationId } },
      });

      // ===== Act =====
      const result = await listMembers({ orderBy: 'asc' });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.length).toBeGreaterThanOrEqual(2);
        // 昇順でソートされていることを確認
        for (let i = 1; i < result.value.length; i++) {
          expect(result.value[i].memberId).toBeGreaterThanOrEqual(result.value[i - 1].memberId);
        }
      }
    });

    it('ソート順を指定できる（降順）', async () => {
      // ===== Arrange =====
      const mailVerification1 = await MailVerificationFactory.create();
      const application1 = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification1.mailVerificationId } },
      });
      const user1 = await UserFactory.create();
      await MemberFactory.create({
        user: { connect: { userId: user1.userId } },
        membershipApplication: { connect: { membershipApplicationId: application1.membershipApplicationId } },
      });

      const mailVerification2 = await MailVerificationFactory.create();
      const application2 = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification2.mailVerificationId } },
      });
      const user2 = await UserFactory.create();
      await MemberFactory.create({
        user: { connect: { userId: user2.userId } },
        membershipApplication: { connect: { membershipApplicationId: application2.membershipApplicationId } },
      });

      // ===== Act =====
      const result = await listMembers({ orderBy: 'desc' });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.length).toBeGreaterThanOrEqual(2);
        // 降順でソートされていることを確認
        for (let i = 1; i < result.value.length; i++) {
          expect(result.value[i].memberId).toBeLessThanOrEqual(result.value[i - 1].memberId);
        }
      }
    });

    it('firstNameで部分一致検索できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        firstName: '太郎',
        firstNameKana: 'タロウ',
      });
      // ===== Act =====
      const result = await listMembers({ firstName: '太' });
      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.some((m) => m.firstName === '太郎')).toBe(true);
      }
    });

    it('lastNameで部分一致検索できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        lastName: '山田',
        lastNameKana: 'ヤマダ',
      });
      // ===== Act =====
      const result = await listMembers({ lastName: '山' });
      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.some((m) => m.lastName === '山田')).toBe(true);
      }
    });

    it('memberNumberで完全一致検索できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        memberNumber: 12345,
      });
      // ===== Act =====
      const result = await listMembers({ memberNumber: 12345 });
      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.some((m) => m.memberNumber === 12345)).toBe(true);
      }
    });

    it('includeRetired=falseで退会者を除外できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      // 退会日が過去
      await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        retirementDate: new Date(Date.now() - 1000 * 60 * 60 * 24),
      });
      // ===== Act =====
      const result = await listMembers({ includeRetired: false });
      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.every((m) => !m.retirementDate || m.retirementDate > new Date())).toBe(true);
      }
    });

    it('includeRetired=trueで退会者も含めて取得できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      // 退会日が過去
      await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        retirementDate: new Date(Date.now() - 1000 * 60 * 60 * 24),
      });
      // ===== Act =====
      const result = await listMembers({ includeRetired: true });
      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.some((m) => m.retirementDate && m.retirementDate < new Date())).toBe(true);
      }
    });

    it('退会者のretirementDateが正しく返却される', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const retirementDate = new Date('2024-01-15T10:30:00Z'); // 固定の退会日
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        retirementDate,
        firstName: '退会者',
      });

      // ===== Act =====
      const result = await listMembers({ includeRetired: true });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const retiredMember = result.value.find((m) => m.firstName === '退会者');
        expect(retiredMember).toBeDefined();
        expect(retiredMember!.retirementDate).toBeDefined();
        expect(retiredMember!.retirementDate).toBeInstanceOf(Date);
        // 実際に保存された値と一致することを確認
        expect(retiredMember!.retirementDate!.getTime()).toBe(member.retirementDate!.getTime());
      }
    });

    it('現役会員のretirementDateはnull', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        firstName: '現役会員',
        // retirementDateは設定しない（nullのまま）
      });

      // ===== Act =====
      const result = await listMembers({ includeRetired: true });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const activeMember = result.value.find((m) => m.firstName === '現役会員');
        expect(activeMember).toBeDefined();
        expect(activeMember!.retirementDate).toBeNull();
      }
    });
  });

  describe('findMemberById', () => {
    it('正常に会員を取得できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      });

      // ===== Act =====
      const result = await findMemberById({ memberId: member.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.memberId).toEqual(member.memberId);
        expect(result.value.user).toBeDefined();
        expect(result.value.user.userId).toEqual(user.userId);
        expect(result.value.membershipApplication).toBeDefined();
        expect(result.value.membershipApplication.membershipApplicationId).toEqual(application.membershipApplicationId);
        expect(result.value.membershipApplication.mailVerification).toBeDefined();
      }
    });

    it('存在しない会員IDの場合はエラーを返す', async () => {
      // ===== Act =====
      const result = await findMemberById({ memberId: 9999 });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(MemberNotFoundError);
      }
    });

    it('会員データに関連データが含まれている', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        firstName: 'テスト',
        lastName: '太郎',
        firstNameKana: 'テスト',
        lastNameKana: 'タロウ',
        phoneNumber: '090-1234-5678',
      });

      // ===== Act =====
      const result = await findMemberById({ memberId: member.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const foundMember = result.value;
        expect(foundMember.firstName).toBe('テスト');
        expect(foundMember.lastName).toBe('太郎');
        expect(foundMember.firstNameKana).toBe('テスト');
        expect(foundMember.lastNameKana).toBe('タロウ');
        expect(foundMember.phoneNumber).toBe('090-1234-5678');
        expect(foundMember.memberNumber).toBeGreaterThan(10000);
        expect(foundMember.user.email).toBeDefined();
        expect(foundMember.membershipApplication.mailVerification.email).toBeDefined();
      }
    });
  });

  describe('updateMember', () => {
    it('会員情報を正常に更新できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        postalCode: '100-0001',
        prefecture: '東京都',
        address: '千代田区千代田1-1',
        apartment: 'アパート101',
        phoneNumber: '03-1234-5678',
      });

      const updateData = {
        postalCode: '100-0002',
        prefecture: '大阪府',
        address: '大阪市北区1-1',
        apartment: 'マンション202',
        phoneNumber: '06-9876-5432',
      };

      // ===== Act =====
      const result = await updateMember({ memberId: member.memberId, data: updateData });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const updatedMember = result.value;
        expect(updatedMember.memberId).toBe(member.memberId);
        expect(updatedMember.postalCode).toBe('100-0002');
        expect(updatedMember.prefecture).toBe('大阪府');
        expect(updatedMember.address).toBe('大阪市北区1-1');
        expect(updatedMember.apartment).toBe('マンション202');
        expect(updatedMember.phoneNumber).toBe('06-9876-5432');

        // 関連データが含まれていることを確認
        expect(updatedMember.user).toBeDefined();
        expect(updatedMember.user.userId).toBe(user.userId);
        expect(updatedMember.membershipApplication).toBeDefined();
        expect(updatedMember.membershipApplication.membershipApplicationId).toBe(application.membershipApplicationId);
        expect(updatedMember.membershipApplication.mailVerification).toBeDefined();
      }
    });

    it('部分更新ができる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        postalCode: '100-0001',
        prefecture: '東京都',
        address: '千代田区千代田1-1',
        apartment: 'アパート101',
        phoneNumber: '03-1234-5678',
      });

      // 郵便番号のみを更新
      const updateData = {
        postalCode: '100-0002',
      };

      // ===== Act =====
      const result = await updateMember({ memberId: member.memberId, data: updateData });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const updatedMember = result.value;

        // 更新されたフィールド
        expect(updatedMember.postalCode).toBe('100-0002');

        // 更新されていないフィールドは元の値のまま
        expect(updatedMember.prefecture).toBe('東京都');
        expect(updatedMember.address).toBe('千代田区千代田1-1');
        expect(updatedMember.apartment).toBe('アパート101');
        expect(updatedMember.phoneNumber).toBe('03-1234-5678');
      }
    });

    it('空文字列でフィールドをクリアできる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        apartment: 'アパート101',
      });

      // 建物名とJRA番号を空文字列でクリア
      const updateData = {
        apartment: '',
      };

      // ===== Act =====
      const result = await updateMember({ memberId: member.memberId, data: updateData });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const updatedMember = result.value;
        expect(updatedMember.apartment).toBe('');
      }
    });

    it('nullでフィールドをクリアできる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        apartment: 'アパート101',
      });

      // nullable フィールドをnullでクリア
      const updateData = {
        apartment: null,
      };

      // ===== Act =====
      const result = await updateMember({ memberId: member.memberId, data: updateData });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const updatedMember = result.value;
        expect(updatedMember.apartment).toBeNull();
      }
    });

    it('存在しない会員IDの場合はMemberNotFoundErrorを返す', async () => {
      // ===== Arrange =====
      const updateData = {
        postalCode: '100-0002',
      };

      // ===== Act =====
      const result = await updateMember({ memberId: 99999, data: updateData });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(MemberNotFoundError);
        expect(result.error.name).toBe('MemberNotFoundError');
      }
    });

    it('空のデータで更新しても正常に動作する', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        postalCode: '100-0001',
        prefecture: '東京都',
      });

      // 空のデータで更新
      const updateData = {};

      // ===== Act =====
      const result = await updateMember({ memberId: member.memberId, data: updateData });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const updatedMember = result.value;
        // 元の値が保持されていることを確認
        expect(updatedMember.postalCode).toBe('100-0001');
        expect(updatedMember.prefecture).toBe('東京都');
      }
    });

    it('boolean型フィールドを更新できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      });

      // boolean型フィールドを更新
      const updateData = {};

      // ===== Act =====
      const result = await updateMember({ memberId: member.memberId, data: updateData });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        // no-op: 更新なし
      }
    });

    it('number型フィールドを更新できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      });

      // number型フィールドを更新
      const updateData = {};

      // ===== Act =====
      const result = await updateMember({ memberId: member.memberId, data: updateData });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        // no-op: 更新なし
      }
    });

    it('複数の異なる型のフィールドを同時に更新できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        postalCode: '100-0001',
        apartment: 'アパート101',
      });

      // 異なる型のフィールドを同時に更新
      const updateData = {
        postalCode: '100-0002', // string
        apartment: '', // string (empty)
      };

      // ===== Act =====
      const result = await updateMember({ memberId: member.memberId, data: updateData });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const updatedMember = result.value;
        expect(updatedMember.postalCode).toBe('100-0002');
        expect(updatedMember.apartment).toBe('');
      }
    });
  });

  describe('listMemberInvestmentHorses', () => {
    it('複数の馬に出資する会員の出資馬一覧を取得できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();

      const memberData = {
        userId: user.userId,
        membershipApplicationId: application.membershipApplicationId,
        firstName: 'テスト',
        lastName: '太郎',
        firstNameKana: 'テスト',
        lastNameKana: 'タロウ',
        postalCode: '123-4567',
        prefecture: '東京都',
        address: '渋谷区1-1-1',
        apartment: 'テストマンション101',
        phoneNumber: '090-1234-5678',
        birthYear: 1990,
        birthMonth: 1,
        birthDay: 1,
        approvedAt: new Date(),
        approvedBy: '<EMAIL>',
      };
      // ===== Act =====
      const member = await createMember(memberData);
      if (member.isErr()) {
        throw member.error;
      }
      const horse1 = await createHorse({
        recruitmentYear: 2024,
        recruitmentNo: 123,
        horseName: '出資馬1',
        birthYear: 2022,
        birthMonth: 1,
        birthDay: 1,
        recruitmentName: '出資馬1の22',
        sharesTotal: 1000,
        amountTotal: 30000000,
        note: '出資馬1備考',
        fundStartYear: 2024,
        fundStartMonth: 1,
        fundStartDay: 1,
      });

      const horse2 = await createHorse({
        recruitmentYear: 2024,
        recruitmentNo: 456,
        horseName: '出資馬2',
        birthYear: 2022,
        birthMonth: 1,
        birthDay: 1,
        recruitmentName: '出資馬2の22',
        sharesTotal: 1000,
        amountTotal: 30000000,
        note: '出資馬2備考',
        fundStartYear: 2024,
        fundStartMonth: 1,
        fundStartDay: 1,
      });

      if (horse1.isErr()) {
        throw horse1.error;
      }
      if (horse2.isErr()) {
        throw horse2.error;
      }
      await vPrisma.client.investmentContract.create({
        data: {
          horseId: horse1.value.horseId,
          memberId: member.value.memberId,
          sharesNumber: 13,
          investmentAmount: 13000000,
          contractStatus: InvestmentContractStatus.COMPLETED,
          discount: 0,
          taxRate: 0,
          investmentAmountBeforeTax: 13000000,
        },
      });
      await vPrisma.client.investmentContract.create({
        data: {
          horseId: horse1.value.horseId,
          memberId: member.value.memberId,
          sharesNumber: 10,
          investmentAmount: 10000000,
          contractStatus: InvestmentContractStatus.COMPLETED,
          discount: 0,
          taxRate: 0,
          investmentAmountBeforeTax: 10000000,
        },
      });
      await vPrisma.client.investmentContract.create({
        data: {
          horseId: horse1.value.horseId,
          memberId: member.value.memberId,
          sharesNumber: 10,
          investmentAmount: 10000000,
          contractStatus: InvestmentContractStatus.STARTED,
          discount: 0,
          taxRate: 0,
          investmentAmountBeforeTax: 10000000,
        },
      });

      await vPrisma.client.investmentContract.create({
        data: {
          horseId: horse2.value.horseId,
          memberId: member.value.memberId,
          sharesNumber: 14,
          investmentAmount: 14000000,
          contractStatus: InvestmentContractStatus.COMPLETED,
          discount: 0,
          taxRate: 0,
          investmentAmountBeforeTax: 14000000,
        },
      });

      // ===== Act =====
      const result = await listMemberInvestmentHorses({ memberId: member.value.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const horses = result.value;
        expect(horses).toHaveLength(2);
        expect(horses[0].horseId).toBe(horse1.value.horseId);
        expect(horses[0].investmentShares).toBe(23);
        expect(horses[0].investmentAmount).toBe(23000000);
        expect(horses[1].horseId).toBe(horse2.value.horseId);
        expect(horses[1].investmentShares).toBe(14);
        expect(horses[1].investmentAmount).toBe(14000000);
      }
    });

    it('存在しない会員IDの場合は空の配列を返す', async () => {
      // ===== Act =====
      const result = await listMemberInvestmentHorses({ memberId: 99999 });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const horses = result.value;
        expect(horses).toHaveLength(0);
      }
    });
  });

  describe('cancelRetirement', () => {
    it('退会予定を正常にキャンセルできる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const retirementDate = new Date(Date.now() + 1000 * 60 * 60 * 24 * 30); // 30日後
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        retirementDate,
        firstName: '退会予定者',
      });

      // 退会日が設定されていることを確認
      expect(member.retirementDate).toBeDefined();
      expect(member.retirementDate).not.toBeNull();

      // ===== Act =====
      const result = await cancelRetirement({ memberId: member.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // データベースから再取得して退会日がnullになっていることを確認
      const updatedMember = await vPrisma.client.member.findUnique({
        where: { memberId: member.memberId },
      });
      expect(updatedMember).toBeDefined();
      expect(updatedMember!.retirementDate).toBeNull();
    });

    it('既に退会日がnullの会員でもエラーにならない', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        retirementDate: null, // 既にnull
        firstName: '現役会員',
      });

      // 退会日がnullであることを確認
      expect(member.retirementDate).toBeNull();

      // ===== Act =====
      const result = await cancelRetirement({ memberId: member.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // データベースから再取得して退会日がnullのままであることを確認
      const updatedMember = await vPrisma.client.member.findUnique({
        where: { memberId: member.memberId },
      });
      expect(updatedMember).toBeDefined();
      expect(updatedMember!.retirementDate).toBeNull();
    });

    it('存在しない会員IDの場合はMemberNotFoundErrorを返す', async () => {
      // ===== Act =====
      const result = await cancelRetirement({ memberId: 99999 });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(MemberNotFoundError);
        expect(result.error.name).toBe('MemberNotFoundError');
      }
    });

    it('退会予定キャンセル後に会員一覧で現役会員として表示される', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const retirementDate = new Date(Date.now() + 1000 * 60 * 60 * 24 * 30); // 30日後
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        retirementDate,
        firstName: 'キャンセル対象者',
      });

      // 退会予定キャンセル前は退会者として表示される
      const beforeResult = await listMembers({ includeRetired: false });
      expect(beforeResult.isOk()).toBe(true);
      if (beforeResult.isOk()) {
        const beforeMember = beforeResult.value.find((m) => m.memberId === member.memberId);
        expect(beforeMember).toBeUndefined(); // 退会者なので含まれない
      }

      // ===== Act =====
      const cancelResult = await cancelRetirement({ memberId: member.memberId });
      expect(cancelResult.isOk()).toBe(true);

      // ===== Assert =====
      // 退会予定キャンセル後は現役会員として表示される
      const afterResult = await listMembers({ includeRetired: false });
      expect(afterResult.isOk()).toBe(true);
      if (afterResult.isOk()) {
        const afterMember = afterResult.value.find((m) => m.memberId === member.memberId);
        expect(afterMember).toBeDefined(); // 現役会員なので含まれる
        expect(afterMember!.retirementDate).toBeNull();
      }
    });
  });
});
