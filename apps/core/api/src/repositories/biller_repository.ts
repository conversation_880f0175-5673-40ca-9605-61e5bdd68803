import { create } from '@bufbuild/protobuf';
import { err, ok, ResultAsync } from 'neverthrow';
import { Biller, BankAccountType as ProtoBankAccountType, BillerSchema } from '@hami/core-admin-api-schema/biller_service_pb';
import { Prisma, BankAccountType } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class BillerNotFoundError extends Error {
  readonly name = 'BillerNotFoundError';
}

export const listBillers = () => {
  return ResultAsync.fromPromise(
    client.biller.findMany({
      orderBy: { id: 'asc' },
    }),
    () => new DatabaseError('Failed to query billers')
  ).map((billers) => billers.map(mapToBiller));
};

export const getBiller = ({ id }: { id: number }) => {
  return ResultAsync.fromPromise(
    client.biller.findUnique({ where: { id } }),
    () => new DatabaseError('Failed to query biller')
  ).andThen((biller) => {
    if (!biller) {
      return err(new BillerNotFoundError());
    }
    return ok(mapToBiller(biller));
  });
};

export const createBiller = (data: {
  name: string;
  address: string;
  bankName: string;
  bankBranch: string;
  bankAccount: string;
  bankAccountType: ProtoBankAccountType;
  bankAccountName: string;
}) => {
  return ResultAsync.fromPromise(
    client.biller.create({
      data: {
        name: data.name,
        address: data.address,
        bankName: data.bankName,
        bankBranch: data.bankBranch,
        bankAccount: data.bankAccount,
        bankAccountType: mapBankAccountTypeToPrisma(data.bankAccountType),
        bankAccountName: data.bankAccountName,
      },
    }),
    () => new DatabaseError('Failed to create biller')
  ).map(mapToBiller);
};

export const updateBiller = (
  id: number,
  data: {
    name: string;
    address: string;
    bankName: string;
    bankBranch: string;
    bankAccount: string;
    bankAccountType: ProtoBankAccountType;
    bankAccountName: string;
  },
): ResultAsync<Record<string, never>, DatabaseError | BillerNotFoundError> => {
  return ResultAsync.fromPromise(
    client.biller.update({
      where: { id },
      data: {
        name: data.name,
        address: data.address,
        bankName: data.bankName,
        bankBranch: data.bankBranch,
        bankAccount: data.bankAccount,
        bankAccountType: mapBankAccountTypeToPrisma(data.bankAccountType),
        bankAccountName: data.bankAccountName,
      },
    }),
    (error) => {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return new BillerNotFoundError();
      }
      return new DatabaseError('Failed to update biller');
    }
  ).map(() => ({}));
};

/**
 * PrismaのモデルをBillerに変換
 */
const mapToBiller = (biller: Prisma.BillerGetPayload<{}>): Biller => {
  return create(BillerSchema, {
    id: biller.id,
    name: biller.name,
    address: biller.address,
    bankName: biller.bankName,
    bankBranch: biller.bankBranch,
    bankAccount: biller.bankAccount,
    bankAccountType: mapBankAccountTypeFromPrisma(biller.bankAccountType),
    bankAccountName: biller.bankAccountName,
  });
};

/**
 * Prismaの口座種別をBillerの口座種別に変換
 */
const mapBankAccountTypeFromPrisma = (type: BankAccountType): ProtoBankAccountType => {
  switch (type) {
    case BankAccountType.savings:
      return ProtoBankAccountType.SAVINGS;
    case BankAccountType.checking:
      return ProtoBankAccountType.CHECKING;
    default:
      return ProtoBankAccountType.UNSPECIFIED;
  }
};

/**
 * Billerの口座種別をPrismaの口座種別に変換
 */
const mapBankAccountTypeToPrisma = (type: ProtoBankAccountType): BankAccountType => {
  switch (type) {
    case ProtoBankAccountType.SAVINGS:
      return BankAccountType.savings;
    case ProtoBankAccountType.CHECKING:
      return BankAccountType.checking;
    default:
      return BankAccountType.savings;
  }
};
