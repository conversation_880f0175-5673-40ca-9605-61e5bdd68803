import crypto from 'node:crypto';
import { err, ok, ResultAsync } from 'neverthrow';
import { Prisma, ReviewType as ReviewTypePrisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

type MembershipApplicationListItem = Prisma.MembershipApplicationGetPayload<{
  include: {
    mailVerification: true;
    identityDocuments: { orderBy: { createdAt: 'asc' } };
    documentGroups: {
      include: { documents: { orderBy: { createdAt: 'asc' } } };
      orderBy: { createdAt: 'asc' };
    };
    applicationReviewLogs: { orderBy: { createdAt: 'desc' }; take: 1 };
    documentGroupReviewLogs: { orderBy: { createdAt: 'desc' }; take: 1 };
    complianceReviewLogs: { orderBy: { createdAt: 'desc' }; take: 1 };
    complianceDocumentGroupReviewLogs: { orderBy: { createdAt: 'desc' }; take: 1 };
    beneficialOwnerDeclarations: { orderBy: { createdAt: 'asc' } };
  };
}>;

export class MembershipApplicationNotFoundError extends Error {
  readonly name = 'MembershipApplicationNotFoundError';
}

export class IdentityDocumentNotFoundError extends Error {
  readonly name = 'IdentityDocumentNotFoundError';
}

export class IdentityDocumentFileNotFoundError extends Error {
  readonly name = 'IdentityDocumentFileNotFoundError';

  constructor(fileKey: string) {
    super(`身分証明書ファイルが見つかりません: ${fileKey}`);
  }
}

/**
 * mailVerificationIdでMembershipApplicationレコードを検索
 * @param mailVerificationId 検索するmailVerificationId
 * @returns 見つかった場合はMembershipApplicationレコード、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const findMembershipApplicationByMailVerificationId = ({ mailVerificationId }: { mailVerificationId: number }) => {
  return ResultAsync.fromPromise(
    client.membershipApplication.findUnique({ where: { mailVerificationId } }),
    () => new DatabaseError('Failed to query membership application')
  ).andThen((record) => {
    if (!record) {
      return err(new MembershipApplicationNotFoundError());
    }
    return ok(record);
  });
};

/**
 * MembershipApplicationレコードを作成または更新
 * @param data 作成/更新するデータ (mailVerificationIdを含む)
 * @returns 作成/更新されたMembershipApplication
 */
export const upsertMembershipApplication = (
  data: Omit<Prisma.MembershipApplicationCreateInput, 'mailVerification' | 'identityUploadToken'> & { mailVerificationId: number }
) => {
  const { mailVerificationId, ...rest } = data;

  // まず既存のレコードの存在を確認
  return ResultAsync.fromPromise(
    client.membershipApplication.findUnique({
      where: { mailVerificationId },
      include: {
        documentGroups: {
          take: 1,
        },
      },
    }),
    (e) => {
      console.error('Failed to check existing membership application:', e);
      return new DatabaseError('Failed to check existing membership application');
    }
  ).andThen((existingApplication) => {
    const isNewApplication = !existingApplication;
    const needsDocumentGroup = isNewApplication || existingApplication.documentGroups.length === 0;

    return ResultAsync.fromPromise(
      client.membershipApplication.upsert({
        where: { mailVerificationId },
        update: Object.fromEntries(Object.entries(rest).filter(([key]) => key !== 'identityUploadToken')),
        create: {
          ...rest,
          mailVerification: { connect: { mailVerificationId } },
          identityUploadToken: crypto.randomBytes(32).toString('hex'),
        },
      }),
      (e) => {
        console.error('Failed to upsert membership application:', e);
        return new DatabaseError('Failed to upsert membership application');
      }
    ).andThen((application) => {
      // 新規作成またはdocument groupが存在しない場合のみ初期document groupsを作成
      if (needsDocumentGroup) {
        return createInitialDocumentGroup(application.membershipApplicationId).map((documentGroup) => ({
          ...application,
          uploadToken: documentGroup.uploadToken,
        }));
      } else {
        return ok(application);
      }
    });
  });
};

export interface PaginationOptions {
  page: number;
  pageSize: number;
}

export interface MembershipApplicationListResult {
  applications: MembershipApplicationListItem[];
  hasNextPage: boolean;
}

/**
 * MembershipApplicationレコードを一覧取得
 * @param pagination ページネーション設定（オプション）
 * @param orderBy ソート順
 * @returns MembershipApplicationレコードの配列と次ページ有無
 */
export type MembershipApplicationStatusFilter = 'ACTION_REQUIRED' | 'APPROVED' | 'SENT' | 'COMPLIANCE_PENDING';

const buildStatusFilterWhere = (statusFilter?: MembershipApplicationStatusFilter): Prisma.MembershipApplicationWhereInput => {
  if (!statusFilter) {
    return {};
  }

  switch (statusFilter) {
    case 'ACTION_REQUIRED':
      return {
        OR: [
          {
            applicationReviewLogs: {
              none: {},
            },
          },
          {
            documentGroupReviewLogs: {
              none: {},
            },
          },
        ],
      };
    case 'APPROVED':
      return {
        AND: [
          {
            applicationReviewLogs: {
              some: {
                reviewType: ReviewTypePrisma.APPROVE,
              },
            },
          },
          {
            documentGroupReviewLogs: {
              some: {
                reviewType: ReviewTypePrisma.APPROVE,
              },
            },
          },
          {
            complianceReviewLogs: {
              some: {
                reviewType: ReviewTypePrisma.APPROVE,
              },
            },
          },
          {
            complianceDocumentGroupReviewLogs: {
              some: {
                reviewType: ReviewTypePrisma.APPROVE,
              },
            },
          },
          {
            isPrinted: false,
          },
        ],
      };
    case 'COMPLIANCE_PENDING':
      return {
        AND: [
          {
            applicationReviewLogs: {
              some: { reviewType: ReviewTypePrisma.APPROVE },
            },
          },
          {
            documentGroupReviewLogs: {
              some: { reviewType: ReviewTypePrisma.APPROVE },
            },
          },
          {
            OR: [
              {
                complianceReviewLogs: {
                  none: { reviewType: ReviewTypePrisma.APPROVE },
                },
              },
              {
                complianceDocumentGroupReviewLogs: {
                  none: { reviewType: ReviewTypePrisma.APPROVE },
                },
              },
            ],
          },
        ],
      };
    case 'SENT':
      return {
        isPrinted: true,
      };
    default:
      return {};
  }
};

export const listMembershipApplications = ({
  pagination,
  orderBy = 'desc',
  statusFilter,
}: {
  pagination?: PaginationOptions;
  orderBy?: 'asc' | 'desc';
  statusFilter?: MembershipApplicationStatusFilter;
} = {}) => {
  return ResultAsync.fromPromise(
    (async () => {
      const includeOptions = {
        mailVerification: true,
        identityDocuments: {
          orderBy: {
            createdAt: 'asc' as const,
          },
        },
        documentGroups: {
          include: {
            documents: {
              orderBy: {
                createdAt: 'asc' as const,
              },
            },
          },
          orderBy: {
            createdAt: 'asc' as const,
          },
        },
        applicationReviewLogs: {
          orderBy: { createdAt: 'desc' as const },
          take: 1,
        },
        documentGroupReviewLogs: {
          orderBy: { createdAt: 'desc' as const },
          take: 1,
        },
        complianceReviewLogs: {
          orderBy: { createdAt: 'desc' as const },
          take: 1,
        },
        complianceDocumentGroupReviewLogs: {
          orderBy: { createdAt: 'desc' as const },
          take: 1,
        },
        beneficialOwnerDeclarations: {
          orderBy: { createdAt: 'asc' as const },
        },
      };

      const where = buildStatusFilterWhere(statusFilter);

      if (!pagination) {
        // ページネーションなしの場合は従来通り
        const applications = await client.membershipApplication.findMany({
          include: includeOptions,
          where,
          orderBy: {
            membershipApplicationId: orderBy,
          },
        });

        return {
          applications,
          hasNextPage: false,
        };
      }

      // ページネーションありの場合
      const skip = (pagination.page - 1) * pagination.pageSize;

      // 次のページがあるかチェックするため、1件多く取得
      const applications = await client.membershipApplication.findMany({
        include: includeOptions,
        where,
        orderBy: {
          membershipApplicationId: orderBy,
        },
        skip,
        take: pagination.pageSize + 1,
      });

      const hasNextPage = applications.length > pagination.pageSize;

      // 実際に返すのは指定されたページサイズ分のみ
      const actualApplications = hasNextPage ? applications.slice(0, pagination.pageSize) : applications;

      return {
        applications: actualApplications,
        hasNextPage,
      };
    })(),
    () => new DatabaseError('Failed to query membership applications')
  );
};

/**
 * IDでMembershipApplicationレコードを検索
 * @param id 検索するID
 * @returns 見つかった場合はMembershipApplicationレコード、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const findMembershipApplicationById = ({ membershipApplicationId }: { membershipApplicationId: number }) => {
  return ResultAsync.fromPromise(
    client.membershipApplication.findUnique({
      where: { membershipApplicationId },
      include: {
        mailVerification: true,
        identityDocuments: {
          orderBy: {
            createdAt: 'asc',
          },
        },
        documentGroups: {
          include: {
            documents: {
              orderBy: {
                createdAt: 'asc',
              },
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
        beneficialOwnerDeclarations: {
          orderBy: {
            createdAt: 'asc',
          },
        },
        applicationReviewLogs: {
          orderBy: {
            createdAt: 'desc',
          },
        },
        documentGroupReviewLogs: {
          orderBy: {
            createdAt: 'desc',
          },
        },
        complianceReviewLogs: {
          orderBy: {
            createdAt: 'desc',
          },
        },
        complianceDocumentGroupReviewLogs: {
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    }),
    () => new DatabaseError('Failed to query membership application')
  ).andThen((record) => {
    if (!record) {
      return err(new MembershipApplicationNotFoundError());
    }
    return ok(record);
  });
};

/**
 * IDで身分証明書レコードを検索
 * @param identityDocumentId 検索する身分証明書ID
 * @returns 見つかった場合は身分証明書レコード、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const findIdentityDocumentById = ({ identityDocumentId }: { identityDocumentId: number }) => {
  return ResultAsync.fromPromise(
    client.membershipApplicationIdentityDocument.findUnique({
      where: { membershipApplicationIdentityDocumentId: identityDocumentId },
      include: {
        membershipApplication: true,
      },
    }),
    () => new DatabaseError('Failed to query identity document')
  ).andThen((record) => {
    if (!record) {
      return err(new IdentityDocumentNotFoundError());
    }
    return ok(record);
  });
};

/**
 * mailVerificationIdでMembershipApplicationレコードを検索し、レビュー情報も含めて取得
 * @param mailVerificationId 検索するmailVerificationId
 * @returns 見つかった場合はMembershipApplicationレコード（レビュー情報含む）、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const findMembershipApplicationWithReviewsByMailVerificationId = ({ mailVerificationId }: { mailVerificationId: number }) => {
  return ResultAsync.fromPromise(
    client.membershipApplication.findUnique({
      where: { mailVerificationId },
      include: {
        mailVerification: true,
        documentGroups: {
          orderBy: {
            createdAt: 'asc',
          },
        },
        applicationReviewLogs: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
        documentGroupReviewLogs: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    }),
    (e) => {
      console.error('Error fetching membership application with reviews:', e);
      return new DatabaseError('Failed to query membership application with reviews');
    }
  ).andThen((record) => {
    if (!record) {
      return err(new MembershipApplicationNotFoundError());
    }
    return ok(record);
  });
};

/**
 * uploadTokenでMembershipApplicationDocumentGroupレコードを検索
 * @param uploadToken 検索するuploadToken
 * @returns 見つかった場合はMembershipApplicationDocumentGroupレコード、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const findDocumentGroupByUploadToken = ({ uploadToken }: { uploadToken: string }) => {
  return ResultAsync.fromPromise(
    client.membershipApplicationDocumentGroup.findUnique({
      where: { uploadToken },
      include: {
        membershipApplication: {
          include: {
            mailVerification: true,
          },
        },
      },
    }),
    (e) => {
      console.error('Failed to query document group by upload token:', e);
      return new DatabaseError('Failed to query document group by upload token');
    }
  ).andThen((record) => {
    if (!record) {
      return err(new MembershipApplicationNotFoundError());
    }
    return ok(record);
  });
};

/**
 * MembershipApplicationIdentityDocumentレコードを作成
 * @param data 作成するデータ
 * @returns 作成されたMembershipApplicationIdentityDocument
 */
export const createMembershipApplicationIdentityDocument = (
  data: Omit<Prisma.MembershipApplicationIdentityDocumentCreateInput, 'membershipApplication' | 'documentGroup'> & {
    membershipApplicationId: number;
    documentGroupId?: number;
  }
) => {
  const { membershipApplicationId, documentGroupId, ...rest } = data;

  return ResultAsync.fromPromise(
    client.membershipApplicationIdentityDocument.create({
      data: {
        ...rest,
        membershipApplication: { connect: { membershipApplicationId } },
        ...(documentGroupId && {
          documentGroup: { connect: { membershipApplicationDocumentGroupId: documentGroupId } },
        }),
      },
    }),
    (e) => {
      console.error('Failed to create membership application identity document:', e);
      return new DatabaseError('Failed to create membership application identity document');
    }
  );
};

/**
 * 最新の有効なdocument groupを取得
 * @param membershipApplicationId 会員申込ID
 * @returns 最新のdocument group
 */
export const findLatestDocumentGroup = (membershipApplicationId: number) => {
  return ResultAsync.fromPromise(
    client.membershipApplicationDocumentGroup.findFirst({
      where: { membershipApplicationId },
      orderBy: { createdAt: 'desc' },
    }),
    (e) => {
      console.error('Failed to find latest document group:', e);
      return new DatabaseError('Failed to find latest document group');
    }
  );
};

/**
 * document groupを作成
 * @param membershipApplicationId 会員申込ID
 * @param type 作成タイプ（'initial' | 'remand'）
 * @returns 作成されたdocument group
 */
export const createDocumentGroup = (membershipApplicationId: number, type: 'initial' | 'remand') => {
  // 完全にランダムなUUIDのみのuploadTokenを生成
  const uploadToken = crypto.randomUUID();

  // document groupを作成（identity_verificationのみ）
  const documentGroup = {
    membershipApplicationId,
    uploadToken,
    groupKey: 'identity_verification',
    isCompleted: false,
  };

  return ResultAsync.fromPromise(
    client.membershipApplicationDocumentGroup.create({
      data: documentGroup,
    }),
    (e) => {
      console.error(`Failed to create ${type} document group:`, e);
      return new DatabaseError('Failed to create document group');
    }
  );
};

/**
 * 初回申し込み時に初期document groupを作成
 * @param membershipApplicationId 会員申込ID
 * @returns 作成されたdocument group
 */
export const createInitialDocumentGroup = (membershipApplicationId: number) => {
  return createDocumentGroup(membershipApplicationId, 'initial');
};

/**
 * 差し戻し時に新しいdocument groupを作成
 * @param membershipApplicationId 会員申込ID
 * @returns 作成されたdocument group
 */
export const createNewDocumentGroupForRemand = (membershipApplicationId: number) => {
  return createDocumentGroup(membershipApplicationId, 'remand');
};

/**
 * 差し戻し時に既存のdocument groupをリセット（再利用）
 * - 既存の本人確認書類を削除
 * - uploadToken を再発行
 * - isCompleted を false に更新
 */
export const resetDocumentGroupForRemand = (membershipApplicationId: number) => {
  return ResultAsync.fromPromise(
    (async () => {
      // 最新のグループを取得（現状 identity_verification のみ想定）
      const latestGroup = await client.membershipApplicationDocumentGroup.findFirst({
        where: { membershipApplicationId },
        orderBy: { createdAt: 'desc' },
      });

      if (!latestGroup) {
        throw new Error('Document group not found');
      }

      const newToken = crypto.randomUUID();

      // 既存書類の削除とグループのリセットをトランザクションで実施
      const [, updated] = await client.$transaction([
        client.membershipApplicationIdentityDocument.deleteMany({
          where: { documentGroupId: latestGroup.membershipApplicationDocumentGroupId },
        }),
        client.membershipApplicationDocumentGroup.update({
          where: { membershipApplicationDocumentGroupId: latestGroup.membershipApplicationDocumentGroupId },
          data: { uploadToken: newToken, isCompleted: false },
        }),
      ]);

      return updated;
    })(),
    (e) => {
      console.error('Failed to reset document group for remand:', e);
      return new DatabaseError('Failed to reset document group for remand');
    }
  );
};

/**
 * document groupの完了状態を更新
 * @param documentGroupId document group ID
 * @param isCompleted 完了状態
 * @returns 更新されたdocument group
 */
export const updateDocumentGroupCompletion = ({ documentGroupId, isCompleted }: { documentGroupId: number; isCompleted: boolean }) => {
  return ResultAsync.fromPromise(
    client.membershipApplicationDocumentGroup.update({
      where: { membershipApplicationDocumentGroupId: documentGroupId },
      data: { isCompleted },
    }),
    (e) => {
      console.error('Failed to update document group completion:', e);
      return new DatabaseError('Failed to update document group completion');
    }
  );
};

/**
 * 指定した入会申込ID群を送付済み（isPrinted=true）に更新
 * @param membershipApplicationIds 更新対象のID配列
 */
export const markMembershipApplicationsAsPrinted = (membershipApplicationIds: number[]) => {
  if (!membershipApplicationIds.length) {
    return ok({ count: 0 });
  }
  return ResultAsync.fromPromise(
    client.membershipApplication.updateMany({
      where: { membershipApplicationId: { in: membershipApplicationIds } },
      data: { isPrinted: true },
    }),
    (e) => {
      console.error('Failed to mark applications as printed:', e);
      return new DatabaseError('Failed to mark applications as printed');
    }
  );
};

export type MembershipApplicationWithMember = Prisma.MembershipApplicationGetPayload<{
  include: {
    mailVerification: true;
    member: {
      include: {
        user: true;
        corporate: true;
      };
    };
  };
}>;

/**
 * 承認済み申し込みと会員データをジョインして取得
 * @param includePrinted 送付済みも含めるかどうか（デフォルト: false）
 * @returns 承認済み申し込みと対応する会員データの配列
 */
export const listApprovedMembershipApplicationsWithMembers = ({ includePrinted = false }: { includePrinted?: boolean } = {}): ResultAsync<MembershipApplicationWithMember[], DatabaseError> => {
  return ResultAsync.fromPromise(
    client.membershipApplication.findMany({
      where: {
        // 承認済みのみ
        applicationReviewLogs: {
          some: {
            reviewType: ReviewTypePrisma.APPROVE,
          },
        },
        documentGroupReviewLogs: {
          some: {
            reviewType: ReviewTypePrisma.APPROVE,
          },
        },
        complianceReviewLogs: {
          some: {
            reviewType: ReviewTypePrisma.APPROVE,
          },
        },
        complianceDocumentGroupReviewLogs: {
          some: {
            reviewType: ReviewTypePrisma.APPROVE,
          },
        },
        // 送付済みフィルター
        ...(includePrinted ? {} : { isPrinted: false }),
      },
      include: {
        mailVerification: true,
        // 会員データをジョイン
        member: {
          include: {
            user: true,
            corporate: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    }),
    (e) => {
      console.error('Failed to list approved applications with members:', e);
      return new DatabaseError('Failed to list approved applications with members');
    }
  );
};
