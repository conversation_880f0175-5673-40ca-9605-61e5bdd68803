import { err, ok, ResultAsync } from 'neverthrow';
import type { Prisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class MemberNotFoundError extends Error {
  readonly name = 'MemberNotFoundError';
}

// 会員情報の更新可能フィールドの型定義
export type MemberUpdateData = Partial<{
  postalCode: string;
  prefecture: string;
  address: string;
  apartment: string | null;
  phoneNumber: string;
}>;

/**
 * 新しい会員を作成
 * @param data 会員作成データ
 * @returns 成功した場合はMemberレコード、DBエラー時はResultAsyncでエラー
 */
export const createMember = (
  data: Omit<Prisma.MemberCreateInput, 'user' | 'membershipApplication' | 'memberNumber'> & {
    userId: number;
    membershipApplicationId: number;
  }
) => {
  const { userId, membershipApplicationId, ...rest } = data;

  return ResultAsync.fromPromise(
    client.member.create({
      data: {
        ...rest,
        user: { connect: { userId } },
        membershipApplication: { connect: { membershipApplicationId } },
      },
      include: {
        user: true,
        membershipApplication: {
          include: {
            mailVerification: true,
          },
        },
      },
    }),
    () => new DatabaseError('Failed to create member')
  );
};

/**
 * 会員一覧を取得（検索条件付き）
 * @param params 検索条件
 * @returns 会員レコードの配列
 */
export const listMembers = ({
  orderBy = 'desc',
  firstName,
  lastName,
  memberNumber,
  includeRetired,
}: {
  orderBy?: 'asc' | 'desc';
  firstName?: string;
  lastName?: string;
  memberNumber?: number;
  includeRetired?: boolean;
} = {}) => {
  const andConditions: Prisma.MemberWhereInput[] = [];

  // firstName条件
  if (firstName && firstName.trim() !== '') {
    andConditions.push({
      OR: [{ firstName: { contains: firstName } }, { firstNameKana: { contains: firstName } }],
    });
  }
  // lastName条件
  if (lastName && lastName.trim() !== '') {
    andConditions.push({
      OR: [{ lastName: { contains: lastName } }, { lastNameKana: { contains: lastName } }],
    });
  }
  // memberNumber条件
  if (memberNumber !== undefined && memberNumber !== null && memberNumber !== 0) {
    andConditions.push({ memberNumber: memberNumber });
  }
  // includeRetired条件
  if (!includeRetired) {
    andConditions.push({
      retirementDate: null,
    });
  }

  const where: Prisma.MemberWhereInput = {
    AND: andConditions,
  };

  return ResultAsync.fromPromise(
    client.member.findMany({
      where,
      include: {
        user: true,
        membershipApplication: {
          include: {
            mailVerification: true,
          },
        },
      },
      orderBy: {
        memberId: orderBy,
      },
    }),
    () => new DatabaseError('Failed to query members')
  );
};

/**
 * IDで会員レコードを検索
 * @param memberId 検索する会員ID
 * @returns 見つかった場合は会員レコード、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const findMemberById = ({ memberId }: { memberId: number }) => {
  return ResultAsync.fromPromise(
    client.member.findUnique({
      where: { memberId },
      include: {
        user: true,
        membershipApplication: {
          include: {
            mailVerification: true,
          },
        },
      },
    }),
    () => new DatabaseError('Failed to query member')
  ).andThen((record) => {
    if (!record) {
      return err(new MemberNotFoundError());
    }
    return ok(record);
  });
};

/**
 * 会員情報を更新
 * @param memberId 更新する会員ID
 * @param data 更新データ
 * @returns 成功した場合は更新された会員レコード、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const updateMember = ({ memberId, data }: { memberId: number; data: MemberUpdateData }) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 会員の存在確認
      const existingMember = await tx.member.findUnique({
        where: { memberId },
      });

      if (!existingMember) {
        throw new MemberNotFoundError();
      }

      // 会員情報を更新
      return tx.member.update({
        where: { memberId },
        data,
        include: {
          user: true,
          membershipApplication: {
            include: {
              mailVerification: true,
            },
          },
        },
      });
    }),
    (e) => {
      console.error('Failed to update member:', e);
      if (e instanceof MemberNotFoundError) {
        return e;
      }
      return new DatabaseError('Failed to update member');
    }
  );
};

/**
 * 会員の出資馬一覧を取得
 * @param memberId 会員ID
 * @returns 会員の出資馬リスト
 */
export const listMemberInvestmentHorses = ({ memberId }: { memberId: number }) => {
  return ResultAsync.fromPromise(
    client.investmentContract.findMany({
      where: {
        memberId,
        contractStatus: 'COMPLETED', // 完了ステータスのもののみ
      },
      include: {
        horse: {
          include: {
            profile: true,
          },
        },
      },
    }),
    () => new DatabaseError('Failed to list member investment horses')
  ).map((contracts) => {
    // horseIdでグループ化してsharesNumberとinvestmentAmountを合計
    const horsesMap = new Map();

    contracts.forEach((contract) => {
      const horseId = contract.horse.horseId;

      if (horsesMap.has(horseId)) {
        // 既存の馬の場合、sharesとamountを合計
        const existing = horsesMap.get(horseId);
        existing.investmentShares += contract.sharesNumber;
        existing.investmentAmount += contract.investmentAmount;
      } else {
        // 新しい馬の場合、新規追加
        horsesMap.set(horseId, {
          horseId: contract.horse.horseId,
          birthYear: contract.horse.birthYear,
          gender: contract.horse.profile?.gender || undefined,
          horseName: contract.horse.horseName || contract.horse.recruitmentName,
          investmentShares: contract.sharesNumber,
          investmentAmount: contract.investmentAmount,
        });
      }
    });

    // MapをArrayに変換して返却
    return Array.from(horsesMap.values());
  });
};

/**
 * 退会予定をキャンセル
 * @param memberId キャンセルする会員ID
 * @returns 成功した場合はvoid、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const cancelRetirement = ({ memberId }: { memberId: number }) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 会員の存在確認
      const existingMember = await tx.member.findUnique({
        where: { memberId },
      });

      if (!existingMember) {
        throw new MemberNotFoundError();
      }

      // 退会日をnullに設定してキャンセル
      await tx.member.update({
        where: { memberId },
        data: {
          retirementDate: null,
        },
      });
    }),
    (e) => {
      console.error('Failed to cancel retirement:', e);
      if (e instanceof MemberNotFoundError) {
        return e;
      }
      return new DatabaseError('Failed to cancel retirement');
    }
  );
};
