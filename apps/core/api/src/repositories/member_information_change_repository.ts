import { ResultAsync, err, ok } from 'neverthrow';
import {
  MemberInformationChangeStatus,
  ReviewType,
  MemberInformationChangeApplication,
  Member,
  User,
  MemberInformationChangeReviewLog,
  Prisma,
} from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

// Type definition for the application with all required relations
export type MemberInformationChangeApplicationWithRelations = MemberInformationChangeApplication & {
  member: Member & {
    user: User;
  };
  reviewLogs: MemberInformationChangeReviewLog[];
};

export class MemberInformationChangeApplicationNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'MemberInformationChangeApplicationNotFoundError';
  }
}

export class UnauthorizedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class InvalidStatusError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InvalidStatusError';
  }
}

/**
 * 登録情報変更申請を作成
 * 新規申請作成時に同一会員の未処理申請を自動的にCANCELLED状態に変更
 */
export const createMemberInformationChangeApplication = ({
  memberId,
  postalCode,
  prefecture,
  address,
  apartment,
  phoneNumber,
  requestedChangeDate,
  reason,
}: {
  memberId: number;
  postalCode?: string;
  prefecture?: string;
  address?: string;
  apartment?: string;
  phoneNumber?: string;
  requestedChangeDate: Date;
  reason?: string;
}) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 同一会員の未処理申請をCANCELLED状態に変更
      await tx.memberInformationChangeApplication.updateMany({
        where: {
          memberId,
          status: MemberInformationChangeStatus.PENDING,
        },
        data: {
          status: MemberInformationChangeStatus.CANCELLED,
        },
      });

      // 新規申請を作成
      return tx.memberInformationChangeApplication.create({
        data: {
          memberId,
          postalCode,
          prefecture,
          address,
          apartment,
          phoneNumber,
          requestedChangeDate,
          reason,
          status: MemberInformationChangeStatus.PENDING,
        },
        include: {
          member: true,
          reviewLogs: {
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      });
    }),
    (e) => {
      console.error('Failed to create member information change application:', e);
      return new DatabaseError('Failed to create member information change application');
    }
  );
};

/**
 * ソートフィールドからPrismaのorderBy句を構築
 */
const buildOrderByClause = (sortBy: string, sortOrder: 'asc' | 'desc') => {
  switch (sortBy) {
    case 'memberNumber':
      return { member: { memberNumber: sortOrder } };
    case 'createdAt':
    case 'updatedAt':
    case 'requestedChangeDate':
    case 'memberInformationChangeApplicationId':
    case 'status':
      return { [sortBy]: sortOrder };
    default:
      return { createdAt: sortOrder };
  }
};

/**
 * 管理者用：申請一覧を取得
 */
export const getAdminMemberInformationChangeApplications = ({
  page = 1,
  limit = 10,
  status,
  sortBy = 'createdAt',
  sortOrder = 'desc',
}: {
  page?: number;
  limit?: number;
  status?: MemberInformationChangeStatus;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) => {
  // Note: sortBy is assumed to be already validated and normalized by the handler layer
  // The handler ensures only valid AdminMemberInformationChangeSortField values are passed here
  const skip = (page - 1) * limit;
  const orderBy = buildOrderByClause(sortBy, sortOrder);

  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      const where = status ? { status } : {};

      const [applications, totalCount] = await Promise.all([
        tx.memberInformationChangeApplication.findMany({
          where,
          include: {
            member: {
              include: {
                user: true,
              },
            },
            reviewLogs: {
              orderBy: {
                createdAt: 'desc',
              },
              take: 1,
            },
          },
          orderBy,
          skip,
          take: limit,
        }),
        tx.memberInformationChangeApplication.count({ where }),
      ]);

      return { applications, totalCount };
    }),
    (e) => {
      console.error('Failed to get admin member information change applications:', e);
      return new DatabaseError('Failed to get admin member information change applications');
    }
  );
};

/**
 * 管理者用：申請詳細を取得
 */
export const getAdminMemberInformationChangeApplicationById = ({
  memberInformationChangeApplicationId,
}: {
  memberInformationChangeApplicationId: number;
}) => {
  return ResultAsync.fromPromise(
    client.memberInformationChangeApplication.findUnique({
      where: { memberInformationChangeApplicationId },
      include: {
        member: {
          include: {
            user: true,
          },
        },
        reviewLogs: {
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    }),
    (e) => {
      console.error('Failed to get admin member information change application:', e);
      return new DatabaseError('Failed to get admin member information change application');
    }
  ).andThen((application) => {
    if (!application) {
      return err(new MemberInformationChangeApplicationNotFoundError('Application not found'));
    }
    return ok(application);
  });
};

/**
 * 申請を審査（承認・拒否）
 */
export const reviewMemberInformationChangeApplication = ({
  memberInformationChangeApplicationId,
  reviewType,
  reviewer,
  comment,
}: {
  memberInformationChangeApplicationId: number;
  reviewType: ReviewType;
  reviewer: string;
  comment?: string;
}) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 申請の存在確認
      const application = await tx.memberInformationChangeApplication.findUnique({
        where: { memberInformationChangeApplicationId },
        include: { member: true },
      });

      if (!application) {
        throw new MemberInformationChangeApplicationNotFoundError('Application not found');
      }

      if (application.status !== MemberInformationChangeStatus.PENDING) {
        throw new InvalidStatusError('Application is not in pending status');
      }

      // 審査ログを作成
      await tx.memberInformationChangeReviewLog.create({
        data: {
          memberInformationChangeApplicationId,
          reviewer,
          reviewType,
          comment,
          timestamp: new Date(),
        },
      });

      // 申請ステータスを更新
      let newStatus: MemberInformationChangeStatus;
      switch (reviewType) {
        case ReviewType.APPROVE:
          newStatus = MemberInformationChangeStatus.APPROVED;
          break;
        case ReviewType.REJECT:
          newStatus = MemberInformationChangeStatus.REJECTED;
          break;
        default:
          throw new Error(
            `Invalid review type: ${reviewType}. Only APPROVE and REJECT are supported for member information change applications.`
          );
      }

      const updatedApplication = await tx.memberInformationChangeApplication.update({
        where: { memberInformationChangeApplicationId },
        data: { status: newStatus },
        include: {
          member: {
            include: {
              user: true,
            },
          },
          reviewLogs: {
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      });

      // 承認の場合、会員情報を更新
      if (reviewType === ReviewType.APPROVE) {
        const updateData: Prisma.MemberUpdateInput = {};
        if (application.postalCode !== null) updateData.postalCode = application.postalCode;
        if (application.prefecture !== null) updateData.prefecture = application.prefecture;
        if (application.address !== null) updateData.address = application.address;
        if (application.apartment !== null) updateData.apartment = application.apartment;
        if (application.phoneNumber !== null) updateData.phoneNumber = application.phoneNumber;

        if (Object.keys(updateData).length > 0) {
          await tx.member.update({
            where: { memberId: application.memberId },
            data: updateData,
          });
        }
      }

      return updatedApplication;
    }),
    (e) => {
      console.error('Failed to review member information change application:', e);
      if (e instanceof MemberInformationChangeApplicationNotFoundError || e instanceof InvalidStatusError) {
        return e;
      }
      return new DatabaseError('Failed to review member information change application');
    }
  );
};
