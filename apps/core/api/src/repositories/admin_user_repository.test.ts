import crypto from 'crypto';
import { AdminUserFactory } from '@core-test/factories/admin_user_factory';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import {
  findAdminUserByEmail,
  upsertAdminUserSession,
  AdminUserNotFoundError,
  AdminUserAlreadyExistsError,
  findAdminUserBySessionToken,
  listAdminUsers,
  createAdminUser,
  updateAdminUser,
} from './admin_user_repository';

describe('admin_user_repository', () => {
  describe('findAdminUserByEmail', () => {
    it('should return AdminUser when found', async () => {
      const adminUser = await AdminUserFactory.create();
      const result = await findAdminUserByEmail({ email: adminUser.email });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const foundUser = result.value;
        expect(foundUser.adminUserId).toBe(adminUser.adminUserId);
        expect(foundUser.email).toBe(adminUser.email);
        expect(foundUser.name).toBe(adminUser.name);
      }
    });

    it('should return AdminUserNotFoundError when not found', async () => {
      const result = await findAdminUserByEmail({ email: '<EMAIL>' });

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(AdminUserNotFoundError);
      }
    });
  });

  describe('findAdminUserBySessionToken', () => {
    it('should return AdminUser when session token is valid', async () => {
      const adminUser = await AdminUserFactory.create();
      const session = await AdminUserSessionFactory.create({
        adminUser: {
          connect: {
            adminUserId: adminUser.adminUserId,
          },
        },
      });

      const result = await findAdminUserBySessionToken({ sessionToken: session.sessionToken });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { user: foundUser, session: foundSession } = result.value;
        expect(foundUser.adminUserId).toBe(adminUser.adminUserId);
        expect(foundUser.email).toBe(adminUser.email);
        expect(foundSession.sessionToken).toBe(session.sessionToken);
        expect(foundSession.adminUserId).toBe(adminUser.adminUserId);
      }
    });

    it('should return AdminUserNotFoundError when session token is invalid', async () => {
      const result = await findAdminUserBySessionToken({ sessionToken: crypto.randomUUID() });

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(AdminUserNotFoundError);
      }
    });
  });

  describe('upsertAdminUserSession', () => {
    it('should create new session when none exists', async () => {
      const adminUser = await AdminUserFactory.create();
      const result = await upsertAdminUserSession({ user: adminUser });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const session = result.value;
        expect(session.adminUserId).toBe(adminUser.adminUserId);
        expect(session.sessionToken).toBeDefined();
        expect(session.expiresAt).toBeInstanceOf(Date);
      }
    });

    it('should refresh expiresAt without rotating token when session already exists', async () => {
      const adminUser = await AdminUserFactory.create();
      const existingSession = await AdminUserSessionFactory.create({
        adminUser: {
          connect: {
            adminUserId: adminUser.adminUserId,
          },
        },
      });

      const result = await upsertAdminUserSession({ user: adminUser });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const session = result.value;
        expect(session.adminUserId).toBe(adminUser.adminUserId);
        // token should be unchanged
        expect(session.sessionToken).toBe(existingSession.sessionToken);
        expect(session.expiresAt).toBeInstanceOf(Date);
      }
    });
  });

  describe('listAdminUsers', () => {
    it('should return all admin users', async () => {
      // Arrange
      const u1 = await AdminUserFactory.create({ name: 'Repo Alice' });
      const u2 = await AdminUserFactory.create({ name: 'Repo Bob' });

      // Act
      const result = await listAdminUsers();

      // Assert
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const ids = result.value.map((u) => u.adminUserId);
        expect(ids).toEqual(expect.arrayContaining([u1.adminUserId, u2.adminUserId]));
      }
    });
  });
});

describe('createAdminUser / updateAdminUser', () => {
  it('should create admin user and store hashed password', async () => {
    const email = '<EMAIL>';
    const result = await createAdminUser({ name: 'Repo New', email, password: 'Password123' });
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      expect(result.value.email).toBe(email);
      expect(result.value.name).toBe('Repo New');
    }
    // DBの保存内容を確認
    const saved = await vPrisma.client.adminUser.findUnique({ where: { email }, select: { password_hash: true, salt: true } });
    expect(saved).toBeTruthy();
    expect(saved!.password_hash).toBeDefined();
    expect(saved!.salt).toBeDefined();
    expect(saved!.password_hash).not.toBe('Password123');
  });

  it('should return AlreadyExists when email duplicated', async () => {
    const email = '<EMAIL>';
    await AdminUserFactory.create({ email });
    const result = await createAdminUser({ name: 'Dup', email, password: 'Password123' });
    expect(result.isErr()).toBe(true);
    if (result.isErr()) {
      expect(result.error).toBeInstanceOf(AdminUserAlreadyExistsError);
    }
  });

  it('should update admin user basic fields', async () => {
    const u = await AdminUserFactory.create({ name: 'Before Repo', email: '<EMAIL>' });
    const result = await updateAdminUser({ adminUserId: u.adminUserId, data: { name: 'After Repo' } });
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      expect(result.value.name).toBe('After Repo');
    }
  });

  it('should update password with new hash and salt', async () => {
    const u = await AdminUserFactory.create({});
    const before = await vPrisma.client.adminUser.findUnique({
      where: { adminUserId: u.adminUserId },
      select: { password_hash: true, salt: true },
    });
    const result = await updateAdminUser({ adminUserId: u.adminUserId, data: { password: 'NewPassword123' } });
    expect(result.isOk()).toBe(true);
    const after = await vPrisma.client.adminUser.findUnique({
      where: { adminUserId: u.adminUserId },
      select: { password_hash: true, salt: true },
    });
    expect(after!.password_hash).not.toBe('NewPassword123');
    expect(after!.password_hash).not.toBe(before!.password_hash);
    expect(after!.salt).not.toBe(before!.salt);
  });

  it('should return NotFound when updating non-existent user', async () => {
    const result = await updateAdminUser({ adminUserId: crypto.randomUUID(), data: { name: 'X' } });
    expect(result.isErr()).toBe(true);
    if (result.isErr()) {
      expect(result.error).toBeInstanceOf(AdminUserNotFoundError);
    }
  });
});
