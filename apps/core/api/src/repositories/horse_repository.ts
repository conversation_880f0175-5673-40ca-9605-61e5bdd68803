import { err, ok, ResultAsync } from 'neverthrow';
import { Prisma, PublishStatus, RecruitmentStatus } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class HorseNotFoundError extends Error {
  readonly name = 'HorseNotFoundError';
}

export class HorseAlreadyExistsError extends Error {
  readonly name = 'HorseAlreadyExistsError';
}

/**
 * 投資者情報の型定義
 */
export interface HorseInvestor {
  memberId: number;
  memberNumber: number;
  memberName: string;
  sharesNumber: number;
  investmentAmount: number;
}

/**
 * 馬と投資者情報の型定義
 */
export interface HorseWithInvestors {
  horse: {
    horseId: number;
    recruitmentYear: number;
    recruitmentNo: number;
    horseName: string;
    recruitmentName: string;
    sharesTotal: number;
    amountTotal: number;
    birthYear: number;
  };
  investors: HorseInvestor[];
}

/**
 * 馬の一覧取得（ページネーション対応）
 * @param params ページネーション、検索、フィルター条件
 * @returns 馬の一覧と総件数
 */
export const listHorses = ({ page, limit, search, birthYear }: { page: number; limit: number; search?: string; birthYear?: number }) => {
  const skip = (page - 1) * limit;

  // 検索・フィルター条件の構築
  const whereConditions: Prisma.HorseWhereInput = {
    deletedAt: null, // 論理削除されていない馬のみ
  };

  if (birthYear) {
    whereConditions.birthYear = birthYear;
  }

  if (search) {
    whereConditions.OR = [
      {
        horseName: {
          contains: search,
          mode: 'insensitive',
        },
      },
      {
        recruitmentName: {
          contains: search,
          mode: 'insensitive',
        },
      },
      {
        profile: {
          damName: {
            contains: search,
            mode: 'insensitive',
          },
        },
      },
    ];
  }

  return ResultAsync.fromPromise(
    Promise.all([
      client.horse.findMany({
        where: whereConditions,
        include: {
          profile: true,
        },
        skip,
        take: limit,
        orderBy: {
          horseId: 'desc',
        },
      }),
      client.horse.count({
        where: whereConditions,
      }),
    ]),
    () => new DatabaseError('Failed to list horses')
  ).map(([horses, totalCount]) => ({
    horses,
    totalCount,
    totalPages: Math.ceil(totalCount / limit),
  }));
};

/**
 * IDで馬を取得
 * @param horseId 馬ID
 * @returns 馬の詳細情報
 */
export const findHorseById = ({ horseId }: { horseId: number }) => {
  return ResultAsync.fromPromise(
    client.horse.findUnique({
      where: {
        horseId,
        deletedAt: null, // 論理削除されていない馬のみ
      },
      include: {
        profile: true,
      },
    }),
    () => new DatabaseError('Failed to find horse')
  ).andThen((horse) => {
    if (!horse) {
      return err(new HorseNotFoundError());
    }
    return ok(horse);
  });
};

/**
 * 新しい馬を作成
 * @param data 馬作成データ
 * @returns 作成された馬
 */
export const createHorse = (data: Prisma.HorseCreateInput) => {
  // まず重複チェックを実行（トランザクション外で実行）
  return ResultAsync.fromPromise(
    client.horse.findFirst({
      where: {
        recruitmentYear: data.recruitmentYear,
        recruitmentNo: data.recruitmentNo,
        deletedAt: null,
      },
      select: { horseId: true },
    }),
    () => new DatabaseError('Failed to check horse duplication before creation')
  ).andThen((existingHorse) => {
    // 既存の馬が見つかった場合は重複エラー
    if (existingHorse) {
      return err(new HorseAlreadyExistsError());
    }

    // 重複がない場合のみトランザクションを開始
    return ResultAsync.fromPromise(
      client.$transaction(async (tx) => {
        // 馬を作成
        const createdHorse = await tx.horse.create({
          data,
        });

        // HorseProfileを作成（horseIdのみ）
        await tx.horseProfile.create({
          data: {
            horse: { connect: { horseId: createdHorse.horseId } },
            publishStatus: PublishStatus.DRAFT,
            specialFlag: false,
            recruitmentStatus: RecruitmentStatus.UPCOMING,
          },
        });

        return createdHorse;
      }),
      (error) => {
        // Prismaの正しいエラーハンドリング
        if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
          // 稀にレースコンディションで重複が発生した場合
          return new HorseAlreadyExistsError();
        }
        // PostgreSQLトランザクションエラーも含む未知のエラー
        if (error instanceof Prisma.PrismaClientUnknownRequestError) {
          return new DatabaseError('Transaction error occurred');
        }
        return new DatabaseError('Failed to create horse');
      }
    );
  });
};

/**
 * 馬の情報を更新
 * @param horseId 馬ID
 * @param data 更新データ
 * @returns 更新された馬
 */
export const updateHorse = ({ horseId, data }: { horseId: number; data: Prisma.HorseUpdateInput }) => {
  return ResultAsync.fromPromise(
    client.horse.update({
      where: {
        horseId,
        deletedAt: null, // 論理削除されていない馬のみ更新可能
      },
      data,
    }),
    (error) => {
      // Prismaの正しいエラーハンドリング
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return new HorseNotFoundError();
      }
      return new DatabaseError('Failed to update horse');
    }
  );
};

/**
 * 馬を論理削除
 * @param horseId 馬ID
 * @returns 削除された馬
 */
export const deleteHorse = ({ horseId }: { horseId: number }) => {
  return ResultAsync.fromPromise(
    client.horse.update({
      where: {
        horseId,
        deletedAt: null, // 既に削除されていない馬のみ削除可能
      },
      data: {
        deletedAt: new Date(),
      },
      include: {
        profile: true,
      },
    }),
    (error) => {
      // Prismaの正しいエラーハンドリング
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        return new HorseNotFoundError();
      }
      return new DatabaseError('Failed to delete horse');
    }
  );
};

/**
 * 馬の存在確認（論理削除チェック含む）
 * @param horseId 馬ID
 * @returns 存在する場合true
 */
export const existsHorse = ({ horseId }: { horseId: number }) => {
  return ResultAsync.fromPromise(
    client.horse.findUnique({
      where: {
        horseId,
        deletedAt: null,
      },
      select: { horseId: true },
    }),
    () => new DatabaseError('Failed to check horse existence')
  ).map((horse) => !!horse);
};

/**
 * 募集年・番号での重複チェック
 * @param recruitmentYear 募集年
 * @param recruitmentNo 募集番号
 * @param excludeHorseId 除外する馬ID（更新時用）
 * @returns 重複している場合true
 */
export const existsHorseByRecruitment = ({
  recruitmentYear,
  recruitmentNo,
  excludeHorseId,
}: {
  recruitmentYear: number;
  recruitmentNo: number;
  excludeHorseId?: number;
}) => {
  const whereConditions: Prisma.HorseWhereInput = {
    recruitmentYear,
    recruitmentNo,
    deletedAt: null,
  };

  if (excludeHorseId) {
    whereConditions.horseId = {
      not: excludeHorseId,
    };
  }

  return ResultAsync.fromPromise(
    client.horse.findFirst({
      where: whereConditions,
      select: { horseId: true },
    }),
    () => new DatabaseError('Failed to check horse recruitment duplication')
  ).map((horse) => !!horse);
};

/**
 * 募集年度・募集番号での馬の存在チェック
 * @param recruitmentYear 募集年度
 * @param recruitmentNo 募集番号
 * @returns 馬が存在する場合true
 */
export const checkHorseExists = ({ recruitmentYear, recruitmentNo }: { recruitmentYear: number; recruitmentNo: number }) => {
  return ResultAsync.fromPromise(
    client.horse.findFirst({
      where: {
        recruitmentYear,
        recruitmentNo,
        deletedAt: null,
      },
      select: { horseId: true },
    }),
    () => new DatabaseError('Failed to check horse existence by recruitment')
  ).map((horse) => !!horse);
};

/**
 * 馬に出資している会員一覧を取得
 * @param horseId 馬ID
 * @returns 出資者の一覧（会員情報と出資情報）
 */
export const listHorseInvestors = ({ horseId }: { horseId: number }) => {
  return ResultAsync.fromPromise(
    client.investmentContract.findMany({
      where: {
        horseId,
      },
      include: {
        member: {
          select: {
            memberId: true,
            memberNumber: true,
            firstName: true,
            lastName: true,
            retirementDate: true,
          },
        },
      },
    }),
    () => new DatabaseError('Failed to list horse investors')
  ).map((contracts) => {
    // 会員ごとに集計
    const memberInvestments = new Map<
      number,
      {
        investmentContractId: number;
        memberId: number;
        memberNumber: number;
        memberName: string;
        contractedAt: Date | null;
        sharesNumber: number;
        investmentAmount: number;
        investmentAmountBeforeTax: number;
        taxRate: number;
        transactionAmount: number;
        monthlyDepreciation: number;
        retirementDate: Date | null;
      }
    >();

    contracts.forEach((contract) => {
      const memberId = contract.member.memberId;
      const memberNumber = contract.member.memberNumber;
      const memberName = `${contract.member.lastName} ${contract.member.firstName}`;

      if (memberInvestments.has(memberId)) {
        const existing = memberInvestments.get(memberId);
        if (existing) {
          existing.sharesNumber += contract.sharesNumber;
          existing.investmentAmount += contract.investmentAmount;
          existing.investmentAmountBeforeTax += contract.investmentAmountBeforeTax;
          existing.taxRate = contract.taxRate.toNumber(); // 税率は全部一緒なので、最新のものを採用
          existing.transactionAmount = contract.transactionAmount || 0;
          existing.monthlyDepreciation = contract.monthlyDepreciation || 0;
        }
      } else {
        memberInvestments.set(memberId, {
          investmentContractId: contract.investmentContractId,
          memberId,
          memberNumber,
          memberName,
          contractedAt: contract.contractedAt,
          sharesNumber: contract.sharesNumber,
          investmentAmount: contract.investmentAmount,
          investmentAmountBeforeTax: contract.investmentAmountBeforeTax,
          taxRate: contract.taxRate.toNumber(),
          transactionAmount: contract.transactionAmount || 0,
          monthlyDepreciation: contract.monthlyDepreciation || 0,
          retirementDate: contract.member.retirementDate,
        });
      }
    });

    // 出資金額の降順でソートして返却
    return Array.from(memberInvestments.values()).sort((a, b) => b.investmentAmount - a.investmentAmount);
  });
};

/**
 * 全ての馬とその出資者情報を一括取得
 * @returns 馬とその出資者情報のマップ
 */
export const listHorsesWithInvestors = (): ResultAsync<
  {
    horses: HorseWithInvestors['horse'][];
    horsesWithInvestors: Map<number, HorseWithInvestors>;
  },
  DatabaseError
> => {
  return ResultAsync.fromPromise(
    client.horse.findMany({
      where: {
        deletedAt: null, // 論理削除されていない馬のみ
      },
      include: {
        investmentContracts: {
          where: {
            contractStatus: 'COMPLETED', // 完了した出資契約のみ
          },
          include: {
            member: true, // 会員情報も含める
          },
        },
      },
      orderBy: [{ recruitmentYear: 'desc' }, { recruitmentNo: 'asc' }],
    }),
    (error) => new DatabaseError(`Failed to fetch horses with investors: ${error}`)
  ).map((horsesWithContracts) => {
    // 馬ごとに出資者情報をマップ形式で整理
    const horsesMap = new Map<number, HorseWithInvestors>();

    for (const horse of horsesWithContracts) {
      // 会員ごとの出資情報を集計
      const memberInvestments = new Map<number, HorseInvestor>();

      for (const contract of horse.investmentContracts) {
        const memberId = contract.member.memberId;
        const existingInvestment = memberInvestments.get(memberId);

        if (existingInvestment) {
          // 既存の出資情報に追加
          existingInvestment.sharesNumber += contract.sharesNumber;
          existingInvestment.investmentAmount += contract.investmentAmount;
        } else {
          // 新規の出資情報を作成
          memberInvestments.set(memberId, {
            memberId: contract.member.memberId,
            memberNumber: contract.member.memberNumber,
            memberName: `${contract.member.lastName} ${contract.member.firstName}`,
            sharesNumber: contract.sharesNumber,
            investmentAmount: contract.investmentAmount,
          });
        }
      }

      // 馬情報と出資者情報をマップに格納
      horsesMap.set(horse.horseId, {
        horse: {
          horseId: horse.horseId,
          recruitmentYear: horse.recruitmentYear,
          recruitmentNo: horse.recruitmentNo,
          horseName: horse.horseName,
          recruitmentName: horse.recruitmentName,
          sharesTotal: horse.sharesTotal,
          amountTotal: horse.amountTotal,
          birthYear: horse.birthYear,
        },
        investors: Array.from(memberInvestments.values()).sort((a, b) => b.investmentAmount - a.investmentAmount),
      });
    }

    return {
      horses: Array.from(horsesMap.values()).map((item) => item.horse),
      horsesWithInvestors: horsesMap,
    };
  });
};
