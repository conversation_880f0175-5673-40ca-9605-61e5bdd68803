import { updateInvestmentContractMonthlyDepreciationAndTransactionAmount } from "./investment_contract_repository";
import { InvestmentContractFactory, HorseFactory, MemberFactory } from "../../test_utils";

describe("investment_contract_repository", () => {
    let testHorseId: number;
    let testMemberId: number;

    beforeEach(async () => {
        // テスト用のデータを作成
        const horse = await HorseFactory.create();
        const member = await MemberFactory.create();
        await InvestmentContractFactory.create({
            horse: { connect: { horseId: horse.horseId } },
            member: { connect: { memberId: member.memberId } },
        });

        testHorseId = horse.horseId;
        testMemberId = member.memberId;
    });

    describe("updateInvestmentContractMonthlyDepreciationAndTransactionAmount", () => {
        it("monthlyDepreciationとtransactionAmountが正常に更新されること", async () => {
            // Arrange
            const monthlyDepreciation = 50000;
            const transactionAmount = 2400000;

            // Act
            const result = await updateInvestmentContractMonthlyDepreciationAndTransactionAmount(
                testHorseId,
                testMemberId,
                monthlyDepreciation,
                transactionAmount
            );

            // Assert
            expect(result.count).toBeGreaterThan(0);

            // データベースから直接確認
            const updatedContract = await vPrisma.client.investmentContract.findFirst({
                where: { horseId: testHorseId, memberId: testMemberId },
            });

            expect(updatedContract).not.toBeNull();
            expect(updatedContract!.monthlyDepreciation).toBe(monthlyDepreciation);
            expect(updatedContract!.transactionAmount).toBe(transactionAmount);
        });

        it("ゼロ値が正しく処理されること", async () => {
            // Arrange
            const monthlyDepreciation = 0;
            const transactionAmount = 0;

            // Act
            const result = await updateInvestmentContractMonthlyDepreciationAndTransactionAmount(
                testHorseId,
                testMemberId,
                monthlyDepreciation,
                transactionAmount
            );

            // Assert
            expect(result.count).toBeGreaterThan(0);

            const updatedContract = await vPrisma.client.investmentContract.findFirst({
                where: { horseId: testHorseId, memberId: testMemberId },
            });
            expect(updatedContract!.monthlyDepreciation).toBe(0);
            expect(updatedContract!.transactionAmount).toBe(0);
        });

        it("負の値が正しく処理されること", async () => {
            // Arrange
            const monthlyDepreciation = -10000;
            const transactionAmount = -100000;

            // Act
            const result = await updateInvestmentContractMonthlyDepreciationAndTransactionAmount(
                testHorseId,
                testMemberId,
                monthlyDepreciation,
                transactionAmount
            );

            // Assert
            expect(result.count).toBeGreaterThan(0);

            const updatedContract = await vPrisma.client.investmentContract.findFirst({
                where: { horseId: testHorseId, memberId: testMemberId },
            });
            expect(updatedContract!.monthlyDepreciation).toBe(monthlyDepreciation);
            expect(updatedContract!.transactionAmount).toBe(transactionAmount);
        });

        it("存在しないhorseIdとmemberIdの組み合わせを指定した場合に更新されないこと", async () => {
            // Arrange
            const nonExistentHorseId = 999999;
            const nonExistentMemberId = 999999;
            const monthlyDepreciation = 50000;
            const transactionAmount = 2400000;

            // Act
            const result = await updateInvestmentContractMonthlyDepreciationAndTransactionAmount(
                nonExistentHorseId,
                nonExistentMemberId,
                monthlyDepreciation,
                transactionAmount
            );

            // Assert
            expect(result.count).toBe(0);
        });

        it("指定したフィールドのみが更新され、他のフィールドに影響しないこと", async () => {
            // Arrange
            const originalContract = await vPrisma.client.investmentContract.findFirst({
                where: { horseId: testHorseId, memberId: testMemberId },
            });

            const monthlyDepreciation = 75000;
            const transactionAmount = 3600000;

            // Act
            const result = await updateInvestmentContractMonthlyDepreciationAndTransactionAmount(
                testHorseId,
                testMemberId,
                monthlyDepreciation,
                transactionAmount
            );

            // Assert
            expect(result.count).toBeGreaterThan(0);

            const updatedContract = await vPrisma.client.investmentContract.findFirst({
                where: { horseId: testHorseId, memberId: testMemberId },
            });

            expect(updatedContract!.monthlyDepreciation).toBe(monthlyDepreciation);
            expect(updatedContract!.transactionAmount).toBe(transactionAmount);
            
            // 他のフィールドが変更されていないことを確認
            expect(updatedContract!.memberId).toBe(originalContract!.memberId);
            expect(updatedContract!.horseId).toBe(originalContract!.horseId);
            expect(updatedContract!.createdAt).toEqual(originalContract!.createdAt);
        });

        it("同じhorseIdとmemberIdの組み合わせを持つ複数のレコードがすべて更新されること", async () => {
            // Arrange
            const member = await MemberFactory.create();
            const horse = await HorseFactory.create();
            
            // 同じhorseIdとmemberIdで複数のInvestmentContractを作成
            const contract1 = await InvestmentContractFactory.create({
                horse: { connect: { horseId: horse.horseId } },
                member: { connect: { memberId: member.memberId } },
                monthlyDepreciation: 10000,
                transactionAmount: 500000,
            });
            
            const contract2 = await InvestmentContractFactory.create({
                horse: { connect: { horseId: horse.horseId } },
                member: { connect: { memberId: member.memberId } },
                monthlyDepreciation: 20000,
                transactionAmount: 1000000,
            });

            const monthlyDepreciation = 75000;
            const transactionAmount = 3600000;

            // Act
            const result = await updateInvestmentContractMonthlyDepreciationAndTransactionAmount(
                horse.horseId,
                member.memberId,
                monthlyDepreciation,
                transactionAmount
            );

            // Assert
            expect(result.count).toBe(2); // 2つのレコードが更新される

            // 両方のレコードが更新されていることを確認
            const updatedContracts = await vPrisma.client.investmentContract.findMany({
                where: { horseId: horse.horseId, memberId: member.memberId },
            });

            expect(updatedContracts).toHaveLength(2);
            updatedContracts.forEach(contract => {
                expect(contract.monthlyDepreciation).toBe(monthlyDepreciation);
                expect(contract.transactionAmount).toBe(transactionAmount);
            });
        });
    });
});
