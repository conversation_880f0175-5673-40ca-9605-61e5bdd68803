import { client } from "@core-api/utils/prisma";    

// investmentContractIdを使ってInvestmentContractのmonthlyDepreciationとtransactionAmountを更新する
export function updateInvestmentContractMonthlyDepreciationAndTransactionAmount(horseId: number, memberId: number, monthlyDepreciation: number, transactionAmount: number) {
    return client.investmentContract.updateMany({
        where: { horseId: horseId, memberId: memberId },
        data: { monthlyDepreciation: monthlyDepreciation, transactionAmount: transactionAmount },
    });
}   