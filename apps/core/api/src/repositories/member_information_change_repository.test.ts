import { MemberFactory } from '@core-test/factories/member_factory';
import { MemberInformationChangeStatus, ReviewType } from '@hami/prisma';
import { DatabaseError } from './index';
import {
  createMemberInformationChangeApplication,
  reviewMemberInformationChangeApplication,
} from './member_information_change_repository';

describe('member_information_change_repository', () => {
  describe('createMemberInformationChangeApplication', () => {
    it('新規申請を作成できる', async () => {
      const member = await MemberFactory.create();
      const requestedChangeDate = new Date('2024-12-31');

      const result = await createMemberInformationChangeApplication({
        memberId: member.memberId,
        postalCode: '100-0001',
        prefecture: '東京都',
        address: '千代田区千代田1-1',
        apartment: 'マンション101',
        phoneNumber: '03-1234-5678',
        requestedChangeDate,
        reason: '引越しのため',
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const application = result.value;
        expect(application.memberId).toBe(member.memberId);
        expect(application.postalCode).toBe('100-0001');
        expect(application.prefecture).toBe('東京都');
        expect(application.address).toBe('千代田区千代田1-1');
        expect(application.apartment).toBe('マンション101');
        expect(application.phoneNumber).toBe('03-1234-5678');
        expect(application.requestedChangeDate).toEqual(requestedChangeDate);
        expect(application.reason).toBe('引越しのため');
        expect(application.status).toBe(MemberInformationChangeStatus.PENDING);
      }
    });

    it('新規申請作成時に古い未処理申請をCANCELLED状態に変更する', async () => {
      const member = await MemberFactory.create();
      const requestedChangeDate = new Date('2024-12-31');

      // 最初の申請を作成
      const firstResult = await createMemberInformationChangeApplication({
        memberId: member.memberId,
        postalCode: '100-0001',
        prefecture: '東京都',
        address: '千代田区千代田1-1',
        requestedChangeDate,
      });
      expect(firstResult.isOk()).toBe(true);

      // 2番目の申請を作成
      const secondResult = await createMemberInformationChangeApplication({
        memberId: member.memberId,
        postalCode: '200-0002',
        prefecture: '神奈川県',
        address: '横浜市西区みなとみらい2-2',
        requestedChangeDate,
      });
      expect(secondResult.isOk()).toBe(true);

      // 最初の申請がCANCELLED状態になっていることを確認
      if (firstResult.isOk()) {
        const firstApplication = await vPrisma.client.memberInformationChangeApplication.findUnique({
          where: { memberInformationChangeApplicationId: firstResult.value.memberInformationChangeApplicationId },
        });
        expect(firstApplication?.status).toBe(MemberInformationChangeStatus.CANCELLED);
      }

      // 2番目の申請がPENDING状態であることを確認
      if (secondResult.isOk()) {
        expect(secondResult.value.status).toBe(MemberInformationChangeStatus.PENDING);
      }
    });

    it('部分的な変更申請を作成できる', async () => {
      const member = await MemberFactory.create();
      const requestedChangeDate = new Date('2024-12-31');

      const result = await createMemberInformationChangeApplication({
        memberId: member.memberId,
        phoneNumber: '03-9999-9999', // 電話番号のみ変更
        requestedChangeDate,
        reason: '電話番号変更のため',
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const application = result.value;
        expect(application.phoneNumber).toBe('03-9999-9999');
        expect(application.postalCode).toBeNull();
        expect(application.prefecture).toBeNull();
        expect(application.address).toBeNull();
        expect(application.apartment).toBeNull();
      }
    });
  });

  describe('reviewMemberInformationChangeApplication', () => {
    it('申請を承認できる', async () => {
      const member = await MemberFactory.create();
      const requestedChangeDate = new Date('2024-12-31');

      const createResult = await createMemberInformationChangeApplication({
        memberId: member.memberId,
        postalCode: '100-0001',
        prefecture: '東京都',
        phoneNumber: '03-1234-5678',
        requestedChangeDate,
      });
      expect(createResult.isOk()).toBe(true);
      if (!createResult.isOk()) return;

      const result = await reviewMemberInformationChangeApplication({
        memberInformationChangeApplicationId: createResult.value.memberInformationChangeApplicationId,
        reviewType: ReviewType.APPROVE,
        reviewer: 'admin-001',
        comment: '承認します',
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const application = result.value;
        expect(application.status).toBe(MemberInformationChangeStatus.APPROVED);
        expect(application.reviewLogs).toHaveLength(1);
        expect(application.reviewLogs[0].reviewType).toBe(ReviewType.APPROVE);
        expect(application.reviewLogs[0].reviewer).toBe('admin-001');
        expect(application.reviewLogs[0].comment).toBe('承認します');
      }

      // 会員情報が更新されていることを確認
      const updatedMember = await vPrisma.client.member.findUnique({
        where: { memberId: member.memberId },
      });
      expect(updatedMember?.postalCode).toBe('100-0001');
      expect(updatedMember?.prefecture).toBe('東京都');
      expect(updatedMember?.phoneNumber).toBe('03-1234-5678');
    });

    it('申請を拒否できる', async () => {
      const member = await MemberFactory.create();
      const requestedChangeDate = new Date('2024-12-31');

      const createResult = await createMemberInformationChangeApplication({
        memberId: member.memberId,
        postalCode: '100-0001',
        requestedChangeDate,
      });
      expect(createResult.isOk()).toBe(true);
      if (!createResult.isOk()) return;

      const result = await reviewMemberInformationChangeApplication({
        memberInformationChangeApplicationId: createResult.value.memberInformationChangeApplicationId,
        reviewType: ReviewType.REJECT,
        reviewer: 'admin-001',
        comment: '情報が不正確です',
      });

      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const application = result.value;
        expect(application.status).toBe(MemberInformationChangeStatus.REJECTED);
        expect(application.reviewLogs[0].reviewType).toBe(ReviewType.REJECT);
      }

      // 会員情報は更新されていないことを確認
      const updatedMember = await vPrisma.client.member.findUnique({
        where: { memberId: member.memberId },
      });
      expect(updatedMember?.postalCode).toBe(member.postalCode); // 元の値のまま
    });

    it('無効なレビュータイプの場合はエラーを投げる', async () => {
      const member = await MemberFactory.create();
      const requestedChangeDate = new Date('2024-12-31');

      const createResult = await createMemberInformationChangeApplication({
        memberId: member.memberId,
        postalCode: '100-0001',
        requestedChangeDate,
      });
      expect(createResult.isOk()).toBe(true);
      if (!createResult.isOk()) return;

      const result = await reviewMemberInformationChangeApplication({
        memberInformationChangeApplicationId: createResult.value.memberInformationChangeApplicationId,
        reviewType: ReviewType.REMAND, // REMANDは会員情報変更では無効
        reviewer: 'admin-001',
        comment: 'テスト',
      });

      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        // エラーはDatabaseErrorでラップされるため、DatabaseErrorが返されることを確認
        expect(result.error).toBeInstanceOf(DatabaseError);
        expect(result.error.message).toBe('Failed to review member information change application');
      }
    });
  });
});
