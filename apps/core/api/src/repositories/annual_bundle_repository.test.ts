import { AnnualBundleFactory } from '@core-test/factories/annual_bundle_factory';
import { AnnualBundleHorseFactory } from '@core-test/factories/annual_bundle_horse_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { createAnnualBundle, findAnnualBundleById, listAnnualBundleHorsesCandidates, updateAnnualBundle } from './annual_bundle_repository';

describe('annual_bundle_repository', () => {
  it('年度バンドルを作成し取得できる', async () => {
    const bundleResult = await createAnnualBundle({
      fiscalYear: 2025,
      name: 'テストバンドル',
      shares: 500,
      publishStatus: 'PRIVATE',
      recruitmentStatus: 'UPCOMING',
      horseIds: [],
    });
    expect(bundleResult.isOk()).toBe(true);
    const bundle = bundleResult._unsafeUnwrap();

    const found = await findAnnualBundleById({ annualBundleId: bundle.annualBundleId });
    expect(found.isOk()).toBe(true);
    const b = found._unsafeUnwrap();
    expect(b.fiscalYear).toBe(2025);
    expect(b.name).toBe('テストバンドル');
  });

  it('候補一覧で同年度の馬のみ、選択状態が付与される', async () => {
    const bundle = await AnnualBundleFactory.create({ fiscalYear: 2030 });
    const horseA = await HorseFactory.create({ recruitmentYear: 2030, recruitmentNo: 1 });
    const horseB = await HorseFactory.create({ recruitmentYear: 2030, recruitmentNo: 2 });
    const horseC = await HorseFactory.create({ recruitmentYear: 2031, recruitmentNo: 1 }); // 異年度

    // Aを選択状態にする
    await AnnualBundleHorseFactory.create({ annualBundle: { connect: { annualBundleId: bundle.annualBundleId } }, horse: { connect: { horseId: horseA.horseId } } });

    const result = await listAnnualBundleHorsesCandidates({ annualBundleId: bundle.annualBundleId, page: 1, limit: 50 });
    expect(result.isOk()).toBe(true);
    const { horses, selectedSet } = result._unsafeUnwrap();
    const ids = horses.map((h) => h.horseId);
    expect(ids).toContain(horseA.horseId);
    expect(ids).toContain(horseB.horseId);
    expect(ids).not.toContain(horseC.horseId);
    expect(selectedSet.has(horseA.horseId)).toBe(true);
    expect(selectedSet.has(horseB.horseId)).toBe(false);
  });

  it('紐付けの全置換更新ができる', async () => {
    const bundle = await AnnualBundleFactory.create({ fiscalYear: 2040 });
    const horse1 = await HorseFactory.create({ recruitmentYear: 2040, recruitmentNo: 10 });
    const horse2 = await HorseFactory.create({ recruitmentYear: 2040, recruitmentNo: 11 });
    const horse3 = await HorseFactory.create({ recruitmentYear: 2040, recruitmentNo: 12 });

    await AnnualBundleHorseFactory.createList([
      { annualBundle: { connect: { annualBundleId: bundle.annualBundleId } }, horse: { connect: { horseId: horse1.horseId } } },
      { annualBundle: { connect: { annualBundleId: bundle.annualBundleId } }, horse: { connect: { horseId: horse2.horseId } } },
    ]);

    const updated = await updateAnnualBundle({ annualBundleId: bundle.annualBundleId, horseIds: [horse2.horseId, horse3.horseId] });
    expect(updated.isOk()).toBe(true);
    const b = updated._unsafeUnwrap();
    const linkedIds = (b?.horses ?? []).map((h) => h.horseId);
    expect(linkedIds.sort()).toEqual([horse2.horseId, horse3.horseId].sort());
  });
});


