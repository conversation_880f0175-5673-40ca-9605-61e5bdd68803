import { err, ok, ResultAsync } from 'neverthrow';
import { BankAccountRegistrationStatus, ApprovalType, Prisma } from '@hami/prisma';
import { BankAccountApprovalStatus } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class BankAccountRegistrationNotFoundError extends Error {
  readonly name = 'BankAccountRegistrationNotFoundError';
}

export class BankAccountRegistrationAlreadyProcessedError extends Error {
  readonly name = 'BankAccountRegistrationAlreadyProcessedError';
}

export class AdminUserNotFoundError extends Error {
  readonly name = 'AdminUserNotFoundError';
}

/**
 * 承認待ち口座登録一覧を取得
 * @param params ページネーション設定
 * @returns 承認待ち口座登録の一覧と総件数
 */
export const listPendingBankAccountRegistrations = ({ page, pageSize }: { page: number; pageSize: number }) => {
  const skip = (page - 1) * pageSize;

  return ResultAsync.fromPromise(
    (async () => {
      // 承認待ちの条件: SUCCESS状態で、まだ承認されていない
      const whereCondition: Prisma.BankAccountRegistrationWhereInput = {
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        approvals: {
          none: {}, // 承認レコードが存在しない
        },
      };

      const [registrations, totalCount] = await Promise.all([
        client.bankAccountRegistration.findMany({
          where: whereCondition,
          include: {
            member: {
              include: {
                user: true,
              },
            },
          },
          orderBy: {
            completedAt: 'asc', // 完了日時の古い順
          },
          skip,
          take: pageSize,
        }),
        client.bankAccountRegistration.count({
          where: whereCondition,
        }),
      ]);

      return { registrations, totalCount };
    })(),
    (e) => {
      console.error('Failed to list pending bank account registrations:', e);
      return new DatabaseError('Failed to list pending bank account registrations');
    }
  );
};

/**
 * 全口座登録一覧を取得（承認済み・却下済み含む）
 * @param params ページネーション設定とフィルタ
 * @returns 全口座登録の一覧と総件数
 */
export const listAllBankAccountRegistrations = ({
  page,
  pageSize,
  statusFilter,
}: {
  page: number;
  pageSize: number;
  statusFilter?: BankAccountApprovalStatus;
}) => {
  const skip = (page - 1) * pageSize;

  return ResultAsync.fromPromise(
    (async () => {
      // 基本条件: SUCCESS状態のもの
      let whereCondition: Prisma.BankAccountRegistrationWhereInput = {
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
      };

      // ステータスフィルタがある場合
      if (statusFilter !== undefined) {
        if (statusFilter === BankAccountApprovalStatus.PENDING) {
          // 承認待ち: 承認レコードが存在しない
          whereCondition.approvals = { none: {} };
        } else if (statusFilter === BankAccountApprovalStatus.APPROVED) {
          // 承認済み: 承認レコードが存在し、最新が承認
          whereCondition.approvals = {
            some: {
              approvalType: ApprovalType.APPROVE,
            },
          };
        } else if (statusFilter === BankAccountApprovalStatus.REJECTED) {
          // 却下済み: 承認レコードが存在し、最新が却下
          whereCondition.approvals = {
            some: {
              approvalType: ApprovalType.REJECT,
            },
          };
        }
      }

      const [registrations, totalCount] = await Promise.all([
        client.bankAccountRegistration.findMany({
          where: whereCondition,
          include: {
            member: {
              include: {
                user: true,
              },
            },
            approvals: {
              include: {
                adminUser: true,
              },
              orderBy: {
                createdAt: 'desc',
              },
              take: 1, // 最新の承認レコードのみ
            },
          },
          orderBy: {
            completedAt: 'desc', // 完了日時の新しい順
          },
          skip,
          take: pageSize,
        }),
        client.bankAccountRegistration.count({
          where: whereCondition,
        }),
      ]);

      // 承認状況を判定してレスポンス用データに変換
      const registrationsWithStatus = registrations.map((registration) => {
        let approvalStatus = BankAccountApprovalStatus.PENDING;
        let approvedAt: Date | null = null;
        let adminUserName = '';

        if (registration.approvals.length > 0) {
          const latestApproval = registration.approvals[0];
          approvalStatus =
            latestApproval.approvalType === ApprovalType.APPROVE ? BankAccountApprovalStatus.APPROVED : BankAccountApprovalStatus.REJECTED;
          approvedAt = latestApproval.createdAt;
          adminUserName = latestApproval.adminUser.name;
        }

        return {
          ...registration,
          approvalStatus,
          approvedAt,
          adminUserName,
        };
      });

      return {
        registrations: registrationsWithStatus,
        totalCount,
      };
    })(),
    (e) => {
      console.error('Failed to list all bank account registrations:', e);
      return new DatabaseError('Failed to list all bank account registrations');
    }
  );
};

/**
 * 口座登録詳細を取得（承認履歴含む）
 * @param bankAccountRegistrationId 口座登録申請ID
 * @returns 口座登録詳細情報
 */
export const getBankAccountRegistrationDetail = ({ bankAccountRegistrationId }: { bankAccountRegistrationId: number }) => {
  return ResultAsync.fromPromise(
    client.bankAccountRegistration.findUnique({
      where: { bankAccountRegistrationId },
      include: {
        member: {
          include: {
            user: true,
          },
        },
        approvals: {
          include: {
            adminUser: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    }),
    (e) => {
      console.error('Failed to get bank account registration detail:', e);
      return new DatabaseError('Failed to get bank account registration detail');
    }
  ).andThen((registration) => {
    if (!registration) {
      return err(new BankAccountRegistrationNotFoundError());
    }
    return ok(registration);
  });
};

/**
 * 口座登録を承認
 * @param params 承認パラメータ
 * @returns 承認結果
 */
export const approveBankAccountRegistration = ({
  bankAccountRegistrationId,
  adminUserId,
  comment,
}: {
  bankAccountRegistrationId: number;
  adminUserId: string;
  comment?: string;
}) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 口座登録申請の存在確認
      const registration = await tx.bankAccountRegistration.findUnique({
        where: { bankAccountRegistrationId },
        include: {
          approvals: true,
        },
      });

      if (!registration) {
        throw new BankAccountRegistrationNotFoundError();
      }

      // 既に承認・却下されているかチェック
      if (registration.approvals.length > 0) {
        throw new BankAccountRegistrationAlreadyProcessedError();
      }

      // SUCCESS状態でない場合はエラー
      if (registration.registrationStatus !== BankAccountRegistrationStatus.SUCCESS) {
        throw new Error('口座登録が完了していません');
      }

      // 管理者ユーザーの存在確認
      const adminUser = await tx.adminUser.findUnique({
        where: { adminUserId },
      });

      if (!adminUser) {
        throw new AdminUserNotFoundError();
      }

      // 承認レコードを作成
      const approval = await tx.bankAccountRegistrationApproval.create({
        data: {
          bankAccountRegistrationId,
          approvalType: ApprovalType.APPROVE,
          adminUserId,
          comment,
        },
        include: {
          adminUser: true,
        },
      });

      return approval;
    }),
    (e) => {
      console.error('Failed to approve bank account registration:', e);
      if (
        e instanceof BankAccountRegistrationNotFoundError ||
        e instanceof BankAccountRegistrationAlreadyProcessedError ||
        e instanceof AdminUserNotFoundError
      ) {
        return e;
      }
      return new DatabaseError('Failed to approve bank account registration');
    }
  );
};

/**
 * 口座登録を却下
 * @param params 却下パラメータ
 * @returns 却下結果
 */
export const rejectBankAccountRegistration = ({
  bankAccountRegistrationId,
  adminUserId,
  rejectionReason,
}: {
  bankAccountRegistrationId: number;
  adminUserId: string;
  rejectionReason: string;
}) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 口座登録申請の存在確認
      const registration = await tx.bankAccountRegistration.findUnique({
        where: { bankAccountRegistrationId },
        include: {
          approvals: true,
        },
      });

      if (!registration) {
        throw new BankAccountRegistrationNotFoundError();
      }

      // 既に承認・却下されているかチェック
      if (registration.approvals.length > 0) {
        throw new BankAccountRegistrationAlreadyProcessedError();
      }

      // SUCCESS状態でない場合はエラー
      if (registration.registrationStatus !== BankAccountRegistrationStatus.SUCCESS) {
        throw new Error('口座登録が完了していません');
      }

      // 管理者ユーザーの存在確認
      const adminUser = await tx.adminUser.findUnique({
        where: { adminUserId },
      });

      if (!adminUser) {
        throw new AdminUserNotFoundError();
      }

      // 却下レコードを作成
      const approval = await tx.bankAccountRegistrationApproval.create({
        data: {
          bankAccountRegistrationId,
          approvalType: ApprovalType.REJECT,
          adminUserId,
          rejectionReason,
        },
        include: {
          adminUser: true,
        },
      });

      return approval;
    }),
    (e) => {
      console.error('Failed to reject bank account registration:', e);
      if (
        e instanceof BankAccountRegistrationNotFoundError ||
        e instanceof BankAccountRegistrationAlreadyProcessedError ||
        e instanceof AdminUserNotFoundError
      ) {
        return e;
      }
      return new DatabaseError('Failed to reject bank account registration');
    }
  );
};
