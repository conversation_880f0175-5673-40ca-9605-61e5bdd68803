import crypto from 'node:crypto';
import { err, ok, ResultAsync } from 'neverthrow';
import { MailVerification } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class MailVerificationNotFoundError extends Error {
  readonly name = 'MailVerificationNotFound';
}
export class MailVerificationExpiredError extends Error {
  readonly name = 'MailVerificationExpired';
}

/**
 * 提供されたトークンに一致するMailVerificationレコードを検索します。
 * @param token 検索する認証トークン。
 * @returns 見つかった場合はMailVerificationレコードを含むResultAsync、
 *          見つからない場合やDBエラーの場合は対応する MailVerificationError を含むResultAsync。
 */
export const findMailVerificationRecord = ({ token }: { token: string }) => {
  return ResultAsync.fromPromise(
    client.mailVerification.findFirst({ where: { token } }),
    () => new DatabaseError('Failed to query mail verification')
  ).andThen((record) => {
    if (!record) {
      return err(new MailVerificationNotFoundError());
    }
    return ok(record);
  });
};

/**
 * MailVerificationレコードを認証済みとしてマークします。
 * すでに認証済みの場合は何もしません。
 * @param record 認証するMailVerificationレコード。
 * @returns 認証済み（または元々認証済みだった）MailVerificationレコードを含むResultAsync、
 *          DBエラーの場合は DatabaseError を含むResultAsync。
 */
export const markAsVerifiedMailVerificationRecord = (record: MailVerification) => {
  if (record.verifiedAt) {
    return ok(record);
  }

  return ResultAsync.fromPromise(
    client.mailVerification.update({
      where: { mailVerificationId: record.mailVerificationId },
      data: { verifiedAt: new Date() },
    }),
    () => new DatabaseError('Failed to update mail verification')
  );
};

/**
 * 提供されたトークンに一致し、かつ有効期限内のMailVerificationレコードを検索します。
 * @param token 検索する認証トークン。
 * @returns 見つかり、かつ有効な場合はMailVerificationレコードを含むResultAsync、
 *          見つからない、期限切れ、またはDBエラーの場合は対応する MailVerificationError を含むResultAsync。
 */
export const findValidMailVerificationRecordByToken = ({ token }: { token: string }) => {
  return ResultAsync.fromPromise(
    client.mailVerification.findFirst({ where: { token } }),
    () => new DatabaseError('Failed to query valid mail verification')
  ).andThen((record) => {
    if (!record) {
      return err(new MailVerificationNotFoundError());
    }
    if (!record.verifiedAt && record.expiresAt < new Date()) {
      return err(new MailVerificationExpiredError());
    }
    return ok(record);
  });
};

/**
 * 提供されたIDに一致し、かつ有効期限内のMailVerificationレコードを検索します。
 * @param id 検索するID。
 * @returns 見つかり、かつ有効な場合はMailVerificationレコードを含むResultAsync、
 *          見つからない、期限切れ、またはDBエラーの場合は対応する MailVerificationError を含むResultAsync。
 */
export const findVerifiedMailVerificationRecordById = ({ mailVerificationId }: { mailVerificationId: number }) => {
  return ResultAsync.fromPromise(
    client.mailVerification.findFirst({ where: { mailVerificationId } }),
    () => new DatabaseError('Failed to query valid mail verification')
  ).andThen((record) => {
    if (!record) {
      return err(new MailVerificationNotFoundError());
    }
    if (!record.verifiedAt) {
      return err(new MailVerificationNotFoundError());
    }
    return ok(record);
  });
};

/**
 * 新しいメール認証レコードを作成し、認証メールを送信します。
 * @param email 認証するメールアドレス。
 * @returns 成功した場合は ok(void)、DBエラーまたはメール送信エラーの場合は対応するエラーを含む ResultAsync。
 */
export const createMailVerification = (email: string, expiresAt: Date): ResultAsync<MailVerification, DatabaseError> => {
  const token = crypto.randomBytes(32).toString('hex');

  return ResultAsync.fromPromise(
    client.mailVerification.create({
      data: { email, token, expiresAt },
    }),
    () => new DatabaseError('Failed to create mail verification record')
  );
};
