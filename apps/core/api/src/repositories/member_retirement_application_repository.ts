import { err, ok, Result } from 'neverthrow';
import { MemberRetirementApplication, Member } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';

export type ListMemberRetirementApplicationsError = {
  type: 'DATABASE_ERROR';
  message: string;
};

export type GetMemberRetirementApplicationError = {
  type: 'DATABASE_ERROR';
  message: string;
} | {
  type: 'NOT_FOUND';
  message: string;
};

export type ApproveMemberRetirementApplicationError = {
  type: 'DATABASE_ERROR';
  message: string;
} | {
  type: 'NOT_FOUND';
  message: string;
} | {
  type: 'ALREADY_PROCESSED';
  message: string;
};

export type RejectMemberRetirementApplicationError = {
  type: 'DATABASE_ERROR';
  message: string;
} | {
  type: 'NOT_FOUND';
  message: string;
} | {
  type: 'ALREADY_PROCESSED';
  message: string;
};

export type MemberRetirementApplicationWithMember = MemberRetirementApplication & {
  member: Member;
};

export const listMemberRetirementApplications = async (
  includeApproved: boolean = false,
  includeRejected: boolean = false
): Promise<Result<MemberRetirementApplicationWithMember[], ListMemberRetirementApplicationsError>> => {
  try {
    // フィルタリング条件を構築
    const whereConditions: {
      AND?: Array<{ approvedAt: null } | { rejectedAt: null }>;
      OR?: Array<{ approvedAt: null; rejectedAt: null } | { approvedAt: { not: null } } | { rejectedAt: { not: null } }>;
    } = {};

    if (!includeApproved && !includeRejected) {
      // 両方ともfalseの場合は、未処理（承認も拒否もされていない）の申請のみ
      whereConditions.AND = [
        { approvedAt: null },
        { rejectedAt: null },
      ];
    } else if (includeApproved && !includeRejected) {
      // 未処理 + 承認済みを含める
      whereConditions.OR = [
        { approvedAt: null, rejectedAt: null },
        { approvedAt: { not: null } },
      ];
    } else if (!includeApproved && includeRejected) {
      // 未処理 + 拒否済みを含める
      whereConditions.OR = [
        { approvedAt: null, rejectedAt: null },
        { rejectedAt: { not: null } },
      ];
    } else {
      // 両方ともtrueの場合は、すべての申請を取得（whereConditionsは空）
    }

    const applications = await client.memberRetirementApplication.findMany({
      where: whereConditions,
      include: {
        member: true,
      },
      orderBy: {
        applicationDate: 'desc',
      },
    });
    return ok(applications);
  } catch (error) {
    return err({
      type: 'DATABASE_ERROR',
      message: error instanceof Error ? error.message : 'Unknown database error',
    });
  }
};

export const getMemberRetirementApplication = async (
  memberRetirementApplicationId: number
): Promise<Result<MemberRetirementApplicationWithMember, GetMemberRetirementApplicationError>> => {
  try {
    const application = await client.memberRetirementApplication.findUnique({
      where: {
        memberRetirementApplicationId,
      },
      include: {
        member: true,
      },
    });

    if (!application) {
      return err({
        type: 'NOT_FOUND',
        message: `Member retirement application with ID ${memberRetirementApplicationId} not found`,
      });
    }

    return ok(application);
  } catch (error) {
    return err({
      type: 'DATABASE_ERROR',
      message: error instanceof Error ? error.message : 'Unknown database error',
    });
  }
};

export const approveMemberRetirementApplication = async (
  memberRetirementApplicationId: number
): Promise<Result<void, ApproveMemberRetirementApplicationError>> => {
  try {
    // 退会申請が存在するかチェック
    const application = await client.memberRetirementApplication.findUnique({
      where: {
        memberRetirementApplicationId,
      },
      include: {
        member: true,
      },
    });

    if (!application) {
      return err({
        type: 'NOT_FOUND',
        message: `Member retirement application with ID ${memberRetirementApplicationId} not found`,
      });
    }

    // 既に処理済みかチェック
    if (application.approvedAt || application.rejectedAt) {
      return err({
        type: 'ALREADY_PROCESSED',
        message: `Member retirement application with ID ${memberRetirementApplicationId} is already processed`,
      });
    }

    // 翌月の末の日付を計算
    const applicationDate = application.applicationDate;
    const nextMonth = new Date(applicationDate.getFullYear(), applicationDate.getMonth() + 1, 1);
    const lastDayOfNextMonth = new Date(nextMonth.getFullYear(), nextMonth.getMonth() + 1, 0);

    // トランザクションで退会申請を承認し、メンバーの退会日を設定
    await client.$transaction(async (tx) => {
      // 退会申請を承認
      await tx.memberRetirementApplication.update({
        where: {
          memberRetirementApplicationId,
        },
        data: {
          approvedAt: new Date(),
        },
      });

      // メンバーの退会日を翌月の末に設定
      await tx.member.update({
        where: {
          memberId: application.memberId,
        },
        data: {
          retirementDate: lastDayOfNextMonth,
        },
      });
    });

    return ok(undefined);
  } catch (error) {
    return err({
      type: 'DATABASE_ERROR',
      message: error instanceof Error ? error.message : 'Unknown database error',
    });
  }
};

export const rejectMemberRetirementApplication = async (
  memberRetirementApplicationId: number
): Promise<Result<void, RejectMemberRetirementApplicationError>> => {
  try {
    // 退会申請が存在するかチェック
    const application = await client.memberRetirementApplication.findUnique({
      where: {
        memberRetirementApplicationId,
      },
    });

    if (!application) {
      return err({
        type: 'NOT_FOUND',
        message: `Member retirement application with ID ${memberRetirementApplicationId} not found`,
      });
    }

    // 既に処理済みかチェック
    if (application.approvedAt || application.rejectedAt) {
      return err({
        type: 'ALREADY_PROCESSED',
        message: `Member retirement application with ID ${memberRetirementApplicationId} is already processed`,
      });
    }

    // 退会申請を却下
    await client.memberRetirementApplication.update({
      where: {
        memberRetirementApplicationId,
      },
      data: {
        rejectedAt: new Date(),
      },
    });

    return ok(undefined);
  } catch (error) {
    return err({
      type: 'DATABASE_ERROR',
      message: error instanceof Error ? error.message : 'Unknown database error',
    });
  }
};
