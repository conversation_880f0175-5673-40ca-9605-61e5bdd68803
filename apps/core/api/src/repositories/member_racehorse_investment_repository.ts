import { MemberRacehorseInvestment } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';

export const getFirstMemberRacehorseInvestment = async (memberId: number, horseId: number): Promise<MemberRacehorseInvestment | null> => {
  const memberRacehorseInvestment = await client.memberRacehorseInvestment.findFirst({
    where: {
      memberId,
      horseId,
    },
    orderBy: {
      investmentDate: 'asc',
    },
  });
  return memberRacehorseInvestment;
};

// memberId, horseId, investmentDateに関してmemberRacehorseInvestmentを取得する
export const getMemberRacehorseInvestment = async (memberId: number, horseId: number, investmentDate: Date): Promise<MemberRacehorseInvestment | null> => {
  const memberRacehorseInvestment = await client.memberRacehorseInvestment.findFirst({
    where: {
      memberId,
      horseId,
      investmentDate,
    },
  });
  return memberRacehorseInvestment;
};