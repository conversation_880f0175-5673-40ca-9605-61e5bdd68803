import { BankAccountType } from '@hami/core-admin-api-schema/biller_service_pb';
import { client } from '@core-api/utils/prisma';
import { listBillers, getBiller, createBiller, updateBiller, BillerNotFoundError } from './biller_repository';
import { billerFactory } from '../../test_utils/factories/biller_factory';

describe('biller_repository', () => {
  beforeEach(async () => {
    await client.biller.deleteMany();
  });

  describe('listBillers', () => {
    it('請求元一覧を取得できること', async () => {
      const biller = await billerFactory.create();
      const result = await listBillers();
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(1);
        expect(result.value[0]).toMatchObject({
          id: biller.id,
          name: biller.name,
          address: biller.address,
          bankName: biller.bankName,
          bankBranch: biller.bankBranch,
          bankAccount: biller.bankAccount,
          bankAccountType: BankAccountType.SAVINGS,
          bankAccountName: biller.bankAccountName,
        });
      }
    });

    it('請求元が存在しない場合は空配列を返すこと', async () => {
      const result = await listBillers();
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(0);
      }
    });

    it('複数の請求元を取得できること', async () => {
      const billers = await Promise.all([
        billerFactory.create({ name: '請求元1' }),
        billerFactory.create({ name: '請求元2' }),
        billerFactory.create({ name: '請求元3' }),
      ]);

      const result = await listBillers();
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(3);
        expect(result.value.map(b => b.name)).toEqual(
          expect.arrayContaining(['請求元1', '請求元2', '請求元3'])
        );
      }
    });
  });

  describe('getBiller', () => {
    it('請求元詳細を取得できること', async () => {
      const biller = await billerFactory.create();
      const result = await getBiller({ id: biller.id });
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toMatchObject({
          id: biller.id,
          name: biller.name,
          address: biller.address,
          bankName: biller.bankName,
          bankBranch: biller.bankBranch,
          bankAccount: biller.bankAccount,
          bankAccountType: BankAccountType.SAVINGS,
          bankAccountName: biller.bankAccountName,
        });
      }
    });

    it('存在しない請求元の場合はエラーを返すこと', async () => {
      const result = await getBiller({ id: 999 });
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(BillerNotFoundError);
      }
    });

    it('不正なIDの場合はエラーを返すこと', async () => {
      const result = await getBiller({ id: -1 });
      expect(result.isErr()).toBe(true);
    });
  });

  describe('createBiller', () => {
    it('請求元を作成できること', async () => {
      const result = await createBiller({
        name: 'テスト請求元',
        address: 'テスト住所',
        bankName: 'テスト銀行',
        bankBranch: 'テスト支店',
        bankAccount: '1234567',
        bankAccountType: BankAccountType.SAVINGS,
        bankAccountName: 'テスト請求元',
      });
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toMatchObject({
          id: expect.any(Number),
          name: 'テスト請求元',
          address: 'テスト住所',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: BankAccountType.SAVINGS,
          bankAccountName: 'テスト請求元',
        });
      }
    });

    it('口座種別が未指定の場合は普通預金として作成されること', async () => {
      const result = await createBiller({
        name: 'テスト請求元',
        address: 'テスト住所',
        bankName: 'テスト銀行',
        bankBranch: 'テスト支店',
        bankAccount: '1234567',
        bankAccountType: BankAccountType.UNSPECIFIED,
        bankAccountName: 'テスト請求元',
      });
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.bankAccountType).toBe(BankAccountType.SAVINGS);
      }
    });
  });

  describe('updateBiller', () => {
    it('請求元を更新できること', async () => {
      const biller = await billerFactory.create();
      const result = await updateBiller(biller.id, {
        name: '更新後の請求元',
        address: '更新後の住所',
        bankName: '更新後の銀行',
        bankBranch: '更新後の支店',
        bankAccount: '7654321',
        bankAccountType: BankAccountType.CHECKING,
        bankAccountName: '更新後の請求元',
      });
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toEqual({});
      }

      // 更新後のデータを確認
      const getResult = await getBiller({ id: biller.id });
      expect(getResult.isOk()).toBe(true);
      if (getResult.isOk()) {
        expect(getResult.value).toMatchObject({
          id: biller.id,
          name: '更新後の請求元',
          address: '更新後の住所',
          bankName: '更新後の銀行',
          bankBranch: '更新後の支店',
          bankAccount: '7654321',
          bankAccountType: BankAccountType.CHECKING,
          bankAccountName: '更新後の請求元',
        });
      }
    });

    it('存在しない請求元の場合はエラーを返すこと', async () => {
      const result = await updateBiller(999, {
        name: '更新後の請求元',
        address: '更新後の住所',
        bankName: '更新後の銀行',
        bankBranch: '更新後の支店',
        bankAccount: '7654321',
        bankAccountType: BankAccountType.CHECKING,
        bankAccountName: '更新後の請求元',
      });
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(BillerNotFoundError);
      }
    });

    it('不正なIDの場合はエラーを返すこと', async () => {
      const result = await updateBiller(-1, {
        name: '更新後の請求元',
        address: '更新後の住所',
        bankName: '更新後の銀行',
        bankBranch: '更新後の支店',
        bankAccount: '7654321',
        bankAccountType: BankAccountType.CHECKING,
        bankAccountName: '更新後の請求元',
      });
      expect(result.isErr()).toBe(true);
    });

    it('口座種別が未指定の場合は普通預金として更新されること', async () => {
      const biller = await billerFactory.create();
      const result = await updateBiller(biller.id, {
        name: '更新後の請求元',
        address: '更新後の住所',
        bankName: '更新後の銀行',
        bankBranch: '更新後の支店',
        bankAccount: '7654321',
        bankAccountType: BankAccountType.UNSPECIFIED,
        bankAccountName: '更新後の請求元',
      });
      expect(result.isOk()).toBe(true);

      const getResult = await getBiller({ id: biller.id });
      expect(getResult.isOk()).toBe(true);
      if (getResult.isOk()) {
        expect(getResult.value.bankAccountType).toBe(BankAccountType.SAVINGS);
      }
    });

  });
});
