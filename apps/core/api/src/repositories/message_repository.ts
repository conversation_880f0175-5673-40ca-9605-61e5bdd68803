import { ResultAsync, err } from 'neverthrow';
import { Prisma, MessageType, MessageStatus } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class MessageNotFoundError extends Error {
  readonly name = 'MessageNotFoundError';
}

/**
 * メッセージ一覧取得のパラメータ
 */
export interface ListMessagesParams {
  page: number;
  pageSize: number;
  messageType?: MessageType;
  status?: MessageStatus;
  senderId?: number;
  searchQuery?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * メッセージ一覧取得
 * @param params 検索・フィルター・ページネーション条件
 * @returns メッセージ一覧と総件数
 */
export const listMessages = (params: ListMessagesParams) => {
  const { page, pageSize, messageType, status, senderId, searchQuery, sortBy = 'createdAt', sortOrder = 'desc' } = params;
  const skip = (page - 1) * pageSize;

  // 検索・フィルター条件の構築
  const whereConditions: Prisma.MessageWhereInput = {};

  if (messageType) {
    whereConditions.messageType = messageType;
  }

  if (status) {
    whereConditions.status = status;
  }

  if (senderId) {
    whereConditions.senderId = senderId;
  }

  if (searchQuery) {
    whereConditions.OR = [
      {
        title: {
          contains: searchQuery,
          mode: 'insensitive',
        },
      },
      {
        body: {
          contains: searchQuery,
          mode: 'insensitive',
        },
      },
    ];
  }

  // ソート条件の構築
  const orderBy: Prisma.MessageOrderByWithRelationInput = {};
  if (sortBy === 'createdAt') {
    orderBy.createdAt = sortOrder;
  } else if (sortBy === 'updatedAt') {
    orderBy.updatedAt = sortOrder;
  } else if (sortBy === 'sentAt') {
    orderBy.sentAt = sortOrder;
  } else {
    orderBy.createdAt = sortOrder;
  }

  return ResultAsync.fromPromise(
    Promise.all([
      client.message.findMany({
        where: whereConditions,
        include: {
          attachments: {
            orderBy: {
              displayOrder: 'asc',
            },
          },
        },
        skip,
        take: pageSize,
        orderBy,
      }),
      client.message.count({
        where: whereConditions,
      }),
    ]),
    () => new DatabaseError('Failed to list messages')
  ).map(([messages, totalCount]) => ({
    messages,
    totalCount,
    totalPages: Math.ceil(totalCount / pageSize),
  }));
};

/**
 * メッセージ詳細取得（public_idで検索）
 * @param publicId メッセージ公開ID
 * @returns メッセージ詳細
 */
export const getMessageByPublicId = (publicId: string) => {
  return ResultAsync.fromPromise(
    client.message.findUnique({
      where: { publicId },
      include: {
        attachments: {
          orderBy: {
            displayOrder: 'asc',
          },
        },
      },
    }),
    () => new DatabaseError('Failed to get message')
  ).andThen((message) => {
    if (!message) {
      return err(new MessageNotFoundError(`Message with public ID ${publicId} not found`));
    }
    return ResultAsync.fromSafePromise(Promise.resolve(message));
  });
};

/**
 * メッセージ詳細取得（内部IDで検索）- 後方互換性のため残す
 * @param messageId メッセージID
 * @returns メッセージ詳細
 */
export const getMessageById = (messageId: number) => {
  return ResultAsync.fromPromise(
    client.message.findUnique({
      where: { messageId },
      include: {
        attachments: {
          orderBy: {
            displayOrder: 'asc',
          },
        },
      },
    }),
    () => new DatabaseError('Failed to get message')
  ).andThen((message) => {
    if (!message) {
      return err(new MessageNotFoundError(`Message with ID ${messageId} not found`));
    }
    return ResultAsync.fromSafePromise(Promise.resolve(message));
  });
};
