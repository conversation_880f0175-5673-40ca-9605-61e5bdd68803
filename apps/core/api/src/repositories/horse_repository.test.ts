import { Prisma } from '@hami/prisma';
import {
  createHorse,
  deleteHorse,
  existsHorse,
  existsHorseByRecruitment,
  findHorseById,
  HorseAlreadyExistsError,
  HorseNotFoundError,
  listHorses,
  updateHorse,
  checkHorseExists,
  listHorseInvestors,
  listHorsesWithInvestors,
} from './horse_repository';

// vPrisma.clientを使用（setup.tsでモックされているため）
const prisma = vPrisma.client;

describe('horse_repository', () => {
  describe('createHorse', () => {
    it('新しい馬とプロフィールを作成できる', async () => {
      // ===== Arrange =====
      const horseData = {
        recruitmentYear: 2024,
        recruitmentNo: 1,
        horseName: 'テスト馬',
        birthYear: 2022,
        birthMonth: 3,
        birthDay: 15,
        recruitmentName: 'テストの22',
        sharesTotal: 1000,
        amountTotal: 50000000,
        note: 'テスト馬の備考',
        fundStartYear: 2024,
        fundStartMonth: 1,
        fundStartDay: 1,
        conflictOfInterest: true,
      };

      // ===== Act =====
      const result = await createHorse(horseData);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const createdHorse = result.value;
        expect(createdHorse.horseName).toBe('テスト馬');
        expect(createdHorse.recruitmentYear).toBe(2024);
        expect(createdHorse.recruitmentNo).toBe(1);
        expect(createdHorse.horseId).toBeGreaterThan(0);
        expect(createdHorse.fundStartYear).toEqual(2024);
        expect(createdHorse.fundStartMonth).toEqual(1);
        expect(createdHorse.fundStartDay).toEqual(1);
        expect(createdHorse.conflictOfInterest).toEqual(true);
      }
    });

    it('同じ募集年・番号で作成しようとした場合はエラーを返す', async () => {
      // ===== Arrange =====
      await prisma.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 999,
          horseName: '既存馬',
          birthYear: 2022,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '既存馬の22',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '既存馬備考',
          fundStartYear: 2024,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
        },
      });

      const duplicateData = {
        recruitmentYear: 2024,
        recruitmentNo: 999,
        horseName: '重複テスト馬',
        birthYear: 2022,
        birthMonth: 3,
        birthDay: 15,
        recruitmentName: '重複テスト',
        sharesTotal: 1000,
        amountTotal: 50000000,
        note: '重複テスト',
        fundStartYear: 2024,
        fundStartMonth: 1,
        fundStartDay: 1,
        conflictOfInterest: true,
      };

      // ===== Act =====
      const result = await createHorse(duplicateData);

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseAlreadyExistsError);
      }
    });
  });

  describe('listHorses', () => {
    it('馬の一覧をページネーションで取得できる', async () => {
      // ===== Arrange =====
      // 削除されていない馬を作成
      const horse1 = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 1,
          horseName: 'リストテスト馬1',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'リストテスト1の21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: 'リストテスト1備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
        },
      });
      await prisma.horseProfile.create({
        data: { horseId: horse1.horseId },
      });

      const horse2 = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 2,
          horseName: 'リストテスト馬2',
          birthYear: 2021,
          birthMonth: 2,
          birthDay: 2,
          recruitmentName: 'リストテスト2の21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: 'リストテスト2備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: true,
        },
      });
      await prisma.horseProfile.create({
        data: { horseId: horse2.horseId },
      });

      // 削除された馬を作成（検索結果に含まれないはず）
      const deletedHorse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 3,
          horseName: '削除馬',
          birthYear: 2021,
          birthMonth: 3,
          birthDay: 3,
          recruitmentName: '削除馬の21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '削除馬備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
          deletedAt: new Date(),
        },
      });

      // ===== Act =====
      const result = await listHorses({
        page: 1,
        limit: 10,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { horses, totalCount, totalPages } = result.value;
        expect(horses.length).toBeGreaterThanOrEqual(2);
        expect(totalCount).toBeGreaterThanOrEqual(2);
        expect(totalPages).toBeGreaterThanOrEqual(1);

        // 削除された馬は含まれていないことを確認
        const horseIds = horses.map((h) => h.horseId);
        expect(horseIds).not.toContain(deletedHorse.horseId);

        // プロフィールが含まれていることを確認
        horses.forEach((horse) => {
          expect(horse.profile).toBeDefined();
        });
      }
    });

    it('馬名で検索できる', async () => {
      // ===== Arrange =====
      const searchTerm = 'サーチテスト馬';
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 10,
          horseName: searchTerm,
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'サーチテストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: 'サーチテスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
        },
      });
      await prisma.horseProfile.create({
        data: { horseId: horse.horseId },
      });

      // ===== Act =====
      const result = await listHorses({
        page: 1,
        limit: 10,
        search: searchTerm,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { horses } = result.value;
        expect(horses.length).toBeGreaterThanOrEqual(1);
        const foundHorse = horses.find((h) => h.horseId === horse.horseId);
        expect(foundHorse).toBeDefined();
        expect(foundHorse?.horseName).toBe(searchTerm);
      }
    });

    it('生年でフィルタリングできる', async () => {
      // ===== Arrange =====
      const targetYear = 2020;
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 11,
          horseName: 'フィルタテスト馬',
          birthYear: targetYear,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'フィルタテストの20',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: 'フィルタテスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
        },
      });
      await prisma.horseProfile.create({
        data: { horseId: horse.horseId },
      });

      // ===== Act =====
      const result = await listHorses({
        page: 1,
        limit: 10,
        birthYear: targetYear,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { horses } = result.value;
        horses.forEach((horse) => {
          expect(horse.birthYear).toBe(targetYear);
        });
      }
    });
  });

  describe('findHorseById', () => {
    it('正常に馬を取得できる', async () => {
      // ===== Arrange =====
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 20,
          horseName: '詳細テスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '詳細テストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '詳細テスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: true,
        },
      });
      const profile = await prisma.horseProfile.create({
        data: { horseId: horse.horseId },
      });

      // ===== Act =====
      const result = await findHorseById({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const foundHorse = result.value;
        expect(foundHorse.horseId).toBe(horse.horseId);
        expect(foundHorse.horseName).toBe(horse.horseName);
        expect(foundHorse.profile).toBeDefined();
        expect(foundHorse.profile?.horseProfileId).toBe(profile.horseProfileId);
      }
    });

    it('存在しない馬IDの場合はエラーを返す', async () => {
      // ===== Act =====
      const result = await findHorseById({ horseId: 99999 });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseNotFoundError);
      }
    });

    it('削除された馬の場合はエラーを返す', async () => {
      // ===== Arrange =====
      const deletedHorse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 21,
          horseName: '削除済みテスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '削除済みテストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '削除済みテスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
          deletedAt: new Date(),
        },
      });

      // ===== Act =====
      const result = await findHorseById({ horseId: deletedHorse.horseId });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseNotFoundError);
      }
    });
  });

  describe('updateHorse', () => {
    it('馬の情報を更新できる', async () => {
      // ===== Arrange =====
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 30,
          horseName: '更新テスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '更新テストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '更新テスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
        },
      });
      await prisma.horseProfile.create({
        data: { horseId: horse.horseId },
      });

      const updateData = {
        horseName: '更新後馬名',
        note: '更新後の備考',
        fundStartYear: 2023,
        fundStartMonth: 1,
        fundStartDay: 2,
        conflictOfInterest: true,
      };

      // ===== Act =====
      const result = await updateHorse({
        horseId: horse.horseId,
        data: updateData,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const updatedHorse = result.value;
        expect(updatedHorse.horseName).toBe('更新後馬名');
        expect(updatedHorse.note).toBe('更新後の備考');
        expect(updatedHorse.fundStartYear).toEqual(2023);
        expect(updatedHorse.fundStartMonth).toEqual(1);
        expect(updatedHorse.fundStartDay).toEqual(2);
        expect(updatedHorse.conflictOfInterest).toEqual(true);
      }
    });

    it('存在しない馬IDの場合はエラーを返す', async () => {
      // ===== Act =====
      const result = await updateHorse({
        horseId: 99999,
        data: { horseName: 'テスト' },
      });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseNotFoundError);
      }
    });

    it('削除された馬は更新できない', async () => {
      // ===== Arrange =====
      const deletedHorse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 32,
          horseName: '削除済み更新テスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '削除済み更新テストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '削除済み更新テスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
          deletedAt: new Date(),
        },
      });

      // ===== Act =====
      const result = await updateHorse({
        horseId: deletedHorse.horseId,
        data: { horseName: 'テスト更新' },
      });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseNotFoundError);
      }
    });
  });

  describe('deleteHorse', () => {
    it('馬を論理削除できる', async () => {
      // ===== Arrange =====
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 40,
          horseName: '削除テスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '削除テストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '削除テスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: true,
        },
      });
      await prisma.horseProfile.create({
        data: { horseId: horse.horseId },
      });

      // ===== Act =====
      const result = await deleteHorse({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const deletedHorse = result.value;
        expect(deletedHorse.deletedAt).not.toBeNull();
        expect(deletedHorse.deletedAt).toBeInstanceOf(Date);
      }
    });

    it('存在しない馬IDの場合はエラーを返す', async () => {
      // ===== Act =====
      const result = await deleteHorse({ horseId: 99999 });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseNotFoundError);
      }
    });

    it('既に削除された馬は再削除できない', async () => {
      // ===== Arrange =====
      const alreadyDeletedHorse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 41,
          horseName: '再削除テスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '再削除テストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '再削除テスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
          deletedAt: new Date(),
        },
      });

      // ===== Act =====
      const result = await deleteHorse({ horseId: alreadyDeletedHorse.horseId });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseNotFoundError);
      }
    });
  });

  describe('existsHorse', () => {
    it('存在する馬の場合はtrueを返す', async () => {
      // ===== Arrange =====
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 50,
          horseName: '存在テスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '存在テストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '存在テスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: true,
        },
      });

      // ===== Act =====
      const result = await existsHorse({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(true);
      }
    });

    it('存在しない馬の場合はfalseを返す', async () => {
      // ===== Act =====
      const result = await existsHorse({ horseId: 99999 });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(false);
      }
    });

    it('削除された馬の場合はfalseを返す', async () => {
      // ===== Arrange =====
      const deletedHorse = await prisma.horse.create({
        data: {
          recruitmentYear: 2023,
          recruitmentNo: 51,
          horseName: '削除存在テスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '削除存在テストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '削除存在テスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
          deletedAt: new Date(),
        },
      });

      // ===== Act =====
      const result = await existsHorse({ horseId: deletedHorse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(false);
      }
    });
  });

  describe('existsHorseByRecruitment', () => {
    it('同じ募集年・番号の馬が存在する場合はtrueを返す', async () => {
      // ===== Arrange =====
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 123,
          horseName: '重複チェックテスト馬',
          birthYear: 2022,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '重複チェックテストの22',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '重複チェックテスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: true,
        },
      });

      // ===== Act =====
      const result = await existsHorseByRecruitment({
        recruitmentYear: 2024,
        recruitmentNo: 123,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(true);
      }
    });

    it('同じ募集年・番号の馬が存在しない場合はfalseを返す', async () => {
      // ===== Act =====
      const result = await existsHorseByRecruitment({
        recruitmentYear: 2024,
        recruitmentNo: 999,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(false);
      }
    });

    it('削除された馬は重複チェックに含まれない', async () => {
      // ===== Arrange =====
      await prisma.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 456,
          horseName: '削除重複チェックテスト馬',
          birthYear: 2022,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '削除重複チェックテストの22',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '削除重複チェックテスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
          deletedAt: new Date(),
        },
      });

      // ===== Act =====
      const result = await existsHorseByRecruitment({
        recruitmentYear: 2024,
        recruitmentNo: 456,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(false);
      }
    });

    it('除外IDを指定した場合、その馬は重複チェックから除外される', async () => {
      // ===== Arrange =====
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 789,
          horseName: '除外重複チェックテスト馬',
          birthYear: 2022,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '除外重複チェックテストの22',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: '除外重複チェックテスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: true,
        },
      });

      // ===== Act =====
      const result = await existsHorseByRecruitment({
        recruitmentYear: 2024,
        recruitmentNo: 789,
        excludeHorseId: horse.horseId,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(false);
      }
    });
  });

  describe('checkHorseExists', () => {
    it('存在する馬の場合はtrueを返す', async () => {
      // ===== Arrange =====
      await prisma.horse.create({
        data: {
          recruitmentYear: 2025,
          recruitmentNo: 100,
          horseName: 'チェック存在テスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'チェック存在テストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: 'チェック存在テスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
        },
      });

      // ===== Act =====
      const result = await checkHorseExists({
        recruitmentYear: 2025,
        recruitmentNo: 100,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(true);
      }
    });

    it('存在しない馬の場合はfalseを返す', async () => {
      // ===== Act =====
      const result = await checkHorseExists({
        recruitmentYear: 2099,
        recruitmentNo: 999,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(false);
      }
    });

    it('削除された馬の場合はfalseを返す', async () => {
      // ===== Arrange =====
      await prisma.horse.create({
        data: {
          recruitmentYear: 2025,
          recruitmentNo: 101,
          horseName: 'チェック削除存在テスト馬',
          birthYear: 2021,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'チェック削除存在テストの21',
          sharesTotal: 1000,
          amountTotal: 30000000,
          note: 'チェック削除存在テスト備考',
          fundStartYear: 2023,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: true,
          deletedAt: new Date(),
        },
      });

      // ===== Act =====
      const result = await checkHorseExists({
        recruitmentYear: 2025,
        recruitmentNo: 101,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(false);
      }
    });
  });

  describe('listHorseInvestors', () => {
    it('馬に出資している会員一覧を取得できる', async () => {
      // ===== Arrange =====
      const now = new Date();
      const retirementDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      // 馬を作成
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 200,
          horseName: '出資者リストテスト馬',
          birthYear: 2022,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '出資者リストテストの22',
          sharesTotal: 1000,
          amountTotal: 50000000,
          note: '出資者リストテスト備考',
          fundStartYear: 2024,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: false,
        },
      });

      // ユーザーと会員申請を作成
      const user1 = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'hashedPassword1',
          salt: 'salt1',
        },
      });
      const application1 = await prisma.membershipApplication.create({
        data: {
          mailVerification: {
            create: {
              email: '<EMAIL>',
              token: 'token1',
              expiresAt: new Date(Date.now() + 60000),
            },
          },
          firstName: '太郎',
          lastName: '田中',
          firstNameKana: 'タロウ',
          lastNameKana: 'タナカ',
          postalCode: '100-0001',
          prefecture: '東京都',
          address: '千代田区千代田1-1-1',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          identityUploadToken: 'token1',
        },
      });
      const member1 = await prisma.member.create({
        data: {
          userId: user1.userId,
          membershipApplicationId: application1.membershipApplicationId,
          memberNumber: 10001,
          firstName: '太郎',
          lastName: '田中',
          firstNameKana: 'タロウ',
          lastNameKana: 'タナカ',
          postalCode: '100-0001',
          prefecture: '東京都',
          address: '千代田区千代田1-1-1',
          phoneNumber: '090-1234-5678',
          birthYear: 1990,
          birthMonth: 1,
          birthDay: 1,
          approvedAt: new Date(),
          approvedBy: 'admin',
        },
      });

      const user2 = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          passwordHash: 'hashedPassword2',
          salt: 'salt2',
        },
      });
      const application2 = await prisma.membershipApplication.create({
        data: {
          mailVerification: {
            create: {
              email: '<EMAIL>',
              token: 'token2',
              expiresAt: new Date(Date.now() + 60000),
            },
          },
          firstName: '花子',
          lastName: '佐藤',
          firstNameKana: 'ハナコ',
          lastNameKana: 'サトウ',
          postalCode: '100-0002',
          prefecture: '東京都',
          address: '千代田区千代田2-2-2',
          phoneNumber: '090-8765-4321',
          birthYear: 1985,
          birthMonth: 5,
          birthDay: 15,
          identityUploadToken: 'token2',
        },
      });
      const member2 = await prisma.member.create({
        data: {
          userId: user2.userId,
          membershipApplicationId: application2.membershipApplicationId,
          memberNumber: 10002,
          firstName: '花子',
          lastName: '佐藤',
          firstNameKana: 'ハナコ',
          lastNameKana: 'サトウ',
          postalCode: '100-0002',
          prefecture: '東京都',
          address: '千代田区千代田2-2-2',
          phoneNumber: '090-8765-4321',
          birthYear: 1985,
          birthMonth: 5,
          birthDay: 15,
          approvedAt: new Date(),
          approvedBy: 'admin',
          retirementDate: retirementDate,
        },
      });

      // 出資契約を作成（田中太郎は複数の契約を持つ）
      await prisma.investmentContract.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          sharesNumber: 10,
          investmentAmount: 500000,
          discount: 0,
          taxRate: new Prisma.Decimal(0.1),
          investmentAmountBeforeTax: 450000,
        },
      });

      await prisma.investmentContract.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          sharesNumber: 5,
          investmentAmount: 250000,
          discount: 0,
          taxRate: new Prisma.Decimal(0.1),
          investmentAmountBeforeTax: 225000,
        },
      });

      await prisma.investmentContract.create({
        data: {
          horseId: horse.horseId,
          memberId: member2.memberId,
          sharesNumber: 3,
          investmentAmount: 150000,
          discount: 0,
          taxRate: new Prisma.Decimal(0.1),
          investmentAmountBeforeTax: 135000,
        },
      });

      // ===== Act =====
      const result = await listHorseInvestors({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const investors = result.value;
        expect(investors.length).toBe(2);

        // 出資金額の降順でソートされることを確認
        expect(investors[0].memberName).toBe('田中 太郎');
        expect(investors[0].memberNumber).toBe(10001);
        expect(investors[0].sharesNumber).toBe(15); // 10 + 5
        expect(investors[0].investmentAmount).toBe(750000); // 500000 + 250000
        expect(investors[0].investmentAmountBeforeTax).toBe(675000); // 450000 + 225000
        expect(investors[0].taxRate).toBe(0.1);
        expect(investors[0].transactionAmount).toBe(0);
        expect(investors[0].monthlyDepreciation).toBe(0);
        expect(investors[0].investmentContractId).toBeDefined();
        expect(investors[0].retirementDate).toBeNull();
        expect(investors[0].sharesNumber).toBe(15);

        expect(investors[1].memberName).toBe('佐藤 花子');
        expect(investors[1].memberNumber).toBe(10002);
        expect(investors[1].sharesNumber).toBe(3);
        expect(investors[1].investmentAmount).toBe(150000);
        expect(investors[1].investmentAmountBeforeTax).toBe(135000); // 135000
        expect(investors[1].taxRate).toBe(0.1);
        expect(investors[1].transactionAmount).toBe(0);
        expect(investors[1].monthlyDepreciation).toBe(0);
        expect(investors[1].investmentContractId).toBeDefined();
        expect(investors[1].retirementDate).toEqual(retirementDate);
        expect(investors[1].sharesNumber).toBe(3);
      }
    });

    it('出資者がいない馬の場合は空の配列を返す', async () => {
      // ===== Arrange =====
      const horse = await prisma.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 201,
          horseName: '出資者なしテスト馬',
          birthYear: 2022,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: '出資者なしテストの22',
          sharesTotal: 1000,
          amountTotal: 50000000,
          note: '出資者なしテスト備考',
          fundStartYear: 2024,
          fundStartMonth: 1,
          fundStartDay: 1,
          conflictOfInterest: true,
        },
      });

      // ===== Act =====
      const result = await listHorseInvestors({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toEqual([]);
      }
    });

    it('存在しない馬IDの場合は空の配列を返す', async () => {
      // ===== Act =====
      const result = await listHorseInvestors({ horseId: 999999 });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toEqual([]);
      }
    });
  });

  describe('listHorsesWithInvestors', () => {
    it('全ての馬とその出資者情報を一括取得できる', async () => {
      // ===== Arrange =====
      // 既存のテストデータを使用（beforeEachで作成されたデータ）

      // ===== Act =====
      const result = await listHorsesWithInvestors();

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { horses, horsesWithInvestors } = result.value;

        // 馬の一覧が取得できることを確認
        expect(horses.length).toBeGreaterThanOrEqual(0);

        // 馬と出資者のマップが正しく構築されることを確認
        expect(horsesWithInvestors.size).toBe(horses.length);

        // 各馬のデータ構造が正しいことを確認
        for (const [horseId, horseData] of horsesWithInvestors) {
          expect(horseData.horse).toBeDefined();
          expect(horseData.horse.horseId).toBe(horseId);
          expect(horseData.investors).toBeDefined();
          expect(Array.isArray(horseData.investors)).toBe(true);

          // 出資者がいる場合の構造確認
          for (const investor of horseData.investors) {
            expect(investor.memberId).toBeDefined();
            expect(investor.memberNumber).toBeDefined();
            expect(investor.memberName).toBeDefined();
            expect(investor.sharesNumber).toBeGreaterThan(0);
            expect(investor.investmentAmount).toBeGreaterThan(0);
          }
        }
      }
    });

    it('N+1クエリ問題が解決されていることを確認', async () => {
      // ===== Act =====
      const result = await listHorsesWithInvestors();

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { horses, horsesWithInvestors } = result.value;

        // 単一のクエリで全ての馬と出資者情報が取得されることを確認
        // （実際のクエリ数の測定は困難なため、データ構造の整合性で確認）
        expect(horsesWithInvestors.size).toBe(horses.length);

        // 全ての馬に対して出資者情報が含まれていることを確認
        for (const horse of horses) {
          const horseData = horsesWithInvestors.get(horse.horseId);
          expect(horseData).toBeDefined();
          expect(horseData!.horse.horseId).toBe(horse.horseId);
        }
      }
    });
  });
});
