import { Prisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';

/**
 * 出資申込馬一覧取得
 * rejected=false かつ contracted=false のレコードの申込口数を馬ごとに集計
 */
export async function listInvestmentApplicationHorses() {
  // 出資申込がある馬を取得
  const horsesWithApplications = await client.horse.findMany({
    where: {
      investmentApplications: {
        some: {
          rejected: false,
          contracted: false,
        },
      },
    },
    include: {
      investmentApplications: {
        where: {
          rejected: false,
          contracted: false,
          member: {
            retirementDate: null,
          },
        },
        select: {
          requestedNumber: true,
          allocatedNumber: true,
        },
      },
      investmentContracts: {
        where: {
          member: {
            retirementDate: null,
          },
        },
        select: {
          sharesNumber: true,
        },
      },
    },
    orderBy: [{ recruitmentYear: 'desc' }, { recruitmentNo: 'asc' }],
  });

  return horsesWithApplications.map((horse) => {
    const totalApplicationShares = horse.investmentApplications.reduce((sum, app) => sum + app.requestedNumber, 0);

    return {
      horseId: horse.horseId,
      recruitmentYear: horse.recruitmentYear,
      recruitmentNo: horse.recruitmentNo,
      horseName: horse.horseName,
      recruitmentName: horse.recruitmentName,
      totalApplicationShares,
      recruitmentShares: horse.sharesTotal,
      contractedShares: horse.investmentContracts.reduce((sum: number, contract) => sum + contract.sharesNumber, 0),
    };
  });
}

/**
 * 出資申込馬詳細取得
 */
export async function getInvestmentApplicationHorseDetail(horseId: number) {
  const horse = await client.horse.findUnique({
    where: { horseId },
  });

  if (!horse) {
    throw new Error('Horse not found');
  }

  // 有効な申込を取得
  const applications = await client.investmentApplication.findMany({
    where: {
      horseId,
      rejected: false,
      contracted: false,
      member: {
        retirementDate: null,
      },
    },
    select: {
      requestedNumber: true,
      allocatedNumber: true,
    },
  });

  // 受入処理済みの申込の合計口数を計算
  const totalApplicationShares = applications
    .filter((app) => app.allocatedNumber !== null && app.allocatedNumber > 0)
    .reduce((sum, app) => sum + (app.allocatedNumber ?? 0), 0);

  // 契約締結済口数（InvestmentContractのsharedNumberの合計）
  const contractedShares = await client.investmentContract.aggregate({
    where: {
      horseId,
      member: {
        retirementDate: null,
      },
    },
    _sum: {
      sharesNumber: true,
    },
  });

  return {
    horseId: horse.horseId,
    birthYear: horse.birthYear,
    birthMonth: horse.birthMonth,
    birthDay: horse.birthDay,
    recruitmentYear: horse.recruitmentYear,
    recruitmentNo: horse.recruitmentNo,
    horseName: horse.horseName || '',
    recruitmentName: horse.recruitmentName,
    totalApplicationShares,
    recruitmentShares: horse.sharesTotal,
    contractedShares: contractedShares._sum.sharesNumber || 0,
    recruitmentTotalAmount: horse.amountTotal,
  };
}

/**
 * 馬ごと出資申込一覧取得
 * rejectedがfalse かつ contractedがfalse
 */
export async function listInvestmentApplicationsByHorse(params: { horseId: number; isWhole?: boolean; sortBy: 'applied_at' | 'member_name_kana'; sortOrder: 'asc' | 'desc' }) {
  const { horseId, isWhole, sortBy, sortOrder } = params;

  const where: Prisma.InvestmentApplicationWhereInput = {
    horseId,
    rejected: false,
    contracted: false,
    member: {
      retirementDate: null,
    },
  };

  if (isWhole !== undefined) {
    where.isWhole = isWhole;
  }

  // orderByの組み立て
  let orderBy: Prisma.InvestmentApplicationOrderByWithRelationInput | Prisma.InvestmentApplicationOrderByWithRelationInput[] = { appliedAt: sortOrder };
  if (sortBy === 'member_name_kana') {
    orderBy = [
      { member: { lastNameKana: sortOrder } },
      { member: { firstNameKana: sortOrder } },
    ];
  }

  const applications = await client.investmentApplication.findMany({
    where,
    include: {
      member: {
        select: {
          memberId: true,
          memberNumber: true,
          lastName: true,
          firstName: true,
          lastNameKana: true,
          firstNameKana: true,
        },
      },
    },
    orderBy,
  });

  return applications.map((app) => ({
    investmentApplicationId: app.investmentApplicationId,
    applicationDate: app.appliedAt,
    memberName: `${app.member.lastName} ${app.member.firstName}`,
    memberNameKana: `${app.member.lastNameKana} ${app.member.firstNameKana}`,
    memberId: app.member.memberId,
    memberNumber: app.member.memberNumber,
    requestedShares: app.requestedNumber,
    rejectPartialAllocation: app.rejectPartialAllocation,
    allocatedShares: app.allocatedNumber,
    isWhole: app.isWhole,
    installmentPayment: app.installmentPayment,
  }));
}

/**
 * 出資受入実施
 * allocatedNumberを更新
 * デッドロック回避のため、IDでソートしてから順次更新
 */
export async function acceptInvestmentApplications(
  acceptances: Array<{
    horseId: number;
    investmentApplicationId: number;
    allocatedShares: number;
  }>
) {
  // デッドロック回避のため、investmentApplicationIdでソート
  const sortedAcceptances = [...acceptances].sort((a, b) => a.investmentApplicationId - b.investmentApplicationId);

  await client.$transaction(
    async (tx) => {
      // Promise.allを使用してバッチ処理（ただし、順序を保つため順次実行）
      for (const acceptance of sortedAcceptances) {
        await tx.investmentApplication.update({
          where: {
            investmentApplicationId: acceptance.investmentApplicationId,
            horseId: acceptance.horseId,
            rejected: false,
            contracted: false,
          },
          data: {
            allocatedNumber: acceptance.allocatedShares,
          },
        });
      }
    },
    {
      timeout: 10000, // 10秒のタイムアウト設定
    }
  );
}

/**
 * 出資申込契約締結対象一覧取得
 * rejectedがfalse かつ contractedがfalse かつ allocatedNumberが0より大きい
 */
export async function listContractTargetApplications(horseId: number) {
  const applications = await client.investmentApplication.findMany({
    where: {
      horseId,
      rejected: false,
      contracted: false,
      allocatedNumber: {
        gt: 0,
      },
    },
    include: {
      member: {
        select: {
          memberId: true,
          memberNumber: true,
          lastName: true,
          firstName: true,
          lastNameKana: true,
          firstNameKana: true,
        },
      },
    },
    orderBy: {
      appliedAt: 'desc',
    },
  });

  return applications.map((app) => ({
    investmentApplicationId: app.investmentApplicationId,
    memberName: `${app.member.lastName} ${app.member.firstName}`,
    memberNameKana: `${app.member.lastNameKana} ${app.member.firstNameKana}`,
    requestedShares: app.requestedNumber,
    allocatedNumber: app.allocatedNumber,
  }));
}

export async function getExistingContracts(horseId: number) {
  const existingContracts = await client.investmentApplication.findMany({
    where: {
      horseId,
      contracted: true,
    },
  });

  return existingContracts.map((contract) => ({
    investmentApplicationId: contract.investmentApplicationId,
    allocatedNumber: contract.allocatedNumber,
  }));
}

export async function getContractTargetApplications(horseId: number, investmentApplicationIds: number[]) {
  const applications = await client.investmentApplication.findMany({
    where: {
      investmentApplicationId: {
        in: investmentApplicationIds,
      },
      horseId,
      rejected: false,
      contracted: false,
      allocatedNumber: {
        gt: 0,
      },
    },
    include: {
      member: {
        select: {
          memberId: true,
          memberNumber: true,
          lastName: true,
          firstName: true,
        },
      },
    },
  });

  return applications.map((app) => ({
    investmentApplicationId: app.investmentApplicationId,
    memberName: `${app.member.lastName} ${app.member.firstName}`,
    requestedShares: app.requestedNumber,
    allocatedNumber: app.allocatedNumber,
    installmentPayment: app.installmentPayment,
  }));
}

export type InvestmentApplicationTypeFilter = 'horse' | 'annual_bundle';
export type InvestmentApplicationSortKey = 'applied_at' | 'member_name_kana';

export type InvestmentApplicationRecord = {
  investmentApplicationId: number;
  type: InvestmentApplicationTypeFilter;
  appliedAt: Date;
  member: {
    memberId: number;
    memberNumber: number;
    firstName: string;
    lastName: string;
    firstNameKana: string;
    lastNameKana: string;
  };
  requestedShares: number;
  allocatedNumber: number | null;
  rejectPartialAllocation: boolean;
  isWhole: boolean;
  installmentPayment: boolean;
  horse?: {
    horseId: number;
    recruitmentYear: number;
    recruitmentNo: number;
    horseName: string;
    recruitmentName: string;
  };
  annualBundle?: {
    annualBundleId: number;
    name: string;
    fiscalYear: number;
    shares: number;
  };
  rejected: boolean;
  contracted: boolean;
};

export async function listInvestmentApplications(params: {
  typeFilter?: InvestmentApplicationTypeFilter;
  horseIds?: number[];
  annualBundleIds?: number[];
  isWhole?: boolean;
  sortBy?: InvestmentApplicationSortKey;
  sortOrder?: 'asc' | 'desc';
}): Promise<InvestmentApplicationRecord[]> {
  const {
    typeFilter,
    horseIds = [],
    annualBundleIds = [],
    isWhole,
    sortBy = 'applied_at',
    sortOrder = 'desc',
  } = params;

  const andConditions: Prisma.InvestmentApplicationWhereInput[] = [
    { rejected: false },
    { contracted: false },
    { member: { retirementDate: null } },
    { OR: [{ horseId: { not: null } }, { annualBundleId: { not: null } }] },
  ];

  if (typeFilter === 'horse') {
    andConditions.push({ horseId: { not: null } });
    andConditions.push({ annualBundleId: null });
  }
  if (typeFilter === 'annual_bundle') {
    andConditions.push({ horseId: null });
    andConditions.push({ annualBundleId: { not: null } });
  }
  if (horseIds.length > 0) {
    andConditions.push({ horseId: { in: horseIds } });
  }
  if (annualBundleIds.length > 0) {
    andConditions.push({ annualBundleId: { in: annualBundleIds } });
  }
  if (isWhole !== undefined) {
    andConditions.push({ isWhole });
  }

  const where: Prisma.InvestmentApplicationWhereInput = { AND: andConditions };

  const orderBy: Prisma.InvestmentApplicationOrderByWithRelationInput | Prisma.InvestmentApplicationOrderByWithRelationInput[] =
    sortBy === 'member_name_kana'
      ? [
          { member: { lastNameKana: sortOrder } },
          { member: { firstNameKana: sortOrder } },
          { appliedAt: sortOrder },
        ]
      : { appliedAt: sortOrder };

  const applications = await client.investmentApplication.findMany({
    where,
    include: {
      member: {
        select: {
          memberId: true,
          memberNumber: true,
          firstName: true,
          lastName: true,
          firstNameKana: true,
          lastNameKana: true,
          retirementDate: true,
        },
      },
      horse: {
        select: {
          horseId: true,
          recruitmentYear: true,
          recruitmentNo: true,
          horseName: true,
          recruitmentName: true,
        },
      },
      annualBundle: {
        select: {
          annualBundleId: true,
          name: true,
          fiscalYear: true,
          shares: true,
        },
      },
    },
    orderBy,
  });

  return applications.map((app) => ({
    investmentApplicationId: app.investmentApplicationId,
    type: app.annualBundleId ? 'annual_bundle' : 'horse',
    appliedAt: app.appliedAt,
    member: {
      memberId: app.member.memberId,
      memberNumber: app.member.memberNumber,
      firstName: app.member.firstName || '',
      lastName: app.member.lastName || '',
      firstNameKana: app.member.firstNameKana || '',
      lastNameKana: app.member.lastNameKana || '',
    },
    requestedShares: app.requestedNumber,
    allocatedNumber: app.allocatedNumber,
    rejectPartialAllocation: app.rejectPartialAllocation,
    isWhole: app.isWhole,
    installmentPayment: app.installmentPayment,
    horse: app.horse
      ? {
          horseId: app.horse.horseId,
          recruitmentYear: app.horse.recruitmentYear,
          recruitmentNo: app.horse.recruitmentNo,
          horseName: app.horse.horseName || '',
          recruitmentName: app.horse.recruitmentName || '',
        }
      : undefined,
    annualBundle: app.annualBundle
      ? {
          annualBundleId: app.annualBundle.annualBundleId,
          name: app.annualBundle.name,
          fiscalYear: app.annualBundle.fiscalYear,
          shares: app.annualBundle.shares,
        }
      : undefined,
    rejected: app.rejected,
    contracted: app.contracted,
  }));
}

export function listAnnualBundleInvestmentApplications(params: {
  annualBundleIds?: number[];
  isWhole?: boolean;
  sortBy?: InvestmentApplicationSortKey;
  sortOrder?: 'asc' | 'desc';
}) {
  return listInvestmentApplications({
    typeFilter: 'annual_bundle',
    ...params,
  });
}

/**
 * 年次バンドル出資申込詳細取得
 */
export async function getAnnualBundleInvestmentApplicationDetail(annualBundleId: number) {
  const annualBundle = await client.annualBundle.findUnique({
    where: { annualBundleId },
    include: {
      horses: {
        include: {
          horse: true,
        },
      },
    },
  });

  if (!annualBundle) {
    throw new Error('Annual bundle not found');
  }

  // 有効な申込を取得
  const applications = await client.investmentApplication.findMany({
    where: {
      annualBundleId,
      rejected: false,
      contracted: true,
      member: {
        retirementDate: null,
      },
    },
    select: {
      requestedNumber: true,
      allocatedNumber: true,
    },
  });

  // 契約締結済みの申込の合計口数を計算
  const contractedShares = applications
    .filter((app) => app.allocatedNumber !== null && app.allocatedNumber > 0)
    .reduce((sum, app) => sum + (app.allocatedNumber ?? 0), 0);

  return {
    annualBundleId: annualBundle.annualBundleId,
    fiscalYear: annualBundle.fiscalYear,
    bundleName: annualBundle.name,
    contractedShares,
    recruitmentShares: annualBundle.shares,
    recruitmentTotalAmount: annualBundle.horses.reduce((sum, h) => sum + h.horse.amountTotal / h.horse.sharesTotal * annualBundle.shares, 0),
  };
}

type AcceptAnnualBundleInvestmentApplicationResult = {
  investmentApplicationId: number;
  success: boolean;
  errorMessage?: string;
  allocatedShares?: number;
  status?: 'applied' | 'accepted' | 'contractCompleted' | 'rejected';
};

export async function acceptAnnualBundleInvestmentApplications(
  acceptances: {
    investmentApplicationId: number;
    annualBundleId: number;
    allocatedShares: number;
  }[],
): Promise<AcceptAnnualBundleInvestmentApplicationResult[]> {
  const sorted = [...acceptances].sort((a, b) => a.investmentApplicationId - b.investmentApplicationId);
  const results: AcceptAnnualBundleInvestmentApplicationResult[] = [];

  for (const acceptance of sorted) {
    try {
      const application = await client.investmentApplication.findUnique({
        where: { investmentApplicationId: acceptance.investmentApplicationId },
        include: {
          member: {
            select: {
              retirementDate: true,
            },
          },
        },
      });

      if (!application || application.annualBundleId !== acceptance.annualBundleId) {
        results.push({
          investmentApplicationId: acceptance.investmentApplicationId,
          success: false,
          errorMessage: '対象の年度バンドル申込が存在しません',
        });
        continue;
      }

      if (application.rejected || application.contracted) {
        results.push({
          investmentApplicationId: acceptance.investmentApplicationId,
          success: false,
          errorMessage: '申込は処理対象外です',
          status: application.contracted ? 'contractCompleted' : application.rejected ? 'rejected' : undefined,
        });
        continue;
      }

      if (application.member?.retirementDate) {
        results.push({
          investmentApplicationId: acceptance.investmentApplicationId,
          success: false,
          errorMessage: '退会済み会員の申込は処理できません',
        });
        continue;
      }

      const updated = await client.investmentApplication.update({
        where: { investmentApplicationId: acceptance.investmentApplicationId },
        data: {
          allocatedNumber: acceptance.allocatedShares,
        },
      });

      results.push({
        investmentApplicationId: acceptance.investmentApplicationId,
        success: true,
        allocatedShares: updated.allocatedNumber ?? 0,
        status: (updated.allocatedNumber ?? 0) > 0 ? 'accepted' : 'applied',
      });
    } catch (error) {
      results.push({
        investmentApplicationId: acceptance.investmentApplicationId,
        success: false,
        errorMessage: error instanceof Error ? error.message : '不明なエラーが発生しました',
      });
    }
  }

  return results;
}

