import { err, ok, ResultAsync } from 'neverthrow';
import { Prisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class MembershipApplicationComplianceDocumentGroupReviewLogNotFoundError extends Error {
  readonly name = 'MembershipApplicationComplianceDocumentGroupReviewLogNotFoundError';
}

/**
 * membershipApplicationIdでMembershipApplicationComplianceDocumentGroupReviewLogレコードを検索
 */
export const findMembershipApplicationComplianceDocumentGroupReviewLogByMembershipApplicationId = ({
  membershipApplicationId,
}: {
  membershipApplicationId: number;
}) => {
  return ResultAsync.fromPromise(
    client.membershipApplicationComplianceDocumentGroupReviewLog.findFirst({
      where: {
        membershipApplication: {
          membershipApplicationId,
        },
      },
      include: {
        membershipApplication: true,
      },
    }),
    () => new DatabaseError('Failed to query membership application compliance document group review log')
  ).andThen((record) => {
    if (!record) {
      return err(new MembershipApplicationComplianceDocumentGroupReviewLogNotFoundError());
    }
    return ok(record);
  });
};

/**
 * MembershipApplicationComplianceDocumentGroupReviewLogレコードを作成
 */
export const createMembershipApplicationComplianceDocumentGroupReviewLog = (
  data: Omit<Prisma.MembershipApplicationComplianceDocumentGroupReviewLogCreateInput, 'membershipApplication' | 'timestamp'> & {
    membershipApplicationId: number;
  }
) => {
  const { membershipApplicationId, ...rest } = data;
  return ResultAsync.fromPromise(
    client.membershipApplicationComplianceDocumentGroupReviewLog.create({
      data: {
        ...rest,
        timestamp: new Date(),
        membershipApplication: { connect: { membershipApplicationId } },
      },
    }),
    () => new DatabaseError('Failed to create membership application compliance document group review log')
  );
};
