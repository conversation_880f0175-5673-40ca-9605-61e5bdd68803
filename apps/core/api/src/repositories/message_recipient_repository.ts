import { ResultAsync } from 'neverthrow';
import { Prisma, RecipientStatus, MessageType, MessageStatus } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

/**
 * 配信状況取得のパラメータ
 */
export interface GetDeliveryStatusParams {
  publicId: string;
  page: number;
  pageSize: number;
  statusFilter?: RecipientStatus;
}

/**
 * 会員別メッセージ一覧取得のパラメータ
 */
export interface ListMemberMessagesParams {
  memberId: number;
  page: number;
  pageSize: number;
  messageType?: MessageType;
  statusFilter?: RecipientStatus;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * メッセージの配信状況取得（public_idで検索）
 * @param params 検索・フィルター・ページネーション条件
 * @returns 配信状況一覧と総件数
 */
export const getDeliveryStatus = (params: GetDeliveryStatusParams) => {
  const { publicId, page, pageSize, statusFilter } = params;
  const skip = (page - 1) * pageSize;

  // 検索・フィルター条件の構築
  const whereConditions: Prisma.MessageRecipientWhereInput = {
    message: {
      publicId,
    },
  };

  if (statusFilter) {
    whereConditions.recipientStatus = statusFilter;
  }

  return ResultAsync.fromPromise(
    Promise.all([
      client.messageRecipient.findMany({
        where: whereConditions,
        include: {
          // 会員情報を取得するためのリレーション
          // 注意: MessageRecipientテーブルにはmemberIdがあるが、
          // Memberテーブルとのリレーションが定義されていない可能性があります
          // その場合は別途会員情報を取得する必要があります
        },
        skip,
        take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      client.messageRecipient.count({
        where: whereConditions,
      }),
    ]),
    () => new DatabaseError('Failed to get delivery status')
  ).map(([recipients, totalCount]) => ({
    recipients,
    totalCount,
    totalPages: Math.ceil(totalCount / pageSize),
  }));
};

/**
 * 会員情報を含む配信状況取得（public_idで検索）
 * 簡素化版：ステータスフィルタリングなしで基本機能のみ実装
 */
export const getDeliveryStatusWithMemberInfo = (params: GetDeliveryStatusParams) => {
  const { publicId, page, pageSize, statusFilter } = params;
  const skip = (page - 1) * pageSize;

  // 検索・フィルター条件の構築
  const whereConditions: Prisma.MessageRecipientWhereInput = {
    message: {
      publicId,
    },
  };

  if (statusFilter) {
    whereConditions.recipientStatus = statusFilter;
  }

  return ResultAsync.fromPromise(
    Promise.all([
      client.messageRecipient.findMany({
        where: whereConditions,
        skip,
        take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      client.messageRecipient.count({
        where: whereConditions,
      }),
    ]),
    () => new DatabaseError('Failed to get delivery status')
  ).andThen(([recipients, totalCount]) => {
    if (recipients.length === 0) {
      return ResultAsync.fromSafePromise(
        Promise.resolve({
          recipients: [],
          totalCount,
          totalPages: Math.ceil(totalCount / pageSize),
        })
      );
    }

    // 会員IDを取得
    const memberIds = recipients.map((r) => r.memberId);

    // 会員情報を取得
    return ResultAsync.fromPromise(
      client.member.findMany({
        where: {
          memberId: {
            in: memberIds,
          },
        },
      }),
      () => new DatabaseError('Failed to get member info')
    ).map((members) => {
      // 会員情報をマップに変換
      const memberMap = new Map(members.map((m) => [m.memberId, m]));

      // MessageRecipientと会員情報を結合
      const recipientsWithMemberInfo = recipients.map((recipient) => {
        const member = memberMap.get(recipient.memberId);
        return {
          ...recipient,
          memberName: member ? `${member.firstName} ${member.lastName}` : '',
          memberNumber: member ? member.memberNumber.toString() : '',
        };
      });

      return {
        recipients: recipientsWithMemberInfo,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
      };
    });
  });
};

/**
 * 会員別メッセージ一覧取得
 * @param params 検索・フィルター・ページネーション条件
 * @returns 会員が受信したメッセージ一覧と総件数
 */
export const listMemberMessages = (params: ListMemberMessagesParams) => {
  const { memberId, page, pageSize, messageType, statusFilter, sortBy = 'createdAt', sortOrder = 'desc' } = params;
  const skip = (page - 1) * pageSize;

  // MessageRecipientの検索条件
  const recipientWhereConditions: Prisma.MessageRecipientWhereInput = {
    memberId,
  };

  if (statusFilter) {
    recipientWhereConditions.recipientStatus = statusFilter;
  }

  // Messageの検索条件
  const messageWhereConditions: Prisma.MessageWhereInput = {
    status: MessageStatus.SENT, // 送信済みメッセージのみ
  };

  if (messageType) {
    messageWhereConditions.messageType = messageType;
  }

  // 結合条件
  recipientWhereConditions.message = messageWhereConditions;

  // ソート条件の構築
  const orderBy: Prisma.MessageRecipientOrderByWithRelationInput = {};
  if (sortBy === 'createdAt') {
    orderBy.message = { createdAt: sortOrder };
  } else if (sortBy === 'sentAt') {
    orderBy.message = { sentAt: sortOrder };
  } else if (sortBy === 'deliveredAt') {
    orderBy.deliveredAt = sortOrder;
  } else if (sortBy === 'readAt') {
    orderBy.readAt = sortOrder;
  } else {
    orderBy.message = { createdAt: sortOrder };
  }

  return ResultAsync.fromPromise(
    Promise.all([
      client.messageRecipient.findMany({
        where: recipientWhereConditions,
        include: {
          message: {
            include: {
              attachments: {
                orderBy: {
                  displayOrder: 'asc',
                },
              },
            },
          },
        },
        skip,
        take: pageSize,
        orderBy,
      }),
      client.messageRecipient.count({
        where: recipientWhereConditions,
      }),
    ]),
    () => new DatabaseError('Failed to list member messages')
  ).map(([recipients, totalCount]) => ({
    recipients,
    totalCount,
    totalPages: Math.ceil(totalCount / pageSize),
  }));
};
