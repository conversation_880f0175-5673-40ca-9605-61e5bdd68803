import { HorseIncomePrizeOrganizer, HorseIncomeOtherName, IncomeType } from '@hami/core-admin-api-schema/horse_income_service_pb';
import {
  listHorseIncomes,
  listHorseIncomePrize,
  getHorseIncomePrize,
  createHorseIncomePrize,
  updateHorseIncomePrize,
  deleteHorseIncomePrize,
  getHorseIncomeOther,
  createHorseIncomeOther,
  updateHorseIncomeOther,
  deleteHorseIncomeOther,
  listHorseIncomePrizeAllowanceNames,
  listHorseIncomeOther,
  HorseIncomePrizeNotFoundError,
  HorseIncomeOtherNotFoundError,
} from './horse_income_repository';

describe('horse_income_repository', () => {
  describe('listHorseIncomes', () => {
    it('指定された馬の賞金収入とその他収入をマージして一覧取得できる', async () => {
      // ===== Arrange =====
      const horse1 = await vPrisma.client.horse.create({
        data: {
          horseId: 1,
          horseName: 'テスト馬1',
          recruitmentYear: 2020,
          recruitmentNo: 1,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const horse2 = await vPrisma.client.horse.create({
        data: {
          horseId: 2,
          horseName: 'テスト馬2',
          recruitmentYear: 2019,
          recruitmentNo: 2,
          birthYear: 2019,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テスト馬2募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬2',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize1 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse1.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: 'テストレース1',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: 'テスト賞金収入1',
          closing: false,
        },
      });

      const other1 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse1.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 20,
          name: 'SALES_DIVIDEND',
          nameOther: '販売配当',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '手数料',
          otherFeeAmount: 10000,
          taxRate: "0.2",
          taxAmount: 100000,
          incomeAmount: 365000,
          note: 'テストその他収入1',
          closing: false,
        },
      });

      // 別の馬の収入（取得されないはず）
      const prize2 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse2.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 10,
          racePlace: '京都',
          raceName: 'テストレース2',
          raceResult: '2着',
          organizer: 'OTHER',
          mainPrizeAmount: 800000,
          appearanceFee: 40000,
          withholdingTax: 80000,
          commissionAmount: 40000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 880000,
          clubFeeAmount: 88000,
          taxAmount: 176000,
          incomeAmount: 616000,
          note: 'テスト賞金収入2',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomes({ horseId: horse1.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const incomes = result.value;
        expect(incomes.incomes.length).toBe(2);
        expect(incomes.incomes.some((income) => income.id === prize1.horseIncomePrizeId)).toBe(true);
        expect(incomes.incomes.some((income) => income.id === other1.horseIncomeOtherId)).toBe(true);
        // 別の馬の収入は含まれていないことを確認
        expect(incomes.incomes.some((income) => income.id === prize2.horseIncomePrizeId)).toBe(false);

        expect(incomes.incomes[0].horseName).toBe(horse1.horseName);
        expect(incomes.incomes[1].horseName).toBe(horse1.horseName);
      }
    });

    it('収入が存在しない馬の場合は空の配列を返す', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 999,
          horseName: 'テスト馬999',
          recruitmentYear: 2020,
          recruitmentNo: 999,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬999募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬999',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 10msec待つ
      await new Promise((resolve) => setTimeout(resolve, 100));

      // ===== Act =====
      const result = await listHorseIncomes({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.incomes).toEqual([]);
        expect(result.value.totalCount).toBe(0);
        expect(result.value.totalPages).toBe(0);
      }
    });

    it('ページネーションが正しく動作する', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 100,
          horseName: 'テスト馬100',
          recruitmentYear: 2020,
          recruitmentNo: 100,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬100募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬100',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 複数の収入を作成
      const prizes = [];
      const others = [];

      for (let i = 1; i <= 5; i++) {
        const prize = await vPrisma.client.horseIncomePrize.create({
          data: {
            horseId: horse.horseId,
            incomeYearMonth: 202400 + i,
            occurredYear: 2024,
            occurredMonth: i,
            occurredDay: i * 5,
            racePlace: `テスト場所${i}`,
            raceName: `テストレース${i}`,
            raceResult: `${i}着`,
            organizer: 'JRA',
            mainPrizeAmount: 100000 * i,
            appearanceFee: 5000 * i,
            withholdingTax: 10000 * i,
            commissionAmount: 5000 * i,
            clubFeeRate: "0.1",
            taxRate: "0.2",
            totalPrizeAmount: 110000 * i,
            clubFeeAmount: 11000 * i,
            taxAmount: 22000 * i,
            incomeAmount: 77000 * i,
            note: `テスト賞金収入${i}`,
            closing: false,
          },
        });
        prizes.push(prize);

        const other = await vPrisma.client.horseIncomeOther.create({
          data: {
            horseId: horse.horseId,
            incomeYearMonth: 202400 + i,
            occurredYear: 2024,
            occurredMonth: i,
            occurredDay: i * 5 + 1,
            name: 'SALES_DIVIDEND',
            nameOther: `販売配当${i}`,
            amount: 50000 * i,
            salesCommission: 2500 * i,
            otherFeeName: '手数料',
            otherFeeAmount: 1000 * i,
            taxRate: "0.2",
            taxAmount: 10000 * i,
            incomeAmount: 36500 * i,
            note: `テストその他収入${i}`,
            closing: false,
          },
        });
        others.push(other);
      }

      // ===== Act =====
      const result1 = await listHorseIncomes({ horseId: horse.horseId, limit: 3, offset: 0 });
      const result2 = await listHorseIncomes({ horseId: horse.horseId, limit: 3, offset: 3 });

      // ===== Assert =====
      expect(result1.isOk()).toBe(true);
      expect(result2.isOk()).toBe(true);

      if (result1.isOk() && result2.isOk()) {
        expect(result1.value.incomes.length).toBe(3);
        expect(result2.value.incomes.length).toBe(3);
        expect(result1.value.totalCount).toBe(10);
        expect(result2.value.totalCount).toBe(10);
        expect(result1.value.totalPages).toBe(4);
        expect(result2.value.totalPages).toBe(4);

        // 重複がないことを確認
        const allIds = [...result1.value.incomes.map((i) => i.id), ...result2.value.incomes.map((i) => i.id)];
        const uniqueIds = new Set(allIds);
        expect(allIds.length).toBe(uniqueIds.size);
      }
    });

    it('ソート順が正しい（発生年月日降順、ID降順）', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 200,
          horseName: 'テスト馬200',
          recruitmentYear: 2020,
          recruitmentNo: 200,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬200募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬200',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 古い日付の収入
      const oldPrize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 1,
          racePlace: '東京',
          raceName: '古いレース',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 100000,
          appearanceFee: 5000,
          withholdingTax: 10000,
          commissionAmount: 5000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 110000,
          clubFeeAmount: 11000,
          taxAmount: 22000,
          incomeAmount: 77000,
          note: '古い賞金収入',
          closing: false,
        },
      });

      // 新しい日付の収入
      const newPrize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 15,
          racePlace: '京都',
          raceName: '新しいレース',
          raceResult: '2着',
          organizer: 'OTHER',
          mainPrizeAmount: 200000,
          appearanceFee: 10000,
          withholdingTax: 20000,
          commissionAmount: 10000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 220000,
          clubFeeAmount: 22000,
          taxAmount: 44000,
          incomeAmount: 154000,
          note: '新しい賞金収入',
          closing: false,
        },
      });

      // ===== Act =====
      // 10msec待つ
      await new Promise((resolve) => setTimeout(resolve, 100));

      const result = await listHorseIncomes({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const incomes = result.value;
        expect(incomes.incomes.length).toBe(2);
        // 新しい日付の収入が最初に来ることを確認
        expect(incomes.incomes[0].id).toBe(newPrize.horseIncomePrizeId);
        expect(incomes.incomes[1].id).toBe(oldPrize.horseIncomePrizeId);
      }
    });

    it('収入種別が正しく設定される', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 300,
          horseName: 'テスト馬300',
          recruitmentYear: 2020,
          recruitmentNo: 300,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬300募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬300',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: 'テストレース',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 100000,
          appearanceFee: 5000,
          withholdingTax: 10000,
          commissionAmount: 5000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 110000,
          clubFeeAmount: 11000,
          taxAmount: 22000,
          incomeAmount: 77000,
          note: 'テスト賞金収入',
          closing: false,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 20,
          name: 'SALES_DIVIDEND',
          nameOther: '販売配当',
          amount: 50000,
          salesCommission: 2500,
          otherFeeName: '手数料',
          otherFeeAmount: 1000,
          taxRate: "0.2",
          taxAmount: 10000,
          incomeAmount: 36500,
          note: 'テストその他収入',
          closing: false,
        },
      });

      // ===== Act =====
      // 10msec待つ
      await new Promise((resolve) => setTimeout(resolve, 100));

      const result = await listHorseIncomes({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const incomes = result.value;
        expect(incomes.incomes.length).toBe(2);

        const prizeIncome = incomes.incomes.find((income) => income.id === prize.horseIncomePrizeId);
        const otherIncome = incomes.incomes.find((income) => income.id === other.horseIncomeOtherId);

        expect(prizeIncome?.incomeType).toBe(IncomeType.PRIZE);
        expect(otherIncome?.incomeType).toBe(IncomeType.OTHER);
      }
    });

    it('収入名が正しく設定される', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 400,
          horseName: 'テスト馬400',
          recruitmentYear: 2020,
          recruitmentNo: 400,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬400募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬400',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: '東京優駿',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 100000,
          appearanceFee: 5000,
          withholdingTax: 10000,
          commissionAmount: 5000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 110000,
          clubFeeAmount: 11000,
          taxAmount: 22000,
          incomeAmount: 77000,
          note: 'テスト賞金収入',
          closing: false,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 20,
          name: 'SALES_DIVIDEND',
          nameOther: '特別配当金',
          amount: 50000,
          salesCommission: 2500,
          otherFeeName: '手数料',
          otherFeeAmount: 1000,
          taxRate: "0.2",
          taxAmount: 10000,
          incomeAmount: 36500,
          note: 'テストその他収入',
          closing: false,
        },
      });

      // ===== Act =====
      // 10msec待つ
      await new Promise((resolve) => setTimeout(resolve, 100));

      const result = await listHorseIncomes({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const incomes = result.value;
        expect(incomes.incomes.length).toBe(2);

        const prizeIncome = incomes.incomes.find((income) => income.id === prize.horseIncomePrizeId);
        const otherIncome = incomes.incomes.find((income) => income.id === other.horseIncomeOtherId);

        // 賞金収入の場合はレース名が設定される
        expect(prizeIncome?.name).toBe('東京優駿');
        // その他収入の場合はnameOtherが設定される
        expect(otherIncome?.name).toBe('SALES_DIVIDEND');
      }
    });

    it('取得した項目の各フィールドが正しく設定されている', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 600,
          horseName: 'テスト馬600',
          recruitmentYear: 2020,
          recruitmentNo: 600,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬600募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬600',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 25,
          racePlace: '中山',
          raceName: '有馬記念',
          raceResult: '3着',
          organizer: 'JRA',
          mainPrizeAmount: 5000000,
          appearanceFee: 100000,
          withholdingTax: 500000,
          commissionAmount: 100000,
          clubFeeRate: 0.08,
          taxRate: "0.15",
          totalPrizeAmount: 5200000,
          clubFeeAmount: 416000,
          taxAmount: 780000,
          incomeAmount: 4004000,
          note: '年末の大レース',
          closing: true,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 30,
          name: 'INSURANCE',
          nameOther: '保険金',
          amount: 1000000,
          salesCommission: 50000,
          otherFeeName: '保険手数料',
          otherFeeAmount: 20000,
          taxRate: "0.1",
          taxAmount: 100000,
          incomeAmount: 830000,
          note: '年末の保険金',
          closing: true,
        },
      });

      // ===== Act =====
      // 10msec待つ
      await new Promise((resolve) => setTimeout(resolve, 100));

      const result = await listHorseIncomes({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const incomes = result.value;
        expect(incomes.incomes.length).toBe(2);

        // 賞金収入の詳細チェック
        const prizeIncome = incomes.incomes.find((income) => income.id === prize.horseIncomePrizeId);
        expect(prizeIncome).toBeDefined();
        if (prizeIncome) {
          expect(prizeIncome.id).toBe(prize.horseIncomePrizeId);
          expect(prizeIncome.closing).toBe(true);
          expect(prizeIncome.incomeYearMonth).toBe(202412);
          expect(prizeIncome.occurredYear).toBe(2024);
          expect(prizeIncome.occurredMonth).toBe(12);
          expect(prizeIncome.occurredDay).toBe(25);
          expect(prizeIncome.incomeType).toBe(IncomeType.PRIZE);
          expect(prizeIncome.name).toBe('有馬記念');
          expect(prizeIncome.amount).toBe(4004000);
        }

        // その他収入の詳細チェック
        const otherIncome = incomes.incomes.find((income) => income.id === other.horseIncomeOtherId);
        expect(otherIncome).toBeDefined();
        if (otherIncome) {
          expect(otherIncome.id).toBe(other.horseIncomeOtherId);
          expect(otherIncome.closing).toBe(true);
          expect(otherIncome.incomeYearMonth).toBe(202412);
          expect(otherIncome.occurredYear).toBe(2024);
          expect(otherIncome.occurredMonth).toBe(12);
          expect(otherIncome.occurredDay).toBe(30);
          expect(otherIncome.incomeType).toBe(IncomeType.OTHER);
          expect(otherIncome.name).toBe('INSURANCE');
          expect(otherIncome.amount).toBe(830000);
        }
      }
    });

    it('複数のその他収入種別が正しく処理される', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 700,
          horseName: 'テスト馬700',
          recruitmentYear: 2020,
          recruitmentNo: 700,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬700募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬700',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const salesDividend = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          name: 'SALES_DIVIDEND',
          nameOther: '販売配当金',
          amount: 300000,
          salesCommission: 15000,
          otherFeeName: '手数料',
          otherFeeAmount: 5000,
          taxRate: "0.2",
          taxAmount: 60000,
          incomeAmount: 220000,
          note: '販売配当',
          closing: false,
        },
      });

      const insurance = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 20,
          name: 'INSURANCE',
          nameOther: '保険金',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '保険手数料',
          otherFeeAmount: 10000,
          taxRate: "0.1",
          taxAmount: 50000,
          incomeAmount: 415000,
          note: '保険金',
          closing: false,
        },
      });

      const sympathy = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 10,
          name: 'SYMPATHY',
          nameOther: '見舞金',
          amount: 100000,
          salesCommission: 5000,
          otherFeeName: '手数料',
          otherFeeAmount: 2000,
          taxRate: "0.05",
          taxAmount: 5000,
          incomeAmount: 88000,
          note: '見舞金',
          closing: false,
        },
      });

      const grant = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202404,
          occurredYear: 2024,
          occurredMonth: 4,
          occurredDay: 5,
          name: 'GRANT',
          nameOther: '助成金',
          amount: 200000,
          salesCommission: 10000,
          otherFeeName: '申請手数料',
          otherFeeAmount: 3000,
          taxRate: "0.0",
          taxAmount: 0,
          incomeAmount: 187000,
          note: '助成金',
          closing: false,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202405,
          occurredYear: 2024,
          occurredMonth: 5,
          occurredDay: 1,
          name: 'OTHER',
          nameOther: 'その他収入',
          amount: 150000,
          salesCommission: 7500,
          otherFeeName: '手数料',
          otherFeeAmount: 2500,
          taxRate: "0.15",
          taxAmount: 22500,
          incomeAmount: 120000,
          note: 'その他',
          closing: false,
        },
      });

      // ===== Act =====
      // 10msec待つ
      await new Promise((resolve) => setTimeout(resolve, 100));

      const result = await listHorseIncomes({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const incomes = result.value;
        expect(incomes.incomes.length).toBe(5);

        // 各その他収入のnameOtherが正しく設定されていることを確認
        const salesDividendIncome = incomes.incomes.find((income) => income.id === salesDividend.horseIncomeOtherId);
        const insuranceIncome = incomes.incomes.find((income) => income.id === insurance.horseIncomeOtherId);
        const sympathyIncome = incomes.incomes.find((income) => income.id === sympathy.horseIncomeOtherId);
        const grantIncome = incomes.incomes.find((income) => income.id === grant.horseIncomeOtherId);
        const otherIncome = incomes.incomes.find((income) => income.id === other.horseIncomeOtherId);

        expect(salesDividendIncome?.name).toBe('SALES_DIVIDEND');
        expect(insuranceIncome?.name).toBe('INSURANCE');
        expect(sympathyIncome?.name).toBe('SYMPATHY');
        expect(grantIncome?.name).toBe('GRANT');
        expect(otherIncome?.name).toBe('OTHER');

        // 全ての収入種別がOTHERであることを確認
        [salesDividendIncome, insuranceIncome, sympathyIncome, grantIncome, otherIncome].forEach((income) => {
          expect(income?.incomeType).toBe(IncomeType.OTHER);
        });
      }
    });

    it('賞金収入のraceNameがnullの場合の処理', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 800,
          horseName: 'テスト馬800',
          recruitmentYear: 2020,
          recruitmentNo: 800,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬800募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬800',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: '', // 空文字列
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 100000,
          appearanceFee: 5000,
          withholdingTax: 10000,
          commissionAmount: 5000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 110000,
          clubFeeAmount: 11000,
          taxAmount: 22000,
          incomeAmount: 77000,
          note: 'テスト賞金収入',
          closing: false,
        },
      });

      // ===== Act =====
      // 10msec待つ
      await new Promise((resolve) => setTimeout(resolve, 100));

      const result = await listHorseIncomes({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const incomes = result.value;
        expect(incomes.incomes.length).toBe(1);

        const prizeIncome = incomes.incomes[0];
        expect(prizeIncome.name).toBe(''); // 空文字列がそのまま設定される
        expect(prizeIncome.incomeType).toBe(IncomeType.PRIZE);
      }
    });

    it('その他収入のnameOtherが空の場合の処理', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 900,
          horseName: 'テスト馬900',
          recruitmentYear: 2020,
          recruitmentNo: 900,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬900募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬900',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 20,
          name: 'SALES_DIVIDEND',
          nameOther: '', // 空文字列
          amount: 50000,
          salesCommission: 2500,
          otherFeeName: '手数料',
          otherFeeAmount: 1000,
          taxRate: "0.2",
          taxAmount: 10000,
          incomeAmount: 36500,
          note: 'テストその他収入',
          closing: false,
        },
      });

      // ===== Act =====
      // 10msec待つ
      await new Promise((resolve) => setTimeout(resolve, 100));

      const result = await listHorseIncomes({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const incomes = result.value;
        expect(incomes.incomes.length).toBe(1);

        const otherIncome = incomes.incomes[0];
        // nameOtherが空の場合、nameフィールド（enum値）が使用される
        expect(otherIncome.name).toBe('SALES_DIVIDEND');
        expect(otherIncome.incomeType).toBe(IncomeType.OTHER);
      }
    });

    it('馬を指定しない場合、全馬の収入一覧を取得できる', async () => {
      // ===== Arrange =====
      const horse1 = await vPrisma.client.horse.create({
        data: {
          horseId: 950,
          horseName: 'テスト馬950',
          recruitmentYear: 2020,
          recruitmentNo: 950,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬950募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬950',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const horse2 = await vPrisma.client.horse.create({
        data: {
          horseId: 951,
          horseName: 'テスト馬951',
          recruitmentYear: 2020,
          recruitmentNo: 951,
          birthYear: 2020,
          birthMonth: 2,
          birthDay: 15,
          recruitmentName: 'テスト馬951募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬951',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const horse3 = await vPrisma.client.horse.create({
        data: {
          horseId: 952,
          horseName: 'テスト馬952',
          recruitmentYear: 2020,
          recruitmentNo: 952,
          birthYear: 2020,
          birthMonth: 3,
          birthDay: 30,
          recruitmentName: 'テスト馬952募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬952',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 馬1の収入
      const prize1 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse1.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: 'テストレース1',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: 'テスト賞金収入1',
          closing: false,
        },
      });

      const other1 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse1.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 20,
          name: 'SALES_DIVIDEND',
          nameOther: '販売配当',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '手数料',
          otherFeeAmount: 10000,
          taxRate: "0.2",
          taxAmount: 100000,
          incomeAmount: 365000,
          note: 'テストその他収入1',
          closing: false,
        },
      });

      // 馬2の収入
      const prize2 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse2.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 10,
          racePlace: '京都',
          raceName: 'テストレース2',
          raceResult: '2着',
          organizer: 'OTHER',
          mainPrizeAmount: 800000,
          appearanceFee: 40000,
          withholdingTax: 80000,
          commissionAmount: 40000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 880000,
          clubFeeAmount: 88000,
          taxAmount: 176000,
          incomeAmount: 616000,
          note: 'テスト賞金収入2',
          closing: false,
        },
      });

      // 馬3の収入
      const other2 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse3.horseId,
          incomeYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 5,
          name: 'INSURANCE',
          nameOther: '保険金',
          amount: 300000,
          salesCommission: 15000,
          otherFeeName: '保険手数料',
          otherFeeAmount: 5000,
          taxRate: "0.1",
          taxAmount: 30000,
          incomeAmount: 250000,
          note: 'テストその他収入2',
          closing: false,
        },
      });

      // ===== Act =====
      // 10msec待つ
      await new Promise((resolve) => setTimeout(resolve, 100));

      const result = await listHorseIncomes({});

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const incomes = result.value;
        expect(incomes.incomes.length).toBe(4); // 全馬の収入が取得される

        // 全ての収入が含まれていることを確認
        const incomeIds = incomes.incomes.map(income => income.id);
        expect(incomeIds).toContain(prize1.horseIncomePrizeId);
        expect(incomeIds).toContain(other1.horseIncomeOtherId);
        expect(incomeIds).toContain(prize2.horseIncomePrizeId);
        expect(incomeIds).toContain(other2.horseIncomeOtherId);

        // horseNameが正しく設定されていることを確認
        const horse1Incomes = incomes.incomes.filter(income => income.horseId === horse1.horseId);
        const horse2Incomes = incomes.incomes.filter(income => income.horseId === horse2.horseId);
        const horse3Incomes = incomes.incomes.filter(income => income.horseId === horse3.horseId);

        horse1Incomes.forEach(income => {
          expect(income.horseName).toBe('テスト馬950');
        });
        horse2Incomes.forEach(income => {
          expect(income.horseName).toBe('テスト馬951');
        });
        horse3Incomes.forEach(income => {
          expect(income.horseName).toBe('テスト馬952');
        });

        // 収入種別が正しく設定されていることを確認
        const prizeIncomes = incomes.incomes.filter(income => income.incomeType === IncomeType.PRIZE);
        const otherIncomes = incomes.incomes.filter(income => income.incomeType === IncomeType.OTHER);

        expect(prizeIncomes.length).toBe(2);
        expect(otherIncomes.length).toBe(2);
      }
    });

    it('馬を指定しない場合のページネーションが正しく動作する', async () => {
      // ===== Arrange =====
      const horse1 = await vPrisma.client.horse.create({
        data: {
          horseId: 960,
          horseName: 'テスト馬960',
          recruitmentYear: 2020,
          recruitmentNo: 960,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬960募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬960',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const horse2 = await vPrisma.client.horse.create({
        data: {
          horseId: 961,
          horseName: 'テスト馬961',
          recruitmentYear: 2020,
          recruitmentNo: 961,
          birthYear: 2020,
          birthMonth: 2,
          birthDay: 15,
          recruitmentName: 'テスト馬961募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬961',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 複数の収入を作成（合計6件）
      const incomes = [];
      for (let i = 1; i <= 3; i++) {
        const prize = await vPrisma.client.horseIncomePrize.create({
          data: {
            horseId: horse1.horseId,
            incomeYearMonth: 202400 + i,
            occurredYear: 2024,
            occurredMonth: i,
            occurredDay: i * 5,
            racePlace: `テスト場所${i}`,
            raceName: `テストレース${i}`,
            raceResult: `${i}着`,
            organizer: 'JRA',
            mainPrizeAmount: 100000 * i,
            appearanceFee: 5000 * i,
            withholdingTax: 10000 * i,
            commissionAmount: 5000 * i,
            clubFeeRate: "0.1",
            taxRate: "0.2",
            totalPrizeAmount: 110000 * i,
            clubFeeAmount: 11000 * i,
            taxAmount: 22000 * i,
            incomeAmount: 77000 * i,
            note: `テスト賞金収入${i}`,
            closing: false,
          },
        });
        incomes.push(prize);

        const other = await vPrisma.client.horseIncomeOther.create({
          data: {
            horseId: horse2.horseId,
            incomeYearMonth: 202400 + i,
            occurredYear: 2024,
            occurredMonth: i,
            occurredDay: i * 5 + 1,
            name: 'SALES_DIVIDEND',
            nameOther: `販売配当${i}`,
            amount: 50000 * i,
            salesCommission: 2500 * i,
            otherFeeName: '手数料',
            otherFeeAmount: 1000 * i,
            taxRate: "0.2",
            taxAmount: 10000 * i,
            incomeAmount: 36500 * i,
            note: `テストその他収入${i}`,
            closing: false,
          },
        });
        incomes.push(other);
      }

      // ===== Act =====
      const result1 = await listHorseIncomes({ limit: 3, offset: 0 });
      const result2 = await listHorseIncomes({ limit: 3, offset: 3 });

      // ===== Assert =====
      expect(result1.isOk()).toBe(true);
      expect(result2.isOk()).toBe(true);

      if (result1.isOk() && result2.isOk()) {
        expect(result1.value.incomes.length).toBe(3);
        expect(result2.value.incomes.length).toBe(3);
        expect(result1.value.totalCount).toBe(6);
        expect(result2.value.totalCount).toBe(6);
        expect(result1.value.totalPages).toBe(2);
        expect(result2.value.totalPages).toBe(2);

        // 重複がないことを確認
        const allIds = [...result1.value.incomes.map((i) => i.id), ...result2.value.incomes.map((i) => i.id)];
        const uniqueIds = new Set(allIds);
        expect(allIds.length).toBe(uniqueIds.size);

        // 全ての収入が含まれていることを確認
        const allIncomeIds = incomes.map(income => {
          if ('horseIncomePrizeId' in income) {
            return income.horseIncomePrizeId;
          } else {
            return income.horseIncomeOtherId;
          }
        });
        allIncomeIds.forEach(id => {
          expect(allIds).toContain(id);
        });
      }
    });
  });

  describe('listHorseIncomePrize', () => {
    it('指定された馬の賞金収入一覧を取得できる', async () => {
      // ===== Arrange =====
      const horse1 = await vPrisma.client.horse.create({
        data: {
          horseId: 10,
          horseName: 'テスト馬10',
          recruitmentYear: 2020,
          recruitmentNo: 10,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬10募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬10',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const horse2 = await vPrisma.client.horse.create({
        data: {
          horseId: 11,
          horseName: 'テスト馬11',
          recruitmentYear: 2019,
          recruitmentNo: 11,
          birthYear: 2019,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テスト馬11募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬11',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize1 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse1.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: 'テストレース1',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: 'テスト賞金収入1',
          closing: false,
        },
      });

      const prize2 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse1.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 20,
          racePlace: '京都',
          raceName: 'テストレース2',
          raceResult: '2着',
          organizer: 'OTHER',
          mainPrizeAmount: 800000,
          appearanceFee: 40000,
          withholdingTax: 80000,
          commissionAmount: 40000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 880000,
          clubFeeAmount: 88000,
          taxAmount: 176000,
          incomeAmount: 616000,
          note: 'テスト賞金収入2',
          closing: false,
        },
      });

      // 別の馬の賞金収入（取得されないはず）
      const prize3 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse2.horseId,
          incomeYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 10,
          racePlace: '阪神',
          raceName: 'テストレース3',
          raceResult: '3着',
          organizer: 'JRA',
          mainPrizeAmount: 600000,
          appearanceFee: 30000,
          withholdingTax: 60000,
          commissionAmount: 30000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 660000,
          clubFeeAmount: 66000,
          taxAmount: 132000,
          incomeAmount: 462000,
          note: 'テスト賞金収入3',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomePrize({ horseId: horse1.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const prizes = result.value;
        expect(prizes.length).toBe(2);
        expect(prizes.some(prize => prize.horseIncomePrizeId === prize1.horseIncomePrizeId)).toBe(true);
        expect(prizes.some(prize => prize.horseIncomePrizeId === prize2.horseIncomePrizeId)).toBe(true);
        // 別の馬の賞金収入は含まれていないことを確認
        expect(prizes.some(prize => prize.horseIncomePrizeId === prize3.horseIncomePrizeId)).toBe(false);
        
        // データの構造を確認
        const firstPrize = prizes.find(prize => prize.horseIncomePrizeId === prize1.horseIncomePrizeId);
        expect(firstPrize).toBeDefined();
        if (firstPrize) {
          expect(firstPrize.horseId).toBe(horse1.horseId);
          expect(firstPrize.racePlace).toBe('東京');
          expect(firstPrize.raceName).toBe('テストレース1');
          expect(firstPrize.raceResult).toBe('1着');
          expect(firstPrize.organizer).toBe(HorseIncomePrizeOrganizer.JRA);
          expect(firstPrize.mainPrizeAmount).toBe(1000000);
          expect(firstPrize.incomeAmount).toBe(770000);
        }
      }
    });

    it('指定された収入年月でフィルタリングできる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 12,
          horseName: 'テスト馬12',
          recruitmentYear: 2020,
          recruitmentNo: 12,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬12募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬12',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize1 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: '1月レース',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: '1月の賞金収入',
          closing: false,
        },
      });

      const prize2 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 20,
          racePlace: '京都',
          raceName: '2月レース',
          raceResult: '2着',
          organizer: 'OTHER',
          mainPrizeAmount: 800000,
          appearanceFee: 40000,
          withholdingTax: 80000,
          commissionAmount: 40000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 880000,
          clubFeeAmount: 88000,
          taxAmount: 176000,
          incomeAmount: 616000,
          note: '2月の賞金収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomePrize({ 
        horseId: horse.horseId, 
        incomeYearMonth: 202401 
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const prizes = result.value;
        expect(prizes.length).toBe(1);
        expect(prizes[0].horseIncomePrizeId).toBe(prize1.horseIncomePrizeId);
        expect(prizes[0].incomeYearMonth).toBe(202401);
        expect(prizes[0].raceName).toBe('1月レース');
      }
    });

    it('ページネーションが正しく動作する', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 13,
          horseName: 'テスト馬13',
          recruitmentYear: 2020,
          recruitmentNo: 13,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬13募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬13',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 3件の賞金収入を作成（発生年月日を降順になるように設定）
      const prize1 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 30,
          racePlace: 'テスト場所1',
          raceName: 'テストレース1',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: 'テスト賞金収入1',
          closing: false,
        },
      });

      const prize2 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 20,
          racePlace: 'テスト場所2',
          raceName: 'テストレース2',
          raceResult: '2着',
          organizer: 'OTHER',
          mainPrizeAmount: 2000000,
          appearanceFee: 100000,
          withholdingTax: 200000,
          commissionAmount: 100000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 2200000,
          clubFeeAmount: 220000,
          taxAmount: 440000,
          incomeAmount: 1540000,
          note: 'テスト賞金収入2',
          closing: false,
        },
      });

      const prize3 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 10,
          racePlace: 'テスト場所3',
          raceName: 'テストレース3',
          raceResult: '3着',
          organizer: 'JRA',
          mainPrizeAmount: 3000000,
          appearanceFee: 150000,
          withholdingTax: 300000,
          commissionAmount: 150000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 3300000,
          clubFeeAmount: 330000,
          taxAmount: 660000,
          incomeAmount: 2310000,
          note: 'テスト賞金収入3',
          closing: false,
        },
      });

      // ===== Act =====
      const result1 = await listHorseIncomePrize({ 
        horseId: horse.horseId, 
        limit: 2, 
        offset: 0 
      });

      const result2 = await listHorseIncomePrize({ 
        horseId: horse.horseId, 
        limit: 2, 
        offset: 2 
      });

      // ===== Assert =====
      expect(result1.isOk()).toBe(true);
      expect(result2.isOk()).toBe(true);
      
      if (result1.isOk() && result2.isOk()) {
        expect(result1.value.length).toBe(2);
        expect(result2.value.length).toBe(1);
        
        // ソート順は発生年月日降順なので、prize1(3月30日) → prize2(2月20日) → prize3(1月10日)の順
        // 最初の2件（prize1, prize2）が取得されていることを確認
        expect(result1.value.some(prize => prize.horseIncomePrizeId === prize1.horseIncomePrizeId)).toBe(true);
        expect(result1.value.some(prize => prize.horseIncomePrizeId === prize2.horseIncomePrizeId)).toBe(true);
        
        // 3件目（prize3）が取得されていることを確認
        expect(result2.value[0].horseIncomePrizeId).toBe(prize3.horseIncomePrizeId);
      }
    });

    it('賞金収入が存在しない馬の場合は空の配列を返す', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 14,
          horseName: 'テスト馬14',
          recruitmentYear: 2020,
          recruitmentNo: 14,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬14募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬14',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomePrize({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toEqual([]);
      }
    });

    it('存在しない収入年月でフィルタリングした場合は空の配列を返す', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 15,
          horseName: 'テスト馬15',
          recruitmentYear: 2020,
          recruitmentNo: 15,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬15募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬15',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: 'テストレース',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: 'テスト賞金収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomePrize({ 
        horseId: horse.horseId, 
        incomeYearMonth: 202412 
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toEqual([]);
      }
    });

    it('取得されたデータの構造が正しいこと', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 16,
          horseName: 'テスト馬16',
          recruitmentYear: 2020,
          recruitmentNo: 16,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬16募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬16',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: 'テストレース',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: 'テスト賞金収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomePrize({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const prizes = result.value;
        expect(prizes.length).toBe(1);
        
        const prizeData = prizes[0];
        expect(prizeData).toHaveProperty('horseIncomePrizeId');
        expect(prizeData).toHaveProperty('horseId');
        expect(prizeData).toHaveProperty('incomeYearMonth');
        expect(prizeData).toHaveProperty('occurredYear');
        expect(prizeData).toHaveProperty('occurredMonth');
        expect(prizeData).toHaveProperty('occurredDay');
        expect(prizeData).toHaveProperty('racePlace');
        expect(prizeData).toHaveProperty('raceName');
        expect(prizeData).toHaveProperty('raceResult');
        expect(prizeData).toHaveProperty('organizer');
        expect(prizeData).toHaveProperty('mainPrizeAmount');
        expect(prizeData).toHaveProperty('appearanceFee');
        expect(prizeData).toHaveProperty('withholdingTax');
        expect(prizeData).toHaveProperty('commissionAmount');
        expect(prizeData).toHaveProperty('clubFeeRate');
        expect(prizeData).toHaveProperty('taxRate');
        expect(prizeData).toHaveProperty('totalPrizeAmount');
        expect(prizeData).toHaveProperty('clubFeeAmount');
        expect(prizeData).toHaveProperty('taxAmount');
        expect(prizeData).toHaveProperty('incomeAmount');
        expect(prizeData).toHaveProperty('note');
        expect(prizeData).toHaveProperty('closing');
        expect(prizeData).toHaveProperty('allowances');
      }
    });

    it('ソート順が正しいこと（発生年月日降順、ID降順）', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 17,
          horseName: 'テスト馬17',
          recruitmentYear: 2020,
          recruitmentNo: 17,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬17募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬17',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 異なる日付で3件の賞金収入を作成
      const prize1 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          racePlace: '東京',
          raceName: '1月15日レース',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: '1月15日の賞金収入',
          closing: false,
        },
      });

      const prize2 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 20,
          racePlace: '京都',
          raceName: '1月20日レース',
          raceResult: '2着',
          organizer: 'OTHER',
          mainPrizeAmount: 800000,
          appearanceFee: 40000,
          withholdingTax: 80000,
          commissionAmount: 40000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 880000,
          clubFeeAmount: 88000,
          taxAmount: 176000,
          incomeAmount: 616000,
          note: '1月20日の賞金収入',
          closing: false,
        },
      });

      const prize3 = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 10,
          racePlace: '阪神',
          raceName: '2月10日レース',
          raceResult: '3着',
          organizer: 'JRA',
          mainPrizeAmount: 600000,
          appearanceFee: 30000,
          withholdingTax: 60000,
          commissionAmount: 30000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 660000,
          clubFeeAmount: 66000,
          taxAmount: 132000,
          incomeAmount: 462000,
          note: '2月10日の賞金収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomePrize({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const prizes = result.value;
        expect(prizes.length).toBe(3);
        
        // 発生年月日降順でソートされていることを確認
        // 2月10日 → 1月20日 → 1月15日の順
        expect(prizes[0].horseIncomePrizeId).toBe(prize3.horseIncomePrizeId);
        expect(prizes[1].horseIncomePrizeId).toBe(prize2.horseIncomePrizeId);
        expect(prizes[2].horseIncomePrizeId).toBe(prize1.horseIncomePrizeId);
      }
    });
  });

  describe('getHorseIncomePrize', () => {
    it('既存の賞金収入を取得できる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 2,
          horseName: 'テスト馬2',
          recruitmentYear: 2019,
          recruitmentNo: 2,
          birthYear: 2019,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テスト馬2募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬2',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 10,
          racePlace: '京都',
          raceName: 'テストレース2',
          raceResult: '2着',
          organizer: 'OTHER',
          mainPrizeAmount: 800000,
          appearanceFee: 40000,
          withholdingTax: 80000,
          commissionAmount: 40000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 880000,
          clubFeeAmount: 88000,
          taxAmount: 176000,
          incomeAmount: 616000,
          note: 'テスト賞金収入2',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await getHorseIncomePrize({ id: prize.horseIncomePrizeId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const foundPrize = result.value;
        expect(foundPrize.horseIncomePrizeId).toBe(prize.horseIncomePrizeId);
        expect(foundPrize.horseId).toBe(horse.horseId);
        expect(foundPrize.raceName).toBe('テストレース2');
        expect(foundPrize.organizer).toBe(HorseIncomePrizeOrganizer.OTHER);
      }
    });

    it('存在しない賞金収入IDで取得しようとした場合はエラーになる', async () => {
      // ===== Act =====
      const result = await getHorseIncomePrize({ id: 99999 });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseIncomePrizeNotFoundError);
      }
    });
  });

  describe('createHorseIncomePrize', () => {
    it('新しい賞金収入を作成できる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 3,
          horseName: 'テスト馬3',
          recruitmentYear: 2021,
          recruitmentNo: 3,
          birthYear: 2021,
          birthMonth: 5,
          birthDay: 20,
          recruitmentName: 'テスト馬3募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬3',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const createData = {
        horseId: horse.horseId,
        incomeYearMonth: 202403,
        occurredYear: 2024,
        occurredMonth: 3,
        occurredDay: 5,
        racePlace: '阪神',
        raceName: 'テストレース3',
        raceResult: '3着',
        organizer: HorseIncomePrizeOrganizer.JRA,
        mainPrizeAmount: 600000,
        appearanceFee: 30000,
        withholdingTax: 60000,
        commissionAmount: 30000,
        clubFeeRate: "0.1",
        taxRate: "0.2",
        totalPrizeAmount: 660000,
        clubFeeAmount: 66000,
        taxAmount: 132000,
        incomeAmount: 462000,
        note: 'テスト賞金収入3',
        allowances: [
          { name: '特別手当', amount: 50000 },
          { name: '交通費', amount: 10000 },
        ],
      };

      // ===== Act =====
      const result = await createHorseIncomePrize(createData);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const createdPrize = result.value;
        expect(createdPrize.horseId).toBe(horse.horseId);
        expect(createdPrize.raceName).toBe('テストレース3');
        expect(createdPrize.organizer).toBe(HorseIncomePrizeOrganizer.JRA);
        expect(createdPrize.allowances.length).toBe(2);
        expect(createdPrize.allowances[0].name).toBe('特別手当');
        expect(createdPrize.allowances[0].amount).toBe(50000);
      }
    });
  });

  describe('updateHorseIncomePrize', () => {
    it('既存の賞金収入を更新できる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 4,
          horseName: 'テスト馬4',
          recruitmentYear: 2022,
          recruitmentNo: 4,
          birthYear: 2022,
          birthMonth: 7,
          birthDay: 10,
          recruitmentName: 'テスト馬4募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬4',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202404,
          occurredYear: 2024,
          occurredMonth: 4,
          occurredDay: 1,
          racePlace: '中山',
          raceName: 'テストレース4',
          raceResult: '4着',
          organizer: 'OTHER',
          mainPrizeAmount: 400000,
          appearanceFee: 20000,
          withholdingTax: 40000,
          commissionAmount: 20000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 440000,
          clubFeeAmount: 44000,
          taxAmount: 88000,
          incomeAmount: 308000,
          note: 'テスト賞金収入4',
          closing: false,
        },
      });

      const updateData = {
        horseId: horse.horseId,
        incomeYearMonth: 202404,
        occurredYear: 2024,
        occurredMonth: 4,
        occurredDay: 2,
        racePlace: '福島',
        raceName: '更新レース',
        raceResult: '1着',
        organizer: HorseIncomePrizeOrganizer.JRA,
        mainPrizeAmount: 1200000,
        appearanceFee: 60000,
        withholdingTax: 120000,
        commissionAmount: 60000,
        clubFeeRate: "0.1",
        taxRate: "0.2",
        totalPrizeAmount: 1320000,
        clubFeeAmount: 132000,
        taxAmount: 264000,
        incomeAmount: 924000,
        note: '更新された賞金収入',
        allowances: [{ name: '優勝手当', amount: 100000 }],
      };

      // ===== Act =====
      const result = await updateHorseIncomePrize(prize.horseIncomePrizeId, updateData);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // データベースで更新されていることを確認
      const updatedPrize = await vPrisma.client.horseIncomePrize.findUnique({
        where: { horseIncomePrizeId: prize.horseIncomePrizeId },
        include: { allowances: true },
      });

      expect(updatedPrize).toBeDefined();
      expect(updatedPrize?.raceName).toBe('更新レース');
      expect(updatedPrize?.organizer).toBe('JRA');
      expect(updatedPrize?.allowances.length).toBe(1);
      expect(updatedPrize?.allowances[0].name).toBe('優勝手当');
    });
  });

  describe('deleteHorseIncomePrize', () => {
    it('既存の賞金収入を削除できる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 5,
          horseName: 'テスト馬5',
          recruitmentYear: 2023,
          recruitmentNo: 5,
          birthYear: 2023,
          birthMonth: 9,
          birthDay: 25,
          recruitmentName: 'テスト馬5募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬5',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202405,
          occurredYear: 2024,
          occurredMonth: 5,
          occurredDay: 15,
          racePlace: '新潟',
          raceName: '削除レース',
          raceResult: '5着',
          organizer: 'JRA',
          mainPrizeAmount: 200000,
          appearanceFee: 10000,
          withholdingTax: 20000,
          commissionAmount: 10000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 220000,
          clubFeeAmount: 22000,
          taxAmount: 44000,
          incomeAmount: 154000,
          note: '削除対象',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await deleteHorseIncomePrize({ id: prize.horseIncomePrizeId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // データベースから削除されていることを確認
      const deletedPrize = await vPrisma.client.horseIncomePrize.findUnique({
        where: { horseIncomePrizeId: prize.horseIncomePrizeId },
      });

      expect(deletedPrize).toBeNull();
    });

    it('手当が関連付けられた賞金収入を削除する場合、手当も一緒に削除される', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 501,
          horseName: 'テスト馬501',
          recruitmentYear: 2023,
          recruitmentNo: 501,
          birthYear: 2023,
          birthMonth: 9,
          birthDay: 25,
          recruitmentName: 'テスト馬501募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬501',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 賞金収入を作成
      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202405,
          occurredYear: 2024,
          occurredMonth: 5,
          occurredDay: 15,
          racePlace: '新潟',
          raceName: '削除レース（手当あり）',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: '削除対象（手当あり）',
          closing: false,
        },
      });

      // 手当を複数作成
      const allowance1 = await vPrisma.client.horseIncomePrizeAllowance.create({
        data: {
          horseIncomePrizeId: prize.horseIncomePrizeId,
          name: '出走奨励賞',
          amount: 100000,
        },
      });

      const allowance2 = await vPrisma.client.horseIncomePrizeAllowance.create({
        data: {
          horseIncomePrizeId: prize.horseIncomePrizeId,
          name: '内国産所有奨励賞',
          amount: 50000,
        },
      });

      const allowance3 = await vPrisma.client.horseIncomePrizeAllowance.create({
        data: {
          horseIncomePrizeId: prize.horseIncomePrizeId,
          name: '付加賞',
          amount: 200000,
        },
      });

      // 削除前に手当が存在することを確認
      const allowancesBefore = await vPrisma.client.horseIncomePrizeAllowance.findMany({
        where: { horseIncomePrizeId: prize.horseIncomePrizeId },
      });
      expect(allowancesBefore).toHaveLength(3);

      // ===== Act =====
      const result = await deleteHorseIncomePrize({ id: prize.horseIncomePrizeId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // 賞金収入が削除されていることを確認
      const deletedPrize = await vPrisma.client.horseIncomePrize.findUnique({
        where: { horseIncomePrizeId: prize.horseIncomePrizeId },
      });
      expect(deletedPrize).toBeNull();

      // 関連する手当もすべて削除されていることを確認
      const allowancesAfter = await vPrisma.client.horseIncomePrizeAllowance.findMany({
        where: { horseIncomePrizeId: prize.horseIncomePrizeId },
      });
      expect(allowancesAfter).toHaveLength(0);

      // 各手当が個別に削除されていることを確認
      const deletedAllowance1 = await vPrisma.client.horseIncomePrizeAllowance.findUnique({
        where: { horseIncomePrizeAllowanceId: allowance1.horseIncomePrizeAllowanceId },
      });
      expect(deletedAllowance1).toBeNull();

      const deletedAllowance2 = await vPrisma.client.horseIncomePrizeAllowance.findUnique({
        where: { horseIncomePrizeAllowanceId: allowance2.horseIncomePrizeAllowanceId },
      });
      expect(deletedAllowance2).toBeNull();

      const deletedAllowance3 = await vPrisma.client.horseIncomePrizeAllowance.findUnique({
        where: { horseIncomePrizeAllowanceId: allowance3.horseIncomePrizeAllowanceId },
      });
      expect(deletedAllowance3).toBeNull();
    });

    it('存在しない賞金収入IDで削除しようとした場合はエラーになる', async () => {
      // ===== Act =====
      const result = await deleteHorseIncomePrize({ id: 99999 });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseIncomePrizeNotFoundError);
      }
    });

    it('削除処理がトランザクション内で実行され、手当が先に削除される', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 502,
          horseName: 'テスト馬502',
          recruitmentYear: 2023,
          recruitmentNo: 502,
          birthYear: 2023,
          birthMonth: 9,
          birthDay: 25,
          recruitmentName: 'テスト馬502募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬502',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 賞金収入を作成
      const prize = await vPrisma.client.horseIncomePrize.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202405,
          occurredYear: 2024,
          occurredMonth: 5,
          occurredDay: 15,
          racePlace: '新潟',
          raceName: 'トランザクションテストレース',
          raceResult: '1着',
          organizer: 'JRA',
          mainPrizeAmount: 1000000,
          appearanceFee: 50000,
          withholdingTax: 100000,
          commissionAmount: 50000,
          clubFeeRate: "0.1",
          taxRate: "0.2",
          totalPrizeAmount: 1100000,
          clubFeeAmount: 110000,
          taxAmount: 220000,
          incomeAmount: 770000,
          note: 'トランザクションテスト',
          closing: false,
        },
      });

      // 大量の手当を作成（削除処理の複雑さを増すため）
      const allowances = [];
      for (let i = 1; i <= 10; i++) {
        const allowance = await vPrisma.client.horseIncomePrizeAllowance.create({
          data: {
            horseIncomePrizeId: prize.horseIncomePrizeId,
            name: `テスト手当${i}`,
            amount: 10000 * i,
          },
        });
        allowances.push(allowance);
      }

      // 削除前に手当が存在することを確認
      const allowancesBefore = await vPrisma.client.horseIncomePrizeAllowance.findMany({
        where: { horseIncomePrizeId: prize.horseIncomePrizeId },
      });
      expect(allowancesBefore).toHaveLength(10);

      // ===== Act =====
      const result = await deleteHorseIncomePrize({ id: prize.horseIncomePrizeId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // 全てのデータが削除されていることを確認（トランザクションが成功）
      const deletedPrize = await vPrisma.client.horseIncomePrize.findUnique({
        where: { horseIncomePrizeId: prize.horseIncomePrizeId },
      });
      expect(deletedPrize).toBeNull();

      // 全ての手当が削除されていることを確認
      const allowancesAfter = await vPrisma.client.horseIncomePrizeAllowance.findMany({
        where: { horseIncomePrizeId: prize.horseIncomePrizeId },
      });
      expect(allowancesAfter).toHaveLength(0);

      // 個別の手当IDでも削除確認
      for (const allowance of allowances) {
        const deletedAllowance = await vPrisma.client.horseIncomePrizeAllowance.findUnique({
          where: { horseIncomePrizeAllowanceId: allowance.horseIncomePrizeAllowanceId },
        });
        expect(deletedAllowance).toBeNull();
      }
    });
  });

  describe('getHorseIncomeOther', () => {
    it('既存のその他収入を取得できる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 6,
          horseName: 'テスト馬6',
          recruitmentYear: 2020,
          recruitmentNo: 6,
          birthYear: 2020,
          birthMonth: 11,
          birthDay: 5,
          recruitmentName: 'テスト馬6募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬6',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202406,
          occurredYear: 2024,
          occurredMonth: 6,
          occurredDay: 10,
          name: 'INSURANCE',
          nameOther: '保険金',
          amount: 300000,
          salesCommission: 15000,
          otherFeeName: '保険手数料',
          otherFeeAmount: 5000,
          taxRate: "0.2",
          taxAmount: 60000,
          incomeAmount: 220000,
          note: 'テストその他収入2',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await getHorseIncomeOther({ id: other.horseIncomeOtherId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const foundOther = result.value;
        expect(foundOther.horseIncomeOtherId).toBe(other.horseIncomeOtherId);
        expect(foundOther.horseId).toBe(horse.horseId);
        expect(foundOther.name).toBe(HorseIncomeOtherName.INSURANCE);
        expect(foundOther.nameOther).toBe('保険金');
      }
    });

    it('存在しないその他収入IDで取得しようとした場合はエラーになる', async () => {
      // ===== Act =====
      const result = await getHorseIncomeOther({ id: 99999 });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(HorseIncomeOtherNotFoundError);
      }
    });
  });

  describe('createHorseIncomeOther', () => {
    it('新しいその他収入を作成できる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 7,
          horseName: 'テスト馬7',
          recruitmentYear: 2021,
          recruitmentNo: 7,
          birthYear: 2021,
          birthMonth: 2,
          birthDay: 28,
          recruitmentName: 'テスト馬7募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬7',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const createData = {
        horseId: horse.horseId,
        incomeYearMonth: 202407,
        occurredYear: 2024,
        occurredMonth: 7,
        occurredDay: 20,
        name: HorseIncomeOtherName.SYMPATHY,
        nameOther: '同情金',
        amount: 150000,
        salesCommission: 7500,
        otherFeeName: '同情手数料',
        otherFeeAmount: 2500,
        taxRate: "0.2",
        taxAmount: 30000,
        incomeAmount: 110000,
        note: 'テストその他収入3',
      };

      // ===== Act =====
      const result = await createHorseIncomeOther(createData);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const createdOther = result.value;
        expect(createdOther.horseId).toBe(horse.horseId);
        expect(createdOther.name).toBe(HorseIncomeOtherName.SYMPATHY);
        expect(createdOther.nameOther).toBe('同情金');
        expect(createdOther.incomeAmount).toBe(110000);
      }
    });
  });

  describe('updateHorseIncomeOther', () => {
    it('既存のその他収入を更新できる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 8,
          horseName: 'テスト馬8',
          recruitmentYear: 2022,
          recruitmentNo: 8,
          birthYear: 2022,
          birthMonth: 8,
          birthDay: 15,
          recruitmentName: 'テスト馬8募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬8',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202408,
          occurredYear: 2024,
          occurredMonth: 8,
          occurredDay: 1,
          name: 'GRANT',
          nameOther: '助成金',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '助成手数料',
          otherFeeAmount: 10000,
          taxRate: "0.2",
          taxAmount: 100000,
          incomeAmount: 365000,
          note: 'テストその他収入4',
          closing: false,
        },
      });

      const updateData = {
        horseId: horse.horseId,
        incomeYearMonth: 202408,
        occurredYear: 2024,
        occurredMonth: 8,
        occurredDay: 2,
        name: HorseIncomeOtherName.GRANT,
        nameOther: '更新助成金',
        amount: 600000,
        salesCommission: 30000,
        otherFeeName: '更新助成手数料',
        otherFeeAmount: 12000,
        taxRate: "0.2",
        taxAmount: 120000,
        incomeAmount: 438000,
        note: '更新されたその他収入',
      };

      // ===== Act =====
      const result = await updateHorseIncomeOther(other.horseIncomeOtherId, updateData);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // データベースで更新されていることを確認
      const updatedOther = await vPrisma.client.horseIncomeOther.findUnique({
        where: { horseIncomeOtherId: other.horseIncomeOtherId },
      });

      expect(updatedOther).toBeDefined();
      expect(updatedOther?.nameOther).toBe('更新助成金');
      expect(updatedOther?.amount).toBe(600000);
      expect(updatedOther?.incomeAmount).toBe(438000);
    });
  });

  describe('deleteHorseIncomeOther', () => {
    it('既存のその他収入を削除できる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 9,
          horseName: 'テスト馬9',
          recruitmentYear: 2023,
          recruitmentNo: 9,
          birthYear: 2023,
          birthMonth: 12,
          birthDay: 31,
          recruitmentName: 'テスト馬9募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬9',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202409,
          occurredYear: 2024,
          occurredMonth: 9,
          occurredDay: 30,
          name: 'OTHER',
          nameOther: 'その他収入',
          amount: 100000,
          salesCommission: 5000,
          otherFeeName: 'その他手数料',
          otherFeeAmount: 2000,
          taxRate: "0.2",
          taxAmount: 20000,
          incomeAmount: 73000,
          note: '削除対象その他収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await deleteHorseIncomeOther({ id: other.horseIncomeOtherId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // データベースから削除されていることを確認
      const deletedOther = await vPrisma.client.horseIncomeOther.findUnique({
        where: { horseIncomeOtherId: other.horseIncomeOtherId },
      });

      expect(deletedOther).toBeNull();
    });
  });

  describe('listHorseIncomePrizeAllowanceNames', () => {
    it('賞金収入手当名一覧を取得できる', async () => {
      // ===== Arrange =====
      const allowanceName1 = await vPrisma.client.horseIncomePrizeAllowanceName.create({
        data: {
          name: '特別手当',
        },
      });

      const allowanceName2 = await vPrisma.client.horseIncomePrizeAllowanceName.create({
        data: {
          name: '交通費',
        },
      });

      // ===== Act =====
      const result = await listHorseIncomePrizeAllowanceNames();

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const names = result.value;
        expect(names.length).toBeGreaterThanOrEqual(2);
        expect(names.some((name) => name.horseIncomePrizeAllowanceNameId === allowanceName1.horseIncomePrizeAllowanceNameId)).toBe(true);
        expect(names.some((name) => name.horseIncomePrizeAllowanceNameId === allowanceName2.horseIncomePrizeAllowanceNameId)).toBe(true);
      }
    });
  });

  describe('listHorseIncomeOther', () => {
    it('指定された馬のその他収入一覧を取得できる', async () => {
      // ===== Arrange =====
      const horse1 = await vPrisma.client.horse.create({
        data: {
          horseId: 1000,
          horseName: 'テスト馬1000',
          recruitmentYear: 2020,
          recruitmentNo: 1000,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1000募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1000',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const horse2 = await vPrisma.client.horse.create({
        data: {
          horseId: 1001,
          horseName: 'テスト馬1001',
          recruitmentYear: 2019,
          recruitmentNo: 1001,
          birthYear: 2019,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テスト馬1001募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1001',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const other1 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse1.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          name: 'SALES_DIVIDEND',
          nameOther: '販売配当',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '手数料',
          otherFeeAmount: 10000,
          taxRate: "0.2",
          taxAmount: 100000,
          incomeAmount: 365000,
          note: 'テストその他収入1',
          closing: false,
        },
      });

      const other2 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse1.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 20,
          name: 'INSURANCE',
          nameOther: '保険金',
          amount: 300000,
          salesCommission: 15000,
          otherFeeName: '保険手数料',
          otherFeeAmount: 5000,
          taxRate: "0.1",
          taxAmount: 30000,
          incomeAmount: 250000,
          note: 'テストその他収入2',
          closing: false,
        },
      });

      // 別の馬のその他収入（取得されないはず）
      const other3 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse2.horseId,
          incomeYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 10,
          name: 'SYMPATHY',
          nameOther: '見舞金',
          amount: 100000,
          salesCommission: 5000,
          otherFeeName: '手数料',
          otherFeeAmount: 2000,
          taxRate: "0.05",
          taxAmount: 5000,
          incomeAmount: 88000,
          note: 'テストその他収入3',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomeOther({ horseId: horse1.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const others = result.value;
        expect(others.length).toBe(2);
        expect(others.some(other => other.horseIncomeOtherId === other1.horseIncomeOtherId)).toBe(true);
        expect(others.some(other => other.horseIncomeOtherId === other2.horseIncomeOtherId)).toBe(true);
        // 別の馬のその他収入は含まれていないことを確認
        expect(others.some(other => other.horseIncomeOtherId === other3.horseIncomeOtherId)).toBe(false);
        
        // データの構造を確認
        const firstOther = others.find(other => other.horseIncomeOtherId === other1.horseIncomeOtherId);
        expect(firstOther).toBeDefined();
        if (firstOther) {
          expect(firstOther.horseId).toBe(horse1.horseId);
          expect(firstOther.name).toBe(HorseIncomeOtherName.SALES_DIVIDEND);
          expect(firstOther.nameOther).toBe('販売配当');
          expect(firstOther.amount).toBe(500000);
          expect(firstOther.incomeAmount).toBe(365000);
        }
      }
    });

    it('指定された収入年月でフィルタリングできる', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1002,
          horseName: 'テスト馬1002',
          recruitmentYear: 2020,
          recruitmentNo: 1002,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1002募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1002',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const other1 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          name: 'SALES_DIVIDEND',
          nameOther: '1月販売配当',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '手数料',
          otherFeeAmount: 10000,
          taxRate: "0.2",
          taxAmount: 100000,
          incomeAmount: 365000,
          note: '1月のその他収入',
          closing: false,
        },
      });

      const other2 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 20,
          name: 'INSURANCE',
          nameOther: '2月保険金',
          amount: 300000,
          salesCommission: 15000,
          otherFeeName: '保険手数料',
          otherFeeAmount: 5000,
          taxRate: "0.1",
          taxAmount: 30000,
          incomeAmount: 250000,
          note: '2月のその他収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomeOther({ 
        horseId: horse.horseId, 
        incomeYearMonth: 202401 
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const others = result.value;
        expect(others.length).toBe(1);
        expect(others[0].horseIncomeOtherId).toBe(other1.horseIncomeOtherId);
        expect(others[0].incomeYearMonth).toBe(202401);
        expect(others[0].nameOther).toBe('1月販売配当');
      }
    });

    it('ページネーションが正しく動作する', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1003,
          horseName: 'テスト馬1003',
          recruitmentYear: 2020,
          recruitmentNo: 1003,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1003募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1003',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 3件のその他収入を作成（発生年月日を降順になるように設定）
      const other1 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 30,
          name: 'SALES_DIVIDEND',
          nameOther: '3月販売配当',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '手数料',
          otherFeeAmount: 10000,
          taxRate: "0.2",
          taxAmount: 100000,
          incomeAmount: 365000,
          note: '3月のその他収入',
          closing: false,
        },
      });

      const other2 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 20,
          name: 'INSURANCE',
          nameOther: '2月保険金',
          amount: 300000,
          salesCommission: 15000,
          otherFeeName: '保険手数料',
          otherFeeAmount: 5000,
          taxRate: "0.1",
          taxAmount: 30000,
          incomeAmount: 250000,
          note: '2月のその他収入',
          closing: false,
        },
      });

      const other3 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 10,
          name: 'SYMPATHY',
          nameOther: '1月見舞金',
          amount: 100000,
          salesCommission: 5000,
          otherFeeName: '手数料',
          otherFeeAmount: 2000,
          taxRate: "0.05",
          taxAmount: 5000,
          incomeAmount: 88000,
          note: '1月のその他収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result1 = await listHorseIncomeOther({ 
        horseId: horse.horseId, 
        limit: 2, 
        offset: 0 
      });

      const result2 = await listHorseIncomeOther({ 
        horseId: horse.horseId, 
        limit: 2, 
        offset: 2 
      });

      // ===== Assert =====
      expect(result1.isOk()).toBe(true);
      expect(result2.isOk()).toBe(true);
      
      if (result1.isOk() && result2.isOk()) {
        expect(result1.value.length).toBe(2);
        expect(result2.value.length).toBe(1);
        
        // ソート順は発生年月日降順なので、other1(3月30日) → other2(2月20日) → other3(1月10日)の順
        // 最初の2件（other1, other2）が取得されていることを確認
        expect(result1.value.some(other => other.horseIncomeOtherId === other1.horseIncomeOtherId)).toBe(true);
        expect(result1.value.some(other => other.horseIncomeOtherId === other2.horseIncomeOtherId)).toBe(true);
        
        // 3件目（other3）が取得されていることを確認
        expect(result2.value[0].horseIncomeOtherId).toBe(other3.horseIncomeOtherId);
      }
    });

    it('その他収入が存在しない馬の場合は空の配列を返す', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1004,
          horseName: 'テスト馬1004',
          recruitmentYear: 2020,
          recruitmentNo: 1004,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1004募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1004',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomeOther({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toEqual([]);
      }
    });

    it('存在しない収入年月でフィルタリングした場合は空の配列を返す', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1005,
          horseName: 'テスト馬1005',
          recruitmentYear: 2020,
          recruitmentNo: 1005,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1005募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1005',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          name: 'SALES_DIVIDEND',
          nameOther: '販売配当',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '手数料',
          otherFeeAmount: 10000,
          taxRate: "0.2",
          taxAmount: 100000,
          incomeAmount: 365000,
          note: 'テストその他収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomeOther({ 
        horseId: horse.horseId, 
        incomeYearMonth: 202412 
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toEqual([]);
      }
    });

    it('取得されたデータの構造が正しいこと', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1006,
          horseName: 'テスト馬1006',
          recruitmentYear: 2020,
          recruitmentNo: 1006,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1006募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1006',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          name: 'SALES_DIVIDEND',
          nameOther: '販売配当',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '手数料',
          otherFeeAmount: 10000,
          taxRate: "0.2",
          taxAmount: 100000,
          incomeAmount: 365000,
          note: 'テストその他収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomeOther({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const others = result.value;
        expect(others.length).toBe(1);
        
        const otherData = others[0];
        expect(otherData).toHaveProperty('horseIncomeOtherId');
        expect(otherData).toHaveProperty('horseId');
        expect(otherData).toHaveProperty('incomeYearMonth');
        expect(otherData).toHaveProperty('occurredYear');
        expect(otherData).toHaveProperty('occurredMonth');
        expect(otherData).toHaveProperty('occurredDay');
        expect(otherData).toHaveProperty('name');
        expect(otherData).toHaveProperty('nameOther');
        expect(otherData).toHaveProperty('amount');
        expect(otherData).toHaveProperty('salesCommission');
        expect(otherData).toHaveProperty('otherFeeName');
        expect(otherData).toHaveProperty('otherFeeAmount');
        expect(otherData).toHaveProperty('taxRate');
        expect(otherData).toHaveProperty('taxAmount');
        expect(otherData).toHaveProperty('incomeAmount');
        expect(otherData).toHaveProperty('note');
        expect(otherData).toHaveProperty('closing');
      }
    });

    it('ソート順が正しいこと（発生年月日降順、ID降順）', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1007,
          horseName: 'テスト馬1007',
          recruitmentYear: 2020,
          recruitmentNo: 1007,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1007募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1007',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 異なる日付で3件のその他収入を作成
      const other1 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          name: 'SALES_DIVIDEND',
          nameOther: '1月15日販売配当',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '手数料',
          otherFeeAmount: 10000,
          taxRate: "0.2",
          taxAmount: 100000,
          incomeAmount: 365000,
          note: '1月15日のその他収入',
          closing: false,
        },
      });

      const other2 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 20,
          name: 'INSURANCE',
          nameOther: '1月20日保険金',
          amount: 300000,
          salesCommission: 15000,
          otherFeeName: '保険手数料',
          otherFeeAmount: 5000,
          taxRate: "0.1",
          taxAmount: 30000,
          incomeAmount: 250000,
          note: '1月20日のその他収入',
          closing: false,
        },
      });

      const other3 = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 10,
          name: 'SYMPATHY',
          nameOther: '2月10日見舞金',
          amount: 100000,
          salesCommission: 5000,
          otherFeeName: '手数料',
          otherFeeAmount: 2000,
          taxRate: "0.05",
          taxAmount: 5000,
          incomeAmount: 88000,
          note: '2月10日のその他収入',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomeOther({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const others = result.value;
        expect(others.length).toBe(3);
        
        // 発生年月日降順でソートされていることを確認
        // 2月10日 → 1月20日 → 1月15日の順
        expect(others[0].horseIncomeOtherId).toBe(other3.horseIncomeOtherId);
        expect(others[1].horseIncomeOtherId).toBe(other2.horseIncomeOtherId);
        expect(others[2].horseIncomeOtherId).toBe(other1.horseIncomeOtherId);
      }
    });

    it('複数のその他収入種別が正しく処理される', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1008,
          horseName: 'テスト馬1008',
          recruitmentYear: 2020,
          recruitmentNo: 1008,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1008募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1008',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      const salesDividend = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202401,
          occurredYear: 2024,
          occurredMonth: 1,
          occurredDay: 15,
          name: 'SALES_DIVIDEND',
          nameOther: '販売配当金',
          amount: 300000,
          salesCommission: 15000,
          otherFeeName: '手数料',
          otherFeeAmount: 5000,
          taxRate: "0.2",
          taxAmount: 60000,
          incomeAmount: 220000,
          note: '販売配当',
          closing: false,
        },
      });

      const insurance = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202402,
          occurredYear: 2024,
          occurredMonth: 2,
          occurredDay: 20,
          name: 'INSURANCE',
          nameOther: '保険金',
          amount: 500000,
          salesCommission: 25000,
          otherFeeName: '保険手数料',
          otherFeeAmount: 10000,
          taxRate: "0.1",
          taxAmount: 50000,
          incomeAmount: 415000,
          note: '保険金',
          closing: false,
        },
      });

      const sympathy = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202403,
          occurredYear: 2024,
          occurredMonth: 3,
          occurredDay: 10,
          name: 'SYMPATHY',
          nameOther: '見舞金',
          amount: 100000,
          salesCommission: 5000,
          otherFeeName: '手数料',
          otherFeeAmount: 2000,
          taxRate: "0.05",
          taxAmount: 5000,
          incomeAmount: 88000,
          note: '見舞金',
          closing: false,
        },
      });

      const grant = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202404,
          occurredYear: 2024,
          occurredMonth: 4,
          occurredDay: 5,
          name: 'GRANT',
          nameOther: '助成金',
          amount: 200000,
          salesCommission: 10000,
          otherFeeName: '申請手数料',
          otherFeeAmount: 3000,
          taxRate: "0.0",
          taxAmount: 0,
          incomeAmount: 187000,
          note: '助成金',
          closing: false,
        },
      });

      const other = await vPrisma.client.horseIncomeOther.create({
        data: {
          horseId: horse.horseId,
          incomeYearMonth: 202405,
          occurredYear: 2024,
          occurredMonth: 5,
          occurredDay: 1,
          name: 'OTHER',
          nameOther: 'その他収入',
          amount: 150000,
          salesCommission: 7500,
          otherFeeName: '手数料',
          otherFeeAmount: 2500,
          taxRate: "0.15",
          taxAmount: 22500,
          incomeAmount: 120000,
          note: 'その他',
          closing: false,
        },
      });

      // ===== Act =====
      const result = await listHorseIncomeOther({ horseId: horse.horseId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const others = result.value;
        expect(others.length).toBe(5);

        // 各その他収入のnameOtherが正しく設定されていることを確認
        const salesDividendOther = others.find(other => other.horseIncomeOtherId === salesDividend.horseIncomeOtherId);
        const insuranceOther = others.find(other => other.horseIncomeOtherId === insurance.horseIncomeOtherId);
        const sympathyOther = others.find(other => other.horseIncomeOtherId === sympathy.horseIncomeOtherId);
        const grantOther = others.find(other => other.horseIncomeOtherId === grant.horseIncomeOtherId);
        const otherOther = others.find(other => other.horseIncomeOtherId === other.horseIncomeOtherId);

        expect(salesDividendOther?.name).toBe(HorseIncomeOtherName.SALES_DIVIDEND);
        expect(insuranceOther?.name).toBe(HorseIncomeOtherName.INSURANCE);
        expect(sympathyOther?.name).toBe(HorseIncomeOtherName.SYMPATHY);
        expect(grantOther?.name).toBe(HorseIncomeOtherName.GRANT);
        expect(otherOther?.name).toBe(HorseIncomeOtherName.OTHER);

        // 各その他収入のnameOtherが正しく設定されていることを確認
        expect(salesDividendOther?.nameOther).toBe('販売配当金');
        expect(insuranceOther?.nameOther).toBe('保険金');
        expect(sympathyOther?.nameOther).toBe('見舞金');
        expect(grantOther?.nameOther).toBe('助成金');
        expect(otherOther?.nameOther).toBe('その他収入');
      }
    });

    it('limitとoffsetが正しく動作すること', async () => {
      // ===== Arrange =====
      const horse = await vPrisma.client.horse.create({
        data: {
          horseId: 1010,
          horseName: 'テスト馬1010',
          recruitmentYear: 2020,
          recruitmentNo: 1010,
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          recruitmentName: 'テスト馬1010募集',
          sharesTotal: 100,
          amountTotal: 10000000,
          note: 'テスト馬1010',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });

      // 10個のその他収入を作成（発生年月日を降順になるように設定）
      const others = [];
      for (let i = 1; i <= 10; i++) {
        const other = await vPrisma.client.horseIncomeOther.create({
          data: {
            horseId: horse.horseId,
            incomeYearMonth: 202400 + i,
            occurredYear: 2024,
            occurredMonth: 12,
            occurredDay: 31 - i, // 降順になるように設定
            name: 'SALES_DIVIDEND',
            nameOther: `販売配当${i}`,
            amount: 100000,
            salesCommission: 5000,
            otherFeeName: '手数料',
            otherFeeAmount: 2000,
            taxRate: "0.2",
            taxAmount: 20000,
            incomeAmount: 73000,
            note: `テストその他収入${i}`,
            closing: false,
          },
        });
        others.push(other);
      }

      // ===== Act =====
      const result1 = await listHorseIncomeOther({ 
        horseId: horse.horseId, 
        limit: 3, 
        offset: 0 
      });

      const result2 = await listHorseIncomeOther({ 
        horseId: horse.horseId, 
        limit: 3, 
        offset: 3 
      });

      const result3 = await listHorseIncomeOther({ 
        horseId: horse.horseId, 
        limit: 3, 
        offset: 6 
      });

      const result4 = await listHorseIncomeOther({ 
        horseId: horse.horseId, 
        limit: 3, 
        offset: 9 
      });

      // ===== Assert =====
      expect(result1.isOk()).toBe(true);
      expect(result2.isOk()).toBe(true);
      expect(result3.isOk()).toBe(true);
      expect(result4.isOk()).toBe(true);
      
      if (result1.isOk() && result2.isOk() && result3.isOk() && result4.isOk()) {
        expect(result1.value.length).toBe(3);
        expect(result2.value.length).toBe(3);
        expect(result3.value.length).toBe(3);
        expect(result4.value.length).toBe(1); // 最後の1件

        // 重複がないことを確認
        const allIds = [
          ...result1.value.map(other => other.horseIncomeOtherId),
          ...result2.value.map(other => other.horseIncomeOtherId),
          ...result3.value.map(other => other.horseIncomeOtherId),
          ...result4.value.map(other => other.horseIncomeOtherId)
        ];
        const uniqueIds = new Set(allIds);
        expect(allIds.length).toBe(uniqueIds.size);

        // 全てのその他収入が含まれていることを確認
        const allOtherIds = others.map(other => other.horseIncomeOtherId);
        allOtherIds.forEach(id => {
          expect(allIds).toContain(id);
        });

        // ソート順が正しいことを確認（発生年月日降順なので、others[0]が最初に来る）
        expect(result1.value[0].horseIncomeOtherId).toBe(others[0].horseIncomeOtherId);
        expect(result1.value[1].horseIncomeOtherId).toBe(others[1].horseIncomeOtherId);
        expect(result1.value[2].horseIncomeOtherId).toBe(others[2].horseIncomeOtherId);
      }
    });
  });
});
