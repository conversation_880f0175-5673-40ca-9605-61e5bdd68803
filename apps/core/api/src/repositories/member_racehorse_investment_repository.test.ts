import {
  getFirstMemberRacehorseInvestment,
  getMemberRacehorseInvestment,
} from "./member_racehorse_investment_repository";
import { 
  HorseFactory,
  MemberFactory,
  MemberRacehorseInvestmentFactory,
} from "../../test_utils";

describe("member_racehorse_investment_repository", () => {
  let testHorseId: number;
  let testMemberId: number;

  beforeEach(async () => {
    // テスト用のデータを作成
    const horse = await HorseFactory.create();
    const member = await MemberFactory.create();

    testHorseId = horse.horseId;
    testMemberId = member.memberId;
  });

  describe("getFirstMemberRacehorseInvestment", () => {
    it("指定されたmemberIdとhorseIdの最初のMemberRacehorseInvestmentが取得できること", async () => {
      // Arrange
      const investmentDate1 = new Date("2024-01-01");
      const investmentDate2 = new Date("2024-02-01");
      
      await MemberRacehorseInvestmentFactory.create({
        memberId: testMemberId,
        horseId: testHorseId,
        investmentDate: investmentDate2, // 後日付
        racehorseInvestmentEquivalent: 1000000,
        discountAllocation: 0,
        racehorseInvestment: 1000000,
      });

      await MemberRacehorseInvestmentFactory.create({
        memberId: testMemberId,
        horseId: testHorseId,
        investmentDate: investmentDate1, // 先日付（これが最初になるはず）
        racehorseInvestmentEquivalent: 2000000,
        discountAllocation: 100000,
        racehorseInvestment: 1900000,
      });

      // Act
      const result = await getFirstMemberRacehorseInvestment(testMemberId, testHorseId);

      // Assert
      expect(result).not.toBeNull();
      expect(result?.memberId).toBe(testMemberId);
      expect(result?.horseId).toBe(testHorseId);
      expect(result?.investmentDate).toEqual(investmentDate1);
      expect(result?.racehorseInvestmentEquivalent).toBe(2000000);
      expect(result?.discountAllocation).toBe(100000);
      expect(result?.racehorseInvestment).toBe(1900000);
    });

    it("指定されたmemberIdとhorseIdの投資記録が存在しない場合はnullが返されること", async () => {
      // Act
      const result = await getFirstMemberRacehorseInvestment(testMemberId, testHorseId);

      // Assert
      expect(result).toBeNull();
    });

    it("異なるmemberIdやhorseIdの投資記録は取得されないこと", async () => {
      // Arrange
      const otherHorse = await HorseFactory.create();
      const otherMember = await MemberFactory.create();
      
      await MemberRacehorseInvestmentFactory.create({
        memberId: otherMember.memberId,
        horseId: testHorseId,
        investmentDate: new Date("2024-01-01"),
      });

      await MemberRacehorseInvestmentFactory.create({
        memberId: testMemberId,
        horseId: otherHorse.horseId,
        investmentDate: new Date("2024-01-01"),
      });

      // Act
      const result = await getFirstMemberRacehorseInvestment(testMemberId, testHorseId);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe("getMemberRacehorseInvestment", () => {
    it("指定されたmemberId、horseId、investmentDateのMemberRacehorseInvestmentが取得できること", async () => {
      // Arrange
      const investmentDate = new Date("2024-01-15");
      
      await MemberRacehorseInvestmentFactory.create({
        memberId: testMemberId,
        horseId: testHorseId,
        investmentDate: investmentDate,
        racehorseInvestmentEquivalent: 1500000,
        discountAllocation: 50000,
        racehorseInvestment: 1450000,
      });

      // Act
      const result = await getMemberRacehorseInvestment(testMemberId, testHorseId, investmentDate);

      // Assert
      expect(result).not.toBeNull();
      expect(result?.memberId).toBe(testMemberId);
      expect(result?.horseId).toBe(testHorseId);
      expect(result?.investmentDate).toEqual(investmentDate);
      expect(result?.racehorseInvestmentEquivalent).toBe(1500000);
      expect(result?.discountAllocation).toBe(50000);
      expect(result?.racehorseInvestment).toBe(1450000);
    });

    it("指定されたinvestmentDateが一致しない場合はnullが返されること", async () => {
      // Arrange
      const investmentDate = new Date("2024-01-15");
      const differentDate = new Date("2024-01-16");
      
      await MemberRacehorseInvestmentFactory.create({
        memberId: testMemberId,
        horseId: testHorseId,
        investmentDate: investmentDate,
      });

      // Act
      const result = await getMemberRacehorseInvestment(testMemberId, testHorseId, differentDate);

      // Assert
      expect(result).toBeNull();
    });

    it("指定されたmemberIdが一致しない場合はnullが返されること", async () => {
      // Arrange
      const otherMember = await MemberFactory.create();
      const investmentDate = new Date("2024-01-15");
      
      await MemberRacehorseInvestmentFactory.create({
        memberId: otherMember.memberId,
        horseId: testHorseId,
        investmentDate: investmentDate,
      });

      // Act
      const result = await getMemberRacehorseInvestment(testMemberId, testHorseId, investmentDate);

      // Assert
      expect(result).toBeNull();
    });

    it("指定されたhorseIdが一致しない場合はnullが返されること", async () => {
      // Arrange
      const otherHorse = await HorseFactory.create();
      const investmentDate = new Date("2024-01-15");
      
      await MemberRacehorseInvestmentFactory.create({
        memberId: testMemberId,
        horseId: otherHorse.horseId,
        investmentDate: investmentDate,
      });

      // Act
      const result = await getMemberRacehorseInvestment(testMemberId, testHorseId, investmentDate);

      // Assert
      expect(result).toBeNull();
    });

    it("複数の投資記録がある場合でも、指定された条件に一致するもののみが取得されること", async () => {
      // Arrange
      const investmentDate1 = new Date("2024-01-15");
      const investmentDate2 = new Date("2024-02-15");
      
      await MemberRacehorseInvestmentFactory.create({
        memberId: testMemberId,
        horseId: testHorseId,
        investmentDate: investmentDate1,
        racehorseInvestmentEquivalent: 1000000,
      });

      await MemberRacehorseInvestmentFactory.create({
        memberId: testMemberId,
        horseId: testHorseId,
        investmentDate: investmentDate2,
        racehorseInvestmentEquivalent: 2000000,
      });

      // Act
      const result = await getMemberRacehorseInvestment(testMemberId, testHorseId, investmentDate1);

      // Assert
      expect(result).not.toBeNull();
      expect(result?.investmentDate).toEqual(investmentDate1);
      expect(result?.racehorseInvestmentEquivalent).toBe(1000000);
    });
  });
});
