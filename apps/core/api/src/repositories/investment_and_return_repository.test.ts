import { ReturnCategory } from "@hami/prisma";
import {
    listInvestmentAndReturnByHorseIdAndMemberId,
    listInvestmentAndReturnInvestmentByHorseIdAndMemberId,
    listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory,
    getTotalRacehorseInvestmentByHorseIdAndMemberIdAndDate,
    getTotalInsuranceInvestmentByHorseIdAndMemberIdAndDate,
    getTotalOtherInvestmentByHorseIdAndMemberIdAndDate,
    getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate,
    getInvestmentAndReturnReturnListByHorseIdAndMemberIdAndReturnCategory,
    createInvestmentAndReturn,
    updatePaymentAmount,
    createInvestmentAndReturnInvestment,
    createInvestmentAndReturnReturn,
    getCreatedInvestmentAndReturnSharesSumByHorseId,
    getInvestmentAndReturnListByHorseIdAndCreatedDate,
    getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId,
    listInvestmentAndReturnByMemberIdAndOccurredDate,
} from "./investment_and_return_repository";
import { 
    HorseFactory,
    MemberFactory,
    InvestmentContractFactory,
} from "../../test_utils";

describe("investment_and_return_repository", () => {
    let testHorseId: number;
    let testMemberId: number;
    let testInvestmentContractId: number;
    let testInvestmentAndReturnId: number;

    beforeEach(async () => {
        // テスト用のデータを作成
        const horse = await HorseFactory.create();
        const member = await MemberFactory.create();
        const investmentContract = await InvestmentContractFactory.create({
            horse: { connect: { horseId: horse.horseId } },
            member: { connect: { memberId: member.memberId } },
        });
        
        // InvestmentAndReturnを手動で作成
        const investmentAndReturn = await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: horse.horseId,
                memberId: member.memberId,
                createdDate: new Date("2023-01-01"),
                progressedMonth: 1,
                yearlyReturnTargetFlag: false,
                sharesNumber: 3,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 400000,
                investmentTotal: 550000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });

        testHorseId = horse.horseId;
        testMemberId = member.memberId;
        testInvestmentContractId = investmentContract.investmentContractId;
        testInvestmentAndReturnId = investmentAndReturn.investmentAndReturnId;
    });

    describe("listInvestmentAndReturnByHorseAndMember", () => {
        it("指定されたhorseIdとmemberIdのInvestmentAndReturnリストが取得できること", async () => {
            // Arrange
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 400000,
                    investmentTotal: 700000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investmentAndReturns = result._unsafeUnwrap();
            expect(investmentAndReturns).toHaveLength(2);
            expect(investmentAndReturns[0].horseId).toBe(testHorseId);
            expect(investmentAndReturns[0].memberId).toBe(testMemberId);
            expect(investmentAndReturns[0].sharesNumber).toBe(3);
            expect(investmentAndReturns[0].runningCostInvestmentTotal).toBe(100000);
            expect(investmentAndReturns[0].insuranceInvestmentTotal).toBe(50000);
            expect(investmentAndReturns[0].otherInvestmentTotal).toBe(400000);
            expect(investmentAndReturns[0].investmentTotal).toBe(550000);
            expect(investmentAndReturns[0].racehorseBookValueEndOfLastMonth).toBe(0);
            expect(investmentAndReturns[0].investmentRefundPaidUpToLastMonth).toBe(0);
            expect(investmentAndReturns[0].organizerWithholdingTaxTotal).toBe(0);
            expect(investmentAndReturns[0].organizerWithholdingTaxCurrentMonthAddition).toBe(0);
            expect(investmentAndReturns[0].clubWithholdingTaxTotal).toBe(0);
            expect(investmentAndReturns[0].clubWithholdingTaxCurrentMonthAddition).toBe(0);
            expect(investmentAndReturns[0].billingAmount).toBe(150000);
            expect(investmentAndReturns[0].paymentAmount).toBe(0);
        });

        it("複数のInvestmentAndReturnが存在する場合、全て取得できること", async () => {
            // Arrange
            const additionalMember = await MemberFactory.create();
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 400000,
                    investmentTotal: 700000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-03-01"),
                    progressedMonth: 3,
                    yearlyReturnTargetFlag: true,
                    sharesNumber: 4,
                    runningCostInvestmentTotal: 300000,
                    insuranceInvestmentTotal: 150000,
                    otherInvestmentTotal: 400000,
                    investmentTotal: 850000,
                    racehorseBookValueEndOfLastMonth: 100000,
                    investmentRefundPaidUpToLastMonth: 50000,
                    organizerWithholdingTaxTotal: 10000,
                    organizerWithholdingTaxCurrentMonthAddition: 5000,
                    clubWithholdingTaxTotal: 5000,
                    clubWithholdingTaxCurrentMonthAddition: 2000,
                    billingAmount: 450000,
                    paymentAmount: 100000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investmentAndReturns = result._unsafeUnwrap();
            expect(investmentAndReturns).toHaveLength(3);
            
            // 全て同じhorseIdとmemberIdであることを確認
            investmentAndReturns.forEach(record => {
                expect(record.horseId).toBe(testHorseId);
                expect(record.memberId).toBe(testMemberId);
            });
        });

        it("異なるhorseIdのデータは取得されないこと", async () => {
            // Arrange
            const differentHorse = await HorseFactory.create();

            const differentInvestmentContract = await InvestmentContractFactory.create({
                horse: { connect: { horseId: differentHorse.horseId } },
                member: { connect: { memberId: testMemberId } },
            });

            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: differentHorse.horseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 400000,
                    investmentTotal: 700000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investmentAndReturns = result._unsafeUnwrap();
            expect(investmentAndReturns).toHaveLength(1); // 元の1件のみ
            expect(investmentAndReturns[0].horseId).toBe(testHorseId);
            expect(investmentAndReturns[0].memberId).toBe(testMemberId);
            expect(investmentAndReturns[0].sharesNumber).toBe(3);
        });

        it("異なるmemberIdのデータは取得されないこと", async () => {
            // Arrange
            const differentMember = await MemberFactory.create();
            const differentHorse = await HorseFactory.create();
            const differentInvestmentContract = await InvestmentContractFactory.create({
                horse: { connect: { horseId: testHorseId } },
                member: { connect: { memberId: differentMember.memberId } },
            });
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: differentHorse.horseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 400000,
                    investmentTotal: 700000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investmentAndReturns = result._unsafeUnwrap();
            expect(investmentAndReturns).toHaveLength(1); // 元の1件のみ
            expect(investmentAndReturns[0].horseId).toBe(testHorseId);
            expect(investmentAndReturns[0].memberId).toBe(testMemberId);
            expect(investmentAndReturns[0].sharesNumber).toBe(3);
        });

        it("存在しないhorseIdとmemberIdの組み合わせの場合は空配列が返されること", async () => {
            // Arrange
            const nonExistentHorseId = 999999;
            const nonExistentMemberId = 999999;

            // Act
            const result = await listInvestmentAndReturnByHorseIdAndMemberId(nonExistentHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investmentAndReturns = result._unsafeUnwrap();
            expect(investmentAndReturns).toHaveLength(0);
        });

        it("horseIdのみ存在しない場合は空配列が返されること", async () => {
            // Arrange
            const nonExistentHorseId = 999999;

            // Act
            const result = await listInvestmentAndReturnByHorseIdAndMemberId(nonExistentHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investmentAndReturns = result._unsafeUnwrap();
            expect(investmentAndReturns).toHaveLength(0);
        });

        it("memberIdのみ存在しない場合は空配列が返されること", async () => {
            // Arrange
            const nonExistentMemberId = 999999;

            // Act
            const result = await listInvestmentAndReturnByHorseIdAndMemberId(testHorseId, nonExistentMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investmentAndReturns = result._unsafeUnwrap();
            expect(investmentAndReturns).toHaveLength(0);
        });

        it("取得されたデータの構造が正しいこと", async () => {
            // Act
            const result = await listInvestmentAndReturnByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investmentAndReturns = result._unsafeUnwrap();
            expect(investmentAndReturns).toHaveLength(1);
            
            const record = investmentAndReturns[0];
            expect(record).toHaveProperty('investmentAndReturnId');
            expect(record).toHaveProperty('horseId');
            expect(record).toHaveProperty('memberId');
            expect(record).toHaveProperty('createdDate');
            expect(record).toHaveProperty('progressedMonth');
            expect(record).toHaveProperty('yearlyReturnTargetFlag');
            expect(record).toHaveProperty('runningCostInvestmentTotal');
            expect(record).toHaveProperty('insuranceInvestmentTotal');
            expect(record).toHaveProperty('investmentTotal');
            expect(record).toHaveProperty('racehorseBookValueEndOfLastMonth');
            expect(record).toHaveProperty('investmentRefundPaidUpToLastMonth');
            expect(record).toHaveProperty('organizerWithholdingTaxTotal');
            expect(record).toHaveProperty('organizerWithholdingTaxCurrentMonthAddition');
            expect(record).toHaveProperty('clubWithholdingTaxTotal');
            expect(record).toHaveProperty('clubWithholdingTaxCurrentMonthAddition');
            expect(record).toHaveProperty('billingAmount');
            expect(record).toHaveProperty('paymentAmount');
        });
    });

    describe("listInvestmentAndReturnInvestmentByHorseAndMember", () => {
        it("指定されたhorseIdとmemberIdのInvestmentAndReturnInvestmentリストが取得できること", async () => {
            // Arrange
            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    racehorseInvestment: 1000000,
                    runningCost: 50000,
                    subsidy: 10000,
                    insuranceInvestment: 30000,
                    otherInvestment: 20000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 40000,
                    currentMonthInvestmentTotal: 1100000,
                    racehorseInvestmentEquivalent: 1000000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnInvestmentByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investments = result._unsafeUnwrap();
            expect(investments).toHaveLength(1);
            expect(investments[0].investmentAndReturnId).toBe(testInvestmentAndReturnId);
        });

        it("複数のInvestmentAndReturnInvestmentが存在する場合、全て取得できること", async () => {
            // Arrange
            const additionalInvestmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 400000,
                    investmentTotal: 700000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    racehorseInvestment: 1000000,
                    runningCost: 50000,
                    subsidy: 10000,
                    insuranceInvestment: 30000,
                    otherInvestment: 20000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 40000,
                    currentMonthInvestmentTotal: 1100000,
                    racehorseInvestmentEquivalent: 1000000,
                },
            });

            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: additionalInvestmentAndReturn.investmentAndReturnId,
                    racehorseInvestment: 0,
                    runningCost: 60000,
                    subsidy: 15000,
                    insuranceInvestment: 40000,
                    otherInvestment: 25000,
                    discountAllocation: 5000,
                    retroactiveRunningCost: 10000,
                    runningCostInvestment: 45000,
                    currentMonthInvestmentTotal: 130000,
                    racehorseInvestmentEquivalent: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnInvestmentByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investments = result._unsafeUnwrap();
            expect(investments).toHaveLength(2);
            
            // 全て同じhorseIdとmemberIdに関連するデータであることを確認
            investments.forEach(investment => {
                expect(investment.investmentAndReturnId).toBeDefined();
            });
        });

        it("異なるhorseIdのデータは取得されないこと", async () => {
            // Arrange
            const differentHorse = await HorseFactory.create();
            const differentInvestmentContract = await InvestmentContractFactory.create({
                horse: { connect: { horseId: differentHorse.horseId } },
                member: { connect: { memberId: testMemberId } },
            });
            const differentInvestmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: differentHorse.horseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 300000,
                    investmentTotal: 600000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: differentInvestmentAndReturn.investmentAndReturnId,
                    racehorseInvestment: 1000000,
                    runningCost: 50000,
                    subsidy: 10000,
                    insuranceInvestment: 30000,
                    otherInvestment: 20000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 40000,
                    currentMonthInvestmentTotal: 1100000,
                    racehorseInvestmentEquivalent: 1000000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnInvestmentByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investments = result._unsafeUnwrap();
            expect(investments).toHaveLength(0); // 異なるhorseIdのデータは取得されない
        });

        it("異なるmemberIdのデータは取得されないこと", async () => {
            // Arrange
            const differentMember = await MemberFactory.create();
            const differentHorse = await HorseFactory.create();
            const differentInvestmentContract = await InvestmentContractFactory.create({
                horse: { connect: { horseId: testHorseId } },
                member: { connect: { memberId: differentMember.memberId } },
            });
            const differentInvestmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: differentHorse.horseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 400000,
                    investmentTotal: 700000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: differentInvestmentAndReturn.investmentAndReturnId,
                    racehorseInvestment: 1000000,
                    runningCost: 50000,
                    subsidy: 10000,
                    insuranceInvestment: 30000,
                    otherInvestment: 20000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 40000,
                    currentMonthInvestmentTotal: 1100000,
                    racehorseInvestmentEquivalent: 1000000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnInvestmentByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investments = result._unsafeUnwrap();
            expect(investments).toHaveLength(0); // 異なるmemberIdのデータは取得されない
        });

        it("InvestmentAndReturnInvestmentが存在しない場合は空配列が返されること", async () => {
            // Arrange
            const newHorse = await HorseFactory.create();
            const newMember = await MemberFactory.create();
            const newInvestmentContract = await InvestmentContractFactory.create({
                horse: { connect: { horseId: newHorse.horseId } },
                member: { connect: { memberId: newMember.memberId } },
            });
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: newHorse.horseId,
                    memberId: newMember.memberId,
                    createdDate: new Date("2023-01-01"),
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 100000,
                    investmentTotal: 250000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnInvestmentByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investments = result._unsafeUnwrap();
            expect(investments).toHaveLength(0);
        });

        it("取得されたデータの構造が正しいこと", async () => {
            // Arrange
            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    racehorseInvestment: 1000000,
                    runningCost: 50000,
                    subsidy: 10000,
                    insuranceInvestment: 30000,
                    otherInvestment: 20000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 40000,
                    currentMonthInvestmentTotal: 1100000,
                    racehorseInvestmentEquivalent: 1000000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnInvestmentByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investments = result._unsafeUnwrap();
            expect(investments).toHaveLength(1);
            
            const investment = investments[0];
            expect(investment).toHaveProperty('investmentAndReturnInvestmentId');
            expect(investment).toHaveProperty('investmentAndReturnId');
            expect(investment).toHaveProperty('racehorseInvestment');
            expect(investment).toHaveProperty('runningCost');
            expect(investment).toHaveProperty('subsidy');
            expect(investment).toHaveProperty('insuranceInvestment');
            expect(investment).toHaveProperty('otherInvestment');
            expect(investment).toHaveProperty('discountAllocation');
            expect(investment).toHaveProperty('retroactiveRunningCost');
            expect(investment).toHaveProperty('runningCostInvestment');
            expect(investment).toHaveProperty('currentMonthInvestmentTotal');
            expect(investment).toHaveProperty('racehorseInvestmentEquivalent');
            // investmentAndReturnIdが正しく設定されていることを確認
            expect(investment.investmentAndReturnId).toBe(testInvestmentAndReturnId);
        });

        it("investmentAndReturnIdが正しく設定されていること", async () => {
            // Arrange
            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    racehorseInvestment: 1000000,
                    runningCost: 50000,
                    subsidy: 10000,
                    insuranceInvestment: 30000,
                    otherInvestment: 20000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 40000,
                    currentMonthInvestmentTotal: 1100000,
                    racehorseInvestmentEquivalent: 1000000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnInvestmentByHorseIdAndMemberId(testHorseId, testMemberId);

            // Assert
            expect(result.isOk()).toBe(true);
            const investments = result._unsafeUnwrap();
            expect(investments).toHaveLength(1);
            
            const investment = investments[0];
            expect(investment.investmentAndReturnId).toBe(testInvestmentAndReturnId);
        });
    });

    describe("listInvestmentAndReturnReturnByHorseAndMemberAndReturnCategory", () => {
        it("指定された条件のInvestmentAndReturnReturnリストが取得できること", async () => {
            // Arrange
            const returnCategory = ReturnCategory.CLUB_TO_FUND_MONTHLY;
            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    returnCategory: returnCategory,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 40000,
                    distributionTargetAmountProfit: 10000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 55000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(
                testHorseId,
                testMemberId,
                returnCategory
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const returns = result._unsafeUnwrap();
            expect(returns).toHaveLength(1);
            expect(returns[0].investmentAndReturnId).toBe(testInvestmentAndReturnId);
            expect(returns[0].returnCategory).toBe(returnCategory);
        });

        it("複数のInvestmentAndReturnReturnが存在する場合、全て取得できること", async () => {
            // Arrange
            const returnCategory = ReturnCategory.CLUB_TO_FUND_MONTHLY;
            const additionalInvestmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 300000,
                    investmentTotal: 600000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    returnCategory: returnCategory,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 40000,
                    distributionTargetAmountProfit: 10000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 55000,
                },
            });

            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: additionalInvestmentAndReturn.investmentAndReturnId,
                    returnCategory: returnCategory,
                    refundableInvestmentAmount: 150000,
                    distributionTargetAmount: 75000,
                    distributionTargetAmountRefundable: 60000,
                    distributionTargetAmountProfit: 15000,
                    distributionTargetAmountWithholdingTax: 7500,
                    distributionAmount: 67500,
                    refundableInvestmentAmountCarriedForward: 82500,
                },
            });

            // Act
            const result = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(
                testHorseId,
                testMemberId,
                returnCategory
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const returns = result._unsafeUnwrap();
            expect(returns).toHaveLength(2);
            
            // 全て同じreturnCategoryであることを確認
            returns.forEach(returnRecord => {
                expect(returnRecord.returnCategory).toBe(returnCategory);
            });
        });

        it("異なるreturnCategoryのデータは取得されないこと", async () => {
            // Arrange
            const targetCategory = ReturnCategory.CLUB_TO_FUND_MONTHLY;
            const differentCategory = ReturnCategory.FUND_TO_MEMBER_MONTHLY;

            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    returnCategory: targetCategory,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 40000,
                    distributionTargetAmountProfit: 10000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 55000,
                },
            });

            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    returnCategory: differentCategory,
                    refundableInvestmentAmount: 200000,
                    distributionTargetAmount: 100000,
                    distributionTargetAmountRefundable: 80000,
                    distributionTargetAmountProfit: 20000,
                    distributionTargetAmountWithholdingTax: 10000,
                    distributionAmount: 90000,
                    refundableInvestmentAmountCarriedForward: 110000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(
                testHorseId,
                testMemberId,
                targetCategory
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const returns = result._unsafeUnwrap();
            expect(returns).toHaveLength(1);
            expect(returns[0].returnCategory).toBe(targetCategory);
        });

        it("異なるhorseIdのデータは取得されないこと", async () => {
            // Arrange
            const returnCategory = ReturnCategory.CLUB_TO_FUND_MONTHLY;
            const differentHorse = await HorseFactory.create();
            const differentInvestmentContract = await InvestmentContractFactory.create({    
                horse: { connect: { horseId: differentHorse.horseId } },
                member: { connect: { memberId: testMemberId } },
            });
            const differentInvestmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: differentHorse.horseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 300000,
                    investmentTotal: 600000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: differentInvestmentAndReturn.investmentAndReturnId,
                    returnCategory: returnCategory,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 40000,
                    distributionTargetAmountProfit: 10000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 55000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(
                testHorseId,
                testMemberId,
                returnCategory
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const returns = result._unsafeUnwrap();
            expect(returns).toHaveLength(0); // 異なるhorseIdのデータは取得されない
        });

        it("異なるmemberIdのデータは取得されないこと", async () => {
            // Arrange
            const returnCategory = ReturnCategory.CLUB_TO_FUND_MONTHLY;
            const differentHorse = await HorseFactory.create();
            const differentMember = await MemberFactory.create();
            const differentInvestmentContract = await InvestmentContractFactory.create({
                horse: { connect: { horseId: testHorseId } },
                member: { connect: { memberId: differentMember.memberId } },
            });
            const differentInvestmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: differentHorse.horseId,
                    memberId: testMemberId,
                    createdDate: new Date("2023-02-01"),
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 300000,
                    investmentTotal: 600000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: differentInvestmentAndReturn.investmentAndReturnId,
                    returnCategory: returnCategory,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 40000,
                    distributionTargetAmountProfit: 10000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 55000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(
                testHorseId,
                testMemberId,
                returnCategory
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const returns = result._unsafeUnwrap();
            expect(returns).toHaveLength(0); // 異なるmemberIdのデータは取得されない
        });

        it("存在しないreturnCategoryの場合は空配列が返されること", async () => {
            // Arrange
            const nonExistentCategory = ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER;

            // Act
            const result = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(
                testHorseId,
                testMemberId,
                nonExistentCategory
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const returns = result._unsafeUnwrap();
            expect(returns).toHaveLength(0);
        });

        it("取得されたデータの構造が正しいこと", async () => {
            // Arrange
            const returnCategory = ReturnCategory.CLUB_TO_FUND_MONTHLY;
            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    returnCategory: returnCategory,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 40000,
                    distributionTargetAmountProfit: 10000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 55000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(
                testHorseId,
                testMemberId,
                returnCategory
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const returns = result._unsafeUnwrap();
            expect(returns).toHaveLength(1);
            
            const returnRecord = returns[0];
            expect(returnRecord).toHaveProperty('investmentAndReturnReturnId');
            expect(returnRecord).toHaveProperty('investmentAndReturnId');
            expect(returnRecord).toHaveProperty('returnCategory');
            expect(returnRecord).toHaveProperty('refundableInvestmentAmount');
            expect(returnRecord).toHaveProperty('distributionTargetAmount');
            expect(returnRecord).toHaveProperty('distributionTargetAmountRefundable');
            expect(returnRecord).toHaveProperty('distributionTargetAmountProfit');
            expect(returnRecord).toHaveProperty('distributionTargetAmountWithholdingTax');
            expect(returnRecord).toHaveProperty('distributionAmount');
            expect(returnRecord).toHaveProperty('refundableInvestmentAmountCarriedForward');
        });

        it("全てのReturnCategoryでテストできること", async () => {
            // Arrange
            const categories = [
                ReturnCategory.CLUB_TO_FUND_MONTHLY,
                ReturnCategory.CLUB_TO_FUND_YEARLY,
                ReturnCategory.FUND_TO_MEMBER_MONTHLY,
                ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER,
                ReturnCategory.FUND_TO_MEMBER_YEARLY_CLUB,
            ];

            for (const category of categories) {
                await vPrisma.client.investmentAndReturnReturn.create({
                    data: {
                        investmentAndReturnId: testInvestmentAndReturnId,
                        returnCategory: category,
                        refundableInvestmentAmount: 100000,
                        distributionTargetAmount: 50000,
                        distributionTargetAmountRefundable: 40000,
                        distributionTargetAmountProfit: 10000,
                        distributionTargetAmountWithholdingTax: 5000,
                        distributionAmount: 45000,
                        refundableInvestmentAmountCarriedForward: 55000,
                    },
                });
            }

            // Act & Assert
            for (const category of categories) {
                const result = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(
                    testHorseId,
                    testMemberId,
                    category
                );

                expect(result.isOk()).toBe(true);
                const returns = result._unsafeUnwrap();
                expect(returns).toHaveLength(1);
                expect(returns[0].returnCategory).toBe(category);
            }
        });
    });

    describe("getTotalRacehorseInvestmentByHorseIdAndMemberIdAndDate", () => {
        it("指定日以前の競走馬出資金の合計額が取得できること", async () => {
            // Arrange
            const targetDate = new Date("2023-12-31");
            const beforeDate = new Date("2023-06-01");
            const afterDate = new Date("2024-06-01");

            const investmentAndReturn1 = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: beforeDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 100000,
                    investmentTotal: 250000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });
            const investmentAndReturn2 = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: afterDate,
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 300000,
                    investmentTotal: 600000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: investmentAndReturn1.investmentAndReturnId,
                    racehorseInvestment: 1000000,
                    runningCost: 50000,
                    subsidy: 10000,
                    insuranceInvestment: 30000,
                    otherInvestment: 20000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 40000,
                    currentMonthInvestmentTotal: 1100000,
                    racehorseInvestmentEquivalent: 1000000,
                },
            });
            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: investmentAndReturn2.investmentAndReturnId,
                    racehorseInvestment: 500000,
                    runningCost: 25000,
                    subsidy: 5000,
                    insuranceInvestment: 15000,
                    otherInvestment: 10000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 20000,
                    currentMonthInvestmentTotal: 550000,
                    racehorseInvestmentEquivalent: 500000,
                },
            });

            // Act
            const result = await getTotalRacehorseInvestmentByHorseIdAndMemberIdAndDate(
                testHorseId,
                testMemberId,
                targetDate
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const total = result._unsafeUnwrap();
            expect(total).toBe(1000000); // afterDateのデータは含まれない
        });
    });

    describe("getTotalInsuranceInvestmentByHorseIdAndMemberIdAndDate", () => {
        it("指定日以前の保険料出資金の合計額が取得できること", async () => {
            // Arrange
            const targetDate = new Date("2023-12-31");
            const beforeDate = new Date("2023-06-01");

            const investmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: beforeDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
                    racehorseInvestment: 1000000,
                    runningCost: 50000,
                    subsidy: 10000,
                    insuranceInvestment: 50000,
                    otherInvestment: 20000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 40000,
                    currentMonthInvestmentTotal: 1100000,
                    racehorseInvestmentEquivalent: 1000000,
                },
            });

            // Act
            const result = await getTotalInsuranceInvestmentByHorseIdAndMemberIdAndDate(
                testHorseId,
                testMemberId,
                targetDate
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const total = result._unsafeUnwrap();
            expect(total).toBe(50000);
        });
    });

    describe("getTotalOtherInvestmentByHorseIdAndMemberIdAndDate", () => {
        it("指定日以前のその他出資金の合計額が取得できること", async () => {
            // Arrange
            const targetDate = new Date("2023-12-31");
            const beforeDate = new Date("2023-06-01");

            const investmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: beforeDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
                    racehorseInvestment: 1000000,
                    runningCost: 50000,
                    subsidy: 10000,
                    insuranceInvestment: 30000,
                    otherInvestment: 30000,
                    discountAllocation: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 40000,
                    currentMonthInvestmentTotal: 1100000,
                    racehorseInvestmentEquivalent: 1000000,
                },
            });

            // Act
            const result = await getTotalOtherInvestmentByHorseIdAndMemberIdAndDate(
                testHorseId,
                testMemberId,
                targetDate
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const total = result._unsafeUnwrap();
            expect(total).toBe(30000);
        });
    });

    describe("getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate", () => {
        it("指定日以前の分配対象額の合計額が取得できること", async () => {
            // Arrange
            const targetDate = new Date("2023-12-31");
            const beforeDate = new Date("2023-06-01");
            const returnCategory = ReturnCategory.CLUB_TO_FUND_MONTHLY;

            const investmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: beforeDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
                    returnCategory: returnCategory,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 100000,
                    distributionTargetAmountProfit: 10000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 55000,
                },
            });

            // Act
            const result = await getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate(
                testHorseId,
                testMemberId,
                targetDate,
                returnCategory
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const total = result._unsafeUnwrap();
            expect(total).toBe(100000);
        });
    });

    describe("getInvestmentAndReturnReturnListByHorseIdAndMemberIdAndReturnCategory", () => {
        it("指定された条件の分配金リストが取得できること", async () => {
            // Arrange
            const returnCategory = ReturnCategory.FUND_TO_MEMBER_MONTHLY;
            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: testInvestmentAndReturnId,
                    returnCategory: returnCategory,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 40000,
                    distributionTargetAmountProfit: 10000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 55000,
                },
            });

            // Act
            const result = await getInvestmentAndReturnReturnListByHorseIdAndMemberIdAndReturnCategory(
                testHorseId,
                testMemberId,
                returnCategory
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const returns = result._unsafeUnwrap();
            expect(returns).toHaveLength(1);
            expect(returns[0].returnCategory).toBe(returnCategory);
        });
    });

    describe("createInvestmentAndReturn", () => {
        it("新しいInvestmentAndReturnが作成できること", async () => {
            // Arrange
            const createdDate = new Date("2023-12-01");
            const data = {
                horseId: testHorseId,
                memberId: testMemberId,
                createdDate: createdDate,
                progressedMonth: 1,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
                sharesNumber: 1,
            };

            // Act
            const result = await createInvestmentAndReturn(
                data.horseId,
                data.memberId,
                data.createdDate,
                data.progressedMonth,
                data.yearlyReturnTargetFlag,
                data.sharesNumber,
                data.runningCostInvestmentTotal,
                data.insuranceInvestmentTotal,
                data.otherInvestmentTotal,
                data.investmentTotal,
                data.racehorseBookValueEndOfLastMonth,
                data.investmentRefundPaidUpToLastMonth,
                data.organizerWithholdingTaxTotal,
                data.organizerWithholdingTaxCurrentMonthAddition,
                data.clubWithholdingTaxTotal,
                data.clubWithholdingTaxCurrentMonthAddition,
                data.billingAmount,
                data.paymentAmount
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const created = result._unsafeUnwrap();
            expect(created.horseId).toBe(data.horseId);
            expect(created.memberId).toBe(data.memberId);
            expect(created.createdDate).toEqual(data.createdDate);
            expect(created.investmentTotal).toBe(data.investmentTotal);
            expect(created.sharesNumber).toBe(data.sharesNumber);
        });
    });

    describe("updatePaymentAmount", () => {
        it("paymentAmountが正常に更新されること", async () => {
            // Arrange
            const newPaymentAmount = 50000;

            // Act
            const result = await updatePaymentAmount(testInvestmentAndReturnId, newPaymentAmount);

            // Assert
            expect(result.isOk()).toBe(true);
            const updated = result._unsafeUnwrap();
            expect(updated.paymentAmount).toBe(newPaymentAmount);
            expect(updated.investmentAndReturnId).toBe(testInvestmentAndReturnId);
        });

        it("存在しないinvestmentAndReturnIdの場合はエラーが発生すること", async () => {
            // Arrange
            const nonExistentId = 999999;
            const newPaymentAmount = 50000;

            // Act
            const result = await updatePaymentAmount(nonExistentId, newPaymentAmount);

            // Assert
            expect(result.isErr()).toBe(true);
        });
    });

    describe("createInvestmentAndReturnInvestment", () => {
        it("新しいInvestmentAndReturnInvestmentが作成できること", async () => {
            // Arrange
            const data = {
                investmentAndReturnId: testInvestmentAndReturnId,
                racehorseInvestment: 1000000,
                runningCost: 50000,
                subsidy: 10000,
                insuranceInvestment: 30000,
                otherInvestment: 20000,
                discountAllocation: 0,
                retroactiveRunningCost: 0,
                runningCostInvestment: 40000,
                currentMonthInvestmentTotal: 1100000,
                racehorseInvestmentEquivalent: 1000000,
            };

            // Act
            const result = await createInvestmentAndReturnInvestment(
                data.investmentAndReturnId,
                data.racehorseInvestment,
                data.runningCost,
                data.subsidy,
                data.insuranceInvestment,
                data.otherInvestment,
                data.discountAllocation,
                data.retroactiveRunningCost,
                data.runningCostInvestment,
                data.currentMonthInvestmentTotal,
                data.racehorseInvestmentEquivalent
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const created = result._unsafeUnwrap();
            expect(created.investmentAndReturnId).toBe(data.investmentAndReturnId);
            expect(created.racehorseInvestment).toBe(data.racehorseInvestment);
            expect(created.runningCost).toBe(data.runningCost);
            expect(created.insuranceInvestment).toBe(data.insuranceInvestment);
        });
    });

    describe("createInvestmentAndReturnReturn", () => {
        it("新しいInvestmentAndReturnReturnが作成できること", async () => {
            // Arrange
            const returnCategory = ReturnCategory.CLUB_TO_FUND_MONTHLY;
            const data = {
                investmentAndReturnId: testInvestmentAndReturnId,
                returnCategory: returnCategory,
                investmentRefundPaidUpToLastMonth: 2000,
                refundableInvestmentAmount: 100000,
                distributionTargetAmount: 50000,
                distributionTargetAmountRefundable: 40000,
                distributionTargetAmountProfit: 10000,
                distributionTargetAmountWithholdingTax: 5000,
                distributionAmount: 45000,
                refundableInvestmentAmountCarriedForward: 55000,
            };

            // Act
            const result = await createInvestmentAndReturnReturn(
                data.investmentAndReturnId,
                data.returnCategory,
                data.investmentRefundPaidUpToLastMonth,
                data.refundableInvestmentAmount,
                data.distributionTargetAmount,
                data.distributionTargetAmountRefundable,
                data.distributionTargetAmountProfit,
                data.distributionTargetAmountWithholdingTax,
                data.distributionAmount,
                data.refundableInvestmentAmountCarriedForward
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const created = result._unsafeUnwrap();
            expect(created.investmentAndReturnId).toBe(data.investmentAndReturnId);
            expect(created.returnCategory).toBe(data.returnCategory);
            expect(created.investmentRefundPaidUpToLastMonth).toBe(data.investmentRefundPaidUpToLastMonth);
            expect(created.refundableInvestmentAmount).toBe(data.refundableInvestmentAmount);
            expect(created.distributionAmount).toBe(data.distributionAmount);
        });
    });

    it("getCreatedInvestmentAndReturnByHorseId: createdDateごとにCOMPLETEDかつcontractEndAtがnullのInvestmentContractのsharesNumberが合計される", async () => {
        // Arrange
        const completedStatus = "COMPLETED";
        const date1 = new Date("2024-01-01");
        const date2 = new Date("2024-02-01");

        // 1つ目のInvestmentContract（COMPLETED, contractEndAt: null）
        const contract1 = await InvestmentContractFactory.create({
            horse: { connect: { horseId: testHorseId } },
            member: { connect: { memberId: testMemberId } },
            sharesNumber: 5,
            contractStatus: completedStatus,
            contractEndAt: null,
        });
        // 2つ目のInvestmentContract（COMPLETED, contractEndAt: null）
        const contract2 = await InvestmentContractFactory.create({
            horse: { connect: { horseId: testHorseId } },
            member: { connect: { memberId: testMemberId } },
            sharesNumber: 7,
            contractStatus: completedStatus,
            contractEndAt: null,
        });
        // 3つ目のInvestmentContract（COMPLETED, contractEndAt: あり→除外）
        await InvestmentContractFactory.create({
            horse: { connect: { horseId: testHorseId } },
            member: { connect: { memberId: testMemberId } },
            sharesNumber: 99,
            contractStatus: completedStatus,
            contractEndAt: new Date("2024-03-01"),
        });
        // 4つ目のInvestmentContract（STARTED→除外）
        await InvestmentContractFactory.create({
            horse: { connect: { horseId: testHorseId } },
            member: { connect: { memberId: testMemberId } },
            sharesNumber: 99,
            contractStatus: "STARTED",
            contractEndAt: null,
        });

        // InvestmentAndReturn（date1, contract1）
        await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: testHorseId,
                memberId: testMemberId,
                createdDate: date1,
                progressedMonth: 1,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });
        // InvestmentAndReturn（date1, contract2）
        await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: testHorseId,
                memberId: testMemberId,
                createdDate: date1,
                progressedMonth: 1,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });
        // InvestmentAndReturn（date2, contract1）
        await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: testHorseId,
                memberId: testMemberId,
                createdDate: date2,
                progressedMonth: 2,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });

        // Act
        const result = await getCreatedInvestmentAndReturnSharesSumByHorseId(testHorseId);

        // Assert
        expect(result.isOk()).toBe(true);
        const list = result._unsafeUnwrap();
        // date1: contract1(5) + contract2(7) = 12, date2: contract1(5)
        const date1Sum = list.find(x => x.createdDate.getTime() === date1.getTime());
        const date2Sum = list.find(x => x.createdDate.getTime() === date2.getTime());
        expect(date1Sum?.sharesSum).toBe(2);
        expect(date2Sum?.sharesSum).toBe(1);
    });

    it("getCreatedInvestmentAndReturnByHorseId: 複数日付で複数件返る場合も正しく集計される", async () => {
        // Arrange
        const completedStatus = "COMPLETED";
        const date1 = new Date("2024-01-01");
        const date2 = new Date("2024-02-01");
        const date3 = new Date("2024-03-01");

        // テスト用の馬・会員を新規作成
        const myHorse = await HorseFactory.create();
        const myMember = await MemberFactory.create();
        const contract1 = await InvestmentContractFactory.create({
            horse: { connect: { horseId: myHorse.horseId } },
            member: { connect: { memberId: myMember.memberId } },
            sharesNumber: 5,
            contractStatus: completedStatus,
            contractEndAt: null,
        });
        // 各日付でInvestmentAndReturnを作成
        await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: myHorse.horseId,
                memberId: myMember.memberId,
                createdDate: date1,
                progressedMonth: 1,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });
        await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: myHorse.horseId,
                memberId: myMember.memberId,
                createdDate: date2,
                progressedMonth: 2,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });
        await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: myHorse.horseId,
                memberId: myMember.memberId,
                createdDate: date3,
                progressedMonth: 3,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });

        // 別の馬・会員（同じ日付で混在させる）
        const otherHorse = await HorseFactory.create();
        const otherMember = await MemberFactory.create();
        const otherContract = await InvestmentContractFactory.create({
            horse: { connect: { horseId: otherHorse.horseId } },
            member: { connect: { memberId: otherMember.memberId } },
            sharesNumber: 99,
            contractStatus: completedStatus,
            contractEndAt: null,
        });
        await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: otherHorse.horseId,
                memberId: otherMember.memberId,
                createdDate: date1,
                progressedMonth: 1,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });

        // Act
        const result = await getCreatedInvestmentAndReturnSharesSumByHorseId(myHorse.horseId);

        // Assert
        expect(result.isOk()).toBe(true);
        const list = result._unsafeUnwrap();
        expect(list.length).toBeGreaterThanOrEqual(3);
        const date1Item = list.find(x => x.createdDate.getTime() === date1.getTime());
        const date2Item = list.find(x => x.createdDate.getTime() === date2.getTime());
        const date3Item = list.find(x => x.createdDate.getTime() === date3.getTime());
        expect(date1Item).toBeDefined();
        expect(date2Item).toBeDefined();
        expect(date3Item).toBeDefined();
        expect(date1Item!.sharesSum).toBe(1);
        expect(date2Item!.sharesSum).toBe(1);
        expect(date3Item!.sharesSum).toBe(1);
    });

    it("getCreatedInvestmentAndReturnByHorseId: 指定したhorseId以外のデータは集計に含まれない", async () => {
        // Arrange
        const completedStatus = "COMPLETED";
        const date1 = new Date("2024-01-01");

        // テスト用の馬・会員を新規作成
        const myHorse = await HorseFactory.create();
        const myMember = await MemberFactory.create();
        const contract1 = await InvestmentContractFactory.create({
            horse: { connect: { horseId: myHorse.horseId } },
            member: { connect: { memberId: myMember.memberId } },
            sharesNumber: 5,
            contractStatus: completedStatus,
            contractEndAt: null,
        });
        await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: myHorse.horseId,
                memberId: myMember.memberId,
                createdDate: date1,
                progressedMonth: 1,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });

        // 別の馬・会員
        const otherHorse = await HorseFactory.create();
        const otherMember = await MemberFactory.create();
        const otherContract = await InvestmentContractFactory.create({
            horse: { connect: { horseId: otherHorse.horseId } },
            member: { connect: { memberId: otherMember.memberId } },
            sharesNumber: 99,
            contractStatus: completedStatus,
            contractEndAt: null,
        });
        await vPrisma.client.investmentAndReturn.create({
            data: {
                horseId: otherHorse.horseId,
                memberId: otherMember.memberId,
                createdDate: date1,
                progressedMonth: 1,
                yearlyReturnTargetFlag: false,
                runningCostInvestmentTotal: 100000,
                insuranceInvestmentTotal: 50000,
                otherInvestmentTotal: 200000,
                investmentTotal: 350000,
                racehorseBookValueEndOfLastMonth: 0,
                investmentRefundPaidUpToLastMonth: 0,
                organizerWithholdingTaxTotal: 0,
                organizerWithholdingTaxCurrentMonthAddition: 0,
                clubWithholdingTaxTotal: 0,
                clubWithholdingTaxCurrentMonthAddition: 0,
                billingAmount: 150000,
                paymentAmount: 0,
            },
        });

        // Act
        const result = await getCreatedInvestmentAndReturnSharesSumByHorseId(myHorse.horseId);

        // Assert
        expect(result.isOk()).toBe(true);
        const list = result._unsafeUnwrap();
        expect(list.length).toBe(1);
        const date1Item = list.find(x => x.createdDate.getTime() === date1.getTime());
        expect(date1Item).toBeDefined();
        expect(date1Item!.sharesSum).toBe(1);
    });

    describe("getInvestmentAndReturnListByHorseIdAndCreatedDate", () => {
        it("指定されたhorseIdとcreatedDateのInvestmentAndReturnリストが取得できること", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            
            // 指定日付のInvestmentAndReturn
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await getInvestmentAndReturnListByHorseIdAndCreatedDate(testHorseId, testDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            expect(list[0].horseId).toBe(testHorseId);
            expect(list[0].memberId).toBe(testMemberId);
            expect(list[0].createdDate).toEqual(testDate);
        });

        it("複数のInvestmentAndReturnが存在する場合、全て取得できること", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const additionalMember = await MemberFactory.create();
            const additionalContract = await InvestmentContractFactory.create({
                horse: { connect: { horseId: testHorseId } },
                member: { connect: { memberId: additionalMember.memberId } },
            });

            // 1つ目のInvestmentAndReturn
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // 2つ目のInvestmentAndReturn
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: additionalMember.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await getInvestmentAndReturnListByHorseIdAndCreatedDate(testHorseId, testDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(2);
            
            // 全て同じhorseIdとcreatedDateであることを確認
            list.forEach(record => {
                expect(record.horseId).toBe(testHorseId);
                expect(record.createdDate).toEqual(testDate);
            });

            // 会員情報が正しく含まれていることを確認
            const memberIds = list.map(record => record.memberId);
            expect(memberIds).toContain(testMemberId);
            expect(memberIds).toContain(additionalMember.memberId);
        });

        it("異なる日付のデータは取得されないこと", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const differentDate = new Date("2024-02-15");
            
            // 指定日付のInvestmentAndReturn
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // 異なる日付のInvestmentAndReturn（これは取得されない）
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: differentDate,
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: true,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 100000,
                },
            });

            // Act
            const result = await getInvestmentAndReturnListByHorseIdAndCreatedDate(testHorseId, testDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            expect(list[0].createdDate).toEqual(testDate);
        });

        it("異なる馬のデータは取得されないこと", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const differentHorse = await HorseFactory.create();
            const differentContract = await InvestmentContractFactory.create({
                horse: { connect: { horseId: differentHorse.horseId } },
                member: { connect: { memberId: testMemberId } },
            });
            
            // 指定馬のInvestmentAndReturn
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // 別の馬のInvestmentAndReturn（これは取得されない）
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: differentHorse.horseId,
                    memberId: testMemberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await getInvestmentAndReturnListByHorseIdAndCreatedDate(testHorseId, testDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            expect(list[0].horseId).toBe(testHorseId);
        });

        it("データが存在しない場合、空のリストが返されること", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");

            // Act
            const result = await getInvestmentAndReturnListByHorseIdAndCreatedDate(testHorseId, testDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(0);
        });

        it("取得されたデータの構造が正しいこと", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await getInvestmentAndReturnListByHorseIdAndCreatedDate(testHorseId, testDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            
            const record = list[0];
            expect(record).toHaveProperty('investmentAndReturnId');
            expect(record).toHaveProperty('horseId');
            expect(record).toHaveProperty('memberId');
            expect(record).toHaveProperty('createdDate');
            expect(record).toHaveProperty('progressedMonth');
            expect(record).toHaveProperty('yearlyReturnTargetFlag');
            expect(record).toHaveProperty('runningCostInvestmentTotal');
            expect(record).toHaveProperty('insuranceInvestmentTotal');
            expect(record).toHaveProperty('investmentTotal');
            expect(record).toHaveProperty('racehorseBookValueEndOfLastMonth');
            expect(record).toHaveProperty('investmentRefundPaidUpToLastMonth');
            expect(record).toHaveProperty('organizerWithholdingTaxTotal');
            expect(record).toHaveProperty('organizerWithholdingTaxCurrentMonthAddition');
            expect(record).toHaveProperty('clubWithholdingTaxTotal');
            expect(record).toHaveProperty('clubWithholdingTaxCurrentMonthAddition');
            expect(record).toHaveProperty('billingAmount');
            expect(record).toHaveProperty('paymentAmount');
            
            // includeされた関連データの構造確認
            expect(record).toHaveProperty('member');
            expect(record.member).toHaveProperty('memberId');
            expect(record.member).toHaveProperty('firstName');
            expect(record.member).toHaveProperty('lastName');
        });

        it("会員情報が正しく含まれていること", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const member = await MemberFactory.create({
                firstName: '太郎',
                lastName: '田中',
            });
            const contract = await InvestmentContractFactory.create({
                horse: { connect: { horseId: testHorseId } },
                member: { connect: { memberId: member.memberId } },
            });

            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: member.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await getInvestmentAndReturnListByHorseIdAndCreatedDate(testHorseId, testDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            
            const record = list[0];
            expect(record.member.memberId).toBe(member.memberId);
            expect(record.member.firstName).toBe('太郎');
            expect(record.member.lastName).toBe('田中');
        });
    });

    describe("getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId", () => {
        it("指定された条件の出資・分配金を正常に取得できること", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const member = await MemberFactory.create({
                firstName: '太郎',
                lastName: '田中',
            });

            // InvestmentAndReturnを作成
            const investmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: member.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // InvestmentAndReturnInvestmentを作成
            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
                    racehorseInvestmentEquivalent: 100000,
                    discountAllocation: 0,
                    racehorseInvestment: 100000,
                    runningCost: 50000,
                    subsidy: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 50000,
                    insuranceInvestment: 50000,
                    otherInvestment: 0,
                    currentMonthInvestmentTotal: 200000,
                },
            });

            // InvestmentAndReturnReturnを作成
            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
                    returnCategory: ReturnCategory.FUND_TO_MEMBER_MONTHLY,
                    investmentRefundPaidUpToLastMonth: 0,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 30000,
                    distributionTargetAmountProfit: 20000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 70000,
                },
            });

            // Act
            const result = await getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(
                testHorseId,
                testDate,
                member.memberId
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const data = result._unsafeUnwrap();
            expect(data).not.toBeNull();
            
            // InvestmentAndReturnの基本情報
            expect(data!.investmentAndReturnId).toBe(investmentAndReturn.investmentAndReturnId);
            expect(data!.horseId).toBe(testHorseId);
            expect(data!.memberId).toBe(member.memberId);
            expect(data!.createdDate).toEqual(testDate);
            expect(data!.progressedMonth).toBe(1);
            expect(data!.yearlyReturnTargetFlag).toBe(false);
            expect(data!.sharesNumber).toBe(2);
            expect(data!.billingAmount).toBe(150000);
            expect(data!.paymentAmount).toBe(0);

            // InvestmentAndReturnInvestmentの情報
            expect(data!.investment).not.toBeNull();
            const investment = data!.investment!;
            expect(investment.racehorseInvestmentEquivalent).toBe(100000);
            expect(investment.discountAllocation).toBe(0);
            expect(investment.racehorseInvestment).toBe(100000);
            expect(investment.runningCost).toBe(50000);
            expect(investment.subsidy).toBe(0);
            expect(investment.retroactiveRunningCost).toBe(0);
            expect(investment.runningCostInvestment).toBe(50000);
            expect(investment.insuranceInvestment).toBe(50000);
            expect(investment.otherInvestment).toBe(0);
            expect(investment.currentMonthInvestmentTotal).toBe(200000);

            // InvestmentAndReturnReturnの情報
            expect(data!.returns).toHaveLength(1);
            const returnItem = data!.returns[0];
            expect(returnItem.returnCategory).toBe(ReturnCategory.FUND_TO_MEMBER_MONTHLY);
            expect(returnItem.investmentRefundPaidUpToLastMonth).toBe(0);
            expect(returnItem.refundableInvestmentAmount).toBe(100000);
            expect(returnItem.distributionTargetAmount).toBe(50000);
            expect(returnItem.distributionTargetAmountRefundable).toBe(30000);
            expect(returnItem.distributionTargetAmountProfit).toBe(20000);
            expect(returnItem.distributionTargetAmountWithholdingTax).toBe(5000);
            expect(returnItem.distributionAmount).toBe(45000);
            expect(returnItem.refundableInvestmentAmountCarriedForward).toBe(70000);
        });

        it("異なる会員のデータは取得されないこと", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const member1 = await MemberFactory.create({
                firstName: '太郎',
                lastName: '田中',
            });
            const member2 = await MemberFactory.create({
                firstName: '花子',
                lastName: '佐藤',
            });

            // member1のInvestmentAndReturnを作成
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: member1.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // member2のInvestmentAndReturnを作成
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: member2.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 150000,
                    insuranceInvestmentTotal: 75000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 225000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(
                testHorseId,
                testDate,
                member2.memberId // member2を指定
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const data = result._unsafeUnwrap();
            expect(data).not.toBeNull();
            expect(data!.memberId).toBe(member2.memberId);
            expect(data!.billingAmount).toBe(225000);
        });

        it("異なる日付のデータは取得されないこと", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const differentDate = new Date("2024-02-15");
            const member = await MemberFactory.create({
                firstName: '太郎',
                lastName: '田中',
            });

            // 指定日付のInvestmentAndReturnを作成
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: member.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // 異なる日付のInvestmentAndReturnを作成
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: member.memberId,
                    createdDate: differentDate,
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: true,
                    runningCostInvestmentTotal: 150000,
                    insuranceInvestmentTotal: 75000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 425000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 225000,
                    paymentAmount: 100000,
                },
            });

            // Act
            const result = await getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(
                testHorseId,
                testDate,
                member.memberId
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const data = result._unsafeUnwrap();
            expect(data).not.toBeNull();
            expect(data!.createdDate).toEqual(testDate);
            expect(data!.progressedMonth).toBe(1);
            expect(data!.yearlyReturnTargetFlag).toBe(false);
            expect(data!.billingAmount).toBe(150000);
        });

        it("異なる馬のデータは取得されないこと", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const otherHorse = await HorseFactory.create({
                recruitmentYear: 2016,
                recruitmentNo: 2,
                horseName: '別の馬',
                recruitmentName: '別の募集馬',
                sharesTotal: 300,
                amountTotal: 27000000,
                birthYear: 2015,
                birthMonth: 6,
                birthDay: 15,
                fundStartYear: 2017,
                fundStartMonth: 5,
                fundStartDay: 1,
            });
            const member = await MemberFactory.create({
                firstName: '太郎',
                lastName: '田中',
            });

            // 指定馬のInvestmentAndReturnを作成
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: member.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // 別の馬のInvestmentAndReturnを作成
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: otherHorse.horseId,
                    memberId: member.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 50000,
                    insuranceInvestmentTotal: 25000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 275000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 75000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(
                testHorseId,
                testDate,
                member.memberId
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const data = result._unsafeUnwrap();
            expect(data).not.toBeNull();
            expect(data!.horseId).toBe(testHorseId);
            expect(data!.billingAmount).toBe(150000);
        });

        it("データが存在しない場合はnullを返すこと", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const member = await MemberFactory.create({
                firstName: '太郎',
                lastName: '田中',
            });

            // Act
            const result = await getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(
                testHorseId,
                testDate,
                member.memberId
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const data = result._unsafeUnwrap();
            expect(data).toBeNull();
        });

        it("InvestmentAndReturnInvestmentが存在しない場合でも正常に動作すること", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const member = await MemberFactory.create({
                firstName: '太郎',
                lastName: '田中',
            });

            // InvestmentAndReturnのみを作成（InvestmentAndReturnInvestmentは作成しない）
            const investmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: member.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(
                testHorseId,
                testDate,
                member.memberId
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const data = result._unsafeUnwrap();
            expect(data).not.toBeNull();
            expect(data!.investmentAndReturnId).toBe(investmentAndReturn.investmentAndReturnId);
            expect(data!.investment).toBeNull(); // InvestmentAndReturnInvestmentは存在しない
            expect(data!.returns).toHaveLength(0); // InvestmentAndReturnReturnも存在しない
        });

        it("複数のInvestmentAndReturnReturnが存在する場合、全て取得されること", async () => {
            // Arrange
            const testDate = new Date("2024-01-15");
            const member = await MemberFactory.create({
                firstName: '太郎',
                lastName: '田中',
            });

            // InvestmentAndReturnを作成
            const investmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: member.memberId,
                    createdDate: testDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // 複数のInvestmentAndReturnReturnを作成
            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
                    returnCategory: ReturnCategory.FUND_TO_MEMBER_MONTHLY,
                    investmentRefundPaidUpToLastMonth: 0,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 30000,
                    distributionTargetAmountProfit: 20000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 70000,
                },
            });

            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
                    returnCategory: ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER,
                    investmentRefundPaidUpToLastMonth: 0,
                    refundableInvestmentAmount: 200000,
                    distributionTargetAmount: 100000,
                    distributionTargetAmountRefundable: 60000,
                    distributionTargetAmountProfit: 40000,
                    distributionTargetAmountWithholdingTax: 10000,
                    distributionAmount: 90000,
                    refundableInvestmentAmountCarriedForward: 140000,
                },
            });

            // Act
            const result = await getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(
                testHorseId,
                testDate,
                member.memberId
            );

            // Assert
            expect(result.isOk()).toBe(true);
            const data = result._unsafeUnwrap();
            expect(data).not.toBeNull();
            expect(data!.returns).toHaveLength(2);
            
            // 最初のreturn
            expect(data!.returns[0].returnCategory).toBe(ReturnCategory.FUND_TO_MEMBER_MONTHLY);
            expect(data!.returns[0].distributionAmount).toBe(45000);
            
            // 2番目のreturn
            expect(data!.returns[1].returnCategory).toBe(ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER);
            expect(data!.returns[1].distributionAmount).toBe(90000);
        });
    });

    describe("listInvestmentAndReturnByMemberIdAndOccurredDate", () => {
        it("指定されたmemberIdとoccurredDateのInvestmentAndReturnが取得できること", async () => {
            // Arrange
            const occurredDate = new Date("2024-01-15");
            
            // 指定日付のInvestmentAndReturn
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: occurredDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByMemberIdAndOccurredDate(testMemberId, occurredDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            expect(list[0].memberId).toBe(testMemberId);
            expect(list[0].horseId).toBe(testHorseId);
            expect(list[0].createdDate).toEqual(occurredDate);
            expect(list[0].sharesNumber).toBe(2);
            expect(list[0].billingAmount).toBe(150000);
            expect(list[0].paymentAmount).toBe(0);
        });

        it("複数のInvestmentAndReturnが存在する場合、全て取得できること", async () => {
            // Arrange
            const occurredDate = new Date("2024-01-15");
            const additionalHorse = await HorseFactory.create();
            
            // 1つ目のInvestmentAndReturn
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: occurredDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // 2つ目のInvestmentAndReturn（別の馬）
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: additionalHorse.horseId,
                    memberId: testMemberId,
                    createdDate: occurredDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: true,
                    sharesNumber: 3,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 300000,
                    investmentTotal: 600000,
                    racehorseBookValueEndOfLastMonth: 50000,
                    investmentRefundPaidUpToLastMonth: 25000,
                    organizerWithholdingTaxTotal: 5000,
                    organizerWithholdingTaxCurrentMonthAddition: 1000,
                    clubWithholdingTaxTotal: 3000,
                    clubWithholdingTaxCurrentMonthAddition: 500,
                    billingAmount: 300000,
                    paymentAmount: 50000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByMemberIdAndOccurredDate(testMemberId, occurredDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(2);
            
            // 全て同じmemberIdとcreatedDateであることを確認
            list.forEach(record => {
                expect(record.memberId).toBe(testMemberId);
                expect(record.createdDate).toEqual(occurredDate);
            });

            // 馬の情報が正しく含まれていることを確認
            const horseIds = list.map(record => record.horse.horseId);
            expect(horseIds).toContain(testHorseId);
            expect(horseIds).toContain(additionalHorse.horseId);
        });

        it("異なるmemberIdのデータは取得されないこと", async () => {
            // Arrange
            const occurredDate = new Date("2024-01-15");
            const differentMember = await MemberFactory.create();
            
            // 異なる会員のInvestmentAndReturn
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: differentMember.memberId,
                    createdDate: occurredDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByMemberIdAndOccurredDate(testMemberId, occurredDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(0); // 異なるmemberIdのデータは取得されない
        });

        it("異なる日付のデータは取得されないこと", async () => {
            // Arrange
            const occurredDate = new Date("2024-01-15");
            const differentDate = new Date("2024-02-15");
            
            // 異なる日付のInvestmentAndReturn
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: differentDate,
                    progressedMonth: 2,
                    yearlyReturnTargetFlag: true,
                    sharesNumber: 3,
                    runningCostInvestmentTotal: 200000,
                    insuranceInvestmentTotal: 100000,
                    otherInvestmentTotal: 300000,
                    investmentTotal: 600000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 300000,
                    paymentAmount: 100000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByMemberIdAndOccurredDate(testMemberId, occurredDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(0); // 異なる日付のデータは取得されない
        });

        it("データが存在しない場合、空のリストが返されること", async () => {
            // Arrange
            const occurredDate = new Date("2024-01-15");

            // Act
            const result = await listInvestmentAndReturnByMemberIdAndOccurredDate(testMemberId, occurredDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(0);
        });

        it("取得されたデータの構造が正しいこと", async () => {
            // Arrange
            const occurredDate = new Date("2024-01-15");
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: occurredDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByMemberIdAndOccurredDate(testMemberId, occurredDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            
            const record = list[0];
            expect(record).toHaveProperty('investmentAndReturnId');
            expect(record).toHaveProperty('horseId');
            expect(record).toHaveProperty('memberId');
            expect(record).toHaveProperty('createdDate');
            expect(record).toHaveProperty('progressedMonth');
            expect(record).toHaveProperty('yearlyReturnTargetFlag');
            expect(record).toHaveProperty('sharesNumber');
            expect(record).toHaveProperty('runningCostInvestmentTotal');
            expect(record).toHaveProperty('insuranceInvestmentTotal');
            expect(record).toHaveProperty('investmentTotal');
            expect(record).toHaveProperty('racehorseBookValueEndOfLastMonth');
            expect(record).toHaveProperty('investmentRefundPaidUpToLastMonth');
            expect(record).toHaveProperty('organizerWithholdingTaxTotal');
            expect(record).toHaveProperty('organizerWithholdingTaxCurrentMonthAddition');
            expect(record).toHaveProperty('clubWithholdingTaxTotal');
            expect(record).toHaveProperty('clubWithholdingTaxCurrentMonthAddition');
            expect(record).toHaveProperty('billingAmount');
            expect(record).toHaveProperty('paymentAmount');
            
            // includeされた関連データの構造確認
            expect(record).toHaveProperty('horse');
            expect(record).toHaveProperty('investment');
            expect(record).toHaveProperty('returns');
            expect(record.horse).toHaveProperty('horseId');
            expect(record.horse).toHaveProperty('horseName');
            expect(Array.isArray(record.returns)).toBe(true);
        });

        it("関連データ（horse、investment、returns）が正しく含まれていること", async () => {
            // Arrange
            const occurredDate = new Date("2024-01-15");
            const horse = await HorseFactory.create({
                horseName: 'テスト馬',
            });
            
            // InvestmentAndReturnを作成
            const investmentAndReturn = await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: horse.horseId,
                    memberId: testMemberId,
                    createdDate: occurredDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // InvestmentAndReturnInvestmentを作成
            await vPrisma.client.investmentAndReturnInvestment.create({
                data: {
                    investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
                    racehorseInvestmentEquivalent: 100000,
                    discountAllocation: 0,
                    racehorseInvestment: 100000,
                    runningCost: 50000,
                    subsidy: 0,
                    retroactiveRunningCost: 0,
                    runningCostInvestment: 50000,
                    insuranceInvestment: 50000,
                    otherInvestment: 0,
                    currentMonthInvestmentTotal: 200000,
                },
            });

            // InvestmentAndReturnReturnを作成
            await vPrisma.client.investmentAndReturnReturn.create({
                data: {
                    investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
                    returnCategory: ReturnCategory.FUND_TO_MEMBER_MONTHLY,
                    investmentRefundPaidUpToLastMonth: 0,
                    refundableInvestmentAmount: 100000,
                    distributionTargetAmount: 50000,
                    distributionTargetAmountRefundable: 30000,
                    distributionTargetAmountProfit: 20000,
                    distributionTargetAmountWithholdingTax: 5000,
                    distributionAmount: 45000,
                    refundableInvestmentAmountCarriedForward: 70000,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByMemberIdAndOccurredDate(testMemberId, occurredDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            
            const record = list[0];
            
            // horseの情報が正しく含まれていることを確認
            expect(record.horse.horseId).toBe(horse.horseId);
            expect(record.horse.horseName).toBe('テスト馬');
            
            // investmentの情報が正しく含まれていることを確認
            expect(record.investment).not.toBeNull();
            expect(record.investment!.racehorseInvestmentEquivalent).toBe(100000);
            expect(record.investment!.racehorseInvestment).toBe(100000);
            expect(record.investment!.runningCost).toBe(50000);
            expect(record.investment!.insuranceInvestment).toBe(50000);
            expect(record.investment!.currentMonthInvestmentTotal).toBe(200000);
            
            // returnsの情報が正しく含まれていることを確認
            expect(record.returns).toHaveLength(1);
            expect(record.returns[0].returnCategory).toBe(ReturnCategory.FUND_TO_MEMBER_MONTHLY);
            expect(record.returns[0].refundableInvestmentAmount).toBe(100000);
            expect(record.returns[0].distributionTargetAmount).toBe(50000);
            expect(record.returns[0].distributionAmount).toBe(45000);
        });

        it("investmentがnullの場合でも正常に動作すること", async () => {
            // Arrange
            const occurredDate = new Date("2024-01-15");
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: occurredDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByMemberIdAndOccurredDate(testMemberId, occurredDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            
            const record = list[0];
            expect(record.investment).toBeNull();
            expect(record.returns).toHaveLength(0);
        });

        it("returnsが空配列の場合でも正常に動作すること", async () => {
            // Arrange
            const occurredDate = new Date("2024-01-15");
            await vPrisma.client.investmentAndReturn.create({
                data: {
                    horseId: testHorseId,
                    memberId: testMemberId,
                    createdDate: occurredDate,
                    progressedMonth: 1,
                    yearlyReturnTargetFlag: false,
                    sharesNumber: 2,
                    runningCostInvestmentTotal: 100000,
                    insuranceInvestmentTotal: 50000,
                    otherInvestmentTotal: 200000,
                    investmentTotal: 350000,
                    racehorseBookValueEndOfLastMonth: 0,
                    investmentRefundPaidUpToLastMonth: 0,
                    organizerWithholdingTaxTotal: 0,
                    organizerWithholdingTaxCurrentMonthAddition: 0,
                    clubWithholdingTaxTotal: 0,
                    clubWithholdingTaxCurrentMonthAddition: 0,
                    billingAmount: 150000,
                    paymentAmount: 0,
                },
            });

            // Act
            const result = await listInvestmentAndReturnByMemberIdAndOccurredDate(testMemberId, occurredDate);

            // Assert
            expect(result.isOk()).toBe(true);
            const list = result._unsafeUnwrap();
            expect(list).toHaveLength(1);
            
            const record = list[0];
            expect(record.returns).toHaveLength(0);
            expect(Array.isArray(record.returns)).toBe(true);
        });
    });
});
