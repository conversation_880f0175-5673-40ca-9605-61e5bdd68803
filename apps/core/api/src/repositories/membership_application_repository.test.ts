import { MailVerificationFactory } from '@core-test/factories/mail_verification_factory';
import { MembershipApplicationComplianceDocumentGroupReviewLogFactory } from '@core-test/factories/membership_application_compliance_document_group_review_log_factory';
import { MembershipApplicationComplianceReviewLogFactory } from '@core-test/factories/membership_application_compliance_review_log_factory';
import { MembershipApplicationDocumentGroupReviewLogFactory } from '@core-test/factories/membership_application_document_group_review_log_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { MembershipApplicationReviewLogFactory } from '@core-test/factories/membership_application_review_log_factory';
import { ReviewType as ReviewTypePrisma } from '@hami/prisma';
import {
  findMembershipApplicationById,
  listMembershipApplications,
  MembershipApplicationNotFoundError,
  createInitialDocumentGroup,
  createNewDocumentGroupForRemand,
  IdentityDocumentFileNotFoundError,
  listApprovedMembershipApplicationsWithMembers,
} from './membership_application_repository';

describe('membership_application_repository', () => {
  describe('findMembershipApplicationById', () => {
    it('IDで入会申し込みを取得できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerification.mailVerificationId },
        },
      });

      // ===== Act =====
      const result = await findMembershipApplicationById({
        membershipApplicationId: application.membershipApplicationId,
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const foundApplication = result.value;
        expect(foundApplication.membershipApplicationId).toBe(application.membershipApplicationId);
        expect(foundApplication.firstName).toBe(application.firstName);
        expect(foundApplication.lastName).toBe(application.lastName);
        expect(foundApplication.mailVerification.email).toBe(mailVerification.email);
      }
    });

    it('存在しないIDの場合はエラーを返す', async () => {
      // ===== Act =====
      const result = await findMembershipApplicationById({
        membershipApplicationId: 99999,
      });

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(MembershipApplicationNotFoundError);
      }
    });
  });

  describe('listMembershipApplications', () => {
    it('入会申し込み一覧を取得できる', async () => {
      // ===== Arrange =====
      const mailVerification1 = await MailVerificationFactory.create();
      const mailVerification2 = await MailVerificationFactory.create();
      const application1 = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerification1.mailVerificationId },
        },
      });
      const application2 = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerification2.mailVerificationId },
        },
      });

      // ===== Act =====
      const result = await listMembershipApplications();

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { applications, hasNextPage } = result.value;
        expect(applications.length).toBeGreaterThanOrEqual(2);
        expect(hasNextPage).toBe(false);

        const foundIds = applications.map((app) => app.membershipApplicationId);
        expect(foundIds).toContain(application1.membershipApplicationId);
        expect(foundIds).toContain(application2.membershipApplicationId);
      }
    });

    it('ページネーション付きで一覧を取得できる', async () => {
      // ===== Arrange =====
      const applications = [];
      for (let i = 0; i < 5; i++) {
        const mailVerification = await MailVerificationFactory.create();
        const application = await MembershipApplicationFactory.create({
          mailVerification: {
            connect: { mailVerificationId: mailVerification.mailVerificationId },
          },
        });
        applications.push(application);
      }

      // ===== Act =====
      const result = await listMembershipApplications({
        pagination: { page: 1, pageSize: 3 },
      });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { applications: resultApps, hasNextPage } = result.value;
        expect(resultApps.length).toBe(3);
        expect(hasNextPage).toBe(true);
      }
    });

    it('statusFilter=ACTION_REQUIREDで未審査のみ取得できる', async () => {
      // ===== Arrange =====
      const mailVerificationPending = await MailVerificationFactory.create();
      const mailVerificationApproved = await MailVerificationFactory.create();

      const pendingApplication = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerificationPending.mailVerificationId },
        },
      });
      const approvedApplication = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerificationApproved.mailVerificationId },
        },
      });

      await MembershipApplicationReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApplication.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });
      // 書類側も承認済みにして「要対応」から除外されるようにする
      await MembershipApplicationDocumentGroupReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApplication.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      // ===== Act =====
      const result = await listMembershipApplications({ statusFilter: 'ACTION_REQUIRED' });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { applications } = result.value;
        const ids = applications.map((app) => app.membershipApplicationId);
        expect(ids).toContain(pendingApplication.membershipApplicationId);
        expect(ids).not.toContain(approvedApplication.membershipApplicationId);
      }
    });

    it('statusFilter=APPROVEDで全審査（申込/書類/コンプライアンス申込/コンプライアンス書類）承認済みのみ取得できる', async () => {
      // ===== Arrange =====
      const mailVerificationApproved = await MailVerificationFactory.create();
      const mailVerificationPending = await MailVerificationFactory.create();

      const approvedApplication = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerificationApproved.mailVerificationId },
        },
      });
      const pendingApplication = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerificationPending.mailVerificationId },
        },
      });

      await MembershipApplicationReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApplication.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });
      await MembershipApplicationDocumentGroupReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApplication.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });
      await MembershipApplicationComplianceReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApplication.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });
      await MembershipApplicationComplianceDocumentGroupReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApplication.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      // ===== Act =====
      const result = await listMembershipApplications({ statusFilter: 'APPROVED' });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { applications } = result.value;
        const ids = applications.map((app) => app.membershipApplicationId);
        expect(ids).toContain(approvedApplication.membershipApplicationId);
        expect(ids).not.toContain(pendingApplication.membershipApplicationId);
      }
    });

    it('statusFilter=SENTで印刷済みのみ取得できる', async () => {
      // ===== Arrange =====
      const mailVerificationSent = await MailVerificationFactory.create();
      const mailVerificationOther = await MailVerificationFactory.create();

      const sentApplication = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerificationSent.mailVerificationId },
        },
        isPrinted: true,
      });
      await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerificationOther.mailVerificationId },
        },
        isPrinted: false,
      });

      // ===== Act =====
      const result = await listMembershipApplications({ statusFilter: 'SENT' });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { applications } = result.value;
        expect(applications).toHaveLength(1);
        expect(applications[0]?.membershipApplicationId).toBe(sentApplication.membershipApplicationId);
        expect(applications[0]?.isPrinted).toBe(true);
      }
    });

    it('statusFilter=COMPLIANCE_PENDINGでコンプラ審査待ちのみ取得できる', async () => {
      // ===== Arrange =====
      const mailVerificationA = await MailVerificationFactory.create();
      const mailVerificationB = await MailVerificationFactory.create();
      const mailVerificationC = await MailVerificationFactory.create();

      // 対象: 一次(申込/書類)は承認、コンプラいずれか未承認
      const targetApp = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerificationA.mailVerificationId } },
      });
      await MembershipApplicationReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: targetApp.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });
      await MembershipApplicationDocumentGroupReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: targetApp.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });
      // コンプラ側は承認ログを作らない

      // 完全承認済み: 除外されるべき
      const approvedApp = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerificationB.mailVerificationId } },
      });
      await MembershipApplicationReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });
      await MembershipApplicationDocumentGroupReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });
      await MembershipApplicationComplianceReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });
      await MembershipApplicationComplianceDocumentGroupReviewLogFactory.create({
        membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      // 一次未承認: 除外されるべき
      await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerificationC.mailVerificationId } },
      });

      // ===== Act =====
      const result = await listMembershipApplications({ statusFilter: 'COMPLIANCE_PENDING' });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const { applications } = result.value;
        const ids = applications.map((a) => a.membershipApplicationId);
        expect(ids).toContain(targetApp.membershipApplicationId);
        expect(ids).not.toContain(approvedApp.membershipApplicationId);
      }
    });
  });

  describe('createInitialDocumentGroup', () => {
    it('初期document groupを作成できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerification.mailVerificationId },
        },
      });

      // ===== Act =====
      const result = await createInitialDocumentGroup(application.membershipApplicationId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const documentGroup = result.value;
        expect(documentGroup.membershipApplicationId).toBe(application.membershipApplicationId);
        expect(documentGroup.groupKey).toBe('identity_verification');
        expect(documentGroup.isCompleted).toBe(false);
        expect(documentGroup.uploadToken).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
      }
    });
  });

  describe('createNewDocumentGroupForRemand', () => {
    it('差し戻し用の新しいdocument groupを作成できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerification.mailVerificationId },
        },
      });

      // ===== Act =====
      const result = await createNewDocumentGroupForRemand(application.membershipApplicationId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const documentGroup = result.value;
        expect(documentGroup.membershipApplicationId).toBe(application.membershipApplicationId);
        expect(documentGroup.groupKey).toBe('identity_verification');
        expect(documentGroup.isCompleted).toBe(false);
        expect(documentGroup.uploadToken).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
      }
    });
  });

  describe('IdentityDocumentFileNotFoundError', () => {
    it('ファイルキーを含む適切なエラーメッセージを生成する', () => {
      // ===== Arrange =====
      const fileKey = 'identity/test-file-123.jpg';

      // ===== Act =====
      const error = new IdentityDocumentFileNotFoundError(fileKey);

      // ===== Assert =====
      expect(error.name).toBe('IdentityDocumentFileNotFoundError');
      expect(error.message).toBe(`身分証明書ファイルが見つかりません: ${fileKey}`);
      expect(error.message).toContain(fileKey);
      expect(error instanceof Error).toBe(true);
    });

    it('空のファイルキーでも適切に動作する', () => {
      // ===== Arrange =====
      const fileKey = '';

      // ===== Act =====
      const error = new IdentityDocumentFileNotFoundError(fileKey);

      // ===== Assert =====
      expect(error.name).toBe('IdentityDocumentFileNotFoundError');
      expect(error.message).toBe('身分証明書ファイルが見つかりません: ');
      expect(error instanceof Error).toBe(true);
    });

    it('特殊文字を含むファイルキーでも適切に動作する', () => {
      // ===== Arrange =====
      const fileKey = 'identity/特殊文字-テスト_123.jpg';

      // ===== Act =====
      const error = new IdentityDocumentFileNotFoundError(fileKey);

      // ===== Assert =====
      expect(error.name).toBe('IdentityDocumentFileNotFoundError');
      expect(error.message).toBe(`身分証明書ファイルが見つかりません: ${fileKey}`);
      expect(error.message).toContain(fileKey);
      expect(error instanceof Error).toBe(true);
    });

    it('エラーがスタックトレースを持つ', () => {
      // ===== Arrange =====
      const fileKey = 'identity/test-file.jpg';

      // ===== Act =====
      const error = new IdentityDocumentFileNotFoundError(fileKey);

      // ===== Assert =====
      expect(error.stack).toBeDefined();
      expect(typeof error.stack).toBe('string');
    });
  });

  describe('listApprovedMembershipApplicationsWithMembers', () => {
    it('承認済み申し込みと会員データをジョインして取得できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerification.mailVerificationId },
        },
      });

      // 承認ログを作成
      await MembershipApplicationReviewLogFactory.create({
        membershipApplication: {
          connect: { membershipApplicationId: application.membershipApplicationId },
        },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      await MembershipApplicationDocumentGroupReviewLogFactory.create({
        membershipApplication: {
          connect: { membershipApplicationId: application.membershipApplicationId },
        },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      await MembershipApplicationComplianceReviewLogFactory.create({
        membershipApplication: {
          connect: { membershipApplicationId: application.membershipApplicationId },
        },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      await MembershipApplicationComplianceDocumentGroupReviewLogFactory.create({
        membershipApplication: {
          connect: { membershipApplicationId: application.membershipApplicationId },
        },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      // ===== Act =====
      const result = await listApprovedMembershipApplicationsWithMembers({ includePrinted: false });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const applications = result.value;
        expect(applications.length).toBeGreaterThan(0);
        
        const foundApplication = applications.find(app => app.membershipApplicationId === application.membershipApplicationId);
        expect(foundApplication).toBeDefined();
        expect(foundApplication?.mailVerification.email).toBe(mailVerification.email);
        expect(foundApplication?.member).toBeNull(); // 会員データはまだ作成されていない
      }
    });

    it('送付済みを含む場合は送付済みも取得できる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerification.mailVerificationId },
        },
        isPrinted: true, // 送付済み
      });

      // 承認ログを作成
      await MembershipApplicationReviewLogFactory.create({
        membershipApplication: {
          connect: { membershipApplicationId: application.membershipApplicationId },
        },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      await MembershipApplicationDocumentGroupReviewLogFactory.create({
        membershipApplication: {
          connect: { membershipApplicationId: application.membershipApplicationId },
        },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      await MembershipApplicationComplianceReviewLogFactory.create({
        membershipApplication: {
          connect: { membershipApplicationId: application.membershipApplicationId },
        },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      await MembershipApplicationComplianceDocumentGroupReviewLogFactory.create({
        membershipApplication: {
          connect: { membershipApplicationId: application.membershipApplicationId },
        },
        reviewType: ReviewTypePrisma.APPROVE,
      });

      // ===== Act =====
      const result = await listApprovedMembershipApplicationsWithMembers({ includePrinted: true });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const applications = result.value;
        const foundApplication = applications.find(app => app.membershipApplicationId === application.membershipApplicationId);
        expect(foundApplication).toBeDefined();
        expect(foundApplication?.isPrinted).toBe(true);
      }
    });

    it('承認されていない申し込みは取得されない', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerification.mailVerificationId },
        },
      });

      // 承認ログを作成しない（未承認状態）

      // ===== Act =====
      const result = await listApprovedMembershipApplicationsWithMembers({ includePrinted: false });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const applications = result.value;
        const foundApplication = applications.find(app => app.membershipApplicationId === application.membershipApplicationId);
        expect(foundApplication).toBeUndefined();
      }
    });
  });
});
