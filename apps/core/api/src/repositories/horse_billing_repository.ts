import { ResultAsync } from 'neverthrow';
import { HorseBillingItemType, Prisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class HorseBillingNotFoundError extends Error {
  readonly name = 'HorseBillingNotFoundError';
}

export class HorseNotFoundError extends Error {
  readonly name = 'HorseNotFoundError';
}

export class BillerNotFoundError extends Error {
  readonly name = 'BillerNotFoundError';
}

export class HorseBillingClosedError extends Error {
  readonly name = 'HorseBillingClosedError';
}

export interface ListHorseBillingsParams {
  horseId: number;
  billingYearMonth?: number;
  itemType?: HorseBillingItemType;
  billerId?: number;
  closing?: boolean;
  page?: number;
  pageSize?: number;
}

export interface ListHorseBillingsResult {
  billings: Array<{
    id: number;
    horseId: number;
    billingYearMonth: number;
    occurredYear: number;
    occurredMonth: number;
    occurredDay: number;
    billerId: number;
    itemType: HorseBillingItemType;
    itemTypeOther: string;
    billingAmount: number;
    taxAmount: number;
    subsidyAmount: number;
    totalAmount: number;
    note: string;
    closing: boolean;
  }>;
  totalCount: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * 馬の支出一覧取得
 */
export function listHorseBillings(params: ListHorseBillingsParams): ResultAsync<ListHorseBillingsResult, DatabaseError> {
  return ResultAsync.fromPromise(
    (async () => {
      const { horseId, billingYearMonth, itemType, billerId, closing, page, pageSize } = params;

      const actualPage = page ?? 1;
      const actualPageSize = pageSize ?? undefined;
      const skip = actualPageSize ? (actualPage - 1) * actualPageSize : 0;

      // 検索条件の構築
      const whereConditions: Prisma.HorseBillingWhereInput = {
        horseId,
      };

      if (billingYearMonth !== undefined) {
        whereConditions.billingYearMonth = billingYearMonth;
      }

      if (itemType !== undefined) {
        whereConditions.itemType = itemType;
      }

      if (billerId !== undefined) {
        whereConditions.billerId = billerId;
      }

      if (closing !== undefined) {
        whereConditions.closing = closing;
      }

      // データ取得と件数取得を並行実行
      const [billings, totalCount] = await Promise.all([
        client.horseBilling.findMany({
          where: whereConditions,
          skip,
          take: actualPageSize,
          orderBy: [{ occurredYear: 'desc' }, { occurredMonth: 'desc' }, { occurredDay: 'desc' }, { id: 'desc' }],
        }),
        client.horseBilling.count({
          where: whereConditions,
        }),
      ]);

      return {
        billings: billings.map((billing) => ({
          id: billing.id,
          horseId: billing.horseId,
          billingYearMonth: billing.billingYearMonth,
          occurredYear: billing.occurredYear,
          occurredMonth: billing.occurredMonth,
          occurredDay: billing.occurredDay,
          billerId: billing.billerId,
          itemType: billing.itemType,
          itemTypeOther: billing.itemTypeOther,
          billingAmount: billing.billingAmount,
          taxAmount: billing.taxAmount,
          subsidyAmount: billing.subsidyAmount,
          totalAmount: billing.totalAmount,
          note: billing.note,
          closing: billing.closing,
        })),
        totalCount,
        page: actualPage,
        limit: actualPageSize ?? totalCount, // pageSizeがnull/undefinedの場合は全件数
        totalPages: actualPageSize ? Math.ceil(totalCount / actualPageSize) : 1, // pageSizeがnull/undefinedの場合は1ページ
      };
    })(),
    (error) => new DatabaseError(`Failed to list horse billings: ${error}`)
  );
}

/**
 * 全馬の支出一覧取得（日付範囲指定）
 */
export function listAllHorseBillings(params: { 
  startYear: number; 
  startMonth: number; 
  startDay: number; 
  endYear: number; 
  endMonth: number; 
  endDay: number; 
}): ResultAsync<{
  billings: Array<{
    id: number;
    horseId: number;
    horseName: string;
    billingYearMonth: number;
    occurredYear: number;
    occurredMonth: number;
    occurredDay: number;
    billerId: number;
    itemType: HorseBillingItemType;
    itemTypeOther: string;
    billingAmount: number;
    taxAmount: number;
    subsidyAmount: number;
    totalAmount: number;
    note: string;
    billerName: string;
    closing: boolean;
  }>;
}, DatabaseError> {
  return ResultAsync.fromPromise(
    (async () => {
      const { startYear, startMonth, startDay, endYear, endMonth, endDay } = params;

      const billings = await client.horseBilling.findMany({
        where: {
          AND: [
            // 開始日以降の条件
            {
              OR: [
                { occurredYear: { gt: startYear } },
                { 
                  occurredYear: startYear,
                  OR: [
                    { occurredMonth: { gt: startMonth } },
                    { occurredMonth: startMonth, occurredDay: { gte: startDay } }
                  ]
                }
              ]
            },
            // 終了日以前の条件
            {
              OR: [
                { occurredYear: { lt: endYear } },
                { 
                  occurredYear: endYear,
                  OR: [
                    { occurredMonth: { lt: endMonth } },
                    { occurredMonth: endMonth, occurredDay: { lte: endDay } }
                  ]
                }
              ]
            }
          ]
        },
        include: {
          horse: {
            select: {
              horseName: true,
            },
          },
          biller: {
            select: {
              name: true,
            },
          },
        },
        orderBy: [{ occurredYear: 'desc' }, { occurredMonth: 'desc' }, { occurredDay: 'desc' }, { id: 'desc' }],
      });

      return {
        billings: billings.map((billing) => ({
          id: billing.id,
          horseId: billing.horseId,
          horseName: billing.horse.horseName || '',
          billerName: billing.biller.name || '',
          billingYearMonth: billing.billingYearMonth,
          occurredYear: billing.occurredYear,
          occurredMonth: billing.occurredMonth,
          occurredDay: billing.occurredDay,
          billerId: billing.billerId,
          itemType: billing.itemType,
          itemTypeOther: billing.itemTypeOther,
          billingAmount: billing.billingAmount,
          taxAmount: billing.taxAmount,
          subsidyAmount: billing.subsidyAmount,
          totalAmount: billing.totalAmount,
          note: billing.note,
          closing: billing.closing,
        })),
      };
    })(),
    (error) => new DatabaseError(`Failed to list all horse billings: ${error}`)
  );
}

/**
 * 馬のbillingYearMonthの前までの税別の維持費の合計を取得する関数
 */
export function getBillingTotalBeforeTax(horseId: number, billingYearMonth: number): ResultAsync<{ totalBillingAmountBeforeTax: number }, DatabaseError> {
  return ResultAsync.fromPromise(
    (async () => {
      const billings = await client.horseBilling.findMany({
        where: {
          horseId,
          billingYearMonth: {
            lt: billingYearMonth,
          },
          itemType: HorseBillingItemType.ENTRUST,
        },
        select: {
          billingAmount: true,
          subsidyAmount: true,
          taxAmount: true,
        },
      });

      if (billings.length === 0) {
        return {
          totalBillingAmountBeforeTax: 0,
        };
      }

      const totalBillingAmount = billings.reduce((sum, billing) => sum + billing.billingAmount, 0);
      const totalSubsidyAmount = billings.reduce((sum, billing) => sum + billing.subsidyAmount, 0);
      const totalTaxAmount = billings.reduce((sum, billing) => sum + billing.taxAmount, 0);
      
      const totalBillingAmountBeforeTax = totalBillingAmount - totalSubsidyAmount - totalTaxAmount;
      
      return {
        totalBillingAmountBeforeTax,
      };
    })(),
    (error) => new DatabaseError(`Failed to get horse billing total before tax: ${error}`)
  );
}

/**
 * 馬の支出詳細取得
 */
export function getHorseBillingDetail(id: number): ResultAsync<
  {
    id: number;
    horseId: number;
    billingYearMonth: number;
    occurredYear: number;
    occurredMonth: number;
    occurredDay: number;
    billerId: number;
    itemType: HorseBillingItemType;
    itemTypeOther: string;
    billingAmount: number;
    taxAmount: number;
    subsidyAmount: number;
    totalAmount: number;
    note: string;
    closing: boolean;
  },
  HorseBillingNotFoundError | DatabaseError
> {
  return ResultAsync.fromPromise(
    (async () => {
      const billing = await client.horseBilling.findUnique({
        where: { id },
      });

      if (!billing) {
        throw new HorseBillingNotFoundError('Horse billing not found');
      }

      return {
        id: billing.id,
        horseId: billing.horseId,
        billingYearMonth: billing.billingYearMonth,
        occurredYear: billing.occurredYear,
        occurredMonth: billing.occurredMonth,
        occurredDay: billing.occurredDay,
        billerId: billing.billerId,
        itemType: billing.itemType,
        itemTypeOther: billing.itemTypeOther,
        billingAmount: billing.billingAmount,
        taxAmount: billing.taxAmount,
        subsidyAmount: billing.subsidyAmount,
        totalAmount: billing.totalAmount,
        note: billing.note,
        closing: billing.closing,
      };
    })(),
    (error) => {
      if (error instanceof HorseBillingNotFoundError) {
        return error;
      }
      return new DatabaseError(`Failed to get horse billing detail: ${error}`);
    }
  );
}

/**
 * 馬の支出作成
 */
export function createHorseBilling(data: {
  horseId: number;
  billingYearMonth: number;
  occurredYear: number;
  occurredMonth: number;
  occurredDay: number;
  billerId: number;
  itemType: HorseBillingItemType;
  itemTypeOther: string;
  billingAmount: number;
  taxAmount: number;
  subsidyAmount: number;
  totalAmount: number;
  note: string;
}): ResultAsync<
  {
    id: number;
    horseId: number;
    billingYearMonth: number;
    occurredYear: number;
    occurredMonth: number;
    occurredDay: number;
    billerId: number;
    itemType: HorseBillingItemType;
    itemTypeOther: string;
    billingAmount: number;
    taxAmount: number;
    subsidyAmount: number;
    totalAmount: number;
    note: string;
    closing: boolean;
  },
  HorseNotFoundError | BillerNotFoundError | DatabaseError
> {
  return ResultAsync.fromPromise(
    (async () => {
      // 馬の存在確認
      const horse = await client.horse.findUnique({
        where: { horseId: data.horseId },
      });

      if (!horse) {
        throw new HorseNotFoundError('Horse not found');
      }

      // 請求者の存在確認
      const biller = await client.biller.findUnique({
        where: { id: data.billerId },
      });

      if (!biller) {
        throw new BillerNotFoundError('Biller not found');
      }

      const billing = await client.horseBilling.create({
        data: {
          ...data,
          closing: false, // 作成時は常にfalse
        },
      });

      return {
        id: billing.id,
        horseId: billing.horseId,
        billingYearMonth: billing.billingYearMonth,
        occurredYear: billing.occurredYear,
        occurredMonth: billing.occurredMonth,
        occurredDay: billing.occurredDay,
        billerId: billing.billerId,
        itemType: billing.itemType,
        itemTypeOther: billing.itemTypeOther,
        billingAmount: billing.billingAmount,
        taxAmount: billing.taxAmount,
        subsidyAmount: billing.subsidyAmount,
        totalAmount: billing.totalAmount,
        note: billing.note,
        closing: billing.closing,
      };
    })(),
    (error) => {
      if (error instanceof HorseNotFoundError || error instanceof BillerNotFoundError) {
        return error;
      }
      return new DatabaseError(`Failed to create horse billing: ${error}`);
    }
  );
}

/**
 * 馬の支出更新
 * closingがtrueのものは更新できない
 */
export function updateHorseBilling(
  id: number,
  data: {
    billingYearMonth: number;
    occurredYear: number;
    occurredMonth: number;
    occurredDay: number;
    billerId: number;
    itemType: HorseBillingItemType;
    itemTypeOther: string;
    billingAmount: number;
    taxAmount: number;
    subsidyAmount: number;
    totalAmount: number;
    note: string;
  }
): ResultAsync<
  {
    id: number;
    horseId: number;
    billingYearMonth: number;
    occurredYear: number;
    occurredMonth: number;
    occurredDay: number;
    billerId: number;
    itemType: HorseBillingItemType;
    itemTypeOther: string;
    billingAmount: number;
    taxAmount: number;
    subsidyAmount: number;
    totalAmount: number;
    note: string;
    closing: boolean;
  },
  HorseBillingNotFoundError | HorseBillingClosedError | BillerNotFoundError | DatabaseError
> {
  return ResultAsync.fromPromise(
    (async () => {
      // 既存レコードの取得とclosingチェック
      const existingBilling = await client.horseBilling.findUnique({
        where: { id },
      });

      if (!existingBilling) {
        throw new HorseBillingNotFoundError('Horse billing not found');
      }

      if (existingBilling.closing) {
        throw new HorseBillingClosedError('Cannot update closed horse billing');
      }

      // 請求者の存在確認
      const biller = await client.biller.findUnique({
        where: { id: data.billerId },
      });

      if (!biller) {
        throw new BillerNotFoundError('Biller not found');
      }

      const billing = await client.horseBilling.update({
        where: { id },
        data,
      });

      return {
        id: billing.id,
        horseId: billing.horseId,
        billingYearMonth: billing.billingYearMonth,
        occurredYear: billing.occurredYear,
        occurredMonth: billing.occurredMonth,
        occurredDay: billing.occurredDay,
        billerId: billing.billerId,
        itemType: billing.itemType,
        itemTypeOther: billing.itemTypeOther,
        billingAmount: billing.billingAmount,
        taxAmount: billing.taxAmount,
        subsidyAmount: billing.subsidyAmount,
        totalAmount: billing.totalAmount,
        note: billing.note,
        closing: billing.closing,
      };
    })(),
    (error) => {
      if (error instanceof HorseBillingNotFoundError || error instanceof HorseBillingClosedError || error instanceof BillerNotFoundError) {
        return error;
      }
      return new DatabaseError(`Failed to update horse billing: ${error}`);
    }
  );
}

/**
 * 馬の支出削除
 * closingがtrueのものは削除できない
 */
export function deleteHorseBilling(id: number): ResultAsync<void, HorseBillingNotFoundError | HorseBillingClosedError | DatabaseError> {
  return ResultAsync.fromPromise(
    (async () => {
      // 既存レコードの取得とclosingチェック
      const existingBilling = await client.horseBilling.findUnique({
        where: { id },
      });

      if (!existingBilling) {
        throw new HorseBillingNotFoundError('Horse billing not found');
      }

      if (existingBilling.closing) {
        throw new HorseBillingClosedError('Cannot delete closed horse billing');
      }

      await client.horseBilling.delete({
        where: { id },
      });
    })(),
    (error) => {
      if (error instanceof HorseBillingNotFoundError || error instanceof HorseBillingClosedError) {
        return error;
      }
      return new DatabaseError(`Failed to delete horse billing: ${error}`);
    }
  );
}
