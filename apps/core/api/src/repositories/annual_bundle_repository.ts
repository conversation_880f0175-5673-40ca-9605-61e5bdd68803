import { err, errAsync, ok, ResultAsync } from 'neverthrow';
import { Prisma, AnnualBundlePublishStatus as PrismaAnnualBundlePublishStatus, AnnualBundleRecruitmentStatus as PrismaAnnualBundleRecruitmentStatus } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class AnnualBundleNotFoundError extends Error {
  readonly name = 'AnnualBundleNotFoundError';
}

export const listAnnualBundles = ({ page, limit, fiscalYear }: { page: number; limit: number; fiscalYear?: number }) => {
  const skip = (page - 1) * limit;

  const where: Prisma.AnnualBundleWhereInput = {};
  if (typeof fiscalYear === 'number') {
    where.fiscalYear = fiscalYear;
  }

  return ResultAsync.fromPromise(
    Promise.all([
      client.annualBundle.findMany({
        where,
        skip,
        take: limit,
        orderBy: { annualBundleId: 'desc' },
      }),
      client.annualBundle.count({ where }),
    ]),
    () => new DatabaseError('Failed to list annual bundles')
  ).map(([bundles, totalCount]) => ({
    bundles,
    totalCount,
    totalPages: Math.ceil(totalCount / limit),
  }));
};

export const findAnnualBundleById = ({ annualBundleId }: { annualBundleId: number }) => {
  return ResultAsync.fromPromise(
    client.annualBundle.findUnique({
      where: { annualBundleId },
      include: {
        horses: {
          select: {
            horseId: true,
            horse: { select: { horseName: true, recruitmentName: true, recruitmentNo: true, sharesTotal: true } },
          },
          orderBy: { horse: { recruitmentNo: 'asc' } },
        },
      },
    }),
    () => new DatabaseError('Failed to find annual bundle')
  ).andThen((bundle) => {
    if (!bundle) return err(new AnnualBundleNotFoundError());
    return ok(bundle);
  });
};

export const createAnnualBundle = ({
  fiscalYear,
  name,
  shares,
  publishStatus,
  recruitmentStatus,
  horseIds,
}: {
  fiscalYear: number;
  name: string;
  shares: number;
  publishStatus: PrismaAnnualBundlePublishStatus;
  recruitmentStatus: PrismaAnnualBundleRecruitmentStatus;
  horseIds: number[];
}) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      const created = await tx.annualBundle.create({
        data: {
          fiscalYear,
          name,
          shares,
          publishStatus,
          recruitmentStatus,
          horses: horseIds.length
            ? {
                create: horseIds.map((horseId) => ({ horse: { connect: { horseId } } })),
              }
            : undefined,
        },
      });
      return created;
    }),
    () => new DatabaseError('Failed to create annual bundle')
  );
};

export const listAnnualBundleHorsesCandidates = ({
  annualBundleId,
  page,
  limit,
  search,
}: {
  annualBundleId: number;
  page: number;
  limit: number;
  search?: string;
}) => {
  const skip = (page - 1) * limit;
  return ResultAsync.fromPromise(
    client.annualBundle.findUnique({ where: { annualBundleId }, select: { fiscalYear: true } }),
    () => new DatabaseError('Failed to resolve bundle fiscal year')
  ).andThen((bundle) => {
    if (!bundle) return errAsync(new AnnualBundleNotFoundError());
    const where: Prisma.HorseWhereInput = { recruitmentYear: bundle.fiscalYear, deletedAt: null };
    if (search) {
      where.OR = [
        { horseName: { contains: search, mode: 'insensitive' } },
        { recruitmentName: { contains: search, mode: 'insensitive' } },
      ];
    }
    return ResultAsync.fromPromise(
      Promise.all([
        client.horse.findMany({ where, select: { horseId: true, horseName: true, recruitmentName: true, recruitmentYear: true, recruitmentNo: true, sharesTotal: true }, skip, take: limit, orderBy: { horseId: 'desc' } }),
        client.horse.count({ where }),
        client.annualBundleHorse.findMany({ where: { annualBundleId }, select: { horseId: true } }),
      ]),
      () => new DatabaseError('Failed to list horse candidates')
    ).map(([horses, totalCount, selected]) => {
      const selectedSet = new Set(selected.map((s) => s.horseId));
      return { horses, totalCount, totalPages: Math.ceil(totalCount / limit), selectedSet };
    });
  });
};

export const updateAnnualBundle = ({
  annualBundleId,
  name,
  shares,
  publishStatus,
  recruitmentStatus,
  horseIds,
}: {
  annualBundleId: number;
  name?: string;
  shares?: number;
  publishStatus?: PrismaAnnualBundlePublishStatus | null;
  recruitmentStatus?: PrismaAnnualBundleRecruitmentStatus | null;
  horseIds?: number[]; // 全置換
}) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 既存取得
      const existing = await tx.annualBundle.findUnique({
        where: { annualBundleId },
        include: { horses: { select: { horseId: true } } },
      });
      if (!existing) throw new AnnualBundleNotFoundError();

      // メイン更新
      await tx.annualBundle.update({
        where: { annualBundleId },
        data: {
          name,
          shares,
          publishStatus: publishStatus ?? undefined,
          recruitmentStatus: recruitmentStatus ?? undefined,
        },
      });

      if (horseIds) {
        const currentIds = new Set(existing.horses.map((h) => h.horseId));
        const nextIds = new Set(horseIds);

        const toAdd: number[] = [];
        const toRemove: number[] = [];

        nextIds.forEach((id) => {
          if (!currentIds.has(id)) toAdd.push(id);
        });
        currentIds.forEach((id) => {
          if (!nextIds.has(id)) toRemove.push(id);
        });

        if (toRemove.length) {
          await tx.annualBundleHorse.deleteMany({ where: { annualBundleId, horseId: { in: toRemove } } });
        }
        if (toAdd.length) {
          await tx.annualBundleHorse.createMany({
            data: toAdd.map((horseId) => ({ annualBundleId, horseId })),
            skipDuplicates: true,
          });
        }
      }

      return tx.annualBundle.findUnique({ where: { annualBundleId }, include: { horses: { select: { horseId: true } } } });
    }),
    (error) => (error instanceof AnnualBundleNotFoundError ? error : new DatabaseError('Failed to update annual bundle'))
  );
};


