import { ResultAsync } from 'neverthrow';
import type { BankAccountRegistrationStatus, Prisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class BankAccountRegistrationNotFoundError extends Error {
  readonly name = 'BankAccountRegistrationNotFoundError';
}

/**
 * 管理者向け口座登録一覧取得（ページネーション・フィルタリング対応）
 */
export const listBankAccountRegistrationsForAdmin = ({
  page = 1,
  limit = 20,
  statusFilter = [],
  searchQuery,
  sortBy = 'created_at',
  sortOrder = 'desc',
  dateFrom,
  dateTo,
}: {
  page?: number;
  limit?: number;
  statusFilter?: BankAccountRegistrationStatus[];
  searchQuery?: string;
  sortBy?: 'created_at' | 'updated_at' | 'member_name';
  sortOrder?: 'asc' | 'desc';
  dateFrom?: Date;
  dateTo?: Date;
}) => {
  const offset = (page - 1) * limit;

  // WHERE条件の構築
  const whereConditions: Prisma.BankAccountRegistrationWhereInput = {};

  // ステータスフィルター
  if (statusFilter.length > 0) {
    whereConditions.registrationStatus = { in: statusFilter };
  }

  // 日付範囲フィルター
  if (dateFrom || dateTo) {
    whereConditions.createdAt = {};
    if (dateFrom) {
      whereConditions.createdAt.gte = dateFrom;
    }
    if (dateTo) {
      whereConditions.createdAt.lte = dateTo;
    }
  }

  // 検索クエリ（会員名・会員番号）
  if (searchQuery) {
    const memberNumberQuery = parseInt(searchQuery);
    const orConditions: Prisma.BankAccountRegistrationWhereInput[] = [
      {
        member: {
          OR: [
            { firstName: { contains: searchQuery, mode: 'insensitive' } },
            { lastName: { contains: searchQuery, mode: 'insensitive' } },
            { firstNameKana: { contains: searchQuery, mode: 'insensitive' } },
            { lastNameKana: { contains: searchQuery, mode: 'insensitive' } },
          ],
        },
      },
    ];

    // 数値として解析できる場合のみ会員番号検索を追加
    if (!isNaN(memberNumberQuery)) {
      orConditions.push({
        member: {
          memberNumber: { equals: memberNumberQuery },
        },
      });
    }

    whereConditions.OR = orConditions;
  }

  // ソート条件の構築
  let orderBy: Prisma.BankAccountRegistrationOrderByWithRelationInput;
  switch (sortBy) {
    case 'member_name':
      orderBy = { member: { lastName: sortOrder } };
      break;
    case 'updated_at':
      orderBy = { updatedAt: sortOrder };
      break;
    default:
      orderBy = { createdAt: sortOrder };
  }

  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 総件数を取得
      const totalCount = await tx.bankAccountRegistration.count({
        where: whereConditions,
      });

      // データを取得
      const registrations = await tx.bankAccountRegistration.findMany({
        where: whereConditions,
        include: {
          member: {
            select: {
              memberId: true,
              memberNumber: true,
              firstName: true,
              lastName: true,
              firstNameKana: true,
              lastNameKana: true,
              user: {
                select: {
                  email: true,
                },
              },
            },
          },
        },
        orderBy,
        skip: offset,
        take: limit,
      });

      return {
        registrations,
        totalCount,
        currentPage: page,
        totalPages: Math.ceil(totalCount / limit),
      };
    }),
    () => new DatabaseError('Failed to list bank account registrations for admin')
  );
};

/**
 * 会員別口座登録詳細取得（管理者向け）
 */
export const getBankAccountRegistrationsByMemberId = ({ memberId }: { memberId: number }) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 会員情報を取得
      const member = await tx.member.findUnique({
        where: { memberId },
        select: {
          memberId: true,
          memberNumber: true,
          firstName: true,
          lastName: true,
          firstNameKana: true,
          lastNameKana: true,
          user: {
            select: {
              email: true,
            },
          },
        },
      });

      if (!member) {
        throw new BankAccountRegistrationNotFoundError('Member not found');
      }

      // 口座登録履歴を取得（ログ付き）
      const registrations = await tx.bankAccountRegistration.findMany({
        where: { memberId },
        orderBy: { createdAt: 'desc' },
      });

      // 有効な口座登録を特定
      const activeRegistration = registrations.find((r) => r.isActive) || null;

      return {
        member: {
          memberId: member.memberId,
          memberName: `${member.lastName} ${member.firstName}`,
          memberNumber: member.memberNumber,
          email: member.user?.email || '',
        },
        registrations,
        activeRegistration,
      };
    }),
    (error) => {
      // BankAccountRegistrationNotFoundErrorはそのまま再スロー
      if (error instanceof BankAccountRegistrationNotFoundError) {
        return error;
      }
      // その他のエラーはDatabaseErrorにマップ
      return new DatabaseError('Failed to get bank account registrations by member ID');
    }
  );
};
