import { err, ok, ResultAsync } from 'neverthrow';
import { Prisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { DatabaseError } from './index';

export class MembershipApplicationReviewLogNotFoundError extends Error {
  readonly name = 'MembershipApplicationReviewLogNotFoundError';
}

/**
 * membershipApplicationIdでMembershipApplicationReviewLogレコードを検索
 * @param membershipApplicationId 検索するmembershipApplicationId
 * @returns 見つかった場合はMembershipApplicationReviewLogレコード、見つからない場合やDBエラー時はResultAsyncでエラー
 */
export const findMembershipApplicationReviewLogByMembershipApplicationId = ({
  membershipApplicationId,
}: {
  membershipApplicationId: number;
}) => {
  return ResultAsync.fromPromise(
    client.membershipApplicationReviewLog.findFirst({
      where: {
        membershipApplication: {
          membershipApplicationId,
        },
      },
      include: {
        membershipApplication: true,
      },
    }),
    () => new DatabaseError('Failed to query membership application review log')
  ).andThen((record) => {
    if (!record) {
      return err(new MembershipApplicationReviewLogNotFoundError());
    }
    return ok(record);
  });
};

/**
 * MembershipApplicationReviewLogレコードを作成
 * @param data 作成するデータ (membershipApplicationIdを含む)
 * @returns 作成されたMembershipApplicationReviewLog
 */
export const createMembershipApplicationReviewLog = (
  data: Omit<Prisma.MembershipApplicationReviewLogCreateInput, 'membershipApplication' | 'timestamp'> & {
    membershipApplicationId: number;
  }
) => {
  const { membershipApplicationId, ...rest } = data;
  return ResultAsync.fromPromise(
    client.membershipApplicationReviewLog.create({
      data: {
        ...rest,
        timestamp: new Date(),
        membershipApplication: { connect: { membershipApplicationId } },
      },
    }),
    () => new DatabaseError('Failed to create membership application review log')
  );
};
