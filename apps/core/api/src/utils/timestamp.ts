import { create } from '@bufbuild/protobuf';
import { TimestampSchema, type Timestamp } from '@bufbuild/protobuf/wkt';

/**
 * JavaScript Date オブジェクトを protobuf Timestamp に変換する
 */
export function dateToTimestamp(date: Date): Timestamp {
  return create(TimestampSchema, {
    seconds: BigInt(Math.floor(date.getTime() / 1000)),
    nanos: (date.getTime() % 1000) * 1000000,
  });
}

/**
 * JavaScript Date オブジェクト（nullable）を protobuf Timestamp（optional）に変換する
 */
export function dateToTimestampOptional(date: Date | null): Timestamp | undefined {
  return date ? dateToTimestamp(date) : undefined;
}
