import { Result, ResultAsync } from 'neverthrow';

async function performUnwrap<T, E>(r: Result<T, E> | ResultAsync<T, E>): Promise<T> {
  const unwrapLogic = (res: Result<T, E>): T => {
    if (res.isOk()) return res.value;
    throw res.error;
  };

  if (r instanceof ResultAsync) {
    const result = await r;
    return unwrapLogic(result);
  } else {
    return unwrapLogic(r);
  }
}

/**
 * Result/ResultAsyncを返すハンドラ関数を受け取り、
 * その結果をアンラップする新しい非同期関数を返す高階関数。
 * 成功時は値を返し、失敗時はエラーをスローする。
 * connectRPCのサービス実装などで利用し、ボイラープレートを削減する。
 * @param handler ResultまたはResultAsyncを返す関数
 * @returns リクエストを受け取り、アンラップされた結果を返す非同期関数
 */
export function unwrapResult<Req, Res, Err, Ctx = unknown>(
  handler: (_req: Req, _ctx: Ctx) => Result<Res, Err> | ResultAsync<Res, Err>
): (_req: Req, _ctx: Ctx) => Promise<Res> {
  return async (req: Req, ctx: Ctx): Promise<Res> => {
    const result = handler(req, ctx);
    return performUnwrap(result);
  };
}
