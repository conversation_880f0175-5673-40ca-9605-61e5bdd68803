import {
  S3Client,
  PutObjectCommand,
  HeadObjectCommand,
  GetObjectCommand,
  S3ClientConfig,
  NotFound,
  S3ServiceException,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { config } from '../config/environment.js';

// S3クライアントを遅延初期化する関数
function getS3Client(): S3Client {
  const clientConfig: S3ClientConfig = {
    region: config.s3.region,
    endpoint: config.s3.endpoint,
    forcePathStyle: !!config.s3.endpoint, // MinIOの場合のみpath-styleを使用
  };

  // AWS認証情報が環境変数で提供されている場合のみ設定
  // ECS環境ではIAMタスクロールを使用するため、認証情報を明示的に設定しない
  if (config.s3.accessKeyId && config.s3.secretAccessKey) {
    clientConfig.credentials = {
      accessKeyId: config.s3.accessKeyId,
      secretAccessKey: config.s3.secretAccessKey,
    };
  }

  return new S3Client(clientConfig);
}

// フロントエンド用S3クライアント（プリサインドURL生成用）を遅延初期化する関数
function getS3ClientForPresignedUrl(): S3Client {
  const clientConfig: S3ClientConfig = {
    region: config.s3.region,
    endpoint: config.s3.publicEndpoint || config.s3.endpoint,
    forcePathStyle: !!(config.s3.publicEndpoint || config.s3.endpoint), // MinIOの場合のみpath-styleを使用
  };

  // AWS認証情報が環境変数で提供されている場合のみ設定
  // ECS環境ではIAMタスクロールを使用するため、認証情報を明示的に設定しない
  if (config.s3.accessKeyId && config.s3.secretAccessKey) {
    clientConfig.credentials = {
      accessKeyId: config.s3.accessKeyId,
      secretAccessKey: config.s3.secretAccessKey,
    };
  }

  return new S3Client(clientConfig);
}

/**
 * S3 にファイルをアップロードします。
 * @param key S3 上のオブジェクトキー
 * @param body ファイルのバイナリデータ (Buffer)
 * @param contentType ファイルの Content-Type
 */
export async function uploadToS3(key: string, body: Buffer, contentType: string): Promise<void> {
  const Bucket = config.s3.bucketName;
  const s3 = getS3Client();
  await s3.send(
    new PutObjectCommand({
      Bucket,
      Key: key,
      Body: body,
      ContentType: contentType,
    })
  );
}

/**
 * アップロード後のファイル検証
 * @param key S3オブジェクトキー
 * @param expectedContentType 期待されるContent-Type
 * @param maxSizeBytes 最大ファイルサイズ（バイト）
 * @returns 検証結果
 */
export async function validateUploadedFile(
  key: string,
  expectedContentType: string,
  maxSizeBytes: number = 10 * 1024 * 1024 // 10MB
): Promise<{ isValid: boolean; error?: string }> {
  try {
    const Bucket = config.s3.bucketName;
    const s3 = getS3Client();
    const headCommand = new HeadObjectCommand({ Bucket, Key: key });
    const response = await s3.send(headCommand);

    // ファイルサイズチェック
    if (response.ContentLength && response.ContentLength > maxSizeBytes) {
      return { isValid: false, error: 'ファイルサイズが上限を超えています' };
    }

    // Content-Typeチェック（緩い検証）
    if (response.ContentType && !response.ContentType.startsWith(expectedContentType.split('/')[0])) {
      return { isValid: false, error: 'ファイル形式が正しくありません' };
    }

    return { isValid: true };
  } catch (error) {
    console.error('File validation error:', error);
    return { isValid: false, error: 'ファイルの検証に失敗しました' };
  }
}

/**
 * 指定されたキーでS3署名付きアップロードURLを発行
 * @param key S3オブジェクトキー
 * @param contentType Content-Type
 * @returns { url, key, expiresIn }
 */
export async function getPresignedUploadUrl(key: string, contentType: string) {
  const Bucket = config.s3.bucketName;
  const command = new PutObjectCommand({
    Bucket,
    Key: key,
    ContentType: contentType,
  });
  const expiresIn = 60 * 5; // 5分

  // フロントエンドからアクセス可能なURLを生成
  // AWS SDK v3の自動チェックサムのみを除外し、基本的なセキュリティは維持
  const s3ForPresignedUrl = getS3ClientForPresignedUrl();
  const url = await getSignedUrl(s3ForPresignedUrl, command, {
    expiresIn,
    // AWS SDK固有のチェックサムヘッダーのみを除外
    // Content-MD5やETAGによる整合性チェックは引き続き有効
    // cspell:disable-next-line
    unsignableHeaders: new Set(['x-amz-checksum-crc32', 'x-amz-sdk-checksum-algorithm']),
  });
  return { url, key, expiresIn };
}

/**
 * S3署名付きダウンロードURLを発行
 * @param key S3オブジェクトキー
 * @param expiresIn 有効期限（秒）デフォルト: 1時間
 * @returns 署名付きURL
 */
export async function getPresignedDownloadUrl(key: string, expiresIn: number = 60 * 60): Promise<string> {
  const Bucket = config.s3.bucketName;
  const command = new GetObjectCommand({
    Bucket,
    Key: key,
  });

  const s3ForPresignedUrl = getS3ClientForPresignedUrl();
  const url = await getSignedUrl(s3ForPresignedUrl, command, {
    expiresIn,
  });

  return url;
}

/**
 * ファイルキーが存在するかチェック
 * @param key S3オブジェクトキー
 * @returns ファイルが存在するかどうか
 * @throws 404以外のS3エラーは再スローされる
 */
export async function checkFileExists(key: string): Promise<boolean> {
  const Bucket = config.s3.bucketName;
  try {
    const s3 = getS3Client();
    const headCommand = new HeadObjectCommand({ Bucket, Key: key });
    // デバッグログ（必要に応じて下行をコメントアウト可）
    await s3.send(headCommand);
    return true;
  } catch (error) {
    // NotFoundエラーまたは404ステータスコードの場合のみfalseを返す
    if (error instanceof NotFound || (error instanceof S3ServiceException && error.$metadata?.httpStatusCode === 404)) {
      return false;
    }

    // その他のエラー（ネットワークエラー、アクセス権限エラーなど）は再スロー
    console.error('[S3] HEAD error (rethrow)', { Bucket, Key: key }, error);
    throw error;
  }
}
