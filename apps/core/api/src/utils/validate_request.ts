import { ok, err, Result } from 'neverthrow';
import { z } from 'zod';
import { ZodTypeAny } from 'zod';

export class ValidationError extends Error {
  readonly name = 'ValidationError';
}

export function validate<S extends ZodTypeAny>(req: unknown, schema: S): Result<z.infer<S>, ValidationError> {
  const result = schema.safeParse(req);
  if (!result.success) {
    const errorMessage = result.error.errors[0]?.message ?? '無効なリクエストです';
    return err(new ValidationError(errorMessage));
  }
  return ok(result.data);
}
