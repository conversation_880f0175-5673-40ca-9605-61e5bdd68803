import { environment } from '@core-api/utils/environment';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Minimal structured logger; can be swapped with pino later
export const log = (level: LogLevel, message: string, meta?: Record<string, unknown>) => {
  if (environment === 'test' && level !== 'error') return; // keep test logs quiet except errors
  const payload = meta ? { message, ...meta } : { message };
  switch (level) {
    case 'debug':
      console.debug(payload);
      break;
    case 'info':
      console.info(payload);
      break;
    case 'warn':
      console.warn(payload);
      break;
    case 'error':
      console.error(payload);
      break;
  }
};

