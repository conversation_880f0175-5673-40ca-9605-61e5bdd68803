/**
 * 出資の締め日を計算（翌々月10日）
 * @param contractDate 契約締結の日付
 * @returns 出資の締め日
 */
export function getInvestmentSealDate(contractDate: Date) {
  return new Date(contractDate.getFullYear(), contractDate.getMonth() + 2, 10);
}

/**
 * 出資金の最初の支払日（firstInvestmentSealDate）と、
 * 競走馬出資金相当額（racehorseInvestmentEquivalent）、
 * 割引充当額（discountAllocation）、
 * 競走馬出資金（racehorseInvestment）と分割回数から、
 * 何月何日にいくら支払うかを計算する
 * @param firstInvestmentSealDate 最初の出資の締め日
 * @param installmentCount 分割回数
 * @param racehorseInvestmentEquivalent 競走馬出資金相当額
 * @param discountAllocation 割引充当額
 * @param racehorseInvestment 競走馬出資金
 * @returns 支払い日と支払い額などの配列
 */
export function getInstallmentDatesAndInvestmentAmounts(
  firstInvestmentSealDate: Date,
  installmentCount: number,
  racehorseInvestmentEquivalent: number,
  discountAllocation: number,
  racehorseInvestment: number
) {
  const installmentDates = [];
  if (installmentCount === 1) {
    installmentDates.push({
      investmentDate: firstInvestmentSealDate,
      racehorseInvestmentEquivalent: racehorseInvestmentEquivalent,
      discountAllocation: discountAllocation,
      racehorseInvestment: racehorseInvestment - discountAllocation,
    });
  } else {
    // 先に競走馬出資金を分割する。
    // 競走馬出資金の総額が分割回数で割り切れない場合は、端数を切り捨てて分割する。余りは初回に加算して、分割回数で割り切れるようにする。
    // 割引充当額でも同様の計算を行う。
    const racehorseInvestmentPerInstallment = Math.floor(racehorseInvestment / installmentCount);
    const remainder = racehorseInvestment % installmentCount;
    const discountAllocationPerInstallment = Math.floor(discountAllocation / installmentCount);
    const remainderDiscountAllocation = discountAllocation % installmentCount;
    for (let i = 0; i < installmentCount; i++) {
      if (i === 0) {
        installmentDates.push({
          investmentDate: firstInvestmentSealDate,
          racehorseInvestmentEquivalent: racehorseInvestmentPerInstallment + remainder + discountAllocationPerInstallment + remainderDiscountAllocation,
          discountAllocation: discountAllocationPerInstallment + remainderDiscountAllocation,
          racehorseInvestment: racehorseInvestmentPerInstallment + remainder,
        });
      } else {
        installmentDates.push({
          investmentDate: new Date(firstInvestmentSealDate.getFullYear(), firstInvestmentSealDate.getMonth() + i, 10),
          racehorseInvestmentEquivalent: racehorseInvestmentPerInstallment + discountAllocationPerInstallment,
          discountAllocation: discountAllocationPerInstallment,
          racehorseInvestment: racehorseInvestmentPerInstallment,
        });
      }
    }
  }
  return installmentDates;
}

/**
 * 分割回数を計算
 * 馬が2歳の4月まで分割に応じることができる
 * 例えばfirstInvestmentSealDateが2歳の3月10日の場合、3月10日と4月10日の2回分割になる
 * 最大の分割回数に制限はない。
 * @param installmentPayment 分割払いフラグ
 * @param horseBirthYear 馬の誕生年
 * @param firstInvestmentSealDate 最初の出資の締め日
 * @returns 分割回数
 */
export function getInstallmentCount(installmentPayment: boolean, horseBirthYear: number, firstInvestmentSealDate: Date) {
  if (!installmentPayment) {
    return 1;
  }
  // firstInvestmentSealDateからhorseBirthYearの2年後の4月までに何回の支払可能日があるかを算出する
  const lastInvestmentDate = new Date(horseBirthYear + 2, 3, 10);
  const month = (lastInvestmentDate.getFullYear() - firstInvestmentSealDate.getFullYear()) * 12 + lastInvestmentDate.getMonth() - firstInvestmentSealDate.getMonth();
  if (month <= 0) {
    return 1;
  }
  return month + 1;
}
