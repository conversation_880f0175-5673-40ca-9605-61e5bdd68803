import crypto from 'crypto';
import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { errAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { AdminUserSchema, CreateAdminUserResponseSchema } from '@hami/core-admin-api-schema/admin_user_service_pb';
import { adminAccountCreationTemplate } from '@core-api/mail_templates/admin_account_creation';
import { UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories';
import { AdminUserAlreadyExistsError, findAdminUserBySessionToken, createAdminUser } from '@core-api/repositories/admin_user_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { MailSendError, sendMail } from '@core-api/utils/mail';
import { dateToTimestamp, dateToTimestampOptional } from '@core-api/utils/timestamp';
import { ValidationError } from '@core-api/utils/validate_request';

const schema = z.object({
  name: z.string().min(1, '名前は必須です'),
  email: z.string().email('有効なメールアドレスを入力してください'),
});

export const createAdminUserHandler = createHandler({
  schema,
  business: (req, ctx) => {
    const sessionToken = ctx.requestHeader.get('sessionToken');
    if (!sessionToken) return errAsync(new UnauthorizedError());
    const tempPassword = crypto.randomBytes(4).toString('hex');
    return findAdminUserBySessionToken({ sessionToken })
      .andThen(() => createAdminUser({ name: req.name, email: req.email, password: tempPassword }))
      .andThen((user) =>
        sendMail({
          to: req.email,
          template: adminAccountCreationTemplate,
          params: { name: req.name, email: req.email, tempPassword },
        }).map(() => user)
      );
  },
  toResponse: (user) =>
    create(CreateAdminUserResponseSchema, {
      adminUser: create(AdminUserSchema, {
        adminUserId: user.adminUserId,
        name: user.name,
        email: user.email,
        createdAt: dateToTimestamp(user.createdAt),
        updatedAt: dateToTimestamp(user.updatedAt),
        lastLoginAt: dateToTimestampOptional(user.adminUserSession?.updatedAt ?? null),
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), () => new ConnectError('Unauthorized', Code.Unauthenticated))
      .with(
        P.instanceOf(AdminUserAlreadyExistsError),
        () => new ConnectError('このメールアドレスは既に使用されています', Code.AlreadyExists)
      )
      .with(P.instanceOf(MailSendError), () => new ConnectError('Internal server error', Code.Internal))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
