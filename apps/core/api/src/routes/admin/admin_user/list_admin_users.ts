import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { errAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { AdminUserSchema, ListAdminUsersResponseSchema } from '@hami/core-admin-api-schema/admin_user_service_pb';
import type { Prisma } from '@hami/prisma';
import { UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories';
import { AdminUserNotFoundError, findAdminUserBySessionToken, listAdminUsers } from '@core-api/repositories/admin_user_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { dateToTimestamp, dateToTimestampOptional } from '@core-api/utils/timestamp';
import { ValidationError } from '@core-api/utils/validate_request';

export const listAdminUsersHandler = createHandler({
  schema: z.object({}),
  business: (_req, ctx) => {
    const sessionToken = ctx.requestHeader.get('sessionToken');
    if (!sessionToken) {
      return errAsync(new UnauthorizedError());
    }
    return findAdminUserBySessionToken({ sessionToken }).andThen(() => listAdminUsers());
  },
  toResponse: (users: Prisma.AdminUserGetPayload<{ include: { adminUserSession: true } }>[]) =>
    create(ListAdminUsersResponseSchema, {
      adminUsers: users.map((u) =>
        create(AdminUserSchema, {
          adminUserId: u.adminUserId,
          name: u.name,
          email: u.email,
          createdAt: dateToTimestamp(u.createdAt),
          updatedAt: dateToTimestamp(u.updatedAt),
          lastLoginAt: dateToTimestampOptional(u.adminUserSession?.updatedAt ?? null),
        })
      ),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), (e) => new ConnectError(e.message, Code.Unauthenticated))
      .with(P.instanceOf(AdminUserNotFoundError), () => new ConnectError('Unauthorized', Code.Unauthenticated))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
