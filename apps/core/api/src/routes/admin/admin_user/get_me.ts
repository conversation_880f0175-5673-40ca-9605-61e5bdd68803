import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetMeResponseSchema } from '@hami/core-admin-api-schema/admin_user_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { AdminUserNotFoundError, findAdminUserBySessionToken } from '@core-api/repositories/admin_user_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getMe = createHandler({
  schema: z.object({
    sessionToken: z.string().min(1),
  }),
  business: ({ sessionToken }) =>
    findAdminUserBySessionToken({ sessionToken }),
  toResponse: ({ user, session }) =>
    create(GetMeResponseSchema, {
      adminUserId: user.adminUserId,
      name: user.name,
      email: user.email,
      lastLoginAt: session.updatedAt
      ? {
          seconds: BigInt(Math.floor(session.updatedAt.getTime() / 1000)),
          nanos: (session.updatedAt.getTime() % 1000) * 1000000,
        }
      : undefined,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Database error', Code.Internal))
      .with(P.instanceOf(AdminUserNotFoundError), () => new ConnectError('Admin user not found', Code.Unauthenticated))
      .exhaustive(),
});
