import { Code } from '@connectrpc/connect';
import { AdminUserFactory } from '@core-test/factories/admin_user_factory';
import bcrypt from 'bcrypt';
import { AdminUserService } from '@hami/core-admin-api-schema/admin_user_service_pb';

describe('login', () => {
  it('正常にログインできる', async ({ getClient }) => {
    // ===== Arrange =====
    const password = 'StrongPassw0rd!';
    const salt = bcrypt.genSaltSync(10);
    const adminUser = await AdminUserFactory.create({ password_hash: bcrypt.hashSync(password, salt), salt });
    const client = getClient(AdminUserService);
    const request = { email: adminUser.email, password };

    // ===== Act =====
    const response = await client.login(request);

    // ===== Assert =====
    expect(response).toEqual(expect.objectContaining({ sessionToken: expect.any(String) }));
    const session = await vPrisma.client.adminUserSession.findFirst({
      where: { adminUserId: adminUser.adminUserId },
    });
    expect(session).not.toBeNull();
    expect(session?.sessionToken).toBe(response.sessionToken);
  });

  it('存在しないメールアドレスでログインしようとするとエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(AdminUserService);
    const request = { email: '<EMAIL>', password: 'password' };

    // ===== Act & Assert =====
    await expect(client.login(request)).rejects.toThrow(
      expect.objectContaining({
        code: Code.NotFound,
        rawMessage: 'Admin user not found',
      })
    );
  });

  it('間違ったパスワードでログインしようとするとエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const password = 'StrongPassw0rd!';
    const salt = bcrypt.genSaltSync(10);
    const adminUser = await AdminUserFactory.create({ password_hash: bcrypt.hashSync(password, salt), salt });
    const client = getClient(AdminUserService);
    const request = { email: adminUser.email, password: 'wrongpassword' };

    // ===== Act & Assert =====
    await expect(client.login(request)).rejects.toThrow(
      expect.objectContaining({
        code: Code.InvalidArgument,
        rawMessage: 'Invalid password',
      })
    );
  });
});
