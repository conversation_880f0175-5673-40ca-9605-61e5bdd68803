import { AdminUserService } from '@hami/core-admin-api-schema/admin_user_service_pb';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { changePassword } from './change_password';
import { createAdminUserHandler } from './create_admin_user';
import { getMe } from './get_me';
import { listAdminUsersHandler } from './list_admin_users';
import { login } from './login';
import { updateAdminUserHandler } from './update_admin_user';

import type { ConnectRouter } from '@connectrpc/connect';

export const implAdminUserService = (router: ConnectRouter) =>
  router.service(AdminUserService, {
    login: unwrapResult(login),
    getMe: unwrapResult(getMe),
    listAdminUsers: unwrapResult(listAdminUsersHandler),
    createAdminUser: unwrapResult(createAdminUserHandler),
    updateAdminUser: unwrapResult(updateAdminUserHandler),
    changePassword: unwrapResult(changePassword),
  });
