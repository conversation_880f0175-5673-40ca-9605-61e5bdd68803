import { Code } from '@connectrpc/connect';
import { AdminUserFactory } from '@core-test/factories/admin_user_factory';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import bcrypt from 'bcrypt';
import { AdminUserService } from '@hami/core-admin-api-schema/admin_user_service_pb';

describe('changePassword', () => {
  it('現在のパスワードが正しければパスワードを変更できる', async ({ getClient }) => {
    // ===== Arrange =====
    const oldPassword = 'OldPassw0rd!';
    const salt = bcrypt.genSaltSync(10);
    const adminUser = await AdminUserFactory.create({ password_hash: bcrypt.hashSync(oldPassword, salt), salt });
    const session = await AdminUserSessionFactory.create({
      adminUser: { connect: { adminUserId: adminUser.adminUserId } },
    });
    const client = getClient(AdminUserService);

    // ===== Act =====
    const res = await client.changePassword({
      sessionToken: session.sessionToken,
      currentPassword: oldPassword,
      newPassword: 'NewPassw0rd!',
    });

    // ===== Assert =====
    expect(res.message).toBe('パスワードを変更しました');

    // 新しいパスワードでログインできることを確認
    const loginRes = await client.login({ email: adminUser.email, password: 'NewPassw0rd!' });
    expect(loginRes.sessionToken).toEqual(expect.any(String));

    // 古いパスワードではログインできないことを確認
    await expect(client.login({ email: adminUser.email, password: oldPassword })).rejects.toThrow(
      expect.objectContaining({ code: Code.InvalidArgument, rawMessage: 'Invalid password' })
    );
  });

  it('現在のパスワードが間違っている場合はエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const oldPassword = 'OldPassw0rd!';
    const salt = bcrypt.genSaltSync(10);
    const adminUser = await AdminUserFactory.create({ password_hash: bcrypt.hashSync(oldPassword, salt), salt });
    const session = await AdminUserSessionFactory.create({
      adminUser: { connect: { adminUserId: adminUser.adminUserId } },
    });
    const client = getClient(AdminUserService);

    // ===== Act & Assert =====
    await expect(
      client.changePassword({ sessionToken: session.sessionToken, currentPassword: 'WrongCurrent', newPassword: 'NewPassw0rd!' })
    ).rejects.toThrow(expect.objectContaining({ code: Code.InvalidArgument, rawMessage: 'Invalid password' }));
  });

  it('存在しないセッショントークンの場合は認証エラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(AdminUserService);

    // ===== Act & Assert =====
    await expect(client.changePassword({ sessionToken: 'nonexistent', currentPassword: 'x', newPassword: 'y' })).rejects.toThrow(
      expect.objectContaining({ code: Code.Unauthenticated, rawMessage: 'Admin user not found' })
    );
  });
});
