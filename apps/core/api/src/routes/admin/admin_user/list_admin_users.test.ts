import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { AdminUserFactory } from '@core-test/factories/admin_user_factory';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { AdminUserService, ListAdminUsersRequestSchema } from '@hami/core-admin-api-schema/admin_user_service_pb';

describe('ListAdminUsers', () => {
  it('運営ユーザー一覧を取得できる（ページネーションなし）', async ({ getClient }) => {
    // Arrange
    const adminSession = await AdminUserSessionFactory.create();
    const u1 = await AdminUserFactory.create({ name: 'Alice Admin' });
    const u2 = await AdminUserFactory.create({ name: 'Bob Admin' });

    const client = getClient(AdminUserService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    // Act
    const res = await client.listAdminUsers(create(ListAdminUsersRequestSchema, {}), { headers });

    // Assert
    // 返却配列に作成したユーザーが含まれる
    const ids = res.adminUsers.map((u) => u.adminUserId);
    expect(ids).toEqual(expect.arrayContaining([u1.adminUserId, u2.adminUserId, adminSession.adminUserId]));

    const alice = res.adminUsers.find((u) => u.adminUserId === u1.adminUserId)!;
    expect(alice.email).toBe(u1.email);
    expect(alice.name).toBe('Alice Admin');
    expect(alice.createdAt).toBeDefined();
    expect(alice.updatedAt).toBeDefined();
    // セッション未作成ユーザーは lastLoginAt が undefined
    expect(alice.lastLoginAt).toBeUndefined();

    const sessionUser = res.adminUsers.find((u) => u.adminUserId === adminSession.adminUserId)!;
    // セッションがあるユーザーは lastLoginAt がある
    expect(sessionUser.lastLoginAt).toBeDefined();
  });

  it('認証されていない場合はUnauthenticatedを返す', async ({ getClient }) => {
    const client = getClient(AdminUserService);

    await expect(client.listAdminUsers(create(ListAdminUsersRequestSchema, {}))).rejects.toThrow(
      expect.objectContaining({ code: Code.Unauthenticated })
    );
  });
});

