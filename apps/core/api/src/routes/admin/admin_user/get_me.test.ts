import { Code } from '@connectrpc/connect';
import { AdminUserFactory } from '@core-test/factories/admin_user_factory';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { AdminUserService } from '@hami/core-admin-api-schema/admin_user_service_pb';

describe('getMe', () => {
  it('正常に管理者情報を取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUser = await AdminUserFactory.create();
    const session = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });
    const client = getClient(AdminUserService);
    const request = { sessionToken: session.sessionToken };

    // ===== Act =====
    const response = await client.getMe(request);

    // ===== Assert =====
    expect(response.adminUserId).toBe(adminUser.adminUserId);
    expect(response.name).toBe(adminUser.name);
    expect(response.email).toBe(adminUser.email);
    expect(response.lastLoginAt).toBeDefined();
  });

  it('存在しないセッショントークンで管理者情報を取得しようとするとエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(AdminUserService);
    const request = { sessionToken: 'nonexistent-session-token' };

    // ===== Act & Assert =====
    await expect(client.getMe(request)).rejects.toThrow(
      expect.objectContaining({
        code: Code.Unauthenticated,
        rawMessage: 'Admin user not found',
      })
    );
  });

  it('空のセッショントークンで管理者情報を取得しようとするとエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(AdminUserService);
    const request = { sessionToken: '' };

    // ===== Act & Assert =====
    await expect(client.getMe(request)).rejects.toThrow(
      expect.objectContaining({
        code: Code.InvalidArgument,
        rawMessage: 'String must contain at least 1 character(s)',
      })
    );
  });
}); 