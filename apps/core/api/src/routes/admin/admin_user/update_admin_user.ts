import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { errAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { AdminUserSchema, UpdateAdminUserResponseSchema } from '@hami/core-admin-api-schema/admin_user_service_pb';
import { UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories';
import {
  AdminUserAlreadyExistsError,
  AdminUserNotFoundError,
  findAdminUserBySessionToken,
  updateAdminUser,
} from '@core-api/repositories/admin_user_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { dateToTimestamp, dateToTimestampOptional } from '@core-api/utils/timestamp';
import { ValidationError } from '@core-api/utils/validate_request';

const schema = z.object({
  adminUserId: z.string().min(1),
  name: z.string().min(1, '名前は必須です').optional(),
  email: z.string().email('有効なメールアドレスを入力してください').optional(),
  password: z
    .string()
    .min(8, 'パスワードは8文字以上で入力してください')
    .regex(/^(?=.*[a-zA-Z])(?=.*[0-9]).*$/, 'パスワードは英字と数字を含む必要があります')
    .optional(),
});

export const updateAdminUserHandler = createHandler({
  schema,
  business: (req, ctx) => {
    const sessionToken = ctx.requestHeader.get('sessionToken');
    if (!sessionToken) return errAsync(new UnauthorizedError());
    const { adminUserId, ...rest } = req;
    return findAdminUserBySessionToken({ sessionToken }).andThen(() => updateAdminUser({ adminUserId, data: rest }));
  },
  toResponse: (user) =>
    create(UpdateAdminUserResponseSchema, {
      adminUser: create(AdminUserSchema, {
        adminUserId: user.adminUserId,
        name: user.name,
        email: user.email,
        createdAt: dateToTimestamp(user.createdAt),
        updatedAt: dateToTimestamp(user.updatedAt),
        lastLoginAt: dateToTimestampOptional(user.adminUserSession?.updatedAt ?? null),
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), () => new ConnectError('Unauthorized', Code.Unauthenticated))
      .with(
        P.instanceOf(AdminUserAlreadyExistsError),
        () => new ConnectError('このメールアドレスは既に使用されています', Code.AlreadyExists)
      )
      .with(P.instanceOf(AdminUserNotFoundError), () => new ConnectError('運営ユーザーが見つかりません', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
