import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { LoginResponseSchema } from '@hami/core-admin-api-schema/admin_user_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { AdminUserNotFoundError, upsertAdminUserSession, findAdminUserByEmail } from '@core-api/repositories/admin_user_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { InvalidPasswordError, VerificationFailedError, verifyAdminUserLogin } from '@core-api/utils/login_verification';
import { ValidationError } from '@core-api/utils/validate_request';

export const login = createHandler({
  schema: z.object({
    email: z.string().email(),
    password: z.string().min(1),
  }),
  business: ({ email, password }) =>
    findAdminUserByEmail({ email })
      .map((user) => ({ email, challengePassword: password, user }))
      .andThen(verifyAdminUserLogin)
      .andThen(upsertAdminUserSession),
  toResponse: (session) =>
    create(LoginResponseSchema, {
      sessionToken: session.sessionToken,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(InvalidPasswordError), () => new ConnectError('Invalid password', Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Database error', Code.Internal))
      .with(P.instanceOf(AdminUserNotFoundError), () => new ConnectError('Admin user not found', Code.NotFound))
      .with(P.instanceOf(VerificationFailedError), () => new ConnectError('Verification failed', Code.Internal))
      .exhaustive(),
});
