import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ChangePasswordResponseSchema } from '@hami/core-admin-api-schema/admin_user_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { AdminUserNotFoundError, findAdminUserBySessionToken, updateAdminUserPassword } from '@core-api/repositories/admin_user_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { InvalidPasswordError, VerificationFailedError, verifyAdminUserLogin } from '@core-api/utils/login_verification';
import { ValidationError } from '@core-api/utils/validate_request';

export const changePassword = createHandler({
  schema: z.object({
    sessionToken: z.string().min(1),
    currentPassword: z.string().min(1),
    newPassword: z.string().min(1),
  }),
  business: ({ sessionToken, currentPassword, newPassword }) =>
    findAdminUserBySessionToken({ sessionToken })
      .map(({ user }) => ({ user, challengePassword: currentPassword }))
      .andThen(verifyAdminUserLogin)
      .andThen(({ user }) => updateAdminUserPassword({ adminUserId: user.adminUserId, newPassword })),
  toResponse: () =>
    create(ChangePasswordResponseSchema, {
      message: 'パスワードを変更しました',
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(InvalidPasswordError), () => new ConnectError('Invalid password', Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Database error', Code.Internal))
      .with(P.instanceOf(AdminUserNotFoundError), () => new ConnectError('Admin user not found', Code.Unauthenticated))
      .with(P.instanceOf(VerificationFailedError), () => new ConnectError('Verification failed', Code.Internal))
      .exhaustive(),
});

