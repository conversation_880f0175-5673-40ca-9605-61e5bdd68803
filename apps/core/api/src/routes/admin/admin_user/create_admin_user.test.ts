import { create } from '@bufbuild/protobuf';
import { Code } from '@connectrpc/connect';
import { AdminUserFactory } from '@core-test/factories/admin_user_factory';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { findCapturedMail } from '@core-test/mail_mock';
import { AdminUserService, CreateAdminUserRequestSchema } from '@hami/core-admin-api-schema/admin_user_service_pb';

describe('CreateAdminUser', () => {
  it('運営ユーザーを作成し、仮パスワードがメール送信される', async ({ getClient }) => {
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({ sessionToken: adminSession.sessionToken });
    const client = getClient(AdminUserService);

    const res = await client.createAdminUser(
      create(CreateAdminUserRequestSchema, {
        name: 'New Admin',
        email: '<EMAIL>',
      }),
      { headers }
    );

    expect(res.adminUser).toBeDefined();
    const admin = res.adminUser!;
    expect(admin.name).toBe('New Admin');
    expect(admin.email).toBe('<EMAIL>');
    expect(admin.adminUserId).toBeDefined();
    // DBの状態確認（ハッシュ・ソルトが存在する）
    const created = await vPrisma.client.adminUser.findUnique({
      where: { email: '<EMAIL>' },
      select: { password_hash: true, salt: true },
    });
    expect(created).toBeTruthy();
    expect(created!.password_hash).toBeDefined();
    expect(created!.salt).toBeDefined();

    // メール送信確認（仮パスワード文言を含む）
    const mail = findCapturedMail({
      to: '<EMAIL>',
      subject: '管理者アカウント作成のお知らせ',
      textContains: '仮パスワード',
    });
    expect(mail).toBeTruthy();
  });

  it('認証されていない場合はUnauthenticatedを返す', async ({ getClient }) => {
    const client = getClient(AdminUserService);

    await expect(
      client.createAdminUser(
        create(CreateAdminUserRequestSchema, {
          name: 'X',
          email: '<EMAIL>',
        })
      )
    ).rejects.toThrow(expect.objectContaining({ code: Code.Unauthenticated }));
  });

  it('同一メールが存在する場合はAlreadyExists', async ({ getClient }) => {
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({ sessionToken: adminSession.sessionToken });
    const client = getClient(AdminUserService);

    const existing = await AdminUserFactory.create({ email: '<EMAIL>' });

    await expect(
      client.createAdminUser(
        create(CreateAdminUserRequestSchema, {
          name: 'Dup',
          email: existing.email,
        }),
        { headers }
      )
    ).rejects.toThrow(expect.objectContaining({ code: Code.AlreadyExists }));
  });
});
