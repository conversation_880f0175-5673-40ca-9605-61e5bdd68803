import { create } from '@bufbuild/protobuf';
import { Code } from '@connectrpc/connect';
import { AdminUserFactory } from '@core-test/factories/admin_user_factory';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { AdminUserService, UpdateAdminUserRequestSchema } from '@hami/core-admin-api-schema/admin_user_service_pb';

describe('UpdateAdminUser', () => {
  it('運営ユーザーを編集できる', async ({ getClient }) => {
    const adminSession = await AdminUserSessionFactory.create();
    const target = await AdminUserFactory.create({ name: 'Before', email: '<EMAIL>' });

    const headers = new Headers({ sessionToken: adminSession.sessionToken });
    const client = getClient(AdminUserService);

    const res = await client.updateAdminUser(
      create(UpdateAdminUserRequestSchema, {
        adminUserId: target.adminUserId,
        name: 'After',
        email: '<EMAIL>',
      }),
      { headers }
    );

    expect(res.adminUser).toBeDefined();
    const admin = res.adminUser!;
    expect(admin.adminUserId).toBe(target.adminUserId);
    expect(admin.name).toBe('After');
    expect(admin.email).toBe('<EMAIL>');
  });

  it('認証されていない場合はUnauthenticated', async ({ getClient }) => {
    const target = await AdminUserFactory.create();
    const client = getClient(AdminUserService);

    await expect(
      client.updateAdminUser(create(UpdateAdminUserRequestSchema, { adminUserId: target.adminUserId, name: 'x' }))
    ).rejects.toThrow(expect.objectContaining({ code: Code.Unauthenticated }));
  });

  it('メール重複はAlreadyExists', async ({ getClient }) => {
    const adminSession = await AdminUserSessionFactory.create();
    const a = await AdminUserFactory.create({ email: '<EMAIL>' });
    const b = await AdminUserFactory.create({ email: '<EMAIL>' });

    const headers = new Headers({ sessionToken: adminSession.sessionToken });
    const client = getClient(AdminUserService);

    await expect(
      client.updateAdminUser(
        create(UpdateAdminUserRequestSchema, {
          adminUserId: a.adminUserId,
          email: b.email,
        }),
        { headers }
      )
    ).rejects.toThrow(expect.objectContaining({ code: Code.AlreadyExists }));
  });

  it('パスワード更新でハッシュとソルトが更新される', async ({ getClient }) => {
    const adminSession = await AdminUserSessionFactory.create();
    const target = await AdminUserFactory.create({ email: '<EMAIL>' });
    const before = await vPrisma.client.adminUser.findUnique({
      where: { adminUserId: target.adminUserId },
      select: { password_hash: true, salt: true },
    });

    const headers = new Headers({ sessionToken: adminSession.sessionToken });
    const client = getClient(AdminUserService);

    await client.updateAdminUser(
      create(UpdateAdminUserRequestSchema, {
        adminUserId: target.adminUserId,
        password: 'Password999',
      }),
      { headers }
    );

    const after = await vPrisma.client.adminUser.findUnique({
      where: { adminUserId: target.adminUserId },
      select: { password_hash: true, salt: true },
    });
    expect(after!.password_hash).not.toBe('Password999');
    expect(after!.password_hash).not.toBe(before!.password_hash);
    expect(after!.salt).not.toBe(before!.salt);
  });

  it('存在しないIDはNotFound', async ({ getClient }) => {
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({ sessionToken: adminSession.sessionToken });
    const client = getClient(AdminUserService);

    await expect(
      client.updateAdminUser(
        create(UpdateAdminUserRequestSchema, {
          adminUserId: '00000000-0000-0000-0000-000000000000',
          name: 'x',
        }),
        { headers }
      )
    ).rejects.toThrow(expect.objectContaining({ code: Code.NotFound }));
  });
});
