import { implAdminUserService } from './admin_user';
import { implBankAccountService } from './bank_account';
import { implBankAccountApprovalService } from './bank_account_approval';
import { implBillerService } from './biller';
import { implHelloService } from './hello';
import { implHorseService } from './horse';
import { implHorseBillingService } from './horse_billing';
import { implHorseIncomeService } from './horse_income';
import { implHorseMemberReportService } from './horse_member_report';
import { implInvestmentAndReturnService } from './investment_and_return';
import { implInvestmentApplicationService } from './investment_application';
import { implMemberService } from './member';
import { implMemberClaimAndPayService } from './member_claim_and_pay';
import { implMemberInformationChangeService } from './member_information_change';
import { implMemberRetirementApplicationService } from './member_retirement_application';
import { implMembershipApplicationService } from './membership_application';
import { implAdminMessageService } from './message';
import { implAnnualBundleService } from './annual_bundle';
import type { ConnectRouter } from '@connectrpc/connect';

export const adminRoutes = (router: ConnectRouter) => {
  implHelloService(router);
  implAdminUserService(router);
  implMembershipApplicationService(router);
  implMemberService(router);
  implHorseService(router);
  implHorseBillingService(router);
  implHorseIncomeService(router);
  implHorseMemberReportService(router);
  implInvestmentAndReturnService(router);
  implMemberInformationChangeService(router);
  implInvestmentApplicationService(router);
  implBankAccountService(router);
  implBankAccountApprovalService(router);
  implBillerService(router);
  implMemberClaimAndPayService(router);
  implAdminMessageService(router);
  implMemberRetirementApplicationService(router);
  implAnnualBundleService(router);
};
