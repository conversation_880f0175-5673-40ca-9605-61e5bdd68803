import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory, getClient } from '@core-test/index';
import { BankAccountType, BillerService, ListBillersRequestSchema } from '@hami/core-admin-api-schema/biller_service_pb';
import { client } from '@core-api/utils/prisma';

describe('listBillers API', () => {
  const apiClient = getClient(BillerService);
  it('請求元一覧を取得できる', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const biller1 = await client.biller.create({
      data: {
        name: 'テスト請求元1',
        address: '東京都渋谷区',
        bankName: 'テスト銀行',
        bankBranch: '渋谷支店',
        bankAccount: '1234567',
        bankAccountType: 'savings',
        bankAccountName: 'テスト請求元1',
      },
    });

    const biller2 = await client.biller.create({
      data: {
        name: 'テスト請求元2',
        address: '東京都新宿区',
        bankName: 'テスト銀行',
        bankBranch: '新宿支店',
        bankAccount: '7654321',
        bankAccountType: 'checking',
        bankAccountName: 'テスト請求元2',
      },
    });

    // ===== Act =====
    const request = create(ListBillersRequestSchema, {});
    const response = await apiClient.listBillers(request, { headers });

    // ===== Assert =====
    expect(response.billers.length).toBeGreaterThanOrEqual(2);
    expect(response.billers.some((b) => b.id === biller1.id)).toBe(true);
    expect(response.billers.some((b) => b.id === biller2.id)).toBe(true);
  });
});
