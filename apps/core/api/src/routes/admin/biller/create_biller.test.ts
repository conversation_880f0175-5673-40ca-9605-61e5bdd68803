import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory, getClient } from '@core-test/index';
import { BankAccountType, BillerService, CreateBillerRequestSchema } from '@hami/core-admin-api-schema/biller_service_pb';

describe('createBiller API', () => {
  const apiClient = getClient(BillerService);
  it('新しい請求元を作成できる', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const createData = {
      name: 'テスト請求元',
      address: '東京都渋谷区',
      bankName: 'テスト銀行',
      bankBranch: '渋谷支店',
      bankAccount: '1234567',
      bankAccountType: BankAccountType.SAVINGS,
      bankAccountName: 'テスト請求元',
    };

    // ===== Act =====
    const request = create(CreateBillerRequestSchema, createData);
    const response = await apiClient.createBiller(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();

    // データベースで作成されていることを確認
    const createdBiller = await vPrisma.client.biller.findFirst({
      where: {
        name: createData.name,
        address: createData.address,
        bankName: createData.bankName,
        bankBranch: createData.bankBranch,
        bankAccount: createData.bankAccount,
        bankAccountType: 'savings',
        bankAccountName: createData.bankAccountName,
      },
    });

    expect(createdBiller).toBeDefined();
    expect(createdBiller?.name).toBe(createData.name);
    expect(createdBiller?.address).toBe(createData.address);
    expect(createdBiller?.bankName).toBe(createData.bankName);
    expect(createdBiller?.bankBranch).toBe(createData.bankBranch);
    expect(createdBiller?.bankAccount).toBe(createData.bankAccount);
    expect(createdBiller?.bankAccountType).toBe('savings');
    expect(createdBiller?.bankAccountName).toBe(createData.bankAccountName);
  });

  it('必須フィールドのバリデーションエラーになる', async () => {
    // ===== Arrange =====
    const invalidData = {
      name: '', // 空文字列
      address: '',
      bankName: '',
      bankBranch: '',
      bankAccount: '',
      bankAccountType: BankAccountType.SAVINGS,
      bankAccountName: '',
    };

    // ===== Act & Assert =====
    const request = create(CreateBillerRequestSchema, invalidData);
    await expect(apiClient.createBiller(request)).rejects.toThrow();
  });
});
