import { create } from '@bufbuild/protobuf';
import { ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory, getClient } from '@core-test/index';
import { BankAccountType, BillerService, UpdateBillerRequestSchema } from '@hami/core-admin-api-schema/biller_service_pb';
import { client } from '@core-api/utils/prisma';

describe('updateBiller API', () => {
  const apiClient = getClient(BillerService);
  it('既存の請求元を更新できる', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const biller = await client.biller.create({
      data: {
        name: '更新前の請求元',
        address: '東京都渋谷区',
        bankName: 'テスト銀行',
        bankBranch: '渋谷支店',
        bankAccount: '1234567',
        bankAccountType: 'savings',
        bankAccountName: '更新前の請求元',
      },
    });

    const updateData = {
      id: biller.id,
      name: '更新後の請求元',
      address: '東京都新宿区',
      bankName: 'テスト銀行',
      bankBranch: '新宿支店',
      bankAccount: '7654321',
      bankAccountType: BankAccountType.CHECKING,
      bankAccountName: '更新後の請求元',
    };

    // ===== Act =====
    const request = create(UpdateBillerRequestSchema, updateData);
    const response = await apiClient.updateBiller(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();

    // データベースで更新されていることを確認
    const updatedBiller = await client.biller.findUnique({
      where: { id: biller.id },
    });

    expect(updatedBiller).toBeDefined();
    expect(updatedBiller?.name).toBe(updateData.name);
    expect(updatedBiller?.address).toBe(updateData.address);
    expect(updatedBiller?.bankName).toBe(updateData.bankName);
    expect(updatedBiller?.bankBranch).toBe(updateData.bankBranch);
    expect(updatedBiller?.bankAccount).toBe(updateData.bankAccount);
    expect(updatedBiller?.bankAccountType).toBe('checking');
    expect(updatedBiller?.bankAccountName).toBe(updateData.bankAccountName);
  });

  it('存在しない請求元IDで更新しようとした場合はエラーになる', async () => {
    // ===== Arrange =====
    const updateData = {
      id: 99999, // 存在しないID
      name: '存在しない請求元',
      address: '東京都新宿区',
      bankName: 'テスト銀行',
      bankBranch: '新宿支店',
      bankAccount: '7654321',
      bankAccountType: BankAccountType.CHECKING,
      bankAccountName: '存在しない請求元',
    };

    // ===== Act & Assert =====
    const request = create(UpdateBillerRequestSchema, updateData);
    await expect(apiClient.updateBiller(request)).rejects.toThrow(ConnectError);
  });

  it('必須フィールドのバリデーションエラーになる', async () => {
    // ===== Arrange =====
    const biller = await client.biller.create({
      data: {
        name: 'バリデーションテスト請求元',
        address: '東京都渋谷区',
        bankName: 'テスト銀行',
        bankBranch: '渋谷支店',
        bankAccount: '1234567',
        bankAccountType: 'savings',
        bankAccountName: 'バリデーションテスト請求元',
      },
    });

    const invalidData = {
      id: biller.id,
      name: '', // 空文字列
      address: '',
      bankName: '',
      bankBranch: '',
      bankAccount: '',
      bankAccountType: BankAccountType.SAVINGS,
      bankAccountName: '',
    };

    // ===== Act & Assert =====
    const request = create(UpdateBillerRequestSchema, invalidData);
    await expect(apiClient.updateBiller(request)).rejects.toThrow(ConnectError);
  });
});
