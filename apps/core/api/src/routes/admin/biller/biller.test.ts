import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory, getClient } from '@core-test/index';
import {
  BankAccountType,
  BillerService,
  CreateBillerRequestSchema,
  GetBillerRequestSchema,
  ListBillersRequestSchema,
  UpdateBillerRequestSchema,
} from '@hami/core-admin-api-schema/biller_service_pb';

describe('BillerService', () => {
  const apiClient = getClient(BillerService);
  it('請求元の作成、取得、一覧取得、更新ができる', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const createData = {
      name: 'テスト請求元',
      address: '東京都渋谷区',
      bankName: 'テスト銀行',
      bankBranch: '渋谷支店',
      bankAccount: '1234567',
      bankAccountType: BankAccountType.SAVINGS,
      bankAccountName: 'テスト請求元',
    };

    // ===== Act & Assert =====
    // 1. 請求元を作成
    const createRequest = create(CreateBillerRequestSchema, createData);
    const createResponse = await apiClient.createBiller(createRequest, { headers });
    expect(createResponse).toBeDefined();

    // データベースで作成されていることを確認
    const createdBiller = await vPrisma.client.biller.findFirst({
      where: {
        name: createData.name,
        address: createData.address,
        bankName: createData.bankName,
        bankBranch: createData.bankBranch,
        bankAccount: createData.bankAccount,
        bankAccountType: 'savings',
        bankAccountName: createData.bankAccountName,
      },
    });
    expect(createdBiller).toBeDefined();
    expect(createdBiller?.name).toBe(createData.name);

    // 2. 作成した請求元を取得
    const getRequest = create(GetBillerRequestSchema, { id: createdBiller!.id });
    const getResponse = await apiClient.getBiller(getRequest, { headers });
    expect(getResponse.biller).toBeDefined();
    const foundBiller = getResponse.biller;
    if (foundBiller) {
      expect(foundBiller.id).toBe(createdBiller!.id);
      expect(foundBiller.name).toBe(createData.name);
    }

    // 3. 請求元一覧を取得
    const listRequest = create(ListBillersRequestSchema, {});
    const listResponse = await apiClient.listBillers(listRequest, { headers });
    expect(listResponse.billers.length).toBeGreaterThan(0);
    expect(listResponse.billers.some((b) => b.id === createdBiller!.id)).toBe(true);

    // 4. 請求元を更新
    const updateData = {
      id: createdBiller!.id,
      name: '更新後の請求元',
      address: '東京都新宿区',
      bankName: 'テスト銀行',
      bankBranch: '新宿支店',
      bankAccount: '7654321',
      bankAccountType: BankAccountType.CHECKING,
      bankAccountName: '更新後の請求元',
    };

    const updateRequest = create(UpdateBillerRequestSchema, updateData);
    const updateResponse = await apiClient.updateBiller(updateRequest, { headers });
    expect(updateResponse).toBeDefined();

    // データベースで更新されていることを確認
    const updatedBiller = await vPrisma.client.biller.findUnique({
      where: { id: createdBiller!.id },
    });
    expect(updatedBiller).toBeDefined();
    expect(updatedBiller?.name).toBe(updateData.name);
    expect(updatedBiller?.address).toBe(updateData.address);
    expect(updatedBiller?.bankName).toBe(updateData.bankName);
    expect(updatedBiller?.bankBranch).toBe(updateData.bankBranch);
    expect(updatedBiller?.bankAccount).toBe(updateData.bankAccount);
    expect(updatedBiller?.bankAccountType).toBe('checking');
    expect(updatedBiller?.bankAccountName).toBe(updateData.bankAccountName);
  });

  it('存在しない請求元IDで取得しようとした場合はエラーになる', async () => {
    const request = create(GetBillerRequestSchema, { id: 99999 });
    await expect(apiClient.getBiller(request)).rejects.toThrow();
  });

  it('必須フィールドのバリデーションエラーになる', async () => {
    const invalidData = {
      name: '', // 空文字列
      address: '',
      bankName: '',
      bankBranch: '',
      bankAccount: '',
      bankAccountType: BankAccountType.SAVINGS,
      bankAccountName: '',
    };

    const request = create(CreateBillerRequestSchema, invalidData);
    await expect(apiClient.createBiller(request)).rejects.toThrow();
  });
});
