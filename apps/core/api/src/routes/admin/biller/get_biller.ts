import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetBillerResponseSchema, BillerSchema } from '@hami/core-admin-api-schema/biller_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { BillerNotFoundError, getBiller } from '@core-api/repositories/biller_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getBillerHandler = createHandler({
  schema: z.object({
    id: z.number().int().positive(),
  }),
  business: ({ id }) => {
    return getBiller({ id });
  },
  toResponse: (biller) =>
    create(GetBillerResponseSchema, {
      biller: create(<PERSON><PERSON><PERSON>che<PERSON>, biller),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .with(P.instanceOf(BillerNotFoundError), () => new ConnectError('Biller not found', Code.NotFound))
      .exhaustive(),
});
