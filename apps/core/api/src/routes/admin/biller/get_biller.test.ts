import { create } from '@bufbuild/protobuf';
import { ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory, getClient } from '@core-test/index';
import { BankAccountType, BillerService, GetBillerRequestSchema } from '@hami/core-admin-api-schema/biller_service_pb';
import { client } from '@core-api/utils/prisma';

describe('getBiller API', () => {
  const apiClient = getClient(BillerService);
  it('既存の請求元を取得できる', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const biller = await client.biller.create({
      data: {
        name: 'テスト請求元',
        address: '東京都渋谷区',
        bankName: 'テスト銀行',
        bankBranch: '渋谷支店',
        bankAccount: '1234567',
        bankAccountType: 'savings',
        bankAccountName: 'テスト請求元',
      },
    });

    // ===== Act =====
    const request = create(GetBillerRequestSchema, { id: biller.id });
    const response = await apiClient.getBiller(request, { headers });

    // ===== Assert =====
    expect(response.biller).toBeDefined();
    const foundBiller = response.biller;
    if (foundBiller) {
      expect(foundBiller.id).toBe(biller.id);
      expect(foundBiller.name).toBe(biller.name);
      expect(foundBiller.address).toBe(biller.address);
      expect(foundBiller.bankName).toBe(biller.bankName);
      expect(foundBiller.bankBranch).toBe(biller.bankBranch);
      expect(foundBiller.bankAccount).toBe(biller.bankAccount);
      expect(foundBiller.bankAccountType).toBe(BankAccountType.SAVINGS);
      expect(foundBiller.bankAccountName).toBe(biller.bankAccountName);
    }
  });

  it('存在しない請求元IDで取得しようとした場合はエラーになる', async () => {
    // ===== Act & Assert =====
    const request = create(GetBillerRequestSchema, { id: 99999 });
    await expect(apiClient.getBiller(request)).rejects.toThrow(ConnectError);
  });
});
