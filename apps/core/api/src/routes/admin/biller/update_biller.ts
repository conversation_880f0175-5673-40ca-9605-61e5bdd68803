import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { UpdateBillerResponseSchema, BankAccountType } from '@hami/core-admin-api-schema/biller_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { BillerNotFoundError, updateBiller } from '@core-api/repositories/biller_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const updateBillerHandler = createHandler({
  schema: z.object({
    id: z.number().int().positive(),
    name: z.string().min(1),
    address: z.string().min(1),
    bankName: z.string().min(1),
    bankBranch: z.string().min(1),
    bankAccount: z.string().min(1),
    bankAccountType: z.nativeEnum(BankAccountType),
    bankAccountName: z.string().min(1),
  }),
  business: ({ id, ...data }) => {
    return updateBiller(id, data);
  },
  toResponse: () => create(UpdateBillerResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .with(P.instanceOf(BillerNotFoundError), () => new ConnectError('Biller not found', Code.NotFound))
      .exhaustive(),
});
