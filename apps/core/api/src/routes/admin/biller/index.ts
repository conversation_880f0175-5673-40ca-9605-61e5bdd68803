import { BillerService } from '@hami/core-admin-api-schema/biller_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { createBillerHandler } from './create_biller';
import { getBillerHandler } from './get_biller';
import { listBillersHandler } from './list_billers';
import { updateBiller<PERSON>andler } from './update_biller';

import type { ConnectRouter } from '@connectrpc/connect';

export const implBillerService = (router: ConnectRouter) =>
  router.service(
    BillerService,
    {
      listBillers: unwrapResult(listBillersHandler),
      getBiller: unwrapResult(getBillerHandler),
      createBiller: unwrapResult(createBillerHandler),
      updateBiller: unwrapResult(updateBillerHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  );
