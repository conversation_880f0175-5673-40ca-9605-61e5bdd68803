import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ListBillersResponseSchema, BillerSchema } from '@hami/core-admin-api-schema/biller_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listBillers } from '@core-api/repositories/biller_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const listBillersHandler = createHandler({
  schema: z.object({}),
  business: () => {
    return listBillers();
  },
  toResponse: (billers) =>
    create(ListBillersResponseSchema, {
      billers: billers.map((biller) => create(BillerSchema, biller)),
      nextPageToken: '',
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
