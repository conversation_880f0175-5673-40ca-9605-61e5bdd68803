import { create } from '@bufbuild/protobuf';
import { HelloService } from '@hami/core-admin-api-schema/hello_pb';
import { HelloRequest, HelloResponse, HelloResponseSchema } from '@hami/core-admin-api-schema/hello_pb';

import type { ConnectRouter } from '@connectrpc/connect';

export const implHelloService = (router: ConnectRouter) =>
  router.service(HelloService, {
    sayHello: async (req: HelloRequest): Promise<HelloResponse> => {
      return create(HelloResponseSchema, { message: `Hello, ${req.name}!` });
    },
  });
