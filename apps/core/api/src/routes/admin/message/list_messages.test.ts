import { create } from '@bufbuild/protobuf';
import { messageFactory } from '@core-test/factories/message_factory';
import { AdminMessageService } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { ListMessagesRequestSchema, MessageType, MessageStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { MessageType as PrismaMessageType, MessageStatus as PrismaMessageStatus } from '@hami/prisma';

describe('ListMessages', () => {
  it('メッセージ一覧を取得できること', async ({ getClient }) => {
    // テストデータ作成
    const message1 = await messageFactory.create({
      title: 'テストメッセージ1',
      body: 'テストメッセージ1の本文',
      messageType: PrismaMessageType.INDIVIDUAL,
      status: PrismaMessageStatus.SENT,
      senderId: 1,
    });

    const message2 = await messageFactory.create({
      title: 'テストメッセージ2',
      body: 'テストメッセージ2の本文',
      messageType: PrismaMessageType.BROADCAST,
      status: PrismaMessageStatus.SENT,
      senderId: 1,
    });

    const client = getClient(AdminMessageService);
    const response = await client.listMessages(
      create(ListMessagesRequestSchema, {
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(response.messages).toHaveLength(2);
    expect(response.totalCount).toBe(2);
    expect(response.totalPages).toBe(1);

    // 最新のメッセージが最初に来ることを確認（created_at desc）
    expect(response.messages[0].messageId).toBe(message2.publicId);
    expect(response.messages[0].title).toBe('テストメッセージ2');
    expect(response.messages[0].messageType).toBe(MessageType.BROADCAST);
    expect(response.messages[0].status).toBe(MessageStatus.SENT);

    expect(response.messages[1].messageId).toBe(message1.publicId);
    expect(response.messages[1].title).toBe('テストメッセージ1');
    expect(response.messages[1].messageType).toBe(MessageType.INDIVIDUAL);
    expect(response.messages[1].status).toBe(MessageStatus.SENT);
  });

  it('メッセージタイプでフィルタリングできること', async ({ getClient }) => {
    // テストデータ作成
    await messageFactory.create({
      title: '個別メッセージ',
      messageType: PrismaMessageType.INDIVIDUAL,
      status: PrismaMessageStatus.SENT,
    });

    await messageFactory.create({
      title: '一斉送信メッセージ',
      messageType: PrismaMessageType.BROADCAST,
      status: PrismaMessageStatus.SENT,
    });

    const client = getClient(AdminMessageService);
    const response = await client.listMessages(
      create(ListMessagesRequestSchema, {
        messageType: MessageType.INDIVIDUAL,
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(response.messages).toHaveLength(1);
    expect(response.messages[0].title).toBe('個別メッセージ');
    expect(response.messages[0].messageType).toBe(MessageType.INDIVIDUAL);
  });

  it('ステータスでフィルタリングできること', async ({ getClient }) => {
    // テストデータ作成
    await messageFactory.create({
      title: '送信済みメッセージ',
      status: PrismaMessageStatus.SENT,
    });

    await messageFactory.create({
      title: '下書きメッセージ',
      status: PrismaMessageStatus.DRAFT,
    });

    const client = getClient(AdminMessageService);
    const response = await client.listMessages(
      create(ListMessagesRequestSchema, {
        status: MessageStatus.DRAFT,
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(response.messages).toHaveLength(1);
    expect(response.messages[0].title).toBe('下書きメッセージ');
    expect(response.messages[0].status).toBe(MessageStatus.DRAFT);
  });

  it('検索クエリでフィルタリングできること', async ({ getClient }) => {
    // テストデータ作成
    await messageFactory.create({
      title: '重要なお知らせ',
      body: 'システムメンテナンスのお知らせです',
    });

    await messageFactory.create({
      title: '一般的なメッセージ',
      body: '通常のメッセージです',
    });

    const client = getClient(AdminMessageService);

    // タイトルで検索
    const titleSearchResponse = await client.listMessages(
      create(ListMessagesRequestSchema, {
        searchQuery: '重要',
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(titleSearchResponse.messages).toHaveLength(1);
    expect(titleSearchResponse.messages[0].title).toBe('重要なお知らせ');

    // 本文で検索
    const bodySearchResponse = await client.listMessages(
      create(ListMessagesRequestSchema, {
        searchQuery: 'メンテナンス',
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(bodySearchResponse.messages).toHaveLength(1);
    expect(bodySearchResponse.messages[0].title).toBe('重要なお知らせ');
  });

  it('ページネーションが正しく動作すること', async ({ getClient }) => {
    // 5件のテストデータ作成
    for (let i = 1; i <= 5; i++) {
      await messageFactory.create({
        title: `テストメッセージ${i}`,
        body: `テストメッセージ${i}の本文`,
      });
    }

    const client = getClient(AdminMessageService);

    // 1ページ目（2件ずつ）
    const page1Response = await client.listMessages(
      create(ListMessagesRequestSchema, {
        page: 1,
        pageSize: 2,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(page1Response.messages).toHaveLength(2);
    expect(page1Response.totalCount).toBe(5);
    expect(page1Response.totalPages).toBe(3);

    // 2ページ目
    const page2Response = await client.listMessages(
      create(ListMessagesRequestSchema, {
        page: 2,
        pageSize: 2,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(page2Response.messages).toHaveLength(2);
    expect(page2Response.totalCount).toBe(5);
    expect(page2Response.totalPages).toBe(3);

    // 3ページ目（最後のページ）
    const page3Response = await client.listMessages(
      create(ListMessagesRequestSchema, {
        page: 3,
        pageSize: 2,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(page3Response.messages).toHaveLength(1);
    expect(page3Response.totalCount).toBe(5);
    expect(page3Response.totalPages).toBe(3);
  });

  it('添付ファイル付きメッセージを取得できること', async ({ getClient }) => {
    // 添付ファイル付きメッセージを作成
    const { message } = await messageFactory.createWithAttachments(
      {
        title: '添付ファイル付きメッセージ',
        body: 'このメッセージには添付ファイルがあります',
      },
      2
    );

    const client = getClient(AdminMessageService);
    const response = await client.listMessages(
      create(ListMessagesRequestSchema, {
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(response.messages).toHaveLength(1);
    expect(response.messages[0].messageId).toBe(message.publicId);
    expect(response.messages[0].attachments).toHaveLength(2);

    // 添付ファイルの順序確認
    expect(response.messages[0].attachments[0].displayOrder).toBe(0);
    expect(response.messages[0].attachments[1].displayOrder).toBe(1);
    expect(response.messages[0].attachments[0].fileName).toBe('test-file-1.pdf');
    expect(response.messages[0].attachments[1].fileName).toBe('test-file-2.pdf');
  });

  it('メッセージが存在しない場合は空の配列を返すこと', async ({ getClient }) => {
    const client = getClient(AdminMessageService);
    const response = await client.listMessages(
      create(ListMessagesRequestSchema, {
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(response.messages).toHaveLength(0);
    expect(response.totalCount).toBe(0);
    expect(response.totalPages).toBe(0);
  });
});
