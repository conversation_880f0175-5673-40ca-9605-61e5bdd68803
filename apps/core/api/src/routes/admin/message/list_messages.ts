import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ListMessagesResponseSchema } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { MessageType, MessageStatus } from '@hami/prisma';

import { DatabaseError } from '@core-api/repositories';
import { listMessages } from '@core-api/services/message_service';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

// proto enumの値をPrisma enumの値にマッピング
const mapProtoMessageTypeToEnum = (messageType?: number): MessageType | undefined => {
  switch (messageType) {
    case 1: // MESSAGE_TYPE_INDIVIDUAL
      return MessageType.INDIVIDUAL;
    case 2: // MESSAGE_TYPE_BROADCAST
      return MessageType.BROADCAST;
    case 3: // MESSAGE_TYPE_NOTIFICATION
      return MessageType.NOTIFICATION;
    case 4: // MESSAGE_TYPE_REMINDER
      return MessageType.REMINDER;
    default:
      return undefined;
  }
};

const mapProtoMessageStatusToEnum = (status?: number): MessageStatus | undefined => {
  switch (status) {
    case 1: // MESSAGE_STATUS_DRAFT
      return MessageStatus.DRAFT;
    case 2: // MESSAGE_STATUS_SENDING
      return MessageStatus.SENDING;
    case 3: // MESSAGE_STATUS_SENT
      return MessageStatus.SENT;
    case 4: // MESSAGE_STATUS_FAILED
      return MessageStatus.FAILED;
    default:
      return undefined;
  }
};

export const listMessagesHandler = createHandler({
  schema: z.object({
    messageType: z.number().int().optional(),
    status: z.number().int().optional(),
    senderId: z.number().int().optional(),
    searchQuery: z.string().optional(),
    page: z.number().int().positive().default(1),
    pageSize: z.number().int().positive().max(100).default(20),
    sortBy: z.string().default('created_at'),
    sortOrder: z.string().default('desc'),
  }),
  business: (params) => {
    const { messageType, status, senderId, searchQuery, page, pageSize, sortBy, sortOrder } = params;

    // proto enumの値をPrisma enumの値に変換
    const messageTypeEnum = mapProtoMessageTypeToEnum(messageType);
    const statusEnum = mapProtoMessageStatusToEnum(status);

    // sortOrderのバリデーション
    const validatedSortOrder = sortOrder === 'asc' ? 'asc' : 'desc';

    return listMessages({
      page,
      pageSize,
      messageType: messageTypeEnum,
      status: statusEnum,
      senderId,
      searchQuery,
      sortBy,
      sortOrder: validatedSortOrder,
    }).map((result) => ({
      ...result,
      requestParams: params,
    }));
  },
  toResponse: ({ messages, totalCount, totalPages }) =>
    create(ListMessagesResponseSchema, {
      messages,
      totalCount,
      totalPages,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
