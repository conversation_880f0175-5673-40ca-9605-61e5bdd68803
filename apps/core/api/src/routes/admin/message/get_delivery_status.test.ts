import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { MemberFactory } from '@core-test/factories/member_factory';
import { messageFactory, messageRecipientFactory } from '@core-test/factories/message_factory';
import { AdminMessageService } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { GetDeliveryStatusRequestSchema, RecipientStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { RecipientStatus as PrismaRecipientStatus } from '@hami/prisma';

describe('GetDeliveryStatus', () => {
  it('配信状況一覧を取得できること', async ({ getClient }) => {
    // テストメッセージを作成
    const message = await messageFactory.create({
      title: 'テスト配信メッセージ',
      body: 'テスト配信メッセージの本文',
    });

    // テスト会員を作成
    const member1 = await MemberFactory.create({
      firstName: '太郎',
      lastName: '田中',
      memberNumber: 1001,
    });

    const member2 = await MemberFactory.create({
      firstName: '花子',
      lastName: '佐藤',
      memberNumber: 1002,
    });

    // 配信状況を作成
    await messageRecipientFactory.create({
      messageId: message.messageId,
      memberId: member1.memberId,
      recipientStatus: PrismaRecipientStatus.DELIVERED,
    });

    await messageRecipientFactory.create({
      messageId: message.messageId,
      memberId: member2.memberId,
      recipientStatus: PrismaRecipientStatus.READ,
    });

    const client = getClient(AdminMessageService);
    const response = await client.getDeliveryStatus(
      create(GetDeliveryStatusRequestSchema, {
        messageId: message.publicId,
        page: 1,
        pageSize: 10,
      })
    );

    expect(response.recipients).toHaveLength(2);
    expect(response.totalCount).toBe(2);
    expect(response.totalPages).toBe(1);

    // 配信状況の確認
    const deliveredRecipient = response.recipients.find((r) => r.status === RecipientStatus.DELIVERED);
    const readRecipient = response.recipients.find((r) => r.status === RecipientStatus.READ);

    expect(deliveredRecipient).toBeDefined();
    expect(deliveredRecipient!.memberName).toBe('太郎 田中');
    expect(deliveredRecipient!.memberNumber).toBe('1001');

    expect(readRecipient).toBeDefined();
    expect(readRecipient!.memberName).toBe('花子 佐藤');
    expect(readRecipient!.memberNumber).toBe('1002');
  });

  it('ステータスでフィルタリングできること', async ({ getClient }) => {
    // テストメッセージを作成
    const message = await messageFactory.create({
      title: 'フィルタリングテストメッセージ',
    });

    // テスト会員を作成
    const member1 = await MemberFactory.create({
      firstName: '太郎',
      lastName: '田中',
      memberNumber: 1003,
    });

    const member2 = await MemberFactory.create({
      firstName: '花子',
      lastName: '佐藤',
      memberNumber: 1004,
    });

    // 異なるステータスの配信状況を作成
    await messageRecipientFactory.create({
      messageId: message.messageId,
      memberId: member1.memberId,
      recipientStatus: PrismaRecipientStatus.DELIVERED,
    });

    await messageRecipientFactory.create({
      messageId: message.messageId,
      memberId: member2.memberId,
      recipientStatus: PrismaRecipientStatus.READ,
    });

    const client = getClient(AdminMessageService);

    // 配信済みのみフィルタリング
    const deliveredResponse = await client.getDeliveryStatus(
      create(GetDeliveryStatusRequestSchema, {
        messageId: message.publicId,
        page: 1,
        pageSize: 10,
        statusFilter: RecipientStatus.DELIVERED,
      })
    );

    expect(deliveredResponse.recipients).toHaveLength(1);
    expect(deliveredResponse.recipients[0].status).toBe(RecipientStatus.DELIVERED);
    expect(deliveredResponse.recipients[0].memberName).toBe('太郎 田中');

    // 既読のみフィルタリング
    const readResponse = await client.getDeliveryStatus(
      create(GetDeliveryStatusRequestSchema, {
        messageId: message.publicId,
        page: 1,
        pageSize: 10,
        statusFilter: RecipientStatus.READ,
      })
    );

    expect(readResponse.recipients).toHaveLength(1);
    expect(readResponse.recipients[0].status).toBe(RecipientStatus.READ);
    expect(readResponse.recipients[0].memberName).toBe('花子 佐藤');
  });

  it('ページネーションが正しく動作すること', async ({ getClient }) => {
    // テストメッセージを作成
    const message = await messageFactory.create({
      title: 'ページネーションテストメッセージ',
    });

    // 5人の会員を作成
    const members = [];
    for (let i = 1; i <= 5; i++) {
      const member = await MemberFactory.create({
        firstName: `太郎${i}`,
        lastName: '田中',
        memberNumber: 2000 + i,
      });
      members.push(member);
    }

    // 配信状況を作成
    for (const member of members) {
      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: member.memberId,
        recipientStatus: PrismaRecipientStatus.DELIVERED,
      });
    }

    const client = getClient(AdminMessageService);

    // 1ページ目（2件ずつ）
    const page1Response = await client.getDeliveryStatus(
      create(GetDeliveryStatusRequestSchema, {
        messageId: message.publicId,
        page: 1,
        pageSize: 2,
      })
    );

    expect(page1Response.recipients).toHaveLength(2);
    expect(page1Response.totalCount).toBe(5);
    expect(page1Response.totalPages).toBe(3);

    // 2ページ目
    const page2Response = await client.getDeliveryStatus(
      create(GetDeliveryStatusRequestSchema, {
        messageId: message.publicId,
        page: 2,
        pageSize: 2,
      })
    );

    expect(page2Response.recipients).toHaveLength(2);
    expect(page2Response.totalCount).toBe(5);
    expect(page2Response.totalPages).toBe(3);

    // 3ページ目（最後のページ）
    const page3Response = await client.getDeliveryStatus(
      create(GetDeliveryStatusRequestSchema, {
        messageId: message.publicId,
        page: 3,
        pageSize: 2,
      })
    );

    expect(page3Response.recipients).toHaveLength(1);
    expect(page3Response.totalCount).toBe(5);
    expect(page3Response.totalPages).toBe(3);
  });

  it('存在しないメッセージIDの場合は空の配列を返すこと', async ({ getClient }) => {
    const client = getClient(AdminMessageService);
    const response = await client.getDeliveryStatus(
      create(GetDeliveryStatusRequestSchema, {
        messageId: '99999',
        page: 1,
        pageSize: 10,
      })
    );

    expect(response.recipients).toHaveLength(0);
    expect(response.totalCount).toBe(0);
    expect(response.totalPages).toBe(0);
  });

  it('無効なメッセージIDの場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    const client = getClient(AdminMessageService);

    await expect(
      client.getDeliveryStatus(
        create(GetDeliveryStatusRequestSchema, {
          messageId: '',
          page: 1,
          pageSize: 10,
        })
      )
    ).rejects.toThrow(ConnectError);

    try {
      await client.getDeliveryStatus(
        create(GetDeliveryStatusRequestSchema, {
          messageId: '',
          page: 1,
          pageSize: 10,
        })
      );
    } catch (error) {
      expect(error).toBeInstanceOf(ConnectError);
      if (error instanceof ConnectError) {
        expect(error.code).toBe(Code.InvalidArgument);
      }
    }
  });
});
