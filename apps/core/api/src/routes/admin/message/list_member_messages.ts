import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ListMemberMessagesResponseSchema } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { MessageType, MessageStatus, RecipientStatus } from '@hami/prisma';

import { DatabaseError } from '@core-api/repositories';
import { listMemberMessages } from '@core-api/services/message_service';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

// proto enumの値をPrisma enumの値にマッピング
const mapProtoMessageTypeToEnum = (messageType?: number): MessageType | undefined => {
  switch (messageType) {
    case 1: // MESSAGE_TYPE_INDIVIDUAL
      return MessageType.INDIVIDUAL;
    case 2: // MESSAGE_TYPE_BROADCAST
      return MessageType.BROADCAST;
    case 3: // MESSAGE_TYPE_NOTIFICATION
      return MessageType.NOTIFICATION;
    case 4: // MESSAGE_TYPE_REMINDER
      return MessageType.REMINDER;
    default:
      return undefined;
  }
};

const mapProtoRecipientStatusToEnum = (status?: number): RecipientStatus | undefined => {
  switch (status) {
    case 1: // RECIPIENT_STATUS_PENDING
      return RecipientStatus.PENDING;
    case 2: // RECIPIENT_STATUS_DELIVERED
      return RecipientStatus.DELIVERED;
    case 3: // RECIPIENT_STATUS_READ
      return RecipientStatus.READ;
    case 4: // RECIPIENT_STATUS_FAILED
      return RecipientStatus.FAILED;
    default:
      return undefined;
  }
};

export const listMemberMessagesHandler = createHandler({
  schema: z.object({
    memberId: z.number().int().positive(),
    messageType: z.number().int().optional(),
    status: z.number().int().optional(),
    page: z.number().int().positive().default(1),
    pageSize: z.number().int().positive().max(100).default(20),
    sortBy: z.string().default('created_at'),
    sortOrder: z.string().default('desc'),
  }),
  business: (params) => {
    const { memberId, messageType, status, page, pageSize, sortBy, sortOrder } = params;

    // proto enumの値をPrisma enumの値に変換
    const messageTypeEnum = mapProtoMessageTypeToEnum(messageType);
    const statusEnum = mapProtoRecipientStatusToEnum(status);

    // sortOrderのバリデーション
    const validatedSortOrder = sortOrder === 'asc' ? 'asc' : 'desc';

    return listMemberMessages({
      memberId,
      page,
      pageSize,
      messageType: messageTypeEnum,
      statusFilter: statusEnum,
      sortBy,
      sortOrder: validatedSortOrder,
    });
  },
  toResponse: ({ messages, totalCount, totalPages }) =>
    create(ListMemberMessagesResponseSchema, {
      messages,
      totalCount,
      totalPages,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
