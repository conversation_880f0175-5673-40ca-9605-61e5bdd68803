import { AdminMessageService } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { getDeliveryStatusHandler } from './get_delivery_status';
import { getMessageHandler } from './get_message';
import { listMemberMessagesHandler } from './list_member_messages';
import { listMessagesHandler } from './list_messages';
import type { ConnectRouter } from '@connectrpc/connect';

export const implAdminMessageService = (router: ConnectRouter) =>
  router.service(AdminMessageService, {
    listMessages: unwrapResult(listMessagesHandler),
    getMessage: unwrapResult(getMessageHandler),
    getDeliveryStatus: unwrapResult(getDeliveryStatusHandler),
    listMemberMessages: unwrapResult(listMemberMessagesHandler),
  });
