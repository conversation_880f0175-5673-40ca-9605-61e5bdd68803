import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetDeliveryStatusResponseSchema } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { RecipientStatus } from '@hami/prisma';

import { DatabaseError } from '@core-api/repositories';
import { MessageNotFoundError } from '@core-api/repositories/message_repository';
import { getDeliveryStatus } from '@core-api/services/message_service';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

// proto enumの値をPrisma enumの値にマッピング
const mapProtoRecipientStatusToEnum = (status?: number): RecipientStatus | undefined => {
  switch (status) {
    case 1: // RECIPIENT_STATUS_PENDING
      return RecipientStatus.PENDING;
    case 2: // RECIPIENT_STATUS_DELIVERED
      return RecipientStatus.DELIVERED;
    case 3: // RECIPIENT_STATUS_READ
      return RecipientStatus.READ;
    case 4: // RECIPIENT_STATUS_FAILED
      return RecipientStatus.FAILED;
    default:
      return undefined;
  }
};

export const getDeliveryStatusHandler = createHandler({
  schema: z.object({
    messageId: z.string().min(1),
    page: z.number().int().positive().default(1),
    pageSize: z.number().int().positive().max(100).default(20),
    statusFilter: z.number().int().optional(),
  }),
  business: (params) => {
    const { messageId: publicId, page, pageSize, statusFilter } = params;

    // proto enumの値をPrisma enumの値に変換
    const statusFilterEnum = mapProtoRecipientStatusToEnum(statusFilter);

    return getDeliveryStatus({
      publicId,
      page,
      pageSize,
      statusFilter: statusFilterEnum,
    });
  },
  toResponse: ({ recipients, totalCount, totalPages }) =>
    create(GetDeliveryStatusResponseSchema, {
      recipients,
      totalCount,
      totalPages,
    }),
  toError: (error) => {
    console.error('GetDeliveryStatus error:', error);
    return match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .with(P.instanceOf(MessageNotFoundError), () => new ConnectError('Message not found', Code.NotFound))
      .exhaustive();
  },
});
