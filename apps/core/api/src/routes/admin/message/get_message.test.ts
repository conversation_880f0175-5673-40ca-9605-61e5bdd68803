import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { messageFactory } from '@core-test/factories/message_factory';
import { AdminMessageService } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { GetMessageRequestSchema } from '@hami/core-admin-api-schema/admin_message_service_pb';

describe('GetMessage', () => {
  it('メッセージ詳細を取得できること', async ({ getClient }) => {
    // 添付ファイル付きメッセージを作成
    const { message } = await messageFactory.createWithAttachments(
      {
        title: 'テストメッセージ',
        body: 'テストメッセージの詳細な本文です。',
      },
      2
    );

    const client = getClient(AdminMessageService);
    const response = await client.getMessage(
      create(GetMessageRequestSchema, {
        messageId: message.publicId,
      })
    );

    expect(response.message).toBeDefined();
    expect(response.message!.messageId).toBe(message.publicId);
    expect(response.message!.title).toBe('テストメッセージ');
    expect(response.message!.body).toBe('テストメッセージの詳細な本文です。');
    expect(response.message!.attachments).toHaveLength(2);

    // 添付ファイルの順序確認
    expect(response.message!.attachments[0].displayOrder).toBe(0);
    expect(response.message!.attachments[1].displayOrder).toBe(1);
    expect(response.message!.attachments[0].fileName).toBe('test-file-1.pdf');
    expect(response.message!.attachments[1].fileName).toBe('test-file-2.pdf');

    // ダウンロードURLが設定されていることを確認
    expect(response.message!.attachments[0].downloadUrl).toContain('/api/attachments/download');
    expect(response.message!.attachments[1].downloadUrl).toContain('/api/attachments/download');
  });

  it('存在しないメッセージIDの場合はエラーを返すこと', async ({ getClient }) => {
    const client = getClient(AdminMessageService);

    await expect(
      client.getMessage(
        create(GetMessageRequestSchema, {
          messageId: 'nonexistent-public-id',
        })
      )
    ).rejects.toThrow(ConnectError);

    try {
      await client.getMessage(
        create(GetMessageRequestSchema, {
          messageId: 'nonexistent-public-id',
        })
      );
    } catch (error) {
      expect(error).toBeInstanceOf(ConnectError);
      if (error instanceof ConnectError) {
        expect(error.code).toBe(Code.NotFound);
        expect(error.message).toContain('Message with public ID');
      }
    }
  });

  it('無効なメッセージIDの場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    const client = getClient(AdminMessageService);

    await expect(
      client.getMessage(
        create(GetMessageRequestSchema, {
          messageId: '',
        })
      )
    ).rejects.toThrow(ConnectError);

    try {
      await client.getMessage(
        create(GetMessageRequestSchema, {
          messageId: '',
        })
      );
    } catch (error) {
      expect(error).toBeInstanceOf(ConnectError);
      if (error instanceof ConnectError) {
        expect(error.code).toBe(Code.InvalidArgument);
      }
    }
  });
});
