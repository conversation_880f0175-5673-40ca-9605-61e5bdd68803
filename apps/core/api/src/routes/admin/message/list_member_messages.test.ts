import { create } from '@bufbuild/protobuf';
import { MemberFactory } from '@core-test/factories/member_factory';
import { messageFactory, messageRecipientFactory } from '@core-test/factories/message_factory';
import { AdminMessageService } from '@hami/core-admin-api-schema/admin_message_service_pb';
import { ListMemberMessagesRequestSchema, MessageType, RecipientStatus } from '@hami/core-admin-api-schema/admin_message_service_pb';
import {
  MessageType as PrismaMessageType,
  MessageStatus as PrismaMessageStatus,
  RecipientStatus as PrismaRecipientStatus,
} from '@hami/prisma';

describe('ListMemberMessages', () => {
  it('会員別メッセージ一覧を取得できること', async ({ getClient }) => {
    // テスト会員を作成
    const member = await MemberFactory.create();

    // テストメッセージを作成
    const message1 = await messageFactory.create({
      title: '会員向けメッセージ1',
      body: 'メッセージ本文1',
      messageType: PrismaMessageType.INDIVIDUAL,
      status: PrismaMessageStatus.SENT,
    });

    const message2 = await messageFactory.create({
      title: '会員向けメッセージ2',
      body: 'メッセージ本文2',
      messageType: PrismaMessageType.BROADCAST,
      status: PrismaMessageStatus.SENT,
    });

    // 配信状況を作成
    await messageRecipientFactory.create({
      messageId: message1.messageId,
      memberId: member.memberId,
      recipientStatus: PrismaRecipientStatus.DELIVERED,
    });

    await messageRecipientFactory.create({
      messageId: message2.messageId,
      memberId: member.memberId,
      recipientStatus: PrismaRecipientStatus.READ,
    });

    const client = getClient(AdminMessageService);
    const response = await client.listMemberMessages(
      create(ListMemberMessagesRequestSchema, {
        memberId: member.memberId,
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(response.messages).toHaveLength(2);
    expect(response.totalCount).toBe(2);
    expect(response.totalPages).toBe(1);

    // メッセージの内容を確認
    const messages = response.messages;
    expect(messages[0].title).toBe('会員向けメッセージ2');
    expect(messages[0].messageType).toBe(MessageType.BROADCAST);
    expect(messages[0].recipientStatus).toBe(RecipientStatus.READ);

    expect(messages[1].title).toBe('会員向けメッセージ1');
    expect(messages[1].messageType).toBe(MessageType.INDIVIDUAL);
    expect(messages[1].recipientStatus).toBe(RecipientStatus.DELIVERED);
  });

  it('メッセージタイプでフィルタリングできること', async ({ getClient }) => {
    // テスト会員を作成
    const member = await MemberFactory.create();

    // 異なるタイプのメッセージを作成
    const individualMessage = await messageFactory.create({
      title: '個別メッセージ',
      messageType: PrismaMessageType.INDIVIDUAL,
      status: PrismaMessageStatus.SENT,
    });

    const broadcastMessage = await messageFactory.create({
      title: '一斉送信メッセージ',
      messageType: PrismaMessageType.BROADCAST,
      status: PrismaMessageStatus.SENT,
    });

    // 配信状況を作成
    await messageRecipientFactory.create({
      messageId: individualMessage.messageId,
      memberId: member.memberId,
      recipientStatus: PrismaRecipientStatus.DELIVERED,
    });

    await messageRecipientFactory.create({
      messageId: broadcastMessage.messageId,
      memberId: member.memberId,
      recipientStatus: PrismaRecipientStatus.DELIVERED,
    });

    const client = getClient(AdminMessageService);
    const response = await client.listMemberMessages(
      create(ListMemberMessagesRequestSchema, {
        memberId: member.memberId,
        messageType: MessageType.INDIVIDUAL,
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(response.messages).toHaveLength(1);
    expect(response.messages[0].title).toBe('個別メッセージ');
    expect(response.messages[0].messageType).toBe(MessageType.INDIVIDUAL);
  });

  it('受信状況でフィルタリングできること', async ({ getClient }) => {
    // テスト会員を作成
    const member = await MemberFactory.create();

    // テストメッセージを作成
    const message1 = await messageFactory.create({
      title: '配信済みメッセージ',
      status: PrismaMessageStatus.SENT,
    });

    const message2 = await messageFactory.create({
      title: '既読メッセージ',
      status: PrismaMessageStatus.SENT,
    });

    // 異なる受信状況で配信状況を作成
    await messageRecipientFactory.create({
      messageId: message1.messageId,
      memberId: member.memberId,
      recipientStatus: PrismaRecipientStatus.DELIVERED,
    });

    await messageRecipientFactory.create({
      messageId: message2.messageId,
      memberId: member.memberId,
      recipientStatus: PrismaRecipientStatus.READ,
    });

    const client = getClient(AdminMessageService);
    const response = await client.listMemberMessages(
      create(ListMemberMessagesRequestSchema, {
        memberId: member.memberId,
        status: RecipientStatus.READ,
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(response.messages).toHaveLength(1);
    expect(response.messages[0].title).toBe('既読メッセージ');
    expect(response.messages[0].recipientStatus).toBe(RecipientStatus.READ);
  });

  it('送信済みメッセージのみ取得すること', async ({ getClient }) => {
    // テスト会員を作成
    const member = await MemberFactory.create();

    // 送信済みメッセージを作成
    const sentMessage = await messageFactory.create({
      title: '送信済みメッセージ',
      status: PrismaMessageStatus.SENT,
    });

    // 下書きメッセージを作成
    const draftMessage = await messageFactory.create({
      title: '下書きメッセージ',
      status: PrismaMessageStatus.DRAFT,
    });

    // 配信状況を作成（下書きメッセージにも配信状況があると仮定）
    await messageRecipientFactory.create({
      messageId: sentMessage.messageId,
      memberId: member.memberId,
      recipientStatus: PrismaRecipientStatus.DELIVERED,
    });

    await messageRecipientFactory.create({
      messageId: draftMessage.messageId,
      memberId: member.memberId,
      recipientStatus: PrismaRecipientStatus.PENDING,
    });

    const client = getClient(AdminMessageService);
    const response = await client.listMemberMessages(
      create(ListMemberMessagesRequestSchema, {
        memberId: member.memberId,
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    // 送信済みメッセージのみ取得されることを確認
    expect(response.messages).toHaveLength(1);
    expect(response.messages[0].title).toBe('送信済みメッセージ');
  });

  it('存在しない会員IDの場合は空の配列を返すこと', async ({ getClient }) => {
    const client = getClient(AdminMessageService);
    const response = await client.listMemberMessages(
      create(ListMemberMessagesRequestSchema, {
        memberId: 99999, // 存在しない会員ID
        page: 1,
        pageSize: 10,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(response.messages).toHaveLength(0);
    expect(response.totalCount).toBe(0);
    expect(response.totalPages).toBe(0);
  });

  it('ページネーションが正しく動作すること', async ({ getClient }) => {
    // テスト会員を作成
    const member = await MemberFactory.create();

    // 5つのメッセージを作成
    const messages = [];
    for (let i = 1; i <= 5; i++) {
      const message = await messageFactory.create({
        title: `メッセージ${i}`,
        status: PrismaMessageStatus.SENT,
      });
      messages.push(message);

      await messageRecipientFactory.create({
        messageId: message.messageId,
        memberId: member.memberId,
        recipientStatus: PrismaRecipientStatus.DELIVERED,
      });
    }

    const client = getClient(AdminMessageService);

    // 1ページ目（2件ずつ）
    const page1Response = await client.listMemberMessages(
      create(ListMemberMessagesRequestSchema, {
        memberId: member.memberId,
        page: 1,
        pageSize: 2,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(page1Response.messages).toHaveLength(2);
    expect(page1Response.totalCount).toBe(5);
    expect(page1Response.totalPages).toBe(3);

    // 2ページ目
    const page2Response = await client.listMemberMessages(
      create(ListMemberMessagesRequestSchema, {
        memberId: member.memberId,
        page: 2,
        pageSize: 2,
        sortBy: 'created_at',
        sortOrder: 'desc',
      })
    );

    expect(page2Response.messages).toHaveLength(2);
    expect(page2Response.totalCount).toBe(5);
    expect(page2Response.totalPages).toBe(3);
  });
});
