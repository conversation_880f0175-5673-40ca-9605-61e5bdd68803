import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetMessageResponseSchema } from '@hami/core-admin-api-schema/admin_message_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { MessageNotFoundError } from '@core-api/repositories/message_repository';
import { getMessage } from '@core-api/services/message_service';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getMessageHandler = createHandler({
  schema: z.object({
    messageId: z.string().min(1),
  }),
  business: (params) => {
    const { messageId: publicId } = params;
    return getMessage(publicId);
  },
  toResponse: (message) =>
    create(GetMessageResponseSchema, {
      message,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .with(P.instanceOf(MessageNotFoundError), (e) => new ConnectError(e.message, Code.NotFound))
      .exhaustive(),
});
