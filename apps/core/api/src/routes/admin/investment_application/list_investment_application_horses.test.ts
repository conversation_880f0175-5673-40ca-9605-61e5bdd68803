import { create } from '@bufbuild/protobuf';
import {
  HorseFactory,
  MemberFactory,
  InvestmentApplicationFactory,
  InvestmentContractFactory,
  getClient,
  AdminUserSessionFactory,
} from '@core-test/index';
import {
  InvestmentApplicationService,
  ListInvestmentApplicationHorsesRequestSchema,
} from '@hami/core-admin-api-schema/investment_application_service_pb';
import { InvestmentContractStatus } from '@hami/prisma';

describe('listInvestmentApplicationHorsesHandler', () => {
  it('出資申込がある馬の一覧を取得できる', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse1 = await HorseFactory.create({
      recruitmentNo: 100,
      recruitmentYear: 2025,
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬1の2025',
      horseName: 'テストホース1',
    });

    const horse2 = await HorseFactory.create({
      recruitmentNo: 200,
      recruitmentYear: 2025,
      birthYear: 2021,
      birthMonth: 5,
      birthDay: 20,
      sharesTotal: 200,
      amountTotal: 2000000,
      recruitmentName: 'テスト馬2の2025',
      horseName: 'テストホース2',
    });

    const member1 = await MemberFactory.create();
    const member2 = await MemberFactory.create();

    // horse1に出資申込を2件作成
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 10,
      allocatedNumber: 10,
      rejected: false,
      contracted: false,
    });

    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 20,
      rejected: false,
      contracted: false,
    });

    // horse2に出資申込を1件作成
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse2.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 15,
      rejected: false,
      contracted: false,
    });

    // horse1に契約を1件作成
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      member: { connect: { memberId: member1.memberId } },
      sharesNumber: 5,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationHorsesRequestSchema, {});
    const response = await apiClient.listInvestmentApplicationHorses(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.horses.length).toBeGreaterThanOrEqual(2);

    // horse1の確認
    const horse1Result = response.horses.find((h) => h.horseId === horse1.horseId);
    expect(horse1Result).toBeDefined();
    expect(horse1Result?.totalApplicationShares).toBe(30); // 10 + 20
    expect(horse1Result?.contractedShares).toBe(5);
    expect(horse1Result?.recruitmentShares).toBe(100);

    // horse2の確認
    const horse2Result = response.horses.find((h) => h.horseId === horse2.horseId);
    expect(horse2Result).toBeDefined();
    expect(horse2Result?.totalApplicationShares).toBe(15);
    expect(horse2Result?.contractedShares).toBe(0);
    expect(horse2Result?.recruitmentShares).toBe(200);
  });

  it('rejectedまたはcontractedがtrueの申込は集計に含まれない', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 300,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member = await MemberFactory.create();

    // 有効な申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 10,
      rejected: false,
      contracted: false,
    });

    // rejected=trueの申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 20,
      rejected: true,
      contracted: false,
    });

    // contracted=trueの申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 30,
      rejected: false,
      contracted: true,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationHorsesRequestSchema, {});
    const response = await apiClient.listInvestmentApplicationHorses(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    const horseResult = response.horses.find((h) => h.horseId === horse.horseId);
    expect(horseResult).toBeDefined();
    expect(horseResult?.totalApplicationShares).toBe(10); // 有効な申込のみ
  });

  it('出資申込がない馬は一覧に含まれない', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 400,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationHorsesRequestSchema, {});
    const response = await apiClient.listInvestmentApplicationHorses(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    const horseResult = response.horses.find((h) => h.horseId === horse.horseId);
    expect(horseResult).toBeUndefined();
  });

  it('退会済み会員の申込と契約は集計に含まれない', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 500,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const activeMember = await MemberFactory.create({
      retirementDate: null, // 現役会員
    });

    const retiredMember = await MemberFactory.create({
      retirementDate: new Date('2024-01-01'), // 退会済み会員
    });

    // 現役会員の申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: activeMember.memberId } },
      requestedNumber: 10,
      rejected: false,
      contracted: false,
    });

    // 退会済み会員の申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: retiredMember.memberId } },
      requestedNumber: 20,
      rejected: false,
      contracted: false,
    });

    // 現役会員の契約
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: activeMember.memberId } },
      sharesNumber: 5,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    // 退会済み会員の契約
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: retiredMember.memberId } },
      sharesNumber: 15,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationHorsesRequestSchema, {});
    const response = await apiClient.listInvestmentApplicationHorses(request, { headers });
    console.log(response);

    // ===== Assert =====
    expect(response).toBeDefined();
    const horseResult = response.horses.find((h) => h.horseId === horse.horseId);
    expect(horseResult).toBeDefined();
    expect(horseResult?.totalApplicationShares).toBe(10); // 現役会員の申込のみ
    expect(horseResult?.contractedShares).toBe(5); // 現役会員の契約のみ
    expect(horseResult?.recruitmentShares).toBe(100);
  });
});
