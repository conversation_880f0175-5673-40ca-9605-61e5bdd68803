import { create } from "@bufbuild/protobuf";
import {
  AnnualBundleFactory,
  MemberFactory,
  InvestmentApplicationFactory,
  getClient,
  AdminUserSessionFactory,
} from "@core-test/index";
import {
  InvestmentApplicationService,
  AcceptAnnualBundleInvestmentApplicationsRequestSchema,
  InvestmentApplicationStatus,
} from "@hami/core-admin-api-schema/investment_application_service_pb";
import { AnnualBundleRecruitmentStatus, AnnualBundlePublishStatus } from "@hami/prisma";

const STATUS_ACCEPTED = InvestmentApplicationStatus?.ACCEPTED ?? 2;
const STATUS_REJECTED = InvestmentApplicationStatus?.REJECTED ?? 4;

describe.sequential("acceptAnnualBundleInvestmentApplicationsHandler", () => {
  it("年度バンドル申込の受入を成功させる", async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const bundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 500,
    });
    const member = await MemberFactory.create();

    const application = await InvestmentApplicationFactory.create({
      annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 5,
      allocatedNumber: null,
      rejected: false,
      contracted: false,
    });

    const request = create(AcceptAnnualBundleInvestmentApplicationsRequestSchema, {
      commands: [
        {
          annualBundleId: bundle.annualBundleId,
          investmentApplicationId: application.investmentApplicationId,
          allocatedShares: 3,
        },
      ],
    });

    const response = await apiClient.acceptAnnualBundleInvestmentApplications(request, { headers });

    expect(response.results.length).toBe(1);
    const result = response.results[0];
    expect(result.success).toBe(true);
    expect(result.allocatedShares).toBe(3);
    expect(result.status).toBe(STATUS_ACCEPTED);

    const updated = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application.investmentApplicationId },
    });
    expect(updated?.allocatedNumber).toBe(3);
  });

  it("対象外の申込はエラー結果になる", async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const bundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 400,
    });
    const member = await MemberFactory.create();

    const rejectedApplication = await InvestmentApplicationFactory.create({
      annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 4,
      rejected: true,
      contracted: false,
      allocatedNumber: null,
    });

    const request = create(AcceptAnnualBundleInvestmentApplicationsRequestSchema, {
      commands: [
        {
          annualBundleId: bundle.annualBundleId,
          investmentApplicationId: rejectedApplication.investmentApplicationId,
          allocatedShares: 2,
        },
        {
          annualBundleId: bundle.annualBundleId,
          investmentApplicationId: 9999999,
          allocatedShares: 1,
        },
      ],
    });

    const response = await apiClient.acceptAnnualBundleInvestmentApplications(request, { headers });

    expect(response.results.length).toBe(2);
    const rejectedResult = response.results.find(
      (result) => result.investmentApplicationId === rejectedApplication.investmentApplicationId,
    );
    expect(rejectedResult?.success).toBe(false);
    expect(rejectedResult?.status).toBe(STATUS_REJECTED);
    expect(rejectedResult?.errorMessage).toBeDefined();

    const missingResult = response.results.find((result) => result.investmentApplicationId === 9999999);
    expect(missingResult?.success).toBe(false);
    expect(missingResult?.errorMessage).toBeDefined();

    const stillRejected = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: rejectedApplication.investmentApplicationId },
    });
    expect(stillRejected?.allocatedNumber).toBeNull();
  });
});
