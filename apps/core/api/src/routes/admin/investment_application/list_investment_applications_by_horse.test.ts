import { create } from '@bufbuild/protobuf';
import { HorseFactory, MemberFactory, InvestmentApplicationFactory, getClient, AdminUserSessionFactory } from '@core-test/index';
import {
  InvestmentApplicationService,
  ListInvestmentApplicationsByHorseRequestSchema,
  InvestmentApplicationItem,
  InvestmentApplicationSortField,
  SortOrder,
} from '@hami/core-admin-api-schema/investment_application_service_pb';

describe.sequential('listInvestmentApplicationsByHorseHandler', () => {
  // テスト間の分離を強化するため、各テストで一意な値を使用
  let testCounter = 0;

  beforeEach(async () => {
    testCounter++;
    // 各テスト前に少し待機してデッドロックを回避
    await new Promise((resolve) => setTimeout(resolve, 10));
  });
  it('指定した馬の出資申込一覧を取得できる', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 100 + testCounter * 1000,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: `テスト馬の2025-${testCounter}`,
      horseName: `テストホース-${testCounter}`,
    });

    // Memberを順次作成してデッドロックを回避
    const member1 = await MemberFactory.create({
      lastName: '山田',
      firstName: '太郎',
    });

    // 少し待機してからmember2を作成
    await new Promise((resolve) => setTimeout(resolve, 5));
    const member2 = await MemberFactory.create({
      lastName: '佐藤',
      firstName: '花子',
    });

    const application1 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 10,
      allocatedNumber: 8,
      rejectPartialAllocation: true,
      rejected: false,
      contracted: false,
      isWhole: true,
    });

    const application2 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 20,
      allocatedNumber: null,
      rejectPartialAllocation: false,
      rejected: false,
      contracted: false,
      isWhole: false,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationsByHorseRequestSchema, {
      horseId: horse.horseId,
      sortField: InvestmentApplicationSortField.SORT_FIELD_APPLIED_AT,
      sortOrder: SortOrder.ASC,
    });
    const response = await apiClient.listInvestmentApplicationsByHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(2);

    const app1 = response.applications.find(
      (a: InvestmentApplicationItem) => a.investmentApplicationId === application1.investmentApplicationId
    );
    expect(app1).toBeDefined();
    expect(app1?.memberName).toBe('山田 太郎');
    expect(app1?.memberId).toBe(member1.memberId);
    expect(app1?.memberNumber).toBe(member1.memberNumber);
    expect(app1?.requestedShares).toBe(10);
    expect(app1?.rejectPartialAllocation).toBe(true);
    expect(app1?.allocatedShares).toBe(8);
    expect(app1?.isWhole).toBe(true);

    const app2 = response.applications.find(
      (a: InvestmentApplicationItem) => a.investmentApplicationId === application2.investmentApplicationId
    );
    expect(app2).toBeDefined();
    expect(app2?.memberName).toBe('佐藤 花子');
    expect(app2?.memberId).toBe(member2.memberId);
    expect(app2?.memberNumber).toBe(member2.memberNumber);
    expect(app2?.requestedShares).toBe(20);
    expect(app2?.rejectPartialAllocation).toBe(false);
    expect(app2?.allocatedShares).toBeUndefined();
    expect(app2?.isWhole).toBe(false);
  });

  it('部分的成功のケースで正しく動作する', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 200 + testCounter * 1000,
      recruitmentYear: 2025,
      sharesTotal: 50,
      amountTotal: 1000000,
      recruitmentName: `テスト馬の2025-${testCounter}`,
      horseName: `テストホース-${testCounter}`,
    });

    // Memberを順次作成してデッドロックを回避
    const member1 = await MemberFactory.create({
      lastName: '鈴木',
      firstName: '一郎',
    });

    // 少し待機してからmember2を作成
    await new Promise((resolve) => setTimeout(resolve, 5));
    const member2 = await MemberFactory.create({
      lastName: '高橋',
      firstName: '次郎',
    });

    // 部分受入OKの申込
    const application1 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 30,
      allocatedNumber: 20, // 部分受入
      rejectPartialAllocation: false,
      rejected: false,
      contracted: false,
      isWhole: true,
    });

    // 部分受入拒否の申込
    const application2 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 25,
      allocatedNumber: 0, // 部分受入拒否のため0
      rejectPartialAllocation: true,
      rejected: false,
      contracted: false,
      isWhole: false,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationsByHorseRequestSchema, {
      horseId: horse.horseId,
      sortField: InvestmentApplicationSortField.SORT_FIELD_APPLIED_AT,
      sortOrder: SortOrder.ASC,
    });
    const response = await apiClient.listInvestmentApplicationsByHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(2);

    const app1 = response.applications.find(
      (a: InvestmentApplicationItem) => a.investmentApplicationId === application1.investmentApplicationId
    );
    expect(app1).toBeDefined();
    expect(app1?.memberName).toBe('鈴木 一郎');
    expect(app1?.memberId).toBe(member1.memberId);
    expect(app1?.memberNumber).toBe(member1.memberNumber);
    expect(app1?.requestedShares).toBe(30);
    expect(app1?.rejectPartialAllocation).toBe(false);
    expect(app1?.allocatedShares).toBe(20);
    expect(app1?.isWhole).toBe(true);
    const app2 = response.applications.find(
      (a: InvestmentApplicationItem) => a.investmentApplicationId === application2.investmentApplicationId
    );
    expect(app2).toBeDefined();
    expect(app2?.memberName).toBe('高橋 次郎');
    expect(app2?.memberId).toBe(member2.memberId);
    expect(app2?.memberNumber).toBe(member2.memberNumber);
    expect(app2?.requestedShares).toBe(25);
    expect(app2?.rejectPartialAllocation).toBe(true);
    expect(app2?.allocatedShares).toBe(0);
    expect(app2?.isWhole).toBe(false);
  });

  it('rejectedまたはcontractedがtrueの申込は取得されない', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 300 + testCounter * 1000,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: `テスト馬の2025-${testCounter}`,
      horseName: `テストホース-${testCounter}`,
    });

    const member = await MemberFactory.create();

    // 有効な申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 10,
      rejected: false,
      contracted: false,
      isWhole: true,
    });

    // rejected=trueの申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 20,
      rejected: true,
      contracted: false,
      isWhole: false,
    });

    // contracted=trueの申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 30,
      rejected: false,
      contracted: true,
      isWhole: false,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationsByHorseRequestSchema, {
      horseId: horse.horseId,
      sortField: InvestmentApplicationSortField.SORT_FIELD_APPLIED_AT,
      sortOrder: SortOrder.ASC,
    });
    const response = await apiClient.listInvestmentApplicationsByHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(1); // 有効な申込のみ
    expect(response.applications[0].requestedShares).toBe(10);
    expect(response.applications[0].isWhole).toBe(true);
  });

  it('存在しない馬IDでも空の配列が返される', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationsByHorseRequestSchema, {
      horseId: 99999,
      sortField: InvestmentApplicationSortField.SORT_FIELD_APPLIED_AT,
      sortOrder: SortOrder.ASC,
    });
    const response = await apiClient.listInvestmentApplicationsByHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(0);
  });

  it('退会済み会員の申込は取得されない', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 400 + testCounter * 1000,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: `テスト馬の2025-${testCounter}`,
      horseName: `テストホース-${testCounter}`,
    });

    // Memberを順次作成してデッドロックを回避
    const activeMember = await MemberFactory.create({
      lastName: '現役',
      firstName: '太郎',
      retirementDate: null, // 現役会員
    });

    // 少し待機してからretiredMemberを作成
    await new Promise((resolve) => setTimeout(resolve, 5));
    const retiredMember = await MemberFactory.create({
      lastName: '退会',
      firstName: '花子',
      retirementDate: new Date('2024-01-01'), // 退会済み会員
    });

    // 現役会員の申込
    const activeApplication = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: activeMember.memberId } },
      requestedNumber: 10,
      allocatedNumber: 8,
      rejectPartialAllocation: false,
      rejected: false,
      contracted: false,
      isWhole: true,
    });

    // 退会済み会員の申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: retiredMember.memberId } },
      requestedNumber: 20,
      allocatedNumber: 15,
      rejectPartialAllocation: true,
      rejected: false,
      contracted: false,
      isWhole: false,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationsByHorseRequestSchema, {
      horseId: horse.horseId,
      sortField: InvestmentApplicationSortField.SORT_FIELD_APPLIED_AT,
      sortOrder: SortOrder.ASC,
    });
    const response = await apiClient.listInvestmentApplicationsByHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(1); // 現役会員の申込のみ

    const activeApp = response.applications.find(
      (a: InvestmentApplicationItem) => a.investmentApplicationId === activeApplication.investmentApplicationId
    );
    expect(activeApp).toBeDefined();
    expect(activeApp?.memberName).toBe('現役 太郎');
    expect(activeApp?.memberId).toBe(activeMember.memberId);
    expect(activeApp?.memberNumber).toBe(activeMember.memberNumber);
    expect(activeApp?.requestedShares).toBe(10);
    expect(activeApp?.rejectPartialAllocation).toBe(false);
    expect(activeApp?.allocatedShares).toBe(8);
    expect(activeApp?.isWhole).toBe(true);
    // 退会済み会員の申込が含まれないことを確認
    const retiredAppResults = response.applications.filter((a: InvestmentApplicationItem) => a.memberName === '退会 花子');
    expect(retiredAppResults).toHaveLength(0);
  });

  it('isWhole=trueの検索条件で全口数申込のみを取得できる', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 500 + testCounter * 1000,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: `テスト馬の2025-${testCounter}`,
      horseName: `テストホース-${testCounter}`,
    });

    // Memberを順次作成してデッドロックを回避
    const member1 = await MemberFactory.create({
      lastName: '全口数',
      firstName: '太郎',
    });

    // 少し待機してからmember2を作成
    await new Promise((resolve) => setTimeout(resolve, 5));
    const member2 = await MemberFactory.create({
      lastName: '部分口数',
      firstName: '花子',
    });

    // 全口数申込（isWhole=true）
    const wholeApplication = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 50,
      allocatedNumber: 50,
      rejectPartialAllocation: false,
      rejected: false,
      contracted: false,
      isWhole: true,
    });

    // 部分口数申込（isWhole=false）
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 30,
      allocatedNumber: 25,
      rejectPartialAllocation: false,
      rejected: false,
      contracted: false,
      isWhole: false,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationsByHorseRequestSchema, {
      horseId: horse.horseId,
      isWhole: true,
      sortField: InvestmentApplicationSortField.SORT_FIELD_APPLIED_AT,
      sortOrder: SortOrder.ASC,
    });
    const response = await apiClient.listInvestmentApplicationsByHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(1); // 全口数申込のみ

    const wholeApp = response.applications.find(
      (a: InvestmentApplicationItem) => a.investmentApplicationId === wholeApplication.investmentApplicationId
    );
    expect(wholeApp).toBeDefined();
    expect(wholeApp?.memberName).toBe('全口数 太郎');
    expect(wholeApp?.memberId).toBe(member1.memberId);
    expect(wholeApp?.memberNumber).toBe(member1.memberNumber);
    expect(wholeApp?.requestedShares).toBe(50);
    expect(wholeApp?.allocatedShares).toBe(50);
    expect(wholeApp?.isWhole).toBe(true);

    // 部分口数申込が含まれないことを確認
    const partialAppResults = response.applications.filter((a: InvestmentApplicationItem) => a.memberName === '部分口数 花子');
    expect(partialAppResults).toHaveLength(0);
  });

  it('isWhole=falseの検索条件で部分口数申込のみを取得できる', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 600 + testCounter * 1000,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: `テスト馬の2025-${testCounter}`,
      horseName: `テストホース-${testCounter}`,
    });

    // Memberを順次作成してデッドロックを回避
    const member1 = await MemberFactory.create({
      lastName: '全口数',
      firstName: '太郎',
    });

    // 少し待機してからmember2を作成
    await new Promise((resolve) => setTimeout(resolve, 5));
    const member2 = await MemberFactory.create({
      lastName: '部分口数',
      firstName: '花子',
    });

    // 全口数申込（isWhole=true）
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 50,
      allocatedNumber: 50,
      rejectPartialAllocation: false,
      rejected: false,
      contracted: false,
      isWhole: true,
    });

    // 部分口数申込（isWhole=false）
    const partialApplication = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 30,
      allocatedNumber: 25,
      rejectPartialAllocation: false,
      rejected: false,
      contracted: false,
      isWhole: false,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationsByHorseRequestSchema, {
      horseId: horse.horseId,
      isWhole: false,
      sortField: InvestmentApplicationSortField.SORT_FIELD_APPLIED_AT,
      sortOrder: SortOrder.ASC,
    });
    const response = await apiClient.listInvestmentApplicationsByHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(1); // 部分口数申込のみ

    const partialApp = response.applications.find(
      (a: InvestmentApplicationItem) => a.investmentApplicationId === partialApplication.investmentApplicationId
    );
    expect(partialApp).toBeDefined();
    expect(partialApp?.memberName).toBe('部分口数 花子');
    expect(partialApp?.memberId).toBe(member2.memberId);
    expect(partialApp?.memberNumber).toBe(member2.memberNumber);
    expect(partialApp?.requestedShares).toBe(30);
    expect(partialApp?.allocatedShares).toBe(25);
    expect(partialApp?.isWhole).toBe(false);

    // 全口数申込が含まれないことを確認
    const wholeAppResults = response.applications.filter((a: InvestmentApplicationItem) => a.memberName === '全口数 太郎');
    expect(wholeAppResults).toHaveLength(0);
  });

  it('カナ昇順で並べ替えできる', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const horse = await HorseFactory.create({
      recruitmentNo: 700 + testCounter * 1000,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: `テスト馬の2025-${testCounter}`,
      horseName: `テストホース-${testCounter}`,
    });

    // カナが異なる会員を作成（lastNameKana/firstNameKanaを意図的に指定）
    const memberA = await MemberFactory.create({
      lastName: '仮名', firstName: 'ア', lastNameKana: 'ア', firstNameKana: 'ア'
    });
    // 少し待機
    await new Promise((r) => setTimeout(r, 5));
    const memberB = await MemberFactory.create({
      lastName: '仮名', firstName: 'カ', lastNameKana: 'カ', firstNameKana: 'カ'
    });

    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: memberB.memberId } },
      requestedNumber: 10,
      rejected: false,
      contracted: false,
      isWhole: false,
    });
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: memberA.memberId } },
      requestedNumber: 10,
      rejected: false,
      contracted: false,
      isWhole: false,
    });

    // ===== Act =====
    const request = create(ListInvestmentApplicationsByHorseRequestSchema, {
      horseId: horse.horseId,
      sortField: InvestmentApplicationSortField.SORT_FIELD_MEMBER_NAME_KANA,
      sortOrder: SortOrder.ASC,
    });
    const response = await apiClient.listInvestmentApplicationsByHorse(request, { headers });

    // ===== Assert =====
    expect(response.applications.length).toBe(2);
    const names = response.applications.map(a => a.memberName);
    // ア→カ の順であること
    expect(names[0]).toContain('ア');
    expect(names[1]).toContain('カ');
  });
});
