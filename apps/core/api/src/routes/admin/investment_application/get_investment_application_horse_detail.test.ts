import { create } from '@bufbuild/protobuf';
import {
  HorseFactory,
  MemberFactory,
  InvestmentApplicationFactory,
  InvestmentContractFactory,
  AdminUserSessionFactory,
  getClient,
} from '@core-test/index';
import {
  InvestmentApplicationService,
  GetInvestmentApplicationHorseDetailRequestSchema,
} from '@hami/core-admin-api-schema/investment_application_service_pb';
import { InvestmentContractStatus } from '@hami/prisma';

describe('getInvestmentApplicationHorseDetailHandler', () => {
  const apiClient = getClient(InvestmentApplicationService);
  it('馬の詳細情報を取得できる', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 6001,
      recruitmentYear: 2025,
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member1 = await MemberFactory.create();
    const member2 = await MemberFactory.create();

    // 出資申込を作成
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 10,
      allocatedNumber: 10,
      rejected: false,
      contracted: false,
    });

    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 20,
      rejected: false,
      contracted: false,
    });

    // 契約を作成
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      sharesNumber: 5,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    // ===== Act =====
    const request = create(GetInvestmentApplicationHorseDetailRequestSchema, {
      horseId: horse.horseId,
    });
    const response = await apiClient.getInvestmentApplicationHorseDetail(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.horseId).toBe(horse.horseId);
    expect(response.birthYear).toBe(2020);
    expect(response.birthMonth).toBe(4);
    expect(response.birthDay).toBe(15);
    expect(response.recruitmentYear).toBe(2025);
    expect(response.recruitmentNo).toBe(6001);
    expect(response.horseName).toBe('テストホース');
    expect(response.recruitmentName).toBe('テスト馬の2025');
    expect(response.totalApplicationShares).toBe(10);
    expect(response.recruitmentShares).toBe(100);
    expect(response.contractedShares).toBe(5);
    expect(response.recruitmentTotalAmount).toBe(1000000);
  });

  it('存在しない馬IDでエラーになる', async () => {
    // ===== Act & Assert =====
    const request = create(GetInvestmentApplicationHorseDetailRequestSchema, {
      horseId: 99999,
    });
    await expect(apiClient.getInvestmentApplicationHorseDetail(request)).rejects.toThrow();
  });

  it('rejectedまたはcontractedがtrueの申込は集計に含まれない', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 6002,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member = await MemberFactory.create();

    // 有効な申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 10,
      allocatedNumber: 5,
      rejected: false,
      contracted: false,
    });

    // rejected=trueの申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 20,
      allocatedNumber: 5,
      rejected: true,
      contracted: false,
    });

    // contracted=trueの申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 30,
      allocatedNumber: 7,
      rejected: false,
      contracted: true,
    });

    // ===== Act =====
    const request = create(GetInvestmentApplicationHorseDetailRequestSchema, {
      horseId: horse.horseId,
    });
    const response = await apiClient.getInvestmentApplicationHorseDetail(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.totalApplicationShares).toBe(5); // 有効な申込のみ
  });

  it('退会済み会員の申込と契約は集計に含まれない', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 6003,
      recruitmentYear: 2025,
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const activeMember = await MemberFactory.create({
      retirementDate: null, // 現役会員
    });

    const retiredMember = await MemberFactory.create({
      retirementDate: new Date('2024-01-01'), // 退会済み会員
    });

    // 現役会員の申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: activeMember.memberId } },
      requestedNumber: 10,
      allocatedNumber: 8,
      rejected: false,
      contracted: false,
    });

    // 退会済み会員の申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: retiredMember.memberId } },
      requestedNumber: 20,
      allocatedNumber: 15,
      rejected: false,
      contracted: false,
    });

    // 現役会員の契約
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: activeMember.memberId } },
      sharesNumber: 5,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    // 退会済み会員の契約
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: retiredMember.memberId } },
      sharesNumber: 12,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    // ===== Act =====
    const request = create(GetInvestmentApplicationHorseDetailRequestSchema, {
      horseId: horse.horseId,
    });
    const response = await apiClient.getInvestmentApplicationHorseDetail(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.horseId).toBe(horse.horseId);
    expect(response.birthYear).toBe(2020);
    expect(response.birthMonth).toBe(4);
    expect(response.birthDay).toBe(15);
    expect(response.recruitmentYear).toBe(2025);
    expect(response.recruitmentNo).toBe(6003);
    expect(response.horseName).toBe('テストホース');
    expect(response.recruitmentName).toBe('テスト馬の2025');
    expect(response.totalApplicationShares).toBe(8); // 現役会員の申込のみ
    expect(response.recruitmentShares).toBe(100);
    expect(response.contractedShares).toBe(5); // 現役会員の契約のみ
    expect(response.recruitmentTotalAmount).toBe(1000000);
  });
});
