import { create } from '@bufbuild/protobuf';
import { HorseFactory, MemberFactory, InvestmentApplicationFactory, getClient, AdminUserSessionFactory } from '@core-test/index';
import {
  InvestmentApplicationService,
  ListContractTargetApplicationsRequestSchema,
} from '@hami/core-admin-api-schema/investment_application_service_pb';
import type { AdminUserSession } from '@hami/prisma';

describe('listContractTargetApplicationsHandler', () => {
  const apiClient = getClient(InvestmentApplicationService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
  });

  it('契約締結対象の申込一覧を取得できる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 100,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member1 = await MemberFactory.create({
      lastName: '山田',
      firstName: '太郎',
    });

    const member2 = await MemberFactory.create({
      lastName: '佐藤',
      firstName: '花子',
    });

    // 契約締結対象の申込（allocatedNumber > 0）
    const application1 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 10,
      allocatedNumber: 8,
      rejected: false,
      contracted: false,
    });

    const application2 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 20,
      allocatedNumber: 15,
      rejected: false,
      contracted: false,
    });

    // 対象外の申込（allocatedNumber = 0）
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 5,
      allocatedNumber: 0,
      rejected: false,
      contracted: false,
    });

    // 対象外の申込（allocatedNumber = null）
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 5,
      allocatedNumber: null,
      rejected: false,
      contracted: false,
    });

    // ===== Act =====
    const request = create(ListContractTargetApplicationsRequestSchema, {
      horseId: horse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listContractTargetApplications(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(2);

    const app1 = response.applications.find((a) => a.investmentApplicationId === application1.investmentApplicationId);
    expect(app1).toBeDefined();
    expect(app1 && 'memberName' in app1 ? app1.memberName : undefined).toBe('山田 太郎');
    expect(app1?.requestedShares).toBe(10);
    expect(app1?.allocatedNumber).toBe(8);

    const app2 = response.applications.find((a) => a.investmentApplicationId === application2.investmentApplicationId);
    expect(app2).toBeDefined();
    expect(app2 && 'memberName' in app2 ? app2.memberName : undefined).toBe('佐藤 花子');
    expect(app2?.requestedShares).toBe(20);
    expect(app2?.allocatedNumber).toBe(15);
  });

  it('rejectedまたはcontractedがtrueの申込は対象外', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 200,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member = await MemberFactory.create();

    // 有効な申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 10,
      allocatedNumber: 8,
      rejected: false,
      contracted: false,
    });

    // rejected=trueの申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 20,
      allocatedNumber: 15,
      rejected: true,
      contracted: false,
    });

    // contracted=trueの申込
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 30,
      allocatedNumber: 25,
      rejected: false,
      contracted: true,
    });

    // ===== Act =====
    const request = create(ListContractTargetApplicationsRequestSchema, {
      horseId: horse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listContractTargetApplications(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(1); // 有効な申込のみ
    expect(response.applications[0].requestedShares).toBe(10);
  });

  it('存在しない馬IDでも空の配列が返される', async () => {
    // ===== Act =====
    const request = create(ListContractTargetApplicationsRequestSchema, {
      horseId: 99999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listContractTargetApplications(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.applications.length).toBe(0);
  });
});
