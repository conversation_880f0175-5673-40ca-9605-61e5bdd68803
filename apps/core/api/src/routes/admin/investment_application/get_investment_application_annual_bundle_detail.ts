import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  GetInvestmentApplicationAnnualBundleDetailResponseSchema 
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { getAnnualBundleInvestmentApplicationDetail } from '@core-api/repositories/investment_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getInvestmentApplicationAnnualBundleDetailHandler = createHandler({
  schema: z.object({
    annualBundleId: z.number().int().positive(),
  }),
  business: ({ annualBundleId }) => {
    return ResultAsync.fromPromise(
      getAnnualBundleInvestmentApplicationDetail(annualBundleId),
      (error) => new DatabaseError(`Failed to get annual bundle investment application detail: ${error}`)
    );
  },
  toResponse: (detail) =>
    create(GetInvestmentApplicationAnnualBundleDetailResponseSchema, {
      annualBundleId: detail.annualBundleId,
      fiscalYear: detail.fiscalYear,
      bundleName: detail.bundleName,
      contractedShares: detail.contractedShares,
      recruitmentShares: detail.recruitmentShares,
      recruitmentTotalAmount: detail.recruitmentTotalAmount,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
