import { InvestmentApplicationService } from '@hami/core-admin-api-schema/investment_application_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { acceptAnnualBundleInvestmentApplicationsHandler } from './accept_annual_bundle_investment_applications';
import { acceptInvestmentApplicationsHandler } from './accept_investment_applications';
import { completeAnnualBundleInvestmentApplicationsHandler } from './complete_annual_bundle_investment_applications';
import { completeInvestmentContractsHandler } from './complete_investment_contracts';
import { getInvestmentApplicationAnnualBundleDetailHandler } from './get_investment_application_annual_bundle_detail';
import { getInvestmentApplicationHorseDetailHandler } from './get_investment_application_horse_detail';
import { listAnnualBundleInvestmentApplicationsHandler } from './list_annual_bundle_investment_applications';
import { listContractTargetApplicationsHandler } from './list_contract_target_applications';
import { listInvestmentApplicationHorsesHandler } from './list_investment_application_horses';
import { listInvestmentApplicationsHandler } from './list_investment_applications';
import { listInvestmentApplicationsByHorseHandler } from './list_investment_applications_by_horse';

import type { ConnectRouter } from '@connectrpc/connect';

export const implInvestmentApplicationService = (router: ConnectRouter) =>
  router.service(
    InvestmentApplicationService,
    {
      listInvestmentApplicationHorses: unwrapResult(listInvestmentApplicationHorsesHandler),
      getInvestmentApplicationHorseDetail: unwrapResult(getInvestmentApplicationHorseDetailHandler),
      getInvestmentApplicationAnnualBundleDetail: unwrapResult(getInvestmentApplicationAnnualBundleDetailHandler),
      listInvestmentApplicationsByHorse: unwrapResult(listInvestmentApplicationsByHorseHandler),
      listInvestmentApplications: unwrapResult(listInvestmentApplicationsHandler),
      listAnnualBundleInvestmentApplications: unwrapResult(listAnnualBundleInvestmentApplicationsHandler),
      acceptInvestmentApplications: unwrapResult(acceptInvestmentApplicationsHandler),
      acceptAnnualBundleInvestmentApplications: unwrapResult(acceptAnnualBundleInvestmentApplicationsHandler),
      listContractTargetApplications: unwrapResult(listContractTargetApplicationsHandler),
      completeInvestmentContracts: unwrapResult(completeInvestmentContractsHandler),
      completeAnnualBundleInvestmentApplications: unwrapResult(completeAnnualBundleInvestmentApplicationsHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  ); 