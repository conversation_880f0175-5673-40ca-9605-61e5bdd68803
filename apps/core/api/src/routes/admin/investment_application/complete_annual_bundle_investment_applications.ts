import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { z } from 'zod';
import {
  CompleteAnnualBundleInvestmentApplicationsResponseSchema,
  CompleteAnnualBundleInvestmentApplicationResultSchema,
  InvestmentApplicationStatus,
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { completeAnnualBundleInvestmentApplicationsUsecase } from '@core-api/usecases/complete_annual_bundle_investment_applications_usecase';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const statusMap: Record<string, InvestmentApplicationStatus> = {
  applied: InvestmentApplicationStatus.APPLIED,
  accepted: InvestmentApplicationStatus.ACCEPTED,
  contractCompleted: InvestmentApplicationStatus.CONTRACT_COMPLETED,
  rejected: InvestmentApplicationStatus.REJECTED,
};

export const completeAnnualBundleInvestmentApplicationsHandler = createHandler({
  schema: z.object({
    commands: z
      .array(
        z.object({
          annualBundleId: z.number().int().positive(),
          investmentApplicationId: z.number().int().positive(),
        }),
      )
      .min(1),
  }),
  business: ({ commands }) =>
    ResultAsync.fromPromise(
      completeAnnualBundleInvestmentApplicationsUsecase(commands, new Date()),
      (error) => new DatabaseError(`Failed to complete annual bundle investment applications: ${error}`),
    ),
  toResponse: (results) =>
    create(CompleteAnnualBundleInvestmentApplicationsResponseSchema, {
      results: results.map((result) =>
        create(CompleteAnnualBundleInvestmentApplicationResultSchema, {
          investmentApplicationId: result.investmentApplicationId,
          success: result.success,
          errorMessage: result.errorMessage,
          contractedShares: result.contractedShares,
          memberName: result.memberName,
          memberNumber: result.memberNumber,
          status: result.status ? statusMap[result.status] : undefined,
        }),
      ),
    }),
  toError: (error) =>
    error instanceof ValidationError ? new ConnectError(error.message, Code.InvalidArgument) : new ConnectError('Internal server error', Code.Internal),
});
