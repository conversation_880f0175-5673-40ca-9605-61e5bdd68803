import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  AcceptInvestmentApplicationsResponseSchema
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { acceptInvestmentApplications } from '@core-api/repositories/investment_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const acceptInvestmentApplicationsHandler = createHandler({
  schema: z.object({
    acceptances: z.array(z.object({
      horseId: z.number().int().positive(),
      investmentApplicationId: z.number().int().positive(),
      allocatedShares: z.number().int().min(0),
    })),
  }),
  business: ({ acceptances }) => {
    return ResultAsync.fromPromise(
      acceptInvestmentApplications(acceptances),
      (error) => new DatabaseError(`Failed to accept investment applications: ${error}`)
    );
  },
  toResponse: () =>
    create(AcceptInvestmentApplicationsResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 