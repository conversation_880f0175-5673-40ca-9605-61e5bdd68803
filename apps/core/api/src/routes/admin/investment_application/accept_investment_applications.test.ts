import { HorseFactory, MemberFactory, InvestmentApplicationFactory, getClient, AdminUserSessionFactory } from '@core-test/index';
import { InvestmentApplicationService } from '@hami/core-admin-api-schema/investment_application_service_pb';

describe('acceptInvestmentApplicationsHandler', () => {
  it('出資申込に受入口数を設定できる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 100,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member = await MemberFactory.create();

    const application1 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 10,
      allocatedNumber: null,
      rejected: false,
      contracted: false,
    });

    const application2 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 20,
      allocatedNumber: null,
      rejected: false,
      contracted: false,
    });

    // ===== Act =====
    const client = getClient(InvestmentApplicationService);
    const adminSession = await AdminUserSessionFactory.create();

    await client.acceptInvestmentApplications(
      {
        acceptances: [
          {
            horseId: horse.horseId,
            investmentApplicationId: application1.investmentApplicationId,
            allocatedShares: 8,
          },
          {
            horseId: horse.horseId,
            investmentApplicationId: application2.investmentApplicationId,
            allocatedShares: 15,
          },
        ],
      },
      {
        headers: {
          sessionToken: adminSession.sessionToken,
        },
      }
    );

    // ===== Assert =====
    // データベースから更新されたレコードを確認
    const updatedApplication1 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application1.investmentApplicationId },
    });
    expect(updatedApplication1?.allocatedNumber).toBe(8);

    const updatedApplication2 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application2.investmentApplicationId },
    });
    expect(updatedApplication2?.allocatedNumber).toBe(15);
  });

  it('存在しない申込IDでエラーになる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 200,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    // ===== Act =====
    const client = getClient(InvestmentApplicationService);
    const adminSession = await AdminUserSessionFactory.create();

    try {
      await client.acceptInvestmentApplications(
        {
          acceptances: [
            {
              horseId: horse.horseId,
              investmentApplicationId: 99999,
              allocatedShares: 10,
            },
          ],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );
    } catch (error) {
      expect(error).toBeDefined();
    }
  });

  it('既にrejected=trueまたはcontracted=trueの申込は更新されない', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 300,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member = await MemberFactory.create();

    const rejectedApplication = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 10,
      allocatedNumber: null,
      rejected: true,
      contracted: false,
    });

    // ===== Act =====
    const client = getClient(InvestmentApplicationService);
    const adminSession = await AdminUserSessionFactory.create();

    try {
      await client.acceptInvestmentApplications(
        {
          acceptances: [
            {
              horseId: horse.horseId,
              investmentApplicationId: rejectedApplication.investmentApplicationId,
              allocatedShares: 8,
            },
          ],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );
    } catch (error) {
      expect(error).toBeDefined();
    }
  });

  it('0口受入（拒否）を設定できる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 400,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member = await MemberFactory.create();

    const application = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 10,
      allocatedNumber: null,
      rejected: false,
      contracted: false,
    });

    // ===== Act =====
    const client = getClient(InvestmentApplicationService);
    const adminSession = await AdminUserSessionFactory.create();

    await client.acceptInvestmentApplications(
      {
        acceptances: [
          {
            horseId: horse.horseId,
            investmentApplicationId: application.investmentApplicationId,
            allocatedShares: 0, // 0口受入（拒否）
          },
        ],
      },
      {
        headers: {
          sessionToken: adminSession.sessionToken,
        },
      }
    );

    // ===== Assert =====
    // データベースから更新されたレコードを確認
    const updatedApplication = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application.investmentApplicationId },
    });
    expect(updatedApplication?.allocatedNumber).toBe(0);
  });

  it('同時実行でもデッドロックが発生しない', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 500,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member1 = await MemberFactory.create();
    const member2 = await MemberFactory.create();

    const application1 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 10,
      allocatedNumber: null,
      rejected: false,
      contracted: false,
    });

    const application2 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 20,
      allocatedNumber: null,
      rejected: false,
      contracted: false,
    });

    // ===== Act =====
    const client = getClient(InvestmentApplicationService);
    const adminSession = await AdminUserSessionFactory.create();

    // 同時に複数のリクエストを実行（デッドロックテスト）
    const promises = [
      client.acceptInvestmentApplications(
        {
          acceptances: [
            {
              horseId: horse.horseId,
              investmentApplicationId: application1.investmentApplicationId,
              allocatedShares: 8,
            },
          ],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      ),
      client.acceptInvestmentApplications(
        {
          acceptances: [
            {
              horseId: horse.horseId,
              investmentApplicationId: application2.investmentApplicationId,
              allocatedShares: 15,
            },
          ],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      ),
    ];

    // 同時実行してもエラーが発生しないことを確認
    await Promise.all(promises);

    // ===== Assert =====
    const updatedApplication1 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application1.investmentApplicationId },
    });
    expect(updatedApplication1?.allocatedNumber).toBe(8);

    const updatedApplication2 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application2.investmentApplicationId },
    });
    expect(updatedApplication2?.allocatedNumber).toBe(15);
  });
});
