import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  GetInvestmentApplicationHorseDetailResponseSchema 
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { getInvestmentApplicationHorseDetail } from '@core-api/repositories/investment_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getInvestmentApplicationHorseDetailHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
  }),
  business: ({ horseId }) => {
    return ResultAsync.fromPromise(
      getInvestmentApplicationHorseDetail(horseId),
      (error) => new DatabaseError(`Failed to get investment application horse detail: ${error}`)
    );
  },
  toResponse: (horseDetail) =>
    create(GetInvestmentApplicationHorseDetailResponseSchema, {
      horseId: horseDetail.horseId,
      birthYear: horseDetail.birthYear,
      birthMonth: horseDetail.birthMonth,
      birthDay: horseDetail.birthDay,
      recruitmentYear: horseDetail.recruitmentYear,
      recruitmentNo: horseDetail.recruitmentNo,
      horseName: horseDetail.horseName,
      recruitmentName: horseDetail.recruitmentName,
      totalApplicationShares: horseDetail.totalApplicationShares,
      recruitmentShares: horseDetail.recruitmentShares,
      contractedShares: horseDetail.contractedShares,
      recruitmentTotalAmount: horseDetail.recruitmentTotalAmount,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 