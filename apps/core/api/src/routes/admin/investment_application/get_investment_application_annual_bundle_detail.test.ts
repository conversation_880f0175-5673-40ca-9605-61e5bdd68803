import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory, AnnualBundleFactory, AnnualBundleHorseFactory, HorseFactory, MemberFactory, InvestmentApplicationFactory, getClient, prisma } from '@core-test/index';
import { InvestmentApplicationService, GetInvestmentApplicationAnnualBundleDetailRequestSchema } from '@hami/core-admin-api-schema/investment_application_service_pb';
import { AnnualBundleRecruitmentStatus, AnnualBundlePublishStatus } from '@hami/prisma';

describe('getInvestmentApplicationAnnualBundleDetail API', () => {
  let session: Awaited<ReturnType<typeof AdminUserSessionFactory.create>>;

  beforeEach(async () => {
    session = await AdminUserSessionFactory.create();
  });

  it('年度バンドルの出資申込詳細情報を取得できる', async ({ getClient }) => {
    const api = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: session.sessionToken });

    // 年度バンドルと馬を作成
    const annualBundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 10,
      fiscalYear: 2025,
      name: 'テストバンドル',
    });

    const horse1 = await HorseFactory.create({
      sharesTotal: 100,
      amountTotal: 5000000,
    });

    const horse2 = await HorseFactory.create({
      sharesTotal: 200,
      amountTotal: 10000000,
    });

    await AnnualBundleHorseFactory.createList([
      { annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } }, horse: { connect: { horseId: horse1.horseId } } },
      { annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } }, horse: { connect: { horseId: horse2.horseId } } },
    ]);

    // 会員を作成
    const member1 = await MemberFactory.create();
    const member2 = await MemberFactory.create();

    // 出資申込を作成（締結済み）
    const contractedApplication = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member1.memberId } },
      annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } },
      requestedNumber: 3,
      allocatedNumber: 3,
      rejectPartialAllocation: false,
      isWhole: false,
      installmentPayment: false,
      rejected: false,
      contracted: true,
      appliedAt: new Date(),
    });

    // 出資申込を作成（受入済み・未締結）
    const acceptedApplication = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member2.memberId } },
      annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } },
      requestedNumber: 2,
      allocatedNumber: 2,
      rejectPartialAllocation: false,
      isWhole: false,
      installmentPayment: false,
      rejected: false,
      contracted: false,
      appliedAt: new Date(),
    });

    const request = create(GetInvestmentApplicationAnnualBundleDetailRequestSchema, {
      annualBundleId: annualBundle.annualBundleId,
    });

    const response = await api.getInvestmentApplicationAnnualBundleDetail(request, { headers });

    expect(response.annualBundleId).toBe(annualBundle.annualBundleId);
    expect(response.fiscalYear).toBe(2025);
    expect(response.bundleName).toBe('テストバンドル');
    expect(response.recruitmentShares).toBe(10);
    
    // 締結済みの申込口数のみカウント
    expect(response.contractedShares).toBe(3);
    
    // 募集総額は (horse1.amountTotal / horse1.sharesTotal * shares) + (horse2.amountTotal / horse2.sharesTotal * shares)
    // = (5000000 / 100 * 10) + (10000000 / 200 * 10)
    // = 500000 + 500000 = 1000000
    expect(response.recruitmentTotalAmount).toBe(1000000);
  });

  it('出資申込がない場合でも正しく情報を取得できる', async ({ getClient }) => {
    const api = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: session.sessionToken });

    const annualBundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 5,
      fiscalYear: 2024,
      name: '申込なしバンドル',
    });

    const horse = await HorseFactory.create({
      sharesTotal: 100,
      amountTotal: 3000000,
    });

    await AnnualBundleHorseFactory.create({
      annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } },
      horse: { connect: { horseId: horse.horseId } },
    });

    const request = create(GetInvestmentApplicationAnnualBundleDetailRequestSchema, {
      annualBundleId: annualBundle.annualBundleId,
    });

    const response = await api.getInvestmentApplicationAnnualBundleDetail(request, { headers });

    expect(response.annualBundleId).toBe(annualBundle.annualBundleId);
    expect(response.fiscalYear).toBe(2024);
    expect(response.bundleName).toBe('申込なしバンドル');
    expect(response.recruitmentShares).toBe(5);
    expect(response.contractedShares).toBe(0);
    
    // 募集総額は 3000000 / 100 * 5 = 150000
    expect(response.recruitmentTotalAmount).toBe(150000);
  });

  it('退会済み会員の申込は除外される', async ({ getClient }) => {
    const api = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: session.sessionToken });

    const annualBundle = await AnnualBundleFactory.create({
      shares: 10,
    });

    const horse = await HorseFactory.create({
      sharesTotal: 100,
      amountTotal: 5000000,
    });

    await AnnualBundleHorseFactory.create({
      annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } },
      horse: { connect: { horseId: horse.horseId } },
    });

    const activeMember = await MemberFactory.create();
    const retiredMember = await MemberFactory.create({
      retirementDate: new Date('2024-12-31'),
    });

    // 有効会員の申込（締結済み）
    await InvestmentApplicationFactory.create({
      member: { connect: { memberId: activeMember.memberId } },
      annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } },
      requestedNumber: 2,
      allocatedNumber: 2,
      rejectPartialAllocation: false,
      isWhole: false,
      installmentPayment: false,
      rejected: false,
      contracted: true,
      appliedAt: new Date(),
    });

    // 退会済み会員の申込（締結済み）- これは除外される
    await InvestmentApplicationFactory.create({
      member: { connect: { memberId: retiredMember.memberId } },
      annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } },
      requestedNumber: 3,
      allocatedNumber: 3,
      rejectPartialAllocation: false,
      isWhole: false,
      installmentPayment: false,
      rejected: false,
      contracted: true,
      appliedAt: new Date(),
    });

    const request = create(GetInvestmentApplicationAnnualBundleDetailRequestSchema, {
      annualBundleId: annualBundle.annualBundleId,
    });

    const response = await api.getInvestmentApplicationAnnualBundleDetail(request, { headers });

    // 退会済み会員の申込は除外されるので、有効会員の2口のみ
    expect(response.contractedShares).toBe(2);
  });

  it('存在しない年度バンドルIDの場合はエラーになる', async ({ getClient }) => {
    const api = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: session.sessionToken });

    const request = create(GetInvestmentApplicationAnnualBundleDetailRequestSchema, {
      annualBundleId: 99999,
    });

    await expect(async () => {
      await api.getInvestmentApplicationAnnualBundleDetail(request, { headers });
    }).rejects.toThrow();
  });

  it('複数の馬が紐づく場合、募集総額は各馬の単価×口数の合計になる', async ({ getClient }) => {
    const api = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: session.sessionToken });

    const annualBundle = await AnnualBundleFactory.create({
      shares: 20,
    });

    // 馬1: 単価50,000円 × 20口 = 1,000,000円
    const horse1 = await HorseFactory.create({
      sharesTotal: 100,
      amountTotal: 5000000, // 単価50,000円
    });

    // 馬2: 単価60,000円 × 20口 = 1,200,000円
    const horse2 = await HorseFactory.create({
      sharesTotal: 200,
      amountTotal: 12000000, // 単価60,000円
    });

    // 馬3: 単価40,000円 × 20口 = 800,000円
    const horse3 = await HorseFactory.create({
      sharesTotal: 50,
      amountTotal: 2000000, // 単価40,000円
    });

    await AnnualBundleHorseFactory.createList([
      { annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } }, horse: { connect: { horseId: horse1.horseId } } },
      { annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } }, horse: { connect: { horseId: horse2.horseId } } },
      { annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } }, horse: { connect: { horseId: horse3.horseId } } },
    ]);

    const request = create(GetInvestmentApplicationAnnualBundleDetailRequestSchema, {
      annualBundleId: annualBundle.annualBundleId,
    });

    const response = await api.getInvestmentApplicationAnnualBundleDetail(request, { headers });

    // 募集総額 = 1,000,000 + 1,200,000 + 800,000 = 3,000,000
    expect(response.recruitmentTotalAmount).toBe(3000000);
  });
});
