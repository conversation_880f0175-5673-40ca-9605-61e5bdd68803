import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  ListContractTargetApplicationsResponseSchema,
  ContractTargetApplicationItemSchema
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listContractTargetApplications } from '@core-api/repositories/investment_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const listContractTargetApplicationsHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
  }),
  business: ({ horseId }) => {
    return ResultAsync.fromPromise(
      listContractTargetApplications(horseId),
      (error) => new DatabaseError(`Failed to list contract target applications: ${error}`)
    );
  },
  toResponse: (applications) =>
    create(ListContractTargetApplicationsResponseSchema, {
      applications: applications.map((app) =>
        create(ContractTargetApplicationItemSchema, {
          investmentApplicationId: app.investmentApplicationId,
          memberName: app.memberName,
          requestedShares: app.requestedShares,
          allocatedNumber: app.allocatedNumber ?? 0,
        })
      ),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 