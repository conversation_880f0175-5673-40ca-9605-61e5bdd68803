import { create } from '@bufbuild/protobuf';
import { TimestampSchema } from '@bufbuild/protobuf/wkt';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { z } from 'zod';
import {
  ListAnnualBundleInvestmentApplicationsResponseSchema,
  InvestmentApplicationSummarySchema,
  AnnualBundleApplicationDetailSchema,
  MemberSummarySchema,
  InvestmentApplicationStatus,
  InvestmentApplicationType,
  InvestmentApplicationSortField,
  SortOrder,
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import {
  listAnnualBundleInvestmentApplications,
  InvestmentApplicationRecord,
} from '@core-api/repositories/investment_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const statusMap: Record<string, InvestmentApplicationStatus> = {
  applied: InvestmentApplicationStatus.APPLIED,
  accepted: InvestmentApplicationStatus.ACCEPTED,
  contractCompleted: InvestmentApplicationStatus.CONTRACT_COMPLETED,
  rejected: InvestmentApplicationStatus.REJECTED,
};

const toStatus = (record: InvestmentApplicationRecord): InvestmentApplicationStatus => {
  if (record.rejected) {
    return statusMap.rejected;
  }
  if (record.contracted) {
    return statusMap.contractCompleted;
  }
  if ((record.allocatedNumber ?? 0) > 0) {
    return statusMap.accepted;
  }
  return statusMap.applied;
};

const toTimestamp = (date: Date) =>
  create(TimestampSchema, {
    seconds: BigInt(Math.floor(date.getTime() / 1000)),
    nanos: (date.getTime() % 1000) * 1_000_000,
  });

const pluralize = (value?: string | null) => value ?? '';

const toSummary = (record: InvestmentApplicationRecord) =>
  create(InvestmentApplicationSummarySchema, {
    investmentApplicationId: record.investmentApplicationId,
    type: InvestmentApplicationType.ANNUAL_BUNDLE,
    applicationDate: toTimestamp(record.appliedAt),
    member: create(MemberSummarySchema, {
      memberId: record.member.memberId,
      memberNumber: record.member.memberNumber,
      memberName: `${pluralize(record.member.lastName)} ${pluralize(record.member.firstName)}`.trim(),
      memberNameKana: `${pluralize(record.member.lastNameKana)} ${pluralize(record.member.firstNameKana)}`.trim(),
    }),
    requestedShares: record.requestedShares,
    allocatedShares: record.allocatedNumber ?? undefined,
    rejectPartialAllocation: record.rejectPartialAllocation,
    isWhole: record.isWhole,
    installmentPayment: record.installmentPayment,
    status: toStatus(record),
    horseDetail: undefined,
    annualBundleDetail: create(AnnualBundleApplicationDetailSchema, {
      annualBundleId: record.annualBundle?.annualBundleId ?? 0,
      bundleName: pluralize(record.annualBundle?.name),
      fiscalYear: record.annualBundle?.fiscalYear ?? 0,
      shares: record.annualBundle?.shares ?? 0,
    }),
  });

const mapSortField = (value?: number) =>
  value === InvestmentApplicationSortField.SORT_FIELD_MEMBER_NAME_KANA ? 'member_name_kana' : 'applied_at';

const mapSortOrder = (value?: number): 'asc' | 'desc' => (value === SortOrder.ASC ? 'asc' : 'desc');

export const listAnnualBundleInvestmentApplicationsHandler = createHandler({
  schema: z.object({
    annualBundleIds: z.array(z.number().int().positive()).optional().default([]),
    isWhole: z.boolean().optional(),
    sortField: z.number().int().optional(),
    sortOrder: z.number().int().optional(),
  }),
  business: (params) =>
    ResultAsync.fromPromise(
      listAnnualBundleInvestmentApplications({
        annualBundleIds: params.annualBundleIds,
        isWhole: params.isWhole,
        sortBy: mapSortField(params.sortField),
        sortOrder: mapSortOrder(params.sortOrder),
      }),
      (error) => new DatabaseError(`Failed to list annual bundle investment applications: ${error}`),
    ),
  toResponse: (records) =>
    create(ListAnnualBundleInvestmentApplicationsResponseSchema, {
      applications: records.map(toSummary),
    }),
  toError: (error) =>
    error instanceof ValidationError ? new ConnectError(error.message, Code.InvalidArgument) : new ConnectError('Internal server error', Code.Internal),
});
