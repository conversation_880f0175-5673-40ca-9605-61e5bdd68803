import { create } from "@bufbuild/protobuf";
import {
  HorseFactory,
  AnnualBundleFactory,
  MemberFactory,
  InvestmentApplicationFactory,
  getClient,
  AdminUserSessionFactory,
} from "@core-test/index";
import {
  InvestmentApplicationService,
  ListInvestmentApplicationsRequestSchema,
  InvestmentApplicationType,
  InvestmentApplicationSortField,
  SortOrder,
} from "@hami/core-admin-api-schema/investment_application_service_pb";
import { AnnualBundleRecruitmentStatus, AnnualBundlePublishStatus } from "@hami/prisma";

const HORSE_TYPE = InvestmentApplicationType?.HORSE ?? 1;
const ANNUAL_BUNDLE_TYPE = InvestmentApplicationType?.ANNUAL_BUNDLE ?? 2;
const SORT_FIELD_MEMBER_NAME_KANA = InvestmentApplicationSortField?.SORT_FIELD_MEMBER_NAME_KANA ?? 2;
const SORT_ORDER_ASC = SortOrder?.ASC ?? 1;

describe.sequential("listInvestmentApplicationsHandler", () => {
  it("馬申込と年度バンドル申込を混在で取得できる", async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const horse = await HorseFactory.create({
      recruitmentNo: 1010,
      recruitmentYear: 2025,
      sharesTotal: 80,
      amountTotal: 3200000,
      recruitmentName: "混在テスト馬2025",
      horseName: "混在テストホース",
    });

    const annualBundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 500,
    });

    const horseMember = await MemberFactory.create({
      firstName: "太郎",
      lastName: "馬場",
      firstNameKana: "タロウ",
      lastNameKana: "ババ",
    });
    const bundleMember = await MemberFactory.create({
      firstName: "花子",
      lastName: "年度",
      firstNameKana: "ハナコ",
      lastNameKana: "ネンド",
    });

    const horseApplication = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: horseMember.memberId } },
      requestedNumber: 5,
      rejectPartialAllocation: false,
      isWhole: false,
      installmentPayment: false,
      allocatedNumber: null,
      rejected: false,
      contracted: false,
    });

    const bundleApplication = await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: bundleMember.memberId } },
        annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } },
        requestedNumber: 3,
        rejectPartialAllocation: false,
        isWhole: true,
        installmentPayment: false,
        allocatedNumber: null,
        rejected: false,
        contracted: false,
      },
    });

    const request = create(ListInvestmentApplicationsRequestSchema, {});
    const response = await apiClient.listInvestmentApplications(request, { headers });

    const summaries = response.applications;
    const horseSummary = summaries.find((summary) => summary.investmentApplicationId === horseApplication.investmentApplicationId);
    const bundleSummary = summaries.find((summary) => summary.investmentApplicationId === bundleApplication.investmentApplicationId);

    expect(horseSummary).toBeDefined();
    expect(horseSummary?.type).toBe(HORSE_TYPE);
    expect(horseSummary?.requestedShares).toBe(5);
    expect(horseSummary?.horseDetail).toBeDefined();
    expect(horseSummary?.horseDetail?.horseId).toBe(horse.horseId);

    expect(bundleSummary).toBeDefined();
    expect(bundleSummary?.type).toBe(ANNUAL_BUNDLE_TYPE);
    expect(bundleSummary?.isWhole).toBe(true);
    expect(bundleSummary?.annualBundleDetail).toBeDefined();
    expect(bundleSummary?.annualBundleDetail?.annualBundleId).toBe(annualBundle.annualBundleId);
  });

  it("typeFilter=HORSE で馬申込のみ取得できる", async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const horse = await HorseFactory.create({
      recruitmentNo: 2020,
      recruitmentYear: 2025,
      sharesTotal: 60,
      amountTotal: 2400000,
      recruitmentName: "馬フィルタテスト2025",
      horseName: "馬フィルタホース",
    });

    const otherHorse = await HorseFactory.create({
      recruitmentNo: 2021,
      recruitmentYear: 2025,
    });

    const member = await MemberFactory.create({
      firstName: "一郎",
      lastName: "馬場",
      firstNameKana: "イチロウ",
      lastNameKana: "ババ",
    });

    const otherMember = await MemberFactory.create();

    const targetApplication = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 7,
      rejected: false,
      contracted: false,
      allocatedNumber: null,
    });

    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: otherHorse.horseId } },
      member: { connect: { memberId: otherMember.memberId } },
      requestedNumber: 4,
      rejected: false,
      contracted: false,
      allocatedNumber: null,
    });

    const request = create(ListInvestmentApplicationsRequestSchema, {
      typeFilter: HORSE_TYPE,
      horseIds: [horse.horseId],
    });

    const response = await apiClient.listInvestmentApplications(request, { headers });

    expect(response.applications.length).toBeGreaterThanOrEqual(1);
    response.applications.forEach((summary) => {
      expect(summary.type).toBe(HORSE_TYPE);
      expect(summary.horseDetail).toBeDefined();
    });

    const targetSummary = response.applications.find((summary) => summary.investmentApplicationId === targetApplication.investmentApplicationId);
    expect(targetSummary).toBeDefined();
  });

  it("年度バンドルIDとisWholeでフィルタできる", async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const bundleA = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 300,
    });
    const bundleB = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 400,
    });

    const memberA = await MemberFactory.create({
      firstName: "咲良",
      lastName: "青木",
      firstNameKana: "サクラ",
      lastNameKana: "アオキ",
    });
    const memberB = await MemberFactory.create({
      firstName: "徹",
      lastName: "伊藤",
      firstNameKana: "トオル",
      lastNameKana: "イトウ",
    });

    const isWholeApplication = await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: memberA.memberId } },
        annualBundle: { connect: { annualBundleId: bundleA.annualBundleId } },
        requestedNumber: 2,
        rejectPartialAllocation: false,
        isWhole: true,
        installmentPayment: false,
        allocatedNumber: null,
        rejected: false,
        contracted: false,
      },
    });

    await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: memberB.memberId } },
        annualBundle: { connect: { annualBundleId: bundleA.annualBundleId } },
        requestedNumber: 4,
        rejectPartialAllocation: false,
        isWhole: false,
        installmentPayment: false,
        allocatedNumber: null,
        rejected: false,
        contracted: false,
      },
    });

    await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: memberA.memberId } },
        annualBundle: { connect: { annualBundleId: bundleB.annualBundleId } },
        requestedNumber: 5,
        rejectPartialAllocation: false,
        isWhole: true,
        installmentPayment: false,
        allocatedNumber: null,
        rejected: false,
        contracted: false,
      },
    });

    const request = create(ListInvestmentApplicationsRequestSchema, {
      typeFilter: ANNUAL_BUNDLE_TYPE,
      annualBundleIds: [bundleA.annualBundleId],
      isWhole: true,
      sortField: SORT_FIELD_MEMBER_NAME_KANA,
      sortOrder: SORT_ORDER_ASC,
    });

    const response = await apiClient.listInvestmentApplications(request, { headers });

    expect(response.applications.length).toBe(1);
    const summary = response.applications[0];
    expect(summary.investmentApplicationId).toBe(isWholeApplication.investmentApplicationId);
    expect(summary.isWhole).toBe(true);
    expect(summary.annualBundleDetail).toBeDefined();
  });
});
