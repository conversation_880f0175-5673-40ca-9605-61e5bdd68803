import { create } from '@bufbuild/protobuf';
import { TimestampSchema } from '@bufbuild/protobuf/wkt';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  ListInvestmentApplicationsByHorseResponseSchema,
  InvestmentApplicationItemSchema,
  SortOrder,
  InvestmentApplicationSortField,
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listInvestmentApplicationsByHorse } from '@core-api/repositories/investment_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const listInvestmentApplicationsByHorseHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
    isWhole: z.boolean().optional(),
    // コード生成前でも動作するように数値で受け取りローカルマッピング
    sortField: z.number().int().optional(), // 1: APPLIED_AT, 2: MEMBER_NAME_KANA
    sortOrder: z.number().int().optional(), // 1: ASC, 2: DESC
  }),
  business: ({ horseId, isWhole, sortField, sortOrder }) => {
    const validatedSortOrder = sortOrder === SortOrder.ASC ? 'asc' : 'desc';
    const validatedSortBy = sortField === InvestmentApplicationSortField.SORT_FIELD_MEMBER_NAME_KANA ? 'member_name_kana' : 'applied_at';

    return ResultAsync.fromPromise(
      listInvestmentApplicationsByHorse({ horseId, isWhole, sortBy: validatedSortBy, sortOrder: validatedSortOrder }),
      (error) => new DatabaseError(`Failed to list investment applications by horse: ${error}`)
    );
  },
  toResponse: (applications) =>
    create(ListInvestmentApplicationsByHorseResponseSchema, {
      applications: applications.map((app) =>
        create(InvestmentApplicationItemSchema, {
          investmentApplicationId: app.investmentApplicationId,
          applicationDate: create(TimestampSchema, { seconds: BigInt(Math.floor(app.applicationDate.getTime() / 1000)), nanos: (app.applicationDate.getTime() % 1000) * 1e6 }),
          memberName: app.memberName,
          memberNameKana: app.memberNameKana,
          memberId: app.memberId,
          memberNumber: app.memberNumber,
          requestedShares: app.requestedShares,
          rejectPartialAllocation: app.rejectPartialAllocation,
          isWhole: app.isWhole,
          allocatedShares: app.allocatedShares ?? undefined,
        })
      ),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 