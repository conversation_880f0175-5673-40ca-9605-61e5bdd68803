import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  ListInvestmentApplicationHorsesResponseSchema, 
  InvestmentApplicationHorseItemSchema 
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listInvestmentApplicationHorses } from '@core-api/repositories/investment_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const listInvestmentApplicationHorsesHandler = createHandler({
  schema: z.object({}),
  business: () => {
    return ResultAsync.fromPromise(
      listInvestmentApplicationHorses(),
      (error) => new DatabaseError(`Failed to list investment application horses: ${error}`)
    );
  },
  toResponse: (horses) =>
    create(ListInvestmentApplicationHorsesResponseSchema, {
      horses: horses.map((horse) =>
        create(InvestmentApplicationHorseItemSchema, {
          horseId: horse.horseId,
          recruitmentYear: horse.recruitmentYear,
          recruitmentNo: horse.recruitmentNo,
          horseName: horse.horseName,
          recruitmentName: horse.recruitmentName,
          totalApplicationShares: horse.totalApplicationShares,
          recruitmentShares: horse.recruitmentShares,
          contractedShares: horse.contractedShares,
        })
      ),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 