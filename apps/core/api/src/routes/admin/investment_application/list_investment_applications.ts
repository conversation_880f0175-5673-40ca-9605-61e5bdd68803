import { create } from '@bufbuild/protobuf';
import { TimestampSchema } from '@bufbuild/protobuf/wkt';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { z } from 'zod';
import {
  InvestmentApplicationSummarySchema,
  HorseApplicationDetailSchema,
  AnnualBundleApplicationDetailSchema,
  MemberSummarySchema,
  ListInvestmentApplicationsResponseSchema,
  InvestmentApplicationType,
  InvestmentApplicationSortField,
  SortOrder,
  InvestmentApplicationStatus,
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import {
  listInvestmentApplications,
  InvestmentApplicationRecord,
  InvestmentApplicationTypeFilter,
  InvestmentApplicationSortKey,
} from '@core-api/repositories/investment_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const statusMap: Record<string, InvestmentApplicationStatus> = {
  applied: InvestmentApplicationStatus.APPLIED,
  accepted: InvestmentApplicationStatus.ACCEPTED,
  contractCompleted: InvestmentApplicationStatus.CONTRACT_COMPLETED,
  rejected: InvestmentApplicationStatus.REJECTED,
};

const toStatus = (record: InvestmentApplicationRecord): InvestmentApplicationStatus => {
  if (record.rejected) {
    return statusMap.rejected;
  }
  if (record.contracted) {
    return statusMap.contractCompleted;
  }
  if ((record.allocatedNumber ?? 0) > 0) {
    return statusMap.accepted;
  }
  return statusMap.applied;
};

const toTimestamp = (date: Date) =>
  create(TimestampSchema, {
    seconds: BigInt(Math.floor(date.getTime() / 1000)),
    nanos: (date.getTime() % 1000) * 1_000_000,
  });

const pluralize = (value?: string | null) => value ?? '';

const toSummary = (record: InvestmentApplicationRecord) =>
  create(InvestmentApplicationSummarySchema, {
    investmentApplicationId: record.investmentApplicationId,
    type:
      record.type === 'horse'
        ? InvestmentApplicationType.HORSE
        : InvestmentApplicationType.ANNUAL_BUNDLE,
    applicationDate: toTimestamp(record.appliedAt),
    member: create(MemberSummarySchema, {
      memberId: record.member.memberId,
      memberNumber: record.member.memberNumber,
      memberName: `${pluralize(record.member.lastName)} ${pluralize(record.member.firstName)}`.trim(),
      memberNameKana: `${pluralize(record.member.lastNameKana)} ${pluralize(record.member.firstNameKana)}`.trim(),
    }),
    requestedShares: record.requestedShares,
    allocatedShares: record.allocatedNumber ?? undefined,
    rejectPartialAllocation: record.rejectPartialAllocation,
    isWhole: record.isWhole,
    installmentPayment: record.installmentPayment,
    status: toStatus(record),
    horseDetail:
      record.type === 'horse'
        ? create(HorseApplicationDetailSchema, {
            horseId: record.horse?.horseId ?? 0,
            recruitmentYear: record.horse?.recruitmentYear ?? 0,
            recruitmentNo: record.horse?.recruitmentNo ?? 0,
            horseName: pluralize(record.horse?.horseName),
            recruitmentName: pluralize(record.horse?.recruitmentName),
          })
        : undefined,
    annualBundleDetail:
      record.type === 'annual_bundle'
        ? create(AnnualBundleApplicationDetailSchema, {
            annualBundleId: record.annualBundle?.annualBundleId ?? 0,
            bundleName: pluralize(record.annualBundle?.name),
            fiscalYear: record.annualBundle?.fiscalYear ?? 0,
            shares: record.annualBundle?.shares ?? 0,
          })
        : undefined,
  });

const mapTypeFilter = (value?: number): InvestmentApplicationTypeFilter | undefined => {
  if (value === InvestmentApplicationType.HORSE) {
    return 'horse';
  }
  if (value === InvestmentApplicationType.ANNUAL_BUNDLE) {
    return 'annual_bundle';
  }
  return undefined;
};

const mapSortField = (value?: number): InvestmentApplicationSortKey =>
  value === InvestmentApplicationSortField.SORT_FIELD_MEMBER_NAME_KANA ? 'member_name_kana' : 'applied_at';

const mapSortOrder = (value?: number): 'asc' | 'desc' => (value === SortOrder.ASC ? 'asc' : 'desc');

export const listInvestmentApplicationsHandler = createHandler({
  schema: z.object({
    typeFilter: z.number().int().optional(),
    horseIds: z.array(z.number().int().positive()).optional().default([]),
    annualBundleIds: z.array(z.number().int().positive()).optional().default([]),
    sortField: z.number().int().optional(),
    sortOrder: z.number().int().optional(),
    isWhole: z.boolean().optional(),
  }),
  business: (params) =>
    ResultAsync.fromPromise(
      listInvestmentApplications({
        typeFilter: mapTypeFilter(params.typeFilter),
        horseIds: params.horseIds,
        annualBundleIds: params.annualBundleIds,
        isWhole: params.isWhole,
        sortBy: mapSortField(params.sortField),
        sortOrder: mapSortOrder(params.sortOrder),
      }),
      (error) => new DatabaseError(`Failed to list investment applications: ${error}`),
    ),
  toResponse: (records) =>
    create(ListInvestmentApplicationsResponseSchema, {
      applications: records.map(toSummary),
    }),
  toError: (error) =>
    error instanceof ValidationError ? new ConnectError(error.message, Code.InvalidArgument) : new ConnectError('Internal server error', Code.Internal),
});
