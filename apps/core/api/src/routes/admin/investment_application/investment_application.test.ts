import { create } from '@bufbuild/protobuf';
import { HorseFactory, MemberFactory, InvestmentApplicationFactory, AnnualBundleFactory, AdminUserSessionFactory, getClient } from '@core-test/index';
import {
  InvestmentApplicationService,
  AcceptInvestmentApplicationsRequestSchema,
  AcceptAnnualBundleInvestmentApplicationsRequestSchema,
  CompleteInvestmentContractsRequestSchema,
  CompleteAnnualBundleInvestmentApplicationsRequestSchema,
  GetInvestmentApplicationHorseDetailRequestSchema,
  ListContractTargetApplicationsRequestSchema,
  ListInvestmentApplicationHorsesRequestSchema,
  ListInvestmentApplicationsRequestSchema,
  ListAnnualBundleInvestmentApplicationsRequestSchema,
  InvestmentApplicationStatus,
  InvestmentApplicationType,
} from '@hami/core-admin-api-schema/investment_application_service_pb';
import { InvestmentContractStatus, AnnualBundleRecruitmentStatus, AnnualBundlePublishStatus } from '@hami/prisma';

describe('InvestmentApplication APIs Integration', () => {
  const apiClient = getClient(InvestmentApplicationService);

  it('出資申込管理の複雑なフローが正常に動作する（複数馬・複数申込者・複数回受入）', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== 1. テストデータ準備（3頭の馬、6人の申込者） =====

    // 馬1: 人気馬（申込多数）
    const horse1 = await HorseFactory.create({
      recruitmentNo: 1001,
      recruitmentYear: 2025,
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 5000000,
      recruitmentName: '人気馬の2025',
      horseName: '人気ホース',
    });

    // 馬2: 中程度の人気
    const horse2 = await HorseFactory.create({
      recruitmentNo: 1002,
      recruitmentYear: 2025,
      birthYear: 2021,
      birthMonth: 6,
      birthDay: 10,
      sharesTotal: 80,
      amountTotal: 3200000,
      recruitmentName: '中堅馬の2025',
      horseName: '中堅ホース',
    });

    // 馬3: 新馬（申込少数）
    const horse3 = await HorseFactory.create({
      recruitmentNo: 1003,
      recruitmentYear: 2025,
      birthYear: 2022,
      birthMonth: 3,
      birthDay: 20,
      sharesTotal: 60,
      amountTotal: 1800000,
      recruitmentName: '新馬の2025',
      horseName: '新ホース',
    });

    // 申込者6人
    const members = await Promise.all([
      MemberFactory.create({ firstName: '太郎', lastName: '田中' }),
      MemberFactory.create({ firstName: '花子', lastName: '佐藤' }),
      MemberFactory.create({ firstName: '次郎', lastName: '鈴木' }),
      MemberFactory.create({ firstName: '美咲', lastName: '高橋' }),
      MemberFactory.create({ firstName: '健一', lastName: '渡辺' }),
      MemberFactory.create({ firstName: '由美', lastName: '山田' }),
    ]);

    // 出資申込作成（複雑なパターン）
    const applications = await Promise.all([
      // 馬1への申込（人気馬のため申込多数）
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[0].memberId } },
        horse: { connect: { horseId: horse1.horseId } },
        requestedNumber: 30,
        contracted: false,
      }),
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[1].memberId } },
        horse: { connect: { horseId: horse1.horseId } },
        requestedNumber: 25,
        contracted: false,
      }),
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[2].memberId } },
        horse: { connect: { horseId: horse1.horseId } },
        requestedNumber: 40,
        contracted: false,
      }),
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[3].memberId } },
        horse: { connect: { horseId: horse1.horseId } },
        requestedNumber: 20,
        contracted: false,
      }),
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[4].memberId } },
        horse: { connect: { horseId: horse1.horseId } },
        requestedNumber: 15,
        contracted: false,
      }),

      // 馬2への申込（中程度）
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[1].memberId } },
        horse: { connect: { horseId: horse2.horseId } },
        requestedNumber: 25,
        contracted: false,
      }),
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[2].memberId } },
        horse: { connect: { horseId: horse2.horseId } },
        requestedNumber: 30,
        contracted: false,
      }),
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[4].memberId } },
        horse: { connect: { horseId: horse2.horseId } },
        requestedNumber: 20,
        contracted: false,
      }),

      // 馬3への申込（少数）
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[3].memberId } },
        horse: { connect: { horseId: horse3.horseId } },
        requestedNumber: 15,
        contracted: false,
      }),
      InvestmentApplicationFactory.create({
        member: { connect: { memberId: members[5].memberId } },
        horse: { connect: { horseId: horse3.horseId } },
        requestedNumber: 10,
        contracted: false,
      }),
    ]);

    // ===== 2. 初期状態の確認 =====
    const initialHorsesRequest = create(ListInvestmentApplicationHorsesRequestSchema, {});
    const initialHorsesResponse = await apiClient.listInvestmentApplicationHorses(initialHorsesRequest, { headers });

    const horses = initialHorsesResponse.horses;
    expect(horses.length).toBe(3);

    const horse1Data = horses.find((h: { horseId: number }) => h.horseId === horse1.horseId);
    expect(horse1Data?.totalApplicationShares).toBe(130); // 30+25+40+20+15

    const horse2Data = horses.find((h: { horseId: number }) => h.horseId === horse2.horseId);
    expect(horse2Data?.totalApplicationShares).toBe(75); // 25+30+20

    const horse3Data = horses.find((h: { horseId: number }) => h.horseId === horse3.horseId);
    expect(horse3Data?.totalApplicationShares).toBe(25); // 15+10

    // ===== 3. 第1回受入処理（馬1のみ、部分的受入） =====
    const firstAcceptRequest = create(AcceptInvestmentApplicationsRequestSchema, {
      acceptances: [
        {
          horseId: horse1.horseId,
          investmentApplicationId: applications[0].investmentApplicationId, // 田中太郎: 30口 → 0口
          allocatedShares: 0,
        },
        {
          horseId: horse1.horseId,
          investmentApplicationId: applications[1].investmentApplicationId, // 佐藤花子: 25口 → 20口
          allocatedShares: 20,
        },
        {
          horseId: horse1.horseId,
          investmentApplicationId: applications[2].investmentApplicationId, // 鈴木次郎: 40口 → 15口
          allocatedShares: 15,
        },
      ],
    });
    const firstAcceptResponse = await apiClient.acceptInvestmentApplications(firstAcceptRequest, { headers });
    expect(firstAcceptResponse).toBeDefined();

    // ===== 4. 第1回契約締結（馬1の一部のみ） =====
    const firstContractRequest = create(CompleteInvestmentContractsRequestSchema, {
      horseId: horse1.horseId,
      investmentApplicationIds: [
        applications[1].investmentApplicationId, // 佐藤花子: 20口
      ],
    });
    const firstContractResponse = await apiClient.completeInvestmentContracts(firstContractRequest, { headers });
    expect(firstContractResponse).toBeDefined();

    // ===== 5. 最終状態確認 =====
    const finalHorse1DetailRequest = create(GetInvestmentApplicationHorseDetailRequestSchema, {
      horseId: horse1.horseId,
    });
    const finalHorse1DetailResponse = await apiClient.getInvestmentApplicationHorseDetail(finalHorse1DetailRequest, { headers });
    expect(finalHorse1DetailResponse).toBeDefined();
    expect(finalHorse1DetailResponse.contractedShares).toBe(20);

    // データベースで契約状態確認
    const contractedApplications = await vPrisma.client.investmentApplication.findMany({
      where: {
        horseId: horse1.horseId,
        contracted: true,
      },
    });
    expect(contractedApplications.length).toBe(1);
    expect(contractedApplications[0].memberId).toBe(members[1].memberId);
  });

  it('複数回の受入処理と部分的な契約締結のエッジケース', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 2001,
      recruitmentYear: 2025,
      sharesTotal: 50,
      amountTotal: 2500000,
    });

    const member = await MemberFactory.create();
    const application = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      horse: { connect: { horseId: horse.horseId } },
      requestedNumber: 30,
      contracted: false,
    });

    // ===== Act & Assert =====
    // 第1回受入: 10口
    const firstAcceptRequest = create(AcceptInvestmentApplicationsRequestSchema, {
      acceptances: [
        {
          horseId: horse.horseId,
          investmentApplicationId: application.investmentApplicationId,
          allocatedShares: 10,
        },
      ],
    });
    await apiClient.acceptInvestmentApplications(firstAcceptRequest, { headers });

    // 第2回受入: 20口に増加
    const secondAcceptRequest = create(AcceptInvestmentApplicationsRequestSchema, {
      acceptances: [
        {
          horseId: horse.horseId,
          investmentApplicationId: application.investmentApplicationId,
          allocatedShares: 20,
        },
      ],
    });
    await apiClient.acceptInvestmentApplications(secondAcceptRequest, { headers });

    // 第3回受入: 5口に減少
    const thirdAcceptRequest = create(AcceptInvestmentApplicationsRequestSchema, {
      acceptances: [
        {
          horseId: horse.horseId,
          investmentApplicationId: application.investmentApplicationId,
          allocatedShares: 5,
        },
      ],
    });
    await apiClient.acceptInvestmentApplications(thirdAcceptRequest, { headers });

    // 最終的に5口で契約締結
    const contractRequest = create(CompleteInvestmentContractsRequestSchema, {
      horseId: horse.horseId,
      investmentApplicationIds: [application.investmentApplicationId],
    });
    const contractResponse = await apiClient.completeInvestmentContracts(contractRequest, { headers });
    expect(contractResponse).toBeDefined();

    // データベースで確認
    const finalApplication = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application.investmentApplicationId },
    });
    expect(finalApplication?.allocatedNumber).toBe(5);
    expect(finalApplication?.contracted).toBe(true);
  });

  it('契約口数制限のエラーハンドリングが正常に動作する', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 3001,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 5000000,
    });

    const member = await MemberFactory.create();
    const application = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      horse: { connect: { horseId: horse.horseId } },
      requestedNumber: 150, // 総口数を超える申込
      allocatedNumber: 150,
      contracted: false,
    });

    // ===== Act & Assert =====
    const contractRequest = create(CompleteInvestmentContractsRequestSchema, {
      horseId: horse.horseId,
      investmentApplicationIds: [application.investmentApplicationId],
    });

    // 契約口数制限エラーが発生することを確認
    await expect(apiClient.completeInvestmentContracts(contractRequest)).rejects.toThrow();
  });

  it('複数馬同時契約での契約口数制限テスト', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const horse1 = await HorseFactory.create({
      recruitmentNo: 4001,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 5000000,
    });

    const horse2 = await HorseFactory.create({
      recruitmentNo: 4002,
      recruitmentYear: 2025,
      sharesTotal: 80,
      amountTotal: 4000000,
    });

    const member = await MemberFactory.create();

    // 既存契約を作成（制限に近い状態）
    const existingApp1 = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      horse: { connect: { horseId: horse1.horseId } },
      requestedNumber: 90,
      allocatedNumber: 90,
      contracted: false,
    });

    const acceptRequest = create(AcceptInvestmentApplicationsRequestSchema, {
      acceptances: [
        {
          horseId: horse1.horseId,
          investmentApplicationId: existingApp1.investmentApplicationId,
          allocatedShares: 90,
        },
      ],
    });
    await apiClient.acceptInvestmentApplications(acceptRequest, { headers });

    const contractRequest1 = create(CompleteInvestmentContractsRequestSchema, {
      horseId: horse1.horseId,
      investmentApplicationIds: [existingApp1.investmentApplicationId],
    });
    await apiClient.completeInvestmentContracts(contractRequest1, { headers });

    // 新しい申込
    const newApp2 = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      horse: { connect: { horseId: horse2.horseId } },
      requestedNumber: 70,
      allocatedNumber: 70,
      contracted: false,
    });

    const acceptRequest2 = create(AcceptInvestmentApplicationsRequestSchema, {
      acceptances: [
        {
          horseId: horse2.horseId,
          investmentApplicationId: newApp2.investmentApplicationId,
          allocatedShares: 70,
        },
      ],
    });
    await apiClient.acceptInvestmentApplications(acceptRequest2, { headers });

    // ===== Act & Assert =====
    const contractRequest2 = create(CompleteInvestmentContractsRequestSchema, {
      horseId: horse2.horseId,
      investmentApplicationIds: [newApp2.investmentApplicationId],
    });
    const contractResponse = await apiClient.completeInvestmentContracts(contractRequest2, { headers });
    expect(contractResponse).toBeDefined();

    // データベースで確認
    const contracts = await vPrisma.client.investmentApplication.findMany({
      where: {
        memberId: member.memberId,
        contracted: true,
      },
    });
    expect(contracts.length).toBe(2);
  });

  it('HorseInvestmentSharesが存在しない場合でも正しく契約が作成される', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 5001,
      recruitmentYear: 2025,
      sharesTotal: 60,
      amountTotal: 3000000,
    });

    const member = await MemberFactory.create();
    const application = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      horse: { connect: { horseId: horse.horseId } },
      requestedNumber: 30,
      allocatedNumber: 30,
      contracted: false,
    });

    // HorseInvestmentSharesが存在しないことを確認
    const initialShares = await vPrisma.client.horseInvestmentShares.findUnique({
      where: {
        horseId: horse.horseId,
      },
    });
    expect(initialShares).toBeNull();

    // ===== Act =====
    const acceptRequest = create(AcceptInvestmentApplicationsRequestSchema, {
      acceptances: [
        {
          horseId: horse.horseId,
          investmentApplicationId: application.investmentApplicationId,
          allocatedShares: 30,
        },
      ],
    });
    await apiClient.acceptInvestmentApplications(acceptRequest, { headers });

    const contractRequest = create(CompleteInvestmentContractsRequestSchema, {
      horseId: horse.horseId,
      investmentApplicationIds: [application.investmentApplicationId],
    });
    const contractResponse = await apiClient.completeInvestmentContracts(contractRequest, { headers });
    expect(contractResponse).toBeDefined();

    // ===== Assert =====
    // HorseInvestmentSharesが正しく作成されたことを確認
    const finalShares = await vPrisma.client.horseInvestmentShares.findUnique({
      where: {
        horseId: horse.horseId,
      },
    });
    expect(finalShares).toBeDefined();
    expect(finalShares?.memberInvestmentShares).toBe(30);

    // 契約状態の確認
    const finalApplication = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application.investmentApplicationId },
    });
    expect(finalApplication?.contracted).toBe(true);
  });

  it('年度バンドル出資申込APIが一連の操作を提供する', async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const annualBundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 500,
    });
    const bundleHorses = await HorseFactory.createList([
      { sharesTotal: 120, amountTotal: 600000 },
      { sharesTotal: 150, amountTotal: 900000 },
    ]);
    await vPrisma.client.annualBundleHorse.createMany({
      data: bundleHorses.map((horse) => ({ annualBundleId: annualBundle.annualBundleId, horseId: horse.horseId })),
      skipDuplicates: true,
    });
    const member = await MemberFactory.create({ firstName: '悠斗', lastName: '斎藤' });

    const bundleApplication = await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: member.memberId } },
        annualBundle: { connect: { annualBundleId: annualBundle.annualBundleId } },
        requestedNumber: 5,
        rejectPartialAllocation: false,
        isWhole: false,
        installmentPayment: false,
        appliedAt: new Date(),
      },
    });

    // 馬向け申込も用意して、混在一覧で両タイプが取得できることを確認する
    await InvestmentApplicationFactory.create();

    const listAllRequest = create(ListInvestmentApplicationsRequestSchema, {
      horseIds: [],
      annualBundleIds: [],
    });
    const listAllResponse = await apiClient.listInvestmentApplications(listAllRequest, { headers });
    expect(
      listAllResponse.applications.some(
        (app) => app.type === InvestmentApplicationType.ANNUAL_BUNDLE,
      ),
    ).toBe(true);
    expect(
      listAllResponse.applications.some(
        (app) => app.type === InvestmentApplicationType.HORSE,
      ),
    ).toBe(true);

    const listBundleRequest = create(ListAnnualBundleInvestmentApplicationsRequestSchema, {
      annualBundleIds: [annualBundle.annualBundleId],
    });
    const listBundleResponse = await apiClient.listAnnualBundleInvestmentApplications(listBundleRequest, { headers });
    const bundleSummary = listBundleResponse.applications.find(
      (app) => app.investmentApplicationId === bundleApplication.investmentApplicationId,
    );
    expect(bundleSummary).toBeDefined();
    expect(bundleSummary?.status).toBe(InvestmentApplicationStatus.APPLIED);
    expect(bundleSummary?.annualBundleDetail).toBeDefined();

    const acceptRequest = create(AcceptAnnualBundleInvestmentApplicationsRequestSchema, {
      commands: [
        {
          annualBundleId: annualBundle.annualBundleId,
          investmentApplicationId: bundleApplication.investmentApplicationId,
          allocatedShares: 3,
        },
      ],
    });
    const acceptResponse = await apiClient.acceptAnnualBundleInvestmentApplications(acceptRequest, { headers });
    expect(acceptResponse.results[0]?.success).toBe(true);
    expect(acceptResponse.results[0]?.allocatedShares).toBe(3);
    expect(acceptResponse.results[0]?.status).toBe(InvestmentApplicationStatus.ACCEPTED);

    const accepted = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: bundleApplication.investmentApplicationId },
    });
    expect(accepted?.allocatedNumber).toBe(3);

    const completeRequest = create(CompleteAnnualBundleInvestmentApplicationsRequestSchema, {
      commands: [
        {
          annualBundleId: annualBundle.annualBundleId,
          investmentApplicationId: bundleApplication.investmentApplicationId,
        },
      ],
    });
    const completeResponse = await apiClient.completeAnnualBundleInvestmentApplications(completeRequest, { headers });
    expect(completeResponse.results[0]?.success).toBe(true);
    expect(completeResponse.results[0]?.contractedShares).toBe(3);
    expect(completeResponse.results[0]?.status).toBe(InvestmentApplicationStatus.CONTRACT_COMPLETED);


    const bundleContracts = await vPrisma.client.investmentContract.findMany({
      where: { memberId: member.memberId },
    });
    expect(bundleContracts.map((contract) => contract.horseId).sort()).toEqual(bundleHorses.map((horse) => horse.horseId).sort());
  });

});
