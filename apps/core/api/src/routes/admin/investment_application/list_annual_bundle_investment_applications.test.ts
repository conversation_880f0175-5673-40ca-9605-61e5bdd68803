import { create } from "@bufbuild/protobuf";
import {
  AnnualBundleFactory,
  MemberFactory,
  getClient,
  AdminUserSessionFactory,
} from "@core-test/index";
import {
  InvestmentApplicationService,
  ListAnnualBundleInvestmentApplicationsRequestSchema,
  InvestmentApplicationStatus,
  InvestmentApplicationSortField,
  SortOrder,
} from "@hami/core-admin-api-schema/investment_application_service_pb";
import { AnnualBundleRecruitmentStatus, AnnualBundlePublishStatus } from "@hami/prisma";

const STATUS_APPLIED = InvestmentApplicationStatus?.APPLIED ?? 1;
const SORT_FIELD_MEMBER_NAME_KANA = InvestmentApplicationSortField?.SORT_FIELD_MEMBER_NAME_KANA ?? 2;
const SORT_ORDER_ASC = SortOrder?.ASC ?? 1;

describe.sequential("listAnnualBundleInvestmentApplicationsHandler", () => {
  it("年度バンドル申込の一覧を取得できる", async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const bundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 600,
      name: "年度バンドルA",
    });

    const member = await MemberFactory.create({
      firstName: "瑞希",
      lastName: "青山",
      firstNameKana: "ミズキ",
      lastNameKana: "アオヤマ",
    });

    const application = await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: member.memberId } },
        annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
        requestedNumber: 8,
        rejectPartialAllocation: true,
        isWhole: false,
        installmentPayment: false,
        allocatedNumber: null,
        rejected: false,
        contracted: false,
      },
    });

    const request = create(ListAnnualBundleInvestmentApplicationsRequestSchema, {
      annualBundleIds: [bundle.annualBundleId],
    });

    const response = await apiClient.listAnnualBundleInvestmentApplications(request, { headers });

    expect(response.applications.length).toBeGreaterThanOrEqual(1);
    const summary = response.applications.find((app) => app.investmentApplicationId === application.investmentApplicationId);
    expect(summary).toBeDefined();
    expect(summary?.status).toBe(STATUS_APPLIED);
    expect(summary?.annualBundleDetail).toBeDefined();
    expect(summary?.annualBundleDetail?.annualBundleId).toBe(bundle.annualBundleId);
    expect(summary?.rejectPartialAllocation).toBe(true);
  });

  it("年度バンドルID・isWhole・ソート条件で絞り込める", async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const bundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 400,
      name: "年度バンドルB",
    });

    const member1 = await MemberFactory.create({
      firstName: "杏奈",
      lastName: "伊勢",
      firstNameKana: "アンナ",
      lastNameKana: "イセ",
    });
    const member2 = await MemberFactory.create({
      firstName: "佳奈",
      lastName: "江口",
      firstNameKana: "カナ",
      lastNameKana: "エグチ",
    });

    const isWholeApplication = await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: member1.memberId } },
        annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
        requestedNumber: 10,
        rejectPartialAllocation: false,
        isWhole: true,
        installmentPayment: false,
        allocatedNumber: null,
        rejected: false,
        contracted: false,
      },
    });

    await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: member2.memberId } },
        annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
        requestedNumber: 6,
        rejectPartialAllocation: false,
        isWhole: false,
        installmentPayment: false,
        allocatedNumber: null,
        rejected: false,
        contracted: false,
      },
    });

    const request = create(ListAnnualBundleInvestmentApplicationsRequestSchema, {
      annualBundleIds: [bundle.annualBundleId],
      isWhole: true,
      sortField: SORT_FIELD_MEMBER_NAME_KANA,
      sortOrder: SORT_ORDER_ASC,
    });

    const response = await apiClient.listAnnualBundleInvestmentApplications(request, { headers });

    expect(response.applications.length).toBe(1);
    const summary = response.applications[0];
    expect(summary.investmentApplicationId).toBe(isWholeApplication.investmentApplicationId);
    expect(summary.isWhole).toBe(true);
  });
});
