import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { CompleteInvestmentContractsResponseSchema } from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { completeInvestmentContractsUsecase } from '@core-api/usecases/complete_investment_contract_usecase';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const completeInvestmentContractsHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
    investmentApplicationIds: z.array(z.number().int().positive()),
  }),
  business: ({ horseId, investmentApplicationIds }) => {
    return ResultAsync.fromPromise(completeInvestmentContractsUsecase(horseId, investmentApplicationIds, new Date()), (error) => {
      if (error instanceof Error && error.message.includes('Contract shares limit exceeded')) {
        return new ValidationError('Total shares would exceed recruitment limit');
      }
      if (error instanceof Error && error.message.includes('Some applications are not eligible for contract completion')) {
        return new ValidationError(error.message);
      }
      if (error instanceof Error && error.message.includes('Horse not found')) {
        return new ValidationError('Some applications are not eligible for contract completion or do not belong to the specified horse');
      }
      return new DatabaseError(`Failed to complete investment contracts: ${error}`);
    });
  },
  toResponse: (contracts) =>
    create(CompleteInvestmentContractsResponseSchema, {
      contracts: contracts.map((contract) => ({
        investmentApplicationId: contract.investmentApplicationId,
        memberNumber: contract.memberNumber,
        memberName: contract.memberName,
        contractedShares: contract.contractedShares,
      })),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
