import { create } from "@bufbuild/protobuf";
import {
  AnnualBundleFactory,
  MemberFactory,
  InvestmentApplicationFactory,
  HorseFactory,
  getClient,
  AdminUserSessionFactory,
} from "@core-test/index";
import {
  InvestmentApplicationService,
  CompleteAnnualBundleInvestmentApplicationsRequestSchema,
  InvestmentApplicationStatus,
} from "@hami/core-admin-api-schema/investment_application_service_pb";
import { AnnualBundleRecruitmentStatus, AnnualBundlePublishStatus } from "@hami/prisma";

const STATUS_CONTRACT_COMPLETED = InvestmentApplicationStatus?.CONTRACT_COMPLETED ?? 3;

describe.sequential("completeAnnualBundleInvestmentApplicationsHandler", () => {
  it("年度バンドル申込の契約を完了できる", async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const bundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 500,
    });
    const member = await MemberFactory.create({
      firstName: "契約",
      lastName: "完成",
      firstNameKana: "ケイヤク",
      lastNameKana: "カンセイ",
    });

    const horses = await HorseFactory.createList([
        { sharesTotal: 100, amountTotal: 1000000 },
        { sharesTotal: 80, amountTotal: 800000 },
    ]);
    await vPrisma.client.annualBundleHorse.createMany({
      data: horses.map((horse) => ({ annualBundleId: bundle.annualBundleId, horseId: horse.horseId })),
      skipDuplicates: true,
    });

    const application = await InvestmentApplicationFactory.create({
      annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 3,
      allocatedNumber: 3,
      rejected: false,
      contracted: false,
    });

    const request = create(CompleteAnnualBundleInvestmentApplicationsRequestSchema, {
      commands: [
        {
          annualBundleId: bundle.annualBundleId,
          investmentApplicationId: application.investmentApplicationId,
        },
      ],
    });

    const response = await apiClient.completeAnnualBundleInvestmentApplications(request, { headers });

    expect(response.results.length).toBe(1);
    const result = response.results[0];
    expect(result.success).toBe(true);
    expect(result.status).toBe(STATUS_CONTRACT_COMPLETED);
    expect(result.contractedShares).toBe(3);
    expect(result.memberName).toBeTruthy();

    const updated = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application.investmentApplicationId },
    });
    expect(updated?.contracted).toBe(true);

    const contracts = await vPrisma.client.investmentContract.findMany({
      where: { memberId: member.memberId },
    });
    expect(contracts.map((contract) => contract.horseId).sort()).toEqual(horses.map((horse) => horse.horseId).sort());
  });

  it("割当口数が設定されていない場合は失敗する", async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const apiClient = getClient(InvestmentApplicationService);
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const bundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 500,
    });
    const member = await MemberFactory.create();

    const horse = await HorseFactory.create({ sharesTotal: 50, amountTotal: 500000 });
    await vPrisma.client.annualBundleHorse.create({ data: { annualBundleId: bundle.annualBundleId, horseId: horse.horseId } });

    const application = await InvestmentApplicationFactory.create({
      annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 4,
      allocatedNumber: null,
      rejected: false,
      contracted: false,
    });

    const request = create(CompleteAnnualBundleInvestmentApplicationsRequestSchema, {
      commands: [
        {
          annualBundleId: bundle.annualBundleId,
          investmentApplicationId: application.investmentApplicationId,
        },
      ],
    });

    const response = await apiClient.completeAnnualBundleInvestmentApplications(request, { headers });

    expect(response.results.length).toBe(1);
    const result = response.results[0];
    expect(result.success).toBe(false);
    expect(result.errorMessage).toBeDefined();
    expect(result.status).toBeUndefined();

    const notContracted = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application.investmentApplicationId },
    });
    expect(notContracted?.contracted).toBe(false);
  });
});
