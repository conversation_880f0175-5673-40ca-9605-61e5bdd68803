import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { z } from 'zod';
import {
  AcceptAnnualBundleInvestmentApplicationsResponseSchema,
  AcceptAnnualBundleInvestmentApplicationResultSchema,
  InvestmentApplicationStatus,
} from '@hami/core-admin-api-schema/investment_application_service_pb';

import { DatabaseError } from '@core-api/repositories';
import {
  acceptAnnualBundleInvestmentApplications,
} from '@core-api/repositories/investment_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const statusMap: Record<string, InvestmentApplicationStatus> = {
  applied: InvestmentApplicationStatus.APPLIED,
  accepted: InvestmentApplicationStatus.ACCEPTED,
  contractCompleted: InvestmentApplicationStatus.CONTRACT_COMPLETED,
  rejected: InvestmentApplicationStatus.REJECTED,
};

export const acceptAnnualBundleInvestmentApplicationsHandler = createHandler({
  schema: z.object({
    commands: z
      .array(
        z.object({
          annualBundleId: z.number().int().positive(),
          investmentApplicationId: z.number().int().positive(),
          allocatedShares: z.number().int().min(0),
        }),
      )
      .min(1),
  }),
  business: ({ commands }) =>
    ResultAsync.fromPromise(
      acceptAnnualBundleInvestmentApplications(commands),
      (error) => new DatabaseError(`Failed to accept annual bundle investment applications: ${error}`),
    ),
  toResponse: (results) =>
    create(AcceptAnnualBundleInvestmentApplicationsResponseSchema, {
      results: results.map((result) =>
        create(AcceptAnnualBundleInvestmentApplicationResultSchema, {
          investmentApplicationId: result.investmentApplicationId,
          success: result.success,
          errorMessage: result.errorMessage,
          allocatedShares: result.allocatedShares,
          status: result.status ? statusMap[result.status] : undefined,
        }),
      ),
    }),
  toError: (error) =>
    error instanceof ValidationError ? new ConnectError(error.message, Code.InvalidArgument) : new ConnectError('Internal server error', Code.Internal),
});
