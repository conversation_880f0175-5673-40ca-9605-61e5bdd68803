import { ConnectError, Code } from '@connectrpc/connect';
import {
  HorseFactory,
  MemberFactory,
  InvestmentApplicationFactory,
  InvestmentContractFactory,
  getClient,
  AdminUserSessionFactory,
} from '@core-test/index';
import { InvestmentApplicationService } from '@hami/core-admin-api-schema/investment_application_service_pb';
import { InvestmentContractStatus } from '@hami/prisma';

describe('completeInvestmentContractsHandler', () => {
  it('契約締結が正常に実行される', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 100,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member1 = await MemberFactory.create();
    const member2 = await MemberFactory.create();

    const application1 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 10,
      allocatedNumber: 8,
      rejected: false,
      contracted: false,
    });

    const application2 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 20,
      allocatedNumber: 15,
      rejected: false,
      contracted: false,
    });

    // ===== Act =====
    const client = getClient(InvestmentApplicationService);
    const adminSession = await AdminUserSessionFactory.create();

    const result = await client.completeInvestmentContracts(
      {
        horseId: horse.horseId,
        investmentApplicationIds: [application1.investmentApplicationId, application2.investmentApplicationId],
      },
      {
        headers: {
          sessionToken: adminSession.sessionToken,
        },
      }
    );

    // ===== Assert =====
    expect(result.contracts).toBeDefined();
    expect(result.contracts.length).toBe(2);

    // 申込のcontractedフラグが更新されていることを確認
    const updatedApplication1 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application1.investmentApplicationId },
    });
    expect(updatedApplication1?.contracted).toBe(true);

    const updatedApplication2 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application2.investmentApplicationId },
    });
    expect(updatedApplication2?.contracted).toBe(true);

    // InvestmentContractレコードが作成されていることを確認
    const contracts = await vPrisma.client.investmentContract.findMany({
      where: {
        horseId: horse.horseId,
        memberId: { in: [member1.memberId, member2.memberId] },
      },
    });

    expect(contracts.length).toBe(2);

    const contract1 = contracts.find((c) => c.memberId === member1.memberId);
    expect(contract1).toBeDefined();
    expect(contract1?.sharesNumber).toBe(8);
    expect(contract1?.investmentAmount).toBe(80000); // (1000000 / 100) * 8
    expect(contract1?.contractStatus).toBe(InvestmentContractStatus.COMPLETED);

    const contract2 = contracts.find((c) => c.memberId === member2.memberId);
    expect(contract2).toBeDefined();
    expect(contract2?.sharesNumber).toBe(15);
    expect(contract2?.investmentAmount).toBe(150000); // (1000000 / 100) * 15
    expect(contract2?.contractStatus).toBe(InvestmentContractStatus.COMPLETED);
  });

  it('存在しない申込IDでエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const client = getClient(InvestmentApplicationService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.completeInvestmentContracts(
        {
          horseId: 99999,
          investmentApplicationIds: [99999],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
      expect(error.message).toContain('Some applications are not eligible for contract completion or do not belong to the specified horse');
    }
  });

  it('既にrejected=trueまたはcontracted=trueの申込は対象外', async ({ getClient }) => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 300,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member = await MemberFactory.create();

    const rejectedApplication = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 10,
      allocatedNumber: 5,
      rejected: true,
      contracted: false,
    });

    const contractedApplication = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 15,
      allocatedNumber: 8,
      rejected: false,
      contracted: true,
    });

    const adminSession = await AdminUserSessionFactory.create();
    const client = getClient(InvestmentApplicationService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.completeInvestmentContracts(
        {
          horseId: horse.horseId,
          investmentApplicationIds: [rejectedApplication.investmentApplicationId, contractedApplication.investmentApplicationId],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
      expect(error.message).toContain('Some applications are not eligible for contract completion or do not belong to the specified horse');
    }
  });

  it('allocatedNumberが0以下の申込は対象外', async ({ getClient }) => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 300,
      recruitmentYear: 2025,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member = await MemberFactory.create();

    const application = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      requestedNumber: 10,
      allocatedNumber: 0,
      rejected: false,
      contracted: false,
    });

    const adminSession = await AdminUserSessionFactory.create();
    const client = getClient(InvestmentApplicationService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.completeInvestmentContracts(
        {
          horseId: horse.horseId,
          investmentApplicationIds: [application.investmentApplicationId],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
      expect(error.message).toContain('Some applications are not eligible for contract completion or do not belong to the specified horse');
    }
  });

  it('契約口数が募集口数を超える場合はエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 400,
      recruitmentYear: 2025,
      sharesTotal: 50, // 募集口数: 50口
      amountTotal: 1000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member1 = await MemberFactory.create();
    const member2 = await MemberFactory.create();

    // 既存の契約（30口）
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      sharesNumber: 30,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 30,
      allocatedNumber: 30,
      rejected: false,
      contracted: true,
    });

    // 新規申込（合計25口）→ 既存30口 + 新規25口 = 55口 > 募集50口
    const application1 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 15,
      allocatedNumber: 15,
      rejected: false,
      contracted: false,
    });

    const application2 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 10,
      allocatedNumber: 10,
      rejected: false,
      contracted: false,
    });

    const adminSession = await AdminUserSessionFactory.create();
    const client = getClient(InvestmentApplicationService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.completeInvestmentContracts(
        {
          horseId: horse.horseId,
          investmentApplicationIds: [application1.investmentApplicationId, application2.investmentApplicationId],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
      expect(error.message).toContain('Total shares would exceed recruitment limit');
    }

    // データベースに変更がないことを確認
    const unchangedApplication1 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application1.investmentApplicationId },
    });
    expect(unchangedApplication1?.contracted).toBe(false);

    const unchangedApplication2 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application2.investmentApplicationId },
    });
    expect(unchangedApplication2?.contracted).toBe(false);

    // 新規契約が作成されていないことを確認
    const contractsCount = await vPrisma.client.investmentContract.count({
      where: { horseId: horse.horseId },
    });
    expect(contractsCount).toBe(1); // 既存の1件のみ
  });

  it('募集口数の範囲内での契約締結は正常に実行される', async ({ getClient }) => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentNo: 500,
      recruitmentYear: 2025,
      sharesTotal: 100, // 募集口数: 100口
      amountTotal: 2000000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
    });

    const member1 = await MemberFactory.create();
    const member2 = await MemberFactory.create();

    // 既存の契約（40口）
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      sharesNumber: 40,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });
    await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 40,
      allocatedNumber: 40,
      rejected: false,
      contracted: true,
    });

    // 新規申込（合計50口）→ 既存40口 + 新規50口 = 90口 <= 募集100口
    const application1 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member1.memberId } },
      requestedNumber: 30,
      allocatedNumber: 30,
      rejected: false,
      contracted: false,
    });

    const application2 = await InvestmentApplicationFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member2.memberId } },
      requestedNumber: 20,
      allocatedNumber: 20,
      rejected: false,
      contracted: false,
    });

    const adminSession = await AdminUserSessionFactory.create();
    const client = getClient(InvestmentApplicationService);

    // ===== Act =====
    const result = await client.completeInvestmentContracts(
      {
        horseId: horse.horseId,
        investmentApplicationIds: [application1.investmentApplicationId, application2.investmentApplicationId],
      },
      {
        headers: {
          sessionToken: adminSession.sessionToken,
        },
      }
    );

    // ===== Assert =====
    expect(result.contracts).toBeDefined();
    expect(result.contracts.length).toBe(2);

    // 契約締結が正常に実行されたことを確認
    const contractedApplication1 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application1.investmentApplicationId },
    });
    expect(contractedApplication1?.contracted).toBe(true);

    const contractedApplication2 = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application2.investmentApplicationId },
    });
    expect(contractedApplication2?.contracted).toBe(true);

    // 新規契約が作成されたことを確認
    const contractsCount = await vPrisma.client.investmentContract.count({
      where: { horseId: horse.horseId },
    });
    expect(contractsCount).toBe(3); // 既存1件 + 新規2件

    // 総契約口数の確認
    const totalContractedShares = await vPrisma.client.investmentContract.aggregate({
      where: { horseId: horse.horseId },
      _sum: { sharesNumber: true },
    });
    expect(totalContractedShares._sum.sharesNumber).toBe(90); // 40 + 30 + 20
  });

  describe('募集口数制限の詳細テスト', () => {
    it('ぴったり募集口数での契約締結（境界値テスト）', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentNo: 600,
        recruitmentYear: 2025,
        sharesTotal: 50, // 募集口数: 50口
        amountTotal: 1000000,
        recruitmentName: '境界値テスト馬',
        horseName: '境界値テストホース',
      });

      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 既存の契約（30口）
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        sharesNumber: 30,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 30,
        allocatedNumber: 30,
        rejected: false,
        contracted: true,
      });

      // 新規申込（20口）→ 既存30口 + 新規20口 = 50口（ぴったり）
      const application = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 20,
        allocatedNumber: 20,
        rejected: false,
        contracted: false,
      });

      // ===== Act =====
      const client = getClient(InvestmentApplicationService);
      const adminSession = await AdminUserSessionFactory.create();

      const result = await client.completeInvestmentContracts(
        {
          horseId: horse.horseId,
          investmentApplicationIds: [application.investmentApplicationId],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );

      // ===== Assert =====
      expect(result.contracts).toBeDefined();
      expect(result.contracts.length).toBe(1);

      // 総契約口数の確認
      const totalContractedShares = await vPrisma.client.investmentContract.aggregate({
        where: { horseId: horse.horseId },
        _sum: { sharesNumber: true },
      });
      expect(totalContractedShares._sum.sharesNumber).toBe(50); // ぴったり募集口数
    });

    it('1口だけ募集口数を超える場合のエラー', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentNo: 601,
        recruitmentYear: 2025,
        sharesTotal: 30, // 募集口数: 30口
        amountTotal: 600000,
        recruitmentName: '1口超過テスト馬',
        horseName: '1口超過テストホース',
      });

      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 既存の契約（29口）
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        sharesNumber: 29,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 29,
        allocatedNumber: 29,
        rejected: false,
        contracted: true,
      });

      // 新規申込（2口）→ 既存29口 + 新規2口 = 31口（1口超過）
      const application = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 2,
        allocatedNumber: 2,
        rejected: false,
        contracted: false,
      });

      // ===== Act/Assert =====
      const client = getClient(InvestmentApplicationService);
      const adminSession = await AdminUserSessionFactory.create();

      let error;
      try {
        await client.completeInvestmentContracts(
          {
            horseId: horse.horseId,
            investmentApplicationIds: [application.investmentApplicationId],
          },
          {
            headers: {
              sessionToken: adminSession.sessionToken,
            },
          }
        );
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
      if (error instanceof ConnectError) {
        expect(error.code).toBe(Code.InvalidArgument);
        expect(error.message).toContain('Total shares would exceed recruitment limit');
      }

      // データベースに変更がないことを確認
      const unchangedApplication = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: application.investmentApplicationId },
      });
      expect(unchangedApplication?.contracted).toBe(false);

      // 契約数が変わらないことを確認
      const contractsCount = await vPrisma.client.investmentContract.count({
        where: { horseId: horse.horseId },
      });
      expect(contractsCount).toBe(1); // 既存の1件のみ
    });

    it('制限ぎりぎりでの部分契約締結', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentNo: 950,
        recruitmentYear: 2025,
        sharesTotal: 40, // 募集口数: 40口
        amountTotal: 800000,
        recruitmentName: '部分契約テスト馬',
        horseName: '部分契約テストホース',
      });

      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();
      const member3 = await MemberFactory.create();

      // 既存契約（25口）
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        sharesNumber: 25,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 25,
        allocatedNumber: 25,
        rejected: false,
        contracted: true,
      });

      // 3つの申込（合計15口に調整）
      const application1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 5,
        allocatedNumber: 5,
        rejected: false,
        contracted: false,
      });

      const application2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 5,
        allocatedNumber: 5,
        rejected: false,
        contracted: false,
      });

      const application3 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member3.memberId } },
        requestedNumber: 10,
        allocatedNumber: 10,
        rejected: false,
        contracted: false,
      });

      // ===== Act & Assert =====

      // 先に2つだけ契約（制限内）→ 25 + 5 + 5 = 35口
      const client = getClient(InvestmentApplicationService);
      const adminSession = await AdminUserSessionFactory.create();

      const firstResult = await client.completeInvestmentContracts(
        {
          horseId: horse.horseId,
          investmentApplicationIds: [application1.investmentApplicationId, application2.investmentApplicationId],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );
      expect(firstResult.contracts).toBeDefined();
      expect(firstResult.contracts.length).toBe(2);

      // 残り1つの契約（制限超過）→ 35 + 10 = 45口 > 40口
      let secondError;
      try {
        await client.completeInvestmentContracts(
          {
            horseId: horse.horseId,
            investmentApplicationIds: [application3.investmentApplicationId],
          },
          {
            headers: {
              sessionToken: adminSession.sessionToken,
            },
          }
        );
      } catch (err) {
        secondError = err;
      }
      expect(secondError).toBeDefined();
      if (secondError instanceof ConnectError) {
        expect(secondError.code).toBe(Code.InvalidArgument);
        expect(secondError.message).toContain('Total shares would exceed recruitment limit');
      }

      // 最終確認
      const finalContractsCount = await vPrisma.client.investmentContract.count({
        where: { horseId: horse.horseId },
      });
      expect(finalContractsCount).toBe(3); // 既存1 + 新規2

      const finalTotalShares = await vPrisma.client.investmentContract.aggregate({
        where: { horseId: horse.horseId },
        _sum: { sharesNumber: true },
      });
      expect(finalTotalShares._sum.sharesNumber).toBe(35); // 25 + 5 + 5

      const application3Final = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: application3.investmentApplicationId },
      });
      expect(application3Final?.contracted).toBe(false); // 3つ目は契約されていない
    });

    it('部分的成功のケースで正しく動作する', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentNo: 300,
        recruitmentYear: 2025,
        sharesTotal: 50, // 募集口数: 50口
        amountTotal: 1000000,
        recruitmentName: 'テスト馬の2025',
        horseName: 'テストホース',
      });

      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();
      const member3 = await MemberFactory.create();

      // 既存の契約（20口）
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        sharesNumber: 20,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 20,
        allocatedNumber: 20,
        rejected: false,
        contracted: true,
      });

      // 新規申込（合計35口）→ 既存20口 + 新規35口 = 55口 > 募集50口
      const application1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 25,
        allocatedNumber: 25,
        rejected: false,
        contracted: false,
      });

      const application2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member3.memberId } },
        requestedNumber: 15,
        allocatedNumber: 10,
        rejected: false,
        contracted: false,
      });

      // ===== Act/Assert =====
      const client = getClient(InvestmentApplicationService);
      const adminSession = await AdminUserSessionFactory.create();

      let error;
      try {
        await client.completeInvestmentContracts(
          {
            horseId: horse.horseId,
            investmentApplicationIds: [application1.investmentApplicationId, application2.investmentApplicationId],
          },
          {
            headers: {
              sessionToken: adminSession.sessionToken,
            },
          }
        );
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
      if (error instanceof ConnectError) {
        expect(error.code).toBe(Code.InvalidArgument);
        expect(error.message).toContain('Total shares would exceed recruitment limit');
      }

      // すべての申込がcontracted=falseのまま
      const application1Status = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: application1.investmentApplicationId },
      });
      expect(application1Status?.contracted).toBe(false);

      const application2Status = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: application2.investmentApplicationId },
      });
      expect(application2Status?.contracted).toBe(false);

      // 契約数の確認（既存1件のみ）
      const contractsCount = await vPrisma.client.investmentContract.count({
        where: { horseId: horse.horseId },
      });
      expect(contractsCount).toBe(1);

      // 総契約口数の確認（既存の20口のみ）
      const totalContractedShares = await vPrisma.client.investmentContract.aggregate({
        where: { horseId: horse.horseId },
        _sum: { sharesNumber: true },
      });
      expect(totalContractedShares._sum.sharesNumber).toBe(20);
    });
  });

  describe('HorseInvestmentSharesの変遷テスト', () => {
    it('新規契約時にHorseInvestmentSharesが正しく作成される', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentNo: 700,
        recruitmentYear: 2025,
        sharesTotal: 50,
        amountTotal: 1000000,
        recruitmentName: '新規契約テスト馬',
        horseName: '新規契約テストホース',
      });

      const member = await MemberFactory.create();

      const application = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        requestedNumber: 10,
        allocatedNumber: 10,
        rejected: false,
        contracted: false,
      });

      // 初期状態の確認
      const initialShares = await vPrisma.client.horseInvestmentShares.findUnique({
        where: { horseId: horse.horseId },
      });
      expect(initialShares).toBeNull();

      // ===== Act =====
      const client = getClient(InvestmentApplicationService);
      const adminSession = await AdminUserSessionFactory.create();

      const result = await client.completeInvestmentContracts(
        {
          horseId: horse.horseId,
          investmentApplicationIds: [application.investmentApplicationId],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );

      // ===== Assert =====
      expect(result.contracts).toBeDefined();
      expect(result.contracts.length).toBe(1);

      // HorseInvestmentSharesが作成され、正しい値が設定されていることを確認
      const finalShares = await vPrisma.client.horseInvestmentShares.findUnique({
        where: { horseId: horse.horseId },
      });
      expect(finalShares).not.toBeNull();
      expect(finalShares?.memberInvestmentShares).toBe(10);
    });

    it('既存のHorseInvestmentSharesに対して正しく加算される', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentNo: 701,
        recruitmentYear: 2025,
        sharesTotal: 50,
        amountTotal: 1000000,
        recruitmentName: '加算テスト馬',
        horseName: '加算テストホース',
      });

      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 既存の契約（20口）
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        sharesNumber: 20,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 20,
        allocatedNumber: 20,
        rejected: false,
        contracted: true,
      });
      await vPrisma.client.horseInvestmentShares.create({
        data: {
          horseId: horse.horseId,
          memberInvestmentShares: 20,
        },
      });

      // 新規申込（15口）
      const application = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 15,
        allocatedNumber: 15,
        rejected: false,
        contracted: false,
      });

      // 初期状態の確認
      const initialShares = await vPrisma.client.horseInvestmentShares.findUnique({
        where: { horseId: horse.horseId },
      });
      expect(initialShares).not.toBeNull();
      expect(initialShares?.memberInvestmentShares).toBe(20);

      // ===== Act =====
      const client = getClient(InvestmentApplicationService);
      const adminSession = await AdminUserSessionFactory.create();

      const result = await client.completeInvestmentContracts(
        {
          horseId: horse.horseId,
          investmentApplicationIds: [application.investmentApplicationId],
        },
        {
          headers: {
            sessionToken: adminSession.sessionToken,
          },
        }
      );

      // ===== Assert =====
      expect(result.contracts).toBeDefined();
      expect(result.contracts.length).toBe(1);

      // HorseInvestmentSharesが正しく加算されていることを確認
      const finalShares = await vPrisma.client.horseInvestmentShares.findUnique({
        where: { horseId: horse.horseId },
      });
      expect(finalShares).not.toBeNull();
      expect(finalShares?.memberInvestmentShares).toBe(35); // 20 + 15
    });

    it('契約口数制限超過時はHorseInvestmentSharesが変更されない', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        recruitmentNo: 702,
        recruitmentYear: 2025,
        sharesTotal: 30, // 募集口数: 30口
        amountTotal: 600000,
        recruitmentName: '制限超過テスト馬',
        horseName: '制限超過テストホース',
      });

      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 既存の契約（20口）
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        sharesNumber: 20,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        requestedNumber: 20,
        allocatedNumber: 20,
        rejected: false,
        contracted: true,
      });
      await vPrisma.client.horseInvestmentShares.create({
        data: {
          horseId: horse.horseId,
          memberInvestmentShares: 20,
        },
      });

      // 新規申込（15口）→ 既存20口 + 新規15口 = 35口 > 募集30口
      const application = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        requestedNumber: 15,
        allocatedNumber: 15,
        rejected: false,
        contracted: false,
      });

      // 初期状態の確認
      const initialShares = await vPrisma.client.horseInvestmentShares.findUnique({
        where: { horseId: horse.horseId },
      });
      expect(initialShares).not.toBeNull();
      expect(initialShares?.memberInvestmentShares).toBe(20);

      // ===== Act/Assert =====
      const client = getClient(InvestmentApplicationService);
      const adminSession = await AdminUserSessionFactory.create();

      let error;
      try {
        await client.completeInvestmentContracts(
          {
            horseId: horse.horseId,
            investmentApplicationIds: [application.investmentApplicationId],
          },
          {
            headers: {
              sessionToken: adminSession.sessionToken,
            },
          }
        );
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
      if (error instanceof ConnectError) {
        expect(error.code).toBe(Code.InvalidArgument);
        expect(error.message).toContain('Total shares would exceed recruitment limit');
      }

      // HorseInvestmentSharesが変更されていないことを確認
      const finalShares = await vPrisma.client.horseInvestmentShares.findUnique({
        where: { horseId: horse.horseId },
      });
      expect(finalShares).not.toBeNull();
      expect(finalShares?.memberInvestmentShares).toBe(20); // 変更なし
    });
  });
});
