import { MemberClaimAndPayService } from '@hami/core-admin-api-schema/member_claim_and_pay_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { generateInvestmentAndReturnPdfHandler } from './generate_investment_and_return_pdf';
import { listMemberClaimAndPaysHandler } from './list_member_claim_and_pays';
import type { ConnectRouter } from '@connectrpc/connect';

export const implMemberClaimAndPayService = (router: ConnectRouter) =>
  router.service(
    MemberClaimAndPayService,
    {
      listMemberClaimAndPays: unwrapResult(listMemberClaimAndPaysHandler),
      generateInvestmentAndReturnPdf: unwrapResult(generateInvestmentAndReturnPdfHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  ); 