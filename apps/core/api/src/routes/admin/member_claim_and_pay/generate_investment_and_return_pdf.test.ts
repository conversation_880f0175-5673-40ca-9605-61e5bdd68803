import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory, MemberFactory, HorseFactory } from '@core-test/index';
import { getClient } from '@core-test/index';
import { MemberClaimAndPayService, GenerateInvestmentAndReturnPdfRequestSchema } from '@hami/core-admin-api-schema/member_claim_and_pay_service_pb';
import { ReturnCategory } from '@hami/prisma';
import { createInvestmentAndReturn, createInvestmentAndReturnInvestment, createInvestmentAndReturnReturn } from '@core-api/repositories/investment_and_return_repository';
import { createMemberClaim, createMemberClaimAndPay } from '@core-api/repositories/member_claim_and_pay_repository';

describe('GenerateInvestmentAndReturnPdf API', () => {
  const apiClient = getClient(MemberClaimAndPayService);

  it('データ投入後、PDFを生成しMemberClaimAndPayにファイルキーが設定される', async () => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const occurredDate = new Date('2024-05-01');
    const occurredDateYear = occurredDate.getFullYear();
    const occurredDateMonth = occurredDate.getMonth() + 1;
    const occurredDateDay = occurredDate.getDate();

    const member = await MemberFactory.create();
    const horse = await HorseFactory.create({
      recruitmentYear: 2024,
      recruitmentNo: 1,
      sharesTotal: 400,
      amountTotal: 36000000,
    });

    // MemberClaimAndPay（当日分）
    const memberClaimAndPayResult = await createMemberClaimAndPay(
      member.memberId,
      occurredDate,
      50000,
      30000
    );
    if (memberClaimAndPayResult.isErr()) throw memberClaimAndPayResult.error;
    const memberClaimAndPayId = memberClaimAndPayResult.value.memberClaimAndPayId;

    // MemberClaim（当日分）
    const claimResult = await createMemberClaim(
      member.memberId,
      occurredDate,
      'テスト請求',
      'テスト請求の説明',
      10,
      1000,
      10000
    );
    if (claimResult.isErr()) throw claimResult.error;

    // InvestmentAndReturn 本体
    const iarResult = await createInvestmentAndReturn(
      horse.horseId,
      member.memberId,
      occurredDate,
      /* progressedMonth */ 1,
      /* yearlyReturnTargetFlag */ false,
      /* sharesNumber */ 1,
      /* runningCostInvestmentTotal */ 1000,
      /* insuranceInvestmentTotal */ 500,
      /* otherInvestmentTotal */ 0,
      /* investmentTotal */ 1500,
      /* racehorseBookValueEndOfLastMonth */ 100000,
      /* investmentRefundPaidUpToLastMonth */ 0,
      /* organizerWithholdingTaxTotal */ 0,
      /* organizerWithholdingTaxCurrentMonthAddition */ 0,
      /* clubWithholdingTaxTotal */ 0,
      /* clubWithholdingTaxCurrentMonthAddition */ 0,
      /* billingAmount */ 2000,
      /* paymentAmount */ 1000
    );
    if (iarResult.isErr()) throw iarResult.error;

    // Investment（詳細）
    const investResult = await createInvestmentAndReturnInvestment(
      iarResult.value.investmentAndReturnId,
      /* racehorseInvestment */ 1000,
      /* runningCost */ 0,
      /* subsidy */ 0,
      /* insuranceInvestment */ 500,
      /* otherInvestment */ 0,
      /* discountAllocation */ 0,
      /* retroactiveRunningCost */ 0,
      /* runningCostInvestment */ 1000,
      /* currentMonthInvestmentTotal */ 1500,
      /* racehorseInvestmentEquivalent */ 1000
    );
    if (investResult.isErr()) throw investResult.error;

    // Returns（最低限 月次1件）
    const returnsResult = await createInvestmentAndReturnReturn(
      iarResult.value.investmentAndReturnId,
      ReturnCategory.FUND_TO_MEMBER_MONTHLY,
      /* investmentRefundPaidUpToLastMonth */ 0,
      /* refundableInvestmentAmount */ 0,
      /* distributionTargetAmount */ 0,
      /* distributionTargetAmountRefundable */ 0,
      /* distributionTargetAmountProfit */ 0,
      /* distributionTargetAmountWithholdingTax */ 0,
      /* distributionAmount */ 0,
      /* refundableInvestmentAmountCarriedForward */ 0
    );
    if (returnsResult.isErr()) throw returnsResult.error;

    const req = create(GenerateInvestmentAndReturnPdfRequestSchema, {
      occurredDateYear,
      occurredDateMonth,
      occurredDateDay,
    });

    // ===== Act =====
    const res = await apiClient.generateInvestmentAndReturnPdf(req, { headers });

    // ===== Assert ===== GenerateInvestmentAndReturnPdfResponseが返される（中身は空）
    expect(res).toBeDefined();
    expect(typeof res).toBe('object');
    expect((res as { $typeName?: string }).$typeName).toBe('core.admin.member_claim_and_pay.GenerateInvestmentAndReturnPdfResponse');

    // MemberClaimAndPay にファイルキーと発行日時が設定されていること
    const updated = await vPrisma.client.memberClaimAndPay.findUnique({
      where: { memberClaimAndPayId },
    });
    expect(updated).not.toBeNull();
    expect(updated?.statementFileKey).toBeTruthy();

    // 期待キー形式: statements/investment-and-return-<memberNumber>-(YY)(MM).pdf
    const yy = String(occurredDateYear - 2000).padStart(2, '0');
    const mm = String(occurredDateMonth).padStart(2, '0');
    expect(updated?.statementFileKey).toContain(`statements/investment-and-return-${member.memberNumber}-${yy}${mm}.pdf`);
    expect(updated?.statementIssuedAt).toBeInstanceOf(Date);
  });

  it('バリデーションエラー（存在しない月）で失敗する', async () => {
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({ sessionToken: adminSession.sessionToken });

    const req = create(GenerateInvestmentAndReturnPdfRequestSchema, {
      occurredDateYear: 2024,
      occurredDateMonth: 13,
      occurredDateDay: 1,
    });

    await expect(apiClient.generateInvestmentAndReturnPdf(req, { headers })).rejects.toThrow();
  });

  it('未認証の場合はエラーになる', async () => {
    const req = create(GenerateInvestmentAndReturnPdfRequestSchema, {
      occurredDateYear: 2024,
      occurredDateMonth: 5,
      occurredDateDay: 1,
    });

    await expect(apiClient.generateInvestmentAndReturnPdf(req)).rejects.toThrow();
  });
});
