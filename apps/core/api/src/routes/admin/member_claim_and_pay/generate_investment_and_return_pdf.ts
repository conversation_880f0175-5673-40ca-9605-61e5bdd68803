import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync, errAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GenerateInvestmentAndReturnPdfResponseSchema } from '@hami/core-admin-api-schema/member_claim_and_pay_service_pb';
import { generateInvestmentAndReturnPdfUsecase } from '@core-api/usecases/generate_investment_and_return_pdf_usecase';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const generateInvestmentAndReturnPdfHandler = createHandler({
  schema: z.object({
    occurredDateYear: z.number().int().min(1900).max(2100),
    occurredDateMonth: z.number().int().min(1).max(12),
    occurredDateDay: z.number().int().min(1).max(31),
  }),
  business: (data) => {
    // 年、月、日からDateオブジェクトを作成
    const occurredDate = new Date(data.occurredDateYear, data.occurredDateMonth - 1, data.occurredDateDay);
    
    // 日付の妥当性をチェック（無効な日付の場合、作成されたDateが異なる値になる）
    if (
      occurredDate.getFullYear() !== data.occurredDateYear ||
      occurredDate.getMonth() !== data.occurredDateMonth - 1 ||
      occurredDate.getDate() !== data.occurredDateDay
    ) {
      return errAsync(new Error('無効な日付です'));
    }

    return ResultAsync.fromPromise(
      generateInvestmentAndReturnPdfUsecase(occurredDate),
      (error) => error instanceof Error ? error : new Error('Unknown error occurred')
    );
  },
  toResponse: () => create(GenerateInvestmentAndReturnPdfResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
