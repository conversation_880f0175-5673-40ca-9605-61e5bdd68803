import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { z } from 'zod';
import { ListMemberClaimAndPaysResponseSchema } from '@hami/core-admin-api-schema/member_claim_and_pay_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listMemberClaimAndPaysWithMember } from '@core-api/repositories/member_claim_and_pay_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const listMemberClaimAndPaysHandler = createHandler({
  schema: z.object({
    year: z.number().int().min(1900).max(2100),
    month: z.number().int().min(1).max(12),
    day: z.number().int().min(1).max(31),
  }),
  business: (data) => {
    return ResultAsync.fromPromise(
      (async () => {
        // 日付の妥当性チェック
        const occurredDate = new Date(data.year, data.month - 1, data.day);
        if (
          occurredDate.getFullYear() !== data.year ||
          occurredDate.getMonth() !== data.month - 1 ||
          occurredDate.getDate() !== data.day
        ) {
          throw new ValidationError('Invalid date. The specified date does not exist.');
        }

        // MemberClaimAndPayを取得（Member情報も含める）
        const result = await listMemberClaimAndPaysWithMember(occurredDate);
        if (result.isErr()) {
          throw result.error;
        }
        return result.value;
      })(),
      (error) => error instanceof Error ? error : new Error(String(error))
    );
  },
  toResponse: (memberClaimAndPays) => {
    const memberClaimAndPayItems = memberClaimAndPays.map((item) => ({
      memberClaimAndPayId: item.memberClaimAndPayId,
      memberId: item.memberId,
      memberNumber: item.member.memberNumber.toString(),
      lastName: item.member.lastName,
      firstName: item.member.firstName,
      lastNameKana: item.member.lastNameKana,
      firstNameKana: item.member.firstNameKana,
      occurredDate: item.occurredDate.toISOString().split('T')[0], // YYYY-MM-DD形式
      claimAmount: item.claimAmount,
      payAmount: item.payAmount,
    }));

    return create(ListMemberClaimAndPaysResponseSchema, {
      memberClaimAndPays: memberClaimAndPayItems,
    });
  },
  toError: (error) => {
    if (error instanceof ValidationError) {
      return new ConnectError(error.message, Code.InvalidArgument);
    }
    if (error instanceof DatabaseError) {
      return new ConnectError('Internal server error', Code.Internal);
    }
    return new ConnectError('Internal server error', Code.Internal);
  },
});
