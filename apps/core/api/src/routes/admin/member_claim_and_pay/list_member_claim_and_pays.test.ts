import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import {
  MemberFactory,
  UserFactory,
  MembershipApplicationFactory,
  MailVerificationFactory,
  AdminUserSessionFactory,
  getClient,
} from '@core-test/index';
import { MemberClaimAndPayService, ListMemberClaimAndPaysRequestSchema } from '@hami/core-admin-api-schema/member_claim_and_pay_service_pb';
import { createMemberClaimAndPay } from '@core-api/repositories/member_claim_and_pay_repository';

describe('listMemberClaimAndPays', () => {
  const apiClient = getClient(MemberClaimAndPayService);

  it('指定した日付の会員請求・支払い一覧を取得できる', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    const occurredDate = new Date('2024-08-15');
    await createMemberClaimAndPay(member.memberId, occurredDate, 2000, 1500);

    // ===== Act =====
    const request = create(ListMemberClaimAndPaysRequestSchema, {
      year: 2024,
      month: 8,
      day: 15,
    });
    const response = await apiClient.listMemberClaimAndPays(request, { headers });

    // ===== Assert =====
    expect(response.memberClaimAndPays).toHaveLength(1);
    const claimPay = response.memberClaimAndPays[0];
    expect(claimPay.memberId).toBe(member.memberId);
    expect(claimPay.memberNumber).toBe(member.memberNumber.toString());
    expect(claimPay.lastName).toBe(member.lastName);
    expect(claimPay.firstName).toBe(member.firstName);
    expect(claimPay.lastNameKana).toBe(member.lastNameKana);
    expect(claimPay.firstNameKana).toBe(member.firstNameKana);
    expect(claimPay.occurredDate).toBe('2024-08-15');
    expect(claimPay.claimAmount).toBe(2000);
    expect(claimPay.payAmount).toBe(1500);
  });

  it('複数の会員の請求・支払い情報を取得できる', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Arrange =====
    const mailVerification1 = await MailVerificationFactory.create();
    const application1 = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification1.mailVerificationId } },
    });
    const user1 = await UserFactory.create();
    const member1 = await MemberFactory.create({
      user: { connect: { userId: user1.userId } },
      membershipApplication: { connect: { membershipApplicationId: application1.membershipApplicationId } },
    });

    const mailVerification2 = await MailVerificationFactory.create();
    const application2 = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification2.mailVerificationId } },
    });
    const user2 = await UserFactory.create();
    const member2 = await MemberFactory.create({
      user: { connect: { userId: user2.userId } },
      membershipApplication: { connect: { membershipApplicationId: application2.membershipApplicationId } },
    });

    const occurredDate = new Date('2024-08-15');
    await createMemberClaimAndPay(member1.memberId, occurredDate, 2000, 1500);
    await createMemberClaimAndPay(member2.memberId, occurredDate, 3000, 2500);

    // ===== Act =====
    const request = create(ListMemberClaimAndPaysRequestSchema, {
      year: 2024,
      month: 8,
      day: 15,
    });
    const response = await apiClient.listMemberClaimAndPays(request, { headers });

    // ===== Assert =====
    expect(response.memberClaimAndPays).toHaveLength(2);
    
    // 会員ID順でソートされていることを確認
    const sortedClaims = response.memberClaimAndPays.sort((a, b) => a.memberId - b.memberId);
    expect(sortedClaims[0].memberId).toBe(member1.memberId);
    expect(sortedClaims[0].claimAmount).toBe(2000);
    expect(sortedClaims[0].payAmount).toBe(1500);
    
    expect(sortedClaims[1].memberId).toBe(member2.memberId);
    expect(sortedClaims[1].claimAmount).toBe(3000);
    expect(sortedClaims[1].payAmount).toBe(2500);
  });

  it('指定した日付にデータが存在しない場合は空配列を返す', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act =====
    const request = create(ListMemberClaimAndPaysRequestSchema, {
      year: 2024,
      month: 8,
      day: 16,
    });
    const response = await apiClient.listMemberClaimAndPays(request, { headers });

    // ===== Assert =====
    expect(response.memberClaimAndPays).toHaveLength(0);
  });

  it('無効な日付の場合はInvalidArgumentエラーを返す', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act/Assert =====
    const request = create(ListMemberClaimAndPaysRequestSchema, {
      year: 2024,
      month: 2,
      day: 30, // 2月30日は存在しない
    });

    let error;
    try {
      await apiClient.listMemberClaimAndPays(request, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('年が範囲外の場合はInvalidArgumentエラーを返す', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act/Assert =====
    const request = create(ListMemberClaimAndPaysRequestSchema, {
      year: 1800, // 範囲外
      month: 8,
      day: 15,
    });

    let error;
    try {
      await apiClient.listMemberClaimAndPays(request, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('月が範囲外の場合はInvalidArgumentエラーを返す', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act/Assert =====
    const request = create(ListMemberClaimAndPaysRequestSchema, {
      year: 2024,
      month: 13, // 範囲外
      day: 15,
    });

    let error;
    try {
      await apiClient.listMemberClaimAndPays(request, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('日が範囲外の場合はInvalidArgumentエラーを返す', async () => {
    // ===== 0. 認証設定 =====
    const adminSession = await AdminUserSessionFactory.create();
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act/Assert =====
    const request = create(ListMemberClaimAndPaysRequestSchema, {
      year: 2024,
      month: 8,
      day: 32, // 範囲外
    });

    let error;
    try {
      await apiClient.listMemberClaimAndPays(request, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('認証されていない場合はUnauthenticatedエラーを返す', async () => {
    // ===== Act/Assert =====
    const request = create(ListMemberClaimAndPaysRequestSchema, {
      year: 2024,
      month: 8,
      day: 15,
    });

    let error;
    try {
      await apiClient.listMemberClaimAndPays(request);
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('無効なセッショントークンの場合はUnauthenticatedエラーを返す', async () => {
    // ===== Arrange =====
    const headers = new Headers({
      sessionToken: 'invalid-session-token',
    });

    // ===== Act/Assert =====
    const request = create(ListMemberClaimAndPaysRequestSchema, {
      year: 2024,
      month: 8,
      day: 15,
    });

    let error;
    try {
      await apiClient.listMemberClaimAndPays(request, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });
});
