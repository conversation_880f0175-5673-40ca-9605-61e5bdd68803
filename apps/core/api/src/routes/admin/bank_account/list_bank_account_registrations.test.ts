import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import {
  MemberFactory,
  UserFactory,
  MembershipApplicationFactory,
  MailVerificationFactory,
  AdminUserSessionFactory,
} from '@core-test/index';
import {
  BankAccountService,
  BankAccountRegistrationStatus,
  ListBankAccountRegistrationsRequestSchema,
} from '@hami/core-admin-api-schema/bank_account_service_pb';

// vPrisma.clientを使用（setup.tsでモックされているため）
const prisma = vPrisma.client;

describe('listBankAccountRegistrations', () => {
  it('口座登録一覧を正常に取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    // 口座登録を作成
    const registration1 = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: 'SUCCESS',
        isActive: true,
      },
    });

    const registration2 = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0002',
        registrationStatus: 'FAIL',
        isActive: false,
      },
    });

    const client = getClient(BankAccountService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listBankAccountRegistrations(
      create(ListBankAccountRegistrationsRequestSchema, {
        page: 1,
        limit: 10,
        statusFilter: [],
        sortBy: 'created_at',
        sortOrder: 'desc',
      }),
      { headers }
    );

    // ===== Assert =====
    expect(response.registrations).toHaveLength(2);
    expect(response.totalCount).toBe(2);
    expect(response.currentPage).toBe(1);
    expect(response.totalPages).toBe(1);

    // 作成日時の降順でソートされていることを確認
    expect(response.registrations[0].bankAccountRegistrationId).toBe(registration2.bankAccountRegistrationId);
    expect(response.registrations[1].bankAccountRegistrationId).toBe(registration1.bankAccountRegistrationId);
  });

  it('ステータスフィルターが正常に動作する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: 'SUCCESS',
        isActive: true,
      },
    });

    await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0002',
        registrationStatus: 'FAIL',
        isActive: false,
      },
    });

    const client = getClient(BankAccountService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listBankAccountRegistrations(
      create(ListBankAccountRegistrationsRequestSchema, {
        page: 1,
        limit: 10,
        statusFilter: [BankAccountRegistrationStatus.SUCCESS],
        sortBy: 'created_at',
        sortOrder: 'desc',
      }),
      { headers }
    );

    // ===== Assert =====
    expect(response.registrations).toHaveLength(1);
    expect(response.registrations[0].registrationStatus).toBe(BankAccountRegistrationStatus.SUCCESS);
  });

  it('検索クエリが正常に動作する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const mailVerification1 = await MailVerificationFactory.create();
    const application1 = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification1.mailVerificationId } },
    });
    const user1 = await UserFactory.create();
    const member1 = await MemberFactory.create({
      user: { connect: { userId: user1.userId } },
      membershipApplication: { connect: { membershipApplicationId: application1.membershipApplicationId } },
      firstName: '太郎',
      lastName: '田中',
    });

    const mailVerification2 = await MailVerificationFactory.create();
    const application2 = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification2.mailVerificationId } },
    });
    const user2 = await UserFactory.create();
    const member2 = await MemberFactory.create({
      user: { connect: { userId: user2.userId } },
      membershipApplication: { connect: { membershipApplicationId: application2.membershipApplicationId } },
      firstName: '花子',
      lastName: '佐藤',
    });

    await prisma.bankAccountRegistration.create({
      data: {
        memberId: member1.memberId,
        bankCode: '0001',
        registrationStatus: 'SUCCESS',
        isActive: true,
      },
    });

    await prisma.bankAccountRegistration.create({
      data: {
        memberId: member2.memberId,
        bankCode: '0002',
        registrationStatus: 'SUCCESS',
        isActive: true,
      },
    });

    const client = getClient(BankAccountService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listBankAccountRegistrations(
      create(ListBankAccountRegistrationsRequestSchema, {
        page: 1,
        limit: 10,
        statusFilter: [],
        searchQuery: '田中',
        sortBy: 'created_at',
        sortOrder: 'desc',
      }),
      { headers }
    );

    // ===== Assert =====
    expect(response.registrations).toHaveLength(1);
    expect(response.registrations[0].memberName).toContain('田中');
  });

  it('バリデーションエラーが正常に処理される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.listBankAccountRegistrations(
        create(ListBankAccountRegistrationsRequestSchema, {
          page: -1, // 無効な値
          limit: 10,
          statusFilter: [],
          sortBy: 'created_at',
          sortOrder: 'desc',
        }),
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Internal);
    }
  });
});
