import { BankAccountService } from '@hami/core-admin-api-schema/bank_account_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { getBankAccountDetailHandler } from './get_bank_account_detail';
import { listBankAccountRegistrationsHandler } from './list_bank_account_registrations';

import type { ConnectRouter } from '@connectrpc/connect';

export const implBankAccountService = (router: ConnectRouter) =>
  router.service(
    BankAccountService,
    {
      listBankAccountRegistrations: unwrapResult(listBankAccountRegistrationsHandler),
      getBankAccountDetail: unwrapResult(getBankAccountDetailHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  );
