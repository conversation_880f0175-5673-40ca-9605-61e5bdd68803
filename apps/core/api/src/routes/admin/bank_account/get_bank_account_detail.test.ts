import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import {
  MemberFactory,
  UserFactory,
  MembershipApplicationFactory,
  MailVerificationFactory,
  AdminUserSessionFactory,
} from '@core-test/index';
import { BankAccountService, GetBankAccountDetailRequestSchema } from '@hami/core-admin-api-schema/bank_account_service_pb';

const prisma = vPrisma.client;

describe('getBankAccountDetail', () => {
  it('会員の口座登録詳細を正常に取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: 'SUCCESS',
        isActive: true,
      },
    });

    const client = getClient(BankAccountService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.getBankAccountDetail(
      create(GetBankAccountDetailRequestSchema, {
        memberId: member.memberId,
      }),
      { headers }
    );

    // ===== Assert =====
    // 会員情報の確認
    expect(response.member).toBeDefined();
    expect(response.member!.memberId).toBe(member.memberId);
    expect(response.member!.memberName).toBe(`${member.lastName} ${member.firstName}`);
    expect(response.member!.memberNumber).toBe(member.memberNumber);

    // 口座登録履歴の確認
    expect(response.registrations).toHaveLength(1);
    expect(response.registrations[0].bankAccountRegistrationId).toBe(registration.bankAccountRegistrationId);
    expect(response.registrations[0].bankCode).toBe('0001');

    // アクティブな口座登録の確認
    expect(response.activeRegistration).not.toBeUndefined();
    expect(response.activeRegistration?.isActive).toBe(true);
  });

  it('複数の口座登録履歴がある場合に正常に取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    // 古い登録（非アクティブ）
    const oldRegistration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: 'FAIL',
        isActive: false,
        createdAt: new Date('2023-01-01'),
      },
    });

    // 新しい登録（アクティブ）
    const newRegistration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0002',
        registrationStatus: 'SUCCESS',
        isActive: true,
        createdAt: new Date('2023-02-01'),
      },
    });

    const client = getClient(BankAccountService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.getBankAccountDetail(
      create(GetBankAccountDetailRequestSchema, {
        memberId: member.memberId,
      }),
      { headers }
    );

    // ===== Assert =====
    // 2つの登録履歴があることを確認
    expect(response.registrations).toHaveLength(2);

    // 作成日時の降順でソートされていることを確認
    expect(response.registrations[0].bankAccountRegistrationId).toBe(newRegistration.bankAccountRegistrationId);
    expect(response.registrations[1].bankAccountRegistrationId).toBe(oldRegistration.bankAccountRegistrationId);

    // アクティブな登録が正しく特定されていることを確認
    expect(response.activeRegistration?.bankAccountRegistrationId).toBe(newRegistration.bankAccountRegistrationId);
    expect(response.activeRegistration?.isActive).toBe(true);
  });

  it('存在しない会員IDの場合はエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.getBankAccountDetail(
        create(GetBankAccountDetailRequestSchema, {
          memberId: 99999,
        }),
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
    }
  });

  it('バリデーションエラーが正常に処理される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountService);
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.getBankAccountDetail(
        create(GetBankAccountDetailRequestSchema, {
          memberId: -1, // 無効な値
        }),
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });
});
