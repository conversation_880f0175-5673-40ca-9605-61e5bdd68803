import { create } from '@bufbuild/protobuf';
import { Code, ConnectError, HandlerContext } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import {
  BankAccountRegistrationSummarySchema,
  ListBankAccountRegistrationsRequest,
  ListBankAccountRegistrationsResponse,
  ListBankAccountRegistrationsResponseSchema,
  BankAccountRegistrationStatus,
} from '@hami/core-admin-api-schema/bank_account_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { listBankAccountRegistrationsForAdmin } from '@core-api/repositories/bank_account_registration_repository';

// ステータス変換関数
const mapStatusToProto = (status: string): BankAccountRegistrationStatus => {
  switch (status) {
    case 'ENTRY':
      return BankAccountRegistrationStatus.ENTRY;
    case 'START':
      return BankAccountRegistrationStatus.START;
    case 'TERM':
      return BankAccountRegistrationStatus.TERM;
    case 'SUCCESS':
      return BankAccountRegistrationStatus.SUCCESS;
    case 'FAIL':
      return BankAccountRegistrationStatus.FAIL;
    case 'UNPROCESSED':
      return BankAccountRegistrationStatus.UNPROCESSED;
    default:
      return BankAccountRegistrationStatus.BANK_ACCOUNT_REGISTRATION_STATUS_UNKNOWN;
  }
};

// protobuf型を直接受け取るハンドラー
export const listBankAccountRegistrationsHandler = (
  req: ListBankAccountRegistrationsRequest,
  ctx: HandlerContext
): ResultAsync<ListBankAccountRegistrationsResponse, ConnectError> => {
  // デフォルト値の設定
  const page = req.page ?? 1;
  const limit = req.limit ?? 20;
  const statusFilter = req.statusFilter ?? [];
  const sortBy = (req.sortBy ?? 'created_at') as 'created_at' | 'updated_at' | 'member_name';
  const sortOrder = (req.sortOrder ?? 'desc') as 'asc' | 'desc';

  // バリデーション
  if (page < 1) {
    return ResultAsync.fromSafePromise(Promise.resolve()).andThen(() =>
      ResultAsync.fromPromise(
        Promise.reject(new ConnectError('ページ番号は1以上である必要があります', Code.InvalidArgument)),
        () => new ConnectError('Internal server error', Code.Internal)
      )
    );
  }

  if (limit < 1 || limit > 100) {
    return ResultAsync.fromSafePromise(Promise.resolve()).andThen(() =>
      ResultAsync.fromPromise(
        Promise.reject(new ConnectError('リミットは1以上100以下である必要があります', Code.InvalidArgument)),
        () => new ConnectError('Internal server error', Code.Internal)
      )
    );
  }

  const dateFrom = req.dateFrom ? new Date(Number(req.dateFrom)) : undefined;
  const dateTo = req.dateTo ? new Date(Number(req.dateTo)) : undefined;

  return listBankAccountRegistrationsForAdmin({
    page,
    limit,
    statusFilter:
      statusFilter.length > 0
        ? statusFilter.map((status) => {
            // プロトのenumから文字列に変換
            switch (status) {
              case BankAccountRegistrationStatus.ENTRY:
                return 'ENTRY';
              case BankAccountRegistrationStatus.START:
                return 'START';
              case BankAccountRegistrationStatus.TERM:
                return 'TERM';
              case BankAccountRegistrationStatus.SUCCESS:
                return 'SUCCESS';
              case BankAccountRegistrationStatus.FAIL:
                return 'FAIL';
              case BankAccountRegistrationStatus.UNPROCESSED:
                return 'UNPROCESSED';
              default:
                return 'ENTRY';
            }
          })
        : [],
    searchQuery: req.searchQuery,
    sortBy,
    sortOrder,
    dateFrom,
    dateTo,
  })
    .map((data) =>
      create(ListBankAccountRegistrationsResponseSchema, {
        registrations: data.registrations.map((registration) =>
          create(BankAccountRegistrationSummarySchema, {
            bankAccountRegistrationId: registration.bankAccountRegistrationId,
            memberId: registration.memberId,
            memberName: `${registration.member.lastName} ${registration.member.firstName}`,
            memberNumber: registration.member.memberNumber,
            registrationStatus: mapStatusToProto(registration.registrationStatus),
            bankCode: registration.bankCode,
            bankName: undefined, // TODO: 銀行名マスターから取得
            resultAccountNumber: registration.resultAccountNumber ?? undefined,
            createdAt: BigInt(registration.createdAt.getTime()),
            updatedAt: BigInt(registration.updatedAt.getTime()),
            completedAt: registration.completedAt ? BigInt(registration.completedAt.getTime()) : undefined,
            errorMessage: registration.errorMessage ?? undefined,
            isActive: registration.isActive,
          })
        ),
        totalCount: data.totalCount,
        currentPage: data.currentPage,
        totalPages: data.totalPages,
      })
    )
    .mapErr((error) => {
      if (error instanceof DatabaseError) {
        return new ConnectError('Internal server error', Code.Internal);
      }
      return new ConnectError('Internal server error', Code.Internal);
    });
};
