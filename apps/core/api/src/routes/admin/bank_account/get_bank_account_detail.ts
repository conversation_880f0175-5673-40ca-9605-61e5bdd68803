import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  BankAccountRegistrationDetailSchema,
  BankAccountRegistrationLogSchema,
  MemberInfoSchema,
  GetBankAccountDetailResponseSchema,
  BankAccountRegistrationStatus,
} from '@hami/core-admin-api-schema/bank_account_service_pb';
import { DatabaseError } from '@core-api/repositories';
import {
  getBankAccountRegistrationsByMemberId,
  BankAccountRegistrationNotFoundError,
} from '@core-api/repositories/bank_account_registration_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

// リクエストスキーマ
const GetBankAccountDetailRequestSchema = z.object({
  memberId: z.number().int().positive(),
});

// ステータス変換関数
const mapStatusToProto = (status: string): BankAccountRegistrationStatus => {
  switch (status) {
    case 'ENTRY':
      return BankAccountRegistrationStatus.ENTRY;
    case 'START':
      return BankAccountRegistrationStatus.START;
    case 'TERM':
      return BankAccountRegistrationStatus.TERM;
    case 'SUCCESS':
      return BankAccountRegistrationStatus.SUCCESS;
    case 'FAIL':
      return BankAccountRegistrationStatus.FAIL;
    case 'UNPROCESSED':
      return BankAccountRegistrationStatus.UNPROCESSED;
    default:
      return BankAccountRegistrationStatus.BANK_ACCOUNT_REGISTRATION_STATUS_UNKNOWN;
  }
};

export const getBankAccountDetailHandler = createHandler({
  schema: GetBankAccountDetailRequestSchema,
  business: (request) => getBankAccountRegistrationsByMemberId({ memberId: request.memberId }),
  toResponse: (data) =>
    create(GetBankAccountDetailResponseSchema, {
      member: create(MemberInfoSchema, {
        memberId: data.member.memberId,
        memberName: data.member.memberName,
        memberNumber: data.member.memberNumber,
        email: data.member.email,
      }),
      registrations: data.registrations.map((registration) =>
        create(BankAccountRegistrationDetailSchema, {
          bankAccountRegistrationId: registration.bankAccountRegistrationId,
          registrationStatus: mapStatusToProto(registration.registrationStatus),
          bankCode: registration.bankCode,
          bankName: undefined, // TODO: 銀行名マスターから取得
          branchCode: registration.branchCode ?? undefined,
          accountType: registration.accountType ?? undefined,
          accountNumber: registration.accountNumber ?? undefined,
          accountName: registration.accountName ?? undefined,
          accountNameKanji: registration.accountNameKanji ?? undefined,
          resultBankCode: registration.resultBankCode ?? undefined,
          resultBranchCode: registration.resultBranchCode ?? undefined,
          resultAccountType: registration.resultAccountType ?? undefined,
          resultAccountNumber: registration.resultAccountNumber ?? undefined,
          resultAccountName: registration.resultAccountName ?? undefined,
          gmoTransactionId: registration.gmoTransactionId ?? undefined,
          gmoToken: registration.gmoToken ?? undefined,
          errorCode: registration.errorCode ?? undefined,
          errorDetail: registration.errorDetail ?? undefined,
          errorMessage: registration.errorMessage ?? undefined,
          createdAt: BigInt(registration.createdAt.getTime()),
          updatedAt: BigInt(registration.updatedAt.getTime()),
          completedAt: registration.completedAt ? BigInt(registration.completedAt.getTime()) : undefined,
          isActive: registration.isActive,
          statusLogs: [], // ログは別途実装が必要
        })
      ),
      activeRegistration: data.activeRegistration
        ? create(BankAccountRegistrationDetailSchema, {
            bankAccountRegistrationId: data.activeRegistration.bankAccountRegistrationId,
            registrationStatus: mapStatusToProto(data.activeRegistration.registrationStatus),
            bankCode: data.activeRegistration.bankCode,
            bankName: undefined, // TODO: 銀行名マスターから取得
            branchCode: data.activeRegistration.branchCode ?? undefined,
            accountType: data.activeRegistration.accountType ?? undefined,
            accountNumber: data.activeRegistration.accountNumber ?? undefined,
            accountName: data.activeRegistration.accountName ?? undefined,
            accountNameKanji: data.activeRegistration.accountNameKanji ?? undefined,
            resultBankCode: data.activeRegistration.resultBankCode ?? undefined,
            resultBranchCode: data.activeRegistration.resultBranchCode ?? undefined,
            resultAccountType: data.activeRegistration.resultAccountType ?? undefined,
            resultAccountNumber: data.activeRegistration.resultAccountNumber ?? undefined,
            resultAccountName: data.activeRegistration.resultAccountName ?? undefined,
            gmoTransactionId: data.activeRegistration.gmoTransactionId ?? undefined,
            gmoToken: data.activeRegistration.gmoToken ?? undefined,
            errorCode: data.activeRegistration.errorCode ?? undefined,
            errorDetail: data.activeRegistration.errorDetail ?? undefined,
            errorMessage: data.activeRegistration.errorMessage ?? undefined,
            createdAt: BigInt(data.activeRegistration.createdAt.getTime()),
            updatedAt: BigInt(data.activeRegistration.updatedAt.getTime()),
            completedAt: data.activeRegistration.completedAt ? BigInt(data.activeRegistration.completedAt.getTime()) : undefined,
            isActive: data.activeRegistration.isActive,
            statusLogs: [], // ログは別途実装が必要
          })
        : undefined,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(BankAccountRegistrationNotFoundError), () => new ConnectError('Member not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
