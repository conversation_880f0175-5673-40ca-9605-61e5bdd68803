import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import {
  GenerateHorseMemberListPdfRequest,
  GenerateHorseMemberListPdfResponse,
  GenerateHorseMemberListPdfResponseSchema,
} from '@hami/core-admin-api-schema/horse_member_report_service_pb';
import { generateHorseMemberListPdfUsecase } from '@core-api/usecases/generate_horse_member_list_pdf_usecase';

export const generateHorseMemberListPdf = async (_req: GenerateHorseMemberListPdfRequest): Promise<GenerateHorseMemberListPdfResponse> => {
  const result = await generateHorseMemberListPdfUsecase();

  if (result.isErr()) {
    console.error('PDF generation failed:', result.error);
    throw new ConnectError('PDF生成に失敗しました', Code.Internal);
  }

  const data = result.value;

  return create(GenerateHorseMemberListPdfResponseSchema, {
    pdfFileKey: data.pdfFileKey,
    downloadUrl: data.downloadUrl,
    totalHorses: data.totalHorses,
    totalMembers: data.totalMembers,
    generatedAt: data.generatedAt,
  });
};
