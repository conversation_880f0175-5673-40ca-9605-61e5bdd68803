import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import {
  GetHorseMemberListPdfDownloadUrlRequest,
  GetHorseMemberListPdfDownloadUrlResponse,
  GetHorseMemberListPdfDownloadUrlResponseSchema,
} from '@hami/core-admin-api-schema/horse_member_report_service_pb';
import { getHorseMemberListPdfDownloadUrlUsecase } from '@core-api/usecases/get_horse_member_list_pdf_download_url_usecase';

export const getHorseMemberListPdfDownloadUrl = async (
  req: GetHorseMemberListPdfDownloadUrlRequest
): Promise<GetHorseMemberListPdfDownloadUrlResponse> => {
  const result = await getHorseMemberListPdfDownloadUrlUsecase(req.pdfFileKey);

  if (result.isErr()) {
    console.error('Download URL generation failed:', result.error);
    throw new ConnectError('ダウンロードURL生成に失敗しました', Code.Internal);
  }

  const data = result.value;

  return create(GetHorseMemberListPdfDownloadUrlResponseSchema, {
    downloadUrl: data.downloadUrl,
    expiresAt: data.expiresAt,
  });
};
