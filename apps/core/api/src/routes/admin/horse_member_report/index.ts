import { HorseMemberReportService } from '@hami/core-admin-api-schema/horse_member_report_service_pb';
import { generateHorseMemberListPdf } from './generate_horse_member_list_pdf';
import { getHorseMemberListPdfDownloadUrl } from './get_horse_member_list_pdf_download_url';

import type { ConnectRouter } from '@connectrpc/connect';

export const implHorseMemberReportService = (router: ConnectRouter) =>
  router.service(HorseMemberReportService, {
    generateHorseMemberListPdf,
    getHorseMemberListPdfDownloadUrl,
  });
