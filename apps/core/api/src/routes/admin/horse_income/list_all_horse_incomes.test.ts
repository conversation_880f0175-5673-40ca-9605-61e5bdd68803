import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseIncomeOtherFactory } from '@core-test/factories/horse_income_other_factory';
import { HorseIncomePrizeFactory } from '@core-test/factories/horse_income_prize_factory';
import { HorseFactory, getClient } from '@core-test/index';
import { HorseIncomeService, ListAllHorseIncomesRequestSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { HorseIncomeOtherName, type Horse, type AdminUserSession } from '@hami/prisma';

describe('listAllHorseIncomesHandler', () => {
  const apiClient = getClient(HorseIncomeService);
  let horse1: Horse;
  let horse2: Horse;
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse1 = await HorseFactory.create({
      horseId: 1,
      horseName: 'テスト馬1',
      recruitmentYear: 2020,
      recruitmentNo: 1,
      birthYear: 2020,
      birthMonth: 1,
      birthDay: 1,
      recruitmentName: 'テスト馬1募集',
      sharesTotal: 100,
      amountTotal: 10000000,
      note: 'テスト馬1',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
    });

    horse2 = await HorseFactory.create({
      horseId: 2,
      horseName: 'テスト馬2',
      recruitmentYear: 2020,
      recruitmentNo: 2,
      birthYear: 2020,
      birthMonth: 2,
      birthDay: 15,
      recruitmentName: 'テスト馬2募集',
      sharesTotal: 100,
      amountTotal: 10000000,
      note: 'テスト馬2',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
    });
  });

  it('指定された日付範囲の全馬の収入一覧を取得できる', async () => {
    // ===== Arrange =====
    // 指定範囲内の収入
    const prize1 = await HorseIncomePrizeFactory.create({
      horseId: horse1.horseId,
      incomeYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 15,
      racePlace: '東京',
      raceName: 'テストレース1',
      raceResult: '1着',
      organizer: 'JRA',
      mainPrizeAmount: 1000000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: 0.1,
      taxRate: 0.2,
      totalPrizeAmount: 1100000,
      clubFeeAmount: 110000,
      taxAmount: 220000,
      incomeAmount: 770000,
      note: 'テスト賞金収入1',
      closing: false,
    });

    const other1 = await HorseIncomeOtherFactory.create({
      horseId: horse2.horseId,
      incomeYearMonth: 202402,
      occurredYear: 2024,
      occurredMonth: 2,
      occurredDay: 20,
      name: HorseIncomeOtherName.SALES_DIVIDEND,
      nameOther: '販売配当',
      amount: 500000,
      salesCommission: 25000,
      otherFeeName: '手数料',
      otherFeeAmount: 10000,
      taxRate: 0.2,
      taxAmount: 100000,
      incomeAmount: 365000,
      note: 'テストその他収入1',
      closing: false,
    });

    // 指定範囲外の収入（取得されないはず）
    const prize2 = await HorseIncomePrizeFactory.create({
      horseId: horse1.horseId,
      incomeYearMonth: 202312,
      occurredYear: 2023,
      occurredMonth: 12,
      occurredDay: 31,
      racePlace: '京都',
      raceName: 'テストレース2',
      raceResult: '2着',
      organizer: 'OTHER',
      mainPrizeAmount: 800000,
      appearanceFee: 40000,
      withholdingTax: 80000,
      commissionAmount: 40000,
      clubFeeRate: 0.1,
      taxRate: 0.2,
      totalPrizeAmount: 880000,
      clubFeeAmount: 88000,
      taxAmount: 176000,
      incomeAmount: 616000,
      note: 'テスト賞金収入2',
      closing: false,
    });

    const request = create(ListAllHorseIncomesRequestSchema, {
      startYear: 2024,
      startMonth: 1,
      startDay: 1,
      endYear: 2024,
      endMonth: 12,
      endDay: 31,
    });

    // ===== Act =====
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });
    const result = await apiClient.listAllHorseIncomes(request, { headers });

    // ===== Assert =====
    expect(result.horseIncomes.length).toBe(2);
    
    // 指定範囲内の収入が含まれていることを確認
    const incomeIds = result.horseIncomes.map(income => income.id);
    expect(incomeIds).toContain(prize1.horseIncomePrizeId);
    expect(incomeIds).toContain(other1.horseIncomeOtherId);
    
    // 指定範囲外の収入は含まれていないことを確認
    expect(incomeIds).not.toContain(prize2.horseIncomePrizeId);

    // horseNameが正しく設定されていることを確認
    const horse1Income = result.horseIncomes.find(income => income.horseId === horse1.horseId);
    const horse2Income = result.horseIncomes.find(income => income.horseId === horse2.horseId);
    
    expect(horse1Income?.horseName).toBe('テスト馬1');
    expect(horse2Income?.horseName).toBe('テスト馬2');
  });

  it('日付範囲が狭い場合、該当する収入のみ取得される', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      horseId: 3,
      horseName: 'テスト馬3',
      recruitmentYear: 2020,
      recruitmentNo: 3,
      birthYear: 2020,
      birthMonth: 3,
      birthDay: 30,
      recruitmentName: 'テスト馬3募集',
      sharesTotal: 100,
      amountTotal: 10000000,
      note: 'テスト馬3',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
    });

    // 1月15日の収入
    const prize1 = await HorseIncomePrizeFactory.create({
      horseId: horse.horseId,
      incomeYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 15,
      racePlace: '東京',
      raceName: '1月レース',
      raceResult: '1着',
      organizer: 'JRA',
      mainPrizeAmount: 1000000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: 0.1,
      taxRate: 0.2,
      totalPrizeAmount: 1100000,
      clubFeeAmount: 110000,
      taxAmount: 220000,
      incomeAmount: 770000,
      note: '1月の賞金収入',
      closing: false,
    });

    // 1月20日の収入
    const prize2 = await HorseIncomePrizeFactory.create({
      horseId: horse.horseId,
      incomeYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 20,
      racePlace: '京都',
      raceName: '1月レース2',
      raceResult: '2着',
      organizer: 'OTHER',
      mainPrizeAmount: 800000,
      appearanceFee: 40000,
      withholdingTax: 80000,
      commissionAmount: 40000,
      clubFeeRate: 0.1,
      taxRate: 0.2,
      totalPrizeAmount: 880000,
      clubFeeAmount: 88000,
      taxAmount: 176000,
      incomeAmount: 616000,
      note: '1月の賞金収入2',
      closing: false,
    });

    // 2月10日の収入
    const other1 = await HorseIncomeOtherFactory.create({
      horseId: horse.horseId,
      incomeYearMonth: 202402,
      occurredYear: 2024,
      occurredMonth: 2,
      occurredDay: 10,
      name: HorseIncomeOtherName.SALES_DIVIDEND,
      nameOther: '販売配当',
      amount: 500000,
      salesCommission: 25000,
      otherFeeName: '手数料',
      otherFeeAmount: 10000,
      taxRate: 0.2,
      taxAmount: 100000,
      incomeAmount: 365000,
      note: '2月のその他収入',
      closing: false,
    });

    const request = create(ListAllHorseIncomesRequestSchema, {
      startYear: 2024,
      startMonth: 1,
      startDay: 15,
      endYear: 2024,
      endMonth: 1,
      endDay: 20,
    });

    // ===== Act =====
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });
    const result = await apiClient.listAllHorseIncomes(request, { headers });

    // ===== Assert =====
    expect(result.horseIncomes.length).toBe(2);
    
    // 1月15日〜20日の収入のみ取得される
    const incomeIds = result.horseIncomes.map(income => income.id);
    expect(incomeIds).toContain(prize1.horseIncomePrizeId);
    expect(incomeIds).toContain(prize2.horseIncomePrizeId);
    
    // 2月10日の収入は含まれていない
    expect(incomeIds).not.toContain(other1.horseIncomeOtherId);
  });

  it('収入が存在しない期間の場合は空の配列を返す', async () => {
    // ===== Arrange =====
    const request = create(ListAllHorseIncomesRequestSchema, {
      startYear: 2025,
      startMonth: 1,
      startDay: 1,
      endYear: 2025,
      endMonth: 12,
      endDay: 31,
    });

    // ===== Act =====
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });
    const result = await apiClient.listAllHorseIncomes(request, { headers });

    // ===== Assert =====
    expect(result.horseIncomes).toEqual([]);
  });

  it('日付範囲が逆順の場合でも正しく動作する', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      horseId: 4,
      horseName: 'テスト馬4',
      recruitmentYear: 2020,
      recruitmentNo: 4,
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      recruitmentName: 'テスト馬4募集',
      sharesTotal: 100,
      amountTotal: 10000000,
      note: 'テスト馬4',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
    });

    const prize = await HorseIncomePrizeFactory.create({
      horseId: horse.horseId,
      incomeYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 15,
      racePlace: '東京',
      raceName: 'テストレース',
      raceResult: '1着',
      organizer: 'JRA',
      mainPrizeAmount: 1000000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: 0.1,
      taxRate: 0.2,
      totalPrizeAmount: 1100000,
      clubFeeAmount: 110000,
      taxAmount: 220000,
      incomeAmount: 770000,
      note: 'テスト賞金収入',
      closing: false,
    });

    const request = create(ListAllHorseIncomesRequestSchema, {
      startYear: 2024,
      startMonth: 1,
      startDay: 15,
      endYear: 2024,
      endMonth: 1,
      endDay: 15,
    });

    // ===== Act =====
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });
    const result = await apiClient.listAllHorseIncomes(request, { headers });

    // ===== Assert =====
    expect(result.horseIncomes.length).toBe(1);
    expect(result.horseIncomes[0].id).toBe(prize.horseIncomePrizeId);
  });

  it('複数の馬の収入が正しく取得される', async () => {
    // ===== Arrange =====
    const horse3 = await HorseFactory.create({
      horseId: 5,
      horseName: 'テスト馬5',
      recruitmentYear: 2020,
      recruitmentNo: 5,
      birthYear: 2020,
      birthMonth: 5,
      birthDay: 1,
      recruitmentName: 'テスト馬5募集',
      sharesTotal: 100,
      amountTotal: 10000000,
      note: 'テスト馬5',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
    });

    const horse4 = await HorseFactory.create({
      horseId: 6,
      horseName: 'テスト馬6',
      recruitmentYear: 2020,
      recruitmentNo: 6,
      birthYear: 2020,
      birthMonth: 6,
      birthDay: 15,
      recruitmentName: 'テスト馬6募集',
      sharesTotal: 100,
      amountTotal: 10000000,
      note: 'テスト馬6',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
    });

    const horse5 = await HorseFactory.create({
      horseId: 7,
      horseName: 'テスト馬7',
      recruitmentYear: 2020,
      recruitmentNo: 7,
      birthYear: 2020,
      birthMonth: 7,
      birthDay: 30,
      recruitmentName: 'テスト馬7募集',
      sharesTotal: 100,
      amountTotal: 10000000,
      note: 'テスト馬7',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
    });

    // 各馬の収入を作成
    const prize1 = await HorseIncomePrizeFactory.create({
      horseId: horse1.horseId,
      incomeYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 15,
      racePlace: '東京',
      raceName: '馬1レース',
      raceResult: '1着',
      organizer: 'JRA',
      mainPrizeAmount: 1000000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: 0.1,
      taxRate: 0.2,
      totalPrizeAmount: 1100000,
      clubFeeAmount: 110000,
      taxAmount: 220000,
      incomeAmount: 770000,
      note: '馬1の賞金収入',
      closing: false,
    });

    const other1 = await HorseIncomeOtherFactory.create({
      horseId: horse2.horseId,
      incomeYearMonth: 202402,
      occurredYear: 2024,
      occurredMonth: 2,
      occurredDay: 20,
      name: HorseIncomeOtherName.SALES_DIVIDEND,
      nameOther: '販売配当',
      amount: 500000,
      salesCommission: 25000,
      otherFeeName: '手数料',
      otherFeeAmount: 10000,
      taxRate: 0.2,
      taxAmount: 100000,
      incomeAmount: 365000,
      note: '馬2のその他収入',
      closing: false,
    });

    const prize2 = await HorseIncomePrizeFactory.create({
      horseId: horse3.horseId,
      incomeYearMonth: 202403,
      occurredYear: 2024,
      occurredMonth: 3,
      occurredDay: 10,
      racePlace: '京都',
      raceName: '馬3レース',
      raceResult: '3着',
      organizer: 'OTHER',
      mainPrizeAmount: 600000,
      appearanceFee: 30000,
      withholdingTax: 60000,
      commissionAmount: 30000,
      clubFeeRate: 0.1,
      taxRate: 0.2,
      totalPrizeAmount: 660000,
      clubFeeAmount: 66000,
      taxAmount: 132000,
      incomeAmount: 462000,
      note: '馬3の賞金収入',
      closing: false,
    });

    const request = create(ListAllHorseIncomesRequestSchema, {
      startYear: 2024,
      startMonth: 1,
      startDay: 1,
      endYear: 2024,
      endMonth: 12,
      endDay: 31,
    });

    // ===== Act =====
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });
    const result = await apiClient.listAllHorseIncomes(request, { headers });

    // ===== Assert =====
    expect(result.horseIncomes.length).toBe(3);
    
    // 全ての収入が含まれていることを確認
    const incomeIds = result.horseIncomes.map(income => income.id);
    expect(incomeIds).toContain(prize1.horseIncomePrizeId);
    expect(incomeIds).toContain(other1.horseIncomeOtherId);
    expect(incomeIds).toContain(prize2.horseIncomePrizeId);

    // 各馬のhorseNameが正しく設定されていることを確認
    const horse1Income = result.horseIncomes.find(income => income.horseId === horse1.horseId);
    const horse2Income = result.horseIncomes.find(income => income.horseId === horse2.horseId);
    const horse3Income = result.horseIncomes.find(income => income.horseId === horse3.horseId);

    expect(horse1Income?.horseName).toBe('テスト馬1');
    expect(horse2Income?.horseName).toBe('テスト馬2');
    expect(horse3Income?.horseName).toBe('テスト馬5');
  });
});
