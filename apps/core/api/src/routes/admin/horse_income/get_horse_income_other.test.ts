import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { HorseIncomeOtherFactory } from '@core-test/factories/horse_income_other_factory';
import { getClient } from '@core-test/index';
import { HorseIncomeService, GetHorseIncomeOtherRequestSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';
import type { AdminUserSession, Horse, HorseIncomeOther } from '@hami/prisma';

describe('getHorseIncomeOther API', () => {
  const apiClient = getClient(HorseIncomeService);
  let adminSession: AdminUserSession;
  let horse: Horse;
  let other: HorseIncomeOther;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse = await HorseFactory.create();
    other = await HorseIncomeOtherFactory.create({
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      nameOther: 'テスト用その他収入',
      amount: 500000,
    });
  });

  it('正常に馬のその他収入を取得できる', async () => {
    const request = create(GetHorseIncomeOtherRequestSchema, {
      id: other.horseIncomeOtherId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.getHorseIncomeOther(request, { headers });

    expect(response).toBeDefined();
    expect(response.horseIncomeOther).toBeDefined();
    expect(response.horseIncomeOther!.horseIncomeOtherId).toBe(other.horseIncomeOtherId);
    expect(response.horseIncomeOther!.horseId).toBe(horse.horseId);
    expect(response.horseIncomeOther!.nameOther).toBe('テスト用その他収入');
    expect(response.horseIncomeOther!.amount).toBe(500000);
  });

  it('存在しないIDの場合はエラーになる', async () => {
    const request = create(GetHorseIncomeOtherRequestSchema, {
      id: 99999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.getHorseIncomeOther(request, { headers })).rejects.toThrow();
  });

  it('無効なIDでエラーになる', async () => {
    const request = create(GetHorseIncomeOtherRequestSchema, {
      id: -1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.getHorseIncomeOther(request, { headers })).rejects.toThrow();
  });
});
