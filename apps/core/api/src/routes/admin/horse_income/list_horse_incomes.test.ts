import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseIncomeOtherFactory } from '@core-test/factories/horse_income_other_factory';
import { HorseIncomePrizeFactory } from '@core-test/factories/horse_income_prize_factory';
import { HorseFactory, getClient } from '@core-test/index';
import { HorseIncomeService, ListHorseIncomesRequestSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { HorseIncomeOtherName, type AdminUserSession, type Horse } from '@hami/prisma';
import { getHorseIncomePrize } from '@core-api/repositories/horse_income_repository';

describe('listHorseIncomesHandler', () => {
  const apiClient = getClient(HorseIncomeService);
  let adminSession: AdminUserSession;
  let horse: Horse;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse = await HorseFactory.create();
  });

  it('正常に馬の収入一覧を取得できる', async () => {
    // テストデータ作成をトランザクション内で実行
    const [prize1, prize2, other1] = await vPrisma.client.$transaction(async (tx) => {
      const p1 = await HorseIncomePrizeFactory.create({
        horseId: horse.horseId,
        incomeYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
      });

      const p2 = await HorseIncomePrizeFactory.create({
        horseId: horse.horseId,
        incomeYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
      });

      const o1 = await HorseIncomeOtherFactory.create({
        horseId: horse.horseId,
        incomeYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 5,
      });

      return [p1, p2, o1];
    });

    // CI環境での安定性向上のため、データ作成後に少し待つ
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // VIEWに反映されるまでポーリング
    let retries = 10;
    let viewRows: any[] = [];
    
    while (retries > 0) {
      viewRows = await vPrisma.client.horseIncomeView.findMany({
        where: { horseId: horse.horseId }
      });
      
      if (viewRows.length === 3) {
        break; // 期待する件数が見つかった
      }
      
      retries--;
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    console.log('🔍 HorseIncomeView contents after polling:', viewRows);
    console.log('🏆 Expected count: 3, Actual count:', viewRows.length);
    
    // デバッグ: 元テーブルの内容も確認
    const prizeRows = await vPrisma.client.horseIncomePrize.findMany({
      where: { horseId: horse.horseId }
    });
    console.log('🏆 HorseIncomePrize contents:', prizeRows);
    
    const otherRows = await vPrisma.client.horseIncomeOther.findMany({
      where: { horseId: horse.horseId }
    });
    console.log('💰 HorseIncomeOther contents:', otherRows);
    
    // CI環境での追加デバッグ情報
    console.log('🔧 Test environment info:');
    console.log('  - NODE_ENV:', process.env.NODE_ENV);
    console.log('  - CI:', process.env.CI);
    console.log('  - DATABASE_URL:', process.env.DATABASE_URL?.substring(0, 20) + '...');
    console.log('  - Horse ID:', horse.horseId);
    console.log('  - Prize1 ID:', prize1.horseIncomePrizeId);
    console.log('  - Prize2 ID:', prize2.horseIncomePrizeId);
    console.log('  - Other1 ID:', other1.horseIncomeOtherId);
    
    // データが作成されていることを確認
    const prize1dbData = await getHorseIncomePrize({
      id: prize1.horseIncomePrizeId,
    });
    if (prize1dbData.isOk()) {
      expect(prize1dbData.value.horseIncomePrizeId).toBe(prize1.horseIncomePrizeId);
      expect(prize1dbData.value.horseId).toBe(horse.horseId);
      expect(prize1dbData.value.incomeYearMonth).toBe(202412);
      expect(prize1dbData.value.occurredYear).toBe(2024);
      expect(prize1dbData.value.occurredMonth).toBe(12);
      expect(prize1dbData.value.occurredDay).toBe(1);
    }

    // APIクライアント経由でテスト
    const request = create(ListHorseIncomesRequestSchema, {
      horseId: horse.horseId,
      limit: 10,
      offset: 0,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseIncomes(request, { headers });

    expect(response).toBeDefined();
    expect(response.horseIncomes).toHaveLength(3);
    expect(response.horseIncomes[0].id).toBe(other1.horseIncomeOtherId);
    expect(response.horseIncomes[0].horseId).toBe(horse.horseId);
    expect(response.horseIncomes[0].horseName).toBe(horse.horseName);
    expect(response.horseIncomes[0].closing).toBe(other1.closing);
    expect(response.horseIncomes[0].incomeYearMonth).toBe(other1.incomeYearMonth);
    expect(response.horseIncomes[0].occurredYear).toBe(other1.occurredYear);
    expect(response.horseIncomes[0].occurredMonth).toBe(other1.occurredMonth);
    expect(response.horseIncomes[0].occurredDay).toBe(other1.occurredDay);
    expect(response.horseIncomes[0].name).toBe(other1.name);
    expect(response.horseIncomes[0].name2).toBe(other1.nameOther);
    expect(response.horseIncomes[0].amount).toBe(other1.incomeAmount);
    expect(response.horseIncomes[1].id).toBe(prize1.horseIncomePrizeId);
    expect(response.horseIncomes[1].horseId).toBe(horse.horseId);
    expect(response.horseIncomes[1].horseName).toBe(horse.horseName);
    expect(response.horseIncomes[1].closing).toBe(prize1.closing);
    expect(response.horseIncomes[1].incomeYearMonth).toBe(prize1.incomeYearMonth);
    expect(response.horseIncomes[1].occurredYear).toBe(prize1.occurredYear);
    expect(response.horseIncomes[1].occurredMonth).toBe(prize1.occurredMonth);
    expect(response.horseIncomes[1].occurredDay).toBe(prize1.occurredDay);
    expect(response.horseIncomes[1].name).toBe(prize1.raceName);
    expect(response.horseIncomes[1].name2).toBe(prize1.raceResult);
    expect(response.horseIncomes[1].amount).toBe(prize1.incomeAmount);
    expect(response.horseIncomes[2].id).toBe(prize2.horseIncomePrizeId);
    expect(response.horseIncomes[2].horseId).toBe(horse.horseId);
    expect(response.horseIncomes[2].horseName).toBe(horse.horseName);
    expect(response.horseIncomes[2].closing).toBe(prize2.closing);
    expect(response.horseIncomes[2].incomeYearMonth).toBe(prize2.incomeYearMonth);
    expect(response.horseIncomes[2].occurredYear).toBe(prize2.occurredYear);
    expect(response.horseIncomes[2].occurredMonth).toBe(prize2.occurredMonth);
    expect(response.horseIncomes[2].occurredDay).toBe(prize2.occurredDay);
    expect(response.horseIncomes[2].name).toBe(prize2.raceName);
    expect(response.horseIncomes[2].name2).toBe(prize2.raceResult);
    expect(response.horseIncomes[2].amount).toBe(prize2.incomeAmount);
  });

  it('limitとoffsetが正しく動作する', async () => {
    // 複数のテストデータ作成
    for (let i = 1; i <= 5; i++) {
      await HorseIncomePrizeFactory.create({
        horseId: horse.horseId,
        incomeYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: i,
      });
    }

    // 100msec待つ
    await new Promise(resolve => setTimeout(resolve, 100));

    // limit=2, offset=1でテスト
    const request = create(ListHorseIncomesRequestSchema, {
      horseId: horse.horseId,
      limit: 2,
      offset: 1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseIncomes(request, { headers });

    expect(response).toBeDefined();
    expect(response.horseIncomes).toHaveLength(2);
    expect(response.totalCount).toBe(5);
    expect(response.totalPages).toBe(3);
  });

  it('存在しない馬IDの場合は空の配列を返す', async () => {
    const request = create(ListHorseIncomesRequestSchema, {
      horseId: 99999,
      limit: 10,
      offset: 0,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseIncomes(request, { headers });

    expect(response).toBeDefined();
    expect(response.horseIncomes).toHaveLength(0);
  });

  it('無効なhorseIdでエラーになる', async () => {
    const request = create(ListHorseIncomesRequestSchema, {
      horseId: -1,
      limit: 10,
      offset: 0,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.listHorseIncomes(request, { headers })).rejects.toThrow();
  });

  it('無効なlimitでエラーになる', async () => {
    const request = create(ListHorseIncomesRequestSchema, {
      horseId: horse.horseId,
      limit: 101, // 最大値100を超える
      offset: 0,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.listHorseIncomes(request, { headers })).rejects.toThrow();
  });

  it('無効なoffsetでエラーになる', async () => {
    const request = create(ListHorseIncomesRequestSchema, {
      horseId: horse.horseId,
      limit: 10,
      offset: -1, // 負の値
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.listHorseIncomes(request, { headers })).rejects.toThrow();
  });
}); 