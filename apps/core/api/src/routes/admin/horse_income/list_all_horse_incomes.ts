import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { z } from 'zod';
import { ListAllHorseIncomesResponseSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { listAllHorseIncomes } from '@core-api/repositories/horse_income_repository';
import { createHandler } from '@core-api/utils/handler_factory';

export const listAllHorseIncomesHandler = createHandler({
  schema: z.object({
    startYear: z.number().int().min(1900).max(2100),
    startMonth: z.number().int().min(1).max(12),
    startDay: z.number().int().min(1).max(31),
    endYear: z.number().int().min(1900).max(2100),
    endMonth: z.number().int().min(1).max(12),
    endDay: z.number().int().min(1).max(31),
  }),
  business: (params) => {
    return listAllHorseIncomes(params);
  },
  toResponse: ({ incomes }) =>
    create(ListAllHorseIncomesResponseSchema, {
      horseIncomes: incomes,
    }),
  toError: (_error) => new ConnectError('Failed to list all horse incomes', Code.Internal),
}); 