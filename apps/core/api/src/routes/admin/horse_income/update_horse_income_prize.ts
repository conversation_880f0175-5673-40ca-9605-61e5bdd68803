import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { UpdateHorseIncomePrizeResponseSchema, HorseIncomePrizeOrganizer } from '@hami/core-admin-api-schema/horse_income_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { updateHorseIncomePrize, HorseIncomePrizeNotFoundError } from '@core-api/repositories/horse_income_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const updateHorseIncomePrizeHandler = createHandler({
  schema: z.object({
    horseIncomePrizeId: z.number().int().positive(),
    horseId: z.number().int().positive(),
    incomeYearMonth: z.number().int().positive(),
    occurredYear: z.number().int().positive(),
    occurredMonth: z.number().int().min(1).max(12),
    occurredDay: z.number().int().min(1).max(31),
    racePlace: z.string().min(1),
    raceName: z.string().min(1),
    raceResult: z.string().min(1),
    organizer: z.nativeEnum(HorseIncomePrizeOrganizer),
    mainPrizeAmount: z.number().int().min(0),
    appearanceFee: z.number().int().min(0),
    withholdingTax: z.number().int().min(0),
    commissionAmount: z.number().int().min(0),
    clubFeeRate: z.string(),
    taxRate: z.string(),
    totalPrizeAmount: z.number().int().min(0),
    clubFeeAmount: z.number().int().min(0),
    taxAmount: z.number().int().min(0),
    incomeAmount: z.number().int().min(0),
    note: z.string(),
    allowances: z.array(
      z.object({
        name: z.string().min(1),
        amount: z.number().int().min(0),
      })
    ),
  }),
  business: (params) => {
    const { horseIncomePrizeId, ...data } = params;
    return updateHorseIncomePrize(horseIncomePrizeId, data);
  },
  toResponse: () => create(UpdateHorseIncomePrizeResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseIncomePrizeNotFoundError), () => new ConnectError('Horse income prize not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
