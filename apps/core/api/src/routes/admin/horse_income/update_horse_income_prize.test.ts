import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { HorseIncomePrizeFactory } from '@core-test/factories/horse_income_prize_factory';
import { getClient } from '@core-test/index';
import {
  HorseIncomeService,
  HorseIncomePrizeOrganizer,
  UpdateHorseIncomePrizeRequestSchema,
} from '@hami/core-admin-api-schema/horse_income_service_pb';
import type { AdminUserSession, Horse, HorseIncomePrize } from '@hami/prisma';

describe('updateHorseIncomePrize API', () => {
  const apiClient = getClient(HorseIncomeService);
  let adminSession: AdminUserSession;
  let horse: Horse;
  let prize: HorseIncomePrize;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse = await HorseFactory.create();
    prize = await HorseIncomePrizeFactory.create({
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '東京競馬場',
      raceName: '東京優駿',
      raceResult: '1着',
    });
  });

  it('正常に馬の賞金収入を更新できる', async () => {
    const request = create(UpdateHorseIncomePrizeRequestSchema, {
      horseIncomePrizeId: prize.horseIncomePrizeId,
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '京都競馬場', // 変更
      raceName: '菊花賞', // 変更
      raceResult: '2着', // 変更
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount: 800000, // 変更
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: "0.1",
      taxRate: "0.2",
      totalPrizeAmount: 900000, // 変更
      clubFeeAmount: 90000, // 変更
      taxAmount: 180000, // 変更
      incomeAmount: 630000, // 変更
      note: '更新されたテスト用データ', // 変更
      allowances: [
        { name: '特別手当', amount: 50000 }, // 変更
      ],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.updateHorseIncomePrize(request, { headers });

    expect(response).toBeDefined();

    // データベースに更新されていることを確認
    const updatedPrize = await vPrisma.client.horseIncomePrize.findUnique({
      where: { horseIncomePrizeId: prize.horseIncomePrizeId },
      include: { allowances: true },
    });
    expect(updatedPrize).toBeDefined();

    // 全項目をチェック
    expect(updatedPrize!.horseIncomePrizeId).toBe(prize.horseIncomePrizeId);
    expect(updatedPrize!.horseId).toBe(horse.horseId);
    expect(updatedPrize!.incomeYearMonth).toBe(202412);
    expect(updatedPrize!.occurredYear).toBe(2024);
    expect(updatedPrize!.occurredMonth).toBe(12);
    expect(updatedPrize!.occurredDay).toBe(1);
    expect(updatedPrize!.racePlace).toBe('京都競馬場');
    expect(updatedPrize!.raceName).toBe('菊花賞');
    expect(updatedPrize!.raceResult).toBe('2着');
    expect(updatedPrize!.organizer).toBe('JRA'); // 文字列として比較
    expect(updatedPrize!.mainPrizeAmount).toBe(800000);
    expect(updatedPrize!.appearanceFee).toBe(50000);
    expect(updatedPrize!.withholdingTax).toBe(100000);
    expect(updatedPrize!.commissionAmount).toBe(50000);
    expect(updatedPrize!.clubFeeRate.toNumber()).toBe(0.1);
    expect(updatedPrize!.taxRate.toNumber()).toBe(0.2);
    expect(updatedPrize!.totalPrizeAmount).toBe(900000);
    expect(updatedPrize!.clubFeeAmount).toBe(90000);
    expect(updatedPrize!.taxAmount).toBe(180000);
    expect(updatedPrize!.incomeAmount).toBe(630000);
    expect(updatedPrize!.note).toBe('更新されたテスト用データ');
    expect(updatedPrize!.closing).toBe(false); // デフォルト値
    expect(updatedPrize!.createdAt).toBeDefined();
    expect(updatedPrize!.updatedAt).toBeDefined();

    // 手当の確認
    expect(updatedPrize!.allowances).toHaveLength(1);
    const allowance = updatedPrize!.allowances[0];
    expect(allowance.name).toBe('特別手当');
    expect(allowance.amount).toBe(50000);
  });

  it('存在しないIDの場合はエラーになる', async () => {
    const request = create(UpdateHorseIncomePrizeRequestSchema, {
      horseIncomePrizeId: 99999,
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '京都競馬場',
      raceName: '菊花賞',
      raceResult: '2着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount: 800000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: "0.1",
      taxRate: "0.2",
      totalPrizeAmount: 900000,
      clubFeeAmount: 90000,
      taxAmount: 180000,
      incomeAmount: 630000,
      note: '更新されたテスト用データ',
      allowances: [],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorseIncomePrize(request, { headers })).rejects.toThrow();
  });

  it('無効なhorseIdでエラーになる', async () => {
    const request = create(UpdateHorseIncomePrizeRequestSchema, {
      horseIncomePrizeId: prize.horseIncomePrizeId,
      horseId: -1, // 無効なhorseId
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '京都競馬場',
      raceName: '菊花賞',
      raceResult: '2着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount: 800000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: "0.1",
      taxRate: "0.2",
      totalPrizeAmount: 900000,
      clubFeeAmount: 90000,
      taxAmount: 180000,
      incomeAmount: 630000,
      note: '更新されたテスト用データ',
      allowances: [],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorseIncomePrize(request, { headers })).rejects.toThrow();
  });

  it('無効な月でエラーになる', async () => {
    const request = create(UpdateHorseIncomePrizeRequestSchema, {
      horseIncomePrizeId: prize.horseIncomePrizeId,
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 13, // 無効な月
      occurredDay: 1,
      racePlace: '京都競馬場',
      raceName: '菊花賞',
      raceResult: '2着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount: 800000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: "0.1",
      taxRate: "0.2",
      totalPrizeAmount: 900000,
      clubFeeAmount: 90000,
      taxAmount: 180000,
      incomeAmount: 630000,
      note: '更新されたテスト用データ',
      allowances: [],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorseIncomePrize(request, { headers })).rejects.toThrow();
  });
});
