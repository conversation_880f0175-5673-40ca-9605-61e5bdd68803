import { HorseIncomeService } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { createHorseIncomeOtherHandler } from './create_horse_income_other';
import { createHorseIncomePrizeHandler } from './create_horse_income_prize';
import { deleteHorseIncomeOtherHandler } from './delete_horse_income_other';
import { deleteHorseIncomePrizeHandler } from './delete_horse_income_prize';
import { getHorseIncomeOtherHandler } from './get_horse_income_other';
import { getHorseIncomePrizeHandler } from './get_horse_income_prize';
import { listAllHorseIncomesHandler } from './list_all_horse_incomes';
import { listHorseIncomePrizeAllowanceNamesHandler } from './list_horse_income_prize_allowance_names';
import { listHorseIncomesHandler } from './list_horse_incomes';
import { updateHorseIncomeOtherHandler } from './update_horse_income_other';
import { updateHorseIncomePrizeHandler } from './update_horse_income_prize';

import type { ConnectRouter } from '@connectrpc/connect';

export const implHorseIncomeService = (router: ConnectRouter) =>
  router.service(
    HorseIncomeService,
    {
      listHorseIncomes: unwrapResult(listHorseIncomesHandler),
      listAllHorseIncomes: unwrapResult(listAllHorseIncomesHandler),
      getHorseIncomePrize: unwrapResult(getHorseIncomePrizeHandler),
      createHorseIncomePrize: unwrapResult(createHorseIncomePrizeHandler),
      updateHorseIncomePrize: unwrapResult(updateHorseIncomePrizeHandler),
      deleteHorseIncomePrize: unwrapResult(deleteHorseIncomePrizeHandler),
      getHorseIncomeOther: unwrapResult(getHorseIncomeOtherHandler),
      createHorseIncomeOther: unwrapResult(createHorseIncomeOtherHandler),
      updateHorseIncomeOther: unwrapResult(updateHorseIncomeOtherHandler),
      deleteHorseIncomeOther: unwrapResult(deleteHorseIncomeOtherHandler),
      listHorseIncomePrizeAllowanceNames: unwrapResult(listHorseIncomePrizeAllowanceNamesHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  ); 