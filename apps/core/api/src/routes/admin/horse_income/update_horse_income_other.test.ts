import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { HorseIncomeOtherFactory } from '@core-test/factories/horse_income_other_factory';
import { getClient } from '@core-test/index';
import {
  HorseIncomeService,
  HorseIncomeOtherName,
  UpdateHorseIncomeOtherRequestSchema,
} from '@hami/core-admin-api-schema/horse_income_service_pb';
import type { AdminUserSession, Horse, HorseIncomeOther } from '@hami/prisma';

describe('updateHorseIncomeOther API', () => {
  const apiClient = getClient(HorseIncomeService);
  let adminSession: AdminUserSession;
  let horse: Horse;
  let other: HorseIncomeOther;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse = await HorseFactory.create();
    other = await HorseIncomeOtherFactory.create({
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      nameOther: 'テスト用その他収入',
      amount: 500000,
    });
  });

  it('正常に馬のその他収入を更新できる', async () => {
    const request = create(UpdateHorseIncomeOtherRequestSchema, {
      horseIncomeOtherId: other.horseIncomeOtherId,
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      name: HorseIncomeOtherName.INSURANCE, // 変更
      nameOther: '更新されたその他収入', // 変更
      amount: 600000, // 変更
      salesCommission: 30000, // 変更
      otherFeeName: '更新された手数料', // 変更
      otherFeeAmount: 15000, // 変更
      taxRate: '0.25', // 変更
      taxAmount: 150000, // 変更
      incomeAmount: 405000, // 変更
      note: '更新されたテスト用データ', // 変更
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.updateHorseIncomeOther(request, { headers });

    expect(response).toBeDefined();

    // データベースに更新されていることを確認
    const updatedOther = await vPrisma.client.horseIncomeOther.findUnique({
      where: { horseIncomeOtherId: other.horseIncomeOtherId },
    });
    expect(updatedOther).toBeDefined();

    // 全項目をチェック
    expect(updatedOther!.horseIncomeOtherId).toBe(other.horseIncomeOtherId);
    expect(updatedOther!.horseId).toBe(horse.horseId);
    expect(updatedOther!.incomeYearMonth).toBe(202412);
    expect(updatedOther!.occurredYear).toBe(2024);
    expect(updatedOther!.occurredMonth).toBe(12);
    expect(updatedOther!.occurredDay).toBe(1);
    expect(updatedOther!.name).toBe('INSURANCE'); // 文字列として比較
    expect(updatedOther!.nameOther).toBe('更新されたその他収入');
    expect(updatedOther!.amount).toBe(600000);
    expect(updatedOther!.salesCommission).toBe(30000);
    expect(updatedOther!.otherFeeName).toBe('更新された手数料');
    expect(updatedOther!.otherFeeAmount).toBe(15000);
    expect(updatedOther!.taxRate.toNumber()).toBe(0.25);
    expect(updatedOther!.taxAmount).toBe(150000);
    expect(updatedOther!.incomeAmount).toBe(405000);
    expect(updatedOther!.note).toBe('更新されたテスト用データ');
    expect(updatedOther!.closing).toBe(false); // デフォルト値
    expect(updatedOther!.createdAt).toBeDefined();
    expect(updatedOther!.updatedAt).toBeDefined();
  });

  it('存在しないIDの場合はエラーになる', async () => {
    const request = create(UpdateHorseIncomeOtherRequestSchema, {
      horseIncomeOtherId: 99999,
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      name: HorseIncomeOtherName.INSURANCE,
      nameOther: '更新されたその他収入',
      amount: 600000,
      salesCommission: 30000,
      otherFeeName: '更新された手数料',
      otherFeeAmount: 15000,
      taxRate: '0.25',
      taxAmount: 150000,
      incomeAmount: 405000,
      note: '更新されたテスト用データ',
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorseIncomeOther(request, { headers })).rejects.toThrow();
  });

  it('無効なhorseIdでエラーになる', async () => {
    const request = create(UpdateHorseIncomeOtherRequestSchema, {
      horseIncomeOtherId: other.horseIncomeOtherId,
      horseId: -1, // 無効なhorseId
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      name: HorseIncomeOtherName.INSURANCE,
      nameOther: '更新されたその他収入',
      amount: 600000,
      salesCommission: 30000,
      otherFeeName: '更新された手数料',
      otherFeeAmount: 15000,
      taxRate: '0.25',
      taxAmount: 150000,
      incomeAmount: 405000,
      note: '更新されたテスト用データ',
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorseIncomeOther(request, { headers })).rejects.toThrow();
  });

  it('無効な月でエラーになる', async () => {
    const request = create(UpdateHorseIncomeOtherRequestSchema, {
      horseIncomeOtherId: other.horseIncomeOtherId,
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 13, // 無効な月
      occurredDay: 1,
      name: HorseIncomeOtherName.INSURANCE,
      nameOther: '更新されたその他収入',
      amount: 600000,
      salesCommission: 30000,
      otherFeeName: '更新された手数料',
      otherFeeAmount: 15000,
      taxRate: '0.25',
      taxAmount: 150000,
      incomeAmount: 405000,
      note: '更新されたテスト用データ',
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorseIncomeOther(request, { headers })).rejects.toThrow();
  });
});
