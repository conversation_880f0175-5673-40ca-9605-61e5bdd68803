import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { HorseIncomePrizeFactory } from '@core-test/factories/horse_income_prize_factory';
import { getClient } from '@core-test/index';
import { HorseIncomeService, GetHorseIncomePrizeRequestSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';
import type { AdminUserSession, Horse, HorseIncomePrize } from '@hami/prisma';

describe('getHorseIncomePrize API', () => {
  const apiClient = getClient(HorseIncomeService);
  let adminSession: AdminUserSession;
  let horse: Horse;
  let prize: HorseIncomePrize;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse = await HorseFactory.create();
    prize = await HorseIncomePrizeFactory.create({
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '東京競馬場',
      raceName: '東京優駿',
      raceResult: '1着',
    });
  });

  it('正常に馬の賞金収入を取得できる', async () => {
    const request = create(GetHorseIncomePrizeRequestSchema, {
      id: prize.horseIncomePrizeId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.getHorseIncomePrize(request, { headers });

    expect(response).toBeDefined();
    expect(response.horseIncomePrize).toBeDefined();
    expect(response.horseIncomePrize!.horseIncomePrizeId).toBe(prize.horseIncomePrizeId);
    expect(response.horseIncomePrize!.horseId).toBe(horse.horseId);
    expect(response.horseIncomePrize!.racePlace).toBe('東京競馬場');
    expect(response.horseIncomePrize!.raceName).toBe('東京優駿');
    expect(response.horseIncomePrize!.raceResult).toBe('1着');
  });

  it('存在しないIDの場合はエラーになる', async () => {
    const request = create(GetHorseIncomePrizeRequestSchema, {
      id: 99999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.getHorseIncomePrize(request, { headers })).rejects.toThrow();
  });

  it('無効なIDでエラーになる', async () => {
    const request = create(GetHorseIncomePrizeRequestSchema, {
      id: -1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.getHorseIncomePrize(request, { headers })).rejects.toThrow();
  });
});
