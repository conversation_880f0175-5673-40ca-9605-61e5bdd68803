import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetHorseIncomePrizeResponseSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { getHorseIncomePrize, HorseIncomePrizeNotFoundError } from '@core-api/repositories/horse_income_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getHorseIncomePrizeHandler = createHandler({
  schema: z.object({
    id: z.number().int().positive(),
  }),
  business: (params) => {
    const { id } = params;
    return getHorseIncomePrize({ id });
  },
  toResponse: (prize) =>
    create(GetHorseIncomePrizeResponseSchema, {
      horseIncomePrize: prize,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseIncomePrizeNotFoundError), () => new ConnectError('Horse income prize not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
