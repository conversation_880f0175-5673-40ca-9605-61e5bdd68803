import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { getClient } from '@core-test/index';
import {
  HorseIncomeService,
  HorseIncomePrizeOrganizer,
  CreateHorseIncomePrizeRequestSchema,
} from '@hami/core-admin-api-schema/horse_income_service_pb';
import type { AdminUserSession, Horse } from '@hami/prisma';

describe('createHorseIncomePrize API', () => {
  const apiClient = getClient(HorseIncomeService);
  let adminSession: AdminUserSession;
  let horse: Horse;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse = await HorseFactory.create();
  });

  it('正常に馬の賞金収入を作成できる', async () => {
    const request = create(CreateHorseIncomePrizeRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '東京競馬場',
      raceName: '東京優駿',
      raceResult: '1着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount: 1000000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: '0.1',
      taxRate: '0.2',
      totalPrizeAmount: 1100000,
      clubFeeAmount: 110000,
      taxAmount: 220000,
      incomeAmount: 770000,
      note: 'テスト用データ',
      allowances: [
        { name: '特別手当', amount: 100000 },
        { name: '交通費', amount: 50000 },
      ],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.createHorseIncomePrize(request, { headers });

    expect(response).toBeDefined();

    // データベースに保存されていることを確認
    const savedPrize = await vPrisma.client.horseIncomePrize.findFirst({
      where: { horseId: horse.horseId },
      include: { allowances: true },
    });
    expect(savedPrize).toBeDefined();

    // 全項目をチェック
    expect(savedPrize!.horseId).toBe(horse.horseId);
    expect(savedPrize!.incomeYearMonth).toBe(202412);
    expect(savedPrize!.occurredYear).toBe(2024);
    expect(savedPrize!.occurredMonth).toBe(12);
    expect(savedPrize!.occurredDay).toBe(1);
    expect(savedPrize!.racePlace).toBe('東京競馬場');
    expect(savedPrize!.raceName).toBe('東京優駿');
    expect(savedPrize!.raceResult).toBe('1着');
    expect(savedPrize!.organizer).toBe('JRA'); // 文字列として比較
    expect(savedPrize!.mainPrizeAmount).toBe(1000000);
    expect(savedPrize!.appearanceFee).toBe(50000);
    expect(savedPrize!.withholdingTax).toBe(100000);
    expect(savedPrize!.commissionAmount).toBe(50000);
    expect(savedPrize!.clubFeeRate.toNumber()).toBe(0.1);
    expect(savedPrize!.taxRate.toNumber()).toBe(0.2);
    expect(savedPrize!.totalPrizeAmount).toBe(1100000);
    expect(savedPrize!.clubFeeAmount).toBe(110000);
    expect(savedPrize!.taxAmount).toBe(220000);
    expect(savedPrize!.incomeAmount).toBe(770000);
    expect(savedPrize!.note).toBe('テスト用データ');
    expect(savedPrize!.closing).toBe(false); // デフォルト値
    expect(savedPrize!.createdAt).toBeDefined();
    expect(savedPrize!.updatedAt).toBeDefined();

    // 手当の確認
    expect(savedPrize!.allowances).toHaveLength(2);
    const allowance1 = savedPrize!.allowances.find((a: any) => a.name === '特別手当');
    const allowance2 = savedPrize!.allowances.find((a: any) => a.name === '交通費');
    expect(allowance1).toBeDefined();
    expect(allowance1!.amount).toBe(100000);
    expect(allowance2).toBeDefined();
    expect(allowance2!.amount).toBe(50000);
  });

  it('無効なhorseIdでエラーになる', async () => {
    const request = create(CreateHorseIncomePrizeRequestSchema, {
      horseId: -1,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '東京競馬場',
      raceName: '東京優駿',
      raceResult: '1着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount: 1000000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: '0.1',
      taxRate: '0.2',
      totalPrizeAmount: 1100000,
      clubFeeAmount: 110000,
      taxAmount: 220000,
      incomeAmount: 770000,
      note: 'テスト用データ',
      allowances: [],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorseIncomePrize(request, { headers })).rejects.toThrow();
  });

  it('無効な月でエラーになる', async () => {
    const request = create(CreateHorseIncomePrizeRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 13, // 無効な月
      occurredDay: 1,
      racePlace: '東京競馬場',
      raceName: '東京優駿',
      raceResult: '1着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount: 1000000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: '0.1',
      taxRate: '0.2',
      totalPrizeAmount: 1100000,
      clubFeeAmount: 110000,
      taxAmount: 220000,
      incomeAmount: 770000,
      note: 'テスト用データ',
      allowances: [],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorseIncomePrize(request, { headers })).rejects.toThrow();
  });

  it('無効な日でエラーになる', async () => {
    const request = create(CreateHorseIncomePrizeRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 32, // 無効な日
      racePlace: '東京競馬場',
      raceName: '東京優駿',
      raceResult: '1着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount: 1000000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: '0.1',
      taxRate: '0.2',
      totalPrizeAmount: 1100000,
      clubFeeAmount: 110000,
      taxAmount: 220000,
      incomeAmount: 770000,
      note: 'テスト用データ',
      allowances: [],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorseIncomePrize(request, { headers })).rejects.toThrow();
  });

  it('空のレース名でエラーになる', async () => {
    const request = create(CreateHorseIncomePrizeRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '東京競馬場',
      raceName: '', // 空の文字列
      raceResult: '1着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount: 1000000,
      appearanceFee: 50000,
      withholdingTax: 100000,
      commissionAmount: 50000,
      clubFeeRate: '0.1',
      taxRate: '0.2',
      totalPrizeAmount: 1100000,
      clubFeeAmount: 110000,
      taxAmount: 220000,
      incomeAmount: 770000,
      note: 'テスト用データ',
      allowances: [],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorseIncomePrize(request, { headers })).rejects.toThrow();
  });

  // Decimal型計算の正確性を検証するテストケース
  it('Decimal型を使用した正確な金融計算ができる', async () => {
    // 浮動小数点数の計算問題が起きやすい値を使用
    const mainPrizeAmount = 1000000;
    const allowanceAmount = 333333; // 3で割り切れない値
    const appearanceFee = 50000;
    const clubFeeRate = 0.333; // 3.33% - 浮動小数点数で問題が起きやすい
    const taxRate = 0.1; // 10%
    const commissionAmount = 50000;
    const withholdingTax = 100000;

    // Decimal型を使用した正確な計算
    const { Prisma } = require('@hami/prisma');
    const decimalMainPrize = new Prisma.Decimal(mainPrizeAmount);
    const decimalAllowance = new Prisma.Decimal(allowanceAmount);
    const decimalAppearanceFee = new Prisma.Decimal(appearanceFee);
    const decimalClubFeeRate = new Prisma.Decimal(clubFeeRate);
    const decimalTaxRate = new Prisma.Decimal(taxRate);
    const decimalCommissionAmount = new Prisma.Decimal(commissionAmount);
    const decimalWithholdingTax = new Prisma.Decimal(withholdingTax);

    const totalPrizeAmount = decimalMainPrize.add(decimalAllowance).add(decimalAppearanceFee);
    const clubFeeAmount = decimalMainPrize.add(decimalAllowance).mul(decimalClubFeeRate.div(100));
    const taxAmount = totalPrizeAmount
      .sub(clubFeeAmount)
      .sub(decimalCommissionAmount)
      .mul(decimalTaxRate.div(new Prisma.Decimal(100).add(decimalTaxRate)));
    const incomeAmount = totalPrizeAmount.sub(decimalWithholdingTax).sub(clubFeeAmount).sub(decimalCommissionAmount).sub(taxAmount);

    const request = create(CreateHorseIncomePrizeRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '東京競馬場',
      raceName: '東京優駿',
      raceResult: '1着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount,
      appearanceFee,
      withholdingTax,
      commissionAmount,
      clubFeeRate: clubFeeRate.toString(),
      taxRate: taxRate.toString(),
      totalPrizeAmount: Math.round(totalPrizeAmount.toNumber()),
      clubFeeAmount: Math.round(clubFeeAmount.toNumber()),
      taxAmount: Math.round(taxAmount.toNumber()),
      incomeAmount: Math.round(incomeAmount.toNumber()),
      note: 'Decimal型正確計算テスト',
      allowances: [{ name: '特別手当', amount: allowanceAmount }],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.createHorseIncomePrize(request, { headers });
    expect(response).toBeDefined();

    // データベースから取得
    const savedPrize = await vPrisma.client.horseIncomePrize.findFirst({
      where: { horseId: horse.horseId },
      orderBy: { createdAt: 'desc' },
    });
    expect(savedPrize).toBeDefined();

    console.log('=== Decimal型を使用した正確な計算結果 ===');
    console.log(`totalPrizeAmount: ${Math.round(totalPrizeAmount.toNumber())}`);
    console.log(`clubFeeAmount: ${Math.round(clubFeeAmount.toNumber())}`);
    console.log(`taxAmount: ${Math.round(taxAmount.toNumber())}`);
    console.log(`incomeAmount: ${Math.round(incomeAmount.toNumber())}`);

    // Decimal型で計算された正確な値が保存されていることを確認
    expect(savedPrize!.totalPrizeAmount).toBe(Math.round(totalPrizeAmount.toNumber()));
    expect(savedPrize!.clubFeeAmount).toBe(Math.round(clubFeeAmount.toNumber()));
    expect(savedPrize!.taxAmount).toBe(Math.round(taxAmount.toNumber()));
    expect(savedPrize!.incomeAmount).toBe(Math.round(incomeAmount.toNumber()));

    // Decimal型の精度が保たれていることを確認
    expect(savedPrize!.clubFeeRate.toNumber()).toBe(clubFeeRate);
    expect(savedPrize!.taxRate.toNumber()).toBe(taxRate);

    console.log('✅ Decimal型を使用した正確な金融計算が成功しました');
  });

  // 正確な計算結果を期待するテスト（現在の実装では落ちる可能性が高い）
  it('正確な金融計算結果と一致する（Decimal型計算を期待）', async () => {
    // より精度問題が起きやすい値を使用
    const mainPrizeAmount = 1000001; // 奇数
    const allowanceAmount = 333333; // 3で割り切れない値
    const appearanceFee = 50001; // 奇数
    const clubFeeRate = 0.1 + 0.2; // 0.30000000000000004 - 浮動小数点数の典型的な問題
    const taxRate = (0.7 * 3) / 10; // 2.0999999999999996 / 10 = 0.20999999999999996 - 複雑な計算
    const commissionAmount = 77777; // 7で割り切れない値
    const withholdingTax = 111111; // 複雑な値

    // Decimal型を使った正確な計算
    const { Prisma } = require('@hami/prisma');
    const decimalClubFeeRate = new Prisma.Decimal(clubFeeRate.toString());
    const decimalTaxRate = new Prisma.Decimal(taxRate.toString());

    const exactTotalPrizeAmount = mainPrizeAmount + allowanceAmount + appearanceFee;
    const exactClubFeeAmount = new Prisma.Decimal(mainPrizeAmount + allowanceAmount).mul(decimalClubFeeRate.div(100)).toNumber();
    const exactTaxAmount = new Prisma.Decimal(exactTotalPrizeAmount - exactClubFeeAmount - commissionAmount)
      .mul(decimalTaxRate.div(new Prisma.Decimal(100).add(decimalTaxRate)))
      .toNumber();
    const exactIncomeAmount = exactTotalPrizeAmount - withholdingTax - exactClubFeeAmount - commissionAmount - exactTaxAmount;

    // フロントエンドの浮動小数点数計算（問題のある計算、Math.floor()なし）
    const floatClubFeeAmount = (mainPrizeAmount + allowanceAmount) * (clubFeeRate / 100);
    const floatTaxAmount = (exactTotalPrizeAmount - floatClubFeeAmount - commissionAmount) * (taxRate / (100 + taxRate));
    const floatIncomeAmount = exactTotalPrizeAmount - withholdingTax - floatClubFeeAmount - commissionAmount - floatTaxAmount;

    console.log('=== 計算結果の比較 ===');
    console.log(
      `clubFeeAmount: 浮動小数点=${floatClubFeeAmount}, 正確=${exactClubFeeAmount}, 差=${Math.abs(floatClubFeeAmount - exactClubFeeAmount)}`
    );
    console.log(`taxAmount: 浮動小数点=${floatTaxAmount}, 正確=${exactTaxAmount}, 差=${Math.abs(floatTaxAmount - exactTaxAmount)}`);
    console.log(
      `incomeAmount: 浮動小数点=${floatIncomeAmount}, 正確=${exactIncomeAmount}, 差=${Math.abs(floatIncomeAmount - exactIncomeAmount)}`
    );

    // 現在の実装（浮動小数点数計算）でリクエストを作成
    const request = create(CreateHorseIncomePrizeRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '東京競馬場',
      raceName: '東京優駿',
      raceResult: '1着',
      organizer: HorseIncomePrizeOrganizer.JRA,
      mainPrizeAmount,
      appearanceFee,
      withholdingTax,
      commissionAmount,
      clubFeeRate: clubFeeRate.toString(),
      taxRate: taxRate.toString(),
      totalPrizeAmount: exactTotalPrizeAmount,
      clubFeeAmount: Math.round(floatClubFeeAmount), // 整数に丸める（データベースはInt型のため）
      taxAmount: Math.round(floatTaxAmount), // 整数に丸める（データベースはInt型のため）
      incomeAmount: Math.round(floatIncomeAmount), // 整数に丸める（データベースはInt型のため）
      note: '精度問題検証テスト',
      allowances: [{ name: '特別手当', amount: allowanceAmount }],
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.createHorseIncomePrize(request, { headers });
    expect(response).toBeDefined();

    // データベースから取得
    const savedPrize = await vPrisma.client.horseIncomePrize.findFirst({
      where: { horseId: horse.horseId },
      orderBy: { createdAt: 'desc' },
    });
    expect(savedPrize).toBeDefined();

    // 正確な計算結果と一致することを期待（このテストは現在の実装では落ちる可能性が高い）
    try {
      expect(savedPrize!.clubFeeAmount).toBe(exactClubFeeAmount);
      expect(savedPrize!.taxAmount).toBe(exactTaxAmount);
      expect(savedPrize!.incomeAmount).toBe(exactIncomeAmount);
      console.log('✅ 正確な計算結果と一致しました');
    } catch (error) {
      console.error('❌ 浮動小数点数の精度問題により正確な計算結果と一致しません');
      console.error(`期待値: clubFee=${exactClubFeeAmount}, tax=${exactTaxAmount}, income=${exactIncomeAmount}`);
      console.error(`実際値: clubFee=${savedPrize!.clubFeeAmount}, tax=${savedPrize!.taxAmount}, income=${savedPrize!.incomeAmount}`);

      // 現在の実装では浮動小数点数の計算結果（丸められた値）が保存されることを確認
      expect(savedPrize!.clubFeeAmount).toBe(Math.round(floatClubFeeAmount));
      expect(savedPrize!.taxAmount).toBe(Math.round(floatTaxAmount));
      expect(savedPrize!.incomeAmount).toBe(Math.round(floatIncomeAmount));

      // 精度問題があることを明確にするため、差異がある場合はテストを失敗させる
      const clubFeeDifference = Math.abs(floatClubFeeAmount - exactClubFeeAmount);
      const taxDifference = Math.abs(floatTaxAmount - exactTaxAmount);
      const incomeDifference = Math.abs(floatIncomeAmount - exactIncomeAmount);

      if (clubFeeDifference > 0.000001 || taxDifference > 0.000001 || incomeDifference > 0.000001) {
        throw new Error(
          `浮動小数点数の精度問題により計算結果に差異があります。clubFee差異=${clubFeeDifference}, tax差異=${taxDifference}, income差異=${incomeDifference}。Decimal型での計算実装が必要です。`
        );
      }
    }
  });
});
