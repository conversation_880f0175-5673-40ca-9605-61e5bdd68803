import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { getClient } from '@core-test/index';
import {
  HorseIncomeService,
  HorseIncomeOtherName,
  CreateHorseIncomeOtherRequestSchema,
} from '@hami/core-admin-api-schema/horse_income_service_pb';
import type { AdminUserSession, Horse } from '@hami/prisma';

describe('createHorseIncomeOther API', () => {
  const apiClient = getClient(HorseIncomeService);
  let adminSession: AdminUserSession;
  let horse: Horse;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse = await HorseFactory.create();
  });

  it('正常に馬のその他収入を作成できる', async () => {
    const request = create(CreateHorseIncomeOtherRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      name: HorseIncomeOtherName.SALES_DIVIDEND,
      nameOther: 'テスト用その他収入',
      amount: 500000,
      salesCommission: 25000,
      otherFeeName: '手数料',
      otherFeeAmount: 10000,
      taxRate: "0.2",
      taxAmount: 100000,
      incomeAmount: 365000,
      note: 'テスト用データ',
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.createHorseIncomeOther(request, { headers });

    expect(response).toBeDefined();

    // データベースに保存されていることを確認
    const savedOther = await vPrisma.client.horseIncomeOther.findFirst({
      where: { horseId: horse.horseId },
    });
    expect(savedOther).toBeDefined();

    // 全項目をチェック
    expect(savedOther!.horseId).toBe(horse.horseId);
    expect(savedOther!.incomeYearMonth).toBe(202412);
    expect(savedOther!.occurredYear).toBe(2024);
    expect(savedOther!.occurredMonth).toBe(12);
    expect(savedOther!.occurredDay).toBe(1);
    expect(savedOther!.name).toBe('SALES_DIVIDEND'); // 文字列として比較
    expect(savedOther!.nameOther).toBe('テスト用その他収入');
    expect(savedOther!.amount).toBe(500000);
    expect(savedOther!.salesCommission).toBe(25000);
    expect(savedOther!.otherFeeName).toBe('手数料');
    expect(savedOther!.otherFeeAmount).toBe(10000);
    expect(savedOther!.taxRate.toNumber()).toBe(0.2);
    expect(savedOther!.taxAmount).toBe(100000);
    expect(savedOther!.incomeAmount).toBe(365000);
    expect(savedOther!.note).toBe('テスト用データ');
    expect(savedOther!.closing).toBe(false); // デフォルト値
    expect(savedOther!.createdAt).toBeDefined();
    expect(savedOther!.updatedAt).toBeDefined();
  });

  it('無効なhorseIdでエラーになる', async () => {
    const request = create(CreateHorseIncomeOtherRequestSchema, {
      horseId: -1,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      name: HorseIncomeOtherName.SALES_DIVIDEND,
      nameOther: 'テスト用その他収入',
      amount: 500000,
      salesCommission: 25000,
      otherFeeName: '手数料',
      otherFeeAmount: 10000,
      taxRate: "0.2",
      taxAmount: 100000,
      incomeAmount: 365000,
      note: 'テスト用データ',
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorseIncomeOther(request, { headers })).rejects.toThrow();
  });

  it('無効な月でエラーになる', async () => {
    const request = create(CreateHorseIncomeOtherRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 13, // 無効な月
      occurredDay: 1,
      name: HorseIncomeOtherName.SALES_DIVIDEND,
      nameOther: 'テスト用その他収入',
      amount: 500000,
      salesCommission: 25000,
      otherFeeName: '手数料',
      otherFeeAmount: 10000,
      taxRate: "0.2",
      taxAmount: 100000,
      incomeAmount: 365000,
      note: 'テスト用データ',
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorseIncomeOther(request, { headers })).rejects.toThrow();
  });

  it('無効な日でエラーになる', async () => {
    const request = create(CreateHorseIncomeOtherRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 32, // 無効な日
      name: HorseIncomeOtherName.SALES_DIVIDEND,
      nameOther: 'テスト用その他収入',
      amount: 500000,
      salesCommission: 25000,
      otherFeeName: '手数料',
      otherFeeAmount: 10000,
      taxRate: "0.2",
      taxAmount: 100000,
      incomeAmount: 365000,
      note: 'テスト用データ',
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorseIncomeOther(request, { headers })).rejects.toThrow();
  });

  it('負の金額でエラーになる', async () => {
    const request = create(CreateHorseIncomeOtherRequestSchema, {
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      name: HorseIncomeOtherName.SALES_DIVIDEND,
      nameOther: 'テスト用その他収入',
      amount: -1000, // 負の金額
      salesCommission: 25000,
      otherFeeName: '手数料',
      otherFeeAmount: 10000,
      taxRate: "0.2",
      taxAmount: 100000,
      incomeAmount: 365000,
      note: 'テスト用データ',
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorseIncomeOther(request, { headers })).rejects.toThrow();
  });
});
