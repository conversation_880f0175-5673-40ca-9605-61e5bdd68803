import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { CreateHorseIncomeOtherResponseSchema, HorseIncomeOtherName } from '@hami/core-admin-api-schema/horse_income_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { createHorseIncomeOther } from '@core-api/repositories/horse_income_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const createHorseIncomeOtherHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
    incomeYearMonth: z.number().int().positive(),
    occurredYear: z.number().int().positive(),
    occurredMonth: z.number().int().min(1).max(12),
    occurredDay: z.number().int().min(1).max(31),
    name: z.nativeEnum(HorseIncomeOtherName),
    nameOther: z.string(),
    amount: z.number().int().min(0),
    salesCommission: z.number().int().min(0),
    otherFeeName: z.string(),
    otherFeeAmount: z.number().int().min(0),
    taxRate: z.string(),
    taxAmount: z.number().int().min(0),
    incomeAmount: z.number().int().min(0),
    note: z.string(),
  }),
  business: (params) => {
    return createHorseIncomeOther(params);
  },
  toResponse: () => create(CreateHorseIncomeOtherResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
