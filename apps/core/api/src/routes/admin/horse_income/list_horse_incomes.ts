import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ListHorseIncomesResponseSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listHorseIncomes } from '@core-api/repositories/horse_income_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const listHorseIncomesHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
    limit: z.number().int().positive().max(100).default(50),
    offset: z.number().int().min(0).default(0),
  }),
  business: (params) => {
    const { horseId, limit, offset } = params;
    return listHorseIncomes({ horseId, limit, offset });
  },
  toResponse: ({ incomes, totalCount, totalPages }) =>
    create(ListHorseIncomesResponseSchema, {
      horseIncomes: incomes,
      totalCount,
      totalPages,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 