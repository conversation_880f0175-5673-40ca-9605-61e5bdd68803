import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { HorseIncomePrizeFactory } from '@core-test/factories/horse_income_prize_factory';
import { getClient } from '@core-test/index';
import { HorseIncomeService, DeleteHorseIncomePrizeRequestSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';
import type { AdminUserSession, Horse, HorseIncomePrize } from '@hami/prisma';

describe('deleteHorseIncomePrize API', () => {
  const apiClient = getClient(HorseIncomeService);
  let adminSession: AdminUserSession;
  let horse: Horse;
  let prize: HorseIncomePrize;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse = await HorseFactory.create();
    prize = await HorseIncomePrizeFactory.create({
      horseId: horse.horseId,
      incomeYearMonth: 202412,
      occurredYear: 2024,
      occurredMonth: 12,
      occurredDay: 1,
      racePlace: '東京競馬場',
      raceName: '東京優駿',
      raceResult: '1着',
      allowances: [
        { name: '特別手当', amount: 100000 },
        { name: '交通費', amount: 50000 },
      ],
    });
  });

  it('正常に馬の賞金収入を削除できる', async () => {
    const request = create(DeleteHorseIncomePrizeRequestSchema, {
      id: prize.horseIncomePrizeId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.deleteHorseIncomePrize(request, { headers });

    expect(response).toBeDefined();

    // データベースから削除されていることを確認
    const deletedPrize = await vPrisma.client.horseIncomePrize.findUnique({
      where: { horseIncomePrizeId: prize.horseIncomePrizeId },
    });
    expect(deletedPrize).toBeNull();
  });

  it('存在しないIDの場合はエラーになる', async () => {
    const request = create(DeleteHorseIncomePrizeRequestSchema, {
      id: 99999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.deleteHorseIncomePrize(request, { headers })).rejects.toThrow();
  });

  it('無効なIDでエラーになる', async () => {
    const request = create(DeleteHorseIncomePrizeRequestSchema, {
      id: -1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.deleteHorseIncomePrize(request, { headers })).rejects.toThrow();
  });
});
