import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { getClient } from '@core-test/index';
import { HorseIncomeService, ListHorseIncomePrizeAllowanceNamesRequestSchema } from '@hami/core-admin-api-schema/horse_income_service_pb';
import type { AdminUserSession } from '@hami/prisma';

describe('listHorseIncomePrizeAllowanceNames API', () => {
  const apiClient = getClient(HorseIncomeService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
  });

  it('正常に賞金手当名一覧を取得できる', async () => {
    // テストデータ作成
    await vPrisma.client.horseIncomePrizeAllowanceName.createMany({
      data: [{ name: '特別手当' }, { name: '交通費' }, { name: '宿泊費' }],
    });

    const request = create(ListHorseIncomePrizeAllowanceNamesRequestSchema, {});

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseIncomePrizeAllowanceNames(request, { headers });

    expect(response).toBeDefined();
    expect(response.horseIncomePrizeAllowanceNames).toHaveLength(3);
    expect(response.horseIncomePrizeAllowanceNames.map((n: any) => n.name)).toContain('特別手当');
    expect(response.horseIncomePrizeAllowanceNames.map((n: any) => n.name)).toContain('交通費');
    expect(response.horseIncomePrizeAllowanceNames.map((n: any) => n.name)).toContain('宿泊費');
  });

  it('データが存在しない場合は空の配列を返す', async () => {
    // 既存データを削除
    await vPrisma.client.horseIncomePrizeAllowanceName.deleteMany();

    const request = create(ListHorseIncomePrizeAllowanceNamesRequestSchema, {});

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseIncomePrizeAllowanceNames(request, { headers });

    expect(response).toBeDefined();
    expect(response.horseIncomePrizeAllowanceNames).toHaveLength(0);
  });
});
