import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MailVerificationFactory } from '@core-test/factories/mail_verification_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { waitForCapturedMail } from '@core-test/mail_mock';
import { MembershipApplicationService, ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';

describe('reviewComplianceMembershipApplicationDocumentGroup', () => {
  it('APPROVE時にコンプライアンス審査承認メールが送信される', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `compliance-approve-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({ email: uniqueEmail });
    const application = await MembershipApplicationFactory.create({
      firstName: '太郎',
      lastName: '山田',
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    await client.reviewComplianceMembershipApplicationDocumentGroup(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.APPROVE,
        remandReason: '',
      },
      { headers }
    );

    // コンプライアンス審査承認メールが送信されることを確認
    const complianceMail = await waitForCapturedMail({
      to: uniqueEmail,
      subject: 'BHC | 【重要】入会審査結果のご連絡',
      timeoutMs: 1000,
    });

    expect(complianceMail).toBeDefined();
    expect(complianceMail?.text).toContain('山田 太郎 様');
    expect(complianceMail?.text).toContain('厳正なる審査の結果、入会を承認いたしました');
    expect(complianceMail?.text).toContain('https://bloominghorseclub.co.jp/signin');

    // Member.temporaryPassword
    const savedMember = await vPrisma.client.member.findFirst({
      where: { membershipApplicationId: application.membershipApplicationId },
    });
    expect(savedMember).toBeTruthy();
    expect(savedMember?.temporaryPassword).toMatch(/^[0-9a-f]{8}$/);
  }, 15000);

  it('REJECT時に拒否メールが送信される', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `compliance-reject-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({ email: uniqueEmail });
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    await client.reviewComplianceMembershipApplicationDocumentGroup(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REJECT,
        remandReason: '',
      },
      { headers }
    );

    const rejectionMail = await waitForCapturedMail({
      to: uniqueEmail,
      subject: 'BHC | 【重要】入会審査結果のご連絡',
      timeoutMs: 1000,
    });

    expect(rejectionMail).toBeDefined();
  }, 15000);

  it('REMAND時に差し戻しメールが送信される', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `compliance-remand-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({ email: uniqueEmail });
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    await client.reviewComplianceMembershipApplicationDocumentGroup(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REMAND,
        remandReason: '書類に不備があります',
      },
      { headers }
    );

    const remandMail = await waitForCapturedMail({
      to: uniqueEmail,
      subject: 'BHC | 【重要】入会申し込み情報の確認・修正のお願い',
      timeoutMs: 1000,
    });

    expect(remandMail).toBeDefined();
    expect(remandMail?.text).toContain('書類に不備があります');
  }, 15000);
});
