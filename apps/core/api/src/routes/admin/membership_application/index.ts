import { MembershipApplicationService } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { getIdentityDocumentImageUrl } from './get_identity_document_image_url';
import { getMembershipApplication } from './get_membership_application';
import { listMembershipApplications } from './list_membership_applications';
import { reviewMembershipApplication } from './review_membership_application';
import { reviewMembershipApplicationDocumentGroup } from './review_membership_application_document_group';
import { reviewComplianceMembershipApplication } from './review_compliance_membership_application';
import { reviewComplianceMembershipApplicationDocumentGroup } from './review_compliance_membership_application_document_group';
import { exportApprovedMembershipApplicationsCsv } from './export_approved_membership_applications_csv';

import type { ConnectRouter } from '@connectrpc/connect';

export const implMembershipApplicationService = (router: ConnectRouter) =>
  router.service(
    MembershipApplicationService,
    {
      getMembershipApplication: unwrapResult(getMembershipApplication),
      listMembershipApplications: unwrapResult(listMembershipApplications),
      reviewMembershipApplication: unwrapResult(reviewMembershipApplication),
      reviewMembershipApplicationDocumentGroup: unwrapResult(reviewMembershipApplicationDocumentGroup),
      reviewComplianceMembershipApplication: unwrapResult(reviewComplianceMembershipApplication),
      reviewComplianceMembershipApplicationDocumentGroup: unwrapResult(reviewComplianceMembershipApplicationDocumentGroup),
      getIdentityDocumentImageUrl: unwrapResult(getIdentityDocumentImageUrl),
      exportApprovedMembershipApplicationsCsv: unwrapResult(exportApprovedMembershipApplicationsCsv),
    },
    { interceptors: [adminUserAuthenticator] }
  );
