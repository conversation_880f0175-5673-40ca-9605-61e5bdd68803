import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok, ResultAsync, err, Result } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  ReviewComplianceMembershipApplicationResponseSchema,
  ReviewType,
} from '@hami/core-admin-api-schema/membership_application_service_pb';
import { ReviewType as ReviewTypePrisma } from '@hami/prisma';
import { config } from '@core-api/config/environment';
import { membershipRejectionTemplate } from '@core-api/mail_templates/membership_rejection';
import { membershipRemandTemplate } from '@core-api/mail_templates/membership_remand';
import { checkAdminUserExists, UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories';
import { createMembershipApplicationComplianceReviewLog } from '@core-api/repositories/membership_application_compliance_review_log_repository';
import {
  findMembershipApplicationById,
  MembershipApplicationNotFoundError,
} from '@core-api/repositories/membership_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { MailSendError, sendMail } from '@core-api/utils/mail';
import { ValidationError } from '@core-api/utils/validate_request';

const mapReviewType = (type: ReviewType): Result<ReviewTypePrisma, Error> => {
  switch (type) {
    case ReviewType.APPROVE:
      return ok(ReviewTypePrisma.APPROVE);
    case ReviewType.REJECT:
      return ok(ReviewTypePrisma.REJECT);
    case ReviewType.REMAND:
      return ok(ReviewTypePrisma.REMAND);
    default:
      return err(new Error('Invalid review type'));
  }
};

const sendComplianceResultEmail = (
  application: { mailVerification: { email: string; token: string }; firstName?: string | null; lastName?: string | null },
  reviewType: ReviewTypePrisma,
  remandReason: string
): ResultAsync<void, MailSendError> => {
  const email = application.mailVerification.email;
  switch (reviewType) {
    case ReviewTypePrisma.REJECT:
      return sendMail({
        to: email,
        template: membershipRejectionTemplate,
        params: {
          firstName: application.firstName || '',
          lastName: application.lastName || '',
        },
      });
    case ReviewTypePrisma.REMAND:
      return sendMail({
        to: email,
        template: membershipRemandTemplate,
        params: {
          firstName: application.firstName || '',
          lastName: application.lastName || '',
          fieldsToFix: remandReason,
          resubmitLink: `${config.frontendEndpoint}/join/application?token=${application.mailVerification.token}`,
        },
      });
    default:
      return ResultAsync.fromSafePromise(Promise.resolve());
  }
};

export const reviewComplianceMembershipApplication = createHandler({
  schema: z.object({
    membershipApplicationId: z.number(),
    reviewType: z.nativeEnum(ReviewType),
    remandReason: z.string(),
  }),
  business: (req, ctx) =>
    ok(req)
      .andThen(() => checkAdminUserExists(ctx).map((user) => ({ ...req, reviewer: user.name })))
      .asyncAndThen((reqWithReviewer) =>
        findMembershipApplicationById({ membershipApplicationId: reqWithReviewer.membershipApplicationId }).map((application) => ({
          reqWithReviewer,
          application,
        }))
      )
      .andThen(({ reqWithReviewer, application }) =>
        mapReviewType(reqWithReviewer.reviewType).asyncAndThen((reviewType) => {
          const { membershipApplicationId, remandReason, reviewer } = reqWithReviewer;
          const reviewData = { membershipApplicationId, reviewType, reviewer, remandReason };
          return createMembershipApplicationComplianceReviewLog(reviewData).andThen(() =>
            sendComplianceResultEmail(application, reviewType, remandReason)
          );
        })
      ),
  toResponse: () => create(ReviewComplianceMembershipApplicationResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(MembershipApplicationNotFoundError), () => new ConnectError('Not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Database error', Code.Internal))
      .with(P.instanceOf(UnauthorizedError), () => new ConnectError('Unauthorized', Code.Unauthenticated))
      .with(P.instanceOf(MailSendError), () => new ConnectError('メールの送信に失敗しました', Code.Internal))
      .otherwise(() => new ConnectError('Internal error', Code.Internal)),
});
