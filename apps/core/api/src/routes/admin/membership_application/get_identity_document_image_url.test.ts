import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { Code, ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory, MembershipApplicationIdentityDocumentFactory } from '@core-test/index';
import { MembershipApplicationService } from '@hami/core-admin-api-schema/membership_application_service_pb';

// 実際のS3クライアントを使用
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'ap-northeast-1',
  endpoint: process.env.S3_ENDPOINT || 'http://localhost:9000',
  forcePathStyle: true,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'minioadmin',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'minioadmin',
  },
});

const BUCKET_NAME = process.env.S3_BUCKET_NAME || 'test-bucket';

/**
 * テスト用ファイルをS3にアップロード
 */
async function uploadTestFile(key: string, content: string = 'test image content'): Promise<void> {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: Buffer.from(content),
    ContentType: 'image/jpeg',
  });

  await s3Client.send(command);
}

/**
 * S3からテストファイルを削除
 */
async function deleteTestFile(key: string): Promise<void> {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  });

  try {
    await s3Client.send(command);
  } catch (error) {
    // ファイルが存在しない場合は無視
    if ((error as any).name !== 'NoSuchKey') {
      throw error;
    }
  }
}

describe('getIdentityDocumentImageUrl', () => {
  it('指定したIDの身分証明書画像URLを取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const identityDocument = await MembershipApplicationIdentityDocumentFactory.create();
    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // S3に実際にファイルをアップロード
    await uploadTestFile(identityDocument.fileKey, 'test identity document image');

    try {
      // ===== Act =====
      const response = await client.getIdentityDocumentImageUrl(
        {
          identityDocumentId: identityDocument.membershipApplicationIdentityDocumentId,
        },
        { headers }
      );

      // ===== Assert =====
      expect(response.imageUrl).toBeDefined();
      expect(response.imageUrl).toContain(identityDocument.fileKey);
      expect(response.expiresAt).toBeDefined();
      expect(Number(response.expiresAt)).toBeGreaterThan(Date.now());
    } finally {
      // クリーンアップ
      await deleteTestFile(identityDocument.fileKey);
    }
  });

  it('存在しない身分証明書IDの場合はNotFoundエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MembershipApplicationService);
    const nonExistentId = 999999;
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.getIdentityDocumentImageUrl(
        {
          identityDocumentId: nonExistentId,
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeInstanceOf(ConnectError);
    expect((error as ConnectError).code).toBe(Code.NotFound);
  });

  it('S3にファイルが存在しない場合はNotFoundエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const identityDocument = await MembershipApplicationIdentityDocumentFactory.create();
    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // S3にファイルをアップロードしない（存在しない状態）

    // ===== Act/Assert =====
    let error;
    try {
      await client.getIdentityDocumentImageUrl(
        {
          identityDocumentId: identityDocument.membershipApplicationIdentityDocumentId,
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeInstanceOf(ConnectError);
    expect((error as ConnectError).code).toBe(Code.NotFound);
    expect((error as ConnectError).message).toContain('画像ファイルが存在しません');
  });

  it('不正なIDの場合はInvalidArgumentエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MembershipApplicationService);
    const invalidId = -1;
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error: unknown;
    try {
      await client.getIdentityDocumentImageUrl(
        {
          identityDocumentId: invalidId,
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeInstanceOf(ConnectError);
    expect((error as ConnectError).code).toBe(Code.InvalidArgument);
  });
});
