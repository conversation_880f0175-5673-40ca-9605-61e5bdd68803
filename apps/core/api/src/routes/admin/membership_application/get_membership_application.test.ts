import { Code, ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { MembershipApplicationReviewLogFactory } from '@core-test/factories/membership_application_review_log_factory';
import { MembershipApplicationDocumentGroupReviewLogFactory } from '@core-test/factories/membership_application_document_group_review_log_factory';
import { MembershipApplicationService, ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { ReviewType as ReviewTypePrisma } from '@hami/prisma';

describe('getMembershipApplication', () => {
  it('指定したIDの会員申込を取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const application = await MembershipApplicationFactory.create();
    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.getMembershipApplication(
      {
        membershipApplicationId: application.membershipApplicationId,
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.membershipApplication).toBeDefined();
    expect(response.membershipApplication?.membershipApplicationId).toBe(application.membershipApplicationId);
  });

  it('存在しない会員申込IDの場合はNotFoundエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MembershipApplicationService);
    const nonExistentId = 99999;
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.getMembershipApplication(
        {
          membershipApplicationId: nonExistentId,
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
      expect(error.message).toContain('Membership application not found');
    }
  });

  it('不正なIDの場合はInvalidArgumentエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MembershipApplicationService);
    const invalidId = -1;
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.getMembershipApplication(
        {
          membershipApplicationId: invalidId,
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('データベースエラーの場合はInternalエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MembershipApplicationService);
    const application = await MembershipApplicationFactory.create();
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    vi.spyOn(vPrisma.client.membershipApplication, 'findUnique').mockRejectedValueOnce(new Error('Failed to query membership application'));

    // ===== Act/Assert =====
    let error;
    try {
      await client.getMembershipApplication(
        {
          membershipApplicationId: application.membershipApplicationId,
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Internal);
      expect(error.message).toContain('Internal server error');
    }
  });

  it('差し戻し後に再提出された申込はNOT_REVIEWEDステータスになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    // 申込を作成
    const application = await MembershipApplicationFactory.create({
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
    });

    // 差し戻しのレビューログを作成
    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      reviewType: ReviewTypePrisma.REMAND,
      createdAt: new Date('2024-01-02T00:00:00Z'),
    });

    // 再提出（申込のupdatedAtを更新）
    await vPrisma.client.membershipApplication.update({
      where: { membershipApplicationId: application.membershipApplicationId },
      data: { updatedAt: new Date('2024-01-03T00:00:00Z') },
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.getMembershipApplication({ membershipApplicationId: application.membershipApplicationId }, { headers });

    // ===== Assert =====
    expect(response.membershipApplication?.applicationReviewStatus).toBe(ReviewType.NOT_REVIEWED);
  });
});

it('審査ログがレスポンスに含まれる（新しい順）', async ({ getClient }) => {
  // ===== Arrange =====
  const adminUserSession = await AdminUserSessionFactory.create();
  const application = await MembershipApplicationFactory.create();

  // 古いログ
  await MembershipApplicationReviewLogFactory.create({
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    reviewType: ReviewTypePrisma.REJECT,
    remandReason: '古いメモ',
    createdAt: new Date('2024-01-01T00:00:00Z'),
  });
  // 新しいログ
  await MembershipApplicationReviewLogFactory.create({
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    reviewType: ReviewTypePrisma.APPROVE,
    remandReason: '新しいメモ',
    createdAt: new Date('2024-02-01T00:00:00Z'),
  });

  const client = getClient(MembershipApplicationService);
  const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

  // ===== Act =====
  const response = await client.getMembershipApplication({ membershipApplicationId: application.membershipApplicationId }, { headers });

  // ===== Assert =====
  expect(response.applicationReviewLogs?.length).toBeGreaterThanOrEqual(2);
  // 降順で並んでいること（先頭が新しい）
  expect(response.applicationReviewLogs?.[0]?.reviewType).toBe(ReviewType.APPROVE);
  expect(response.applicationReviewLogs?.[0]?.remandReason).toBe('新しいメモ');
});

it('書類審査ログがレスポンスに含まれる（新しい順）', async ({ getClient }) => {
  // ===== Arrange =====
  const adminUserSession = await AdminUserSessionFactory.create();
  const application = await MembershipApplicationFactory.create();

  // 古いログ
  await MembershipApplicationDocumentGroupReviewLogFactory.create({
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    reviewType: ReviewTypePrisma.REJECT,
    remandReason: '古いメモ(書類)',
    createdAt: new Date('2024-03-01T00:00:00Z'),
  });
  // 新しいログ
  await MembershipApplicationDocumentGroupReviewLogFactory.create({
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    reviewType: ReviewTypePrisma.APPROVE,
    remandReason: '新しいメモ(書類)',
    createdAt: new Date('2024-04-01T00:00:00Z'),
  });

  const client = getClient(MembershipApplicationService);
  const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

  // ===== Act =====
  const response = await client.getMembershipApplication({ membershipApplicationId: application.membershipApplicationId }, { headers });

  // ===== Assert =====
  expect(response.documentGroupReviewLogs?.length).toBeGreaterThanOrEqual(2);
  // 降順で並んでいること（先頭が新しい）
  expect(response.documentGroupReviewLogs?.[0]?.reviewType).toBe(ReviewType.APPROVE);
  expect(response.documentGroupReviewLogs?.[0]?.remandReason).toBe('新しいメモ(書類)');
});
