import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { z } from 'zod';
import { ExportApprovedMembershipApplicationsCsvResponseSchema } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { exportApprovedMembershipApplicationsCsvUsecase } from '@core-api/usecases/export_approved_membership_applications_csv_usecase';
import { createHandler } from '@core-api/utils/handler_factory';
 

export const exportApprovedMembershipApplicationsCsv = createHandler({
  schema: z.object({}).optional().default({}),
  business: () =>
    exportApprovedMembershipApplicationsCsvUsecase(),
  toResponse: ({ fileKey, downloadUrl, total }) =>
    create(ExportApprovedMembershipApplicationsCsvResponseSchema, {
      csvFileKey: fileKey,
      downloadUrl,
      totalApplications: total,
    }),
  toError: (error) => {
    console.error('Export CSV error:', error);
    return new ConnectError('CSVの生成に失敗しました', Code.Internal);
  },
});


