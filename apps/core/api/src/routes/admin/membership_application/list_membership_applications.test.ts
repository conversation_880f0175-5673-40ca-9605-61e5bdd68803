import { Code, ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MembershipApplicationComplianceDocumentGroupReviewLogFactory } from '@core-test/factories/membership_application_compliance_document_group_review_log_factory';
import { MembershipApplicationComplianceReviewLogFactory } from '@core-test/factories/membership_application_compliance_review_log_factory';
import { MembershipApplicationDocumentGroupReviewLogFactory } from '@core-test/factories/membership_application_document_group_review_log_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { MembershipApplicationReviewLogFactory } from '@core-test/factories/membership_application_review_log_factory';
import { MembershipApplicationService, ReviewType, MembershipApplicationListStatus, } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { ReviewType as ReviewTypePrisma } from '@hami/prisma';

describe('listMembershipApplications', () => {
  it('会員申込一覧を取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const application1 = await MembershipApplicationFactory.create();
    const application2 = await MembershipApplicationFactory.create();
    // 新しいレビューログを作成
    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: application1.membershipApplicationId } },
      reviewType: ReviewTypePrisma.REMAND,
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMembershipApplications({}, { headers });

    // ===== Assert =====
    expect(response.membershipApplications).toHaveLength(2);
    const applications = response.membershipApplications;
    expect(applications).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          membershipApplicationId: application1.membershipApplicationId,
          applicationReviewStatus: ReviewType.REMAND,
          isPrinted: false,
        }),
        expect.objectContaining({
          membershipApplicationId: application2.membershipApplicationId,
          applicationReviewStatus: ReviewType.NOT_REVIEWED,
          isPrinted: false,
        }),
      ])
    );
  });

  it('会員申込が存在しない場合は空配列を返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMembershipApplications({}, { headers });

    // ===== Assert =====
    expect(response.membershipApplications).toHaveLength(0);
  });

  it('エラーが発生した場合はInternalエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    vi.spyOn(vPrisma.client.membershipApplication, 'findMany').mockRejectedValueOnce(new Error('Failed to query membership applications'));

    // ===== Act/Assert =====
    let error;
    try {
      await client.listMembershipApplications({}, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Internal);
      expect(error.message).toContain('Internal server error');
    }
  });

  it('ページネーション機能が正常に動作する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    // テストデータを5件作成
    await MembershipApplicationFactory.createList(5);

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    // 1ページ目（2件取得）
    const response1 = await client.listMembershipApplications({ page: 1, pageSize: 2 }, { headers });

    // 2ページ目（2件取得）
    const response2 = await client.listMembershipApplications({ page: 2, pageSize: 2 }, { headers });

    // 3ページ目（1件取得）
    const response3 = await client.listMembershipApplications({ page: 3, pageSize: 2 }, { headers });

    // ===== Assert =====
    expect(response1.membershipApplications).toHaveLength(2);
    expect(response1.hasNextPage).toBe(true);

    expect(response2.membershipApplications).toHaveLength(2);
    expect(response2.hasNextPage).toBe(true);

    expect(response3.membershipApplications).toHaveLength(1);
    expect(response3.hasNextPage).toBe(false);

    // 1ページ目と2ページ目で異なるデータが取得されることを確認
    const firstPageIds = response1.membershipApplications.map((app) => app.membershipApplicationId);
    const secondPageIds = response2.membershipApplications.map((app) => app.membershipApplicationId);

    expect(firstPageIds).not.toEqual(secondPageIds);
  });

  it('ページネーションなしの場合は全件取得される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    // テストデータを3件作成
    await MembershipApplicationFactory.createList(3);

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    // pageとpageSizeを指定しない場合
    const response = await client.listMembershipApplications({}, { headers });

    // ===== Assert =====
    // ページネーションなしなので全件取得され、hasNextPageはfalse
    expect(response.membershipApplications).toHaveLength(3);
    expect(response.hasNextPage).toBe(false);
  });

  it('デフォルト値が正常に適用される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    // テストデータを25件作成（デフォルトページサイズ20を超える）
    await MembershipApplicationFactory.createList(25);

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    // pageのみ指定（pageSizeはデフォルト20が適用される）
    const response = await client.listMembershipApplications({ page: 1 }, { headers });

    // ===== Assert =====
    // デフォルトページサイズ20で動作することを確認
    expect(response.membershipApplications).toHaveLength(20);
    expect(response.hasNextPage).toBe(true);
  });

  it('差し戻し後に再提出された申込はNOT_REVIEWEDステータスになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    // 申込を作成
    const application = await MembershipApplicationFactory.create({
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
    });

    // 差し戻しのレビューログを作成
    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      reviewType: ReviewTypePrisma.REMAND,
      createdAt: new Date('2024-01-02T00:00:00Z'),
    });

    // 再提出（申込のupdatedAtを更新）
    await vPrisma.client.membershipApplication.update({
      where: { membershipApplicationId: application.membershipApplicationId },
      data: { updatedAt: new Date('2024-01-03T00:00:00Z') },
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMembershipApplications({}, { headers });

    // ===== Assert =====
    expect(response.membershipApplications).toHaveLength(1);
    expect(response.membershipApplications[0].applicationReviewStatus).toBe(ReviewType.NOT_REVIEWED);
    expect(response.membershipApplications[0].isResubmittedAfterRemand).toBe(true);
  });

  it('statusFilter=ACTION_REQUIREDで未審査のみ取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const pendingApp = await MembershipApplicationFactory.create();
    const approvedApp = await MembershipApplicationFactory.create();

    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });
    // 書類側も承認済みにして「要対応」から除外されるようにする
    await MembershipApplicationDocumentGroupReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    const response = await client.listMembershipApplications(
      { statusFilter: MembershipApplicationListStatus.ACTION_REQUIRED },
      { headers }
    );

    // ===== Assert =====
    expect(response.membershipApplications).toHaveLength(1);
    expect(response.membershipApplications[0]?.membershipApplicationId).toBe(pendingApp.membershipApplicationId);
  });

  it('statusFilter=APPROVEDで全審査（申込/書類/コンプライアンス申込/コンプライアンス書類）が承認済みのみ取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const approvedApp = await MembershipApplicationFactory.create();
    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });
    await MembershipApplicationDocumentGroupReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });
    await MembershipApplicationComplianceReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });
    await MembershipApplicationComplianceDocumentGroupReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: approvedApp.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    const pendingApp = await MembershipApplicationFactory.create();

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMembershipApplications(
      { statusFilter: MembershipApplicationListStatus.APPROVED },
      { headers }
    );

    // ===== Assert =====
    const ids = response.membershipApplications.map((app) => app.membershipApplicationId);
    expect(ids).toContain(approvedApp.membershipApplicationId);
    expect(ids).not.toContain(pendingApp.membershipApplicationId);
  });

  it('statusFilter=SENTで印刷済みのみ取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const printedApp = await MembershipApplicationFactory.create({ isPrinted: true });
    await MembershipApplicationFactory.create({ isPrinted: false });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMembershipApplications(
      { statusFilter: MembershipApplicationListStatus.SENT },
      { headers }
    );

    // ===== Assert =====
    expect(response.membershipApplications).toHaveLength(1);
    expect(response.membershipApplications[0]?.membershipApplicationId).toBe(printedApp.membershipApplicationId);
    expect(response.membershipApplications[0]?.isPrinted).toBe(true);
  });

  it('statusFilter=COMPLIANCE_PENDINGでコンプラ審査待ちのみ取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const target = await MembershipApplicationFactory.create();
    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: target.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });
    await MembershipApplicationDocumentGroupReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: target.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });
    // コンプラ承認は作成しない

    const fullApproved = await MembershipApplicationFactory.create();
    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: fullApproved.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });
    await MembershipApplicationDocumentGroupReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: fullApproved.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });
    await MembershipApplicationComplianceReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: fullApproved.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });
    await MembershipApplicationComplianceDocumentGroupReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: fullApproved.membershipApplicationId } },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMembershipApplications(
      { statusFilter: MembershipApplicationListStatus.COMPLIANCE_PENDING },
      { headers }
    );

    // ===== Assert =====
    const ids = response.membershipApplications.map((app) => app.membershipApplicationId);
    expect(ids).toContain(target.membershipApplicationId);
    expect(ids).not.toContain(fullApproved.membershipApplicationId);
  });
});
