import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MailVerificationFactory } from '@core-test/factories/mail_verification_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { waitForCapturedMail } from '@core-test/mail_mock';
import { MembershipApplicationService, ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';

describe('reviewComplianceMembershipApplication', () => {
  it('コンプラ申込審査: REJECT で拒否メールが送信される', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `compliance-reject-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({ email: uniqueEmail });
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    await client.reviewComplianceMembershipApplication(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REJECT,
        remandReason: '',
      },
      { headers }
    );

    const rejectionMessage = await waitForCapturedMail({ to: uniqueEmail, subject: 'BHC | 【重要】入会審査結果のご連絡', timeoutMs: 5000 });
    expect(rejectionMessage).toBeDefined();
  });

  it('コンプラ申込審査: REMAND で差し戻しメールが送信される', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `compliance-remand-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({ email: uniqueEmail });
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    const remandReason = 'コンプラ差し戻し理由';

    await client.reviewComplianceMembershipApplication(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REMAND,
        remandReason,
      },
      { headers }
    );

    const remandMessage = await waitForCapturedMail({
      to: uniqueEmail,
      subject: 'BHC | 【重要】入会申し込み情報の確認・修正のお願い',
      timeoutMs: 5000,
    });
    expect(remandMessage).toBeDefined();
  });
});
