import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetIdentityDocumentImageUrlResponseSchema } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { DatabaseError } from '@core-api/repositories';
import {
  findIdentityDocumentById,
  IdentityDocumentNotFoundError,
  IdentityDocumentFileNotFoundError,
} from '@core-api/repositories/membership_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { getPresignedDownloadUrl, checkFileExists } from '@core-api/utils/s3';
import { ValidationError } from '@core-api/utils/validate_request';

export const getIdentityDocumentImageUrl = createHandler({
  schema: z.object({
    identityDocumentId: z.number().int().positive(),
  }),
  business: (req) =>
    findIdentityDocumentById({ identityDocumentId: req.identityDocumentId }).andThen((identityDocument) =>
      ResultAsync.fromPromise(
        (async () => {
          const fileExists = await checkFileExists(identityDocument.fileKey);
          if (!fileExists) {
            throw new IdentityDocumentFileNotFoundError(identityDocument.fileKey);
          }

          // 署名付きダウンロードURLを生成（1時間有効）
          const expiresIn = 60 * 60; // 1時間
          const imageUrl = await getPresignedDownloadUrl(identityDocument.fileKey, expiresIn);
          const expiresAt = BigInt(Date.now() + expiresIn * 1000);

          return {
            imageUrl,
            expiresAt,
          };
        })(),
        (error) => (error instanceof IdentityDocumentFileNotFoundError ? error : new DatabaseError(`画像URLの生成に失敗しました: ${error}`))
      )
    ),
  toResponse: ({ imageUrl, expiresAt }) =>
    create(GetIdentityDocumentImageUrlResponseSchema, {
      imageUrl,
      expiresAt,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(IdentityDocumentNotFoundError), () => new ConnectError('身分証明書が見つかりません', Code.NotFound))
      .with(P.instanceOf(IdentityDocumentFileNotFoundError), () => new ConnectError('画像ファイルが存在しません', Code.NotFound))
      .with(P.instanceOf(DatabaseError), (e) => {
        console.error('GetIdentityDocumentImageUrl internal error:', e);
        return new ConnectError('Internal server error', Code.Internal);
      })
      .exhaustive(),
});
