import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ReviewMembershipApplicationResponseSchema, ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { ReviewType as ReviewTypePrisma } from '@hami/prisma';
import { config } from '@core-api/config/environment';
import { membershipRejectionTemplate } from '@core-api/mail_templates/membership_rejection';
import { membershipRemandTemplate } from '@core-api/mail_templates/membership_remand';

import { checkAdminUserExists, UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories';
import {
  findMembershipApplicationById,
  MembershipApplicationNotFoundError,
} from '@core-api/repositories/membership_application_repository';
import { createMembershipApplicationReviewLog } from '@core-api/repositories/membership_application_review_log_repository';
import { createHandler } from '@core-api/utils/handler_factory';

import { MailSendError, sendMail } from '@core-api/utils/mail';

import { ValidationError } from '@core-api/utils/validate_request';

const mapReviewType = (type: ReviewType): ReviewTypePrisma => {
  switch (type) {
    case ReviewType.APPROVE:
      return ReviewTypePrisma.APPROVE;
    case ReviewType.REJECT:
      return ReviewTypePrisma.REJECT;
    case ReviewType.REMAND:
      return ReviewTypePrisma.REMAND;
    default:
      throw new Error('Invalid review type');
  }
};

export const reviewMembershipApplication = createHandler({
  schema: z.object({
    membershipApplicationId: z.number(),
    reviewType: z.nativeEnum(ReviewType),
    remandReason: z.string(),
  }),
  business: (req, ctx) =>
    ok(req)
      .andThen(() => checkAdminUserExists(ctx).map((user) => ({ ...req, reviewer: user.name })))
      .asyncAndThen((reqWithReviewer) =>
        findMembershipApplicationById({ membershipApplicationId: reqWithReviewer.membershipApplicationId }).map((application) => ({
          reqWithReviewer,
          application,
        }))
      )
      .andThen(({ reqWithReviewer, application }) => {
        const { membershipApplicationId, remandReason, reviewer } = reqWithReviewer;
        const reviewType = mapReviewType(reqWithReviewer.reviewType);
        const reviewData = { membershipApplicationId, reviewType, reviewer, remandReason };
        return createMembershipApplicationReviewLog(reviewData).andThen(() => {
          const email = application.mailVerification.email;
          if (reviewType === ReviewTypePrisma.REMAND) {
            return sendMail({
              to: email,
              template: membershipRemandTemplate,
              params: {
                firstName: application.firstName || '',
                lastName: application.lastName || '',
                fieldsToFix: remandReason,
                resubmitLink: `${config.frontendEndpoint}/join/application?token=${application.mailVerification.token}`,
              },
            });
          } else if (reviewType === ReviewTypePrisma.REJECT) {
            return sendMail({
              to: email,
              template: membershipRejectionTemplate,
              params: {
                firstName: application.firstName || '',
                lastName: application.lastName || '',
              },
            });
          }
          return ok(undefined);
        });
      }),
  toResponse: () => create(ReviewMembershipApplicationResponseSchema, {}),
  toError: (error) => {
    console.error(error);
    return match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(MembershipApplicationNotFoundError), () => new ConnectError('Not found', Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Database error', Code.Internal))
      .with(P.instanceOf(UnauthorizedError), () => new ConnectError('Unauthorized', Code.Unauthenticated))
      .with(P.instanceOf(MailSendError), () => new ConnectError('メールの送信に失敗しました', Code.Internal))
      .otherwise(() => new ConnectError('Internal error', Code.Internal));
  },
});
