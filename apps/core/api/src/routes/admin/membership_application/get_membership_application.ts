import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  MembershipApplicationSchema,
  GetMembershipApplicationResponseSchema,
  IdentityDocumentSchema,
  DocumentGroupSchema,
  BeneficialOwnerDeclarationSchema,
  DocumentType,
  ReviewType,
  MembershipApplicationReviewLogSchema,
  MembershipApplicationDocumentGroupReviewLogSchema,
} from '@hami/core-admin-api-schema/membership_application_service_pb';
import { FileType, ReviewType as PrismaReviewType } from '@hami/prisma';
import { DatabaseError } from '@core-api/repositories';
import {
  findMembershipApplicationById,
  MembershipApplicationNotFoundError,
} from '@core-api/repositories/membership_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getMembershipApplication = createHandler({
  schema: z.object({
    membershipApplicationId: z.number().int().positive(),
  }),
  business: (req) => findMembershipApplicationById({ membershipApplicationId: req.membershipApplicationId }),
  toResponse: (applicationWithMail) => {
    const app = applicationWithMail;

    // FileTypeをDocumentTypeにマッピング
    const mapFileTypeToDocumentType = (fileType: FileType | null | undefined): DocumentType => {
      if (fileType == null) return DocumentType.DOCUMENT_TYPE_UNKNOWN; // 個人書類などfileType未設定時
      switch (fileType) {
        case FileType.IDENTITY_FRONT:
          return DocumentType.IDENTITY_FRONT;
        case FileType.IDENTITY_BACK:
          return DocumentType.IDENTITY_BACK;
        case FileType.CORP_SEAL_CERT:
          return DocumentType.CORP_SEAL_CERT;
        case FileType.CORP_REGISTRY_CERT:
          return DocumentType.CORP_REGISTRY_CERT;
        default:
          return DocumentType.DOCUMENT_TYPE_UNKNOWN;
      }
    };

    // PrismaReviewTypeをReviewTypeにマッピング
    const mapPrismaReviewTypeToReviewType = (reviewType?: PrismaReviewType): ReviewType => {
      if (!reviewType) return ReviewType.NOT_REVIEWED;
      switch (reviewType) {
        case PrismaReviewType.APPROVE:
          return ReviewType.APPROVE;
        case PrismaReviewType.REJECT:
          return ReviewType.REJECT;
        case PrismaReviewType.REMAND:
          return ReviewType.REMAND;
        default:
          return ReviewType.NOT_REVIEWED;
      }
    };

    // 最新のレビューステータスを取得
    const latestApplicationReview = app.applicationReviewLogs[0];
    const latestDocumentGroupReview = app.documentGroupReviewLogs[0];

    // 差し戻し後の再提出を検出してステータスを調整
    const applicationReviewStatus = (() => {
      const status = mapPrismaReviewTypeToReviewType(latestApplicationReview?.reviewType);
      // 差し戻し後にアプリケーションが更新された場合はNOT_REVIEWEDに変更
      if (status === ReviewType.REMAND && latestApplicationReview && app.updatedAt > latestApplicationReview.createdAt) {
        return ReviewType.NOT_REVIEWED;
      }
      return status;
    })();
    const documentGroupReviewStatus = (() => {
      const status = mapPrismaReviewTypeToReviewType(latestDocumentGroupReview?.reviewType);
      // 差し戻し後にユーザーが再提出（アップロード完了）した場合は NOT_REVIEWED に戻す
      if (status === ReviewType.REMAND && latestDocumentGroupReview) {
        // 1) document group 側の更新（isCompleted=true かつ updatedAt が差し戻し後）を優先して検知
        const anyGroupCompletedAfterRemand = app.documentGroups?.some((g) => {
          const updatedAt: Date | undefined = g.updatedAt;
          return g.isCompleted === true && updatedAt && updatedAt > latestDocumentGroupReview.createdAt;
        });
        if (anyGroupCompletedAfterRemand) return ReviewType.NOT_REVIEWED;

        // 2) フォールバック: 本人確認書類の uploadedAt が差し戻し後に新しくなっている場合
        const latestDocUploadedAt =
          app.identityDocuments?.reduce((max: number, d) => {
            const t = d.uploadedAt ? (d.uploadedAt.getTime?.() ?? d.uploadedAt) : 0;
            return t && t > max ? t : max;
          }, 0) ?? 0;
        if (latestDocUploadedAt && latestDocUploadedAt > latestDocumentGroupReview.createdAt.getTime()) {
          return ReviewType.NOT_REVIEWED;
        }
      }
      return status;
    })();
    // コンプライアンス審査ステータス（差し戻し後の再提出を考慮）
    const complianceLatest = app.complianceReviewLogs?.[0];
    let complianceApplicationReviewStatus = mapPrismaReviewTypeToReviewType(complianceLatest?.reviewType);
    if (complianceApplicationReviewStatus === ReviewType.REMAND && complianceLatest && app.updatedAt > complianceLatest.createdAt) {
      complianceApplicationReviewStatus = ReviewType.NOT_REVIEWED;
    }

    const complianceDocLatest = app.complianceDocumentGroupReviewLogs?.[0];
    let complianceDocumentGroupReviewStatus = mapPrismaReviewTypeToReviewType(complianceDocLatest?.reviewType);
    if (complianceDocumentGroupReviewStatus === ReviewType.REMAND && complianceDocLatest) {
      // 1) document group 側の再提出（isCompleted=true かつ updatedAt が差し戻し後）を優先して検知
      const anyGroupCompletedAfterRemand = app.documentGroups?.some((g) => {
        const updatedAt: Date | undefined = g.updatedAt;
        return g.isCompleted === true && updatedAt && updatedAt > complianceDocLatest.createdAt;
      });
      if (anyGroupCompletedAfterRemand) {
        complianceDocumentGroupReviewStatus = ReviewType.NOT_REVIEWED;
      } else {
        // 2) フォールバック: 本人確認書類の uploadedAt が差し戻し後に新しくなっている場合
        const latestDocUploadedAt =
          app.identityDocuments?.reduce((max: number, d) => {
            const t = d.uploadedAt ? (d.uploadedAt.getTime?.() ?? d.uploadedAt) : 0;
            return t && t > max ? t : max;
          }, 0) ?? 0;
        if (latestDocUploadedAt && latestDocUploadedAt > complianceDocLatest.createdAt.getTime()) {
          complianceDocumentGroupReviewStatus = ReviewType.NOT_REVIEWED;
        }
      }
    }

    // identity documentsを変換
    const identityDocuments = app.identityDocuments.map((doc) =>
      create(IdentityDocumentSchema, {
        identityDocumentId: doc.membershipApplicationIdentityDocumentId,
        fileKey: doc.fileKey,
        documentType: mapFileTypeToDocumentType(doc.fileType),
        uploadedAt: BigInt(doc.uploadedAt.getTime()),
        createdAt: BigInt(doc.createdAt.getTime()),
      })
    );

    // document groupsを変換
    const documentGroups = app.documentGroups.map((group) =>
      create(DocumentGroupSchema, {
        documentGroupId: group.membershipApplicationDocumentGroupId,
        uploadToken: group.uploadToken,
        groupKey: group.groupKey,
        isCompleted: group.isCompleted,
        documents: group.documents.map((doc) =>
          create(IdentityDocumentSchema, {
            identityDocumentId: doc.membershipApplicationIdentityDocumentId,
            fileKey: doc.fileKey,
            documentType: mapFileTypeToDocumentType(doc.fileType),
            uploadedAt: BigInt(doc.uploadedAt.getTime()),
            createdAt: BigInt(doc.createdAt.getTime()),
          })
        ),
        createdAt: BigInt(group.createdAt.getTime()),
      })
    );

    // application review logs を変換
    const applicationReviewLogs = (app.applicationReviewLogs ?? []).map((log) =>
      create(MembershipApplicationReviewLogSchema, {
        membershipApplicationReviewLogId: log.membershipApplicationReviewLogId,
        membershipApplicationId: log.membershipApplicationId,
        timestamp: BigInt(log.timestamp.getTime()),
        reviewer: log.reviewer,
        reviewType: mapPrismaReviewTypeToReviewType(log.reviewType),
        remandReason: log.remandReason ?? undefined,
        createdAt: BigInt(log.createdAt.getTime()),
      })
    );

    const documentGroupReviewLogs = (app.documentGroupReviewLogs ?? []).map((log) =>
      create(MembershipApplicationDocumentGroupReviewLogSchema, {
        membershipApplicationDocumentGroupReviewLogId: log.membershipApplicationDocumentGroupReviewLogId,
        membershipApplicationId: log.membershipApplicationId,
        timestamp: BigInt(log.timestamp.getTime()),
        reviewer: log.reviewer,
        reviewType: mapPrismaReviewTypeToReviewType(log.reviewType),
        remandReason: log.remandReason ?? undefined,
        createdAt: BigInt(log.createdAt.getTime()),
      })
    );

    // compliance review logs を変換
    const complianceApplicationReviewLogs = (app.complianceReviewLogs ?? []).map((log) =>
      create(MembershipApplicationReviewLogSchema, {
        membershipApplicationReviewLogId: log.membershipApplicationComplianceReviewLogId,
        membershipApplicationId: log.membershipApplicationId,
        timestamp: BigInt(log.timestamp.getTime()),
        reviewer: log.reviewer,
        reviewType: mapPrismaReviewTypeToReviewType(log.reviewType),
        remandReason: log.remandReason ?? undefined,
        createdAt: BigInt(log.createdAt.getTime()),
      })
    );

    const complianceDocumentGroupReviewLogs = (app.complianceDocumentGroupReviewLogs ?? []).map((log) =>
      create(MembershipApplicationDocumentGroupReviewLogSchema, {
        membershipApplicationDocumentGroupReviewLogId: log.membershipApplicationComplianceDocumentGroupReviewLogId,
        membershipApplicationId: log.membershipApplicationId,
        timestamp: BigInt(log.timestamp.getTime()),
        reviewer: log.reviewer,
        reviewType: mapPrismaReviewTypeToReviewType(log.reviewType),
        remandReason: log.remandReason ?? undefined,
        createdAt: BigInt(log.createdAt.getTime()),
      })
    );

    return create(GetMembershipApplicationResponseSchema, {
      membershipApplication: create(MembershipApplicationSchema, {
        membershipApplicationId: app.membershipApplicationId,
        email: app.mailVerification.email,
        appliedAt: BigInt(app.createdAt.getTime()),
        updatedAt: BigInt(app.updatedAt.getTime()),
        // 申込者タイプ
        applicantType: app.applicantType === 'INDIVIDUAL' ? 1 : app.applicantType === 'CORPORATE' ? 2 : 0,
        firstName: app.firstName ?? undefined,
        lastName: app.lastName ?? undefined,
        firstNameKana: app.firstNameKana ?? undefined,
        lastNameKana: app.lastNameKana ?? undefined,
        postalCode: app.postalCode,
        prefecture: app.prefecture,
        address: app.address,
        apartment: app.apartment ?? undefined,
        phoneNumber: app.phoneNumber,
        birthYear: app.birthYear ?? undefined,
        birthMonth: app.birthMonth ?? undefined,
        birthDay: app.birthDay ?? undefined,

        // 個人用：金融情報
        annualIncome: app.annualIncome ?? undefined,
        depositAmount: app.depositAmount ?? undefined,
        financialAssets: app.financialAssets ?? undefined,

        // 個人用：勤務先情報（任意）
        companyName: app.companyName ?? undefined,
        companyAddress: app.companyAddress ?? undefined,
        companyPhoneNumber: app.companyPhoneNumber ?? undefined,

        // 個人用：職業
        occupation: app.occupation ?? undefined,

        // 個人情報保護方針への同意
        privacyPolicyAgreement: app.privacyPolicyAgreement,

        // 法人用フィールド
        corporateName: app.corporateName ?? undefined,
        corporateNameKana: app.corporateNameKana ?? undefined,
        representativeName: app.representativeName ?? undefined,
        representativeNameKana: app.representativeNameKana ?? undefined,
        representativePosition: app.representativePosition ?? undefined,
        corporateNumber: app.corporateNumber ?? undefined,
        establishedYear: app.establishedYear ?? undefined,
        establishedMonth: app.establishedMonth ?? undefined,
        establishedDay: app.establishedDay ?? undefined,

        // 実質的支配者申告
        beneficialOwnerDeclarations:
          app.beneficialOwnerDeclarations?.map((declaration) =>
            create(BeneficialOwnerDeclarationSchema, {
              beneficialOwnerDeclarationId: declaration.beneficialOwnerDeclarationId,
              beneficialOwnerName: declaration.beneficialOwnerName,
              beneficialOwnerBirthYear: declaration.beneficialOwnerBirthYear,
              beneficialOwnerBirthMonth: declaration.beneficialOwnerBirthMonth,
              beneficialOwnerBirthDay: declaration.beneficialOwnerBirthDay,
              beneficialOwnerPostalCode: declaration.beneficialOwnerPostalCode,
              beneficialOwnerPrefecture: declaration.beneficialOwnerPrefecture,
              beneficialOwnerAddress: declaration.beneficialOwnerAddress,
              beneficialOwnerApartment: declaration.beneficialOwnerApartment ?? undefined,
              declarantName: declaration.declarantName,
              declarantPosition: declaration.declarantPosition,
              isConfirmed: declaration.isConfirmed,
              confirmedAt: declaration.confirmedAt ? BigInt(declaration.confirmedAt.getTime()) : undefined,
              createdAt: BigInt(declaration.createdAt.getTime()),
            })
          ) ?? [],
        applicationReviewStatus,
        documentGroupReviewStatus,
        complianceApplicationReviewStatus,
        complianceDocumentGroupReviewStatus,
        identityDocuments,
        documentGroups,
        // 差し戻し後の再提出かどうか
        isResubmittedAfterRemand: Boolean(
          latestApplicationReview &&
            latestApplicationReview.reviewType === PrismaReviewType.REMAND &&
            app.updatedAt > latestApplicationReview.createdAt
        ),
      }),
      applicationReviewLogs,
      documentGroupReviewLogs,
      complianceApplicationReviewLogs,
      complianceDocumentGroupReviewLogs,
    });
  },
  toError: (error) => {
    return match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(MembershipApplicationNotFoundError), () => new ConnectError('Membership application not found.', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive();
  },
});
