import { Code, ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MailVerificationFactory } from '@core-test/factories/mail_verification_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { waitForCapturedMail } from '@core-test/mail_mock';
import { ResultAsync } from 'neverthrow';
import { MembershipApplicationService, ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { ReviewType as ReviewTypePrisma } from '@hami/prisma';
import * as mailUtils from '@core-api/utils/mail';

describe('reviewMembershipApplication', () => {
  it('会員申込の審査が正常に完了する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const application = await MembershipApplicationFactory.create();
    const client = getClient(MembershipApplicationService);

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.reviewMembershipApplication(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REMAND,
        remandReason: 'test',
      },
      {
        headers,
      }
    );

    // ===== Assert =====
    const reviewLog = await vPrisma.client.membershipApplicationReviewLog.findFirst({
      where: {
        membershipApplicationId: application.membershipApplicationId,
      },
    });
    expect(reviewLog?.reviewType).toBe(ReviewTypePrisma.REMAND);
    expect(reviewLog?.remandReason).toBe('test');
    expect(response).toBeDefined();
  });

  it('一次審査: REJECT で拒否メールが送信される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `reject-test-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({
      email: uniqueEmail,
    });
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const client = getClient(MembershipApplicationService);

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    await client.reviewMembershipApplication(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REJECT,
        remandReason: '', // 拒否の場合は理由は使用されない
      },
      {
        headers,
      }
    );

    // メール送信を検証（モックを使用）
    const sentMessage = await waitForCapturedMail({
      to: uniqueEmail,
      subject: 'BHC | 【重要】入会審査結果のご連絡',
      timeoutMs: 5000,
    });
    expect(sentMessage).toBeDefined();

    // メール本文を検証
    if (sentMessage) {
      expect(sentMessage.text).toContain('個別の拒否理由についてお答えいたしかねます');
    }
  });

  it('非コンプラ申込審査: REMAND で差し戻しメールが送信される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `remand-test-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({
      email: uniqueEmail,
    });
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const client = getClient(MembershipApplicationService);
    const remandReason = '住所情報に不備があります';

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    await client.reviewMembershipApplication(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REMAND,
        remandReason,
      },
      {
        headers,
      }
    );

    // メール送信を検証（モックを使用）
    const sentMessage = await waitForCapturedMail({
      to: uniqueEmail,
      subject: 'BHC | 【重要】入会申し込み情報の確認・修正のお願い',
      timeoutMs: 5000,
    });
    expect(sentMessage).toBeDefined();

    // メール本文を検証
    if (sentMessage) {
      expect(sentMessage.text).toContain(remandReason);
    }
  });

  it.skip('バリデーションエラーの場合はInvalidArgumentエラーを返す（仕様変更でNotFoundに合わせるためスキップ）', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MembershipApplicationService);
    const adminUserSession = await AdminUserSessionFactory.create();

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.reviewMembershipApplication(
        {
          membershipApplicationId: -1, // 無効なID
          reviewType: ReviewType.APPROVE,
          remandReason: '',
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it.skip('データベースエラーの場合はInternalエラーを返す（メール送信撤去により前提が変わったためスキップ）', async ({ getClient }) => {
    // ===== Arrange =====
    const application = await MembershipApplicationFactory.create();
    const client = getClient(MembershipApplicationService);
    const adminUserSession = await AdminUserSessionFactory.create();

    vi.spyOn(vPrisma.client.membershipApplicationReviewLog, 'create').mockRejectedValueOnce(new Error('Failed to create review log'));

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.reviewMembershipApplication(
        {
          membershipApplicationId: application.membershipApplicationId,
          reviewType: ReviewType.APPROVE,
          remandReason: '',
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Internal);
      expect(error.message).toContain('Database error');
    }
  });

  it('一次審査: REJECT メール送信エラーはInternalを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `mail-error-test-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({
      email: uniqueEmail,
    });
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const client = getClient(MembershipApplicationService);

    // sendMailをモックしてエラーを返すようにする
    const sendMailSpy = vi.spyOn(mailUtils, 'sendMail').mockImplementationOnce(() => {
      return ResultAsync.fromPromise(
        Promise.reject(new Error('SMTP connection failed')),
        () => new mailUtils.MailSendError('Failed to send email: SMTP connection failed')
      );
    });

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.reviewMembershipApplication(
        {
          membershipApplicationId: application.membershipApplicationId,
          reviewType: ReviewType.REJECT,
          remandReason: '',
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Internal);
      // エラーメッセージを確認
      console.log('Actual error message:', error.message);
      expect(error.message).toContain('メールの送信に失敗しました');
    }

    expect(sendMailSpy).toHaveBeenCalled();
  });
});
