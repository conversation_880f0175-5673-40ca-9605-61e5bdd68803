import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MailVerificationFactory } from '@core-test/factories/mail_verification_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { MembershipApplicationService, ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';

describe('getMembershipApplication: コンプラ差し戻し後の再提出で NOT_REVIEWED に戻る', () => {
  it('コンプラ申込審査がREMAND後に申込を再更新すると、complianceApplicationReviewStatus が NOT_REVIEWED になる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `compliance-resubmit-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({ email: uniqueEmail });
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      address: '東京都千代田区1-1-1',
    });

    const client = getClient(MembershipApplicationService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    // 1) コンプラ申込を差し戻し
    await client.reviewComplianceMembershipApplication(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REMAND,
        remandReason: '修正が必要です',
      },
      { headers }
    );

    // 2) 申込者が内容を修正して再提出（updatedAt を進める）
    await vPrisma.client.membershipApplication.update({
      where: { membershipApplicationId: application.membershipApplicationId },
      data: { address: '東京都千代田区1-1-2 再提出' },
    });

    // 3) 取得時に NOT_REVIEWED に戻ること
    const resp = await client.getMembershipApplication({ membershipApplicationId: application.membershipApplicationId }, { headers });

    expect(resp.membershipApplication?.complianceApplicationReviewStatus).toBe(ReviewType.NOT_REVIEWED);
  });
});
