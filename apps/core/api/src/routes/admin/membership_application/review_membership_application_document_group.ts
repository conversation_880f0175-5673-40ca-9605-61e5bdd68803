import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok, err, Result } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  ReviewMembershipApplicationDocumentGroupResponseSchema,
  ReviewType,
} from '@hami/core-admin-api-schema/membership_application_service_pb';
import { ReviewType as ReviewTypePrisma } from '@hami/prisma';
import { config } from '@core-api/config/environment';
import { membershipRemandTemplate } from '@core-api/mail_templates/membership_remand';
import { checkAdminUserExists, UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories';
import { createMembershipApplicationDocumentGroupReviewLog } from '@core-api/repositories/membership_application_document_group_review_log_repository';
import {
  findMembershipApplicationById,
  MembershipApplicationNotFoundError,
  resetDocumentGroupForRemand,
} from '@core-api/repositories/membership_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { MailSendError, sendMail } from '@core-api/utils/mail';

import { ValidationError } from '@core-api/utils/validate_request';

class InvalidReviewTypeError extends Error {
  readonly name = 'InvalidReviewTypeError';
}

const mapReviewType = (type: ReviewType): Result<ReviewTypePrisma, Error> => {
  switch (type) {
    case ReviewType.APPROVE:
      return ok(ReviewTypePrisma.APPROVE);
    case ReviewType.REJECT:
      return ok(ReviewTypePrisma.REJECT);
    case ReviewType.REMAND:
      return ok(ReviewTypePrisma.REMAND);
    default:
      return err(new InvalidReviewTypeError());
  }
};

export const reviewMembershipApplicationDocumentGroup = createHandler({
  schema: z.object({
    membershipApplicationId: z.number(),
    reviewType: z.nativeEnum(ReviewType),
    remandReason: z.string(),
  }),
  business: (req, ctx) =>
    ok(req)
      .andThen(() => checkAdminUserExists(ctx).map((user) => ({ ...req, reviewer: user.name })))
      .asyncAndThen((reqWithReviewer) =>
        findMembershipApplicationById(reqWithReviewer).map((application) => ({ reqWithReviewer, application }))
      )
      .andThen(({ reqWithReviewer, application }) => {
        const { membershipApplicationId, remandReason, reviewer } = reqWithReviewer;
        return mapReviewType(reqWithReviewer.reviewType).asyncAndThen((reviewType) => {
          const reviewData = { membershipApplicationId, reviewType, reviewer, remandReason };

          // レビューログを作成
          return createMembershipApplicationDocumentGroupReviewLog(reviewData).andThen(() => {
            // 差し戻しの場合は新しいdocument groupを作成し、メール送信する
            if (reviewType === ReviewTypePrisma.REMAND) {
              return resetDocumentGroupForRemand(membershipApplicationId).andThen(() =>
                sendMail({
                  to: application.mailVerification.email,
                  template: membershipRemandTemplate,
                  params: {
                    firstName: application.firstName || '',
                    lastName: application.lastName || '',
                    fieldsToFix: remandReason,
                    resubmitLink: `${config.frontendEndpoint}/join/application?token=${application.mailVerification.token}`,
                  },
                })
              );
            } else if (reviewType === ReviewTypePrisma.APPROVE) {
              // 書類グループ承認時は何もしない（アカウント作成はコンプラ承認に移動）
              return ok(undefined);
            } else {
              return ok(undefined);
            }
          });
        });
      }),
  toResponse: () => create(ReviewMembershipApplicationDocumentGroupResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(MembershipApplicationNotFoundError), () => new ConnectError('Not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Database error', Code.Internal))
      .with(P.instanceOf(UnauthorizedError), () => new ConnectError('Unauthorized', Code.Unauthenticated))
      .with(
        P.instanceOf(MailSendError),
        () => new ConnectError('\u30e1\u30fc\u30eb\u306e\u9001\u4fe1\u306b\u5931\u6557\u3057\u307e\u3057\u305f', Code.Internal)
      )
      .with(P.instanceOf(InvalidReviewTypeError), () => new ConnectError('Invalid review type', Code.InvalidArgument))
      .exhaustive(),
});
