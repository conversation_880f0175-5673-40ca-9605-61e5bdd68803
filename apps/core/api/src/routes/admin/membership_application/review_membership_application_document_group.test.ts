import { Code, ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MailVerificationFactory } from '@core-test/factories/mail_verification_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { MembershipApplicationDocumentGroupFactory } from '@core-test/factories/membership_application_document_group_factory';
import { waitForCapturedMail } from '@core-test/mail_mock';
import { ResultAsync } from 'neverthrow';
import { MembershipApplicationService, ReviewType } from '@hami/core-admin-api-schema/membership_application_service_pb';
import { ReviewType as ReviewTypePrisma } from '@hami/prisma';
import * as mailUtils from '@core-api/utils/mail';

describe('reviewMembershipApplicationDocumentGroup', () => {
  it('本人確認書類の審査が正常に完了する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: {
        connect: {
          mailVerificationId: mailVerification.mailVerificationId,
        },
      },
    });
    const client = getClient(MembershipApplicationService);

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // 既存のdocument groupを用意（差し戻しは既存グループの上書き運用）
    await MembershipApplicationDocumentGroupFactory.create({
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    // ===== Act =====
    const response = await client.reviewMembershipApplicationDocumentGroup(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REMAND,
        remandReason: 'document test reason',
      },
      {
        headers,
      }
    );

    // ===== Assert =====
    const reviewLog = await vPrisma.client.membershipApplicationDocumentGroupReviewLog.findFirst({
      where: {
        membershipApplicationId: application.membershipApplicationId,
      },
    });
    expect(reviewLog?.reviewType).toBe(ReviewTypePrisma.REMAND);
    expect(reviewLog?.remandReason).toBe('document test reason');
    expect(response).toBeDefined();
  });

  it('認証されていない場合はエラーが返る', async ({ getClient }) => {
    // ===== Arrange =====
    const application = await MembershipApplicationFactory.create();
    const client = getClient(MembershipApplicationService);

    // ===== Act & Assert =====
    let error;
    try {
      await client.reviewMembershipApplicationDocumentGroup({
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.APPROVE,
        remandReason: '',
      });
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('存在しない申込IDの場合はエラーが返る', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MembershipApplicationService);

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act & Assert =====
    let error;
    try {
      await client.reviewMembershipApplicationDocumentGroup(
        {
          membershipApplicationId: 99999,
          reviewType: ReviewType.APPROVE,
          remandReason: '',
        },
        {
          headers,
        }
      );
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
    }
  });

  it.skip('REJECT時にメールが送信される（コンプラへ移管のためスキップ）', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `reject-test-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({
      email: uniqueEmail,
    });
    const application = await MembershipApplicationFactory.create({
      mailVerification: {
        connect: {
          mailVerificationId: mailVerification.mailVerificationId,
        },
      },
    });
    const client = getClient(MembershipApplicationService);

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    await client.reviewMembershipApplicationDocumentGroup(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REJECT,
        remandReason: '',
      },
      {
        headers,
      }
    );

    // ===== Assert =====
    // メールが送信されるまで待機（モックを使用）
    const rejectionMessage = await waitForCapturedMail({
      to: uniqueEmail,
      subject: '入会申込結果のお知らせ',
      timeoutMs: 5000,
    });

    expect(rejectionMessage).toBeDefined();
    expect(rejectionMessage?.subject).toContain('入会申込結果のお知らせ');

    // メール本文を検証
    if (rejectionMessage) {
      expect(rejectionMessage.text).toContain('入会をお断りさせていただく');
      expect(rejectionMessage.text).toContain('個別の拒否理由についてはお答えいたしかねます');
    }
  });

  it('非コンプラ書類グループ審査: REMAND で差し戻しメールが送信される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `remand-test-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({
      email: uniqueEmail,
    });
    const application = await MembershipApplicationFactory.create({
      mailVerification: {
        connect: {
          mailVerificationId: mailVerification.mailVerificationId,
        },
      },
    });
    const client = getClient(MembershipApplicationService);

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    const remandReason = 'ドキュメントの再提出が必要です';

    // 既存のdocument groupを用意（差し戻しは既存グループの上書き運用）
    await MembershipApplicationDocumentGroupFactory.create({
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    // ===== Act =====
    await client.reviewMembershipApplicationDocumentGroup(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.REMAND,
        remandReason,
      },
      {
        headers,
      }
    );

    // ===== Assert =====
    // メールが送信されるまで待機（モックを使用）
    const remandMessage = await waitForCapturedMail({
      to: uniqueEmail,
      subject: 'BHC | 【重要】入会申し込み情報の確認・修正のお願い',
      timeoutMs: 5000,
    });

    expect(remandMessage).toBeDefined();

    if (remandMessage) {
      expect(remandMessage.text).toContain(remandReason);
    }
  });

  it.skip('APPROVE時は書類承認・アカウント作成統合メールが送信される（コンプラへ移管のためスキップ）', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `approve-test-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({
      email: uniqueEmail,
    });
    const application = await MembershipApplicationFactory.create({
      mailVerification: {
        connect: {
          mailVerificationId: mailVerification.mailVerificationId,
        },
      },
    });
    const client = getClient(MembershipApplicationService);

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    await client.reviewMembershipApplicationDocumentGroup(
      {
        membershipApplicationId: application.membershipApplicationId,
        reviewType: ReviewType.APPROVE,
        remandReason: '',
      },
      {
        headers,
      }
    );

    // ===== Assert =====
    // 書類承認・アカウント作成統合メールが送信されることを確認（モックを使用）
    const integrationMail = await waitForCapturedMail({
      to: uniqueEmail,
      subject: '書類審査完了・アカウント作成のお知らせ',
      timeoutMs: 10000, // 10秒待機
    });
    expect(integrationMail).toBeDefined();

    // メール本文を検証
    if (integrationMail) {
      expect(integrationMail.text).toContain('書類の審査が完了し');
      expect(integrationMail.text).toContain('アカウントを作成いたしました');
      expect(integrationMail.text).toContain('仮パスワード');
    }
  }, 15000); // 15秒のタイムアウト

  it('存在しない申込IDの場合はNotFoundエラーを返す（負の値）', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MembershipApplicationService);
    const adminUserSession = await AdminUserSessionFactory.create();

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.reviewMembershipApplicationDocumentGroup(
        {
          membershipApplicationId: -1, // 存在しないID
          reviewType: ReviewType.APPROVE,
          remandReason: '',
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
    }
  });

  it('データベースエラーの場合はInternalエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const application = await MembershipApplicationFactory.create();
    const client = getClient(MembershipApplicationService);
    const adminUserSession = await AdminUserSessionFactory.create();

    vi.spyOn(vPrisma.client.membershipApplicationDocumentGroupReviewLog, 'create').mockRejectedValueOnce(
      new Error('Failed to create review log')
    );

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.reviewMembershipApplicationDocumentGroup(
        {
          membershipApplicationId: application.membershipApplicationId,
          reviewType: ReviewType.APPROVE,
          remandReason: '',
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Internal);
      expect(error.message).toContain('Database error');
    }
  });

  it.skip('メール送信エラーの場合はInternalエラーを返す（コンプラへ移管のためスキップ）', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const uniqueEmail = `mail-error-test-${Date.now()}-${Math.random().toString(36).substring(2)}@example.com`;
    const mailVerification = await MailVerificationFactory.create({
      email: uniqueEmail,
    });
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const client = getClient(MembershipApplicationService);

    // sendMailをモックしてエラーを返すようにする
    const sendMailSpy = vi.spyOn(mailUtils, 'sendMail').mockImplementationOnce(() => {
      return ResultAsync.fromPromise(
        Promise.reject(new Error('SMTP connection failed')),
        () => new mailUtils.MailSendError('Failed to send email: SMTP connection failed')
      );
    });

    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.reviewMembershipApplicationDocumentGroup(
        {
          membershipApplicationId: application.membershipApplicationId,
          reviewType: ReviewType.REJECT,
          remandReason: '',
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }

    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Internal);
      expect(error.message).toContain('メールの送信に失敗しました');
    }

    expect(sendMailSpy).toHaveBeenCalled();
  });
});
