import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import {
  MemberFactory,
  UserFactory,
  MembershipApplicationFactory,
  MailVerificationFactory,
  AdminUserSessionFactory,
} from '@core-test/index';
import { MemberService } from '@hami/core-admin-api-schema/member_service_pb';
import { ListMembersRequestSchema } from '@hami/core-admin-api-schema/member_service_pb';

describe('listMembers', () => {
  it('会員一覧を取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification1 = await MailVerificationFactory.create();
    const application1 = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification1.mailVerificationId } },
    });
    const user1 = await UserFactory.create();
    await MemberFactory.create({
      user: { connect: { userId: user1.userId } },
      membershipApplication: { connect: { membershipApplicationId: application1.membershipApplicationId } },
    });

    const mailVerification2 = await MailVerificationFactory.create();
    const application2 = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification2.mailVerificationId } },
    });
    const user2 = await UserFactory.create();
    await MemberFactory.create({
      user: { connect: { userId: user2.userId } },
      membershipApplication: { connect: { membershipApplicationId: application2.membershipApplicationId } },
    });

    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMembers({}, { headers });

    // ===== Assert =====
    expect(response.members).toHaveLength(2);
    expect(response.members[0].email).toBeDefined();
    expect(response.members[0].firstName).toBeDefined();
    expect(response.members[0].lastName).toBeDefined();
    expect(response.members[0].memberNumber).toBeGreaterThan(10000);
  });

  it('会員が存在しない場合は空配列を返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMembers({}, { headers });

    // ===== Assert =====
    expect(response.members).toHaveLength(0);
  });

  it('認証されていない場合はUnauthenticatedエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MemberService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.listMembers({});
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('無効なセッショントークンの場合はUnauthenticatedエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: 'invalid-session-token',
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.listMembers({}, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('firstNameで部分一致検索できる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      firstName: '太郎',
      firstNameKana: 'タロウ',
    });
    const client = getClient(MemberService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });
    const req = create(ListMembersRequestSchema, { firstName: '太' });
    const response = await client.listMembers(req, { headers });
    expect(response.members.some(m => m.firstName === '太郎')).toBe(true);
  });

  it('lastNameで部分一致検索できる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      lastName: '山田',
      lastNameKana: 'ヤマダ',
    });
    const client = getClient(MemberService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });
    const req = create(ListMembersRequestSchema, { lastName: '山' });
    const response = await client.listMembers(req, { headers });
    expect(response.members.some(m => m.lastName === '山田')).toBe(true);
  });

  it('memberNumberで完全一致検索できる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      memberNumber: 12345,
    });
    const client = getClient(MemberService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });
    const req = create(ListMembersRequestSchema, { memberNumber: 12345 });
    const response = await client.listMembers(req, { headers });
    expect(response.members.some(m => m.memberNumber === 12345)).toBe(true);
  });

  it('includeRetired=falseで退会者を除外できる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      retirementDate: new Date(Date.now() - 1000 * 60 * 60 * 24),
      firstName: '退会者',
    });
    const client = getClient(MemberService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });
    const req = create(ListMembersRequestSchema, { includeRetired: false });
    const response = await client.listMembers(req, { headers });
    expect(response.members.every(m => m.firstName !== '退会者')).toBe(true);
  });

  it('includeRetired=trueで退会者も含めて取得できる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      retirementDate: new Date(Date.now() - 1000 * 60 * 60 * 24),
      firstName: '退会者',
    });
    const client = getClient(MemberService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });
    const req = create(ListMembersRequestSchema, { includeRetired: true });
    const response = await client.listMembers(req, { headers });
    expect(response.members.some(m => m.firstName === '退会者')).toBe(true);
  });

  it('退会者のretirementDateが正しく返る', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const retirementDate = new Date(Date.now() - 1000 * 60 * 60 * 24); // 昨日
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      retirementDate,
      firstName: '退会者',
    });
    const client = getClient(MemberService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });
    const req = create(ListMembersRequestSchema, { includeRetired: true });
    const response = await client.listMembers(req, { headers });

    // 退会者が含まれていること
    const retired = response.members.find(m => m.firstName === '退会者');
    expect(retired).toBeDefined();
    // retirementAtが設定されていること
    expect(retired!.retirementAt).toBeDefined();
    // retirementAtが数値であること
    expect(typeof Number(retired!.retirementAt)).toBe('number');
    // retirementAtが正しい形式（秒単位のタイムスタンプ）であること
    expect(Number(retired!.retirementAt)).toBeGreaterThan(0);
  });

  it('現役会員のretirementDateはundefined', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      firstName: '現役会員',
    });
    const client = getClient(MemberService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });
    const req = create(ListMembersRequestSchema, { includeRetired: true });
    const response = await client.listMembers(req, { headers });

    const active = response.members.find(m => m.firstName === '現役会員');
    expect(active).toBeDefined();
    expect(active!.retirementAt).toBeUndefined();
  });

  it('退会日の具体的な値が正しく返ってくる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const retirementDate = new Date('2024-01-15T10:30:00Z'); // 固定の退会日
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      retirementDate,
      firstName: '退会者',
    });
    const client = getClient(MemberService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });
    const req = create(ListMembersRequestSchema, { includeRetired: true });
    const response = await client.listMembers(req, { headers });

    // 退会者が含まれていること
    const retired = response.members.find(m => m.firstName === '退会者');
    expect(retired).toBeDefined();
    expect(retired!.retirementAt).toBeDefined();
    
    // retirementAtがnumberで返ることを確認
    expect(typeof retired!.retirementAt).toBe('number');
    
    // 23:59:59のタイムスタンプが返ることを確認
    const expectedRetirementDate = new Date(retirementDate);
    expectedRetirementDate.setUTCHours(23 - 9, 59, 59, 999); // UTC+9なので9時間引く
    const expectedTimestamp = Math.floor(expectedRetirementDate.getTime() / 1000);
    expect(retired!.retirementAt).toBe(expectedTimestamp);
  });
});
