import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  CancelRetirementResponseSchema
} from '@hami/core-admin-api-schema/member_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { cancelRetirement, MemberNotFoundError } from '@core-api/repositories/member_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const CancelRetirementRequestValidationSchema = z.object({
  memberId: z.number().int().positive(),
});

export const cancelRetirementHandler = createHandler({
  schema: CancelRetirementRequestValidationSchema,
  business: ({ memberId }) => 
    cancelRetirement({ memberId }),
  toResponse: () =>
    create(CancelRetirementResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(MemberNotFoundError), () => new ConnectError('Member not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .with(P._, () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 