import { MemberService } from '@hami/core-admin-api-schema/member_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { cancelRetirementHandler } from './cancel_retirement';
import { getMemberHandler } from './get_member';
import { listMemberInvestmentHorsesHandler } from './list_member_investment_horses';
import { listMembersHandler } from './list_members';
import { updateMemberHandler } from './update_member';

import type { ConnectRouter } from '@connectrpc/connect';

export const implMemberService = (router: ConnectRouter) =>
  router.service(
    MemberService,
    {
      listMembers: unwrapResult(listMembersHandler),
      getMember: unwrapResult(getMemberHandler),
      updateMember: unwrapResult(updateMemberHandler),
      listMemberInvestmentHorses: unwrapResult(listMemberInvestmentHorsesHandler),
      cancelRetirement: unwrapResult(cancelRetirementHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  );
