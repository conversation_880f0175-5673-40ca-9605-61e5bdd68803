import { Code, ConnectError } from '@connectrpc/connect';
import {
  MemberFactory,
  UserFactory,
  MembershipApplicationFactory,
  MailVerificationFactory,
  AdminUserSessionFactory,
} from '@core-test/index';
import { MemberService } from '@hami/core-admin-api-schema/member_service_pb';

describe('updateMember', () => {
  it('管理者が会員情報を正常に更新できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const membershipApplication = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
      postalCode: '100-0001',
      prefecture: '東京都',
      address: '千代田区千代田1-1',
      apartment: null,
      phoneNumber: '03-1234-5678',
    });

    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.updateMember(
      {
        memberId: member.memberId,
        postalCode: '100-0002',
        prefecture: '東京都',
        address: '千代田区丸の内1-1',
        apartment: 'マンション101',
        phoneNumber: '03-9876-5432',
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.member).toBeDefined();
    expect(response.member?.memberId).toBe(member.memberId);
    expect(response.member?.postalCode).toBe('100-0002');
    expect(response.member?.prefecture).toBe('東京都');
    expect(response.member?.address).toBe('千代田区丸の内1-1');
    expect(response.member?.apartment).toBe('マンション101');
    expect(response.member?.phoneNumber).toBe('03-9876-5432');
  });

  it('存在しない会員IDを指定した場合はエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.updateMember(
        {
          memberId: 99999,
          postalCode: '100-0002',
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
      expect(error.message).toContain('会員が見つかりません');
    }
  });

  it('認証されていない場合はUnauthenticatedエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MemberService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.updateMember({
        memberId: 1,
        postalCode: '100-0002',
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('指定していないフィールドが更新されないことを確認する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const membershipApplication = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
      postalCode: '100-0001',
      prefecture: '東京都',
      address: '千代田区千代田1-1',
      apartment: 'アパート202',
      phoneNumber: '03-1234-5678',
    });

    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // 更新前の状態を取得
    const beforeResponse = await client.getMember({ memberId: member.memberId }, { headers });
    const beforeMember = beforeResponse.member!;

    // ===== Act =====
    // postalCodeのみを更新
    const response = await client.updateMember(
      {
        memberId: member.memberId,
        postalCode: '100-0002',
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.member).toBeDefined();
    expect(response.member?.memberId).toBe(member.memberId);

    // 更新されたフィールド
    expect(response.member?.postalCode).toBe('100-0002');

    // 更新されていないフィールドが元の値のまま保持されていることを確認
    expect(response.member?.prefecture).toBe(beforeMember.prefecture);
    expect(response.member?.address).toBe(beforeMember.address);
    expect(response.member?.apartment).toBe(beforeMember.apartment);
    expect(response.member?.phoneNumber).toBe(beforeMember.phoneNumber);

    // 更新対象外のフィールドも変更されていないことを確認
    expect(response.member?.memberNumber).toBe(beforeMember.memberNumber);
    expect(response.member?.email).toBe(beforeMember.email);
    expect(response.member?.firstName).toBe(beforeMember.firstName);
    expect(response.member?.lastName).toBe(beforeMember.lastName);
    expect(response.member?.firstNameKana).toBe(beforeMember.firstNameKana);
    expect(response.member?.lastNameKana).toBe(beforeMember.lastNameKana);
    expect(response.member?.birthYear).toBe(beforeMember.birthYear);
    expect(response.member?.birthMonth).toBe(beforeMember.birthMonth);
    expect(response.member?.birthDay).toBe(beforeMember.birthDay);
    expect(response.member?.membershipApplicationId).toBe(beforeMember.membershipApplicationId);
  });

  it('指定されたフィールドのみを更新できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const membershipApplication = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
      postalCode: '100-0001',
      prefecture: '東京都',
      address: '千代田区千代田1-1',
      apartment: 'アパート202',
      phoneNumber: '03-1234-5678',
    });

    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    // postalCodeのみを指定して更新
    const response = await client.updateMember(
      {
        memberId: member.memberId,
        postalCode: '100-0002',
        // 他のフィールドは指定しない（undefinedのまま）
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.member).toBeDefined();
    expect(response.member?.memberId).toBe(member.memberId);

    // 指定されたフィールドのみが更新される
    expect(response.member?.postalCode).toBe('100-0002');

    // 指定されていないフィールドは元の値のまま
    expect(response.member?.prefecture).toBe('東京都');
    expect(response.member?.address).toBe('千代田区千代田1-1');
    expect(response.member?.apartment).toBe('アパート202');
    expect(response.member?.phoneNumber).toBe('03-1234-5678');
  });

  it('空文字列でフィールドをクリアできる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const membershipApplication = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
      postalCode: '100-0001',
      prefecture: '東京都',
      address: '千代田区千代田1-1',
      apartment: 'アパート202', // 初期値として建物名を設定
      phoneNumber: '03-1234-5678',
    });

    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    // 建物名を空文字列でクリア
    const response = await client.updateMember(
      {
        memberId: member.memberId,
        apartment: '', // 建物名をクリア
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.member).toBeDefined();
    expect(response.member?.memberId).toBe(member.memberId);

    // 空文字列で更新されたフィールドが空になっている
    expect(response.member?.apartment).toBe('');

    // 他のフィールドは元の値のまま
    expect(response.member?.postalCode).toBe('100-0001');
    expect(response.member?.prefecture).toBe('東京都');
    expect(response.member?.address).toBe('千代田区千代田1-1');
    expect(response.member?.phoneNumber).toBe('03-1234-5678');
  });
});
