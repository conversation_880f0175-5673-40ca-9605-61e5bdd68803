import { Code, ConnectError } from '@connectrpc/connect';
import { MemberFactory, UserFactory, MembershipApplicationFactory, MailVerificationFactory } from '@core-test/index';
import { client } from '@core-api/utils/prisma';
import { cancelRetirementHandler } from './cancel_retirement';

describe('cancelRetirementHandler', () => {
  describe('正常なケース', () => {
    it('退会予定を正常にキャンセルできる', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const retirementDate = new Date(Date.now() + 1000 * 60 * 60 * 24 * 30); // 30日後
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        retirementDate,
        firstName: '退会予定者',
      });

      // 退会日が設定されていることを確認
      expect(member.retirementDate).toBeDefined();
      expect(member.retirementDate).not.toBeNull();

      const request = {
        memberId: member.memberId,
      };

      // ===== Act =====
      const result = await cancelRetirementHandler(request, {} as any);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // データベースから再取得して退会日がnullになっていることを確認
      const updatedMember = await client.member.findUnique({
        where: { memberId: member.memberId },
      });
      expect(updatedMember).toBeDefined();
      expect(updatedMember!.retirementDate).toBeNull();
    });

    it('既に退会日がnullの会員でもエラーにならない', async () => {
      // ===== Arrange =====
      const mailVerification = await MailVerificationFactory.create();
      const application = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
        membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
        retirementDate: null, // 既にnull
        firstName: '現役会員',
      });

      // 退会日がnullであることを確認
      expect(member.retirementDate).toBeNull();

      const request = {
        memberId: member.memberId,
      };

      // ===== Act =====
      const result = await cancelRetirementHandler(request, {} as any);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // データベースから再取得して退会日がnullのままであることを確認
      const updatedMember = await client.member.findUnique({
        where: { memberId: member.memberId },
      });
      expect(updatedMember).toBeDefined();
      expect(updatedMember!.retirementDate).toBeNull();
    });
  });

  describe('エラーケース', () => {
    it('存在しない会員IDの場合はNotFoundエラーを返す', async () => {
      // ===== Arrange =====
      const request = {
        memberId: 99999,
      };

      // ===== Act =====
      const result = await cancelRetirementHandler(request, {} as any);

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(ConnectError);
        const error = result.error as ConnectError;
        expect(error.code).toBe(Code.NotFound);
      }
    });

    it('無効なmemberIdの場合はInvalidArgumentエラーを返す', async () => {
      // ===== Arrange =====
      const request = {
        memberId: -1, // 無効な値
      };

      // ===== Act =====
      const result = await cancelRetirementHandler(request, {} as any);

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(ConnectError);
        const error = result.error as ConnectError;
        expect(error.code).toBe(Code.InvalidArgument);
      }
    });

    it('memberIdが0の場合はInvalidArgumentエラーを返す', async () => {
      // ===== Arrange =====
      const request = {
        memberId: 0, // 無効な値
      };

      // ===== Act =====
      const result = await cancelRetirementHandler(request, {} as any);

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error).toBeInstanceOf(ConnectError);
        const error = result.error as ConnectError;
        expect(error.code).toBe(Code.InvalidArgument);
      }
    });
  });


});
