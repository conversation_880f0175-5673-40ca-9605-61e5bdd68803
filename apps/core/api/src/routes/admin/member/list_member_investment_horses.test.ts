import { Code, ConnectError } from '@connectrpc/connect';
import {
  MemberFactory,
  UserFactory,
  MembershipApplicationFactory,
  MailVerificationFactory,
  AdminUserSessionFactory,
  HorseFactory,
  InvestmentContractFactory,
} from '@core-test/index';
import { MemberService } from '@hami/core-admin-api-schema/member_service_pb';
import { InvestmentContractStatus } from '@hami/prisma';

describe('listMemberInvestmentHorses', () => {
  it('指定した会員の出資馬一覧を取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    // 馬を作成
    const horse1 = await HorseFactory.create({
      recruitmentYear: 2024,
      recruitmentNo: 123,
      horseName: '出資馬1',
      recruitmentName: '出資馬1の24',
    });

    const horse2 = await HorseFactory.create({
      recruitmentYear: 2024,
      recruitmentNo: 456,
      horseName: '出資馬2',
      recruitmentName: '出資馬2の24',
    });

    // 投資契約を作成
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      member: { connect: { memberId: member.memberId } },
      sharesNumber: 10,
      investmentAmount: 1000000,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse2.horseId } },
      member: { connect: { memberId: member.memberId } },
      sharesNumber: 5,
      investmentAmount: 500000,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMemberInvestmentHorses({ memberId: member.memberId }, { headers });

    // ===== Assert =====
    expect(response.horses).toHaveLength(2);
    
    const investmentHorses = response.horses.sort((a, b) => a.horseId - b.horseId);
    expect(investmentHorses[0].horseId).toBe(horse1.horseId);
    expect(investmentHorses[0].horseName).toBe('出資馬1');
    expect(investmentHorses[0].investmentShares).toBe(10);
    expect(investmentHorses[0].investmentAmount).toBe(1000000);
    
    expect(investmentHorses[1].horseId).toBe(horse2.horseId);
    expect(investmentHorses[1].horseName).toBe('出資馬2');
    expect(investmentHorses[1].investmentShares).toBe(5);
    expect(investmentHorses[1].investmentAmount).toBe(500000);
  });

  it('同じ馬に複数の契約がある場合は口数と金額を合計する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    // 馬を作成
    const horse = await HorseFactory.create({
      recruitmentYear: 2024,
      recruitmentNo: 789,
      horseName: '合計テスト馬',
      recruitmentName: '合計テスト馬の24',
    });

    // 同じ馬に複数の投資契約を作成
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      sharesNumber: 10,
      investmentAmount: 1000000,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      sharesNumber: 5,
      investmentAmount: 500000,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMemberInvestmentHorses({ memberId: member.memberId }, { headers });

    // ===== Assert =====
    expect(response.horses).toHaveLength(1);
    expect(response.horses[0].horseId).toBe(horse.horseId);
    expect(response.horses[0].investmentShares).toBe(15); // 10 + 5
    expect(response.horses[0].investmentAmount).toBe(1500000); // 1000000 + 500000
  });

  it('COMPLETED以外の契約は除外される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    // 馬を作成
    const horse = await HorseFactory.create({
      recruitmentYear: 2024,
      recruitmentNo: 999,
      horseName: 'ステータステスト馬',
      recruitmentName: 'ステータステスト馬の24',
    });

    // 異なるステータスの投資契約を作成
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      sharesNumber: 10,
      investmentAmount: 1000000,
      contractStatus: InvestmentContractStatus.PENDING, // 完了ではない
    });

    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMemberInvestmentHorses({ memberId: member.memberId }, { headers });

    // ===== Assert =====
    expect(response.horses).toHaveLength(0);
  });

  it('存在しない会員IDの場合は空の配列を返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.listMemberInvestmentHorses({ memberId: 99999 }, { headers });

    // ===== Assert =====
    expect(response.horses).toHaveLength(0);
  });

  it('バリデーションエラーの場合はInvalidArgumentエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    await expect(
      client.listMemberInvestmentHorses({ memberId: -1 }, { headers })
    ).rejects.toThrow(ConnectError);
    
    try {
      await client.listMemberInvestmentHorses({ memberId: -1 }, { headers });
    } catch (error) {
      expect(error).toBeInstanceOf(ConnectError);
      if (error instanceof ConnectError) {
        expect(error.code).toBe(Code.InvalidArgument);
      }
    }
  });

  it('認証されていない場合はUnauthenticatedエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MemberService);

    // ===== Act/Assert =====
    await expect(
      client.listMemberInvestmentHorses({ memberId: 1 })
    ).rejects.toThrow(ConnectError);
    
    try {
      await client.listMemberInvestmentHorses({ memberId: 1 });
    } catch (error) {
      expect(error).toBeInstanceOf(ConnectError);
      if (error instanceof ConnectError) {
        expect(error.code).toBe(Code.Unauthenticated);
      }
    }
  });
});
