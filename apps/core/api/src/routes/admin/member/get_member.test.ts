import { Code, ConnectError } from '@connectrpc/connect';
import {
  MemberFactory,
  UserFactory,
  MembershipApplicationFactory,
  MailVerificationFactory,
  AdminUserSessionFactory,
} from '@core-test/index';
import { MemberService } from '@hami/core-admin-api-schema/member_service_pb';

describe('getMember', () => {
  it('指定したIDの会員を取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
    });

    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.getMember({ memberId: member.memberId }, { headers });

    // ===== Assert =====
    expect(response.member).toBeDefined();
    expect(response.member?.memberId).toBe(member.memberId);
    expect(response.member?.memberNumber).toBe(member.memberNumber);
    expect(response.member?.email).toBe(user.email);
    expect(response.member?.firstName).toBe(member.firstName);
    expect(response.member?.lastName).toBe(member.lastName);
  });

  it('存在しない会員IDの場合はNotFoundエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.getMember({ memberId: 9999 }, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
      expect(error.message).toContain('Member not found');
    }
  });

  it('バリデーションエラーの場合はInvalidArgumentエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      // @ts-ignore - Intentionally passing invalid memberId for testing
      await client.getMember({ memberId: -1 }, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('認証されていない場合はUnauthenticatedエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MemberService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.getMember({ memberId: 1 });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('無効なセッショントークンの場合はUnauthenticatedエラーを返す', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MemberService);
    const headers = new Headers({
      sessionToken: 'invalid-session-token',
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.getMember({ memberId: 1 }, { headers });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });
});
