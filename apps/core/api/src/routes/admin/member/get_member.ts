import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { MemberSchema, GetMemberResponseSchema } from '@hami/core-admin-api-schema/member_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { findMemberById, MemberNotFoundError } from '@core-api/repositories/member_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getMemberHandler = createHandler({
  schema: z.object({
    memberId: z.number().int().positive(),
  }),
  business: ({ memberId }) => findMemberById({ memberId }),
  toResponse: (member) =>
    create(GetMemberResponseSchema, {
      member: create(MemberSchema, {
        memberId: member.memberId,
        memberNumber: member.memberNumber,
        email: member.user.email,
        firstName: member.firstName,
        lastName: member.lastName,
        firstNameKana: member.firstNameKana,
        lastNameKana: member.lastNameKana,
        postalCode: member.postalCode,
        prefecture: member.prefecture,
        address: member.address,
        apartment: member.apartment ?? undefined,
        phoneNumber: member.phoneNumber,
        birthYear: member.birthYear ?? undefined,
        birthMonth: member.birthMonth ?? undefined,
        birthDay: member.birthDay ?? undefined,
        approvedAt: BigInt(member.approvedAt.getTime()),
        approvedBy: member.approvedBy ?? '',
        membershipApplicationId: member.membershipApplicationId,
        retirementAt: (() => {
          if (member.retirementDate && member.retirementDate instanceof Date && !isNaN(member.retirementDate.getTime())) {
            // 退会日の日本時間23:59:59のタイムスタンプを返す
            const retirementDate = new Date(member.retirementDate);
            // 日本時間（UTC+9）で23:59:59に設定
            retirementDate.setUTCHours(23 - 9, 59, 59, 999); // UTC+9なので9時間引く
            const timestamp = Math.floor(retirementDate.getTime() / 1000);
            return timestamp;
          }
          return undefined;
        })(),
        createdAt: BigInt(member.createdAt.getTime()),
        updatedAt: BigInt(member.updatedAt.getTime()),
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(MemberNotFoundError), () => new ConnectError('Member not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
