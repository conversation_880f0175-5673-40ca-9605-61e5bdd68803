import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  ListMemberInvestmentHorsesResponseSchema,
  MemberInvestmentHorseSchema,
} from '@hami/core-admin-api-schema/member_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { listMemberInvestmentHorses } from '@core-api/repositories/member_repository';
import { convertPrismaGenderToProtoGender } from '@core-api/routes/admin/horse/utils/convert_utils';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const listMemberInvestmentHorsesHandler = createHandler({
  schema: z.object({
    memberId: z.number().int().positive(),
  }),
  business: ({ memberId }) => listMemberInvestmentHorses({ memberId }),
  toResponse: (horses) =>
    create(ListMemberInvestmentHorsesResponseSchema, {
      horses: horses.map((horse) => {
        const baseData = {
          horseId: horse.horseId,
          birthYear: horse.birthYear,
          horseName: horse.horseName,
          investmentShares: horse.investmentShares,
          investmentAmount: horse.investmentAmount,
        };
        
        // genderをPrismaからProtobufに変換
        const genderEnum = convertPrismaGenderToProtoGender(horse.gender);
        
        const dataWithGender = genderEnum !== undefined
          ? { ...baseData, gender: genderEnum }
          : baseData;
          
        return create(MemberInvestmentHorseSchema, dataWithGender);
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
