import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { MemberSchema, UpdateMemberResponseSchema } from '@hami/core-admin-api-schema/member_service_pb';
import type { Prisma } from '@hami/prisma';
import { checkAdminUserExists, UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories';
import { updateMember, MemberNotFoundError, type MemberUpdateData } from '@core-api/repositories/member_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const schema = z.object({
  memberId: z.number().int().positive(),
  postalCode: z.string().optional(),
  prefecture: z.string().optional(),
  address: z.string().optional(),
  apartment: z.string().optional(),
  phoneNumber: z.string().optional(),
});

/**
 * undefinedでない値のみを含むオブジェクトを作成
 */
const filterDefinedValues = (data: MemberUpdateData): MemberUpdateData =>
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  Object.fromEntries(Object.entries(data).filter(([, value]) => value !== undefined)) as MemberUpdateData;

/**
 * リクエストからMemberUpdateDataオブジェクトを構築
 */
const buildUpdateDataFromRequest = (req: z.infer<typeof schema>): MemberUpdateData => {
  // memberId を除外し、残りを MemberUpdateData とみなす
  const { memberId: _memberId, ...rest } = req;

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return filterDefinedValues(rest as MemberUpdateData);
};

export const updateMemberHandler = createHandler({
  schema,
  business: (req, ctx) =>
    checkAdminUserExists(ctx).asyncAndThen(() => {
      const updateData = buildUpdateDataFromRequest(req);

      return updateMember({
        memberId: req.memberId,
        data: updateData,
      });
    }),
  toResponse: (
    member: Prisma.MemberGetPayload<{
      include: {
        user: true;
        membershipApplication: {
          include: {
            mailVerification: true;
          };
        };
      };
    }>
  ) =>
    create(UpdateMemberResponseSchema, {
      member: create(MemberSchema, {
        memberId: member.memberId,
        memberNumber: member.memberNumber,
        email: member.user.email,
        firstName: member.firstName,
        lastName: member.lastName,
        firstNameKana: member.firstNameKana,
        lastNameKana: member.lastNameKana,
        postalCode: member.postalCode,
        prefecture: member.prefecture,
        address: member.address,
        apartment: member.apartment ?? undefined,
        phoneNumber: member.phoneNumber,
        birthYear: member.birthYear ?? undefined,
        birthMonth: member.birthMonth ?? undefined,
        birthDay: member.birthDay ?? undefined,
        approvedAt: BigInt(member.approvedAt.getTime()),
        approvedBy: member.approvedBy ?? '',
        membershipApplicationId: member.membershipApplicationId,
        createdAt: BigInt(member.createdAt.getTime()),
        updatedAt: BigInt(member.updatedAt.getTime()),
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), (e) => new ConnectError(e.message, Code.Unauthenticated))
      .with(P.instanceOf(MemberNotFoundError), () => new ConnectError('会員が見つかりません', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
