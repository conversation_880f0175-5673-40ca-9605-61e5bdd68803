import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  GetMemberInformationChangeApplicationsResponseSchema,
  MemberInformationChangeApplicationSchema,
  MemberInformationChangeStatus,
  MemberInformationChangeSortField,
} from '@hami/core-admin-api-schema/member_information_change_service_pb';
import {
  MemberInformationChangeStatus as PrismaMemberInformationChangeStatus,
  MemberInformationChangeApplication,
  Member,
  User,
  MemberInformationChangeReviewLog,
} from '@hami/prisma';
import { checkAdminUserExists, UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories/index';
import { getAdminMemberInformationChangeApplications } from '@core-api/repositories/member_information_change_repository';
import { createHand<PERSON> } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

// Type for admin applications response
type AdminApplicationWithRelations = MemberInformationChangeApplication & {
  member: Member & {
    user: User;
  };
  reviewLogs: MemberInformationChangeReviewLog[];
};

// Type for sort order
type SortOrder = 'asc' | 'desc';

// Type guard for sort order
const isSortOrder = (value: string): value is SortOrder => {
  return ['asc', 'desc'].includes(value);
};

// Type for response data
type AdminApplicationsResponse = {
  applications: AdminApplicationWithRelations[];
  totalCount: number;
  pagination: {
    page: number;
    limit: number;
  };
};

const schema = z.object({
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).max(100).optional(),
  status: z.nativeEnum(MemberInformationChangeStatus).optional(),
  sortBy: z.nativeEnum(MemberInformationChangeSortField).optional(),
  sortOrder: z.string().optional(),
});

const statusToPrismaMapping = {
  [PrismaMemberInformationChangeStatus.PENDING]: MemberInformationChangeStatus.PENDING,
  [PrismaMemberInformationChangeStatus.APPROVED]: MemberInformationChangeStatus.APPROVED,
  [PrismaMemberInformationChangeStatus.REJECTED]: MemberInformationChangeStatus.REJECTED,
  [PrismaMemberInformationChangeStatus.CANCELLED]: MemberInformationChangeStatus.CANCELLED,
} satisfies Record<PrismaMemberInformationChangeStatus, MemberInformationChangeStatus>;

const convertStatusToProto = (status: PrismaMemberInformationChangeStatus): MemberInformationChangeStatus => {
  return statusToPrismaMapping[status] ?? MemberInformationChangeStatus.UNSPECIFIED;
};

const convertStatusFromProto = (status?: MemberInformationChangeStatus): PrismaMemberInformationChangeStatus | undefined => {
  if (status === undefined) return undefined;
  switch (status) {
    case MemberInformationChangeStatus.PENDING:
      return PrismaMemberInformationChangeStatus.PENDING;
    case MemberInformationChangeStatus.APPROVED:
      return PrismaMemberInformationChangeStatus.APPROVED;
    case MemberInformationChangeStatus.REJECTED:
      return PrismaMemberInformationChangeStatus.REJECTED;
    case MemberInformationChangeStatus.CANCELLED:
      return PrismaMemberInformationChangeStatus.CANCELLED;
    default:
      return undefined;
  }
};

// enum値をrepository層で使用する文字列に変換
const convertSortFieldFromProto = (sortField?: MemberInformationChangeSortField): string => {
  if (sortField === undefined) return 'createdAt';
  switch (sortField) {
    case MemberInformationChangeSortField.CREATED_AT:
      return 'createdAt';
    case MemberInformationChangeSortField.UPDATED_AT:
      return 'updatedAt';
    case MemberInformationChangeSortField.REQUESTED_CHANGE_DATE:
      return 'requestedChangeDate';
    case MemberInformationChangeSortField.MEMBER_INFORMATION_CHANGE_APPLICATION_ID:
      return 'memberInformationChangeApplicationId';
    case MemberInformationChangeSortField.STATUS:
      return 'status';
    case MemberInformationChangeSortField.MEMBER_NUMBER:
      return 'memberNumber';
    case MemberInformationChangeSortField.UNSPECIFIED:
    default:
      return 'createdAt';
  }
};

export const getAdminMemberInformationChangeApplicationsHandler = createHandler({
  schema,
  business: (req, ctx) =>
    ok(req)
      .andThen(() => checkAdminUserExists(ctx))
      .asyncAndThen(() => {
        const page = req.page ?? 1;
        const limit = req.limit ?? 10;
        const sortOrderValue = req.sortOrder ?? 'desc';
        const sortOrder = isSortOrder(sortOrderValue) ? sortOrderValue : 'desc';
        return getAdminMemberInformationChangeApplications({
          page,
          limit,
          status: req.status ? convertStatusFromProto(req.status) : undefined,
          sortBy: convertSortFieldFromProto(req.sortBy),
          sortOrder,
        }).map(
          (result): AdminApplicationsResponse => ({
            ...result,
            pagination: {
              page,
              limit,
            },
          })
        );
      }),
  toResponse: (data: AdminApplicationsResponse) => {
    const { applications, totalCount, pagination } = data;
    return create(GetMemberInformationChangeApplicationsResponseSchema, {
      applications: applications.map((application) =>
        create(MemberInformationChangeApplicationSchema, {
          memberInformationChangeApplicationId: application.memberInformationChangeApplicationId,
          memberId: application.memberId,
          memberNumber: application.member.memberNumber,
          memberEmail: application.member.user.email,
          memberName: `${application.member.lastName} ${application.member.firstName}`,

          // 現在の登録情報
          currentPostalCode: application.member.postalCode,
          currentPrefecture: application.member.prefecture,
          currentAddress: application.member.address,
          currentApartment: application.member.apartment || undefined,
          currentPhoneNumber: application.member.phoneNumber,

          // 変更希望情報
          newPostalCode: application.postalCode || undefined,
          newPrefecture: application.prefecture || undefined,
          newAddress: application.address || undefined,
          newApartment: application.apartment || undefined,
          newPhoneNumber: application.phoneNumber || undefined,

          requestedChangeDate: {
            seconds: BigInt(Math.floor(application.requestedChangeDate.getTime() / 1000)),
            nanos: (application.requestedChangeDate.getTime() % 1000) * 1000000,
          },
          reason: application.reason || undefined,
          status: convertStatusToProto(application.status),
          createdAt: {
            seconds: BigInt(Math.floor(application.createdAt.getTime() / 1000)),
            nanos: (application.createdAt.getTime() % 1000) * 1000000,
          },
          updatedAt: {
            seconds: BigInt(Math.floor(application.updatedAt.getTime() / 1000)),
            nanos: (application.updatedAt.getTime() % 1000) * 1000000,
          },
        })
      ),
      totalCount,
      page: pagination.page,
      limit: pagination.limit,
    });
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), (e) => new ConnectError(e.message, Code.Unauthenticated))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
