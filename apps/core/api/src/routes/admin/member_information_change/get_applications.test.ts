import { Code, ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MemberFactory } from '@core-test/factories/member_factory';
import { MemberInformationChangeApplicationFactory } from '@core-test/factories/member_information_change_application_factory';
import { UserFactory } from '@core-test/factories/user_factory';
import {
  MemberInformationChangeService,
  MemberInformationChangeStatus,
  MemberInformationChangeSortField,
} from '@hami/core-admin-api-schema/member_information_change_service_pb';
import { MemberInformationChangeStatus as PrismaMemberInformationChangeStatus } from '@hami/prisma';

describe('getMemberInformationChangeApplications', () => {
  it('申請一覧を正常に取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    const user1 = await UserFactory.create({ email: '<EMAIL>' });
    const member1 = await MemberFactory.create({
      user: { connect: { userId: user1.userId } },
      memberNumber: 12345,
      lastName: '田中',
      firstName: '太郎',
    });

    const user2 = await UserFactory.create({ email: '<EMAIL>' });
    const member2 = await MemberFactory.create({
      user: { connect: { userId: user2.userId } },
      memberNumber: 12346,
      lastName: '佐藤',
      firstName: '花子',
    });

    await MemberInformationChangeApplicationFactory.create({
      member: { connect: { memberId: member1.memberId } },
      status: PrismaMemberInformationChangeStatus.PENDING,
    });

    await MemberInformationChangeApplicationFactory.create({
      member: { connect: { memberId: member2.memberId } },
      status: PrismaMemberInformationChangeStatus.APPROVED,
    });

    const client = getClient(MemberInformationChangeService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.getMemberInformationChangeApplications(
      {
        page: 1,
        limit: 10,
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.applications).toBeDefined();
    expect(response.applications.length).toBe(2);
    expect(response.totalCount).toBe(2);
    expect(response.page).toBe(1);
    expect(response.limit).toBe(10);

    // 最初の申請をチェック
    const firstApp = response.applications[0];
    expect(firstApp.memberNumber).toBeOneOf([12345, 12346]);
    expect(firstApp.memberEmail).toBeOneOf(['<EMAIL>', '<EMAIL>']);
  });

  it('ステータスでフィルタリングできる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    const user1 = await UserFactory.create();
    const member1 = await MemberFactory.create({
      user: { connect: { userId: user1.userId } },
    });

    const user2 = await UserFactory.create();
    const member2 = await MemberFactory.create({
      user: { connect: { userId: user2.userId } },
    });

    await MemberInformationChangeApplicationFactory.create({
      member: { connect: { memberId: member1.memberId } },
      status: PrismaMemberInformationChangeStatus.PENDING,
    });

    await MemberInformationChangeApplicationFactory.create({
      member: { connect: { memberId: member2.memberId } },
      status: PrismaMemberInformationChangeStatus.APPROVED,
    });

    const client = getClient(MemberInformationChangeService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.getMemberInformationChangeApplications(
      {
        page: 1,
        limit: 10,
        status: MemberInformationChangeStatus.PENDING,
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.applications).toBeDefined();
    expect(response.applications.length).toBe(1);
    expect(response.totalCount).toBe(1);
    expect(response.applications[0].status).toBe(MemberInformationChangeStatus.PENDING);
  });

  it('認証されていない場合は401エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MemberInformationChangeService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.getMemberInformationChangeApplications({
        page: 1,
        limit: 10,
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('ページネーションが正常に動作する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    // 3つの申請を作成
    for (let i = 0; i < 3; i++) {
      const user = await UserFactory.create();
      const member = await MemberFactory.create({
        user: { connect: { userId: user.userId } },
      });
      await MemberInformationChangeApplicationFactory.create({
        member: { connect: { memberId: member.memberId } },
      });
    }

    const client = getClient(MemberInformationChangeService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.getMemberInformationChangeApplications(
      {
        page: 1,
        limit: 2,
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.applications).toBeDefined();
    expect(response.applications.length).toBe(2);
    expect(response.totalCount).toBe(3);
    expect(response.page).toBe(1);
    expect(response.limit).toBe(2);
  });
});
