import { MemberInformationChangeService } from '@hami/core-admin-api-schema/member_information_change_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { getAdminMemberInformationChangeApplicationHandler } from './get_application';
import { getAdminMemberInformationChangeApplicationsHandler } from './get_applications';
import { reviewMemberInformationChangeApplicationHandler } from './review_application';

import type { ConnectRouter } from '@connectrpc/connect';

export const implMemberInformationChangeService = (router: ConnectRouter) => {
  router.service(
    MemberInformationChangeService,
    {
      getMemberInformationChangeApplications: unwrapResult(getAdminMemberInformationChangeApplicationsHandler),
      getMemberInformationChangeApplication: unwrapResult(getAdminMemberInformationChangeApplicationHandler),
      reviewMemberInformationChangeApplication: unwrapResult(reviewMemberInformationChangeApplicationHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  );
};
