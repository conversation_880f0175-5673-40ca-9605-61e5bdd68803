import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  ReviewMemberInformationChangeApplicationResponseSchema,
  MemberInformationChangeApplicationSchema,
  MemberInformationChangeStatus,
  MemberInformationChangeReviewType,
} from '@hami/core-admin-api-schema/member_information_change_service_pb';
import { MemberInformationChangeStatus as PrismaMemberInformationChangeStatus, ReviewType as PrismaReviewType } from '@hami/prisma';
import { checkAdminUserExists, UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories/index';
import {
  reviewMemberInformationChangeApplication,
  MemberInformationChangeApplicationNotFoundError,
  InvalidStatusError,
} from '@core-api/repositories/member_information_change_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const schema = z.object({
  memberInformationChangeApplicationId: z.number().int().min(1),
  reviewType: z.nativeEnum(MemberInformationChangeReviewType),
  comment: z.string().optional(),
});

const statusMapping = {
  [PrismaMemberInformationChangeStatus.PENDING]: MemberInformationChangeStatus.PENDING,
  [PrismaMemberInformationChangeStatus.APPROVED]: MemberInformationChangeStatus.APPROVED,
  [PrismaMemberInformationChangeStatus.REJECTED]: MemberInformationChangeStatus.REJECTED,
  [PrismaMemberInformationChangeStatus.CANCELLED]: MemberInformationChangeStatus.CANCELLED,
} satisfies Record<PrismaMemberInformationChangeStatus, MemberInformationChangeStatus>;

const convertStatusToProto = (status: PrismaMemberInformationChangeStatus): MemberInformationChangeStatus => {
  return statusMapping[status] ?? MemberInformationChangeStatus.UNSPECIFIED;
};

const convertReviewTypeFromProto = (reviewType: MemberInformationChangeReviewType): PrismaReviewType => {
  switch (reviewType) {
    case MemberInformationChangeReviewType.APPROVE:
      return PrismaReviewType.APPROVE;
    case MemberInformationChangeReviewType.REJECT:
      return PrismaReviewType.REJECT;
    default:
      throw new Error(
        `Invalid review type: ${reviewType}. Only APPROVE and REJECT are supported for member information change applications.`
      );
  }
};

export const reviewMemberInformationChangeApplicationHandler = createHandler({
  schema,
  business: (req, ctx) =>
    ok(req)
      .andThen(() => checkAdminUserExists(ctx))
      .asyncAndThen((adminUser) =>
        reviewMemberInformationChangeApplication({
          memberInformationChangeApplicationId: req.memberInformationChangeApplicationId,
          reviewType: convertReviewTypeFromProto(req.reviewType),
          reviewer: adminUser.adminUserId,
          comment: req.comment,
        })
      ),
  toResponse: (application) =>
    create(ReviewMemberInformationChangeApplicationResponseSchema, {
      application: create(MemberInformationChangeApplicationSchema, {
        memberInformationChangeApplicationId: application.memberInformationChangeApplicationId,
        memberId: application.memberId,
        memberNumber: application.member.memberNumber,
        memberEmail: application.member.user.email,
        memberName: `${application.member.lastName} ${application.member.firstName}`,

        // 現在の登録情報
        currentPostalCode: application.member.postalCode,
        currentPrefecture: application.member.prefecture,
        currentAddress: application.member.address,
        currentApartment: application.member.apartment || undefined,
        currentPhoneNumber: application.member.phoneNumber,

        // 変更希望情報
        newPostalCode: application.postalCode || undefined,
        newPrefecture: application.prefecture || undefined,
        newAddress: application.address || undefined,
        newApartment: application.apartment || undefined,
        newPhoneNumber: application.phoneNumber || undefined,

        requestedChangeDate: {
          seconds: BigInt(Math.floor(application.requestedChangeDate.getTime() / 1000)),
          nanos: (application.requestedChangeDate.getTime() % 1000) * 1000000,
        },
        reason: application.reason || undefined,
        status: convertStatusToProto(application.status),
        createdAt: {
          seconds: BigInt(Math.floor(application.createdAt.getTime() / 1000)),
          nanos: (application.createdAt.getTime() % 1000) * 1000000,
        },
        updatedAt: {
          seconds: BigInt(Math.floor(application.updatedAt.getTime() / 1000)),
          nanos: (application.updatedAt.getTime() % 1000) * 1000000,
        },
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), (e) => new ConnectError(e.message, Code.Unauthenticated))
      .with(P.instanceOf(MemberInformationChangeApplicationNotFoundError), () => new ConnectError('Application not found', Code.NotFound))
      .with(P.instanceOf(InvalidStatusError), () => new ConnectError('Invalid application status', Code.FailedPrecondition))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
