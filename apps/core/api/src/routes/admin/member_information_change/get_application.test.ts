import { Code, ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MemberFactory } from '@core-test/factories/member_factory';
import { MemberInformationChangeApplicationFactory } from '@core-test/factories/member_information_change_application_factory';
import { MemberInformationChangeReviewLogFactory } from '@core-test/factories/member_information_change_review_log_factory';
import { UserFactory } from '@core-test/factories/user_factory';
import { MemberInformationChangeService } from '@hami/core-admin-api-schema/member_information_change_service_pb';
import { MemberInformationChangeStatus, ReviewType } from '@hami/prisma';

describe('getMemberInformationChangeApplication', () => {
  it('申請詳細を正常に取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    const user = await UserFactory.create({
      email: '<EMAIL>',
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      memberNumber: 12345,
      lastName: '田中',
      firstName: '太郎',
      postalCode: '100-0001',
      prefecture: '東京都',
      address: '千代田区千代田1-1',
      apartment: '千代田マンション101',
      phoneNumber: '03-1234-5678',
    });

    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 30);

    const application = await MemberInformationChangeApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      postalCode: '160-0022',
      prefecture: '東京都',
      address: '新宿区新宿1-1',
      apartment: '新宿マンション202',
      phoneNumber: '03-9876-5432',
      requestedChangeDate: futureDate,
      reason: '引越しのため',
      status: MemberInformationChangeStatus.PENDING,
    });

    const client = getClient(MemberInformationChangeService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.getMemberInformationChangeApplication(
      {
        memberInformationChangeApplicationId: application.memberInformationChangeApplicationId,
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.application).toBeDefined();
    expect(response.application?.memberInformationChangeApplicationId).toBe(application.memberInformationChangeApplicationId);
    expect(response.application?.memberId).toBe(member.memberId);
    expect(response.application?.memberNumber).toBe(12345);
    expect(response.application?.memberEmail).toBe('<EMAIL>');
    expect(response.application?.memberName).toBe('田中 太郎');

    // 現在の登録情報
    expect(response.application?.currentPostalCode).toBe('100-0001');
    expect(response.application?.currentPrefecture).toBe('東京都');
    expect(response.application?.currentAddress).toBe('千代田区千代田1-1');
    expect(response.application?.currentApartment).toBe('千代田マンション101');
    expect(response.application?.currentPhoneNumber).toBe('03-1234-5678');

    // 変更希望情報
    expect(response.application?.newPostalCode).toBe('160-0022');
    expect(response.application?.newPrefecture).toBe('東京都');
    expect(response.application?.newAddress).toBe('新宿区新宿1-1');
    expect(response.application?.newApartment).toBe('新宿マンション202');
    expect(response.application?.newPhoneNumber).toBe('03-9876-5432');

    expect(response.application?.reason).toBe('引越しのため');
    expect(response.application?.status).toBe(1); // PENDING

    expect(response.reviewLogs).toBeDefined();
    expect(Array.isArray(response.reviewLogs)).toBe(true);
  });

  it('認証されていない場合は401エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MemberInformationChangeService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.getMemberInformationChangeApplication({
        memberInformationChangeApplicationId: 1,
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('存在しない申請IDの場合は404エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MemberInformationChangeService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.getMemberInformationChangeApplication(
        {
          memberInformationChangeApplicationId: 99999, // 存在しないID
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
      expect(error.message).toContain('Member information change application not found');
    }
  });
});
