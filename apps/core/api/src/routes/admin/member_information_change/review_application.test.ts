import { Code, ConnectError } from '@connectrpc/connect';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MemberFactory } from '@core-test/factories/member_factory';
import { MemberInformationChangeApplicationFactory } from '@core-test/factories/member_information_change_application_factory';
import { UserFactory } from '@core-test/factories/user_factory';
import {
  MemberInformationChangeService,
  MemberInformationChangeReviewType,
} from '@hami/core-admin-api-schema/member_information_change_service_pb';
import { MemberInformationChangeStatus } from '@hami/prisma';

describe('reviewMemberInformationChangeApplication', () => {
  it('申請を承認できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      postalCode: '100-0001',
      prefecture: '東京都',
      address: '千代田区千代田1-1',
      phoneNumber: '03-1234-5678',
    });

    const application = await MemberInformationChangeApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      postalCode: '160-0022',
      prefecture: '東京都',
      address: '新宿区新宿1-1',
      phoneNumber: '03-9876-5432',
      status: MemberInformationChangeStatus.PENDING,
    });

    const client = getClient(MemberInformationChangeService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.reviewMemberInformationChangeApplication(
      {
        memberInformationChangeApplicationId: application.memberInformationChangeApplicationId,
        reviewType: MemberInformationChangeReviewType.APPROVE,
        comment: '承認します',
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.application).toBeDefined();
    expect(response.application?.status).toBe(2); // APPROVED

    // 会員情報が更新されているかチェック
    const updatedMember = await globalThis.vPrisma.client.member.findUnique({
      where: { memberId: member.memberId },
    });
    expect(updatedMember?.postalCode).toBe('160-0022');
    expect(updatedMember?.prefecture).toBe('東京都');
    expect(updatedMember?.address).toBe('新宿区新宿1-1');
    expect(updatedMember?.phoneNumber).toBe('03-9876-5432');

    // 審査ログが作成されているかチェック
    const reviewLogs = await globalThis.vPrisma.client.memberInformationChangeReviewLog.findMany({
      where: { memberInformationChangeApplicationId: application.memberInformationChangeApplicationId },
    });
    expect(reviewLogs.length).toBe(1);
    expect(reviewLogs[0].reviewer).toBe(adminUserSession.adminUserId);
    expect(reviewLogs[0].comment).toBe('承認します');
  });

  it('申請を拒否できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      postalCode: '100-0001',
      prefecture: '東京都',
      address: '千代田区千代田1-1',
    });

    const application = await MemberInformationChangeApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      postalCode: '160-0022',
      status: MemberInformationChangeStatus.PENDING,
    });

    const client = getClient(MemberInformationChangeService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act =====
    const response = await client.reviewMemberInformationChangeApplication(
      {
        memberInformationChangeApplicationId: application.memberInformationChangeApplicationId,
        reviewType: MemberInformationChangeReviewType.REJECT,
        comment: '書類に不備があります',
      },
      { headers }
    );

    // ===== Assert =====
    expect(response.application).toBeDefined();
    expect(response.application?.status).toBe(3); // REJECTED

    // 会員情報は更新されていないかチェック
    const updatedMember = await globalThis.vPrisma.client.member.findUnique({
      where: { memberId: member.memberId },
    });
    expect(updatedMember?.postalCode).toBe('100-0001'); // 元のまま

    // 審査ログが作成されているかチェック
    const reviewLogs = await globalThis.vPrisma.client.memberInformationChangeReviewLog.findMany({
      where: { memberInformationChangeApplicationId: application.memberInformationChangeApplicationId },
    });
    expect(reviewLogs.length).toBe(1);
    expect(reviewLogs[0].comment).toBe('書類に不備があります');
  });

  it('認証されていない場合は401エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const client = getClient(MemberInformationChangeService);

    // ===== Act/Assert =====
    let error;
    try {
      await client.reviewMemberInformationChangeApplication({
        memberInformationChangeApplicationId: 1,
        reviewType: MemberInformationChangeReviewType.APPROVE,
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('存在しない申請IDの場合は404エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(MemberInformationChangeService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // ===== Act/Assert =====
    let error;
    try {
      await client.reviewMemberInformationChangeApplication(
        {
          memberInformationChangeApplicationId: 99999,
          reviewType: MemberInformationChangeReviewType.APPROVE,
        },
        { headers }
      );
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
    }
  });
});
