import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  GetMemberInformationChangeApplicationResponseSchema,
  MemberInformationChangeApplicationSchema,
  MemberInformationChangeReviewLogSchema,
  MemberInformationChangeStatus,
  MemberInformationChangeReviewType,
} from '@hami/core-admin-api-schema/member_information_change_service_pb';
import { MemberInformationChangeStatus as PrismaMemberInformationChangeStatus, ReviewType as PrismaReviewType } from '@hami/prisma';
import { checkAdminUserExists, UnauthorizedError } from '@core-api/middlewares/interceptors';
import { DatabaseError } from '@core-api/repositories/index';
import {
  getAdminMemberInformationChangeApplicationById,
  MemberInformationChangeApplicationNotFoundError,
  MemberInformationChangeApplicationWithRelations,
} from '@core-api/repositories/member_information_change_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const schema = z.object({
  memberInformationChangeApplicationId: z.number().int().min(1),
});

const statusMapping = {
  [PrismaMemberInformationChangeStatus.PENDING]: MemberInformationChangeStatus.PENDING,
  [PrismaMemberInformationChangeStatus.APPROVED]: MemberInformationChangeStatus.APPROVED,
  [PrismaMemberInformationChangeStatus.REJECTED]: MemberInformationChangeStatus.REJECTED,
  [PrismaMemberInformationChangeStatus.CANCELLED]: MemberInformationChangeStatus.CANCELLED,
} satisfies Record<PrismaMemberInformationChangeStatus, MemberInformationChangeStatus>;

const convertStatusToProto = (status: PrismaMemberInformationChangeStatus): MemberInformationChangeStatus => {
  return statusMapping[status] ?? MemberInformationChangeStatus.UNSPECIFIED;
};

const convertReviewTypeToProto = (reviewType: PrismaReviewType): MemberInformationChangeReviewType => {
  switch (reviewType) {
    case PrismaReviewType.APPROVE:
      return MemberInformationChangeReviewType.APPROVE;
    case PrismaReviewType.REJECT:
      return MemberInformationChangeReviewType.REJECT;
    default:
      // REMANDは会員情報変更では使用しないため、UNKNOWNを返す
      return MemberInformationChangeReviewType.UNKNOWN;
  }
};

export const getAdminMemberInformationChangeApplicationHandler = createHandler({
  schema,
  business: (req, ctx) =>
    ok(req)
      .andThen(() => checkAdminUserExists(ctx))
      .asyncAndThen(() =>
        getAdminMemberInformationChangeApplicationById({
          memberInformationChangeApplicationId: req.memberInformationChangeApplicationId,
        })
      ),
  toResponse: (application: MemberInformationChangeApplicationWithRelations) => {
    return create(GetMemberInformationChangeApplicationResponseSchema, {
      application: create(MemberInformationChangeApplicationSchema, {
        memberInformationChangeApplicationId: application.memberInformationChangeApplicationId,
        memberId: application.memberId,
        memberNumber: application.member.memberNumber,
        memberEmail: application.member.user.email,
        memberName: `${application.member.lastName} ${application.member.firstName}`,

        // 現在の登録情報
        currentPostalCode: application.member.postalCode,
        currentPrefecture: application.member.prefecture,
        currentAddress: application.member.address,
        currentApartment: application.member.apartment || undefined,
        currentPhoneNumber: application.member.phoneNumber,

        // 変更希望情報
        newPostalCode: application.postalCode || undefined,
        newPrefecture: application.prefecture || undefined,
        newAddress: application.address || undefined,
        newApartment: application.apartment || undefined,
        newPhoneNumber: application.phoneNumber || undefined,

        requestedChangeDate: {
          seconds: BigInt(Math.floor(application.requestedChangeDate.getTime() / 1000)),
          nanos: (application.requestedChangeDate.getTime() % 1000) * 1000000,
        },
        reason: application.reason || undefined,
        status: convertStatusToProto(application.status),
        createdAt: {
          seconds: BigInt(Math.floor(application.createdAt.getTime() / 1000)),
          nanos: (application.createdAt.getTime() % 1000) * 1000000,
        },
        updatedAt: {
          seconds: BigInt(Math.floor(application.updatedAt.getTime() / 1000)),
          nanos: (application.updatedAt.getTime() % 1000) * 1000000,
        },
      }),
      reviewLogs: application.reviewLogs.map((log) =>
        create(MemberInformationChangeReviewLogSchema, {
          memberInformationChangeReviewLogId: log.memberInformationChangeReviewLogId,
          memberInformationChangeApplicationId: log.memberInformationChangeApplicationId,
          timestamp: {
            seconds: BigInt(Math.floor(log.timestamp.getTime() / 1000)),
            nanos: (log.timestamp.getTime() % 1000) * 1000000,
          },
          reviewer: log.reviewer,
          reviewType: convertReviewTypeToProto(log.reviewType),
          comment: log.comment || undefined,
          createdAt: {
            seconds: BigInt(Math.floor(log.createdAt.getTime() / 1000)),
            nanos: (log.createdAt.getTime() % 1000) * 1000000,
          },
        })
      ),
    });
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), (e) => new ConnectError(e.message, Code.Unauthenticated))
      .with(
        P.instanceOf(MemberInformationChangeApplicationNotFoundError),
        () => new ConnectError('Member information change application not found', Code.NotFound)
      )
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
