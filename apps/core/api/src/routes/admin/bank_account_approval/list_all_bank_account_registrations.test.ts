import {
  AdminUserFactory,
  AdminUserSessionFactory,
  MemberFactory,
  UserFactory,
  MailVerificationFactory,
  MembershipApplicationFactory,
} from '@core-test/index';
import { getClient } from '@core-test/index';
import { BankAccountApprovalService, BankAccountApprovalStatus } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { BankAccountRegistrationStatus, ApprovalType } from '@hami/prisma';

// vPrisma.clientを使用（setup.tsでモックされているため）
const prisma = vPrisma.client;

// テストデータ作成ヘルパー
const createTestMember = async () => {
  const mailVerification = await MailVerificationFactory.create();
  const application = await MembershipApplicationFactory.create({
    mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
  });
  const user = await UserFactory.create();
  const member = await MemberFactory.create({
    user: { connect: { userId: user.userId } },
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
  });
  return { user, member, application, mailVerification };
};

describe('listAllBankAccountRegistrations', () => {
  it('全口座登録一覧を取得できること（承認済み・却下済み含む）', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const adminUser = await AdminUserFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member: member1 } = await createTestMember();
    const { member: member2 } = await createTestMember();
    const { member: member3 } = await createTestMember();

    // 承認待ち（SUCCESS状態で承認レコードなし）
    const pendingRegistration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member1.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストタロウ',
      },
    });

    // 承認済み（SUCCESS状態で承認レコードあり）
    const approvedRegistration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member2.memberId,
        bankCode: '0002',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0002',
        resultBranchCode: '002',
        resultAccountNumber: '2345678',
        resultAccountName: 'テストハナコ',
      },
    });

    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: approvedRegistration.bankAccountRegistrationId,
        approvalType: ApprovalType.APPROVE,
        adminUserId: adminUser.adminUserId,
        comment: '承認しました',
      },
    });

    // 却下済み（SUCCESS状態で却下レコードあり）
    const rejectedRegistration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member3.memberId,
        bankCode: '0003',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0003',
        resultBranchCode: '003',
        resultAccountNumber: '3456789',
        resultAccountName: 'テストジロウ',
      },
    });

    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: rejectedRegistration.bankAccountRegistrationId,
        approvalType: ApprovalType.REJECT,
        adminUserId: adminUser.adminUserId,
        rejectionReason: '名義が一致しません',
      },
    });

    // API呼び出し（フィルタなし）
    const response = await client.listAllBankAccountRegistrations(
      {
        page: 1,
        pageSize: 10,
      },
      { headers }
    );

    // レスポンス検証
    expect(response.items).toHaveLength(3);
    expect(response.totalCount).toBe(3);
    expect(response.page).toBe(1);
    expect(response.pageSize).toBe(10);

    // 各アイテムの検証
    const pendingItem = response.items.find((item) => item.memberId === member1.memberId);
    expect(pendingItem).toBeDefined();
    expect(pendingItem?.approvalStatus).toBe(BankAccountApprovalStatus.PENDING);
    expect(pendingItem?.adminUserName).toBe('');
    expect(pendingItem?.resultAccountNumber).toBe('***4567'); // 1234567 -> ***4567

    const approvedItem = response.items.find((item) => item.memberId === member2.memberId);
    expect(approvedItem).toBeDefined();
    expect(approvedItem?.approvalStatus).toBe(BankAccountApprovalStatus.APPROVED);
    expect(approvedItem?.adminUserName).toBe(adminUser.name);
    expect(approvedItem?.resultAccountNumber).toBe('***5678'); // 2345678 -> ***5678

    const rejectedItem = response.items.find((item) => item.memberId === member3.memberId);
    expect(rejectedItem).toBeDefined();
    expect(rejectedItem?.approvalStatus).toBe(BankAccountApprovalStatus.REJECTED);
    expect(rejectedItem?.adminUserName).toBe(adminUser.name);
    expect(rejectedItem?.resultAccountNumber).toBe('***6789'); // 3456789 -> ***6789
  });

  it('承認待ちのみをフィルタして取得できること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const adminUser = await AdminUserFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member: member1 } = await createTestMember();
    const { member: member2 } = await createTestMember();

    // 承認待ち
    await prisma.bankAccountRegistration.create({
      data: {
        memberId: member1.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストタロウ',
      },
    });

    // 承認済み
    const approvedRegistration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member2.memberId,
        bankCode: '0002',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0002',
        resultBranchCode: '002',
        resultAccountNumber: '2345678',
        resultAccountName: 'テストハナコ',
      },
    });

    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: approvedRegistration.bankAccountRegistrationId,
        approvalType: ApprovalType.APPROVE,
        adminUserId: adminUser.adminUserId,
        comment: '承認しました',
      },
    });

    // API呼び出し（承認待ちフィルタ）
    const response = await client.listAllBankAccountRegistrations(
      {
        page: 1,
        pageSize: 10,
        statusFilter: BankAccountApprovalStatus.PENDING,
      },
      { headers }
    );

    // レスポンス検証
    expect(response.items).toHaveLength(1);
    expect(response.totalCount).toBe(1);
    expect(response.items[0].memberId).toBe(member1.memberId);
    expect(response.items[0].approvalStatus).toBe(BankAccountApprovalStatus.PENDING);
  });

  it('承認済みのみをフィルタして取得できること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const adminUser = await AdminUserFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member: member1 } = await createTestMember();
    const { member: member2 } = await createTestMember();

    // 承認待ち
    await prisma.bankAccountRegistration.create({
      data: {
        memberId: member1.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストタロウ',
      },
    });

    // 承認済み
    const approvedRegistration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member2.memberId,
        bankCode: '0002',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0002',
        resultBranchCode: '002',
        resultAccountNumber: '2345678',
        resultAccountName: 'テストハナコ',
      },
    });

    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: approvedRegistration.bankAccountRegistrationId,
        approvalType: ApprovalType.APPROVE,
        adminUserId: adminUser.adminUserId,
        comment: '承認しました',
      },
    });

    // API呼び出し（承認済みフィルタ）
    const response = await client.listAllBankAccountRegistrations(
      {
        page: 1,
        pageSize: 10,
        statusFilter: BankAccountApprovalStatus.APPROVED,
      },
      { headers }
    );

    // レスポンス検証
    expect(response.items).toHaveLength(1);
    expect(response.totalCount).toBe(1);
    expect(response.items[0].memberId).toBe(member2.memberId);
    expect(response.items[0].approvalStatus).toBe(BankAccountApprovalStatus.APPROVED);
    expect(response.items[0].adminUserName).toBe(adminUser.name);
  });

  it('ページネーションが正しく動作すること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // 複数のテストデータ作成
    for (let i = 0; i < 5; i++) {
      const { member } = await createTestMember();
      await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: `000${i + 1}`,
          registrationStatus: BankAccountRegistrationStatus.SUCCESS,
          isActive: true,
          completedAt: new Date(),
          resultBankCode: `000${i + 1}`,
          resultBranchCode: '001',
          resultAccountNumber: `123456${i}`,
          resultAccountName: `テストユーザー${i + 1}`,
        },
      });
    }

    // 1ページ目（2件ずつ）
    const page1Response = await client.listAllBankAccountRegistrations(
      {
        page: 1,
        pageSize: 2,
      },
      { headers }
    );

    expect(page1Response.items).toHaveLength(2);
    expect(page1Response.totalCount).toBe(5);
    expect(page1Response.page).toBe(1);
    expect(page1Response.pageSize).toBe(2);

    // 2ページ目
    const page2Response = await client.listAllBankAccountRegistrations(
      {
        page: 2,
        pageSize: 2,
      },
      { headers }
    );

    expect(page2Response.items).toHaveLength(2);
    expect(page2Response.totalCount).toBe(5);
    expect(page2Response.page).toBe(2);
    expect(page2Response.pageSize).toBe(2);

    // 3ページ目（残り1件）
    const page3Response = await client.listAllBankAccountRegistrations(
      {
        page: 3,
        pageSize: 2,
      },
      { headers }
    );

    expect(page3Response.items).toHaveLength(1);
    expect(page3Response.totalCount).toBe(5);
    expect(page3Response.page).toBe(3);
    expect(page3Response.pageSize).toBe(2);
  });
});
