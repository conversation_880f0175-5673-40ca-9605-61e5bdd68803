import {
  AdminUserFactory,
  AdminUserSessionFactory,
  MemberFactory,
  UserFactory,
  MailVerificationFactory,
  MembershipApplicationFactory,
} from '@core-test/index';
import { getClient } from '@core-test/index';
import { BankAccountApprovalService } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { BankAccountRegistrationStatus, ApprovalType } from '@hami/prisma';

// vPrisma.clientを使用（setup.tsでモックされているため）
const prisma = vPrisma.client;

// テストデータ作成ヘルパー
const createTestMember = async () => {
  const mailVerification = await MailVerificationFactory.create();
  const application = await MembershipApplicationFactory.create({
    mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
  });
  const user = await UserFactory.create();
  const member = await MemberFactory.create({
    user: { connect: { userId: user.userId } },
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
  });

  // userを再取得してリレーションを含める
  const memberWithUser = await prisma.member.findUnique({
    where: { memberId: member.memberId },
    include: { user: true },
  });

  return { user, member: memberWithUser!, application, mailVerification };
};

describe('rejectBankAccountRegistration', () => {
  it('口座登録を却下できること', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストユーザー',
      },
    });

    // API呼び出し
    const response = await client.rejectBankAccountRegistration(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        rejectionReason: '口座名義が一致しません',
      },
      { headers }
    );

    // レスポンス検証
    expect(response).toBeDefined();

    // データベースに却下レコードが作成されていることを確認
    const approval = await prisma.bankAccountRegistrationApproval.findFirst({
      where: { bankAccountRegistrationId: registration.bankAccountRegistrationId },
    });
    expect(approval).toBeDefined();
    expect(approval?.approvalType).toBe(ApprovalType.REJECT);
    expect(approval?.adminUserId).toBe(adminUser.adminUserId);
    expect(approval?.rejectionReason).toBe('口座名義が一致しません');
    expect(approval?.comment).toBeNull(); // 却下の場合はcommentはnull
    expect(approval?.createdAt).toBeDefined();
  });

  it('存在しない口座登録IDの場合エラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.rejectBankAccountRegistration(
        {
          bankAccountRegistrationId: 99999,
          rejectionReason: 'テスト却下',
        },
        { headers }
      )
    ).rejects.toThrow('Bank account registration not found');
  });

  it('既に処理済みの場合エラーを返すこと', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
      },
    });

    // 先に却下処理
    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        approvalType: ApprovalType.REJECT,
        adminUserId: adminUser.adminUserId,
        rejectionReason: '既に却下済み',
      },
    });

    // 再度却下を試行
    await expect(
      client.rejectBankAccountRegistration(
        {
          bankAccountRegistrationId: registration.bankAccountRegistrationId,
          rejectionReason: '重複却下',
        },
        { headers }
      )
    ).rejects.toThrow('Bank account registration already processed');
  });

  it('認証されていない場合はUnauthenticatedエラーを返すこと', async ({ getClient }) => {
    const client = getClient(BankAccountApprovalService);

    await expect(
      client.rejectBankAccountRegistration({
        bankAccountRegistrationId: 1,
        rejectionReason: 'テスト却下',
      })
    ).rejects.toThrow('Unauthorized');
  });

  it('却下理由が空の場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.rejectBankAccountRegistration(
        {
          bankAccountRegistrationId: 1,
          rejectionReason: '', // 空の却下理由
        },
        { headers }
      )
    ).rejects.toThrow();
  });

  it('無効なIDの場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.rejectBankAccountRegistration(
        {
          bankAccountRegistrationId: 0, // 無効なID
          rejectionReason: 'テスト却下',
        },
        { headers }
      )
    ).rejects.toThrow();
  });

  it('負のIDの場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.rejectBankAccountRegistration(
        {
          bankAccountRegistrationId: -1, // 負のID
          rejectionReason: 'テスト却下',
        },
        { headers }
      )
    ).rejects.toThrow();
  });

  it('複数回の処理履歴がある場合でもエラーを返すこと', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser1 = await AdminUserFactory.create();
    const adminUser2 = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser1.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
      },
    });

    // 複数の処理履歴を作成
    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        approvalType: ApprovalType.APPROVE,
        adminUserId: adminUser1.adminUserId,
        comment: '最初の承認',
      },
    });

    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        approvalType: ApprovalType.REJECT,
        adminUserId: adminUser2.adminUserId,
        rejectionReason: '再審査で却下',
      },
    });

    // 3回目の却下を試行
    await expect(
      client.rejectBankAccountRegistration(
        {
          bankAccountRegistrationId: registration.bankAccountRegistrationId,
          rejectionReason: '3回目の却下',
        },
        { headers }
      )
    ).rejects.toThrow('Bank account registration already processed');
  });

  it('長い却下理由でも正しく処理されること', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストユーザー',
      },
    });

    const longRejectionReason = '口座名義が一致しないため却下します。'.repeat(50); // 約1500文字

    // API呼び出し
    const response = await client.rejectBankAccountRegistration(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        rejectionReason: longRejectionReason,
      },
      { headers }
    );

    // レスポンス検証
    expect(response).toBeDefined();

    // データベースに却下レコードが作成されていることを確認
    const approval = await prisma.bankAccountRegistrationApproval.findFirst({
      where: { bankAccountRegistrationId: registration.bankAccountRegistrationId },
    });
    expect(approval).toBeDefined();
    expect(approval?.rejectionReason).toBe(longRejectionReason);
  });
});
