import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { RejectBankAccountRegistrationResponseSchema } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { DatabaseError } from '@core-api/repositories';
import {
  rejectBankAccountRegistration,
  BankAccountRegistrationNotFoundError,
  BankAccountRegistrationAlreadyProcessedError,
  AdminUserNotFoundError,
} from '@core-api/repositories/bank_account_approval_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';
import { checkAdminUserExists, UnauthorizedError } from '@core-api/middlewares/interceptors';

export const rejectBankAccountRegistrationHandler = createHandler({
  schema: z.object({
    bankAccountRegistrationId: z.number().int().positive(),
    rejectionReason: z.string().min(1, '却下理由は必須です'),
  }),
  business: ({ bankAccountRegistrationId, rejectionReason }, ctx) => {
    return checkAdminUserExists(ctx).asyncAndThen((adminUser) => {
      return rejectBankAccountRegistration({
        bankAccountRegistrationId,
        adminUserId: adminUser.adminUserId,
        rejectionReason,
      });
    });
  },
  toResponse: () => {
    return create(RejectBankAccountRegistrationResponseSchema, {});
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), () => new ConnectError('Unauthorized', Code.Unauthenticated))
      .with(
        P.instanceOf(BankAccountRegistrationNotFoundError),
        () => new ConnectError('Bank account registration not found', Code.NotFound)
      )
      .with(
        P.instanceOf(BankAccountRegistrationAlreadyProcessedError),
        () => new ConnectError('Bank account registration already processed', Code.FailedPrecondition)
      )
      .with(P.instanceOf(AdminUserNotFoundError), () => new ConnectError('Admin user not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
