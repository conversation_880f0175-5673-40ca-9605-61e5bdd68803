import {
  AdminUserFactory,
  AdminUserSessionFactory,
  MemberFactory,
  UserFactory,
  MailVerificationFactory,
  MembershipApplicationFactory,
} from '@core-test/index';
import { getClient } from '@core-test/index';
import { BankAccountApprovalService } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { BankAccountRegistrationStatus, ApprovalType } from '@hami/prisma';

// vPrisma.clientを使用（setup.tsでモックされているため）
const prisma = vPrisma.client;

// テストデータ作成ヘルパー
const createTestMember = async () => {
  const mailVerification = await MailVerificationFactory.create();
  const application = await MembershipApplicationFactory.create({
    mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
  });
  const user = await UserFactory.create();
  const member = await MemberFactory.create({
    user: { connect: { userId: user.userId } },
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
  });

  // userを再取得してリレーションを含める
  const memberWithUser = await prisma.member.findUnique({
    where: { memberId: member.memberId },
    include: { user: true },
  });

  return { user, member: memberWithUser!, application, mailVerification };
};

describe('getBankAccountRegistrationDetail', () => {
  it('口座登録詳細を取得できること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストユーザー',
      },
    });

    // API呼び出し
    const response = await client.getBankAccountRegistrationDetail(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
      },
      { headers }
    );

    // 検証
    expect(response.detail).toBeDefined();
    expect(Number(response.detail?.bankAccountRegistrationId)).toBe(registration.bankAccountRegistrationId);
    expect(Number(response.detail?.memberId)).toBe(member.memberId);
    expect(response.detail?.memberName).toBe(`${member.lastName} ${member.firstName}`);
    expect(response.detail?.memberNameKana).toBe(`${member.lastNameKana} ${member.firstNameKana}`);
    expect(response.detail?.memberEmail).toBe(member.user.email);
    expect(response.detail?.resultBankCode).toBe('0001');
    expect(response.detail?.resultBranchCode).toBe('001');
    expect(response.detail?.resultAccountNumber).toBe('1234567');
    expect(response.detail?.resultAccountName).toBe('テストユーザー');
    expect(response.detail?.completedAt).toBeDefined();
    // protobuf Timestampをミリ秒に変換して検証
    const completedAtMs =
      Number(response.detail?.completedAt?.seconds) * 1000 + Math.floor((response.detail?.completedAt?.nanos || 0) / 1000000);
    expect(completedAtMs).toBeGreaterThan(0);
    expect(response.detail?.approvalHistories).toHaveLength(0); // 承認履歴なし
  });

  it('承認履歴がある口座登録詳細を取得できること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストユーザー',
      },
    });

    // 承認履歴を作成（複数）
    const adminUser1 = await AdminUserFactory.create();
    const adminUser2 = await AdminUserFactory.create();

    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        approvalType: ApprovalType.REJECT,
        adminUserId: adminUser1.adminUserId,
        rejectionReason: '口座名義が一致しません',
        comment: '口座名義が一致しません', // 却下理由をcommentに設定
      },
    });

    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        approvalType: ApprovalType.APPROVE,
        adminUserId: adminUser2.adminUserId,
        comment: '再審査で承認',
      },
    });

    // API呼び出し
    const response = await client.getBankAccountRegistrationDetail(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
      },
      { headers }
    );

    // 検証
    expect(response.detail).toBeDefined();
    expect(response.detail?.approvalHistories).toHaveLength(2);

    // 承認履歴は作成日時の降順（新しい順）で返される
    const histories = response.detail?.approvalHistories || [];
    expect(histories[0].approvalStatus).toBe(2); // APPROVED
    expect(histories[0].adminUserName).toBe(adminUser2.name);
    expect(histories[0].comment).toBe('再審査で承認');

    expect(histories[1].approvalStatus).toBe(3); // REJECTED
    expect(histories[1].adminUserName).toBe(adminUser1.name);
    expect(histories[1].comment).toBe('口座名義が一致しません'); // 却下理由はcommentに含まれる
  });

  it('存在しない口座登録IDの場合エラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.getBankAccountRegistrationDetail(
        {
          bankAccountRegistrationId: 99999,
        },
        { headers }
      )
    ).rejects.toThrow('Bank account registration not found');
  });

  it('非アクティブな口座登録の場合エラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成（非アクティブ）
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: false, // 非アクティブ
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストユーザー',
      },
    });

    // 非アクティブでも取得できることを確認（Repository関数では非アクティブをフィルタリングしていない）
    const response = await client.getBankAccountRegistrationDetail(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
      },
      { headers }
    );

    expect(response.detail).toBeDefined();
    expect(Number(response.detail?.bankAccountRegistrationId)).toBe(registration.bankAccountRegistrationId);
  });

  it('認証されていない場合はUnauthenticatedエラーを返すこと', async ({ getClient }) => {
    const client = getClient(BankAccountApprovalService);

    await expect(
      client.getBankAccountRegistrationDetail({
        bankAccountRegistrationId: 1,
      })
    ).rejects.toThrow('Unauthorized');
  });

  it('無効なIDの場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.getBankAccountRegistrationDetail(
        {
          bankAccountRegistrationId: 0, // 無効なID
        },
        { headers }
      )
    ).rejects.toThrow();
  });

  it('口座番号がマスクされていない場合も正しく表示されること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成（マスクされていない口座番号）
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567', // マスクなし（7桁以内）
        resultAccountName: 'テストユーザー',
      },
    });

    // API呼び出し
    const response = await client.getBankAccountRegistrationDetail(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
      },
      { headers }
    );

    // 検証（詳細画面では完全な口座番号を表示）
    expect(response.detail?.resultAccountNumber).toBe('1234567');
  });

  it('必須フィールドがnullの場合も適切に処理されること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成（一部フィールドがnull）
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: null, // null値
        resultBranchCode: null, // null値
        resultAccountNumber: null, // null値
        resultAccountName: null, // null値
      },
    });

    // API呼び出し
    const response = await client.getBankAccountRegistrationDetail(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
      },
      { headers }
    );

    // 検証（null値は空文字として返される）
    expect(response.detail?.resultBankCode).toBe('');
    expect(response.detail?.resultBranchCode).toBe('');
    expect(response.detail?.resultAccountNumber).toBe('');
    expect(response.detail?.resultAccountName).toBe('');
  });
});
