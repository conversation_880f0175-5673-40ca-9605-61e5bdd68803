import {
  AdminUserFactory,
  AdminUserSessionFactory,
  MemberFactory,
  UserFactory,
  MailVerificationFactory,
  MembershipApplicationFactory,
} from '@core-test/index';
import { BankAccountApprovalService } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { BankAccountRegistrationStatus, ApprovalType } from '@hami/prisma';

// vPrisma.clientを使用（setup.tsでモックされているため）
const prisma = vPrisma.client;

// テストデータ作成ヘルパー
const createTestMember = async () => {
  const mailVerification = await MailVerificationFactory.create();
  const application = await MembershipApplicationFactory.create({
    mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
  });
  const user = await UserFactory.create();
  const member = await MemberFactory.create({
    user: { connect: { userId: user.userId } },
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
  });

  // userを再取得してリレーションを含める
  const memberWithUser = await prisma.member.findUnique({
    where: { memberId: member.memberId },
    include: { user: true },
  });

  return { user, member: memberWithUser!, application, mailVerification };
};

describe('approveBankAccountRegistration', () => {
  it('口座登録を承認できること', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストユーザー',
      },
    });

    // API呼び出し
    const response = await client.approveBankAccountRegistration(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        comment: 'テスト承認コメント',
      },
      { headers }
    );

    // レスポンス検証
    expect(response).toBeDefined();

    // データベースに承認レコードが作成されていることを確認
    const approval = await prisma.bankAccountRegistrationApproval.findFirst({
      where: { bankAccountRegistrationId: registration.bankAccountRegistrationId },
    });
    expect(approval).toBeDefined();
    expect(approval?.approvalType).toBe(ApprovalType.APPROVE);
    expect(approval?.adminUserId).toBe(adminUser.adminUserId);
    expect(approval?.comment).toBe('テスト承認コメント');
    expect(approval?.rejectionReason).toBeNull();
    expect(approval?.createdAt).toBeDefined();
  });

  it('コメントなしで口座登録を承認できること', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストユーザー',
      },
    });

    // API呼び出し（コメントなし）
    const response = await client.approveBankAccountRegistration(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
      },
      { headers }
    );

    // レスポンス検証
    expect(response).toBeDefined();

    // データベースに承認レコードが作成されていることを確認
    const approval = await prisma.bankAccountRegistrationApproval.findFirst({
      where: { bankAccountRegistrationId: registration.bankAccountRegistrationId },
    });
    expect(approval).toBeDefined();
    expect(approval?.approvalType).toBe(ApprovalType.APPROVE);
    expect(approval?.adminUserId).toBe(adminUser.adminUserId);
    expect(approval?.comment).toBe(''); // undefinedは空文字として保存される
  });

  it('存在しない口座登録IDの場合エラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.approveBankAccountRegistration(
        {
          bankAccountRegistrationId: 99999,
          comment: 'テスト',
        },
        { headers }
      )
    ).rejects.toThrow('Bank account registration not found');
  });

  it('既に処理済みの場合エラーを返すこと', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
      },
    });

    // 先に承認処理
    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        approvalType: ApprovalType.APPROVE,
        adminUserId: adminUser.adminUserId,
        comment: '既に承認済み',
      },
    });

    // 再度承認を試行
    await expect(
      client.approveBankAccountRegistration(
        {
          bankAccountRegistrationId: registration.bankAccountRegistrationId,
          comment: '重複承認',
        },
        { headers }
      )
    ).rejects.toThrow('Bank account registration already processed');
  });

  it('認証されていない場合はUnauthenticatedエラーを返すこと', async ({ getClient }) => {
    const client = getClient(BankAccountApprovalService);

    await expect(
      client.approveBankAccountRegistration({
        bankAccountRegistrationId: 1,
        comment: 'テスト',
      })
    ).rejects.toThrow('Unauthorized');
  });

  it('無効なIDの場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.approveBankAccountRegistration(
        {
          bankAccountRegistrationId: 0, // 無効なID
          comment: 'テスト',
        },
        { headers }
      )
    ).rejects.toThrow();
  });

  it('負のIDの場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.approveBankAccountRegistration(
        {
          bankAccountRegistrationId: -1, // 負のID
          comment: 'テスト',
        },
        { headers }
      )
    ).rejects.toThrow();
  });

  it('複数回の承認処理履歴がある場合でもエラーを返すこと', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser1 = await AdminUserFactory.create();
    const adminUser2 = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser1.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
      },
    });

    // 複数の承認履歴を作成
    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        approvalType: ApprovalType.REJECT,
        adminUserId: adminUser1.adminUserId,
        rejectionReason: '最初の却下',
      },
    });

    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        approvalType: ApprovalType.APPROVE,
        adminUserId: adminUser2.adminUserId,
        comment: '再審査で承認',
      },
    });

    // 3回目の承認を試行
    await expect(
      client.approveBankAccountRegistration(
        {
          bankAccountRegistrationId: registration.bankAccountRegistrationId,
          comment: '3回目の承認',
        },
        { headers }
      )
    ).rejects.toThrow('Bank account registration already processed');
  });

  it('長いコメントでも正しく処理されること', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();
    const registration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '1234567',
        resultAccountName: 'テストユーザー',
      },
    });

    const longComment = 'これは非常に長いコメントです。'.repeat(50); // 約1500文字

    // API呼び出し
    const response = await client.approveBankAccountRegistration(
      {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        comment: longComment,
      },
      { headers }
    );

    // レスポンス検証
    expect(response).toBeDefined();

    // データベースに承認レコードが作成されていることを確認
    const approval = await prisma.bankAccountRegistrationApproval.findFirst({
      where: { bankAccountRegistrationId: registration.bankAccountRegistrationId },
    });
    expect(approval).toBeDefined();
    expect(approval?.comment).toBe(longComment);
  });
});
