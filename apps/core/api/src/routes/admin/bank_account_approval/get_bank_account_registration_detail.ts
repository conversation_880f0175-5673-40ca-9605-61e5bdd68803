import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  GetBankAccountRegistrationDetailResponseSchema,
  BankAccountApprovalDetailSchema,
  BankAccountApprovalStatus,
  BankAccountApprovalHistorySchema,
} from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { ApprovalType } from '@hami/prisma';
import { DatabaseError } from '@core-api/repositories';
import {
  getBankAccountRegistrationDetail,
  BankAccountRegistrationNotFoundError,
} from '@core-api/repositories/bank_account_approval_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { dateToTimestamp, dateToTimestampOptional } from '@core-api/utils/timestamp';
import { ValidationError } from '@core-api/utils/validate_request';

// ApprovalTypeをBankAccountApprovalStatusに変換
const convertApprovalTypeToStatus = (approvalType: ApprovalType): BankAccountApprovalStatus => {
  switch (approvalType) {
    case ApprovalType.APPROVE:
      return BankAccountApprovalStatus.APPROVED;
    case ApprovalType.REJECT:
      return BankAccountApprovalStatus.REJECTED;
    default:
      return BankAccountApprovalStatus.BANK_ACCOUNT_APPROVAL_STATUS_UNKNOWN;
  }
};

export const getBankAccountRegistrationDetailHandler = createHandler({
  schema: z.object({
    bankAccountRegistrationId: z.number().int().positive(),
  }),
  business: ({ bankAccountRegistrationId }) => {
    return getBankAccountRegistrationDetail({ bankAccountRegistrationId });
  },
  toResponse: (registration) => {
    // 承認履歴を変換
    const approvalHistories = registration.approvals.map((approval) =>
      create(BankAccountApprovalHistorySchema, {
        bankAccountApprovalId: approval.bankAccountRegistrationApprovalId,
        bankAccountRegistrationId: approval.bankAccountRegistrationId,
        approvalStatus: convertApprovalTypeToStatus(approval.approvalType),
        comment: approval.comment || approval.rejectionReason || '',
        adminUserName: approval.adminUser.name,
        createdAt: dateToTimestamp(approval.createdAt),
      })
    );

    // 現在の承認状態を判定
    const currentStatus =
      registration.approvals.length > 0
        ? convertApprovalTypeToStatus(registration.approvals[0].approvalType)
        : BankAccountApprovalStatus.PENDING;

    const detail = create(BankAccountApprovalDetailSchema, {
      bankAccountRegistrationId: registration.bankAccountRegistrationId,
      memberId: registration.memberId,
      memberName: `${registration.member.lastName} ${registration.member.firstName}`,
      memberNameKana: `${registration.member.lastNameKana} ${registration.member.firstNameKana}`,
      memberEmail: registration.member.user.email,
      memberNumber: registration.member.memberNumber,
      resultBankCode: registration.resultBankCode || '',
      resultBankName: '', // 銀行名はスキーマに存在しないため空文字
      resultBranchCode: registration.resultBranchCode || '',
      resultBranchName: '', // 支店名はスキーマに存在しないため空文字
      resultAccountNumber: registration.resultAccountNumber || '', // 詳細画面では完全な口座番号を表示
      resultAccountName: registration.resultAccountName || '',
      completedAt: dateToTimestampOptional(registration.completedAt),
      approvalStatus: currentStatus,
      approvalHistories,
    });

    return create(GetBankAccountRegistrationDetailResponseSchema, {
      detail,
    });
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(
        P.instanceOf(BankAccountRegistrationNotFoundError),
        () => new ConnectError('Bank account registration not found', Code.NotFound)
      )
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
