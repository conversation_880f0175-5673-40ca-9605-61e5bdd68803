import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  BankAccountRegistrationItemSchema,
  ListAllBankAccountRegistrationsResponseSchema,
  BankAccountApprovalStatus,
} from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { listAllBankAccountRegistrations } from '@core-api/repositories/bank_account_approval_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { dateToTimestampOptional } from '@core-api/utils/timestamp';
import { ValidationError } from '@core-api/utils/validate_request';
import { maskAccountNumber } from '@core-api/utils/bank_account_utils';

export const listAllBankAccountRegistrationsHandler = createHandler({
  schema: z.object({
    page: z.number().int().min(1).default(1),
    pageSize: z.number().int().min(1).max(100).default(20),
    statusFilter: z.nativeEnum(BankAccountApprovalStatus).optional(),
  }),
  business: ({ page, pageSize, statusFilter }) => {
    return listAllBankAccountRegistrations({ page, pageSize, statusFilter }).map((result) => ({
      ...result,
      page,
      pageSize,
    }));
  },
  toResponse: ({ registrations, totalCount, page, pageSize }) => {
    const items = registrations.map((registration) =>
      create(BankAccountRegistrationItemSchema, {
        bankAccountRegistrationId: registration.bankAccountRegistrationId,
        memberId: registration.memberId,
        memberName: `${registration.member.lastName} ${registration.member.firstName}`,
        memberNameKana: `${registration.member.lastNameKana} ${registration.member.firstNameKana}`,
        resultAccountName: registration.resultAccountName || '',
        resultBankCode: registration.resultBankCode || '',
        resultBranchCode: registration.resultBranchCode || '',
        resultAccountNumber: maskAccountNumber(registration.resultAccountNumber || ''),
        completedAt: dateToTimestampOptional(registration.completedAt),
        approvalStatus: registration.approvalStatus || BankAccountApprovalStatus.PENDING,
        approvedAt: dateToTimestampOptional(registration.approvedAt),
        adminUserName: registration.adminUserName || '',
      })
    );

    return create(ListAllBankAccountRegistrationsResponseSchema, {
      items,
      totalCount,
      page,
      pageSize,
    });
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
