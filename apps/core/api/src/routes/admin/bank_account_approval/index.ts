import { BankAccountApprovalService } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { approveBankAccountRegistrationHandler } from './approve_bank_account_registration';
import { getBankAccountRegistrationDetailHandler } from './get_bank_account_registration_detail';
import { listPendingBankAccountRegistrationsHandler } from './list_pending_bank_account_registrations';
import { listAllBankAccountRegistrationsHandler } from './list_all_bank_account_registrations';
import { rejectBankAccountRegistrationHandler } from './reject_bank_account_registration';
import type { ConnectRouter } from '@connectrpc/connect';

export const implBankAccountApprovalService = (router: ConnectRouter) =>
  router.service(
    BankAccountApprovalService,
    {
      listPendingBankAccountRegistrations: unwrapResult(listPendingBankAccountRegistrationsHandler),
      listAllBankAccountRegistrations: unwrapResult(listAllBankAccountRegistrationsHandler),
      getBankAccountRegistrationDetail: unwrapResult(getBankAccountRegistrationDetailHandler),
      approveBankAccountRegistration: unwrapResult(approveBankAccountRegistrationHandler),
      rejectBankAccountRegistration: unwrapResult(rejectBankAccountRegistrationHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  );
