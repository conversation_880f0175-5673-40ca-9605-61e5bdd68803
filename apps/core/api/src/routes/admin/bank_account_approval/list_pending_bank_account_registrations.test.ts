import {
  AdminUserFactory,
  AdminUserSessionFactory,
  MemberFactory,
  UserFactory,
  MailVerificationFactory,
  MembershipApplicationFactory,
} from '@core-test/index';
import { getClient } from '@core-test/index';
import { BankAccountApprovalService } from '@hami/core-admin-api-schema/bank_account_approval_service_pb';
import { BankAccountRegistrationStatus } from '@hami/prisma';

// vPrisma.clientを使用（setup.tsでモックされているため）
const prisma = vPrisma.client;

// テストデータ作成ヘルパー
const createTestMember = async () => {
  const mailVerification = await MailVerificationFactory.create();
  const application = await MembershipApplicationFactory.create({
    mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
  });
  const user = await UserFactory.create();
  const member = await MemberFactory.create({
    user: { connect: { userId: user.userId } },
    membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
  });
  return { user, member, application, mailVerification };
};

describe('listPendingBankAccountRegistrations', () => {
  it('承認待ち口座登録一覧を取得できること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();

    // 承認待ち（SUCCESS状態で承認レコードなし）
    const pendingRegistration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '****1234',
        resultAccountName: 'テストユーザー',
      },
    });

    // API呼び出し
    const response = await client.listPendingBankAccountRegistrations(
      {
        page: 1,
        pageSize: 10,
      },
      { headers }
    );

    // 検証
    expect(response.totalCount).toBeGreaterThanOrEqual(1);
    expect(response.items.length).toBeGreaterThanOrEqual(1);
    expect(response.page).toBe(1);
    expect(response.pageSize).toBe(10);

    const item = response.items.find(
      (item: any) => Number(item.bankAccountRegistrationId) === pendingRegistration.bankAccountRegistrationId
    );
    expect(item).toBeDefined();
    expect(item?.memberName).toBe(`${member.lastName} ${member.firstName}`);
    expect(item?.memberNameKana).toBe(`${member.lastNameKana} ${member.firstNameKana}`);
    expect(item?.resultAccountNumber).toBe('****1234');
    expect(item?.resultBankCode).toBe('0001');
    expect(item?.resultBranchCode).toBe('001');
    expect(item?.resultAccountName).toBe('テストユーザー');
  });

  it('ページネーションが正しく動作すること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成（3件の承認待ち）
    const { member } = await createTestMember();

    for (let i = 0; i < 3; i++) {
      await prisma.bankAccountRegistration.create({
        data: {
          memberId: member.memberId,
          bankCode: `000${i + 1}`,
          registrationStatus: BankAccountRegistrationStatus.SUCCESS,
          isActive: true,
          completedAt: new Date(),
          resultBankCode: `000${i + 1}`,
          resultBranchCode: '001',
          resultAccountNumber: `****123${i}`,
          resultAccountName: 'テストユーザー',
        },
      });
    }

    // 1ページ目（2件取得）
    const page1Response = await client.listPendingBankAccountRegistrations(
      {
        page: 1,
        pageSize: 2,
      },
      { headers }
    );
    expect(page1Response.totalCount).toBeGreaterThanOrEqual(3);
    expect(page1Response.items.length).toBeLessThanOrEqual(2);
    expect(page1Response.page).toBe(1);
    expect(page1Response.pageSize).toBe(2);

    // 2ページ目
    const page2Response = await client.listPendingBankAccountRegistrations(
      {
        page: 2,
        pageSize: 2,
      },
      { headers }
    );
    expect(page2Response.totalCount).toBeGreaterThanOrEqual(3);
    expect(page2Response.page).toBe(2);
    expect(page2Response.pageSize).toBe(2);
  });

  it('承認済みの口座登録は一覧に含まれないこと', async ({ getClient }) => {
    // 管理者とセッション作成
    const adminUser = await AdminUserFactory.create();
    const adminUserSession = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();

    // 承認済み（SUCCESS状態で承認レコードあり）
    const approvedRegistration = await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '****1234',
        resultAccountName: 'テストユーザー',
      },
    });

    // 承認レコードを作成
    await prisma.bankAccountRegistrationApproval.create({
      data: {
        bankAccountRegistrationId: approvedRegistration.bankAccountRegistrationId,
        approvalType: 'APPROVE',
        adminUserId: adminUser.adminUserId,
        comment: '承認済み',
      },
    });

    // API呼び出し
    const response = await client.listPendingBankAccountRegistrations(
      {
        page: 1,
        pageSize: 10,
      },
      { headers }
    );

    // 承認済みの登録は含まれないことを確認
    const approvedItem = response.items.find(
      (item: any) => Number(item.bankAccountRegistrationId) === approvedRegistration.bankAccountRegistrationId
    );
    expect(approvedItem).toBeUndefined();
  });

  it('認証されていない場合はUnauthenticatedエラーを返すこと', async ({ getClient }) => {
    const client = getClient(BankAccountApprovalService);

    await expect(
      client.listPendingBankAccountRegistrations({
        page: 1,
        pageSize: 10,
      })
    ).rejects.toThrow('Unauthorized');
  });

  it('無効なページ番号の場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.listPendingBankAccountRegistrations(
        {
          page: 0, // 無効なページ番号
          pageSize: 10,
        },
        { headers }
      )
    ).rejects.toThrow();
  });

  it('無効なページサイズの場合はバリデーションエラーを返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    await expect(
      client.listPendingBankAccountRegistrations(
        {
          page: 1,
          pageSize: 101, // 最大値を超えるページサイズ
        },
        { headers }
      )
    ).rejects.toThrow();
  });

  it('データが存在しない場合は空の配列を返すこと', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // API呼び出し（データが存在しない状態）
    const response = await client.listPendingBankAccountRegistrations(
      {
        page: 1,
        pageSize: 10,
      },
      { headers }
    );

    // 検証
    expect(response.totalCount).toBe(0);
    expect(response.items).toHaveLength(0);
    expect(response.page).toBe(1);
    expect(response.pageSize).toBe(10);
  });

  it('口座番号のマスキングが正しく適用されること', async ({ getClient }) => {
    // 管理者セッション作成
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(BankAccountApprovalService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    // テストデータ作成
    const { member } = await createTestMember();

    // 実際の口座番号を使用した承認待ちデータ
    await prisma.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        bankCode: '0001',
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        isActive: true,
        completedAt: new Date(),
        resultBankCode: '0001',
        resultBranchCode: '001',
        resultAccountNumber: '********', // 8桁の実際の口座番号
        resultAccountName: 'テストユーザー',
      },
    });

    // API呼び出し
    const response = await client.listPendingBankAccountRegistrations(
      {
        page: 1,
        pageSize: 10,
      },
      { headers }
    );

    // 検証
    expect(response.items).toHaveLength(1);
    const item = response.items[0];
    expect(item.resultAccountNumber).toBe('****5678'); // ******** -> ****5678
  });
});
