import { create } from '@bufbuild/protobuf';
import { HorseFactory, MemberFactory, InvestmentContractFactory, AdminUserSessionFactory, getClient } from '@core-test/index';
import { GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema, InvestmentAndReturnService } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { InvestmentContractStatus, Horse, Member, AdminUserSession, InvestmentContract } from '@hami/prisma';

describe('getInvestmentAndReturnListByHorseIdAndCreatedDateHandler', () => {
  const apiClient = getClient(InvestmentAndReturnService);
  let horse: Horse;
  let member1: Member;
  let member2: Member;
  let adminSession: AdminUserSession;
  let investmentContract1: InvestmentContract;
  let investmentContract2: InvestmentContract;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    
    horse = await HorseFactory.create({
      recruitmentYear: 2015,
      recruitmentNo: 1,
      horseName: 'テスト馬',
      recruitmentName: 'テスト募集馬',
      sharesTotal: 400,
      amountTotal: 36000000,
      birthYear: 2014,
      birthMonth: 5,
      birthDay: 3,
      fundStartYear: 2016,
      fundStartMonth: 4,
      fundStartDay: 1,
    });

    member1 = await MemberFactory.create({
      firstName: '太郎',
      lastName: '田中',
    });
    member2 = await MemberFactory.create({
      firstName: '花子',
      lastName: '佐藤',
    });

    // 2つの出資契約を作成（同じ馬、異なる会員）
    investmentContract1 = await InvestmentContractFactory.create({
      member: { connect: { memberId: member1.memberId } },
      horse: { connect: { horseId: horse.horseId } },
      sharesNumber: 2,
      investmentAmount: 100000,
      discount: 0,
      taxRate: 8,
      investmentAmountBeforeTax: 92593,
      transactionAmount: 92593,
      monthlyDepreciation: 1000,
      contractedAt: new Date(),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    investmentContract2 = await InvestmentContractFactory.create({
      member: { connect: { memberId: member2.memberId } },
      horse: { connect: { horseId: horse.horseId } },
      sharesNumber: 3,
      investmentAmount: 150000,
      discount: 0,
      taxRate: 8,
      investmentAmountBeforeTax: 138889,
      transactionAmount: 138889,
      monthlyDepreciation: 1500,
      contractedAt: new Date(),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });
  });

  describe('getInvestmentAndReturnListByHorseIdAndCreatedDate', () => {
    it('正常に実行できる', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const testDate = new Date('2024-01-15');
      
      // InvestmentAndReturn1（member1）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      // InvestmentAndReturn2（member2）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member2.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 150000,
          insuranceInvestmentTotal: 75000,
          otherInvestmentTotal: 200000,
          investmentTotal: 425000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 225000,
          paymentAmount: 0,
        },
      });

      // ===== Act =====
      const request = create(GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
      });
      const response = await apiClient.getInvestmentAndReturnListByHorseIdAndCreatedDate(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(2);
      
      // member1のInvestmentAndReturn
      const member1Item = response.list.find(item => item.memberId === member1.memberId);
      expect(member1Item).toBeDefined();
      expect(member1Item!.investmentAndReturnId).toBeDefined();
      expect(member1Item!.createdDateYear).toBe(testDate.getFullYear());
      expect(member1Item!.createdDateMonth).toBe(testDate.getMonth() + 1);
      expect(member1Item!.createdDateDay).toBe(testDate.getDate());
      expect(member1Item!.progressedMonth).toBe(1);
      expect(member1Item!.yearlyReturnTargetFlag).toBe(false);
      expect(member1Item!.billingAmount).toBe(150000);
      expect(member1Item!.paymentAmount).toBe(0);
      expect(member1Item!.memberId).toBe(member1.memberId);
      expect(member1Item!.memberName).toBe('田中太郎');

      // member2のInvestmentAndReturn
      const member2Item = response.list.find(item => item.memberId === member2.memberId);
      expect(member2Item).toBeDefined();
      expect(member2Item!.investmentAndReturnId).toBeDefined();
      expect(member2Item!.createdDateYear).toBe(testDate.getFullYear());
      expect(member2Item!.createdDateMonth).toBe(testDate.getMonth() + 1);
      expect(member2Item!.createdDateDay).toBe(testDate.getDate());
      expect(member2Item!.progressedMonth).toBe(1);
      expect(member2Item!.yearlyReturnTargetFlag).toBe(false);
      expect(member2Item!.billingAmount).toBe(225000);
      expect(member2Item!.paymentAmount).toBe(0);
      expect(member2Item!.memberId).toBe(member2.memberId);
      expect(member2Item!.memberName).toBe('佐藤花子');

      // 全アイテムでmemberIdとmemberNameが正しく設定されていることを確認
      response.list.forEach(item => {
        expect(item.memberId).toBeGreaterThan(0);
        expect(typeof item.memberName).toBe('string');
      });
    });



    it('異なる日付のデータは取得されない', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const testDate = new Date('2024-01-15');
      const differentDate = new Date('2024-02-15');
      
      // 指定日付のInvestmentAndReturn
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      // 異なる日付のInvestmentAndReturn（これは取得されない）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member2.memberId,
          createdDate: differentDate,
          progressedMonth: 2,
          yearlyReturnTargetFlag: true,
          runningCostInvestmentTotal: 150000,
          insuranceInvestmentTotal: 75000,
          otherInvestmentTotal: 200000,
          investmentTotal: 425000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 225000,
          paymentAmount: 100000,
        },
      });

      // ===== Act =====
      const request = create(GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
      });
      const response = await apiClient.getInvestmentAndReturnListByHorseIdAndCreatedDate(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(1);
      expect(response.list[0].createdDateYear).toBe(testDate.getFullYear());
      expect(response.list[0].createdDateMonth).toBe(testDate.getMonth() + 1);
      expect(response.list[0].createdDateDay).toBe(testDate.getDate());
      expect(response.list[0].memberId).toBe(member1.memberId);
      expect(response.list[0].memberName).toBe('田中太郎');
    });

    it('異なる馬のデータは取得されない', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const otherHorse = await HorseFactory.create({
        recruitmentYear: 2016,
        recruitmentNo: 2,
        horseName: '別の馬',
        recruitmentName: '別の募集馬',
        sharesTotal: 300,
        amountTotal: 27000000,
        birthYear: 2015,
        birthMonth: 6,
        birthDay: 15,
        fundStartYear: 2017,
        fundStartMonth: 5,
        fundStartDay: 1,
      });

      const otherContract = await InvestmentContractFactory.create({
        member: { connect: { memberId: member1.memberId } },
        horse: { connect: { horseId: otherHorse.horseId } },
        sharesNumber: 1,
        investmentAmount: 50000,
        discount: 0,
        taxRate: 8,
        investmentAmountBeforeTax: 46296,
        transactionAmount: 46296,
        monthlyDepreciation: 500,
        contractedAt: new Date(),
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      const testDate = new Date('2024-01-15');
      
      // 指定馬のInvestmentAndReturn
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      // 別の馬のInvestmentAndReturn（これは取得されない）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: otherHorse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 50000,
          insuranceInvestmentTotal: 25000,
          otherInvestmentTotal: 200000,
          investmentTotal: 275000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 75000,
          paymentAmount: 0,
        },
      });

      // ===== Act =====
      const request = create(GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
      });
      const response = await apiClient.getInvestmentAndReturnListByHorseIdAndCreatedDate(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(1);
      expect(response.list[0].investmentAndReturnId).toBeDefined();
      expect(response.list[0].memberId).toBe(member1.memberId);
      expect(response.list[0].memberName).toBe('田中太郎');
    });

    it('データが存在しない場合、空のリストが返される', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const testDate = new Date('2024-01-15');

      // ===== Act =====
      const request = create(GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
      });
      const response = await apiClient.getInvestmentAndReturnListByHorseIdAndCreatedDate(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(0);
    });

    it('年次分配対象フラグがtrueのデータも取得される', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const testDate = new Date('2024-01-15');
      
      // 年次分配対象フラグがtrueのInvestmentAndReturn
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 12,
          yearlyReturnTargetFlag: true,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 50000,
        },
      });

      // ===== Act =====
      const request = create(GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
      });
      const response = await apiClient.getInvestmentAndReturnListByHorseIdAndCreatedDate(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(1);
      expect(response.list[0].yearlyReturnTargetFlag).toBe(true);
      expect(response.list[0].progressedMonth).toBe(12);
      expect(response.list[0].paymentAmount).toBe(50000);
      expect(response.list[0].memberId).toBe(member1.memberId);
      expect(response.list[0].memberName).toBe('田中太郎');
    });
  });
});
