import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { CreateInvestmentAndReturnResponseSchema } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { createInvestmentAndReturnUsecase } from '@core-api/usecases/investment_and_return_usecase';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const createInvestmentAndReturnHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
    targetYear: z.number().int().positive(),
    targetMonth: z.number().int().positive(),
    yearlyReturnTargetFlag: z.boolean(),
    retirementFlag: z.boolean(),
  }),
  business: (data) => ResultAsync.fromPromise(
    createInvestmentAndReturnUsecase(
      data.horseId,
      data.targetYear,
      data.targetMonth,
      data.yearlyReturnTargetFlag,
      data.retirementFlag
    ),
    (error) => error instanceof Error ? error : new Error('Unknown error occurred')
  ),
  toResponse: () => create(CreateInvestmentAndReturnResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
