import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { z } from 'zod';
import { GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponseSchema } from '@hami/core-admin-api-schema/investment_and_return_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId } from '@core-api/repositories/investment_and_return_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
    createdDateYear: z.number().int().min(1900).max(2100),
    createdDateMonth: z.number().int().min(1).max(12),
    createdDateDay: z.number().int().min(1).max(31),
    memberId: z.number().int().positive(),
  }),
  business: (data) => {
    return ResultAsync.fromPromise(
      (async () => {
        // 日付の妥当性チェック
        const createdDate = new Date(data.createdDateYear, data.createdDateMonth - 1, data.createdDateDay);
        const isValidDate = createdDate.getFullYear() === data.createdDateYear &&
                           createdDate.getMonth() === data.createdDateMonth - 1 &&
                           createdDate.getDate() === data.createdDateDay;
        
        if (!isValidDate) {
          throw new ValidationError('無効な日付です');
        }

        const result = await getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(
          data.horseId,
          createdDate,
          data.memberId
        );
        
        if (result.isErr()) {
          throw result.error;
        }

        if (!result.value) {
          throw new ConnectError('指定された条件の出資・分配金が見つかりません', Code.NotFound);
        }

        return result.value;
      })(),
      (error) => error instanceof Error ? error : new Error(String(error))
    );
  },
  toResponse: (investmentAndReturn) => {
    return create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponseSchema, {
      investmentAndReturn: {
        investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
        horseId: investmentAndReturn.horseId,
        memberId: investmentAndReturn.memberId,
        createdDateYear: investmentAndReturn.createdDate.getFullYear(),
        createdDateMonth: investmentAndReturn.createdDate.getMonth() + 1,
        createdDateDay: investmentAndReturn.createdDate.getDate(),
        progressedMonth: investmentAndReturn.progressedMonth,
        yearlyReturnTargetFlag: investmentAndReturn.yearlyReturnTargetFlag,
        sharesNumber: investmentAndReturn.sharesNumber,
        billingAmount: investmentAndReturn.billingAmount ?? undefined,
        paymentAmount: investmentAndReturn.paymentAmount ?? undefined,
        investment: investmentAndReturn.investment ? {
          investmentAndReturnInvestmentId: investmentAndReturn.investment.investmentAndReturnInvestmentId,
          investmentAndReturnId: investmentAndReturn.investment.investmentAndReturnId,
          racehorseInvestmentEquivalent: investmentAndReturn.investment.racehorseInvestmentEquivalent,
          discountAllocation: investmentAndReturn.investment.discountAllocation,
          racehorseInvestment: investmentAndReturn.investment.racehorseInvestment,
          runningCost: investmentAndReturn.investment.runningCost,
          subsidy: investmentAndReturn.investment.subsidy,
          retroactiveRunningCost: investmentAndReturn.investment.retroactiveRunningCost,
          runningCostInvestment: investmentAndReturn.investment.runningCostInvestment,
          insuranceInvestment: investmentAndReturn.investment.insuranceInvestment,
          otherInvestment: investmentAndReturn.investment.otherInvestment,
          currentMonthInvestmentTotal: investmentAndReturn.investment.currentMonthInvestmentTotal,
        } : undefined,
        returns: investmentAndReturn.returns.map(ret => ({
          investmentAndReturnReturnId: ret.investmentAndReturnReturnId,
          investmentAndReturnId: ret.investmentAndReturnId,
          returnCategory: ret.returnCategory,
          investmentRefundPaidUpToLastMonth: ret.investmentRefundPaidUpToLastMonth ?? undefined,
          refundableInvestmentAmount: ret.refundableInvestmentAmount,
          distributionTargetAmount: ret.distributionTargetAmount,
          distributionTargetAmountRefundable: ret.distributionTargetAmountRefundable,
          distributionTargetAmountProfit: ret.distributionTargetAmountProfit,
          distributionTargetAmountWithholdingTax: ret.distributionTargetAmountWithholdingTax,
          distributionAmount: ret.distributionAmount,
          refundableInvestmentAmountCarriedForward: ret.refundableInvestmentAmountCarriedForward,
        })),
      },
    });
  },
  toError: (error) => {
    if (error instanceof ValidationError) {
      return new ConnectError(error.message, Code.InvalidArgument);
    }
    if (error instanceof ConnectError) {
      return error;
    }
    if (error instanceof DatabaseError) {
      return new ConnectError('データベースエラーが発生しました', Code.Internal);
    }
    return new ConnectError('内部エラーが発生しました', Code.Internal);
  },
});
