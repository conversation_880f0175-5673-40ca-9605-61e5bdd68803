import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { CreateMemberClaimAndPayResponseSchema } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { processMemberClaimAndPayByCreatedDate } from '@core-api/usecases/member_claim_and_pay_usecase';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const createMemberClaimAndPayHandler = createHandler({
  schema: z.object({
    createdDate: z.string().datetime(),
    membershipDues: z.number().int().positive(),
    taxRate: z.number().int().min(0).max(100),
  }),
  business: (data) =>
    ResultAsync.fromPromise(
      processMemberClaimAndPayByCreatedDate(new Date(data.createdDate), data.membershipDues, data.taxRate),
      (error) => (error instanceof Error ? error : new Error('Unknown error occurred'))
    ),
  toResponse: () => create(CreateMemberClaimAndPayResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
