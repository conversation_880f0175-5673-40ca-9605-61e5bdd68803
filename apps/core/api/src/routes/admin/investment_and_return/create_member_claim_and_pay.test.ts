import { create } from '@bufbuild/protobuf';
import { HorseFactory, MemberFactory, InvestmentContractFactory, AdminUserSessionFactory } from '@core-test/index';
import { getClient } from '@core-test/index';
import { CreateMemberClaimAndPayRequestSchema, InvestmentAndReturnService } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { InvestmentContractStatus, Horse, Member, AdminUserSession, InvestmentContract } from '@hami/prisma';
import { createInvestmentAndReturn } from '@core-api/repositories/investment_and_return_repository';
import { listMemberClaimByOccurredDate } from '@core-api/repositories/member_claim_and_pay_repository';

describe('createMemberClaimAndPay', () => {
  const apiClient = getClient(InvestmentAndReturnService);
  let horse: Horse;
  let member: Member;
  let adminSession: AdminUserSession;
  let investmentContract: InvestmentContract;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    horse = await HorseFactory.create({
      recruitmentYear: 2024,
      recruitmentNo: 1,
      horseName: 'テスト馬',
      recruitmentName: 'テスト募集馬',
      sharesTotal: 400,
      amountTotal: 36000000,
      birthYear: 2023,
      birthMonth: 5,
      birthDay: 3,
      fundStartYear: 2024,
      fundStartMonth: 4,
      fundStartDay: 1,
    });

    member = await MemberFactory.create();

    // 出資契約を作成
    investmentContract = await InvestmentContractFactory.create({
      member: {
        connect: { memberId: member.memberId },
      },
      horse: { connect: { horseId: horse.horseId } },
      sharesNumber: 1,
      investmentAmount: 100000,
      discount: 0,
      taxRate: 8,
      investmentAmountBeforeTax: 92593,
      transactionAmount: 92593,
      monthlyDepreciation: 520,
      contractedAt: new Date(),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });
  });

  describe('createMemberClaimAndPay', () => {
    it('正常に実行できる', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const createdDate = new Date('2024-01-01');
      const membershipDues = 3000;
      const taxRate = 10;

      // InvestmentAndReturnデータを作成
      await createInvestmentAndReturn(
        horse.horseId,
        member.memberId,
        createdDate,
        1, // progressedMonth
        false, // yearlyReturnTargetFlag
        1, // sharesNumber
        1000, // runningCostInvestmentTotal
        500, // insuranceInvestmentTotal
        3000, // otherInvestmentTotal
        4500, // investmentTotal
        2000, // racehorseBookValueEndOfLastMonth
        0, // investmentRefundPaidUpToLastMonth
        100, // organizerWithholdingTaxTotal
        10, // organizerWithholdingTaxCurrentMonthAddition
        50, // clubWithholdingTaxTotal
        5, // clubWithholdingTaxCurrentMonthAddition
        100, // billingAmount
        50 // paymentAmount
      );

      // ===== Act =====
      const request = create(CreateMemberClaimAndPayRequestSchema, {
        createdDate: createdDate.toISOString(),
        membershipDues: membershipDues,
        taxRate: taxRate,
      });
      const response = await apiClient.createMemberClaimAndPay(request, { headers });

      // ===== Assert =====
      expect(response).toBeDefined();

      // 作成されたMemberClaimデータを検証
      const memberClaimsResult = await listMemberClaimByOccurredDate(createdDate);
      expect(memberClaimsResult.isOk()).toBe(true);
      if (memberClaimsResult.isOk()) {
        const memberClaims = memberClaimsResult.value;
        expect(memberClaims.length).toBeGreaterThan(0);
        
        // 指定した会員のMemberClaimが存在することを確認
        const memberClaim = memberClaims.find(claim => claim.memberId === member.memberId);
        expect(memberClaim).toBeDefined();
        if (memberClaim) {
          expect(memberClaim.title).toBe('月会費');
          expect(memberClaim.description).toBe(`月会費（${createdDate.getFullYear()}年${createdDate.getMonth() + 1}月）`);
          expect(Number(memberClaim.taxRate)).toBe(taxRate);
          expect(memberClaim.taxAmount).toBe(membershipDues * taxRate / 100);
          expect(memberClaim.amount).toBe(membershipDues + (membershipDues * taxRate / 100));
        }
      }
    });

    it('引退精算の馬（progressedMonth=999）のみの場合は会費請求しない', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const createdDate = new Date('2024-01-01');
      const membershipDues = 3000;
      const taxRate = 10;

      // 引退精算のInvestmentAndReturnデータを作成
      await createInvestmentAndReturn(
        horse.horseId,
        member.memberId,
        createdDate,
        999, // progressedMonth（引退精算）
        false, // yearlyReturnTargetFlag
        1, // sharesNumber
        1000, // runningCostInvestmentTotal
        500, // insuranceInvestmentTotal
        3000, // otherInvestmentTotal
        4500, // investmentTotal
        2000, // racehorseBookValueEndOfLastMonth
        0, // investmentRefundPaidUpToLastMonth
        100, // organizerWithholdingTaxTotal
        10, // organizerWithholdingTaxCurrentMonthAddition
        50, // clubWithholdingTaxTotal
        5, // clubWithholdingTaxCurrentMonthAddition
        100, // billingAmount
        50 // paymentAmount
      );

      // ===== Act =====
      const request = create(CreateMemberClaimAndPayRequestSchema, {
        createdDate: createdDate.toISOString(),
        membershipDues: membershipDues,
        taxRate: taxRate,
      });
      const response = await apiClient.createMemberClaimAndPay(request, { headers });

      // ===== Assert =====
      expect(response).toBeDefined();

      // 引退精算の馬のみなので会費請求されないことを確認
      const memberClaimsResult = await listMemberClaimByOccurredDate(createdDate);
      expect(memberClaimsResult.isOk()).toBe(true);
      if (memberClaimsResult.isOk()) {
        const memberClaims = memberClaimsResult.value;
        // 引退精算の馬のみなので会費請求されない
        expect(memberClaims.length).toBe(0);
      }
    });

    it('複数の会員と馬で正常に処理が完了する', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const createdDate = new Date('2024-01-01');
      const membershipDues = 3000;
      const taxRate = 10;

      // 複数の会員を作成
      const member2 = await MemberFactory.create();
      const member3 = await MemberFactory.create();

      // 複数の馬を作成
      const horse2 = await HorseFactory.create({
        recruitmentYear: 2024,
        recruitmentNo: 2,
        horseName: 'テスト馬2',
        recruitmentName: 'テスト募集馬2',
        sharesTotal: 400,
        amountTotal: 36000000,
        birthYear: 2023,
        birthMonth: 6,
        birthDay: 15,
        fundStartYear: 2024,
        fundStartMonth: 5,
        fundStartDay: 1,
      });

      const horse3 = await HorseFactory.create({
        recruitmentYear: 2024,
        recruitmentNo: 3,
        horseName: 'テスト馬3',
        recruitmentName: 'テスト募集馬3',
        sharesTotal: 400,
        amountTotal: 36000000,
        birthYear: 2023,
        birthMonth: 7,
        birthDay: 20,
        fundStartYear: 2024,
        fundStartMonth: 6,
        fundStartDay: 1,
      });

      // 複数の投資契約を作成
      const investmentContract2 = await InvestmentContractFactory.create({
        member: {
          connect: { memberId: member2.memberId },
        },
        horse: { connect: { horseId: horse2.horseId } },
        sharesNumber: 2,
        investmentAmount: 200000,
        discount: 0,
        taxRate: 8,
        investmentAmountBeforeTax: 185185,
        transactionAmount: 185185,
        monthlyDepreciation: 1040,
        contractedAt: new Date(),
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      const investmentContract3 = await InvestmentContractFactory.create({
        member: {
          connect: { memberId: member3.memberId },
        },
        horse: { connect: { horseId: horse3.horseId } },
        sharesNumber: 3,
        investmentAmount: 300000,
        discount: 0,
        taxRate: 8,
        investmentAmountBeforeTax: 277778,
        transactionAmount: 277778,
        monthlyDepreciation: 1560,
        contractedAt: new Date(),
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      // 複数のInvestmentAndReturnデータを作成
      const investmentAndReturns = [
        // 会員1の馬1（通常）
        {
          horseId: horse.horseId,
          memberId: member.memberId,
          progressedMonth: 1,
          billingAmount: 100,
          paymentAmount: 50,
        },
        // 会員2の馬2（通常）
        {
          horseId: horse2.horseId,
          memberId: member2.memberId,
          progressedMonth: 2,
          billingAmount: 200,
          paymentAmount: 100,
        },
        // 会員3の馬3（引退精算）
        {
          horseId: horse3.horseId,
          memberId: member3.memberId,
          progressedMonth: 999,
          billingAmount: 300,
          paymentAmount: 150,
        },
      ];

      for (const data of investmentAndReturns) {
        await createInvestmentAndReturn(
          data.horseId,
          data.memberId,
          createdDate,
          data.progressedMonth,
          false, // yearlyReturnTargetFlag
          1, // sharesNumber
          1000, // runningCostInvestmentTotal
          500, // insuranceInvestmentTotal
          3000, // otherInvestmentTotal
          4500, // investmentTotal
          2000, // racehorseBookValueEndOfLastMonth
          0, // investmentRefundPaidUpToLastMonth
          100, // organizerWithholdingTaxTotal
          10, // organizerWithholdingTaxCurrentMonthAddition
          50, // clubWithholdingTaxTotal
          5, // clubWithholdingTaxCurrentMonthAddition
          data.billingAmount,
          data.paymentAmount
        );
      }

      // ===== Act =====
      const request = create(CreateMemberClaimAndPayRequestSchema, {
        createdDate: createdDate.toISOString(),
        membershipDues: membershipDues,
        taxRate: taxRate,
      });
      const response = await apiClient.createMemberClaimAndPay(request, { headers });

      // ===== Assert =====
      expect(response).toBeDefined();

      // 作成されたMemberClaimデータを検証
      const memberClaimsResult = await listMemberClaimByOccurredDate(createdDate);
      expect(memberClaimsResult.isOk()).toBe(true);
      if (memberClaimsResult.isOk()) {
        const memberClaims = memberClaimsResult.value;
        
        // 会員1と会員2は会費請求される（通常の馬がいる）
        const member1Claim = memberClaims.find(claim => claim.memberId === member.memberId);
        expect(member1Claim).toBeDefined();
        
        const member2Claim = memberClaims.find(claim => claim.memberId === member2.memberId);
        expect(member2Claim).toBeDefined();
        
        // 会員3は会費請求されない（引退精算の馬のみ）
        const member3Claim = memberClaims.find(claim => claim.memberId === member3.memberId);
        expect(member3Claim).toBeUndefined();
      }
    });

    it('無効なパラメータでエラーになる', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // ===== Act & Assert =====
      // 無効な日付形式
      const invalidDateRequest = create(CreateMemberClaimAndPayRequestSchema, {
        createdDate: 'invalid-date',
        membershipDues: 3000,
        taxRate: 10,
      });
      await expect(apiClient.createMemberClaimAndPay(invalidDateRequest, { headers })).rejects.toThrow();

      // 負の月会費
      const negativeDuesRequest = create(CreateMemberClaimAndPayRequestSchema, {
        createdDate: new Date('2024-01-01').toISOString(),
        membershipDues: -1000,
        taxRate: 10,
      });
      await expect(apiClient.createMemberClaimAndPay(negativeDuesRequest, { headers })).rejects.toThrow();

      // 無効な税率
      const invalidTaxRateRequest = create(CreateMemberClaimAndPayRequestSchema, {
        createdDate: new Date('2024-01-01').toISOString(),
        membershipDues: 3000,
        taxRate: 150, // 100%を超える
      });
      await expect(apiClient.createMemberClaimAndPay(invalidTaxRateRequest, { headers })).rejects.toThrow();
    });
  });
});
