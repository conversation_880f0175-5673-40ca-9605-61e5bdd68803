import fs from 'fs';
import path from 'path';
import { create } from '@bufbuild/protobuf';
import { createHorseBillingCarrotc201565 } from '@core-test/factories/billing/horse_billing_carrotc201565_factory';
import { HorseIncomeOtherFactory } from '@core-test/factories/horse_income_other_factory';
import { HorseIncomePrizeFactory } from '@core-test/factories/horse_income_prize_factory';
import { createHorseIncomeOtherCarrotc201565 } from '@core-test/factories/income/horse_income_carrotc201565_factory';
import { createHorseIncomePrizeCarrotc201565 } from '@core-test/factories/income/horse_income_carrotc201565_factory';
import { MemberRacehorseInvestmentFactory } from '@core-test/factories/member_racehorse_investment_factory';
import { HorseFactory, MemberFactory, InvestmentContractFactory, billerFactory, horseBillingFactory, AdminUserSessionFactory } from '@core-test/index';
import { getClient } from '@core-test/index';
import { CreateInvestmentAndReturnRequestSchema, InvestmentAndReturnService } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { InvestmentContractStatus, Horse, Member, Biller, ReturnCategory, AdminUserSession, InvestmentContract } from '@hami/prisma';
import { listInvestmentAndReturnByHorseIdAndMemberId, listInvestmentAndReturnInvestmentByHorseIdAndMemberId, listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory } from '@core-api/repositories/investment_and_return_repository';
import { InvestmentAndReturnInvestmentModel } from '../../../usecases/models/investment_and_return_investment_model';
import { InvestmentAndReturnModel } from '../../../usecases/models/investment_and_return_model';
import { InvestmentAndReturnReturnModel } from '../../../usecases/models/investment_and_return_return_model';

describe('InvestmentAndReturnUsecase', () => {
  const apiClient = getClient(InvestmentAndReturnService);
    let horse: Horse;
    let member: Member;
    let biller: Biller;
    let adminSession: AdminUserSession;
    let investmentContract: InvestmentContract;
    beforeEach(async () => {
        adminSession = await AdminUserSessionFactory.create();
        horse = await HorseFactory.create({
            recruitmentYear: 2015,
            recruitmentNo: 1,
            horseName: 'テスト馬',
            recruitmentName: 'テスト募集馬',
            sharesTotal: 400,
            amountTotal: 36000000,
            birthYear: 2014,
            birthMonth: 5,
            birthDay: 3,
            fundStartYear: 2016,
            fundStartMonth: 4,
            fundStartDay: 1,
        });

        member = await MemberFactory.create();

        // 出資契約を作成
        const investmentAmount2 = 86107;
        const investmentAmountBeforeTax2 = Math.ceil(investmentAmount2 / (1 + (8 / 100)));
        investmentContract = await InvestmentContractFactory.create({
            member: {
                connect: { memberId: member.memberId },
            },
            horse: { connect: { horseId: horse.horseId } },
            sharesNumber: 1,
            investmentAmount: investmentAmount2,
            discount: 2093,
            taxRate: 8,
            investmentAmountBeforeTax: investmentAmountBeforeTax2,
            transactionAmount: 81961,
            monthlyDepreciation: 81961 - Math.ceil(81961 * 47 / 48),
            contractedAt: new Date('2015-09-30'),
            contractStatus: InvestmentContractStatus.COMPLETED,
        });
        await MemberRacehorseInvestmentFactory.create({
            memberId: member.memberId,
            horseId: horse.horseId,
            investmentDate: new Date('2015-11-10'),
            racehorseInvestmentEquivalent: investmentAmount2,
            discountAllocation: 2093,
            racehorseInvestment: investmentAmount2,
        });

        biller = await billerFactory.create();

        const horseBillings = createHorseBillingCarrotc201565(horse.horseId, biller.id, biller.id, biller.id, biller.id);
        for (const horseBilling of horseBillings) {
            await horseBillingFactory.create(horseBilling);
        }

        // 特定の馬の収入データを作成
        const horseIncomeOther = createHorseIncomeOtherCarrotc201565(horse.horseId);
        for (const horseIncomeOtherItem of horseIncomeOther) {
            await HorseIncomeOtherFactory.create(horseIncomeOtherItem);
        }
        const horseIncomePrize = createHorseIncomePrizeCarrotc201565(horse.horseId);
        for (const horseIncomePrizeItem of horseIncomePrize) {
            const { allowances, ...itemWithoutAllowances } = horseIncomePrizeItem;
            await HorseIncomePrizeFactory.create(itemWithoutAllowances);
        }
    });

    describe('createInvestmentAndReturn', () => {
        it('正常に実行できる', async () => {
            // ===== Arrange =====
            const headers = new Headers({
              sessionToken: adminSession.sessionToken,
            });
        
            // ===== Act =====
            const request201511 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2015, targetMonth: 11, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201511 = await apiClient.createInvestmentAndReturn(request201511, { headers });

            const request201512 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2015, targetMonth: 12, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201512 = await apiClient.createInvestmentAndReturn(request201512, { headers });

            const request201601 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 1, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201601 = await apiClient.createInvestmentAndReturn(request201601, { headers });

            const request201602 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 2, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201602 = await apiClient.createInvestmentAndReturn(request201602, { headers });

            const request201603 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 3, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201603 = await apiClient.createInvestmentAndReturn(request201603, { headers });

            const request201604 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 4, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201604 = await apiClient.createInvestmentAndReturn(request201604, { headers });

            const request201605 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 5, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201605 = await apiClient.createInvestmentAndReturn(request201605, { headers });

            const request201606 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 6, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201606 = await apiClient.createInvestmentAndReturn(request201606, { headers });

            const request201607 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 7, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201607 = await apiClient.createInvestmentAndReturn(request201607, { headers });

            const request201608 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 8, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201608 = await apiClient.createInvestmentAndReturn(request201608, { headers });

            const request201609 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 9, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201609 = await apiClient.createInvestmentAndReturn(request201609, { headers });

            const request201610 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 10, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201610 = await apiClient.createInvestmentAndReturn(request201610, { headers });

            const request201611 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 11, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201611 = await apiClient.createInvestmentAndReturn(request201611, { headers });

            const request201612 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2016, targetMonth: 12, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201612 = await apiClient.createInvestmentAndReturn(request201612, { headers });

            const request201701 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 1, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201701 = await apiClient.createInvestmentAndReturn(request201701, { headers });

            const request201702 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 2, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201702 = await apiClient.createInvestmentAndReturn(request201702, { headers });

            const request201703 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 3, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201703 = await apiClient.createInvestmentAndReturn(request201703, { headers });

            const request201704 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 4, yearlyReturnTargetFlag: true, retirementFlag: false});
            const response201704 = await apiClient.createInvestmentAndReturn(request201704, { headers });

            const request201705 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 5, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201705 = await apiClient.createInvestmentAndReturn(request201705, { headers });

            const request201706 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 6, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201706 = await apiClient.createInvestmentAndReturn(request201706, { headers });

            const request201707 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 7, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201707 = await apiClient.createInvestmentAndReturn(request201707, { headers });

            const request201708 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 8, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201708 = await apiClient.createInvestmentAndReturn(request201708, { headers });

            const request201709 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 9, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201709 = await apiClient.createInvestmentAndReturn(request201709, { headers });

            const request201710 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 10, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201710 = await apiClient.createInvestmentAndReturn(request201710, { headers });

            const request201711 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 11, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201711 = await apiClient.createInvestmentAndReturn(request201711, { headers });

            const request201712 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2017, targetMonth: 12, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201712 = await apiClient.createInvestmentAndReturn(request201712, { headers });

            const request201801 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 1, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201801 = await apiClient.createInvestmentAndReturn(request201801, { headers });

            const request201802 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 2, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201802 = await apiClient.createInvestmentAndReturn(request201802, { headers });

            const request201803 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 3, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201803 = await apiClient.createInvestmentAndReturn(request201803, { headers });

            const request201804 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 4, yearlyReturnTargetFlag: true, retirementFlag: false});
            const response201804 = await apiClient.createInvestmentAndReturn(request201804, { headers });

            const request201805 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 5, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201805 = await apiClient.createInvestmentAndReturn(request201805, { headers });

            const request201806 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 6, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201806 = await apiClient.createInvestmentAndReturn(request201806, { headers });

            const request201807 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 7, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201807 = await apiClient.createInvestmentAndReturn(request201807, { headers });

            const request201808 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 8, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201808 = await apiClient.createInvestmentAndReturn(request201808, { headers });

            const request201809 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 9, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201809 = await apiClient.createInvestmentAndReturn(request201809, { headers });

            const request201810 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 10, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201810 = await apiClient.createInvestmentAndReturn(request201810, { headers });

            const request201811 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 11, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201811 = await apiClient.createInvestmentAndReturn(request201811, { headers });

            const request201812 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2018, targetMonth: 12, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201812 = await apiClient.createInvestmentAndReturn(request201812, { headers });

            const request201901 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 1, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201901 = await apiClient.createInvestmentAndReturn(request201901, { headers });

            const request201902 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 2, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201902 = await apiClient.createInvestmentAndReturn(request201902, { headers });

            const request201903 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 3, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201903 = await apiClient.createInvestmentAndReturn(request201903, { headers });

            const request201904 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 4, yearlyReturnTargetFlag: true, retirementFlag: false});
            const response201904 = await apiClient.createInvestmentAndReturn(request201904, { headers });

            const request201905 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 5, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201905 = await apiClient.createInvestmentAndReturn(request201905, { headers });

            const request201906 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 6, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201906 = await apiClient.createInvestmentAndReturn(request201906, { headers });

            const request201907 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 7, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201907 = await apiClient.createInvestmentAndReturn(request201907, { headers });

            const request201908 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 8, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201908 = await apiClient.createInvestmentAndReturn(request201908, { headers });

            const request201909 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 9, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201909 = await apiClient.createInvestmentAndReturn(request201909, { headers });

            const request201910 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 10, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201910 = await apiClient.createInvestmentAndReturn(request201910, { headers });

            const request201911 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 11, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201911 = await apiClient.createInvestmentAndReturn(request201911, { headers });

            const request201912 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2019, targetMonth: 12, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response201912 = await apiClient.createInvestmentAndReturn(request201912, { headers });

            const request202001 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 1, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202001 = await apiClient.createInvestmentAndReturn(request202001, { headers });

            const request202002 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 2, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202002 = await apiClient.createInvestmentAndReturn(request202002, { headers });

            const request202003 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 3, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202003 = await apiClient.createInvestmentAndReturn(request202003, { headers });

            const request202004 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 4, yearlyReturnTargetFlag: true, retirementFlag: false});
            const response202004 = await apiClient.createInvestmentAndReturn(request202004, { headers });

            const request202005 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 5, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202005 = await apiClient.createInvestmentAndReturn(request202005, { headers });

            const request202006 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 6, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202006 = await apiClient.createInvestmentAndReturn(request202006, { headers });

            const request202007 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 7, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202007 = await apiClient.createInvestmentAndReturn(request202007, { headers });

            const request202008 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 8, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202008 = await apiClient.createInvestmentAndReturn(request202008, { headers });

            const request202009 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 9, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202009 = await apiClient.createInvestmentAndReturn(request202009, { headers });

            const request202010 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 10, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202010 = await apiClient.createInvestmentAndReturn(request202010, { headers });

            const request202011 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 11, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202011 = await apiClient.createInvestmentAndReturn(request202011, { headers });

            const request202012 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2020, targetMonth: 12, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202012 = await apiClient.createInvestmentAndReturn(request202012, { headers });

            const request202101 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 1, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202101 = await apiClient.createInvestmentAndReturn(request202101, { headers });

            const request202102 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 2, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202102 = await apiClient.createInvestmentAndReturn(request202102, { headers });

            const request202103 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 3, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202103 = await apiClient.createInvestmentAndReturn(request202103, { headers });

            const request202104 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 4, yearlyReturnTargetFlag: true, retirementFlag: false});
            const response202104 = await apiClient.createInvestmentAndReturn(request202104, { headers });

            const request202105 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 5, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202105 = await apiClient.createInvestmentAndReturn(request202105, { headers });

            const request202106 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 6, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202106 = await apiClient.createInvestmentAndReturn(request202106, { headers });

            const request202107 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 7, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202107 = await apiClient.createInvestmentAndReturn(request202107, { headers });

            const request202108 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 8, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202108 = await apiClient.createInvestmentAndReturn(request202108, { headers });

            const request202109 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 9, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202109 = await apiClient.createInvestmentAndReturn(request202109, { headers });

            const request202110 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 10, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202110 = await apiClient.createInvestmentAndReturn(request202110, { headers });

            const request202111 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 11, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202111 = await apiClient.createInvestmentAndReturn(request202111, { headers });

            const request202112 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2021, targetMonth: 12, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202112 = await apiClient.createInvestmentAndReturn(request202112, { headers });

            const request202201 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 1, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202201 = await apiClient.createInvestmentAndReturn(request202201, { headers });

            const request202202 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 2, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202202 = await apiClient.createInvestmentAndReturn(request202202, { headers });

            const request202203 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 3, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202203 = await apiClient.createInvestmentAndReturn(request202203, { headers });

            const request202204 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 4, yearlyReturnTargetFlag: true, retirementFlag: false});
            const response202204 = await apiClient.createInvestmentAndReturn(request202204, { headers });

            const request202205 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 5, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202205 = await apiClient.createInvestmentAndReturn(request202205, { headers });

            const request202206 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 6, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202206 = await apiClient.createInvestmentAndReturn(request202206, { headers });

            const request202207 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 7, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202207 = await apiClient.createInvestmentAndReturn(request202207, { headers });

            const request202208 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 8, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202208 = await apiClient.createInvestmentAndReturn(request202208, { headers });

            const request202209 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 9, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202209 = await apiClient.createInvestmentAndReturn(request202209, { headers });

            const request202210 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 10, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202210 = await apiClient.createInvestmentAndReturn(request202210, { headers });

            const request202211 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 11, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202211 = await apiClient.createInvestmentAndReturn(request202211, { headers });

              const request202212 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2022, targetMonth: 12, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202212 = await apiClient.createInvestmentAndReturn(request202212, { headers });

            const request202301 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 1, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202301 = await apiClient.createInvestmentAndReturn(request202301, { headers });

            const request202302 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 2, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202302 = await apiClient.createInvestmentAndReturn(request202302, { headers });

            const request202303 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 3, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202303 = await apiClient.createInvestmentAndReturn(request202303, { headers });

            const request202304 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 4, yearlyReturnTargetFlag: true, retirementFlag: false});
            const response202304 = await apiClient.createInvestmentAndReturn(request202304, { headers });

            const request202305 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 5, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202305 = await apiClient.createInvestmentAndReturn(request202305, { headers });

            const request202306 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 6, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202306 = await apiClient.createInvestmentAndReturn(request202306, { headers });

            const request202307 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 7, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202307 = await apiClient.createInvestmentAndReturn(request202307, { headers });

            const request202308 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 8, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202308 = await apiClient.createInvestmentAndReturn(request202308, { headers });

            const request202309 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 9, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202309 = await apiClient.createInvestmentAndReturn(request202309, { headers });

            const request202310 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 10, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202310 = await apiClient.createInvestmentAndReturn(request202310, { headers });

            const request202311 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 11, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202311 = await apiClient.createInvestmentAndReturn(request202311, { headers });

            const request202312 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2023, targetMonth: 12, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202312 = await apiClient.createInvestmentAndReturn(request202312, { headers });

            const request202401 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2024, targetMonth: 1, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202401 = await apiClient.createInvestmentAndReturn(request202401, { headers });

            const request202402 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2024, targetMonth: 2, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202402 = await apiClient.createInvestmentAndReturn(request202402, { headers });

            const request202403 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2024, targetMonth: 3, yearlyReturnTargetFlag: false, retirementFlag: false});
            const response202403 = await apiClient.createInvestmentAndReturn(request202403, { headers });

            const request202404 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2024, targetMonth: 4, yearlyReturnTargetFlag: true, retirementFlag: false});
            const response202404 = await apiClient.createInvestmentAndReturn(request202404, { headers });

            const request202405 = create(CreateInvestmentAndReturnRequestSchema, {horseId: horse.horseId, targetYear: 2024, targetMonth: 5, yearlyReturnTargetFlag: true, retirementFlag: true});
            const response202405 = await apiClient.createInvestmentAndReturn(request202405, { headers });

            // ===== Assert =====
            // この会員・この馬の出資と分配データの構築確認
            const investmentAndReturns = await listInvestmentAndReturnByHorseIdAndMemberId(horse.horseId, member.memberId);
            if (investmentAndReturns.isErr()) {
                throw investmentAndReturns.error;
            }
            const investmentAndReturnInvestments = await listInvestmentAndReturnInvestmentByHorseIdAndMemberId(horse.horseId, member.memberId);
            if (investmentAndReturnInvestments.isErr()) {
                throw investmentAndReturnInvestments.error;
            }
            const investmentAndReturnReturnsClubToFundMonthly = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(horse.horseId, member.memberId, ReturnCategory.CLUB_TO_FUND_MONTHLY);
            if (investmentAndReturnReturnsClubToFundMonthly.isErr()) {
                throw investmentAndReturnReturnsClubToFundMonthly.error;
            }
            const investmentAndReturnReturnsFundToMemberMonthly = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(horse.horseId, member.memberId, ReturnCategory.FUND_TO_MEMBER_MONTHLY);
            if (investmentAndReturnReturnsFundToMemberMonthly.isErr()) {
                throw investmentAndReturnReturnsFundToMemberMonthly.error;
            }
            const investmentAndReturnReturnsClubToFundYearly = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(horse.horseId, member.memberId, ReturnCategory.CLUB_TO_FUND_YEARLY);
            if (investmentAndReturnReturnsClubToFundYearly.isErr()) {
                throw investmentAndReturnReturnsClubToFundYearly.error;
            }
            const investmentAndReturnReturnsFundToMemberYearlyOrganizer = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(horse.horseId, member.memberId, ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER);
            if (investmentAndReturnReturnsFundToMemberYearlyOrganizer.isErr()) {
                throw investmentAndReturnReturnsFundToMemberYearlyOrganizer.error;
            }
            const investmentAndReturnReturnsFundToMemberYearlyClub = await listInvestmentAndReturnReturnByHorseIdAndMemberIdAndReturnCategory(horse.horseId, member.memberId, ReturnCategory.FUND_TO_MEMBER_YEARLY_CLUB);
            if (investmentAndReturnReturnsFundToMemberYearlyClub.isErr()) {
                throw investmentAndReturnReturnsFundToMemberYearlyClub.error;
            }
            // テストデータの構築
            const {
              investmentAndReturnList,
              investmentAndReturnInvestmentList,
              investmentAndReturnReturnsClubToFundMonthlyList,
              investmentAndReturnReturnsClubToFundYearlyList,
              investmentAndReturnReturnsFundToMemberMonthlyList,
              investmentAndReturnReturnsFundToMemberYearlyOrganizerList,
              investmentAndReturnReturnsFundToMemberYearlyClubList
            } = createTestData('carrotc201565');
            expect(investmentAndReturns.value.length).toBe(investmentAndReturnList.length);
            for (let i = 0; i < investmentAndReturnList.length; i++) {
              const errorIndex = `index ${investmentAndReturnList[i].createdDate.getFullYear()}/${String(investmentAndReturnList[i].createdDate.getMonth() + 1).padStart(2, '0')}/${String(investmentAndReturnList[i].createdDate.getDate()).padStart(2, '0')}`;
              expect(investmentAndReturns.value[i].createdDate, `${errorIndex}: createdDate`).toStrictEqual(investmentAndReturnList[i].createdDate);
              expect(investmentAndReturns.value[i].progressedMonth, `${errorIndex}: progressedMonth`).toBe(investmentAndReturnList[i].progressedMonth);
              expect(investmentAndReturns.value[i].yearlyReturnTargetFlag, `${errorIndex}: yearlyReturnTargetFlag`).toBe(investmentAndReturnList[i].yearlyReturnTargetFlag);
              expect(investmentAndReturns.value[i].runningCostInvestmentTotal, `${errorIndex}: runningCostInvestmentTotal`).toBe(investmentAndReturnList[i].runningCostInvestmentTotal);
              expect(investmentAndReturns.value[i].insuranceInvestmentTotal, `${errorIndex}: insuranceInvestmentTotal`).toBe(investmentAndReturnList[i].insuranceInvestmentTotal);
              expect(investmentAndReturns.value[i].investmentTotal, `${errorIndex}: investmentTotal`).toBe(investmentAndReturnList[i].investmentTotal);
              expect(investmentAndReturns.value[i].racehorseBookValueEndOfLastMonth, `${errorIndex}: racehorseBookValueEndOfLastMonth`).toBe(investmentAndReturnList[i].racehorseBookValueEndOfLastMonth);
              expect(investmentAndReturns.value[i].investmentRefundPaidUpToLastMonth, `${errorIndex}: investmentRefundPaidUpToLastMonth`).toBe(investmentAndReturnList[i].investmentRefundPaidUpToLastMonth);
              expect(investmentAndReturns.value[i].organizerWithholdingTaxTotal, `${errorIndex}: organizerWithholdingTaxTotal`).toBe(investmentAndReturnList[i].organizerWithholdingTaxTotal);
              expect(investmentAndReturns.value[i].organizerWithholdingTaxCurrentMonthAddition, `${errorIndex}: organizerWithholdingTaxCurrentMonthAddition`).toBe(investmentAndReturnList[i].organizerWithholdingTaxCurrentMonthAddition);
              expect(investmentAndReturns.value[i].clubWithholdingTaxTotal, `${errorIndex}: clubWithholdingTaxTotal`).toBe(investmentAndReturnList[i].clubWithholdingTaxTotal);
              expect(investmentAndReturns.value[i].clubWithholdingTaxCurrentMonthAddition, `${errorIndex}: clubWithholdingTaxCurrentMonthAddition`).toBe(investmentAndReturnList[i].clubWithholdingTaxCurrentMonthAddition);
              expect(investmentAndReturns.value[i].billingAmount, `${errorIndex}: billingAmount`).toBe(investmentAndReturnList[i].billingAmount);
              expect(investmentAndReturns.value[i].paymentAmount, `${errorIndex}: paymentAmount`).toBe(investmentAndReturnList[i].paymentAmount);
          }
          for (let i = 0; i < investmentAndReturnInvestmentList.length; i++) {
            const errorIndex = `index ${investmentAndReturnList[i].createdDate.getFullYear()}/${String(investmentAndReturnList[i].createdDate.getMonth() + 1).padStart(2, '0')}/${String(investmentAndReturnList[i].createdDate.getDate()).padStart(2, '0')}`;
            expect(investmentAndReturnInvestments.value[i].racehorseInvestmentEquivalent, `${errorIndex}: racehorseInvestmentEquivalent`).toBe(investmentAndReturnInvestmentList[i].racehorseInvestmentEquivalent);
            expect(investmentAndReturnInvestments.value[i].discountAllocation, `${errorIndex}: discountAllocation`).toBe(investmentAndReturnInvestmentList[i].discountAllocation);
            expect(investmentAndReturnInvestments.value[i].racehorseInvestment, `${errorIndex}: racehorseInvestment`).toBe(investmentAndReturnInvestmentList[i].racehorseInvestment);
            expect(investmentAndReturnInvestments.value[i].runningCost, `${errorIndex}: runningCost`).toBe(investmentAndReturnInvestmentList[i].runningCost);
            expect(investmentAndReturnInvestments.value[i].subsidy, `${errorIndex}: subsidy`).toBe(investmentAndReturnInvestmentList[i].subsidy);
            expect(investmentAndReturnInvestments.value[i].retroactiveRunningCost, `${errorIndex}: retroactiveRunningCost`).toBe(investmentAndReturnInvestmentList[i].retroactiveRunningCost);
            expect(investmentAndReturnInvestments.value[i].runningCostInvestment, `${errorIndex}: runningCostInvestment`).toBe(investmentAndReturnInvestmentList[i].runningCostInvestment);
            expect(investmentAndReturnInvestments.value[i].insuranceInvestment, `${errorIndex}: insuranceInvestment`).toBe(investmentAndReturnInvestmentList[i].insuranceInvestment);
            expect(investmentAndReturnInvestments.value[i].otherInvestment, `${errorIndex}: otherInvestment`).toBe(investmentAndReturnInvestmentList[i].otherInvestment);
            expect(investmentAndReturnInvestments.value[i].currentMonthInvestmentTotal, `${errorIndex}: currentMonthInvestmentTotal`).toBe(investmentAndReturnInvestmentList[i].currentMonthInvestmentTotal);
          }
          for (let i = 0; i < investmentAndReturnReturnsClubToFundMonthlyList.length; i++) {
            const errorIndex = `index ${investmentAndReturnList[i].createdDate.getFullYear()}/${String(investmentAndReturnList[i].createdDate.getMonth() + 1).padStart(2, '0')}/${String(investmentAndReturnList[i].createdDate.getDate()).padStart(2, '0')}`;
            expect(investmentAndReturnReturnsClubToFundMonthly.value[i].distributionAmount, `${errorIndex}: distributionAmount`).toBe(investmentAndReturnReturnsClubToFundMonthlyList[i].distributionAmount);
            expect(investmentAndReturnReturnsClubToFundMonthly.value[i].refundableInvestmentAmount, `${errorIndex}: refundableInvestmentAmount`).toBe(investmentAndReturnReturnsClubToFundMonthlyList[i].refundableInvestmentAmount);
            expect(investmentAndReturnReturnsClubToFundMonthly.value[i].distributionTargetAmount, `${errorIndex}: distributionTargetAmount`).toBe(investmentAndReturnReturnsClubToFundMonthlyList[i].distributionTargetAmount);
            expect(investmentAndReturnReturnsClubToFundMonthly.value[i].distributionTargetAmountRefundable, `${errorIndex}: distributionTargetAmountRefundable`).toBe(investmentAndReturnReturnsClubToFundMonthlyList[i].distributionTargetAmountRefundable);
            expect(investmentAndReturnReturnsClubToFundMonthly.value[i].distributionTargetAmountProfit, `${errorIndex}: distributionTargetAmountProfit`).toBe(investmentAndReturnReturnsClubToFundMonthlyList[i].distributionTargetAmountProfit);
            expect(investmentAndReturnReturnsClubToFundMonthly.value[i].distributionTargetAmountWithholdingTax, `${errorIndex}: distributionTargetAmountWithholdingTax`).toBe(investmentAndReturnReturnsClubToFundMonthlyList[i].distributionTargetAmountWithholdingTax);
          }
          for (let i = 0; i < investmentAndReturnReturnsFundToMemberMonthlyList.length; i++) {
            const errorIndex = `index ${investmentAndReturnList[i].createdDate.getFullYear()}/${String(investmentAndReturnList[i].createdDate.getMonth() + 1).padStart(2, '0')}/${String(investmentAndReturnList[i].createdDate.getDate()).padStart(2, '0')}`;
            expect(investmentAndReturnReturnsFundToMemberMonthly.value[i].distributionAmount, `${errorIndex}: distributionAmount`).toBe(investmentAndReturnReturnsFundToMemberMonthlyList[i].distributionAmount);
            expect(investmentAndReturnReturnsFundToMemberMonthly.value[i].refundableInvestmentAmount, `${errorIndex}: refundableInvestmentAmount`).toBe(investmentAndReturnReturnsFundToMemberMonthlyList[i].refundableInvestmentAmount);
            expect(investmentAndReturnReturnsFundToMemberMonthly.value[i].distributionTargetAmount, `${errorIndex}: distributionTargetAmount`).toBe(investmentAndReturnReturnsFundToMemberMonthlyList[i].distributionTargetAmount);
            expect(investmentAndReturnReturnsFundToMemberMonthly.value[i].distributionTargetAmountRefundable, `${errorIndex}: distributionTargetAmountRefundable`).toBe(investmentAndReturnReturnsFundToMemberMonthlyList[i].distributionTargetAmountRefundable);
            expect(investmentAndReturnReturnsFundToMemberMonthly.value[i].distributionTargetAmountProfit, `${errorIndex}: distributionTargetAmountProfit`).toBe(investmentAndReturnReturnsFundToMemberMonthlyList[i].distributionTargetAmountProfit);
            expect(investmentAndReturnReturnsFundToMemberMonthly.value[i].distributionTargetAmountWithholdingTax, `${errorIndex}: distributionTargetAmountWithholdingTax`).toBe(investmentAndReturnReturnsFundToMemberMonthlyList[i].distributionTargetAmountWithholdingTax);
          }
          for (let i = 0; i < investmentAndReturnReturnsClubToFundYearlyList.length; i++) {
            const errorIndex = `index ${investmentAndReturnList[i].createdDate.getFullYear()}/${String(investmentAndReturnList[i].createdDate.getMonth() + 1).padStart(2, '0')}/${String(investmentAndReturnList[i].createdDate.getDate()).padStart(2, '0')}`;
            expect(investmentAndReturnReturnsClubToFundYearly.value[i].distributionAmount, `${errorIndex}: distributionAmount`).toBe(investmentAndReturnReturnsClubToFundYearlyList[i].distributionAmount);
            expect(investmentAndReturnReturnsClubToFundYearly.value[i].refundableInvestmentAmount, `${errorIndex}: refundableInvestmentAmount`).toBe(investmentAndReturnReturnsClubToFundYearlyList[i].refundableInvestmentAmount);
            expect(investmentAndReturnReturnsClubToFundYearly.value[i].distributionTargetAmount, `${errorIndex}: distributionTargetAmount`).toBe(investmentAndReturnReturnsClubToFundYearlyList[i].distributionTargetAmount);
            expect(investmentAndReturnReturnsClubToFundYearly.value[i].distributionTargetAmountRefundable, `${errorIndex}: distributionTargetAmountRefundable`).toBe(investmentAndReturnReturnsClubToFundYearlyList[i].distributionTargetAmountRefundable);
            expect(investmentAndReturnReturnsClubToFundYearly.value[i].distributionTargetAmountProfit, `${errorIndex}: distributionTargetAmountProfit`).toBe(investmentAndReturnReturnsClubToFundYearlyList[i].distributionTargetAmountProfit);
            expect(investmentAndReturnReturnsClubToFundYearly.value[i].distributionTargetAmountWithholdingTax, `${errorIndex}: distributionTargetAmountWithholdingTax`).toBe(investmentAndReturnReturnsClubToFundYearlyList[i].distributionTargetAmountWithholdingTax);
          }
          for (let i = 0; i < investmentAndReturnReturnsFundToMemberYearlyOrganizerList.length; i++) {
            const errorIndex = `index ${investmentAndReturnList[i].createdDate.getFullYear()}/${String(investmentAndReturnList[i].createdDate.getMonth() + 1).padStart(2, '0')}/${String(investmentAndReturnList[i].createdDate.getDate()).padStart(2, '0')}`;
            expect(investmentAndReturnReturnsFundToMemberYearlyOrganizer.value[i].distributionAmount, `${errorIndex}: distributionAmount`).toBe(investmentAndReturnReturnsFundToMemberYearlyOrganizerList[i].distributionAmount);
            expect(investmentAndReturnReturnsFundToMemberYearlyOrganizer.value[i].refundableInvestmentAmount, `${errorIndex}: refundableInvestmentAmount`).toBe(investmentAndReturnReturnsFundToMemberYearlyOrganizerList[i].refundableInvestmentAmount);
            expect(investmentAndReturnReturnsFundToMemberYearlyOrganizer.value[i].distributionTargetAmount, `${errorIndex}: distributionTargetAmount`).toBe(investmentAndReturnReturnsFundToMemberYearlyOrganizerList[i].distributionTargetAmount);
            expect(investmentAndReturnReturnsFundToMemberYearlyOrganizer.value[i].distributionTargetAmountRefundable, `${errorIndex}: distributionTargetAmountRefundable`).toBe(investmentAndReturnReturnsFundToMemberYearlyOrganizerList[i].distributionTargetAmountRefundable);
            expect(investmentAndReturnReturnsFundToMemberYearlyOrganizer.value[i].distributionTargetAmountProfit, `${errorIndex}: distributionTargetAmountProfit`).toBe(investmentAndReturnReturnsFundToMemberYearlyOrganizerList[i].distributionTargetAmountProfit);
            expect(investmentAndReturnReturnsFundToMemberYearlyOrganizer.value[i].distributionTargetAmountWithholdingTax, `${errorIndex}: distributionTargetAmountWithholdingTax`).toBe(investmentAndReturnReturnsFundToMemberYearlyOrganizerList[i].distributionTargetAmountWithholdingTax);
          }
          for (let i = 0; i < investmentAndReturnReturnsFundToMemberYearlyClubList.length; i++) {
            const errorIndex = `index ${investmentAndReturnList[i].createdDate.getFullYear()}/${String(investmentAndReturnList[i].createdDate.getMonth() + 1).padStart(2, '0')}/${String(investmentAndReturnList[i].createdDate.getDate()).padStart(2, '0')}`;
            expect(investmentAndReturnReturnsFundToMemberYearlyClub.value[i].distributionAmount, `${errorIndex}: distributionAmount`).toBe(investmentAndReturnReturnsFundToMemberYearlyClubList[i].distributionAmount);
            expect(investmentAndReturnReturnsFundToMemberYearlyClub.value[i].refundableInvestmentAmount, `${errorIndex}: refundableInvestmentAmount`).toBe(investmentAndReturnReturnsFundToMemberYearlyClubList[i].refundableInvestmentAmount);
            expect(investmentAndReturnReturnsFundToMemberYearlyClub.value[i].distributionTargetAmount, `${errorIndex}: distributionTargetAmount`).toBe(investmentAndReturnReturnsFundToMemberYearlyClubList[i].distributionTargetAmount);
            expect(investmentAndReturnReturnsFundToMemberYearlyClub.value[i].distributionTargetAmountRefundable, `${errorIndex}: distributionTargetAmountRefundable`).toBe(investmentAndReturnReturnsFundToMemberYearlyClubList[i].distributionTargetAmountRefundable);
            expect(investmentAndReturnReturnsFundToMemberYearlyClub.value[i].distributionTargetAmountProfit, `${errorIndex}: distributionTargetAmountProfit`).toBe(investmentAndReturnReturnsFundToMemberYearlyClubList[i].distributionTargetAmountProfit);
            expect(investmentAndReturnReturnsFundToMemberYearlyClub.value[i].distributionTargetAmountWithholdingTax, `${errorIndex}: distributionTargetAmountWithholdingTax`).toBe(investmentAndReturnReturnsFundToMemberYearlyClubList[i].distributionTargetAmountWithholdingTax);
          }
        });
    });

    // テストデータの構築の関数を作成する
    function createTestData(fileName: string) {
        const testData = fs.readFileSync(path.join(__dirname, `../../../../test_utils/factories/investment_and_return/csv/${fileName}.tsv`), 'utf8');
        const testDataLines = testData.split('\n');
        const investmentAndReturnList = [];
        const investmentAndReturnInvestmentList = [];
        const investmentAndReturnReturnsClubToFundMonthlyList = [];
        const investmentAndReturnReturnsClubToFundYearlyList = [];
        const investmentAndReturnReturnsFundToMemberMonthlyList = [];
        const investmentAndReturnReturnsFundToMemberYearlyOrganizerList = [];
        const investmentAndReturnReturnsFundToMemberYearlyClubList = [];
        
        for (let i = 0; i < testDataLines.length; i++) {
            const testDataLine = testDataLines[i].split('\t');  
            const createdDate = new Date(testDataLine[0].replace('/', '-'));
            const investmentAndReturn:InvestmentAndReturnModel = {
                horseId: horse.horseId,
                memberId: member.memberId,
                createdDate: createdDate,
                progressedMonth: parseInt(testDataLine[1]),
                yearlyReturnTargetFlag: testDataLine[28] !== "-",
                runningCostInvestmentTotal: parseInt(testDataLine[13]),
                insuranceInvestmentTotal: parseInt(testDataLine[14]),
                otherInvestmentTotal: parseInt(testDataLine[15]),
                investmentTotal: parseInt(testDataLine[16]), 
                racehorseBookValueEndOfLastMonth: parseInt(testDataLine[17]),
                investmentRefundPaidUpToLastMonth: parseInt(testDataLine[18]),
                organizerWithholdingTaxTotal: parseInt(testDataLine[67]),
                organizerWithholdingTaxCurrentMonthAddition: parseInt(testDataLine[68]),
                clubWithholdingTaxTotal: parseInt(testDataLine[69]),
                clubWithholdingTaxCurrentMonthAddition: parseInt(testDataLine[70]),
                billingAmount: parseInt(testDataLine[72]),
                paymentAmount: parseInt(testDataLine[73]),
            };
            investmentAndReturnList.push(investmentAndReturn);
            const investmentAndReturnInvestment:InvestmentAndReturnInvestmentModel = {
                racehorseInvestmentEquivalent: parseInt(testDataLine[2]),
                discountAllocation: parseInt(testDataLine[3]),
                racehorseInvestment: parseInt(testDataLine[4]),
                runningCost: parseInt(testDataLine[5]),
                subsidy: parseInt(testDataLine[6]),
                retroactiveRunningCost: parseInt(testDataLine[7]),
                runningCostInvestment: parseInt(testDataLine[8]),
                insuranceInvestment: parseInt(testDataLine[9]),
                otherInvestment: parseInt(testDataLine[10]),
                currentMonthInvestmentTotal: parseInt(testDataLine[11]),
            };
            investmentAndReturnInvestmentList.push(investmentAndReturnInvestment);
            const investmentAndReturnReturnsClubToFundMonthly:InvestmentAndReturnReturnModel = {
              returnCategory: ReturnCategory.CLUB_TO_FUND_MONTHLY,
              investmentRefundPaidUpToLastMonth: parseInt(testDataLine[18]),
              refundableInvestmentAmount: parseInt(testDataLine[19]),
              distributionTargetAmount: parseInt(testDataLine[20]),
              distributionTargetAmountRefundable: parseInt(testDataLine[21]),
              distributionTargetAmountProfit: parseInt(testDataLine[22]),
              distributionTargetAmountWithholdingTax: parseInt(testDataLine[23]),
              distributionAmount: parseInt(testDataLine[24]),
              refundableInvestmentAmountCarriedForward: parseInt(testDataLine[27]),
            };
            investmentAndReturnReturnsClubToFundMonthlyList.push(investmentAndReturnReturnsClubToFundMonthly);
            if (testDataLine[28] !== "-") {
              const investmentAndReturnReturnsClubToFundYearly:InvestmentAndReturnReturnModel = {
                returnCategory: ReturnCategory.CLUB_TO_FUND_YEARLY,
                investmentRefundPaidUpToLastMonth: 0,
                refundableInvestmentAmount: parseInt(testDataLine[28]),
                distributionTargetAmount: parseInt(testDataLine[29]),
                distributionTargetAmountRefundable: parseInt(testDataLine[30]),
                distributionTargetAmountProfit: parseInt(testDataLine[31]),
                distributionTargetAmountWithholdingTax: parseInt(testDataLine[32]),
                distributionAmount: parseInt(testDataLine[33]),
                refundableInvestmentAmountCarriedForward: parseInt(testDataLine[36]),
              };
              investmentAndReturnReturnsClubToFundYearlyList.push(investmentAndReturnReturnsClubToFundYearly);
            }
            const investmentAndReturnReturnsFundToMemberMonthly:InvestmentAndReturnReturnModel = {
              returnCategory: ReturnCategory.FUND_TO_MEMBER_MONTHLY,
              investmentRefundPaidUpToLastMonth: parseInt(testDataLine[38]),
              refundableInvestmentAmount: parseInt(testDataLine[39]),
              distributionTargetAmount: parseInt(testDataLine[40]),
              distributionTargetAmountRefundable: parseInt(testDataLine[41]),
              distributionTargetAmountProfit: parseInt(testDataLine[42]),
              distributionTargetAmountWithholdingTax: parseInt(testDataLine[43]),
              distributionAmount: parseInt(testDataLine[44]),
              refundableInvestmentAmountCarriedForward: parseInt(testDataLine[47]),
            };
            investmentAndReturnReturnsFundToMemberMonthlyList.push(investmentAndReturnReturnsFundToMemberMonthly);

            if (testDataLine[48] !== "-") {
              const investmentAndReturnReturnsFundToMemberYearlyOrganizer:InvestmentAndReturnReturnModel = {
                returnCategory: ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER,
                investmentRefundPaidUpToLastMonth: 0,
                refundableInvestmentAmount: parseInt(testDataLine[48]),
                distributionTargetAmount: parseInt(testDataLine[49]),
                distributionTargetAmountRefundable: parseInt(testDataLine[50]),
                distributionTargetAmountProfit: parseInt(testDataLine[51]),
                distributionTargetAmountWithholdingTax: parseInt(testDataLine[52]),
                distributionAmount: parseInt(testDataLine[53]),
                refundableInvestmentAmountCarriedForward: parseInt(testDataLine[56]),
              };
              investmentAndReturnReturnsFundToMemberYearlyOrganizerList.push(investmentAndReturnReturnsFundToMemberYearlyOrganizer);
            }
            if (testDataLine[57] !== "-") {
              const investmentAndReturnReturnsFundToMemberYearlyClub:InvestmentAndReturnReturnModel = {
                returnCategory: ReturnCategory.FUND_TO_MEMBER_YEARLY_CLUB,
                investmentRefundPaidUpToLastMonth: 0,
                refundableInvestmentAmount: parseInt(testDataLine[57]),
                distributionTargetAmount: parseInt(testDataLine[58]),
                distributionTargetAmountRefundable: parseInt(testDataLine[59]),
                distributionTargetAmountProfit: parseInt(testDataLine[60]),
                distributionTargetAmountWithholdingTax: parseInt(testDataLine[61]),
                distributionAmount: parseInt(testDataLine[62]),
                refundableInvestmentAmountCarriedForward: parseInt(testDataLine[65]),
              };
              investmentAndReturnReturnsFundToMemberYearlyClubList.push(investmentAndReturnReturnsFundToMemberYearlyClub);
              }
        }
        return {
          investmentAndReturnList,
          investmentAndReturnInvestmentList,
          investmentAndReturnReturnsClubToFundMonthlyList,
          investmentAndReturnReturnsClubToFundYearlyList,
          investmentAndReturnReturnsFundToMemberMonthlyList,
          investmentAndReturnReturnsFundToMemberYearlyOrganizerList,
          investmentAndReturnReturnsFundToMemberYearlyClubList
        };
    }
});