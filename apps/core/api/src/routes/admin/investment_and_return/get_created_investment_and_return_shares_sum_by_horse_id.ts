import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetCreatedInvestmentAndReturnSharesSumByHorseIdResponseSchema } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { getCreatedInvestmentAndReturnSharesSumByHorseId } from '@core-api/repositories/investment_and_return_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getCreatedInvestmentAndReturnSharesSumByHorseIdHandler = createHandler({
  schema: z.object({
    horseId: z.number().positive(),
  }),
  business: (data) => getCreatedInvestmentAndReturnSharesSumByHorseId(Number(data.horseId)),
  toResponse: (result) => create(GetCreatedInvestmentAndReturnSharesSumByHorseIdResponseSchema, {
    list: result.map(item => ({
      createdDate: item.createdDate.toISOString(),
      sharesSum: item.sharesSum,
    }))
  }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
