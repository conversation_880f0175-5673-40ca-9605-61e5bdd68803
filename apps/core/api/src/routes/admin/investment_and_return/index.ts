import { InvestmentAndReturnService } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { createInvestmentAndReturnHandler } from './create_investment_and_return';
import { createMemberClaimAndPayHandler } from './create_member_claim_and_pay';
import { getCreatedInvestmentAndReturnSharesSumByHorseIdHandler } from './get_created_investment_and_return_shares_sum_by_horse_id';
import { getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdHandler } from './get_investment_and_return_by_horse_id_and_created_date_and_member_id';
import { getInvestmentAndReturnListByHorseIdAndCreatedDateHandler } from './get_investment_and_return_list_by_horse_id_and_created_date';

import type { ConnectRouter } from '@connectrpc/connect';

export const implInvestmentAndReturnService = (router: ConnectRouter) =>
  router.service(
    InvestmentAndReturnService,
    {
      createInvestmentAndReturn: unwrapResult(createInvestmentAndReturnHandler),
      createMemberClaimAndPay: unwrapResult(createMemberClaimAndPayHandler),
      getCreatedInvestmentAndReturnSharesSumByHorseId: unwrapResult(getCreatedInvestmentAndReturnSharesSumByHorseIdHandler),
      getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId: unwrapResult(
        getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdHandler
      ),
      getInvestmentAndReturnListByHorseIdAndCreatedDate: unwrapResult(getInvestmentAndReturnListByHorseIdAndCreatedDateHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  );
