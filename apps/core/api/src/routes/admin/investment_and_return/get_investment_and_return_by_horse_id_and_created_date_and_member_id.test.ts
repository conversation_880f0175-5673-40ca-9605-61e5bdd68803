import { create } from '@bufbuild/protobuf';
import { HorseFactory, MemberFactory, InvestmentContractFactory, AdminUserSessionFactory, getClient } from '@core-test/index';
import { GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, InvestmentAndReturnService } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { InvestmentContractStatus, Horse, Member, AdminUserSession, InvestmentContract, ReturnCategory } from '@hami/prisma';

describe('getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdHandler', () => {
  const apiClient = getClient(InvestmentAndReturnService);
  let horse: Horse;
  let member1: Member;
  let member2: Member;
  let adminSession: AdminUserSession;
  let investmentContract1: InvestmentContract;
  let investmentContract2: InvestmentContract;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    
    horse = await HorseFactory.create({
      recruitmentYear: 2015,
      recruitmentNo: 1,
      horseName: 'テスト馬',
      recruitmentName: 'テスト募集馬',
      sharesTotal: 400,
      amountTotal: 36000000,
      birthYear: 2014,
      birthMonth: 5,
      birthDay: 3,
      fundStartYear: 2016,
      fundStartMonth: 4,
      fundStartDay: 1,
    });

    member1 = await MemberFactory.create({
      firstName: '太郎',
      lastName: '田中',
    });
    member2 = await MemberFactory.create({
      firstName: '花子',
      lastName: '佐藤',
    });

    // 2つの出資契約を作成（同じ馬、異なる会員）
    investmentContract1 = await InvestmentContractFactory.create({
      member: { connect: { memberId: member1.memberId } },
      horse: { connect: { horseId: horse.horseId } },
      sharesNumber: 2,
      investmentAmount: 100000,
      discount: 0,
      taxRate: 8,
      investmentAmountBeforeTax: 92593,
      transactionAmount: 92593,
      monthlyDepreciation: 1000,
      contractedAt: new Date(),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    investmentContract2 = await InvestmentContractFactory.create({
      member: { connect: { memberId: member2.memberId } },
      horse: { connect: { horseId: horse.horseId } },
      sharesNumber: 3,
      investmentAmount: 150000,
      discount: 0,
      taxRate: 8,
      investmentAmountBeforeTax: 138889,
      transactionAmount: 138889,
      monthlyDepreciation: 1500,
      contractedAt: new Date(),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });
  });

  describe('getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId', () => {
    it('指定した条件の出資・分配金を正常に取得できる', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const testDate = new Date('2024-01-15');
      
      // InvestmentAndReturn（member1）
      const investmentAndReturn = await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          sharesNumber: 2,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      // InvestmentAndReturnInvestment
      await vPrisma.client.investmentAndReturnInvestment.create({
        data: {
          investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
          racehorseInvestmentEquivalent: 100000,
          discountAllocation: 0,
          racehorseInvestment: 100000,
          runningCost: 50000,
          subsidy: 0,
          retroactiveRunningCost: 0,
          runningCostInvestment: 50000,
          insuranceInvestment: 50000,
          otherInvestment: 0,
          currentMonthInvestmentTotal: 200000,
        },
      });

      // InvestmentAndReturnReturn
      await vPrisma.client.investmentAndReturnReturn.create({
        data: {
          investmentAndReturnId: investmentAndReturn.investmentAndReturnId,
          returnCategory: ReturnCategory.FUND_TO_MEMBER_MONTHLY,
          investmentRefundPaidUpToLastMonth: 0,
          refundableInvestmentAmount: 100000,
          distributionTargetAmount: 50000,
          distributionTargetAmountRefundable: 30000,
          distributionTargetAmountProfit: 20000,
          distributionTargetAmountWithholdingTax: 5000,
          distributionAmount: 45000,
          refundableInvestmentAmountCarriedForward: 70000,
        },
      });

      // ===== Act =====
      const request = create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
        memberId: member1.memberId,
      });
      const response = await apiClient.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(request, { headers });

      // ===== Assert =====
      expect(response.investmentAndReturn).toBeDefined();
      const result = response.investmentAndReturn!;
      
      // InvestmentAndReturnの基本情報
      expect(result.investmentAndReturnId).toBe(investmentAndReturn.investmentAndReturnId);
      expect(result.horseId).toBe(horse.horseId);
      expect(result.memberId).toBe(member1.memberId);
      expect(result.createdDateYear).toBe(testDate.getFullYear());
      expect(result.createdDateMonth).toBe(testDate.getMonth() + 1);
      expect(result.createdDateDay).toBe(testDate.getDate());
      expect(result.progressedMonth).toBe(1);
      expect(result.yearlyReturnTargetFlag).toBe(false);
      expect(result.sharesNumber).toBe(2);
      expect(result.billingAmount).toBe(150000);
      expect(result.paymentAmount).toBe(0);

      // InvestmentAndReturnInvestmentの情報
      expect(result.investment).toBeDefined();
      const investment = result.investment!;
      expect(investment.racehorseInvestmentEquivalent).toBe(100000);
      expect(investment.discountAllocation).toBe(0);
      expect(investment.racehorseInvestment).toBe(100000);
      expect(investment.runningCost).toBe(50000);
      expect(investment.subsidy).toBe(0);
      expect(investment.retroactiveRunningCost).toBe(0);
      expect(investment.runningCostInvestment).toBe(50000);
      expect(investment.insuranceInvestment).toBe(50000);
      expect(investment.otherInvestment).toBe(0);
      expect(investment.currentMonthInvestmentTotal).toBe(200000);

      // InvestmentAndReturnReturnの情報
      expect(result.returns).toHaveLength(1);
      const returnItem = result.returns[0];
      expect(returnItem.returnCategory).toBe(ReturnCategory.FUND_TO_MEMBER_MONTHLY);
      expect(returnItem.investmentRefundPaidUpToLastMonth).toBe(0);
      expect(returnItem.refundableInvestmentAmount).toBe(100000);
      expect(returnItem.distributionTargetAmount).toBe(50000);
      expect(returnItem.distributionTargetAmountRefundable).toBe(30000);
      expect(returnItem.distributionTargetAmountProfit).toBe(20000);
      expect(returnItem.distributionTargetAmountWithholdingTax).toBe(5000);
      expect(returnItem.distributionAmount).toBe(45000);
      expect(returnItem.refundableInvestmentAmountCarriedForward).toBe(70000);
    });

    it('異なる会員のデータは取得されない', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const testDate = new Date('2024-01-15');
      
      // member1のInvestmentAndReturn
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          sharesNumber: 2,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      // member2のInvestmentAndReturn
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member2.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          sharesNumber: 3,
          runningCostInvestmentTotal: 150000,
          insuranceInvestmentTotal: 75000,
          otherInvestmentTotal: 200000,
          investmentTotal: 425000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 225000,
          paymentAmount: 0,
        },
      });

      // ===== Act =====
      const request = create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
        memberId: member2.memberId, // member2を指定
      });
      const response = await apiClient.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(request, { headers });

      // ===== Assert =====
      expect(response.investmentAndReturn).toBeDefined();
      expect(response.investmentAndReturn!.memberId).toBe(member2.memberId);
      expect(response.investmentAndReturn!.billingAmount).toBe(225000);
      expect(response.investmentAndReturn!.sharesNumber).toBe(3);
    });

    it('データが存在しない場合はNotFoundエラーを返す', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const testDate = new Date('2024-01-15');

      // ===== Act & Assert =====
      const request = create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
        memberId: member1.memberId,
      });

      await expect(apiClient.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(request, { headers })).rejects.toThrow('指定された条件の出資・分配金が見つかりません');
    });

    it('無効な日付の場合はInvalidArgumentエラーを返す', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // ===== Act & Assert =====
      const request = create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: 2024,
        createdDateMonth: 2,
        createdDateDay: 30, // 2月30日は無効
        memberId: member1.memberId,
      });

      await expect(apiClient.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(request, { headers })).rejects.toThrow('無効な日付です');
    });

    it('年が範囲外の場合はInvalidArgumentエラーを返す', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // ===== Act & Assert =====
      const request = create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: 1800, // 範囲外
        createdDateMonth: 1,
        createdDateDay: 1,
        memberId: member1.memberId,
      });

      await expect(apiClient.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(request, { headers })).rejects.toThrow('Number must be greater than or equal to 1900');
    });

    it('月が範囲外の場合はInvalidArgumentエラーを返す', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // ===== Act & Assert =====
      const request = create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: 2024,
        createdDateMonth: 13, // 範囲外
        createdDateDay: 1,
        memberId: member1.memberId,
      });

      await expect(apiClient.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(request, { headers })).rejects.toThrow('Number must be less than or equal to 12');
    });

    it('日が範囲外の場合はInvalidArgumentエラーを返す', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // ===== Act & Assert =====
      const request = create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: 2024,
        createdDateMonth: 1,
        createdDateDay: 32, // 範囲外
        memberId: member1.memberId,
      });

      await expect(apiClient.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(request, { headers })).rejects.toThrow('Number must be less than or equal to 31');
    });

    it('認証されていない場合はUnauthenticatedエラーを返す', async () => {
      // ===== Arrange =====
      const testDate = new Date('2024-01-15');

      // ===== Act & Assert =====
      const request = create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
        memberId: member1.memberId,
      });

      await expect(apiClient.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(request, {})).rejects.toThrow('Unauthorized');
    });

    it('無効なセッショントークンの場合はUnauthenticatedエラーを返す', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: 'invalid-token',
      });

      const testDate = new Date('2024-01-15');

      // ===== Act & Assert =====
      const request = create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema, {
        horseId: horse.horseId,
        createdDateYear: testDate.getFullYear(),
        createdDateMonth: testDate.getMonth() + 1,
        createdDateDay: testDate.getDate(),
        memberId: member1.memberId,
      });

      await expect(apiClient.getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId(request, { headers })).rejects.toThrow('Unauthorized');
    });
  });
});
