import { create } from '@bufbuild/protobuf';
import { HorseFactory, MemberFactory, InvestmentContractFactory, AdminUserSessionFactory, getClient } from '@core-test/index';
import { GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema, InvestmentAndReturnService } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { InvestmentContractStatus, Horse, Member, AdminUserSession, InvestmentContract } from '@hami/prisma';

describe('getCreatedInvestmentAndReturnSharesSumByHorseIdHandler', () => {
  const apiClient = getClient(InvestmentAndReturnService);
  let horse: Horse;
  let member1: Member;
  let member2: Member;
  let adminSession: AdminUserSession;
  let investmentContract1: InvestmentContract;
  let investmentContract2: InvestmentContract;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    
    horse = await HorseFactory.create({
      recruitmentYear: 2015,
      recruitmentNo: 1,
      horseName: 'テスト馬',
      recruitmentName: 'テスト募集馬',
      sharesTotal: 400,
      amountTotal: 36000000,
      birthYear: 2014,
      birthMonth: 5,
      birthDay: 3,
      fundStartYear: 2016,
      fundStartMonth: 4,
      fundStartDay: 1,
    });

    member1 = await MemberFactory.create();
    member2 = await MemberFactory.create();

    // 2つの出資契約を作成（同じ馬、異なる会員）
    investmentContract1 = await InvestmentContractFactory.create({
      member: { connect: { memberId: member1.memberId } },
      horse: { connect: { horseId: horse.horseId } },
      sharesNumber: 2,
      investmentAmount: 100000,
      discount: 0,
      taxRate: 8,
      investmentAmountBeforeTax: 92593,
      transactionAmount: 92593,
      monthlyDepreciation: 1000,
      contractedAt: new Date(),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    investmentContract2 = await InvestmentContractFactory.create({
      member: { connect: { memberId: member2.memberId } },
      horse: { connect: { horseId: horse.horseId } },
      sharesNumber: 3,
      investmentAmount: 150000,
      discount: 0,
      taxRate: 8,
      investmentAmountBeforeTax: 138889,
      transactionAmount: 138889,
      monthlyDepreciation: 1500,
      contractedAt: new Date(),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });
  });

  describe('getCreatedInvestmentAndReturnSharesSumByHorseId', () => {
    it('正常に実行できる', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // テストデータを作成：同じ日付で2つのInvestmentAndReturnを作成
      const testDate = new Date('2024-01-15');
      
      // InvestmentAndReturn1（member1）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      // InvestmentAndReturn2（member2）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member2.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 150000,
          insuranceInvestmentTotal: 75000,
          otherInvestmentTotal: 200000,
          investmentTotal: 425000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 225000,
          paymentAmount: 0,
        },
      });

      // ===== Act =====
      const request = create(GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema, {
        horseId: horse.horseId,
      });
      const response = await apiClient.getCreatedInvestmentAndReturnSharesSumByHorseId(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(1);
      expect(response.list[0].createdDate).toBe(testDate.toISOString());
      expect(response.list[0].sharesSum).toBe(2);
    });

    it('複数の日付でデータが存在する場合、日付ごとにグループ化される', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const date1 = new Date('2024-01-15');
      const date2 = new Date('2024-02-15');

      // date1のInvestmentAndReturn（member1のみ）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: date1,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      // date2のInvestmentAndReturn（member1 + member2）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: date2,
          progressedMonth: 2,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member2.memberId,
          createdDate: date2,
          progressedMonth: 2,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 150000,
          insuranceInvestmentTotal: 75000,
          otherInvestmentTotal: 200000,
          investmentTotal: 425000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 225000,
          paymentAmount: 0,
        },
      });

      // ===== Act =====
      const request = create(GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema, {
        horseId: horse.horseId,
      });
      const response = await apiClient.getCreatedInvestmentAndReturnSharesSumByHorseId(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(2);
      
      // date1の結果: member1のみ
      const date1Result = response.list.find(item => item.createdDate === date1.toISOString());
      expect(date1Result).toBeDefined();
      expect(date1Result!.sharesSum).toBe(1);

      // date2の結果: member1 + member2の2人
      const date2Result = response.list.find(item => item.createdDate === date2.toISOString());
      expect(date2Result).toBeDefined();
      expect(date2Result!.sharesSum).toBe(2);
    });

    it('データが存在しない場合、空のリストが返される', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // ===== Act =====
      const request = create(GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema, {
        horseId: horse.horseId,
      });
      const response = await apiClient.getCreatedInvestmentAndReturnSharesSumByHorseId(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(0);
    });

    it('COMPLETEDでない契約は除外される', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // PENDINGの契約を作成
      const pendingContract = await InvestmentContractFactory.create({
        member: { connect: { memberId: member1.memberId } },
        horse: { connect: { horseId: horse.horseId } },
        sharesNumber: 1,
        investmentAmount: 50000,
        discount: 0,
        taxRate: 8,
        investmentAmountBeforeTax: 46296,
        transactionAmount: 46296,
        monthlyDepreciation: 500,
        contractedAt: new Date(),
        contractStatus: InvestmentContractStatus.PENDING,
      });

      const testDate = new Date('2024-01-15');

      // COMPLETED契約のInvestmentAndReturn
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      // PENDING契約のInvestmentAndReturn（これは除外されるべき）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 50000,
          insuranceInvestmentTotal: 25000,
          otherInvestmentTotal: 200000,
          investmentTotal: 275000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 75000,
          paymentAmount: 0,
        },
      });

      // ===== Act =====
      const request = create(GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema, {
        horseId: horse.horseId,
      });
      const response = await apiClient.getCreatedInvestmentAndReturnSharesSumByHorseId(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(1);
      expect(response.list[0].createdDate).toBe(testDate.toISOString());
      // PENDING契約は除外されるため、COMPLETED契約のみ: 2
      expect(response.list[0].sharesSum).toBe(2);
    });

    it('contractEndAtが設定されている契約は除外される', async () => {
      // ===== Arrange =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // contractEndAtが設定されている契約を作成
      const endedContract = await InvestmentContractFactory.create({
        member: { connect: { memberId: member1.memberId } },
        horse: { connect: { horseId: horse.horseId } },
        sharesNumber: 1,
        investmentAmount: 50000,
        discount: 0,
        taxRate: 8,
        investmentAmountBeforeTax: 46296,
        transactionAmount: 46296,
        monthlyDepreciation: 500,
        contractedAt: new Date(),
        contractEndAt: new Date('2024-12-31'),
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      const testDate = new Date('2024-01-15');

      // contractEndAtがnullの契約のInvestmentAndReturn
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 100000,
          insuranceInvestmentTotal: 50000,
          otherInvestmentTotal: 200000,
          investmentTotal: 350000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 150000,
          paymentAmount: 0,
        },
      });

      // contractEndAtが設定されている契約のInvestmentAndReturn（これは除外されるべき）
      await vPrisma.client.investmentAndReturn.create({
        data: {
          horseId: horse.horseId,
          memberId: member1.memberId,
          createdDate: testDate,
          progressedMonth: 1,
          yearlyReturnTargetFlag: false,
          runningCostInvestmentTotal: 50000,
          insuranceInvestmentTotal: 25000,
          otherInvestmentTotal: 200000,
          investmentTotal: 275000,
          racehorseBookValueEndOfLastMonth: 0,
          investmentRefundPaidUpToLastMonth: 0,
          organizerWithholdingTaxTotal: 0,
          organizerWithholdingTaxCurrentMonthAddition: 0,
          clubWithholdingTaxTotal: 0,
          clubWithholdingTaxCurrentMonthAddition: 0,
          billingAmount: 75000,
          paymentAmount: 0,
        },
      });

      // ===== Act =====
      const request = create(GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema, {
        horseId: horse.horseId,
      });
      const response = await apiClient.getCreatedInvestmentAndReturnSharesSumByHorseId(request, { headers });

      // ===== Assert =====
      expect(response.list).toHaveLength(1);
      expect(response.list[0].createdDate).toBe(testDate.toISOString());
      // contractEndAtが設定されている契約は除外されるため、contractEndAtがnullの契約のみ: 2
      expect(response.list[0].sharesSum).toBe(2);
    });
  });
});
