import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetInvestmentAndReturnListByHorseIdAndCreatedDateResponseSchema } from '@hami/core-admin-api-schema/investment_and_return_service_pb';
import { getInvestmentAndReturnListByHorseIdAndCreatedDate } from '@core-api/repositories/investment_and_return_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const getInvestmentAndReturnListByHorseIdAndCreatedDateHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
    createdDateYear: z.number().int().min(1900).max(2100),
    createdDateMonth: z.number().int().min(1).max(12),
    createdDateDay: z.number().int().min(1).max(31),
  }),
  business: (data) => {
    const createdDate = new Date(data.createdDateYear, data.createdDateMonth - 1, data.createdDateDay);
    return getInvestmentAndReturnListByHorseIdAndCreatedDate(data.horseId, createdDate);
  },
  toResponse: (result) => create(GetInvestmentAndReturnListByHorseIdAndCreatedDateResponseSchema, {
    list: result.map(item => ({
      investmentAndReturnId: item.investmentAndReturnId,
      createdDateYear: item.createdDate.getFullYear(),
      createdDateMonth: item.createdDate.getMonth() + 1,
      createdDateDay: item.createdDate.getDate(),
      progressedMonth: item.progressedMonth,
      yearlyReturnTargetFlag: item.yearlyReturnTargetFlag,
      billingAmount: item.billingAmount || 0,
      paymentAmount: item.paymentAmount || 0,
      memberId: item.member.memberId,
      memberName: `${item.member.lastName ?? ''}${item.member.firstName ?? ''}`,
    }))
  }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
