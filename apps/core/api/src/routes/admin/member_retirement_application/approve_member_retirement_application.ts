import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  ApproveMemberRetirementApplicationResponseSchema
} from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { approveMemberRetirementApplication } from '@core-api/repositories/member_retirement_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const ApproveMemberRetirementApplicationRequestValidationSchema = z.object({
  memberRetirementApplicationId: z.number(),
});

export const approveMemberRetirementApplicationHandler = createHandler({
  schema: ApproveMemberRetirementApplicationRequestValidationSchema,
  business: ({ memberRetirementApplicationId }) => 
    ResultAsync.fromPromise(
      approveMemberRetirementApplication(memberRetirementApplicationId),
      (_error) => new DatabaseError('Failed to approve member retirement application')
    ).andThen((result) => 
      result.mapErr((error) => {
        if (error.type === 'NOT_FOUND') {
          return new ConnectError('Member retirement application not found', Code.NotFound);
        }
        if (error.type === 'ALREADY_PROCESSED') {
          return new ConnectError('Member retirement application is already processed', Code.FailedPrecondition);
        }
        return new DatabaseError('Failed to approve member retirement application');
      })
    ),
  toResponse: () =>
    create(ApproveMemberRetirementApplicationResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .with(P._, () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 