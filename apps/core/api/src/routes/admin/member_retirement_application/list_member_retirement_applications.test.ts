import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MemberFactory } from '@core-test/factories/member_factory';
import { getClient } from '@core-test/index';
import {
  MemberRetirementApplicationService,
  ListMemberRetirementApplicationsRequestSchema,
} from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import type { AdminUserSession, Member, MemberRetirementApplication } from '@hami/prisma';

describe('listMemberRetirementApplications API', () => {
  const apiClient = getClient(MemberRetirementApplicationService);
  let adminSession: AdminUserSession;
  let member1: Member;
  let member2: Member;
  let member3: Member;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();

    // テスト用のメンバーを作成
    member1 = await MemberFactory.create({
      firstName: '太郎',
      lastName: '山田',
      memberNumber: 10001,
    });

    member2 = await MemberFactory.create({
      firstName: '花子',
      lastName: '佐藤',
      memberNumber: 10002,
    });

    member3 = await MemberFactory.create({
      firstName: '次郎',
      lastName: '田中',
      memberNumber: 10003,
    });

    // テスト用の退会申請を作成
    await vPrisma.client.memberRetirementApplication.createMany({
      data: [
        {
          memberId: member1.memberId,
          applicationDate: new Date('2024-01-15'),
          approvedAt: null,
          rejectedAt: null,
        },
        {
          memberId: member2.memberId,
          applicationDate: new Date('2024-01-20'),
          approvedAt: new Date('2024-01-25'),
          rejectedAt: null,
        },
        {
          memberId: member3.memberId,
          applicationDate: new Date('2024-01-30'),
          approvedAt: null,
          rejectedAt: new Date('2024-02-05'),
        },
      ],
    });
  });

  it('デフォルトでは未処理の退会申請のみを取得する', async () => {
    const request = create(ListMemberRetirementApplicationsRequestSchema, {});

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listMemberRetirementApplications(request, { headers });

    expect(response).toBeDefined();
    expect(response.applications).toHaveLength(1);
    expect(response.applications[0]?.memberId).toBe(member1.memberId);
    expect(response.applications[0]?.approvedAt).toBe('');
    expect(response.applications[0]?.rejectedAt).toBe('');
  });

  it('承認済みを含む場合、未処理と承認済みの申請を取得する', async () => {
    const request = create(ListMemberRetirementApplicationsRequestSchema, {
      includeApproved: true,
      includeRejected: false,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listMemberRetirementApplications(request, { headers });

    expect(response).toBeDefined();
    expect(response.applications).toHaveLength(2);

    const memberIds = response.applications.map((app) => app?.memberId).sort();
    expect(memberIds).toEqual([member1.memberId, member2.memberId].sort());
  });

  it('拒否済みを含む場合、未処理と拒否済みの申請を取得する', async () => {
    const request = create(ListMemberRetirementApplicationsRequestSchema, {
      includeApproved: false,
      includeRejected: true,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listMemberRetirementApplications(request, { headers });

    expect(response).toBeDefined();
    expect(response.applications).toHaveLength(2);

    const memberIds = response.applications.map((app) => app?.memberId).sort();
    expect(memberIds).toEqual([member1.memberId, member3.memberId].sort());
  });

  it('承認済みと拒否済みの両方を含む場合、すべての申請を取得する', async () => {
    const request = create(ListMemberRetirementApplicationsRequestSchema, {
      includeApproved: true,
      includeRejected: true,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listMemberRetirementApplications(request, { headers });

    expect(response).toBeDefined();
    expect(response.applications).toHaveLength(3);

    const memberIds = response.applications.map((app) => app?.memberId).sort();
    expect(memberIds).toEqual([member1.memberId, member2.memberId, member3.memberId].sort());
  });

  it('申請日時の降順でソートされている', async () => {
    const request = create(ListMemberRetirementApplicationsRequestSchema, {
      includeApproved: true,
      includeRejected: true,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listMemberRetirementApplications(request, { headers });

    expect(response).toBeDefined();
    expect(response.applications).toHaveLength(3);

    const applicationDates = response.applications.map((app) => app?.applicationDate);
    expect(applicationDates).toEqual([
      '2024-01-30T00:00:00.000Z', // member3
      '2024-01-20T00:00:00.000Z', // member2
      '2024-01-15T00:00:00.000Z', // member1
    ]);
  });
});
