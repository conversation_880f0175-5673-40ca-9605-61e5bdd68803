import { MemberRetirementApplicationService } from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { approveMemberRetirementApplicationHandler } from './approve_member_retirement_application';
import { getMemberRetirementApplicationHandler } from './get_member_retirement_application';
import { listMemberRetirementApplicationsHandler } from './list_member_retirement_applications';
import { rejectMemberRetirementApplicationHandler } from './reject_member_retirement_application';

import type { ConnectRouter } from '@connectrpc/connect';

export const implMemberRetirementApplicationService = (router: ConnectRouter) =>
  router.service(
    MemberRetirementApplicationService,
    {
      listMemberRetirementApplications: unwrapResult(listMemberRetirementApplications<PERSON>and<PERSON>),
      getMemberRetirementApplication: unwrapResult(getMemberRetirementApplicationHandler),
      approveMemberRetirementApplication: unwrapResult(approveMemberRetirementApplicationHandler),
      rejectMemberRetirementApplication: unwrapResult(rejectMemberRetirementApplicationHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  ); 