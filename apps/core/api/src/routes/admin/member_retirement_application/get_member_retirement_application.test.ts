import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MemberFactory } from '@core-test/factories/member_factory';
import { getClient } from '@core-test/index';
import {
  MemberRetirementApplicationService,
  GetMemberRetirementApplicationRequestSchema,
} from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import type { AdminUserSession, Member, MemberRetirementApplication } from '@hami/prisma';

describe('getMemberRetirementApplication API', () => {
  const apiClient = getClient(MemberRetirementApplicationService);
  let adminSession: AdminUserSession;
  let member: Member;
  let application: MemberRetirementApplication;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();

    // テスト用のメンバーを作成
    member = await MemberFactory.create({
      firstName: '太郎',
      lastName: '山田',
      memberNumber: 10001,
    });

    // テスト用の退会申請を作成
    application = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-15'),
        approvedAt: null,
        rejectedAt: null,
      },
    });
  });

  it('指定したIDの退会申請詳細を取得できる', async () => {
    const request = create(GetMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: application.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.getMemberRetirementApplication(request, { headers });

    expect(response).toBeDefined();
    expect(response.application).toBeDefined();
    expect(response.application?.memberRetirementApplicationId).toBe(application.memberRetirementApplicationId);
    expect(response.application?.memberId).toBe(member.memberId);
    expect(response.application?.memberNumber).toBe(member.memberNumber);
    expect(response.application?.memberName).toBe(`${member.lastName} ${member.firstName}`);
    expect(response.application?.applicationDate).toBe(application.applicationDate.toISOString());
    expect(response.application?.approvedAt).toBe('');
    expect(response.application?.rejectedAt).toBe('');
  });

  it('承認済みの退会申請詳細を取得できる', async () => {
    // 承認済みの退会申請を作成
    const approvedApplication = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-20'),
        approvedAt: new Date('2024-01-25'),
        rejectedAt: null,
      },
    });

    const request = create(GetMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: approvedApplication.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.getMemberRetirementApplication(request, { headers });

    expect(response).toBeDefined();
    expect(response.application).toBeDefined();
    expect(response.application?.approvedAt).toBe(approvedApplication.approvedAt?.toISOString());
    expect(response.application?.rejectedAt).toBe('');
  });

  it('拒否済みの退会申請詳細を取得できる', async () => {
    // 拒否済みの退会申請を作成
    const rejectedApplication = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-30'),
        approvedAt: null,
        rejectedAt: new Date('2024-02-05'),
      },
    });

    const request = create(GetMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: rejectedApplication.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.getMemberRetirementApplication(request, { headers });

    expect(response).toBeDefined();
    expect(response.application).toBeDefined();
    expect(response.application?.approvedAt).toBe('');
    expect(response.application?.rejectedAt).toBe(rejectedApplication.rejectedAt?.toISOString());
  });

  it('存在しない退会申請IDの場合はエラーになる', async () => {
    const request = create(GetMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: 99999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.getMemberRetirementApplication(request, { headers })).rejects.toThrow();
  });

  it('無効な退会申請IDの場合はエラーになる', async () => {
    const request = create(GetMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: -1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.getMemberRetirementApplication(request, { headers })).rejects.toThrow();
  });
});
