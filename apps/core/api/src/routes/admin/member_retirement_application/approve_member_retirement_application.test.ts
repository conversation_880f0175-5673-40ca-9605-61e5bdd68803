import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MemberFactory } from '@core-test/factories/member_factory';
import { getClient } from '@core-test/index';
import {
  MemberRetirementApplicationService,
  ApproveMemberRetirementApplicationRequestSchema,
} from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import type { AdminUserSession, Member, MemberRetirementApplication } from '@hami/prisma';

describe('approveMemberRetirementApplication API', () => {
  const apiClient = getClient(MemberRetirementApplicationService);
  let adminSession: AdminUserSession;
  let member: Member;
  let application: MemberRetirementApplication;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();

    // テスト用のメンバーを作成
    member = await MemberFactory.create({
      firstName: '太郎',
      lastName: '山田',
      memberNumber: 10001,
    });

    // テスト用の未処理退会申請を作成
    application = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-15'),
        approvedAt: null,
        rejectedAt: null,
      },
    });
  });

  it('未処理の退会申請を承認できる', async () => {
    const request = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: application.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.approveMemberRetirementApplication(request, { headers });

    expect(response).toBeDefined();

    // 承認後、退会申請が承認済みになっていることを確認
    const updatedApplication = await vPrisma.client.memberRetirementApplication.findUnique({
      where: { memberRetirementApplicationId: application.memberRetirementApplicationId },
    });

    expect(updatedApplication).toBeDefined();
    expect(updatedApplication?.approvedAt).not.toBeNull();
    expect(updatedApplication?.rejectedAt).toBeNull();

    // メンバーの退会日が翌月の末に設定されていることを確認
    const updatedMember = await vPrisma.client.member.findUnique({
      where: { memberId: member.memberId },
    });

    expect(updatedMember).toBeDefined();
    // 2024年1月15日の翌月の末は2024年2月29日（うるう年）
    const expectedRetirementDate = new Date('2024-02-29');
    expect(updatedMember?.retirementDate).toEqual(expectedRetirementDate);
  });

  it('承認時にメンバーの退会日が翌月の末に正しく設定される', async () => {
    const applicationDate = new Date('2024-03-20');
    const testApplication = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: applicationDate,
        approvedAt: null,
        rejectedAt: null,
      },
    });

    const request = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: testApplication.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.approveMemberRetirementApplication(request, { headers });

    expect(response).toBeDefined();

    // メンバーの退会日が翌月の末と一致することを確認
    const updatedMember = await vPrisma.client.member.findUnique({
      where: { memberId: member.memberId },
    });

    // 2024年3月20日の翌月の末は2024年4月30日
    const expectedRetirementDate = new Date('2024-04-30');
    expect(updatedMember?.retirementDate).toEqual(expectedRetirementDate);
  });

  it('年をまたぐ場合の退会日計算が正しい', async () => {
    const applicationDate = new Date('2024-12-15');
    const testApplication = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: applicationDate,
        approvedAt: null,
        rejectedAt: null,
      },
    });

    const request = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: testApplication.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.approveMemberRetirementApplication(request, { headers });

    expect(response).toBeDefined();

    // メンバーの退会日が翌月の末と一致することを確認
    const updatedMember = await vPrisma.client.member.findUnique({
      where: { memberId: member.memberId },
    });

    // 2024年12月15日の翌月の末は2025年1月31日
    const expectedRetirementDate = new Date('2025-01-31');
    expect(updatedMember?.retirementDate).toEqual(expectedRetirementDate);
  });

  it('うるう年でない年の2月の退会日計算が正しい', async () => {
    const applicationDate = new Date('2023-01-15');
    const testApplication = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: applicationDate,
        approvedAt: null,
        rejectedAt: null,
      },
    });

    const request = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: testApplication.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.approveMemberRetirementApplication(request, { headers });

    expect(response).toBeDefined();

    // メンバーの退会日が翌月の末と一致することを確認
    const updatedMember = await vPrisma.client.member.findUnique({
      where: { memberId: member.memberId },
    });

    // 2023年1月15日の翌月の末は2023年2月28日（うるう年でない）
    const expectedRetirementDate = new Date('2023-02-28');
    expect(updatedMember?.retirementDate).toEqual(expectedRetirementDate);
  });

  it('既に承認済みの退会申請は承認できない', async () => {
    // 承認済みの退会申請を作成
    const approvedApplication = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-20'),
        approvedAt: new Date('2024-01-25'),
        rejectedAt: null,
      },
    });

    const request = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: approvedApplication.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.approveMemberRetirementApplication(request, { headers })).rejects.toThrow();

    // 承認日時が変更されていないことを確認
    const unchangedApplication = await vPrisma.client.memberRetirementApplication.findUnique({
      where: { memberRetirementApplicationId: approvedApplication.memberRetirementApplicationId },
    });

    expect(unchangedApplication?.approvedAt).toEqual(approvedApplication.approvedAt);
  });

  it('既に拒否済みの退会申請は承認できない', async () => {
    // 拒否済みの退会申請を作成
    const rejectedApplication = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-30'),
        approvedAt: null,
        rejectedAt: new Date('2024-02-05'),
      },
    });

    const request = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: rejectedApplication.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.approveMemberRetirementApplication(request, { headers })).rejects.toThrow();

    // 拒否日時が変更されていないことを確認
    const unchangedApplication = await vPrisma.client.memberRetirementApplication.findUnique({
      where: { memberRetirementApplicationId: rejectedApplication.memberRetirementApplicationId },
    });

    expect(unchangedApplication?.rejectedAt).toEqual(rejectedApplication.rejectedAt);
    expect(unchangedApplication?.approvedAt).toBeNull();
  });

  it('存在しない退会申請IDの場合はエラーになる', async () => {
    const request = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: 99999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.approveMemberRetirementApplication(request, { headers })).rejects.toThrow();
  });

  it('無効な退会申請IDの場合はエラーになる', async () => {
    const request = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: -1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.approveMemberRetirementApplication(request, { headers })).rejects.toThrow();
  });

  it('複数の退会申請がある場合でも正しく処理される', async () => {
    // 同じメンバーの複数の退会申請を作成
    const application1 = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-10'),
        approvedAt: null,
        rejectedAt: null,
      },
    });

    const application2 = await vPrisma.client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-15'),
        approvedAt: null,
        rejectedAt: null,
      },
    });

    // 最初の申請を承認
    const request1 = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: application1.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response1 = await apiClient.approveMemberRetirementApplication(request1, { headers });

    expect(response1).toBeDefined();

    // 2番目の申請も承認できることを確認
    const request2 = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: application2.memberRetirementApplicationId,
    });

    const response2 = await apiClient.approveMemberRetirementApplication(request2, { headers });

    expect(response2).toBeDefined();

    // 両方の申請が承認済みになっていることを確認
    const updatedApplication1 = await vPrisma.client.memberRetirementApplication.findUnique({
      where: { memberRetirementApplicationId: application1.memberRetirementApplicationId },
    });

    const updatedApplication2 = await vPrisma.client.memberRetirementApplication.findUnique({
      where: { memberRetirementApplicationId: application2.memberRetirementApplicationId },
    });

    expect(updatedApplication1?.approvedAt).not.toBeNull();
    expect(updatedApplication2?.approvedAt).not.toBeNull();
  });

  it('承認日時が現在時刻で設定される', async () => {
    const beforeApproval = new Date();

    const request = create(ApproveMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: application.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.approveMemberRetirementApplication(request, { headers });

    expect(response).toBeDefined();

    const afterApproval = new Date();

    // 承認後、退会申請の承認日時を確認
    const updatedApplication = await vPrisma.client.memberRetirementApplication.findUnique({
      where: { memberRetirementApplicationId: application.memberRetirementApplicationId },
    });

    expect(updatedApplication?.approvedAt).toBeDefined();
    expect(updatedApplication?.approvedAt).toBeInstanceOf(Date);

    if (updatedApplication?.approvedAt) {
      expect(updatedApplication.approvedAt.getTime()).toBeGreaterThanOrEqual(beforeApproval.getTime());
      expect(updatedApplication.approvedAt.getTime()).toBeLessThanOrEqual(afterApproval.getTime());
    }
  });
});
