import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  ListMemberRetirementApplicationsResponseSchema,
  MemberRetirementApplicationSchema 
} from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { listMemberRetirementApplications } from '@core-api/repositories/member_retirement_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const ListMemberRetirementApplicationsRequestValidationSchema = z.object({
  includeApproved: z.boolean().optional(),
  includeRejected: z.boolean().optional(),
});

export const listMemberRetirementApplicationsHandler = createHandler({
  schema: ListMemberRetirementApplicationsRequestValidationSchema,
  business: ({ includeApproved, includeRejected }) => 
    ResultAsync.fromPromise(
      listMemberRetirementApplications(includeApproved ?? false, includeRejected ?? false),
      (_error) => new DatabaseError('Failed to list member retirement applications')
    ),
  toResponse: (result) => {
    const applications = result.unwrapOr([]);
    return create(ListMemberRetirementApplicationsResponseSchema, {
      applications: applications.map((application) =>
        create(MemberRetirementApplicationSchema, {
          memberRetirementApplicationId: application.memberRetirementApplicationId,
          memberId: application.memberId,
          memberNumber: application.member.memberNumber,
          memberName: `${application.member.lastName} ${application.member.firstName}`,
          applicationDate: application.applicationDate.toISOString(),
          approvedAt: application.approvedAt?.toISOString() ?? '',
          rejectedAt: application.rejectedAt?.toISOString() ?? '',
        })
      ),
    });
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .with(P._, () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 