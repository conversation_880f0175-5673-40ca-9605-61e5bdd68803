import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { MemberFactory } from '@core-test/factories/member_factory';
import { getClient } from '@core-test/index';
import {
  MemberRetirementApplicationService,
  RejectMemberRetirementApplicationRequestSchema,
} from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import type { AdminUserSession, Member, MemberRetirementApplication } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';

describe('rejectMemberRetirementApplication API', () => {
  const apiClient = getClient(MemberRetirementApplicationService);
  let adminSession: AdminUserSession;
  let member: Member;
  let application: MemberRetirementApplication;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();

    // テスト用のメンバーを作成
    member = await MemberFactory.create({
      firstName: '太郎',
      lastName: '山田',
      memberNumber: 10001,
    });

    // テスト用の未処理退会申請を作成
    application = await client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-15'),
        approvedAt: null,
        rejectedAt: null,
      },
    });
  });

  it('未処理の退会申請を却下できる', async () => {
    const request = create(RejectMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: application.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.rejectMemberRetirementApplication(request, { headers });

    expect(response).toBeDefined();

    // 却下後、退会申請が却下済みになっていることを確認
    const updatedApplication = await client.memberRetirementApplication.findUnique({
      where: { memberRetirementApplicationId: application.memberRetirementApplicationId },
    });

    expect(updatedApplication).toBeDefined();
    expect(updatedApplication?.rejectedAt).not.toBeNull();
    expect(updatedApplication?.approvedAt).toBeNull();
  });

  it('既に承認済みの退会申請は却下できない', async () => {
    // 承認済みの退会申請を作成
    const approvedApplication = await client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-20'),
        approvedAt: new Date('2024-01-25'),
        rejectedAt: null,
      },
    });

    const request = create(RejectMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: approvedApplication.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.rejectMemberRetirementApplication(request, { headers })).rejects.toThrow();
  });

  it('既に拒否済みの退会申請は却下できない', async () => {
    // 拒否済みの退会申請を作成
    const rejectedApplication = await client.memberRetirementApplication.create({
      data: {
        memberId: member.memberId,
        applicationDate: new Date('2024-01-30'),
        approvedAt: null,
        rejectedAt: new Date('2024-02-05'),
      },
    });

    const request = create(RejectMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: rejectedApplication.memberRetirementApplicationId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.rejectMemberRetirementApplication(request, { headers })).rejects.toThrow();
  });

  it('存在しない退会申請IDの場合はエラーになる', async () => {
    const request = create(RejectMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: 99999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.rejectMemberRetirementApplication(request, { headers })).rejects.toThrow();
  });

  it('無効な退会申請IDの場合はエラーになる', async () => {
    const request = create(RejectMemberRetirementApplicationRequestSchema, {
      memberRetirementApplicationId: -1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.rejectMemberRetirementApplication(request, { headers })).rejects.toThrow();
  });
});
