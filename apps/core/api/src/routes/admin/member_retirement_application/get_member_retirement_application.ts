import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  GetMemberRetirementApplicationResponseSchema,
  MemberRetirementApplicationSchema 
} from '@hami/core-admin-api-schema/member_retirement_application_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { getMemberRetirementApplication } from '@core-api/repositories/member_retirement_application_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

const GetMemberRetirementApplicationRequestValidationSchema = z.object({
  memberRetirementApplicationId: z.number(),
});

export const getMemberRetirementApplicationHandler = createHandler({
  schema: GetMemberRetirementApplicationRequestValidationSchema,
  business: ({ memberRetirementApplicationId }) => 
    ResultAsync.fromPromise(
      getMemberRetirementApplication(memberRetirementApplicationId),
      (_error) => new DatabaseError('Failed to get member retirement application')
    ).andThen((result) => 
      result.mapErr((error) => {
        if (error.type === 'NOT_FOUND') {
          return new ConnectError('Member retirement application not found', Code.NotFound);
        }
        return new DatabaseError('Failed to get member retirement application');
      })
    ),
  toResponse: (application) => {
    return create(GetMemberRetirementApplicationResponseSchema, {
      application: create(MemberRetirementApplicationSchema, {
        memberRetirementApplicationId: application.memberRetirementApplicationId,
        memberId: application.memberId,
        memberNumber: application.member.memberNumber,
        memberName: `${application.member.lastName} ${application.member.firstName}`,
        applicationDate: application.applicationDate.toISOString(),
        approvedAt: application.approvedAt?.toISOString() ?? '',
        rejectedAt: application.rejectedAt?.toISOString() ?? '',
      }),
    });
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .with(P._, () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 