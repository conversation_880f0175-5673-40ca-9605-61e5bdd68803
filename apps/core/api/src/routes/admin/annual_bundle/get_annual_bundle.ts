import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetAnnualBundleResponseSchema, AnnualBundleDetailSchema, AnnualBundleHorseRelationSchema, AnnualBundlePublishStatus as ProtoPublish, AnnualBundleRecruitmentStatus as ProtoRecruitment } from '@hami/core-admin-api-schema/annual_bundle_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { findAnnualBundleById, AnnualBundleNotFoundError } from '@core-api/repositories/annual_bundle_repository';
import { createHandler } from '@core-api/utils/handler_factory';

export const getAnnualBundleHandler = createHandler({
  schema: z.object({
    annualBundleId: z.number().int().positive(),
  }),
  business: ({ annualBundleId }) => findAnnualBundleById({ annualBundleId }),
  toResponse: (bundle) =>
    create(GetAnnualBundleResponseSchema, {
      bundle: create(AnnualBundleDetailSchema, {
        annualBundleId: bundle.annualBundleId,
        fiscalYear: bundle.fiscalYear,
        name: bundle.name,
        shares: bundle.shares,
        publishStatus: bundle.publishStatus === 'PUBLIC' ? ProtoPublish.AB_PUBLIC : ProtoPublish.AB_PRIVATE,
        recruitmentStatus:
          bundle.recruitmentStatus === 'UPCOMING'
            ? ProtoRecruitment.AB_UPCOMING
            : bundle.recruitmentStatus === 'ACTIVE'
            ? ProtoRecruitment.AB_ACTIVE
            : bundle.recruitmentStatus === 'CLOSED'
            ? ProtoRecruitment.AB_CLOSED
            : ProtoRecruitment.AB_FULL,
        horses: bundle.horses.map((h) =>
          create(
            AnnualBundleHorseRelationSchema,
            {
              horseId: h.horseId,
              horseName: h.horse.horseName,
              recruitmentName: h.horse.recruitmentName,
              recruitmentNo: h.horse.recruitmentNo,
              sharesTotal: h.horse.sharesTotal,
            }
          )
        ),
        createdAt: { seconds: BigInt(Math.floor(bundle.createdAt.getTime() / 1000)) },
        updatedAt: { seconds: BigInt(Math.floor(bundle.updatedAt.getTime() / 1000)) },
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(AnnualBundleNotFoundError), () => new ConnectError('Annual bundle not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});


