import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ListAnnualBundleHorsesCandidatesResponseSchema, AnnualBundleHorseCandidateSchema } from '@hami/core-admin-api-schema/annual_bundle_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listAnnualBundleHorsesCandidates, AnnualBundleNotFoundError } from '@core-api/repositories/annual_bundle_repository';
import { createHandler } from '@core-api/utils/handler_factory';

export const listAnnualBundleHorsesCandidatesHandler = createHandler({
  schema: z.object({
    annualBundleId: z.number().int().positive(),
    page: z.number().int().positive().default(1),
    limit: z.number().int().positive().max(100).default(20),
    search: z.string().optional(),
  }),
  business: (params) => listAnnualBundleHorsesCandidates(params),
  toResponse: ({ horses, totalCount, totalPages, selectedSet }) =>
    create(ListAnnualBundleHorsesCandidatesResponseSchema, {
      horses: horses.map((h) =>
        create(AnnualBundleHorseCandidateSchema, {
          horseId: h.horseId,
          horseName: h.horseName,
          recruitmentName: h.recruitmentName,
          recruitmentYear: h.recruitmentYear,
          recruitmentNo: h.recruitmentNo,
          sharesTotal: h.sharesTotal,
          selected: selectedSet.has(h.horseId),
        })
      ),
      totalCount,
      page: 1,
      limit: horses.length,
      totalPages,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(AnnualBundleNotFoundError), () => new ConnectError('Annual bundle not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});


