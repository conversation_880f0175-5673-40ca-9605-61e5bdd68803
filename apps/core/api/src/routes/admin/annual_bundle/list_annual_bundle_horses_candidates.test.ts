import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { AnnualBundleFactory } from '@core-test/factories/annual_bundle_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { getClient } from '@core-test/index';
import { AnnualBundleService, ListAnnualBundleHorsesCandidatesRequestSchema } from '@hami/core-admin-api-schema/annual_bundle_service_pb';


describe('listAnnualBundleHorsesCandidates API', () => {
  const api = getClient(AnnualBundleService);
  let session: Awaited<ReturnType<typeof AdminUserSessionFactory.create>>;

  beforeEach(async () => {
    session = await AdminUserSessionFactory.create();
  });

  it('同年度の馬のみが返り、選択状態が付く', async () => {
    const b = await AnnualBundleFactory.create({ fiscalYear: 2035 });
    const h1 = await HorseFactory.create({ recruitmentYear: 2035, recruitmentNo: 1, horseName: 'A' });
    const h2 = await HorseFactory.create({ recruitmentYear: 2035, recruitmentNo: 2, horseName: 'B' });
    await HorseFactory.create({ recruitmentYear: 2036, recruitmentNo: 1, horseName: 'C' });

    const req = create(ListAnnualBundleHorsesCandidatesRequestSchema, { annualBundleId: b.annualBundleId, page: 1, limit: 50 });
    const headers = new Headers({ sessionToken: session.sessionToken });
    const res = await api.listAnnualBundleHorsesCandidates(req, { headers });
    const ids = res.horses.map((h) => h.horseId);
    expect(ids).toContain(h1.horseId);
    expect(ids).toContain(h2.horseId);
  });
});


