import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { AnnualBundleFactory } from '@core-test/factories/annual_bundle_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { AnnualBundleService, UpdateAnnualBundleRequestSchema, GetAnnualBundleRequestSchema, AnnualBundlePublishStatus } from '@hami/core-admin-api-schema/annual_bundle_service_pb';

describe('updateAnnualBundle API', () => {
    let session: Awaited<ReturnType<typeof AdminUserSessionFactory.create>>;
    
    beforeEach(async () => {
        session = await AdminUserSessionFactory.create();
    });
    
    it('名称・口数・公開ステータス・紐付け馬を更新できる', async ({ getClient }) => {
        const api = getClient(AnnualBundleService);
        const b = await AnnualBundleFactory.create({ fiscalYear: 2060, name: 'before', shares: 100 });
        const h1 = await HorseFactory.create({ recruitmentYear: 2060, recruitmentNo: 10 });
        const h2 = await HorseFactory.create({ recruitmentYear: 2060, recruitmentNo: 11 });

        const headers = new Headers({ sessionToken: session.sessionToken });
        await api.updateAnnualBundle(create(UpdateAnnualBundleRequestSchema, {
        annualBundleId: b.annualBundleId,
        name: 'after',
        shares: 200,
        publishStatus: AnnualBundlePublishStatus.AB_PUBLIC,
        horseIds: [h1.horseId, h2.horseId],
        }), { headers });

        const res = await api.getAnnualBundle(create(GetAnnualBundleRequestSchema, { annualBundleId: b.annualBundleId }), { headers });
        expect(res.bundle?.name).toBe('after');
        expect(res.bundle?.shares).toBe(200);
        expect(res.bundle?.horses.map(h => h.horseId).sort()).toEqual([h1.horseId, h2.horseId].sort());
    });
});
