import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import {
  AnnualBundleService,
  CreateAnnualBundleRequestSchema,
  GetAnnualBundleRequestSchema,
  AnnualBundlePublishStatus,
  AnnualBundleRecruitmentStatus,
} from '@hami/core-admin-api-schema/annual_bundle_service_pb';

describe('createAnnualBundle API', () => {
  let session: Awaited<ReturnType<typeof AdminUserSessionFactory.create>>;

  beforeEach(async () => {
    session = await AdminUserSessionFactory.create();
  });

  it('年度バンドルを作成できる', async ({ getClient }) => {
    const api = getClient(AnnualBundleService);
    const req = create(CreateAnnualBundleRequestSchema, {
      fiscalYear: 2033,
      name: '作成テスト',
      shares: 777,
      publishStatus: AnnualBundlePublishStatus.AB_PRIVATE,
      recruitmentStatus: AnnualBundleRecruitmentStatus.AB_UPCOMING,
      horseIds: [],
    });
    const headers = new Headers({ sessionToken: session.sessionToken });

    const res = await api.createAnnualBundle(req, { headers });

    const getRes = await api.getAnnualBundle(create(GetAnnualBundleRequestSchema, { annualBundleId: res.annualBundleId }), { headers });
    expect(getRes.bundle?.fiscalYear).toBe(2033);
    expect(getRes.bundle?.name).toBe('作成テスト');
    expect(getRes.bundle?.shares).toBe(777);
  });
});


