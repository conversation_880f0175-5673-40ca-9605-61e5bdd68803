import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { AnnualBundleFactory } from '@core-test/factories/annual_bundle_factory';
import { AnnualBundleService, ListAnnualBundlesRequestSchema } from '@hami/core-admin-api-schema/annual_bundle_service_pb';

describe('listAnnualBundles API', () => {
    let session: Awaited<ReturnType<typeof AdminUserSessionFactory.create>>;
    
    beforeEach(async () => {
        session = await AdminUserSessionFactory.create();
    });
    
    it('一覧を取得でき、年度フィルタが効く', async ({ getClient }) => {
      const api = getClient(AnnualBundleService);
        await AnnualBundleFactory.create({ fiscalYear: 2050 });
        await AnnualBundleFactory.create({ fiscalYear: 2051 });

        const headers = new Headers({ sessionToken: session.sessionToken });
        const res = await api.listAnnualBundles(create(ListAnnualBundlesRequestSchema, { page: 1, limit: 20, fiscalYear: 2050 }), { headers });
        expect(res.bundles.every((b) => b.fiscalYear === 2050)).toBe(true);
    });
});


