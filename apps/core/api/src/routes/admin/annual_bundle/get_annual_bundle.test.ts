import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { AnnualBundleFactory } from '@core-test/factories/annual_bundle_factory';
import { HorseFactory } from '@core-test/factories/horse_factory';
import { AnnualBundleService, GetAnnualBundleRequestSchema } from '@hami/core-admin-api-schema/annual_bundle_service_pb';

describe('getAnnualBundle API', () => {
    let session: Awaited<ReturnType<typeof AdminUserSessionFactory.create>>;
    
    beforeEach(async () => {
        session = await AdminUserSessionFactory.create();
    });
    
    it('詳細を取得でき、紐付け馬が募集番号順で返る', async ({ getClient }) => {
        const api = getClient(AnnualBundleService);
        const bundle = await AnnualBundleFactory.create({ fiscalYear: 2041 });
        const h1 = await HorseFactory.create({ recruitmentYear: 2041, recruitmentNo: 2, horseName: 'B' });
        const h2 = await HorseFactory.create({ recruitmentYear: 2041, recruitmentNo: 1, horseName: 'A' });
        await vPrisma.client.annualBundleHorse.createMany({ data: [
        { annualBundleId: bundle.annualBundleId, horseId: h1.horseId },
        { annualBundleId: bundle.annualBundleId, horseId: h2.horseId },
        ]});

        const req = create(GetAnnualBundleRequestSchema, { annualBundleId: bundle.annualBundleId });
        const headers = new Headers({ sessionToken: session.sessionToken });
        const res = await api.getAnnualBundle(req, { headers });
        expect(res.bundle?.horses[0]?.recruitmentNo).toBe(1);
        expect(res.bundle?.horses[1]?.recruitmentNo).toBe(2);
    });
});


