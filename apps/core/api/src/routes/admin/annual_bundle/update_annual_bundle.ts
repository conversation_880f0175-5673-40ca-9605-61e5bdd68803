import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { UpdateAnnualBundleResponseSchema, AnnualBundlePublishStatus as ProtoPublish, AnnualBundleRecruitmentStatus as ProtoRecruitment } from '@hami/core-admin-api-schema/annual_bundle_service_pb';
import { AnnualBundlePublishStatus as PrismaPublish, AnnualBundleRecruitmentStatus as PrismaRecruitment } from '@hami/prisma';
import { DatabaseError } from '@core-api/repositories';
import { updateAnnualBundle, AnnualBundleNotFoundError } from '@core-api/repositories/annual_bundle_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const updateAnnualBundleHandler = createHandler({
  schema: z.object({
    annualBundleId: z.number().int().positive(),
    name: z.string().min(1).optional(),
    shares: z.number().int().positive().optional(),
    publishStatus: z.nativeEnum(ProtoPublish).optional(),
    recruitmentStatus: z.nativeEnum(ProtoRecruitment).optional(),
    horseIds: z.array(z.number().int().positive()).optional(),
  }),
  business: (data) =>
    updateAnnualBundle({
      annualBundleId: data.annualBundleId,
      name: data.name,
      shares: data.shares,
      publishStatus:
        data.publishStatus === undefined
          ? null
          : data.publishStatus === ProtoPublish.AB_PUBLIC
          ? PrismaPublish.PUBLIC
          : PrismaPublish.PRIVATE,
      recruitmentStatus:
        data.recruitmentStatus === undefined
          ? null
          : data.recruitmentStatus === ProtoRecruitment.AB_UPCOMING
          ? PrismaRecruitment.UPCOMING
          : data.recruitmentStatus === ProtoRecruitment.AB_ACTIVE
          ? PrismaRecruitment.ACTIVE
          : data.recruitmentStatus === ProtoRecruitment.AB_CLOSED
          ? PrismaRecruitment.CLOSED
          : PrismaRecruitment.FULL,
      horseIds: data.horseIds,
    }),
  toResponse: () => create(UpdateAnnualBundleResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(AnnualBundleNotFoundError), () => new ConnectError('Annual bundle not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});


