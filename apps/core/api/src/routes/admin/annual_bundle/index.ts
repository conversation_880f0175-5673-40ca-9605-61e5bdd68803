import { AnnualBundleService } from '@hami/core-admin-api-schema/annual_bundle_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { listAnnualBundlesHandler } from './list_annual_bundles';
import { getAnnualBundleHandler } from './get_annual_bundle';
import { createAnnualBundleHandler } from './create_annual_bundle';
import { updateAnnualBundleHandler } from './update_annual_bundle';
import { listAnnualBundleHorsesCandidatesHandler } from './list_annual_bundle_horses_candidates';

import type { ConnectRouter } from '@connectrpc/connect';

export const implAnnualBundleService = (router: ConnectRouter) =>
  router.service(
    AnnualBundleService,
    {
      listAnnualBundles: unwrapResult(listAnnualBundlesHandler),
      getAnnualBundle: unwrapResult(getAnnualBundleHandler),
      createAnnualBundle: unwrapResult(createAnnualBundleHandler),
      updateAnnualBundle: unwrapResult(updateAnnualBundleHandler),
      listAnnualBundleHorsesCandidates: unwrapResult(listAnnualBundleHorsesCandidatesHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  );


