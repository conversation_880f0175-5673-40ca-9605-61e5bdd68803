import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { CreateAnnualBundleResponseSchema, AnnualBundlePublishStatus as ProtoPublish, AnnualBundleRecruitmentStatus as ProtoRecruitment } from '@hami/core-admin-api-schema/annual_bundle_service_pb';
import { AnnualBundlePublishStatus as PrismaPublish, AnnualBundleRecruitmentStatus as PrismaRecruitment } from '@hami/prisma';
import { DatabaseError } from '@core-api/repositories';
import { createAnnualBundle } from '@core-api/repositories/annual_bundle_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const createAnnualBundleHandler = createHandler({
  schema: z.object({
    fiscalYear: z.number().int().positive(),
    name: z.string().min(1),
    shares: z.number().int().positive(),
    publishStatus: z.nativeEnum(ProtoPublish),
    recruitmentStatus: z.nativeEnum(ProtoRecruitment),
    horseIds: z.array(z.number().int().positive()).default([]),
  }),
  business: (data) =>
    createAnnualBundle({
      fiscalYear: data.fiscalYear,
      name: data.name,
      shares: data.shares,
      publishStatus: data.publishStatus === ProtoPublish.AB_PUBLIC ? PrismaPublish.PUBLIC : PrismaPublish.PRIVATE,
      recruitmentStatus:
        data.recruitmentStatus === ProtoRecruitment.AB_UPCOMING
          ? PrismaRecruitment.UPCOMING
          : data.recruitmentStatus === ProtoRecruitment.AB_ACTIVE
          ? PrismaRecruitment.ACTIVE
          : data.recruitmentStatus === ProtoRecruitment.AB_CLOSED
          ? PrismaRecruitment.CLOSED
          : PrismaRecruitment.FULL,
      horseIds: data.horseIds,
    }),
  toResponse: (bundle) => create(CreateAnnualBundleResponseSchema, { annualBundleId: bundle.annualBundleId }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});


