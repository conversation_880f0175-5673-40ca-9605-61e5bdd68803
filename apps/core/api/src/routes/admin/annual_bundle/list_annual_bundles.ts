import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ListAnnualBundlesResponseSchema, AnnualBundleListItemSchema, AnnualBundlePublishStatus as ProtoPublish, AnnualBundleRecruitmentStatus as ProtoRecruitment } from '@hami/core-admin-api-schema/annual_bundle_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listAnnualBundles } from '@core-api/repositories/annual_bundle_repository';
import { createHandler } from '@core-api/utils/handler_factory';

export const listAnnualBundlesHandler = createHandler({
  schema: z.object({
    page: z.number().int().positive().default(1),
    limit: z.number().int().positive().max(100).default(20),
    fiscalYear: z.number().int().optional(),
  }),
  business: (params) => listAnnualBundles(params).map((result) => ({ ...result, requestParams: params })),
  toResponse: ({ bundles, totalCount, totalPages, requestParams }) =>
    create(ListAnnualBundlesResponseSchema, {
      bundles: bundles.map(b =>
        create(AnnualBundleListItemSchema, {
          annualBundleId: b.annualBundleId,
          fiscalYear: b.fiscalYear,
          name: b.name,
          shares: b.shares,
          publishStatus: b.publishStatus === 'PUBLIC' ? ProtoPublish.AB_PUBLIC : ProtoPublish.AB_PRIVATE,
          recruitmentStatus:
            b.recruitmentStatus === 'UPCOMING'
              ? ProtoRecruitment.AB_UPCOMING
              : b.recruitmentStatus === 'ACTIVE'
              ? ProtoRecruitment.AB_ACTIVE
              : b.recruitmentStatus === 'CLOSED'
              ? ProtoRecruitment.AB_CLOSED
              : ProtoRecruitment.AB_FULL,
        })
      ),
      totalCount,
      page: requestParams.page,
      limit: requestParams.limit,
      totalPages,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});


