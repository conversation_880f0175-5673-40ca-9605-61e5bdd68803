import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { CreateHorseResponseSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { createHorse, HorseAlreadyExistsError } from '@core-api/repositories/horse_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const createHorseHandler = createHandler({
  schema: z.object({
    recruitmentYear: z.number().int().positive(),
    recruitmentNo: z.number().int().positive(),
    recruitmentName: z.string().min(1),
    horseName: z.string(),
    birthYear: z.number().int().positive(),
    birthMonth: z.number().int().min(1).max(12),
    birthDay: z.number().int().min(1).max(31),
    sharesTotal: z.number().int().positive(),
    amountTotal: z.number().int().positive(),
    note: z.string().default(''),
    fundStartYear: z.number().int().positive(),
    fundStartMonth: z.number().int().min(1).max(12),
    fundStartDay: z.number().int().min(1).max(31),
    conflictOfInterest: z.boolean().default(false),
  }),
  business: (data) => createHorse({
    ...data,
  }),
  toResponse: () => create(CreateHorseResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseAlreadyExistsError), () => new ConnectError('Horse already exists with this recruitment year and number', Code.AlreadyExists))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 