import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { DeleteHorseResponseSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import { DatabaseError } from '@core-api/repositories';
import { deleteHorse, HorseNotFoundError } from '@core-api/repositories/horse_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const deleteHorseHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
  }),
  business: ({ horseId }) => deleteHorse({ horseId }),
  toResponse: () => create(DeleteHorseResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseNotFoundError), () => new ConnectError('Horse not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
