import { HorseGender as ProtoHorseGender } from '@hami/core-admin-api-schema/common_enums_pb';
import { RecruitmentStatus as ProtoRecruitmentStatus, PublishStatus as ProtoPublishStatus } from '@hami/core-admin-api-schema/horse_service_pb';
import { HorseGender as PrismaHorseGender, PublishStatus as PrismaPublishStatus, RecruitmentStatus as PrismaRecruitmentStatus } from '@hami/prisma';

// Prisma -> Protobuf 変換
export function convertPrismaGenderToProtoGender(prismaGender: PrismaHorseGender | null | undefined): ProtoHorseGender | undefined {
  if (!prismaGender) return undefined;
  
  switch (prismaGender) {
    case PrismaHorseGender.STALLION:
      return ProtoHorseGender.STALLION;
    case PrismaHorseGender.MARE:
      return ProtoHorseGender.MARE;
    case PrismaHorseGender.GELDING:
      return ProtoHorseGender.GELDING;
    default:
      return undefined;
  }
}

// Protobuf -> Prisma 変換
export function convertProtoGenderToPrismaGender(protoGender: ProtoHorseGender | number | undefined): PrismaHorseGender | null {
  if (protoGender === undefined) return null;
  
  // 数値で渡された場合の処理
  const genderValue = typeof protoGender === 'number' ? protoGender : protoGender;
  
  switch (genderValue) {
    case ProtoHorseGender.STALLION:
      return PrismaHorseGender.STALLION;
    case ProtoHorseGender.MARE:
      return PrismaHorseGender.MARE;
    case ProtoHorseGender.GELDING:
      return PrismaHorseGender.GELDING;
    default:
      return null;
  }
}

export function convertPrismaRecruitmentStatusToProtoRecruitmentStatus(prismaStatus: PrismaRecruitmentStatus | null | undefined): ProtoRecruitmentStatus | undefined {
  if (!prismaStatus) return undefined;
  
  switch (prismaStatus) {
    case PrismaRecruitmentStatus.UPCOMING:
      return ProtoRecruitmentStatus.UPCOMING;
    case PrismaRecruitmentStatus.ACTIVE:
      return ProtoRecruitmentStatus.ACTIVE;
    case PrismaRecruitmentStatus.FULL:
      return ProtoRecruitmentStatus.FULL;
    case PrismaRecruitmentStatus.CLOSED:
      return ProtoRecruitmentStatus.CLOSED;
    default:
      return undefined;
  }
}

export function convertProtoRecruitmentStatusToPrismaRecruitmentStatus(protoStatus: ProtoRecruitmentStatus | number | undefined): PrismaRecruitmentStatus | null {
  if (protoStatus === undefined) return null;
  
  // 数値で渡された場合の処理
  const statusValue = typeof protoStatus === 'number' ? protoStatus : protoStatus;
  
  switch (statusValue) {
    case ProtoRecruitmentStatus.UPCOMING:
      return PrismaRecruitmentStatus.UPCOMING;
    case ProtoRecruitmentStatus.ACTIVE:
      return PrismaRecruitmentStatus.ACTIVE;
    case ProtoRecruitmentStatus.FULL:
      return PrismaRecruitmentStatus.FULL;
    case ProtoRecruitmentStatus.CLOSED:
      return PrismaRecruitmentStatus.CLOSED;
    default:
      return null;
  }
}

export function convertPrismaPublishStatusToProtoPublishStatus(prismaStatus: PrismaPublishStatus | null | undefined): ProtoPublishStatus | undefined {
  if (!prismaStatus) return undefined;
  
  switch (prismaStatus) {
    case PrismaPublishStatus.DRAFT:
      return ProtoPublishStatus.DRAFT;
    case PrismaPublishStatus.PUBLISHED:
      return ProtoPublishStatus.PUBLISHED;
    case PrismaPublishStatus.ARCHIVED:
      return ProtoPublishStatus.ARCHIVED;
    default:
      return undefined;
  }
}

export function convertProtoPublishStatusToPrismaPublishStatus(protoStatus: ProtoPublishStatus | number | undefined): PrismaPublishStatus | null {
  if (protoStatus === undefined) return null;
  
  // 数値で渡された場合の処理
  const statusValue = typeof protoStatus === 'number' ? protoStatus : protoStatus;
  
  switch (statusValue) {
    case ProtoPublishStatus.DRAFT:
      return PrismaPublishStatus.DRAFT;
    case ProtoPublishStatus.PUBLISHED:
      return PrismaPublishStatus.PUBLISHED;
    case ProtoPublishStatus.ARCHIVED:
      return PrismaPublishStatus.ARCHIVED;
    default:
      return null;
  }
}
