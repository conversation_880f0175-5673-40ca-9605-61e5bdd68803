import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';

import { DatabaseError } from '@core-api/repositories';
import { checkHorseExists } from '@core-api/repositories/horse_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const checkHorseExistsHandler = createHandler({
  schema: z.object({
    recruitmentYear: z.number().int().min(1900).max(2100),
    recruitmentNo: z.number().int().positive(),
  }),
  business: ({ recruitmentYear, recruitmentNo }) => checkHorseExists({ recruitmentYear, recruitmentNo }),
  toResponse: (exists) => ({
    exists,
  }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
