import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory, HorseProfileFactory, getClient } from '@core-test/index';
import { HorseService, ListHorsesRequestSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import {
  HorseGender as PrismaHorseGender,
  PublishStatus as PrismaPublishStatus,
  RecruitmentStatus as PrismaRecruitmentStatus,
} from '@hami/prisma';
import type { AdminUserSession } from '@hami/prisma';

describe('listHorses API', () => {
  const apiClient = getClient(HorseService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
  });

  it('馬の一覧をページネーションで取得できる', async () => {
    // ===== Arrange =====
    const horse1 = await HorseFactory.create({
      recruitmentNo: 100,
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'テスト馬1の2025',
      horseName: 'テストホース1',
      note: 'テスト用のノート1',
    });

    const horse2 = await HorseFactory.create({
      recruitmentNo: 200,
      birthYear: 2021,
      birthMonth: 5,
      birthDay: 20,
      sharesTotal: 200,
      amountTotal: 2000000,
      recruitmentName: 'テスト馬2の2025',
      horseName: 'テストホース2',
      note: 'テスト用のノート2',
    });

    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      gender: PrismaHorseGender.STALLION,
      publishStatus: PrismaPublishStatus.PUBLISHED,
      recruitmentStatus: PrismaRecruitmentStatus.ACTIVE,
    });

    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse2.horseId } },
      gender: PrismaHorseGender.MARE,
      publishStatus: PrismaPublishStatus.DRAFT,
      recruitmentStatus: PrismaRecruitmentStatus.UPCOMING,
    });

    // ===== Act =====
    const request = create(ListHorsesRequestSchema, {
      page: 1,
      limit: 10,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorses(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.horses.length).toBeGreaterThanOrEqual(2);
    expect(response.totalCount).toBeGreaterThanOrEqual(2);
    expect(response.page).toBe(1);
    expect(response.limit).toBe(10);
    expect(response.totalPages).toBeGreaterThanOrEqual(1);

    // 作成した馬が含まれていることを確認
    const horseIds = response.horses.map((h: any) => h.horseId);
    expect(horseIds).toContain(horse1.horseId);
    expect(horseIds).toContain(horse2.horseId);
  });

  it('馬名で検索できる', async () => {
    // ===== Arrange =====
    const searchTerm = 'サーチテスト馬';
    const horse = await HorseFactory.create({
      recruitmentNo: 300,
      birthYear: 2020,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'サーチテストの2025',
      horseName: searchTerm,
      note: 'サーチテスト備考',
    });

    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
    });

    // ===== Act =====
    const request = create(ListHorsesRequestSchema, {
      page: 1,
      limit: 10,
      search: searchTerm,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorses(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.horses.length).toBeGreaterThan(0);

    // 検索結果に作成した馬が含まれていることを確認
    const foundHorse = response.horses.find((h: any) => h.horseId === horse.horseId);
    expect(foundHorse).toBeDefined();
    expect(foundHorse?.horseName).toBe(searchTerm);
  });

  it('募集名で検索できる', async () => {
    // ===== Arrange =====
    const searchTerm = 'サーチテスト募集';
    const horse = await HorseFactory.create({
      recruitmentNo: 400,
      birthYear: 2020,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: searchTerm,
      horseName: '募集サーチテスト馬',
      note: '募集サーチテスト備考',
    });

    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
    });

    // ===== Act =====
    const request = create(ListHorsesRequestSchema, {
      page: 1,
      limit: 10,
      search: searchTerm,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorses(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.horses.length).toBeGreaterThan(0);

    // 検索結果に作成した馬が含まれていることを確認
    const foundHorse = response.horses.find((h: any) => h.horseId === horse.horseId);
    expect(foundHorse).toBeDefined();
    expect(foundHorse?.recruitmentName).toBe(searchTerm);
  });

  it('生年でフィルタリングできる', async () => {
    // ===== Arrange =====
    const targetBirthYear = 2019;
    const horse = await HorseFactory.create({
      recruitmentNo: 500,
      birthYear: targetBirthYear,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: 'フィルタテスト募集',
      horseName: 'フィルタテスト馬',
      note: 'フィルタテスト備考',
    });

    // ===== Act =====
    const request = create(ListHorsesRequestSchema, {
      page: 1,
      limit: 10,
      birthYear: targetBirthYear,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorses(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();

    // すべての結果が指定した生年であることを確認
    response.horses.forEach((horse: any) => {
      expect(horse.birthYear).toBe(targetBirthYear);
    });

    // 作成した馬が含まれていることを確認
    const foundHorse = response.horses.find((h: any) => h.horseId === horse.horseId);
    expect(foundHorse).toBeDefined();
  });

  it('論理削除された馬は結果に含まれない', async () => {
    // ===== Arrange =====
    const deletedHorse = await HorseFactory.create({
      recruitmentNo: 600,
      birthYear: 2020,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 100,
      amountTotal: 1000000,
      recruitmentName: '削除済みテスト',
      horseName: '削除済みテスト馬',
      note: '削除済みテスト備考',
      deletedAt: new Date(),
    });

    // ===== Act =====
    const request = create(ListHorsesRequestSchema, {
      page: 1,
      limit: 10,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorses(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();

    // 削除された馬は結果に含まれていないことを確認
    const horseIds = response.horses.map((h: any) => h.horseId);
    expect(horseIds).not.toContain(deletedHorse.horseId);
  });

  it('無効なページ番号でバリデーションエラーになる', async () => {
    // ===== Act =====
    const request = create(ListHorsesRequestSchema, {
      page: 0,
      limit: 10,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.listHorses(request, { headers })).rejects.toThrow();
  });

  it('無効なlimit値でバリデーションエラーになる', async () => {
    // ===== Act =====
    const request = create(ListHorsesRequestSchema, {
      page: 1,
      limit: 0,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.listHorses(request, { headers })).rejects.toThrow();
  });
});
