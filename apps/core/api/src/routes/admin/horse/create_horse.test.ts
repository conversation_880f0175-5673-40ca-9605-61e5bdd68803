import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory, getClient } from '@core-test/index';
import { HorseService, CreateHorseRequestSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import type { AdminUserSession } from '@hami/prisma';
import { createHorse } from '@core-api/repositories/horse_repository';

describe('createHorse API', () => {
  const apiClient = getClient(HorseService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    // テスト前にテストデータをクリーンアップ
    // 外部キー制約のため、先にHorseProfileを削除
    await vPrisma.client.horseProfile.deleteMany({
      where: {
        horse: {
          recruitmentYear: 2025,
        },
      },
    });

    // その後にHorseを削除
    await vPrisma.client.horse.deleteMany({
      where: {
        recruitmentYear: 2025,
      },
    });
  });

  it('リポジトリが正しく動作する', async () => {
    // リポジトリ層を直接テスト
    const horseData = {
      recruitmentYear: 2025,
      recruitmentNo: 1,
      recruitmentName: 'リポジトリテスト馬の2025',
      horseName: 'リポジトリテストホース',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: 'リポジトリテスト用のノート',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: false,
    };

    const result = await createHorse(horseData);

    if (result.isErr()) {
      throw new Error(`Repository failed: ${result.error.message}`);
    }

    expect(result.isOk()).toBe(true);

    const createdHorse = result.value;

    // データベースで確認
    const foundHorse = await vPrisma.client.horse.findUnique({
      where: { horseId: createdHorse.horseId },
      include: { profile: true },
    });

    expect(foundHorse).toBeDefined();
    expect(foundHorse?.horseName).toBe(horseData.horseName);
    expect(foundHorse?.profile).toBeDefined();
    expect(foundHorse?.fundStartYear).toBe(horseData.fundStartYear);
    expect(foundHorse?.fundStartMonth).toBe(horseData.fundStartMonth);
    expect(foundHorse?.fundStartDay).toBe(horseData.fundStartDay);
    expect(foundHorse?.conflictOfInterest).toBe(horseData.conflictOfInterest);
  });

  it('新しい馬を作成できる', async () => {
    // ===== Arrange =====
    const horseData = {
      recruitmentYear: 2025,
      recruitmentNo: 1001,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: 'テスト用のノート',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: true,
    };

    // ===== Act =====
    const request = create(CreateHorseRequestSchema, horseData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.createHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();

    // データベースで確認
    const allHorses = await vPrisma.client.horse.findMany({
      where: { recruitmentYear: 2025 },
      include: { profile: true },
    });

    expect(allHorses.length).toBeGreaterThan(0);

    const createdHorse = allHorses.find((h) => h.recruitmentNo === horseData.recruitmentNo);
    if (!createdHorse) {
      throw new Error(
        `Horse with recruitmentNo ${horseData.recruitmentNo} not found. Available horses: ${allHorses.map((h) => h.recruitmentNo).join(', ')}`
      );
    }

    expect(createdHorse).toBeDefined();
    expect(createdHorse?.horseName).toBe(horseData.horseName);
    expect(createdHorse?.profile).toBeDefined();
    expect(createdHorse?.fundStartYear).toBe(horseData.fundStartYear);
    expect(createdHorse?.fundStartMonth).toBe(horseData.fundStartMonth);
    expect(createdHorse?.fundStartDay).toBe(horseData.fundStartDay);
    expect(createdHorse?.conflictOfInterest).toBe(horseData.conflictOfInterest);
  });

  it('同じ募集年・番号で作成しようとした場合はエラーになる', async () => {
    // ===== Arrange =====
    // 既存の馬を作成
    await HorseFactory.create({
      recruitmentYear: 2025,
      recruitmentNo: 2000,
      recruitmentName: '既存馬の2025',
      horseName: '既存馬',
      birthYear: 2020,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '既存馬ノート',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: false,
    });

    const duplicateData = {
      recruitmentYear: 2025,
      recruitmentNo: 2000, // 同じ番号で重複させる
      recruitmentName: '重複テスト馬の2025',
      horseName: '重複テスト馬',
      birthYear: 2021,
      birthMonth: 2,
      birthDay: 2,
      sharesTotal: 200,
      amountTotal: 2000000,
      note: '重複テスト',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: true,
    };

    // ===== Act =====
    const request = create(CreateHorseRequestSchema, duplicateData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorse(request, { headers })).rejects.toThrow();
  });

  it('必須フィールドが空の場合はバリデーションエラーになる', async () => {
    // ===== Arrange =====
    const invalidData = {
      recruitmentYear: 2025,
      recruitmentNo: 3000,
      recruitmentName: '', // 空文字列
      horseName: 'テストホース',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: 'テスト用のノート',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: true,
    };

    // ===== Act =====
    const request = create(CreateHorseRequestSchema, invalidData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorse(request, { headers })).rejects.toThrow();
  });

  it('無効な日付でバリデーションエラーになる', async () => {
    // ===== Arrange =====
    const invalidData = {
      recruitmentYear: 2025,
      recruitmentNo: 4000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
      birthYear: 2020,
      birthMonth: 13, // 無効な月
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: 'テスト用のノート',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: true,
    };

    // ===== Act =====
    const request = create(CreateHorseRequestSchema, invalidData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorse(request, { headers })).rejects.toThrow();
  });

  it('負の値でバリデーションエラーになる', async () => {
    // ===== Arrange =====
    const invalidData = {
      recruitmentYear: 2025,
      recruitmentNo: 5000,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: -100, // 負の値
      amountTotal: 1000000,
      note: 'テスト用のノート',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: true,
    };

    // ===== Act =====
    const request = create(CreateHorseRequestSchema, invalidData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.createHorse(request, { headers })).rejects.toThrow();
  });

  it('デフォルト値が正しく設定される', async () => {
    // ===== Arrange =====
    const horseData = {
      recruitmentYear: 2025,
      recruitmentNo: 6000,
      recruitmentName: 'デフォルトテスト馬の2025',
      horseName: 'デフォルトテスト馬',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '', // 明示的に空文字列を指定
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
    };

    // ===== Act =====
    const request = create(CreateHorseRequestSchema, horseData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.createHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();

    // データベースでデフォルト値を確認
    const createdHorse = await vPrisma.client.horse.findFirst({
      where: {
        recruitmentYear: horseData.recruitmentYear,
        recruitmentNo: horseData.recruitmentNo,
      },
    });

    expect(createdHorse).toBeDefined();
    expect(createdHorse?.note).toBe(''); // デフォルト値
    expect(createdHorse?.conflictOfInterest).toBe(false); // デフォルト値
  });
});
