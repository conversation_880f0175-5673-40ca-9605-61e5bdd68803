import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory, HorseProfileFactory, getClient } from '@core-test/index';
import { HorseService, DeleteHorseRequestSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import type { AdminUserSession } from '@hami/prisma';

describe('deleteHorse API', () => {
  const apiClient = getClient(HorseService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
  });

  it('既存の馬を論理削除できる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: '削除テスト馬の2025',
      horseName: '削除テスト馬',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '削除テスト',
    });

    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
    });

    // ===== Act =====
    const request = create(DeleteHorseRequestSchema, {
      horseId: horse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.deleteHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();

    // データベースで論理削除されていることを確認
    const deletedHorse = await vPrisma.client.horse.findUnique({
      where: { horseId: horse.horseId },
    });

    expect(deletedHorse).toBeDefined();
    expect(deletedHorse?.deletedAt).not.toBeNull();
    expect(deletedHorse?.deletedAt).toBeInstanceOf(Date);
  });

  it('存在しない馬IDで削除しようとした場合はエラーになる', async () => {
    // ===== Act =====
    const request = create(DeleteHorseRequestSchema, {
      horseId: 99999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.deleteHorse(request, { headers })).rejects.toThrow();
  });

  it('既に論理削除された馬は削除できない', async () => {
    // ===== Arrange =====
    const alreadyDeletedHorse = await HorseFactory.create({
      recruitmentName: '既削除馬の2025',
      horseName: '既削除馬',
      birthYear: 2020,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '既削除テスト',
      deletedAt: new Date(),
    });

    // ===== Act =====
    const request = create(DeleteHorseRequestSchema, {
      horseId: alreadyDeletedHorse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.deleteHorse(request, { headers })).rejects.toThrow();
  });

  it('無効なhorseId（0）でバリデーションエラーになる', async () => {
    // ===== Act =====
    const request = create(DeleteHorseRequestSchema, {
      horseId: 0,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.deleteHorse(request, { headers })).rejects.toThrow();
  });

  it('無効なhorseId（負の数）でバリデーションエラーになる', async () => {
    // ===== Act =====
    const request = create(DeleteHorseRequestSchema, {
      horseId: -1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.deleteHorse(request, { headers })).rejects.toThrow();
  });

  it('削除後も他の一覧取得で表示されないことを確認', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: '非表示テスト馬の2025',
      horseName: '非表示テスト馬',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '非表示テスト',
    });

    // ===== Act =====
    const request = create(DeleteHorseRequestSchema, {
      horseId: horse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.deleteHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();

    // 一覧取得で表示されないことを確認
    const allHorses = await vPrisma.client.horse.findMany({
      where: {
        deletedAt: null,
      },
    });

    const horseIds = allHorses.map((h: any) => h.horseId);
    expect(horseIds).not.toContain(horse.horseId);
  });
});
