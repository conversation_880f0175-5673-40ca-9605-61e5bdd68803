import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetHorseResponseSchema, HorseDetailSchema } from '@hami/core-admin-api-schema/horse_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { findHorseById, HorseNotFoundError } from '@core-api/repositories/horse_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';
import {
  convertPrismaGenderToProtoGender,
  convertPrismaPublishStatusToProtoPublishStatus,
  convertPrismaRecruitmentStatusToProtoRecruitmentStatus,
} from './utils/convert_utils';

export const getHorseHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
  }),
  business: ({ horseId }) => findHorseById({ horseId }),
  toResponse: (horse) =>
    create(GetHorseResponseSchema, {
      horse: create(HorseDetailSchema, {
        horseId: horse.horseId,
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        recruitmentName: horse.recruitmentName,
        horseName: horse.horseName,
        birthYear: horse.birthYear,
        birthMonth: horse.birthMonth,
        birthDay: horse.birthDay,
        sharesTotal: horse.sharesTotal,
        amountTotal: horse.amountTotal,
        note: horse.note,
        fundStartYear: horse.fundStartYear,
        fundStartMonth: horse.fundStartMonth,
        fundStartDay: horse.fundStartDay,
        conflictOfInterest: horse.conflictOfInterest,
        gender: convertPrismaGenderToProtoGender(horse.profile?.gender ?? undefined),
        publishStatus: convertPrismaPublishStatusToProtoPublishStatus(horse.profile?.publishStatus ?? undefined),
        recruitmentStatus: convertPrismaRecruitmentStatusToProtoRecruitmentStatus(horse.profile?.recruitmentStatus ?? undefined),
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseNotFoundError), () => new ConnectError('Horse not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
