import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { InvestmentContractFactory } from '@core-test/factories/investment_contract_factory';
import { MailVerificationFactory } from '@core-test/factories/mail_verification_factory';
import { MemberFactory } from '@core-test/factories/member_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { UserFactory } from '@core-test/factories/user_factory';
import { HorseFactory, getClient } from '@core-test/index';
import { HorseService, ListHorseInvestorsRequestSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import type { AdminUserSession } from '@hami/prisma';

describe('listHorseInvestors API', () => {
  const apiClient = getClient(HorseService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
  });

  it('馬に出資している会員一覧を正しく集計・ソートして取得できる', async () => {
    // ===== Arrange =====
    const now = new Date();
    const retirementDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    // 馬A, 馬Bを作成
    const horseA = await HorseFactory.create({
      recruitmentName: '出資者リストテスト馬Aの2025',
      horseName: 'テストホースA',
      birthYear: 2022,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 1000,
      amountTotal: 50000000,
      note: '出資者リストテスト備考A',
      fundStartYear: 2024,
      fundStartMonth: 1,
      fundStartDay: 1,
    });

    const horseB = await HorseFactory.create({
      recruitmentName: '出資者リストテスト馬Bの2025',
      horseName: 'テストホースB',
      birthYear: 2023,
      birthMonth: 2,
      birthDay: 2,
      sharesTotal: 500,
      amountTotal: 25000000,
      note: '出資者リストテスト備考B',
      fundStartYear: 2024,
      fundStartMonth: 2,
      fundStartDay: 2,
    });

    // 会員1, 会員2, 会員3を作成
    const mailVerification1 = await MailVerificationFactory.create();
    const application1 = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification1.mailVerificationId } },
    });
    const user1 = await UserFactory.create();
    const member1 = await MemberFactory.create({
      user: { connect: { userId: user1.userId } },
      membershipApplication: { connect: { membershipApplicationId: application1.membershipApplicationId } },
      memberNumber: 1234567890,
      firstName: '一郎',
      lastName: '山田',
    });

    const mailVerification2 = await MailVerificationFactory.create();
    const application2 = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification2.mailVerificationId } },
    });
    const user2 = await UserFactory.create();
    const member2 = await MemberFactory.create({
      user: { connect: { userId: user2.userId } },
      membershipApplication: { connect: { membershipApplicationId: application2.membershipApplicationId } },
      memberNumber: 1234567891,
      firstName: '二郎',
      lastName: '鈴木',
    });

    const mailVerification3 = await MailVerificationFactory.create();
    const application3 = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification3.mailVerificationId } },
    });
    const user3 = await UserFactory.create();
    const member3 = await MemberFactory.create({
      user: { connect: { userId: user3.userId } },
      membershipApplication: { connect: { membershipApplicationId: application3.membershipApplicationId } },
      memberNumber: 1234567892,
      firstName: '三郎',
      lastName: '佐藤',
      retirementDate: retirementDate,
    });

    // 馬Aに対する出資契約（会員1が2件、会員2が1件、会員3が1件）
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horseA.horseId } },
      member: { connect: { memberId: member1.memberId } },
      sharesNumber: 5,
      investmentAmount: 100000,
    });

    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horseA.horseId } },
      member: { connect: { memberId: member1.memberId } },
      sharesNumber: 3,
      investmentAmount: 60000,
    });

    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horseA.horseId } },
      member: { connect: { memberId: member2.memberId } },
      sharesNumber: 4,
      investmentAmount: 80000,
    });

    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horseA.horseId } },
      member: { connect: { memberId: member3.memberId } },
      sharesNumber: 2,
      investmentAmount: 40000,
    });

    // 馬Bにも出資契約（馬Aの結果に混ざらないことを確認）
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horseB.horseId } },
      member: { connect: { memberId: member1.memberId } },
      sharesNumber: 1,
      investmentAmount: 20000,
    });

    // ===== Act =====
    const request = create(ListHorseInvestorsRequestSchema, {
      horseId: horseA.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseInvestors(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    const investors = response.investors;
    expect(investors).toHaveLength(3);

    // 金額降順でソートされることを確認
    expect(investors[0].memberName).toBe('山田 一郎');
    expect(investors[0].memberNumber).toBe(1234567890);
    expect(investors[0].sharesNumber).toBe(8); // 5 + 3
    expect(investors[0].investmentAmount).toBe(160000); // 100000 + 60000
    expect(investors[0].retirementDate).toBeUndefined();

    expect(investors[1].memberName).toBe('鈴木 二郎');
    expect(investors[1].memberNumber).toBe(1234567891);
    expect(investors[1].sharesNumber).toBe(4);
    expect(investors[1].investmentAmount).toBe(80000);
    expect(investors[1].retirementDate).toBeUndefined();

    expect(investors[2].memberName).toBe('佐藤 三郎');
    expect(investors[2].memberNumber).toBe(1234567892);
    expect(investors[2].sharesNumber).toBe(2);
    expect(investors[2].investmentAmount).toBe(40000);
    expect(investors[2].retirementDate).toBeDefined();
    expect(investors[2].retirementDate?.seconds).toBe(BigInt(Math.floor(retirementDate.getTime() / 1000)));
    expect(investors[2].retirementDate?.nanos).toBe(0);
  });

  it('同じ金額の場合はmemberId昇順でソートされる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: '同額ソートテスト馬の2025',
      horseName: 'テストホース',
      birthYear: 2022,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 1000,
      amountTotal: 50000000,
      note: '同額ソートテスト備考',
      fundStartYear: 2024,
      fundStartMonth: 1,
      fundStartDay: 1,
    });

    // 会員A（memberIdが小さい）
    const mailVerificationA = await MailVerificationFactory.create();
    const applicationA = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerificationA.mailVerificationId } },
    });
    const userA = await UserFactory.create();
    const memberA = await MemberFactory.create({
      user: { connect: { userId: userA.userId } },
      membershipApplication: { connect: { membershipApplicationId: applicationA.membershipApplicationId } },
      memberNumber: 1234567890,
      firstName: 'A子',
      lastName: '田中',
    });

    // 会員B（memberIdが大きい）
    const mailVerificationB = await MailVerificationFactory.create();
    const applicationB = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerificationB.mailVerificationId } },
    });
    const userB = await UserFactory.create();
    const memberB = await MemberFactory.create({
      user: { connect: { userId: userB.userId } },
      membershipApplication: { connect: { membershipApplicationId: applicationB.membershipApplicationId } },
      memberNumber: 1234567891,
      firstName: 'B子',
      lastName: '佐藤',
    });

    // 同じ金額の出資契約を作成
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: memberA.memberId } },
      sharesNumber: 5,
      investmentAmount: 100000,
    });

    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: memberB.memberId } },
      sharesNumber: 5,
      investmentAmount: 100000,
    });

    // ===== Act =====
    const request = create(ListHorseInvestorsRequestSchema, {
      horseId: horse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseInvestors(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    const investors = response.investors;
    expect(investors).toHaveLength(2);

    // memberIdの小さい順でソートされることを確認
    expect(investors[0].memberName).toBe('田中 A子');
    expect(investors[0].memberNumber).toBe(1234567890);
    expect(investors[1].memberName).toBe('佐藤 B子');
    expect(investors[1].memberNumber).toBe(1234567891);
  });

  it('会員名が正しく結合される', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: '名前結合テスト馬の2025',
      horseName: 'テストホース',
      birthYear: 2022,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 1000,
      amountTotal: 50000000,
      note: '名前結合テスト備考',
      fundStartYear: 2024,
      fundStartMonth: 1,
      fundStartDay: 1,
    });

    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
    });
    const user = await UserFactory.create();
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: { connect: { membershipApplicationId: application.membershipApplicationId } },
      memberNumber: 1234567890,
      firstName: '太郎',
      lastName: '山田',
    });

    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: member.memberId } },
      sharesNumber: 10,
      investmentAmount: 500000,
    });

    // ===== Act =====
    const request = create(ListHorseInvestorsRequestSchema, {
      horseId: horse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseInvestors(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    const investors = response.investors;
    expect(investors).toHaveLength(1);
    expect(investors[0].memberName).toBe('山田 太郎');
    expect(investors[0].memberNumber).toBe(1234567890);
  });

  it('出資者がいない馬の場合は空の配列を返す', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: '出資者なしテスト馬の2025',
      horseName: 'テストホース',
      birthYear: 2022,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 1000,
      amountTotal: 50000000,
      note: '出資者なしテスト備考',
      fundStartYear: 2024,
      fundStartMonth: 1,
      fundStartDay: 1,
    });

    // ===== Act =====
    const request = create(ListHorseInvestorsRequestSchema, {
      horseId: horse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseInvestors(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.investors).toEqual([]);
  });

  it('存在しない馬IDの場合は空の配列を返す', async () => {
    // ===== Act =====
    const request = create(ListHorseInvestorsRequestSchema, {
      horseId: 999999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.listHorseInvestors(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.investors).toEqual([]);
  });

  it('無効な馬IDの場合はエラーを返す', async () => {
    // ===== Act =====
    const request = create(ListHorseInvestorsRequestSchema, {
      horseId: -1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.listHorseInvestors(request, { headers })).rejects.toThrow();
  });

  it('馬IDが0の場合はエラーを返す', async () => {
    // ===== Act =====
    const request = create(ListHorseInvestorsRequestSchema, {
      horseId: 0,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.listHorseInvestors(request, { headers })).rejects.toThrow();
  });
});
