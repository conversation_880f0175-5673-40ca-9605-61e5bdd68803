import { create } from '@bufbuild/protobuf';
import { TimestampSchema } from '@bufbuild/protobuf/wkt';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ListHorseInvestorsResponseSchema, HorseInvestorSchema } from '@hami/core-admin-api-schema/horse_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listHorseInvestors } from '@core-api/repositories/horse_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const listHorseInvestorsHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
  }),
  business: ({ horseId }) => listHorseInvestors({ horseId }),
  toResponse: (investors) =>
    create(ListHorseInvestorsResponseSchema, {
      investors: investors.map((investor) =>
        create(HorseInvestorSchema, {
          memberId: investor.memberId,
          memberNumber: investor.memberNumber,
          memberName: investor.memberName,
          sharesNumber: investor.sharesNumber,
          investmentAmount: investor.investmentAmount,
          retirementDate: investor.retirementDate ? create(TimestampSchema, { seconds: BigInt(Math.floor(investor.retirementDate.getTime() / 1000)), nanos: (investor.retirementDate.getTime() % 1000) * 1e6 }) : undefined,
        })
      ),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
