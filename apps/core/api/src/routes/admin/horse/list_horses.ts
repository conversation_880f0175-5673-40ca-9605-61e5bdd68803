import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { ListHorsesResponseSchema, HorseListItemSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import type { Prisma } from '@hami/prisma';

import { DatabaseError } from '@core-api/repositories';
import { listHorses } from '@core-api/repositories/horse_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';
import {
  convertPrismaGenderToProtoGender,
  convertPrismaPublishStatusToProtoPublishStatus,
  convertPrismaRecruitmentStatusToProtoRecruitmentStatus,
} from './utils/convert_utils';

export const listHorsesHandler = createHandler({
  schema: z.object({
    page: z.number().int().positive().default(1),
    limit: z.number().int().positive().max(100).default(20),
    search: z.string().optional(),
    birthYear: z.number().int().optional(),
  }),
  business: (params) => {
    const { page, limit, search, birthYear } = params;
    return listHorses({ page, limit, search, birthYear }).map((result) => ({
      ...result,
      requestParams: params,
    }));
  },
  toResponse: ({ horses, totalCount, totalPages, requestParams }) =>
    create(ListHorsesResponseSchema, {
      horses: horses.map((horse: Prisma.HorseGetPayload<{ include: { profile: true } }>) =>
        create(HorseListItemSchema, {
          horseId: horse.horseId,
          recruitmentYear: horse.recruitmentYear,
          recruitmentNo: horse.recruitmentNo,
          recruitmentName: horse.recruitmentName,
          horseName: horse.horseName,
          birthYear: horse.birthYear,
          gender: convertPrismaGenderToProtoGender(horse.profile?.gender ?? undefined),
          publishStatus: convertPrismaPublishStatusToProtoPublishStatus(horse.profile?.publishStatus ?? undefined),
          recruitmentStatus: convertPrismaRecruitmentStatusToProtoRecruitmentStatus(horse.profile?.recruitmentStatus ?? undefined),
        })
      ),
      totalCount,
      page: requestParams.page,
      limit: requestParams.limit,
      totalPages,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
