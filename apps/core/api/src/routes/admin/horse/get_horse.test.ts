import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory, HorseProfileFactory, getClient } from '@core-test/index';
import { HorseGender } from '@hami/core-admin-api-schema/common_enums_pb';
import { HorseService, PublishStatus, RecruitmentStatus, GetHorseRequestSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import {
  HorseGender as PrismaHorseGender,
  PublishStatus as PrismaPublishStatus,
  RecruitmentStatus as PrismaRecruitmentStatus,
} from '@hami/prisma';
import type { AdminUserSession } from '@hami/prisma';

describe('getHorse API', () => {
  const apiClient = getClient(HorseService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
  });

  it('指定したIDの馬を取得できる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: 'ウインドインハーヘアの2025',
      horseName: 'ディープインパクト',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: 'テスト用のノート',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: true,
    });

    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      gender: PrismaHorseGender.STALLION,
      publishStatus: PrismaPublishStatus.PUBLISHED,
      recruitmentStatus: PrismaRecruitmentStatus.ACTIVE,
    });

    // ===== Act =====
    const request = create(GetHorseRequestSchema, {
      horseId: horse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.getHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.horse).toBeDefined();
    expect(response.horse?.horseId).toBe(horse.horseId);
    expect(response.horse?.recruitmentYear).toBe(horse.recruitmentYear);
    expect(response.horse?.recruitmentNo).toBe(horse.recruitmentNo);
    expect(response.horse?.recruitmentName).toBe('ウインドインハーヘアの2025');
    expect(response.horse?.horseName).toBe('ディープインパクト');
    expect(response.horse?.birthYear).toBe(2020);
    expect(response.horse?.birthMonth).toBe(4);
    expect(response.horse?.birthDay).toBe(15);
    expect(response.horse?.sharesTotal).toBe(100);
    expect(response.horse?.amountTotal).toBe(1000000);
    expect(response.horse?.note).toBe('テスト用のノート');
    expect(response.horse?.fundStartYear).toBe(2025);
    expect(response.horse?.fundStartMonth).toBe(4);
    expect(response.horse?.fundStartDay).toBe(15);
    expect(response.horse?.conflictOfInterest).toBe(true);
    expect(response.horse?.gender).toBe(HorseGender.STALLION);
    expect(response.horse?.publishStatus).toBe(PublishStatus.PUBLISHED);
    expect(response.horse?.recruitmentStatus).toBe(RecruitmentStatus.ACTIVE);
  });

  it('複数の馬がいる状態で指定したIDの馬のみを取得できる', async () => {
    // ===== Arrange =====
    // 複数の馬を作成
    const horse1 = await HorseFactory.create({
      recruitmentName: '第1馬の2025',
      horseName: 'テストホース1',
      birthYear: 2020,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '第1馬のノート',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: false,
    });

    const horse2 = await HorseFactory.create({
      recruitmentName: '第2馬の2025',
      horseName: 'テストホース2',
      birthYear: 2021,
      birthMonth: 2,
      birthDay: 2,
      sharesTotal: 200,
      amountTotal: 2000000,
      note: '第2馬のノート',
      fundStartYear: 2024,
      fundStartMonth: 3,
      fundStartDay: 14,
      conflictOfInterest: true,
    });

    const horse3 = await HorseFactory.create({
      recruitmentName: '第3馬の2025',
      horseName: 'テストホース3',
      birthYear: 2022,
      birthMonth: 3,
      birthDay: 3,
      sharesTotal: 300,
      amountTotal: 3000000,
      note: '第3馬のノート',
      fundStartYear: 2023,
      fundStartMonth: 2,
      fundStartDay: 13,
      conflictOfInterest: false,
    });

    // それぞれにプロフィールを作成
    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      gender: PrismaHorseGender.STALLION,
      publishStatus: PrismaPublishStatus.PUBLISHED,
      recruitmentStatus: PrismaRecruitmentStatus.ACTIVE,
    });

    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse2.horseId } },
      gender: PrismaHorseGender.MARE,
      publishStatus: PrismaPublishStatus.DRAFT,
      recruitmentStatus: PrismaRecruitmentStatus.UPCOMING,
    });

    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse3.horseId } },
      gender: PrismaHorseGender.GELDING,
      publishStatus: PrismaPublishStatus.PUBLISHED,
      recruitmentStatus: PrismaRecruitmentStatus.FULL,
    });

    // ===== Act =====
    // 第2馬を取得
    const request = create(GetHorseRequestSchema, {
      horseId: horse2.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.getHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.horse).toBeDefined();

    // 指定した馬のデータが正しく取得されていることを確認
    expect(response.horse?.horseId).toBe(horse2.horseId);
    expect(response.horse?.recruitmentName).toBe('第2馬の2025');
    expect(response.horse?.horseName).toBe('テストホース2');
    expect(response.horse?.birthYear).toBe(2021);
    expect(response.horse?.birthMonth).toBe(2);
    expect(response.horse?.birthDay).toBe(2);
    expect(response.horse?.sharesTotal).toBe(200);
    expect(response.horse?.amountTotal).toBe(2000000);
    expect(response.horse?.note).toBe('第2馬のノート');
    expect(response.horse?.fundStartYear).toBe(2024);
    expect(response.horse?.fundStartMonth).toBe(3);
    expect(response.horse?.fundStartDay).toBe(14);
    expect(response.horse?.gender).toBe(HorseGender.MARE);
    expect(response.horse?.publishStatus).toBe(PublishStatus.DRAFT);
    expect(response.horse?.recruitmentStatus).toBe(RecruitmentStatus.UPCOMING);
    expect(response.horse?.conflictOfInterest).toBe(true);

    // 他の馬のデータではないことを確認
    expect(response.horse?.horseId).not.toBe(horse1.horseId);
    expect(response.horse?.horseId).not.toBe(horse3.horseId);
    expect(response.horse?.horseName).not.toBe('テストホース1');
    expect(response.horse?.horseName).not.toBe('テストホース3');
  });

  it('プロフィールがない馬でも取得できる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
      birthYear: 2021,
      birthMonth: 5,
      birthDay: 20,
      sharesTotal: 200,
      amountTotal: 2000000,
      note: '',
      fundStartYear: 2025,
      fundStartMonth: 4,
      fundStartDay: 15,
      conflictOfInterest: false,
    });

    // ===== Act =====
    const request = create(GetHorseRequestSchema, {
      horseId: horse.horseId,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.getHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.horse).toBeDefined();
    expect(response.horse?.horseId).toBe(horse.horseId);
    expect(response.horse?.recruitmentYear).toBe(horse.recruitmentYear);
    expect(response.horse?.recruitmentNo).toBe(horse.recruitmentNo);
    expect(response.horse?.recruitmentName).toBe('テスト馬の2025');
    expect(response.horse?.horseName).toBe('テストホース');
    expect(response.horse?.birthYear).toBe(2021);
    expect(response.horse?.birthMonth).toBe(5);
    expect(response.horse?.birthDay).toBe(20);
    expect(response.horse?.sharesTotal).toBe(200);
    expect(response.horse?.amountTotal).toBe(2000000);
    expect(response.horse?.note).toBe('');
    expect(response.horse?.fundStartYear).toBe(2025);
    expect(response.horse?.fundStartMonth).toBe(4);
    expect(response.horse?.fundStartDay).toBe(15);
    expect(response.horse?.gender).toBe(undefined);
    expect(response.horse?.publishStatus).toBe(undefined);
    expect(response.horse?.recruitmentStatus).toBe(undefined);
    expect(response.horse?.conflictOfInterest).toBe(false);
  });

  it('存在しない馬IDでエラーになる', async () => {
    // ===== Act =====
    const request = create(GetHorseRequestSchema, {
      horseId: 99999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.getHorse(request, { headers })).rejects.toThrow();
  });

  it('無効なhorseId（0）でバリデーションエラーになる', async () => {
    // ===== Act =====
    const request = create(GetHorseRequestSchema, {
      horseId: 0,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.getHorse(request, { headers })).rejects.toThrow();
  });

  it('無効なhorseId（負の数）でバリデーションエラーになる', async () => {
    // ===== Act =====
    const request = create(GetHorseRequestSchema, {
      horseId: -1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.getHorse(request, { headers })).rejects.toThrow();
  });
});
