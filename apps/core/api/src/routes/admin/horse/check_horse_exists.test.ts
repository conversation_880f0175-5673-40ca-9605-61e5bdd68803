import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory, getClient } from '@core-test/index';
import { HorseService, CheckHorseExistsRequestSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import type { AdminUserSession } from '@hami/prisma';

describe('checkHorseExists API', () => {
  const apiClient = getClient(HorseService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
  });

  it('存在する馬の場合はtrueを返す', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentYear: 2025,
      recruitmentNo: 100,
      horseName: 'チェック存在テスト馬',
      recruitmentName: 'チェック存在テストの22',
      birthYear: 2022,
      birthMonth: 3,
      birthDay: 15,
      sharesTotal: 1000,
      amountTotal: 50000000,
      note: 'チェック存在テスト備考',
    });

    // ===== Act =====
    const request = create(CheckHorseExistsRequestSchema, {
      recruitmentYear: 2025,
      recruitmentNo: 100,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.checkHorseExists(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.exists).toBe(true);
  });

  it('存在しない馬の場合はfalseを返す', async () => {
    // ===== Act =====
    const request = create(CheckHorseExistsRequestSchema, {
      recruitmentYear: 2099,
      recruitmentNo: 999,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.checkHorseExists(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.exists).toBe(false);
  });

  it('削除された馬の場合はfalseを返す', async () => {
    // ===== Arrange =====
    const deletedHorse = await HorseFactory.create({
      recruitmentYear: 2025,
      recruitmentNo: 101,
      horseName: 'チェック削除存在テスト馬',
      recruitmentName: 'チェック削除存在テストの22',
      birthYear: 2022,
      birthMonth: 3,
      birthDay: 15,
      sharesTotal: 1000,
      amountTotal: 50000000,
      note: 'チェック削除存在テスト備考',
      deletedAt: new Date(),
    });

    // ===== Act =====
    const request = create(CheckHorseExistsRequestSchema, {
      recruitmentYear: 2025,
      recruitmentNo: 101,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.checkHorseExists(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();
    expect(response.exists).toBe(false);
  });

  it('無効な募集年でバリデーションエラーになる', async () => {
    // ===== Act =====
    const request = create(CheckHorseExistsRequestSchema, {
      recruitmentYear: 1800, // 範囲外
      recruitmentNo: 1,
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.checkHorseExists(request, { headers })).rejects.toThrow();
  });

  it('無効な募集番号でバリデーションエラーになる', async () => {
    // ===== Act =====
    const request = create(CheckHorseExistsRequestSchema, {
      recruitmentYear: 2025,
      recruitmentNo: 0, // 無効な値
    });

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.checkHorseExists(request, { headers })).rejects.toThrow();
  });
});
