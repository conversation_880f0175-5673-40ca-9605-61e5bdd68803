import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { HorseFactory, HorseProfileFactory, getClient } from '@core-test/index';
import { HorseService, UpdateHorseRequestSchema } from '@hami/core-admin-api-schema/horse_service_pb';
import type { AdminUserSession } from '@hami/prisma';

describe('updateHorse API', () => {
  const apiClient = getClient(HorseService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
  });

  it('既存の馬を更新できる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: '更新前の馬の2025',
      horseName: '更新前の馬',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '更新前のノート',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
      conflictOfInterest: false,
    });

    await HorseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
    });

    const updateData = {
      horseId: horse.horseId,
      recruitmentName: '更新後の馬の2025',
      horseName: '更新後の馬',
      birthYear: 2021,
      birthMonth: 5,
      birthDay: 20,
      sharesTotal: 200,
      amountTotal: 2000000,
      note: '更新後のノート',
      fundStartYear: 2023,
      fundStartMonth: 11,
      fundStartDay: 2,
      conflictOfInterest: true,
    };

    // ===== Act =====
    const request = create(UpdateHorseRequestSchema, updateData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    const response = await apiClient.updateHorse(request, { headers });

    // ===== Assert =====
    expect(response).toBeDefined();

    // データベースで更新されていることを確認
    const updatedHorse = await vPrisma.client.horse.findUnique({
      where: { horseId: horse.horseId },
    });

    expect(updatedHorse).toBeDefined();
    expect(updatedHorse?.recruitmentName).toBe(updateData.recruitmentName);
    expect(updatedHorse?.horseName).toBe(updateData.horseName);
    expect(updatedHorse?.birthYear).toBe(updateData.birthYear);
    expect(updatedHorse?.birthMonth).toBe(updateData.birthMonth);
    expect(updatedHorse?.birthDay).toBe(updateData.birthDay);
    expect(updatedHorse?.sharesTotal).toBe(updateData.sharesTotal);
    expect(updatedHorse?.amountTotal).toBe(updateData.amountTotal);
    expect(updatedHorse?.note).toBe(updateData.note);
    expect(updatedHorse?.fundStartYear).toBe(updateData.fundStartYear);
    expect(updatedHorse?.fundStartMonth).toBe(updateData.fundStartMonth);
    expect(updatedHorse?.fundStartDay).toBe(updateData.fundStartDay);
    expect(updatedHorse?.conflictOfInterest).toBe(updateData.conflictOfInterest);
  });

  it('存在しない馬IDで更新しようとした場合はエラーになる', async () => {
    // ===== Arrange =====
    const updateData = {
      horseId: 99999, // 存在しないID
      recruitmentName: '存在しない馬の2025',
      horseName: '存在しない馬',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: 'テストノート',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
      conflictOfInterest: false,
    };

    // ===== Act =====
    const request = create(UpdateHorseRequestSchema, updateData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorse(request, { headers })).rejects.toThrow();
  });

  it('論理削除された馬は更新できない', async () => {
    // ===== Arrange =====
    const deletedHorse = await HorseFactory.create({
      recruitmentName: '削除済み馬の2025',
      horseName: '削除済み馬',
      birthYear: 2020,
      birthMonth: 1,
      birthDay: 1,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '削除済みノート',
      deletedAt: new Date(),
    });

    const updateData = {
      horseId: deletedHorse.horseId,
      recruitmentName: '削除済み馬更新の2025',
      horseName: '削除済み馬更新',
      birthYear: 2021,
      birthMonth: 2,
      birthDay: 2,
      sharesTotal: 200,
      amountTotal: 2000000,
      note: '削除済み馬更新ノート',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
      conflictOfInterest: false,
    };

    // ===== Act =====
    const request = create(UpdateHorseRequestSchema, updateData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorse(request, { headers })).rejects.toThrow();
  });

  it('必須フィールドが空の場合はバリデーションエラーになる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: 'バリデーションテスト馬の2025',
      horseName: 'バリデーションテスト馬',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: 'バリデーションテスト',
      conflictOfInterest: false,
    });

    const invalidData = {
      horseId: horse.horseId,
      recruitmentName: '', // 空文字列
      horseName: 'テストホース',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: 'テストノート',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
      conflictOfInterest: false,
    };

    // ===== Act =====
    const request = create(UpdateHorseRequestSchema, invalidData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorse(request, { headers })).rejects.toThrow();
  });

  it('無効な日付でバリデーションエラーになる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: '日付テスト馬の2025',
      horseName: '日付テスト馬',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '日付テスト',
      conflictOfInterest: false,
    });

    const invalidData = {
      horseId: horse.horseId,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
      birthYear: 2020,
      birthMonth: 13, // 無効な月
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: 'テストノート',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
      conflictOfInterest: false,
    };

    // ===== Act =====
    const request = create(UpdateHorseRequestSchema, invalidData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorse(request, { headers })).rejects.toThrow();
  });

  it('負の値でバリデーションエラーになる', async () => {
    // ===== Arrange =====
    const horse = await HorseFactory.create({
      recruitmentName: '負の値テスト馬の2025',
      horseName: '負の値テスト馬',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: 100,
      amountTotal: 1000000,
      note: '負の値テスト',
      conflictOfInterest: false,
    });

    const invalidData = {
      horseId: horse.horseId,
      recruitmentName: 'テスト馬の2025',
      horseName: 'テストホース',
      birthYear: 2020,
      birthMonth: 4,
      birthDay: 15,
      sharesTotal: -100, // 負の値
      amountTotal: 1000000,
      note: 'テストノート',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
      conflictOfInterest: false,
    };

    // ===== Act =====
    const request = create(UpdateHorseRequestSchema, invalidData);

    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });

    await expect(apiClient.updateHorse(request, { headers })).rejects.toThrow();
  });
});
