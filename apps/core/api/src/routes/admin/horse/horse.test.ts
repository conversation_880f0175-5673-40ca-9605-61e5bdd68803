import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { getClient } from '@core-test/index';
import {
  HorseService,
  CheckHorseExistsRequestSchema,
  CreateHorseRequestSchema,
  DeleteHorseRequestSchema,
  GetHorseRequestSchema,
  ListHorsesRequestSchema,
  UpdateHorseRequestSchema,
} from '@hami/core-admin-api-schema/horse_service_pb';
import type { AdminUserSession } from '@hami/prisma';

describe('馬管理APIシナリオテスト', () => {
  const apiClient = getClient(HorseService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
  });

  describe('基本的な作成・取得・更新・削除フロー', () => {
    it('馬を作成して取得し、更新してから削除する一連のフロー', async () => {
      const recruitmentYear = 2025;
      const recruitmentNo = 10000 + Math.floor(Math.random() * 30000);

      // ===== 0. 作成前：馬が存在しないことを確認 =====
      const checkBeforeCreateRequest = create(CheckHorseExistsRequestSchema, {
        recruitmentYear,
        recruitmentNo,
      });

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const checkBeforeCreateResult = await apiClient.checkHorseExists(checkBeforeCreateRequest, { headers });
      expect(checkBeforeCreateResult.exists).toBe(false);

      // ===== 1. 馬を作成 =====
      const createRequest = create(CreateHorseRequestSchema, {
        recruitmentYear,
        recruitmentNo,
        recruitmentName: 'シナリオテスト馬の2025',
        horseName: 'シナリオテストホース',
        birthYear: 2020,
        birthMonth: 3,
        birthDay: 15,
        sharesTotal: 100,
        amountTotal: 1000000,
        note: '作成時のノート',
        fundStartYear: 2024,
        fundStartMonth: 12,
        fundStartDay: 1,
      });

      const createResult = await apiClient.createHorse(createRequest, { headers });
      expect(createResult).toBeDefined();

      // ===== 1.5. 作成後：馬が存在することを確認 =====
      const checkAfterCreateRequest = create(CheckHorseExistsRequestSchema, {
        recruitmentYear,
        recruitmentNo,
      });

      const checkAfterCreateResult = await apiClient.checkHorseExists(checkAfterCreateRequest, { headers });
      expect(checkAfterCreateResult.exists).toBe(true);

      // ===== 2. 作成した馬のIDを取得するため一覧から検索 =====
      const listRequest = create(ListHorsesRequestSchema, {
        page: 1,
        limit: 10,
        search: 'シナリオテスト馬の2025',
      });

      const listResult = await apiClient.listHorses(listRequest, { headers });
      expect(listResult.horses.length).toBe(1);
      const createdHorse = listResult.horses[0];
      expect(createdHorse.recruitmentName).toBe('シナリオテスト馬の2025');
      expect(createdHorse.horseName).toBe('シナリオテストホース');

      const horseId = createdHorse.horseId;

      // ===== 3. 詳細取得で確認 =====
      const getRequest = create(GetHorseRequestSchema, { horseId });
      const getResult = await apiClient.getHorse(getRequest, { headers });
      expect(getResult.horse).toBeDefined();
      if (getResult.horse) {
        expect(getResult.horse.horseId).toBe(horseId);
        expect(getResult.horse.recruitmentName).toBe('シナリオテスト馬の2025');
        expect(getResult.horse.horseName).toBe('シナリオテストホース');
        expect(getResult.horse.note).toBe('作成時のノート');
      }

      // ===== 4. 馬の情報を更新 =====
      const updateRequest = create(UpdateHorseRequestSchema, {
        horseId,
        recruitmentName: '更新されたシナリオテスト馬の2025',
        horseName: '更新されたシナリオテストホース',
        birthYear: 2021,
        birthMonth: 4,
        birthDay: 20,
        sharesTotal: 150,
        amountTotal: 1500000,
        note: '更新後のノート',
        fundStartYear: 2023,
        fundStartMonth: 11,
        fundStartDay: 2,
      });

      const updateResult = await apiClient.updateHorse(updateRequest, { headers });
      expect(updateResult).toBeDefined();

      // ===== 4.5. 更新後も馬が存在することを確認 =====
      const checkAfterUpdateRequest = create(CheckHorseExistsRequestSchema, {
        recruitmentYear,
        recruitmentNo,
      });

      const checkAfterUpdateResult = await apiClient.checkHorseExists(checkAfterUpdateRequest, { headers });
      expect(checkAfterUpdateResult.exists).toBe(true);

      // ===== 5. 更新が反映されていることを確認 =====
      const getUpdatedRequest = create(GetHorseRequestSchema, { horseId });
      const getUpdatedResult = await apiClient.getHorse(getUpdatedRequest, { headers });
      expect(getUpdatedResult.horse).toBeDefined();
      if (getUpdatedResult.horse) {
        expect(getUpdatedResult.horse.recruitmentName).toBe('更新されたシナリオテスト馬の2025');
        expect(getUpdatedResult.horse.horseName).toBe('更新されたシナリオテストホース');
        expect(getUpdatedResult.horse.birthYear).toBe(2021);
        expect(getUpdatedResult.horse.birthMonth).toBe(4);
        expect(getUpdatedResult.horse.birthDay).toBe(20);
        expect(getUpdatedResult.horse.sharesTotal).toBe(150);
        expect(getUpdatedResult.horse.amountTotal).toBe(1500000);
        expect(getUpdatedResult.horse.note).toBe('更新後のノート');
        expect(getUpdatedResult.horse.fundStartYear).toBe(2023);
        expect(getUpdatedResult.horse.fundStartMonth).toBe(11);
        expect(getUpdatedResult.horse.fundStartDay).toBe(2);
      }

      // ===== 6. 馬を削除 =====
      const deleteRequest = create(DeleteHorseRequestSchema, { horseId });
      const deleteResult = await apiClient.deleteHorse(deleteRequest, { headers });
      expect(deleteResult).toBeDefined();

      // ===== 6.5. 削除後：馬が存在しないことを確認 =====
      const checkAfterDeleteRequest = create(CheckHorseExistsRequestSchema, {
        recruitmentYear,
        recruitmentNo,
      });

      const checkAfterDeleteResult = await apiClient.checkHorseExists(checkAfterDeleteRequest, { headers });
      expect(checkAfterDeleteResult.exists).toBe(false);

      // ===== 7. 削除後は取得できなくなることを確認 =====
      try {
        const getDeletedRequest = create(GetHorseRequestSchema, { horseId });
        await apiClient.getHorse(getDeletedRequest, { headers });
        // エラーが発生しなかった場合はテスト失敗
        expect(true).toBe(false);
      } catch (error) {
        // エラーが発生することを期待
        expect(error).toBeDefined();
      }

      // ===== 8. 一覧からも除外されることを確認 =====
      const listAfterDeleteRequest = create(ListHorsesRequestSchema, {
        page: 1,
        limit: 10,
        search: '更新されたシナリオテスト馬の2025',
      });

      const listAfterDeleteResult = await apiClient.listHorses(listAfterDeleteRequest, { headers });
      expect(listAfterDeleteResult.horses.length).toBe(0);
    });
  });

  describe('複数馬管理シナリオ', () => {
    it('複数の馬を作成・管理し、検索・フィルタリング機能を確認する', async () => {
      // ===== 0. 認証設定 =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      // ===== 1. 異なる年の馬を3頭作成 =====
      const baseNo = 20000 + Math.floor(Math.random() * 20000);
      const horses = [
        {
          recruitmentYear: 2025,
          recruitmentNo: baseNo + 1000,
          recruitmentName: '2020年生まれ馬の2025',
          horseName: '2020ホース',
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          sharesTotal: 100,
          amountTotal: 1000000,
          note: '2020年生まれ',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
        {
          recruitmentYear: 2025,
          recruitmentNo: baseNo + 1001,
          recruitmentName: '2021年生まれ馬の2025',
          horseName: '2021ホース',
          birthYear: 2021,
          birthMonth: 2,
          birthDay: 2,
          sharesTotal: 200,
          amountTotal: 2000000,
          note: '2021年生まれ',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
        {
          recruitmentYear: 2025,
          recruitmentNo: baseNo + 1002,
          recruitmentName: '2022年生まれ馬の2025',
          horseName: '2022ホース',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 3,
          sharesTotal: 300,
          amountTotal: 3000000,
          note: '2022年生まれ',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      ];

      // ===== 1.0. 作成前：全ての馬が存在しないことを確認 =====
      for (const horse of horses) {
        const checkRequest = create(CheckHorseExistsRequestSchema, {
          recruitmentYear: horse.recruitmentYear,
          recruitmentNo: horse.recruitmentNo,
        });

        const checkResult = await apiClient.checkHorseExists(checkRequest, { headers });
        expect(checkResult.exists).toBe(false);
      }

      // 全ての馬を作成
      for (const horse of horses) {
        const createRequest = create(CreateHorseRequestSchema, horse);
        const createResult = await apiClient.createHorse(createRequest, { headers });
        expect(createResult).toBeDefined();

        // ===== 1.1. 作成後：各馬が存在することを確認 =====
        const checkAfterCreateRequest = create(CheckHorseExistsRequestSchema, {
          recruitmentYear: horse.recruitmentYear,
          recruitmentNo: horse.recruitmentNo,
        });

        const checkAfterCreateResult = await apiClient.checkHorseExists(checkAfterCreateRequest, { headers });
        expect(checkAfterCreateResult.exists).toBe(true);
      }

      // ===== 2. 全体一覧で3頭確認 =====
      const allHorsesRequest = create(ListHorsesRequestSchema, {
        page: 1,
        limit: 10,
      });

      const allHorsesResult = await apiClient.listHorses(allHorsesRequest, { headers });
      // 最低でも3頭はいることを確認（他のテストで作成された馬も含む可能性がある）
      expect(allHorsesResult.horses.length).toBeGreaterThanOrEqual(3);

      // 作成した馬が含まれていることを確認
      const horseNames = allHorsesResult.horses.map((h: any) => h.horseName);
      expect(horseNames).toContain('2020ホース');
      expect(horseNames).toContain('2021ホース');
      expect(horseNames).toContain('2022ホース');

      // ===== 3. 生年フィルタリングテスト =====
      const filtered2021Request = create(ListHorsesRequestSchema, {
        page: 1,
        limit: 10,
        birthYear: 2021,
      });

      const filtered2021Result = await apiClient.listHorses(filtered2021Request, { headers });
      expect(filtered2021Result.horses.length).toBeGreaterThanOrEqual(1);
      const found2021Horse = filtered2021Result.horses.find((h: any) => h.horseName === '2021ホース');
      expect(found2021Horse).toBeDefined();
      expect(found2021Horse?.birthYear).toBe(2021);

      // ===== 4. 馬名検索テスト =====
      const searchRequest = create(ListHorsesRequestSchema, {
        page: 1,
        limit: 10,
        search: '2020ホース',
      });

      const searchResult = await apiClient.listHorses(searchRequest, { headers });
      expect(searchResult.horses.length).toBeGreaterThanOrEqual(1);
      const foundHorse = searchResult.horses.find((h: any) => h.horseName === '2020ホース');
      expect(foundHorse).toBeDefined();

      // ===== 5. 募集名検索テスト =====
      const recruitmentSearchRequest = create(ListHorsesRequestSchema, {
        page: 1,
        limit: 10,
        search: '2022年生まれ馬の2025',
      });

      const recruitmentSearchResult = await apiClient.listHorses(recruitmentSearchRequest, { headers });
      expect(recruitmentSearchResult.horses.length).toBeGreaterThanOrEqual(1);
      const foundRecruitmentHorse = recruitmentSearchResult.horses.find((h: any) => h.recruitmentName === '2022年生まれ馬の2025');
      expect(foundRecruitmentHorse).toBeDefined();

      // ===== 6. 1頭ずつ削除して確認 =====
      // まず作成した馬のIDを取得
      const allHorsesForDeletionRequest = create(ListHorsesRequestSchema, {
        page: 1,
        limit: 10,
      });

      const allHorsesForDeletionResult = await apiClient.listHorses(allHorsesForDeletionRequest, { headers });

      const createdHorses = allHorsesForDeletionResult.horses.filter((h: any) =>
        ['2020ホース', '2021ホース', '2022ホース'].includes(h.horseName)
      );

      // 各馬を削除
      for (const horse of createdHorses) {
        const deleteRequest = create(DeleteHorseRequestSchema, { horseId: horse.horseId });
        const deleteResult = await apiClient.deleteHorse(deleteRequest, { headers });
        expect(deleteResult).toBeDefined();

        // ===== 6.1. 削除後：該当する馬が存在しないことを確認 =====
        const checkAfterDeleteRequest = create(CheckHorseExistsRequestSchema, {
          recruitmentYear: horse.recruitmentYear,
          recruitmentNo: horse.recruitmentNo,
        });

        const checkAfterDeleteResult = await apiClient.checkHorseExists(checkAfterDeleteRequest, { headers });
        expect(checkAfterDeleteResult.exists).toBe(false);
      }

      // ===== 7. 全て削除後、該当する馬が一覧に表示されないことを確認 =====
      const finalListRequest = create(ListHorsesRequestSchema, {
        page: 1,
        limit: 10,
      });

      const finalListResult = await apiClient.listHorses(finalListRequest, { headers });
      const remainingHorseNames = finalListResult.horses.map((h: any) => h.horseName);
      expect(remainingHorseNames).not.toContain('2020ホース');
      expect(remainingHorseNames).not.toContain('2021ホース');
      expect(remainingHorseNames).not.toContain('2022ホース');
    });
  });

  describe('エラーハンドリングシナリオ', () => {
    it('重複エラーの確認', async () => {
      // ===== 0. 認証設定 =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const baseNo = 90000 + Math.floor(Math.random() * 9000);
      const recruitmentYear = 2025;
      const recruitmentNo = baseNo;

      // ===== 0.5. 作成前：馬が存在しないことを確認 =====
      const checkBeforeCreateRequest = create(CheckHorseExistsRequestSchema, {
        recruitmentYear,
        recruitmentNo,
      });

      const checkBeforeCreateResult = await apiClient.checkHorseExists(checkBeforeCreateRequest, { headers });
      expect(checkBeforeCreateResult.exists).toBe(false);

      // ===== 1. 馬を作成 =====
      const createData = {
        recruitmentYear,
        recruitmentNo,
        recruitmentName: '重複チェック馬の2025',
        horseName: '重複チェックホース',
        birthYear: 2020,
        birthMonth: 1,
        birthDay: 1,
        sharesTotal: 100,
        amountTotal: 1000000,
        note: 'テスト用',
        fundStartYear: 2024,
        fundStartMonth: 12,
        fundStartDay: 1,
      };

      const firstRequest = create(CreateHorseRequestSchema, createData);
      const firstResult = await apiClient.createHorse(firstRequest, { headers });
      expect(firstResult).toBeDefined();

      // ===== 1.5. 作成後：馬が存在することを確認 =====
      const checkAfterCreateRequest = create(CheckHorseExistsRequestSchema, {
        recruitmentYear,
        recruitmentNo,
      });

      const checkAfterCreateResult = await apiClient.checkHorseExists(checkAfterCreateRequest, { headers });
      expect(checkAfterCreateResult.exists).toBe(true);

      // ===== 2. 同じ募集年・番号で重複作成を試行 =====
      try {
        const duplicateRequest = create(CreateHorseRequestSchema, createData);
        await apiClient.createHorse(duplicateRequest, { headers });
        // エラーが発生しなかった場合はテスト失敗
        expect(true).toBe(false);
      } catch (error) {
        // エラーが発生することを期待
        expect(error).toBeDefined();
      }

      // ===== 2.5. 重複作成失敗後も元の馬は存在することを確認 =====
      const checkAfterDuplicateAttemptRequest = create(CheckHorseExistsRequestSchema, {
        recruitmentYear,
        recruitmentNo,
      });

      const checkAfterDuplicateAttemptResult = await apiClient.checkHorseExists(checkAfterDuplicateAttemptRequest, { headers });
      expect(checkAfterDuplicateAttemptResult.exists).toBe(true);
    });

    it('存在しない馬の更新・削除エラーを確認するフロー', async () => {
      // ===== 0. 認証設定 =====
      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const nonExistentHorseId = 99999;
      const nonExistentRecruitmentYear = 2099;
      const nonExistentRecruitmentNo = 99999;

      // ===== 0. 存在しない馬の存在確認 =====
      const checkNonExistentRequest = create(CheckHorseExistsRequestSchema, {
        recruitmentYear: nonExistentRecruitmentYear,
        recruitmentNo: nonExistentRecruitmentNo,
      });

      const checkNonExistentResult = await apiClient.checkHorseExists(checkNonExistentRequest, { headers });
      expect(checkNonExistentResult.exists).toBe(false);

      // ===== 1. 存在しない馬の取得でエラー =====
      try {
        const getRequest = create(GetHorseRequestSchema, { horseId: nonExistentHorseId });
        await apiClient.getHorse(getRequest, { headers });
        // エラーが発生しなかった場合はテスト失敗
        expect(true).toBe(false);
      } catch (error) {
        // エラーが発生することを期待
        expect(error).toBeDefined();
      }

      // ===== 2. 存在しない馬の更新でエラー =====
      try {
        const updateRequest = create(UpdateHorseRequestSchema, {
          horseId: nonExistentHorseId,
          recruitmentName: '存在しない馬の更新',
          horseName: '存在しない馬',
          birthYear: 2020,
          birthMonth: 1,
          birthDay: 1,
          sharesTotal: 100,
          amountTotal: 1000000,
          note: 'エラーテスト',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        });
        await apiClient.updateHorse(updateRequest, { headers });
        // エラーが発生しなかった場合はテスト失敗
        expect(true).toBe(false);
      } catch (error) {
        // エラーが発生することを期待
        expect(error).toBeDefined();
      }

      // ===== 3. 存在しない馬の削除でエラー =====
      try {
        const deleteRequest = create(DeleteHorseRequestSchema, { horseId: nonExistentHorseId });
        await apiClient.deleteHorse(deleteRequest, { headers });
        // エラーが発生しなかった場合はテスト失敗
        expect(true).toBe(false);
      } catch (error) {
        // エラーが発生することを期待
        expect(error).toBeDefined();
      }

      // ===== 4. エラー後も該当する募集年度・番号の馬は存在しないことを再確認 =====
      const checkAfterErrorsRequest = create(CheckHorseExistsRequestSchema, {
        recruitmentYear: nonExistentRecruitmentYear,
        recruitmentNo: nonExistentRecruitmentNo,
      });

      const checkAfterErrorsResult = await apiClient.checkHorseExists(checkAfterErrorsRequest, { headers });
      expect(checkAfterErrorsResult.exists).toBe(false);
    });
  });
});
