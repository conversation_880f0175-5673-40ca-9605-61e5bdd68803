import { HorseService } from '@hami/core-admin-api-schema/horse_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { checkHorseExistsHandler } from './check_horse_exists';
import { createHorseHandler } from './create_horse';
import { deleteHorseHandler } from './delete_horse';
import { getHorseHandler } from './get_horse';
import { listHorseInvestorsHandler } from './list_horse_investors';
import { listHorsesHandler } from './list_horses';
import { updateHorseHandler } from './update_horse';

import type { ConnectRouter } from '@connectrpc/connect';

export const implHorseService = (router: ConnectRouter) =>
  router.service(
    HorseService,
    {
      listHorses: unwrapResult(listHorsesHandler),
      getHorse: unwrapResult(getHorseHandler),
      createHorse: unwrap<PERSON><PERSON><PERSON>(createHorseHandler),
      updateHorse: unwrap<PERSON><PERSON><PERSON>(updateHorseHandler),
      deleteHorse: unwrap<PERSON><PERSON><PERSON>(deleteHorseHandler),
      checkHorseExists: unwrapResult(checkHorseExistsHandler),
      listHorseInvestors: unwrapResult(listHorseInvestorsHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  );
