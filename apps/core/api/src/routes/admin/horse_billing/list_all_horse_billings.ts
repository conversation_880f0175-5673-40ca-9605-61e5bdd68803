// 全馬の支出一覧取得API（日付範囲指定）
import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { z } from 'zod';
import { ListAllHorseBillingsResponseSchema, HorseBillingItemSchema } from '@hami/core-admin-api-schema/horse_billing_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listAllHorseBillings } from '@core-api/repositories/horse_billing_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';
import { convertPrismaHorseBillingItemTypeToProto } from './utils/convert_utils';

export const listAllHorseBillingsHandler = createHandler({
  schema: z.object({
    startYear: z.number().int().min(1900).max(2100),
    startMonth: z.number().int().min(1).max(12),
    startDay: z.number().int().min(1).max(31),
    endYear: z.number().int().min(1900).max(2100),
    endMonth: z.number().int().min(1).max(12),
    endDay: z.number().int().min(1).max(31),
  }),
  business: (params) => {
    return listAllHorseBillings(params);
  },
  toResponse: ({ billings }) =>
    create(ListAllHorseBillingsResponseSchema, {
      billings: billings.map((billing) =>
        create(HorseBillingItemSchema, {
          id: billing.id,
          horseId: billing.horseId,
          horseName: billing.horseName,
          billingYearMonth: billing.billingYearMonth,
          occurredYear: billing.occurredYear,
          occurredMonth: billing.occurredMonth,
          occurredDay: billing.occurredDay,
          billerId: billing.billerId,
          itemType: convertPrismaHorseBillingItemTypeToProto(billing.itemType),
          itemTypeOther: billing.itemTypeOther,
          billingAmount: billing.billingAmount,
          taxAmount: billing.taxAmount,
          subsidyAmount: billing.subsidyAmount,
          totalAmount: billing.totalAmount,
          note: billing.note,
          billerName: billing.billerName,
          closing: billing.closing,
        })
      ),
    }),
  toError: (error) =>
    error instanceof ValidationError
      ? new ConnectError(error.message, Code.InvalidArgument)
      : error instanceof DatabaseError
        ? new ConnectError('Internal server error', Code.Internal)
        : new ConnectError('Failed to list all horse billings', Code.Internal),
});
