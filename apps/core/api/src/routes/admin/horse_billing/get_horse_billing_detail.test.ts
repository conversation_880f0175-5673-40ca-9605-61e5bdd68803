// 馬の支出詳細取得APIテスト
import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { getClient } from '@core-test/index';
import {
  HorseBillingService,
  HorseBillingItemType as ProtoHorseBillingItemType,
  GetHorseBillingDetailRequestSchema,
} from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { HorseBillingItemType } from '@hami/prisma';
import type { AdminUserSession } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';

describe('getHorseBillingDetail API', () => {
  const apiClient = getClient(HorseBillingService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    await client.horseBilling.deleteMany();
    await client.horse.deleteMany();
    await client.biller.deleteMany();
  });

  describe('正常系', () => {
    it('馬の支出詳細を取得できる', async () => {
      // Arrange
      const horse = await client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出',
          closing: false,
        },
      });

      const requestData = {
        id: billing.id,
      };

      // Act
      const request = create(GetHorseBillingDetailRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.getHorseBillingDetail(request, { headers });

      // Assert
      expect(response.billing).toBeDefined();
      const billingDetail = response.billing!;
      expect(billingDetail.id).toBe(billing.id);
      expect(billingDetail.horseId).toBe(horse.horseId);
      expect(billingDetail.billerId).toBe(biller.id);
      expect(billingDetail.billingYearMonth).toBe(202412);
      expect(billingDetail.occurredYear).toBe(2024);
      expect(billingDetail.occurredMonth).toBe(12);
      expect(billingDetail.occurredDay).toBe(1);
      expect(billingDetail.itemType).toBe(ProtoHorseBillingItemType.ENTRUST);
      expect(billingDetail.itemTypeOther).toBe('');
      expect(billingDetail.billingAmount).toBe(10000);
      expect(billingDetail.taxAmount).toBe(1000);
      expect(billingDetail.subsidyAmount).toBe(0);
      expect(billingDetail.totalAmount).toBe(11000);
      expect(billingDetail.note).toBe('テスト支出');
      expect(billingDetail.closing).toBe(false);
    });

    it('締切済みの支出詳細を取得できる', async () => {
      // Arrange
      const horse = await client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202411,
          occurredYear: 2024,
          occurredMonth: 11,
          occurredDay: 1,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 5000,
          taxAmount: 500,
          subsidyAmount: 0,
          totalAmount: 5500,
          note: '締切済支出',
          closing: true,
        },
      });

      const requestData = {
        id: billing.id,
      };

      // Act
      const request = create(GetHorseBillingDetailRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.getHorseBillingDetail(request, { headers });

      // Assert
      expect(response.billing).toBeDefined();
      const billingDetail = response.billing!;
      expect(billingDetail.id).toBe(billing.id);
      expect(billingDetail.closing).toBe(true);
      expect(billingDetail.itemType).toBe(ProtoHorseBillingItemType.INSURANCE);
      expect(billingDetail.note).toBe('締切済支出');
    });
  });

  describe('異常系', () => {
    it('存在しないIDでエラーになる', async () => {
      // Arrange
      const requestData = {
        id: 99999,
      };

      // Act & Assert
      const request = create(GetHorseBillingDetailRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.getHorseBillingDetail(request, { headers })).rejects.toThrow();
    });
  });

  describe('バリデーションエラー', () => {
    it('IDが負の数でエラーになる', async () => {
      // Arrange
      const requestData = {
        id: -1,
      };

      // Act & Assert
      const request = create(GetHorseBillingDetailRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.getHorseBillingDetail(request, { headers })).rejects.toThrow();
    });

    it('IDが0でエラーになる', async () => {
      // Arrange
      const requestData = {
        id: 0,
      };

      // Act & Assert
      const request = create(GetHorseBillingDetailRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.getHorseBillingDetail(request, { headers })).rejects.toThrow();
    });
  });
});
