import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { billerFactory } from '@core-test/factories/biller_factory';
import { horseBillingFactory } from '@core-test/factories/horse_billing_factory';
import { HorseFactory, getClient } from '@core-test/index';
import { HorseBillingService, ListAllHorseBillingsRequestSchema } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { HorseBillingItemType, type Horse, type AdminUserSession, type Biller } from '@hami/prisma';

describe('listAllHorseBillingsHandler', () => {
  const apiClient = getClient(HorseBillingService);
  let horse1: Horse;
  let horse2: Horse;
  let biller1: Biller;
  let biller2: Biller;
  let biller3: Biller;
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    
    // Billerレコードを作成
    biller1 = await billerFactory.create({ name: 'テスト請求者1' });
    biller2 = await billerFactory.create({ name: 'テスト請求者2' });
    biller3 = await billerFactory.create({ name: 'テスト請求者3' });
    
    horse1 = await HorseFactory.create({
      horseId: 1,
      horseName: 'テスト馬1',
      recruitmentYear: 2020,
      recruitmentNo: 1,
      birthYear: 2020,
      birthMonth: 1,
      birthDay: 1,
      recruitmentName: 'テスト馬1募集',
      sharesTotal: 100,
      amountTotal: 10000000,
      note: 'テスト馬1',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
    });

    horse2 = await HorseFactory.create({
      horseId: 2,
      horseName: 'テスト馬2',
      recruitmentYear: 2020,
      recruitmentNo: 2,
      birthYear: 2020,
      birthMonth: 2,
      birthDay: 15,
      recruitmentName: 'テスト馬2募集',
      sharesTotal: 100,
      amountTotal: 10000000,
      note: 'テスト馬2',
      fundStartYear: 2024,
      fundStartMonth: 12,
      fundStartDay: 1,
    });
  });

  it('指定された日付範囲の全馬の支出一覧を取得できる', async () => {
    // ===== Arrange =====
    // 指定範囲内の支出
    const billing1 = await horseBillingFactory.create({
      horseId: horse1.horseId,
      billingYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 15,
      billerId: biller1.id,
      itemType: HorseBillingItemType.ENTRUST,
      itemTypeOther: '',
      billingAmount: 500000,
      taxAmount: 50000,
      subsidyAmount: 0,
      totalAmount: 550000,
      note: 'テスト支出1',
      closing: false,
    });

    const billing2 = await horseBillingFactory.create({
      horseId: horse2.horseId,
      billingYearMonth: 202402,
      occurredYear: 2024,
      occurredMonth: 2,
      occurredDay: 20,
      billerId: biller2.id,
      itemType: HorseBillingItemType.INSURANCE,
      itemTypeOther: '',
      billingAmount: 300000,
      taxAmount: 30000,
      subsidyAmount: 50000,
      totalAmount: 280000,
      note: 'テスト支出2',
      closing: false,
    });

    // 指定範囲外の支出（取得されないはず）
    const billing3 = await horseBillingFactory.create({
      horseId: horse1.horseId,
      billingYearMonth: 202312,
      occurredYear: 2023,
      occurredMonth: 12,
      occurredDay: 31,
      billerId: biller1.id,
      itemType: HorseBillingItemType.OTHER,
      itemTypeOther: '',
      billingAmount: 200000,
      taxAmount: 20000,
      subsidyAmount: 0,
      totalAmount: 220000,
      note: 'テスト支出3（範囲外）',
      closing: false,
    });

    const request = create(ListAllHorseBillingsRequestSchema, {
      startYear: 2024,
      startMonth: 1,
      startDay: 1,
      endYear: 2024,
      endMonth: 12,
      endDay: 31,
    });

    // ===== Act =====
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });
    
    const result = await apiClient.listAllHorseBillings(request, { headers });

    // ===== Assert =====
    expect(result.billings.length).toBe(2);
    
    // 指定範囲内の支出が含まれていることを確認
    const billingIds = result.billings.map((billing) => billing.id);
    expect(billingIds).toContain(billing1.id);
    expect(billingIds).toContain(billing2.id);
    
    // 指定範囲外の支出は含まれていないことを確認
    expect(billingIds).not.toContain(billing3.id);

    // データの詳細を確認
    const billing1Data = result.billings.find((billing) => billing.id === billing1.id);
    const billing2Data = result.billings.find((billing) => billing.id === billing2.id);

    expect(billing1Data).toBeDefined();
    if (billing1Data) {
      expect(billing1Data.horseId).toBe(horse1.horseId);
      expect(billing1Data.horseName).toBe(horse1.horseName);
      expect(billing1Data.billerName).toBe(biller1.name);
      expect(billing1Data.billingYearMonth).toBe(202401);
      expect(billing1Data.occurredYear).toBe(2024);
      expect(billing1Data.occurredMonth).toBe(1);
      expect(billing1Data.occurredDay).toBe(15);
      expect(billing1Data.billerId).toBe(biller1.id);
      expect(billing1Data.itemType).toBe(1); // HORSE_BILLING_ITEM_TYPE_ENTRUST
      expect(billing1Data.billingAmount).toBe(500000);
      expect(billing1Data.totalAmount).toBe(550000);
      expect(billing1Data.closing).toBe(false);
    }

    expect(billing2Data).toBeDefined();
    if (billing2Data) {
      expect(billing2Data.horseId).toBe(horse2.horseId);
      expect(billing2Data.horseName).toBe(horse2.horseName);
      expect(billing2Data.billerName).toBe(biller2.name);
      expect(billing2Data.billingYearMonth).toBe(202402);
      expect(billing2Data.occurredYear).toBe(2024);
      expect(billing2Data.occurredMonth).toBe(2);
      expect(billing2Data.occurredDay).toBe(20);
      expect(billing2Data.billerId).toBe(biller2.id);
      expect(billing2Data.itemType).toBe(2); // HORSE_BILLING_ITEM_TYPE_INSURANCE
      expect(billing2Data.billingAmount).toBe(300000);
      expect(billing2Data.totalAmount).toBe(280000);
      expect(billing2Data.closing).toBe(false);
    }
  });

  it('日付範囲が狭い場合、該当する支出のみ取得される', async () => {
    // ===== Arrange =====
    // 範囲内の支出
    const billing1 = await horseBillingFactory.create({
      horseId: horse1.horseId,
      billingYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 15,
      billerId: biller1.id,
      itemType: HorseBillingItemType.ENTRUST,
      itemTypeOther: '',
      billingAmount: 500000,
      taxAmount: 50000,
      subsidyAmount: 0,
      totalAmount: 550000,
      note: 'テスト支出1',
      closing: false,
    });

    // 範囲外の支出
    const billing2 = await horseBillingFactory.create({
      horseId: horse2.horseId,
      billingYearMonth: 202402,
      occurredYear: 2024,
      occurredMonth: 2,
      occurredDay: 20,
      billerId: biller2.id,
      itemType: HorseBillingItemType.INSURANCE,
      itemTypeOther: '',
      billingAmount: 300000,
      taxAmount: 30000,
      subsidyAmount: 50000,
      totalAmount: 280000,
      note: 'テスト支出2',
      closing: false,
    });

    const request = create(ListAllHorseBillingsRequestSchema, {
      startYear: 2024,
      startMonth: 1,
      startDay: 1,
      endYear: 2024,
      endMonth: 1,
      endDay: 31,
    });

    // ===== Act =====
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });
    
    const result = await apiClient.listAllHorseBillings(request, { headers });

    // ===== Assert =====
    expect(result.billings.length).toBe(1);
    expect(result.billings[0].id).toBe(billing1.id);
  });

  it('支出が存在しない場合、空の配列を返す', async () => {
    // ===== Arrange =====
    const request = create(ListAllHorseBillingsRequestSchema, {
      startYear: 2025,
      startMonth: 1,
      startDay: 1,
      endYear: 2025,
      endMonth: 12,
      endDay: 31,
    });

    // ===== Act =====
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });
    
    const result = await apiClient.listAllHorseBillings(request, { headers });

    // ===== Assert =====
    expect(result.billings.length).toBe(0);
  });

  it('複数の支出種別が正しく処理される', async () => {
    // ===== Arrange =====
    const billing1 = await horseBillingFactory.create({
      horseId: horse1.horseId,
      billingYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 15,
      billerId: biller1.id,
      itemType: HorseBillingItemType.ENTRUST,
      itemTypeOther: '',
      billingAmount: 500000,
      taxAmount: 50000,
      subsidyAmount: 0,
      totalAmount: 550000,
      note: '預託料',
      closing: false,
    });

    const billing2 = await horseBillingFactory.create({
      horseId: horse2.horseId,
      billingYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 20,
      billerId: biller2.id,
      itemType: HorseBillingItemType.INSURANCE,
      itemTypeOther: '',
      billingAmount: 300000,
      taxAmount: 30000,
      subsidyAmount: 50000,
      totalAmount: 280000,
      note: '保険料',
      closing: false,
    });

    const billing3 = await horseBillingFactory.create({
      horseId: horse1.horseId,
      billingYearMonth: 202401,
      occurredYear: 2024,
      occurredMonth: 1,
      occurredDay: 25,
      billerId: biller3.id,
      itemType: HorseBillingItemType.OTHER,
      itemTypeOther: 'その他費用',
      billingAmount: 100000,
      taxAmount: 10000,
      subsidyAmount: 0,
      totalAmount: 110000,
      note: 'その他',
      closing: false,
    });

    const request = create(ListAllHorseBillingsRequestSchema, {
      startYear: 2024,
      startMonth: 1,
      startDay: 1,
      endYear: 2024,
      endMonth: 12,
      endDay: 31,
    });

    // ===== Act =====
    const headers = new Headers({
      sessionToken: adminSession.sessionToken,
    });
    
    const result = await apiClient.listAllHorseBillings(request, { headers });

    // ===== Assert =====
    expect(result.billings.length).toBe(3);
    
    const itemTypes = result.billings.map((billing) => billing.itemType);
    expect(itemTypes).toContain(1); // HORSE_BILLING_ITEM_TYPE_ENTRUST
    expect(itemTypes).toContain(2); // HORSE_BILLING_ITEM_TYPE_INSURANCE
    expect(itemTypes).toContain(3); // HORSE_BILLING_ITEM_TYPE_OTHER
    
    // その他の項目の詳細確認
    const otherBilling = result.billings.find((billing) => billing.itemType === 3); // HORSE_BILLING_ITEM_TYPE_OTHER
    expect(otherBilling?.itemTypeOther).toBe('その他費用');
  });
});
