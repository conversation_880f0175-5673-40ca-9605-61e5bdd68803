// 馬の支出削除API
import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { DeleteHorseBillingResponseSchema } from '@hami/core-admin-api-schema/horse_billing_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { deleteHorseBilling, HorseBillingNotFoundError, HorseBillingClosedError } from '@core-api/repositories/horse_billing_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';

export const deleteHorseBillingHandler = createHandler({
  schema: z.object({
    id: z.number().int().positive(),
  }),
  business: (params) => {
    const { id } = params;
    return deleteHorseBilling(id).map(() => ({
      requestParams: params,
    }));
  },
  toResponse: () => create(DeleteHorseBillingResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseBillingNotFoundError), () => new ConnectError('Horse billing not found', Code.NotFound))
      .with(P.instanceOf(HorseBillingClosedError), () => new ConnectError('Cannot delete closed horse billing', Code.FailedPrecondition))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
