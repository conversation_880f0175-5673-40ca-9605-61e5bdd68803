// 馬の支出一覧取得APIテスト
import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { getClient } from '@core-test/index';
import {
  HorseBillingService,
  HorseBillingItemType as ProtoHorseBillingItemType,
  ListHorseBillingsRequestSchema,
} from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { HorseBillingItemType } from '@hami/prisma';
import type { AdminUserSession } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';

describe('listHorseBillings API', () => {
  const apiClient = getClient(HorseBillingService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    await client.horseBilling.deleteMany();
    await client.horse.deleteMany();
    await client.biller.deleteMany();
  });

  describe('正常系', () => {
    it('馬の支出一覧を取得できる', async () => {
      // Arrange
      const horse = await client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing1 = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出1',
          closing: false,
        },
      });
      const billing2 = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 2,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 5000,
          taxAmount: 500,
          subsidyAmount: 0,
          totalAmount: 5500,
          note: 'テスト支出2',
          closing: false,
        },
      });

      const requestData = {
        horseId: horse.horseId,
        page: 1,
        pageSize: 20,
      };

      // Act
      const request = create(ListHorseBillingsRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.listHorseBillings(request, { headers });

      // Assert
      expect(response.billings).toHaveLength(2);
      expect(response.totalCount).toBe(2);
      expect(response.page).toBe(1);
      expect(response.limit).toBe(20);
      expect(response.totalPages).toBe(1);

      // 新しい順で取得されることを確認
      expect(response.billings[0].id).toBe(billing2.id);
      expect(response.billings[1].id).toBe(billing1.id);

      // 1件目の詳細確認
      const firstBilling = response.billings[0];
      expect(firstBilling.horseId).toBe(horse.horseId);
      expect(firstBilling.billerId).toBe(biller.id);
      expect(firstBilling.billingYearMonth).toBe(202412);
      expect(firstBilling.occurredYear).toBe(2024);
      expect(firstBilling.occurredMonth).toBe(12);
      expect(firstBilling.occurredDay).toBe(2);
      expect(firstBilling.itemType).toBe(ProtoHorseBillingItemType.INSURANCE);
      expect(firstBilling.itemTypeOther).toBe('');
      expect(firstBilling.billingAmount).toBe(5000);
      expect(firstBilling.taxAmount).toBe(500);
      expect(firstBilling.subsidyAmount).toBe(0);
      expect(firstBilling.totalAmount).toBe(5500);
      expect(firstBilling.note).toBe('テスト支出2');
      expect(firstBilling.closing).toBe(false);
    });

    it('検索条件で絞り込みができる', async () => {
      // Arrange
      const horse = await client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出1',
          closing: false,
        },
      });
      await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202411,
          occurredYear: 2024,
          occurredMonth: 11,
          occurredDay: 1,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 5000,
          taxAmount: 500,
          subsidyAmount: 0,
          totalAmount: 5500,
          note: 'テスト支出2',
          closing: false,
        },
      });

      const requestData = {
        horseId: horse.horseId,
        billingYearMonth: 202412,
        page: 1,
        pageSize: 20,
      };

      // Act
      const request = create(ListHorseBillingsRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.listHorseBillings(request, { headers });

      // Assert
      expect(response.billings).toHaveLength(1);
      expect(response.totalCount).toBe(1);
      expect(response.billings[0].billingYearMonth).toBe(202412);
    });

    it('ページネーションが動作する', async () => {
      // Arrange
      const horse = await client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });

      // 25件のデータを作成
      for (let i = 1; i <= 25; i++) {
        await client.horseBilling.create({
          data: {
            horseId: horse.horseId,
            billerId: biller.id,
            billingYearMonth: 202412,
            occurredYear: 2024,
            occurredMonth: 12,
            occurredDay: i,
            itemType: HorseBillingItemType.ENTRUST,
            itemTypeOther: '',
            billingAmount: 1000 * i,
            taxAmount: 100 * i,
            subsidyAmount: 0,
            totalAmount: 1100 * i,
            note: `テスト支出${i}`,
            closing: false,
          },
        });
      }

      const requestData = {
        horseId: horse.horseId,
        page: 2,
        pageSize: 10,
      };

      // Act
      const request = create(ListHorseBillingsRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.listHorseBillings(request, { headers });

      // Assert
      expect(response.billings).toHaveLength(10);
      expect(response.totalCount).toBe(25);
      expect(response.page).toBe(2);
      expect(response.limit).toBe(10);
      expect(response.totalPages).toBe(3);
    });

    it('closingフラグで絞り込みができる', async () => {
      // Arrange
      const horse = await client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: '未締切支出',
          closing: false,
        },
      });
      await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202411,
          occurredYear: 2024,
          occurredMonth: 11,
          occurredDay: 1,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 5000,
          taxAmount: 500,
          subsidyAmount: 0,
          totalAmount: 5500,
          note: '締切済支出',
          closing: true,
        },
      });

      const requestData = {
        horseId: horse.horseId,
        closing: false,
        page: 1,
        pageSize: 20,
      };

      // Act
      const request = create(ListHorseBillingsRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.listHorseBillings(request, { headers });

      // Assert
      expect(response.billings).toHaveLength(1);
      expect(response.billings[0].closing).toBe(false);
      expect(response.billings[0].note).toBe('未締切支出');
    });
  });

  describe('異常系', () => {
    it('存在しない馬IDで空の結果が返される', async () => {
      // Arrange
      const requestData = {
        horseId: 99999,
        page: 1,
        pageSize: 20,
      };

      // Act
      const request = create(ListHorseBillingsRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.listHorseBillings(request, { headers });

      // Assert
      expect(response.billings).toHaveLength(0);
      expect(response.totalCount).toBe(0);
      expect(response.page).toBe(1);
      expect(response.limit).toBe(20);
      expect(response.totalPages).toBe(0);
    });
  });

  describe('バリデーションエラー', () => {
    it('馬IDが負の数でエラーになる', async () => {
      // Arrange
      const requestData = {
        horseId: -1,
        page: 1,
        pageSize: 20,
      };

      // Act & Assert
      const request = create(ListHorseBillingsRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.listHorseBillings(request, { headers })).rejects.toThrow();
    });

    it('ページが負の数でエラーになる', async () => {
      // Arrange
      const requestData = {
        horseId: 1,
        page: -1,
        pageSize: 20,
      };

      // Act & Assert
      const request = create(ListHorseBillingsRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.listHorseBillings(request, { headers })).rejects.toThrow();
    });

    it('ページサイズが上限を超えるとエラーになる', async () => {
      // Arrange
      const requestData = {
        horseId: 1,
        page: 1,
        pageSize: 101,
      };

      // Act & Assert
      const request = create(ListHorseBillingsRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.listHorseBillings(request, { headers })).rejects.toThrow();
    });
  });
});
