import { HorseBillingItemType as ProtoHorseBillingItemType } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { HorseBillingItemType as PrismaHorseBillingItemType } from '@hami/prisma';
import {
  convertPrismaHorseBillingItemTypeToProto,
  convertProtoHorseBillingItemTypeToPrisma,
} from './convert_utils';

describe('convert_utils', () => {
  describe('convertPrismaHorseBillingItemTypeToProto', () => {
    it('ENTRUSTを正しく変換できる', () => {
      // Act
      const result = convertPrismaHorseBillingItemTypeToProto(PrismaHorseBillingItemType.ENTRUST);

      // Assert
      expect(result).toBe(ProtoHorseBillingItemType.ENTRUST);
    });

    it('INSURANCEを正しく変換できる', () => {
      // Act
      const result = convertPrismaHorseBillingItemTypeToProto(PrismaHorseBillingItemType.INSURANCE);

      // Assert
      expect(result).toBe(ProtoHorseBillingItemType.INSURANCE);
    });

    it('OTHERを正しく変換できる', () => {
      // Act
      const result = convertPrismaHorseBillingItemTypeToProto(PrismaHorseBillingItemType.OTHER);

      // Assert
      expect(result).toBe(ProtoHorseBillingItemType.OTHER);
    });

    it('未知の値はUNSPECIFIEDに変換される', () => {
      // Act
      const result = convertPrismaHorseBillingItemTypeToProto('UNKNOWN' as any);

      // Assert
      expect(result).toBe(ProtoHorseBillingItemType.UNSPECIFIED);
    });
  });

  describe('convertProtoHorseBillingItemTypeToPrisma', () => {
    it('ENTRUSTを正しく変換できる', () => {
      // Act
      const result = convertProtoHorseBillingItemTypeToPrisma(ProtoHorseBillingItemType.ENTRUST);

      // Assert
      expect(result).toBe(PrismaHorseBillingItemType.ENTRUST);
    });

    it('INSURANCEを正しく変換できる', () => {
      // Act
      const result = convertProtoHorseBillingItemTypeToPrisma(ProtoHorseBillingItemType.INSURANCE);

      // Assert
      expect(result).toBe(PrismaHorseBillingItemType.INSURANCE);
    });

    it('OTHERを正しく変換できる', () => {
      // Act
      const result = convertProtoHorseBillingItemTypeToPrisma(ProtoHorseBillingItemType.OTHER);

      // Assert
      expect(result).toBe(PrismaHorseBillingItemType.OTHER);
    });

    it('UNSPECIFIEDはエラーになる', () => {
      // Act & Assert
      expect(() => convertProtoHorseBillingItemTypeToPrisma(ProtoHorseBillingItemType.UNSPECIFIED))
        .toThrow('Unknown HorseBillingItemType: 0');
    });

    it('未知の値はエラーになる', () => {
      // Act & Assert
      expect(() => convertProtoHorseBillingItemTypeToPrisma(999 as any))
        .toThrow('Unknown HorseBillingItemType: 999');
    });
  });

  describe('双方向変換の整合性', () => {
    it('ENTRUSTの双方向変換が一致する', () => {
      // Arrange
      const prismaType = PrismaHorseBillingItemType.ENTRUST;

      // Act
      const protoType = convertPrismaHorseBillingItemTypeToProto(prismaType);
      const convertedBack = convertProtoHorseBillingItemTypeToPrisma(protoType);

      // Assert
      expect(convertedBack).toBe(prismaType);
    });

    it('INSURANCEの双方向変換が一致する', () => {
      // Arrange
      const prismaType = PrismaHorseBillingItemType.INSURANCE;

      // Act
      const protoType = convertPrismaHorseBillingItemTypeToProto(prismaType);
      const convertedBack = convertProtoHorseBillingItemTypeToPrisma(protoType);

      // Assert
      expect(convertedBack).toBe(prismaType);
    });

    it('OTHERの双方向変換が一致する', () => {
      // Arrange
      const prismaType = PrismaHorseBillingItemType.OTHER;

      // Act
      const protoType = convertPrismaHorseBillingItemTypeToProto(prismaType);
      const convertedBack = convertProtoHorseBillingItemTypeToPrisma(protoType);

      // Assert
      expect(convertedBack).toBe(prismaType);
    });
  });
}); 