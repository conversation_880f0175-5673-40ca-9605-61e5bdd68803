// 型変換ユーティリティ 

// TODO: protoファイル生成後に実装
// import { HorseBillingItemType as ProtoHorseBillingItemType } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { HorseBillingItemType as ProtoHorseBillingItemType } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { HorseBillingItemType as PrismaHorseBillingItemType } from '@hami/prisma';

/**
 * PrismaのHorseBillingItemTypeをprotoのHorseBillingItemTypeに変換
 */
export function convertPrismaHorseBillingItemTypeToProto(
  itemType: PrismaHorseBillingItemType
): ProtoHorseBillingItemType {
  switch (itemType) {
    case PrismaHorseBillingItemType.ENTRUST:
      return ProtoHorseBillingItemType.ENTRUST;
    case PrismaHorseBillingItemType.INSURANCE:
      return ProtoHorseBillingItemType.INSURANCE;
    case PrismaHorseBillingItemType.OTHER:
      return ProtoHorseBillingItemType.OTHER;
    default:
      return ProtoHorseBillingItemType.UNSPECIFIED;
  }
}

/**
 * protoのHorseBillingItemTypeをPrismaのHorseBillingItemTypeに変換
 */
export function convertProtoHorseBillingItemTypeToPrisma(
  itemType: ProtoHorseBillingItemType
): PrismaHorseBillingItemType {
  switch (itemType) {
    case ProtoHorseBillingItemType.ENTRUST:
      return PrismaHorseBillingItemType.ENTRUST;
    case ProtoHorseBillingItemType.INSURANCE:
      return PrismaHorseBillingItemType.INSURANCE;
    case ProtoHorseBillingItemType.OTHER:
      return PrismaHorseBillingItemType.OTHER;
    default:
      throw new Error(`Unknown HorseBillingItemType: ${itemType}`);
  }
} 