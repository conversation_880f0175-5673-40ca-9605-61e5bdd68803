// 馬の支出更新APIテスト
import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { getClient } from '@core-test/index';
import {
  HorseBillingService,
  HorseBillingItemType as ProtoHorseBillingItemType,
  UpdateHorseBillingRequestSchema,
} from '@hami/core-admin-api-schema/horse_billing_service_pb';
import type { AdminUserSession } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { billerFactory } from '../../../../test_utils/factories/biller_factory';
import { HorseFactory } from '../../../../test_utils/factories/horse_factory';

describe('updateHorseBilling API', () => {
  const apiClient = getClient(HorseBillingService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    await client.horseBilling.deleteMany();
    await client.horse.deleteMany();
    await client.biller.deleteMany();
  });

  describe('正常系', () => {
    it('馬の支出を更新できる', async () => {
      // Arrange
      const horse = await HorseFactory.create();
      const biller = await billerFactory.create();
      const billing = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: 'ENTRUST',
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: '更新前の支出',
          closing: false,
        },
      });

      const requestData = {
        id: billing.id,
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: biller.id,
        itemType: ProtoHorseBillingItemType.INSURANCE,
        itemTypeOther: '保険料詳細',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 500,
        totalAmount: 17000,
        note: '更新後の支出',
      };

      // Act
      const request = create(UpdateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.updateHorseBilling(request, { headers });

      // Assert
      expect(response).toBeDefined();

      // データベースに更新されていることを確認
      const updatedBilling = await client.horseBilling.findUnique({
        where: { id: billing.id },
      });
      expect(updatedBilling).toBeTruthy();
      expect(updatedBilling?.billingYearMonth).toBe(202411);
      expect(updatedBilling?.itemType).toBe('INSURANCE');
      expect(updatedBilling?.itemTypeOther).toBe('保険料詳細');
      expect(updatedBilling?.billingAmount).toBe(15000);
      expect(updatedBilling?.note).toBe('更新後の支出');
    });
  });

  describe('異常系', () => {
    it('存在しないIDでエラーになる', async () => {
      // Arrange
      const biller = await billerFactory.create();

      const requestData = {
        id: 999,
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: biller.id,
        itemType: ProtoHorseBillingItemType.INSURANCE,
        itemTypeOther: '保険料詳細',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 500,
        totalAmount: 17000,
        note: '更新後の支出',
      };

      // Act
      const request = create(UpdateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.updateHorseBilling(request, { headers })).rejects.toThrow();
    });

    it('締切済みの支出は更新できない', async () => {
      // Arrange
      const horse = await HorseFactory.create();
      const biller = await billerFactory.create();
      const billing = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: 'ENTRUST',
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: '締切済み支出',
          closing: true,
        },
      });

      const requestData = {
        id: billing.id,
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: biller.id,
        itemType: ProtoHorseBillingItemType.INSURANCE,
        itemTypeOther: '保険料詳細',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 500,
        totalAmount: 17000,
        note: '更新後の支出',
      };

      // Act
      const request = create(UpdateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.updateHorseBilling(request, { headers })).rejects.toThrow();
    });

    it('存在しない請求者IDでエラーになる', async () => {
      // Arrange
      const horse = await HorseFactory.create();
      const biller = await billerFactory.create();
      const billing = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: 'ENTRUST',
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: '更新前の支出',
          closing: false,
        },
      });

      const requestData = {
        id: billing.id,
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: 999,
        itemType: ProtoHorseBillingItemType.INSURANCE,
        itemTypeOther: '保険料詳細',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 500,
        totalAmount: 17000,
        note: '更新後の支出',
      };

      // Act
      const request = create(UpdateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.updateHorseBilling(request, { headers })).rejects.toThrow();
    });
  });

  describe('バリデーションエラー', () => {
    it('IDが負の数でエラーになる', async () => {
      // Arrange
      const requestData = {
        id: -1,
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: 1,
        itemType: ProtoHorseBillingItemType.INSURANCE,
        itemTypeOther: '保険料詳細',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 500,
        totalAmount: 17000,
        note: '更新後の支出',
      };

      // Act & Assert
      const request = create(UpdateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.updateHorseBilling(request, { headers })).rejects.toThrow();
    });

    it('IDが0でエラーになる', async () => {
      // Arrange
      const requestData = {
        id: 0,
        billingYearMonth: 202411,
        occurredYear: 2024,
        occurredMonth: 11,
        occurredDay: 15,
        billerId: 1,
        itemType: ProtoHorseBillingItemType.INSURANCE,
        itemTypeOther: '保険料詳細',
        billingAmount: 15000,
        taxAmount: 1500,
        subsidyAmount: 500,
        totalAmount: 17000,
        note: '更新後の支出',
      };

      // Act & Assert
      const request = create(UpdateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.updateHorseBilling(request, { headers })).rejects.toThrow();
    });
  });
});
