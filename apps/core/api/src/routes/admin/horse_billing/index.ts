// 馬の支出管理サービス実装 
import { HorseBillingService } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { adminUserAuthenticator } from '@core-api/middlewares/interceptors';
import { unwrapResult } from '@core-api/utils/unwrap_handler';
import { createHorseBillingHandler } from './create_horse_billing';
import { deleteHorseBillingHandler } from './delete_horse_billing';
import { getHorseBillingDetailHandler } from './get_horse_billing_detail';
import { listAllHorseBillingsHandler } from './list_all_horse_billings';
import { listHorseBillingsHandler } from './list_horse_billings';
import { updateHorseBillingHandler } from './update_horse_billing';

import type { ConnectRouter } from '@connectrpc/connect';

export const implHorseBillingService = (router: ConnectRouter) =>
  router.service(
    HorseBillingService,
    {
      listHorseBillings: unwrapResult(listHorseBillingsHandler),
      listAllHorseBillings: unwrapResult(listAllHorseBillingsHandler),
      getHorseBillingDetail: unwrapResult(getHorseBillingDetailHandler),
      createHorseBilling: unwrapResult(createHorseBillingHandler),
      updateHorseBilling: unwrapResult(updateHorseBillingHandler),
      deleteHorseBilling: unwrapResult(deleteHorseBillingHandler),
    },
    { interceptors: [adminUserAuthenticator] }
  ); 