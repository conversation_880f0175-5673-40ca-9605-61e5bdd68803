// 馬の支出作成API
import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { CreateHorseBillingResponseSchema } from '@hami/core-admin-api-schema/horse_billing_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { createHorseBilling, HorseNotFoundError, BillerNotFoundError } from '@core-api/repositories/horse_billing_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';
import { convertProtoHorseBillingItemTypeToPrisma } from './utils/convert_utils';

export const createHorseBillingHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
    billingYearMonth: z.number().int(),
    occurredYear: z.number().int(),
    occurredMonth: z.number().int(),
    occurredDay: z.number().int(),
    billerId: z.number().int().positive(),
    itemType: z.number().int(),
    itemTypeOther: z.string(),
    billingAmount: z.number().int(),
    taxAmount: z.number().int(),
    subsidyAmount: z.number().int(),
    totalAmount: z.number().int(),
    note: z.string(),
  }),
  business: (params) => {
    const {
      horseId,
      billingYearMonth,
      occurredYear,
      occurredMonth,
      occurredDay,
      billerId,
      itemType,
      itemTypeOther,
      billingAmount,
      taxAmount,
      subsidyAmount,
      totalAmount,
      note,
    } = params;

    return createHorseBilling({
      horseId,
      billingYearMonth,
      occurredYear,
      occurredMonth,
      occurredDay,
      billerId,
      itemType: convertProtoHorseBillingItemTypeToPrisma(itemType),
      itemTypeOther,
      billingAmount,
      taxAmount,
      subsidyAmount,
      totalAmount,
      note,
    }).map(() => ({
      requestParams: params,
    }));
  },
  toResponse: () => create(CreateHorseBillingResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseNotFoundError), () => new ConnectError('Horse not found', Code.NotFound))
      .with(P.instanceOf(BillerNotFoundError), () => new ConnectError('Biller not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
