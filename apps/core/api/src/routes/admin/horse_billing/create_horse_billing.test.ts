// 馬の支出作成APIテスト
import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { getClient } from '@core-test/index';
import {
  HorseBillingService,
  HorseBillingItemType as ProtoHorseBillingItemType,
  CreateHorseBillingRequestSchema,
} from '@hami/core-admin-api-schema/horse_billing_service_pb';
import type { AdminUserSession } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { billerFactory } from '../../../../test_utils/factories/biller_factory';
import { HorseFactory } from '../../../../test_utils/factories/horse_factory';

describe('createHorseBilling API', () => {
  const apiClient = getClient(HorseBillingService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    await client.horseBilling.deleteMany();
    await client.horse.deleteMany();
    await client.biller.deleteMany();
  });

  describe('正常系', () => {
    it('馬の支出を作成できる', async () => {
      // Arrange
      const horse = await HorseFactory.create();
      const biller = await billerFactory.create();

      const requestData = {
        horseId: horse.horseId,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: biller.id,
        itemType: ProtoHorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: 10000,
        taxAmount: 1000,
        subsidyAmount: 500,
        totalAmount: 9500,
        note: '預託料',
      };

      // Act
      const request = create(CreateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.createHorseBilling(request, { headers });

      // Assert
      expect(response).toBeDefined();

      // データベースに保存されていることを確認
      const savedBilling = await client.horseBilling.findFirst({
        where: { horseId: horse.horseId },
      });
      expect(savedBilling).toBeTruthy();
      expect(savedBilling?.billingAmount).toBe(10000);
      expect(savedBilling?.note).toBe('預託料');
    });

    it('保険料の支出を作成できる', async () => {
      // Arrange
      const horse = await HorseFactory.create();
      const biller = await billerFactory.create();

      const requestData = {
        horseId: horse.horseId,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 2,
        billerId: biller.id,
        itemType: ProtoHorseBillingItemType.INSURANCE,
        itemTypeOther: '',
        billingAmount: 5000,
        taxAmount: 500,
        subsidyAmount: 0,
        totalAmount: 4500,
        note: '保険料',
      };

      // Act
      const request = create(CreateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.createHorseBilling(request, { headers });

      // Assert
      expect(response).toBeDefined();

      // データベースに保存されていることを確認
      const savedBilling = await client.horseBilling.findFirst({
        where: { horseId: horse.horseId },
      });
      expect(savedBilling).toBeTruthy();
      expect(savedBilling?.billingAmount).toBe(5000);
      expect(savedBilling?.note).toBe('保険料');
    });

    it('その他の支出を作成できる', async () => {
      // Arrange
      const horse = await HorseFactory.create();
      const biller = await billerFactory.create();

      const requestData = {
        horseId: horse.horseId,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 3,
        billerId: biller.id,
        itemType: ProtoHorseBillingItemType.OTHER,
        itemTypeOther: '特別料金',
        billingAmount: 3000,
        taxAmount: 300,
        subsidyAmount: 0,
        totalAmount: 2700,
        note: 'その他費用',
      };

      // Act
      const request = create(CreateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.createHorseBilling(request, { headers });

      // Assert
      expect(response).toBeDefined();

      // データベースに保存されていることを確認
      const savedBilling = await client.horseBilling.findFirst({
        where: { horseId: horse.horseId },
      });
      expect(savedBilling).toBeTruthy();
      expect(savedBilling?.billingAmount).toBe(3000);
      expect(savedBilling?.itemTypeOther).toBe('特別料金');
      expect(savedBilling?.note).toBe('その他費用');
    });
  });

  describe('異常系', () => {
    it('存在しない馬IDでエラーになる', async () => {
      // Arrange
      const biller = await billerFactory.create();

      const requestData = {
        horseId: 999,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: biller.id,
        itemType: ProtoHorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: 10000,
        taxAmount: 1000,
        subsidyAmount: 500,
        totalAmount: 9500,
        note: '預託料',
      };

      // Act
      const request = create(CreateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.createHorseBilling(request, { headers })).rejects.toThrow();
    });

    it('存在しない請求者IDでエラーになる', async () => {
      // Arrange
      const horse = await HorseFactory.create();

      const requestData = {
        horseId: horse.horseId,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: 999,
        itemType: ProtoHorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: 10000,
        taxAmount: 1000,
        subsidyAmount: 500,
        totalAmount: 9500,
        note: '預託料',
      };

      // Act
      const request = create(CreateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.createHorseBilling(request, { headers })).rejects.toThrow();
    });
  });

  describe('バリデーションエラー', () => {
    it('馬IDが負の数でエラーになる', async () => {
      // Arrange
      const requestData = {
        horseId: -1,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: 1,
        itemType: ProtoHorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: 10000,
        taxAmount: 1000,
        subsidyAmount: 500,
        totalAmount: 9500,
        note: '預託料',
      };

      // Act & Assert
      const request = create(CreateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.createHorseBilling(request, { headers })).rejects.toThrow();
    });

    it('請求者IDが負の数でエラーになる', async () => {
      // Arrange
      const requestData = {
        horseId: 1,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: -1,
        itemType: ProtoHorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: 10000,
        taxAmount: 1000,
        subsidyAmount: 500,
        totalAmount: 9500,
        note: '預託料',
      };

      // Act & Assert
      const request = create(CreateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.createHorseBilling(request, { headers })).rejects.toThrow();
    });

    it('金額が負の数でエラーになる', async () => {
      // Arrange
      const requestData = {
        horseId: 1,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: 1,
        itemType: ProtoHorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: -10000,
        taxAmount: 1000,
        subsidyAmount: 500,
        totalAmount: 9500,
        note: '預託料',
      };

      // Act & Assert
      const request = create(CreateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.createHorseBilling(request, { headers })).rejects.toThrow();
    });

    it('必須項目が不足でエラーになる', async () => {
      // Arrange
      const requestData = {
        horseId: 1,
        billingYearMonth: 202412,
        occurredYear: 2024,
        occurredMonth: 12,
        occurredDay: 1,
        billerId: 1,
        itemType: ProtoHorseBillingItemType.ENTRUST,
        itemTypeOther: '',
        billingAmount: 10000,
        taxAmount: 1000,
        subsidyAmount: 500,
        totalAmount: 9500,
        note: '預託料',
      };

      // Act & Assert
      const request = create(CreateHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.createHorseBilling(request, { headers })).rejects.toThrow();
    });
  });
});
