// 馬の支出削除APIテスト
import { create } from '@bufbuild/protobuf';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { getClient } from '@core-test/index';
import { HorseBillingService, DeleteHorseBillingRequestSchema } from '@hami/core-admin-api-schema/horse_billing_service_pb';
import { HorseBillingItemType } from '@hami/prisma';
import type { AdminUserSession } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';

describe('deleteHorseBilling API', () => {
  const apiClient = getClient(HorseBillingService);
  let adminSession: AdminUserSession;

  beforeEach(async () => {
    adminSession = await AdminUserSessionFactory.create();
    await client.horseBilling.deleteMany();
    await client.horse.deleteMany();
    await client.biller.deleteMany();
  });

  describe('正常系', () => {
    it('馬の支出を削除できる', async () => {
      // Arrange
      const horse = await client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出',
          closing: false,
        },
      });

      const requestData = {
        id: billing.id,
      };

      // Act
      const request = create(DeleteHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      const response = await apiClient.deleteHorseBilling(request, { headers });

      // Assert
      expect(response).toBeDefined();

      // DBから削除されていることを確認
      const deletedBilling = await client.horseBilling.findUnique({
        where: { id: billing.id },
      });
      expect(deletedBilling).toBeNull();
    });

    it('複数の支出を削除できる', async () => {
      // Arrange
      const horse = await client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing1 = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 1,
          itemType: HorseBillingItemType.ENTRUST,
          itemTypeOther: '',
          billingAmount: 10000,
          taxAmount: 1000,
          subsidyAmount: 0,
          totalAmount: 11000,
          note: 'テスト支出1',
          closing: false,
        },
      });
      const billing2 = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202412,
          occurredYear: 2024,
          occurredMonth: 12,
          occurredDay: 2,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 5000,
          taxAmount: 500,
          subsidyAmount: 0,
          totalAmount: 5500,
          note: 'テスト支出2',
          closing: false,
        },
      });

      // 1件目を削除
      const requestData1 = {
        id: billing1.id,
      };
      const request1 = create(DeleteHorseBillingRequestSchema, requestData1);
      const headers1 = new Headers({
        sessionToken: adminSession.sessionToken,
      });
      await apiClient.deleteHorseBilling(request1, { headers: headers1 });

      // 2件目を削除
      const requestData2 = {
        id: billing2.id,
      };
      const request2 = create(DeleteHorseBillingRequestSchema, requestData2);
      const headers2 = new Headers({
        sessionToken: adminSession.sessionToken,
      });
      await apiClient.deleteHorseBilling(request2, { headers: headers2 });

      // Assert
      const deletedBilling1 = await client.horseBilling.findUnique({
        where: { id: billing1.id },
      });
      const deletedBilling2 = await client.horseBilling.findUnique({
        where: { id: billing2.id },
      });
      expect(deletedBilling1).toBeNull();
      expect(deletedBilling2).toBeNull();
    });
  });

  describe('異常系', () => {
    it('存在しないIDでエラーになる', async () => {
      // Arrange
      const requestData = {
        id: 99999,
      };

      // Act & Assert
      const request = create(DeleteHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.deleteHorseBilling(request, { headers })).rejects.toThrow();
    });

    it('締切済みの支出は削除できない', async () => {
      // Arrange
      const horse = await client.horse.create({
        data: {
          recruitmentYear: 2024,
          recruitmentNo: 1,
          horseName: 'テスト馬',
          birthYear: 2022,
          birthMonth: 3,
          birthDay: 15,
          recruitmentName: 'テストの22',
          sharesTotal: 1000,
          amountTotal: ********,
          note: 'テスト馬の備考',
          fundStartYear: 2024,
          fundStartMonth: 12,
          fundStartDay: 1,
        },
      });
      const biller = await client.biller.create({
        data: {
          name: 'テスト請求者',
          address: '東京都渋谷区',
          bankName: 'テスト銀行',
          bankBranch: 'テスト支店',
          bankAccount: '1234567',
          bankAccountType: 'checking',
          bankAccountName: 'テスト請求者',
        },
      });
      const billing = await client.horseBilling.create({
        data: {
          horseId: horse.horseId,
          billerId: biller.id,
          billingYearMonth: 202411,
          occurredYear: 2024,
          occurredMonth: 11,
          occurredDay: 1,
          itemType: HorseBillingItemType.INSURANCE,
          itemTypeOther: '',
          billingAmount: 5000,
          taxAmount: 500,
          subsidyAmount: 0,
          totalAmount: 5500,
          note: '締切済支出',
          closing: true,
        },
      });

      const requestData = {
        id: billing.id,
      };

      // Act & Assert
      const request = create(DeleteHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.deleteHorseBilling(request, { headers })).rejects.toThrow();

      // 削除されていないことを確認
      const notDeletedBilling = await client.horseBilling.findUnique({
        where: { id: billing.id },
      });
      expect(notDeletedBilling).not.toBeNull();
    });
  });

  describe('バリデーションエラー', () => {
    it('IDが負の数でエラーになる', async () => {
      // Arrange
      const requestData = {
        id: -1,
      };

      // Act & Assert
      const request = create(DeleteHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.deleteHorseBilling(request, { headers })).rejects.toThrow();
    });

    it('IDが0でエラーになる', async () => {
      // Arrange
      const requestData = {
        id: 0,
      };

      // Act & Assert
      const request = create(DeleteHorseBillingRequestSchema, requestData);

      const headers = new Headers({
        sessionToken: adminSession.sessionToken,
      });

      await expect(apiClient.deleteHorseBilling(request, { headers })).rejects.toThrow();
    });
  });
});
