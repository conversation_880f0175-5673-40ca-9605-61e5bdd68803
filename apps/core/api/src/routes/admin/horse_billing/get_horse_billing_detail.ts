// 馬の支出詳細取得API
import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetHorseBillingDetailResponseSchema, HorseBillingItemSchema } from '@hami/core-admin-api-schema/horse_billing_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { getHorseBillingDetail, HorseBillingNotFoundError } from '@core-api/repositories/horse_billing_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';
import { convertPrismaHorseBillingItemTypeToProto } from './utils/convert_utils';

export const getHorseBillingDetailHandler = createHandler({
  schema: z.object({
    id: z.number().int().positive(),
  }),
  business: (params) => {
    const { id } = params;
    return getHorseBillingDetail(id).map((result) => ({
      ...result,
      requestParams: params,
    }));
  },
  toResponse: ({
    id,
    horseId,
    billingYearMonth,
    occurredYear,
    occurredMonth,
    occurredDay,
    billerId,
    itemType,
    itemTypeOther,
    billingAmount,
    taxAmount,
    subsidyAmount,
    totalAmount,
    note,
    closing,
  }) =>
    create(GetHorseBillingDetailResponseSchema, {
      billing: create(HorseBillingItemSchema, {
        id,
        horseId,
        billingYearMonth,
        occurredYear,
        occurredMonth,
        occurredDay,
        billerId,
        itemType: convertPrismaHorseBillingItemTypeToProto(itemType),
        itemTypeOther,
        billingAmount,
        taxAmount,
        subsidyAmount,
        totalAmount,
        note,
        closing,
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseBillingNotFoundError), () => new ConnectError('Horse billing not found', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
