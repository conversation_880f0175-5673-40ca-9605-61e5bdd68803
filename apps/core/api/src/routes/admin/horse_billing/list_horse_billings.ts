// 馬の支出一覧取得API 
import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { 
  ListHorseBillingsResponseSchema, 
  HorseBillingItemSchema,
  HorseBillingItemType as ProtoHorseBillingItemType
} from '@hami/core-admin-api-schema/horse_billing_service_pb';

import { DatabaseError } from '@core-api/repositories';
import { listHorseBillings } from '@core-api/repositories/horse_billing_repository';
import { createHandler } from '@core-api/utils/handler_factory';
import { ValidationError } from '@core-api/utils/validate_request';
import { convertPrismaHorseBillingItemTypeToProto, convertProtoHorseBillingItemTypeToPrisma } from './utils/convert_utils';

export const listHorseBillingsHandler = createHandler({
  schema: z.object({
    horseId: z.number().int().positive(),
    billingYearMonth: z.number().int().optional(),
    itemType: z.nativeEnum(ProtoHorseBillingItemType).optional(),
    billerId: z.number().int().positive().optional(),
    closing: z.boolean().optional(),
    page: z.number().int().positive().optional(),
    pageSize: z.number().int().positive().max(100).optional(),
  }),
  business: (params) => {
    const { 
      horseId, 
      billingYearMonth, 
      itemType, 
      billerId, 
      closing, 
      page = 1, 
      pageSize = 20 
    } = params;
    
    return listHorseBillings({ 
      horseId, 
      billingYearMonth, 
      itemType: itemType ? convertProtoHorseBillingItemTypeToPrisma(itemType) : undefined, 
      billerId, 
      closing, 
      page, 
      pageSize 
    }).map((result) => ({
      ...result,
      requestParams: { ...params, page, pageSize },
    }));
  },
  toResponse: ({ billings, totalCount, totalPages, requestParams }) =>
    create(ListHorseBillingsResponseSchema, {
      billings: billings.map((billing) =>
        create(HorseBillingItemSchema, {
          id: billing.id,
          horseId: billing.horseId,
          billingYearMonth: billing.billingYearMonth,
          occurredYear: billing.occurredYear,
          occurredMonth: billing.occurredMonth,
          occurredDay: billing.occurredDay,
          billerId: billing.billerId,
          itemType: convertPrismaHorseBillingItemTypeToProto(billing.itemType),
          itemTypeOther: billing.itemTypeOther,
          billingAmount: billing.billingAmount,
          taxAmount: billing.taxAmount,
          subsidyAmount: billing.subsidyAmount,
          totalAmount: billing.totalAmount,
          note: billing.note,
          closing: billing.closing,
        })
      ),
      totalCount,
      page: requestParams.page,
      limit: requestParams.pageSize,
      totalPages,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
}); 