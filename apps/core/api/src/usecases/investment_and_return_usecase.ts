import { ResultAsync } from 'neverthrow';
import { HorseIncomeOtherName } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { HorseBillingItemType, ReturnCategory, InvestmentAndReturn } from '@hami/prisma';
import { getBillingTotalBeforeTax, listHorseBillings } from '@core-api/repositories/horse_billing_repository';
import { listHorseIncomes, listHorseIncomePrize, createHorseIncomeOther } from '@core-api/repositories/horse_income_repository';
import { findHorseById, listHorseInvestors } from '@core-api/repositories/horse_repository';
import {
  createInvestmentAndReturn,
  createInvestmentAndReturnInvestment,
  createInvestmentAndReturnReturn,
  getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate,
  getTotalRacehorseInvestmentByHorseIdAndMemberIdAndDate,
  listInvestmentAndReturnByHorseIdAndMemberId,
  updatePaymentAmount,
} from '@core-api/repositories/investment_and_return_repository';
import { getFirstMemberRacehorseInvestment, getMemberRacehorseInvestment } from '@core-api/repositories/member_racehorse_investment_repository';
import { updateInvestmentContractMonthlyDepreciationAndTransactionAmount } from '../repositories/investment_contract_repository';
import {
  calculateInvestorShare,
  calculateRunningCostInvestment,
  calculateMonthlyDepreciation,
  calculateRacehorseBookValue,
  sumAmounts,
  subtractAmounts,
} from '../utils/investment_calculation';
import { createInvestmentAndReturnInvestmentModel } from './models/investment_and_return_investment_model';
import { createInvestmentAndReturnModel } from './models/investment_and_return_model';
import { createInvestmentAndReturnReturnModel } from './models/investment_and_return_return_model';

// 定数
const TAX_RATE = 0.2042;
const INITIAL_DAY = 10;

// 型定義
interface BillingResult {
  totalEntrustAmount: number;
  totalInsuranceAmount: number;
  totalOtherAmount: number;
  totalSubsidyAmount: number;
  totalTaxAmount: number;
}

interface HorseInvestor {
  investmentContractId: number;
  memberId: number;
  memberName: string;
  investmentAmount: number;
  investmentAmountBeforeTax: number;
  contractedAt: Date | null;
  sharesNumber: number;
  taxRate: number;
  transactionAmount: number;
  monthlyDepreciation: number;
}

// listHorseInvestors の戻り値の型
interface HorseInvestorFromRepository {
  investmentContractId: number;
  memberId: number;
  memberNumber: number;
  memberName: string;
  contractedAt: Date | null;
  sharesNumber: number;
  investmentAmount: number;
  investmentAmountBeforeTax: number;
  taxRate: number;
  transactionAmount: number;
  monthlyDepreciation: number;
}

interface Horse {
  horseId: number;
  horseName: string | null;
  recruitmentName: string | null;
  birthYear: number;
  sharesTotal: number;
  fundStartYear: number;
  fundStartMonth: number;
  fundStartDay: number;
}

// ヘルパー関数
const createYearMonth = (date: Date): number => {
  return date.getFullYear() * 100 + date.getMonth() + 1;
};

const createDateFromYearMonth = (yearMonth: number): Date => {
  return new Date(yearMonth / 100, yearMonth % 100 - 1, INITIAL_DAY);
};


// ヘルパー関数
const calculateProgressedMonth = (
  retirementFlag: boolean,
  fundStartYear: number,
  fundStartMonth: number,
  fundStartDay: number,
  targetDate: Date,
  lastProgressedMonth: number
): number => {
  if (retirementFlag) {
    return 999;
  }
  const fundStartDate = new Date(fundStartYear, fundStartMonth - 1, fundStartDay);
  if (targetDate < fundStartDate) {
    return 0;
  } else {
    return lastProgressedMonth + 1;
  }
};

const calculateTransactionAmount = (horse: Horse, memberId: number, sharesNumber: number, targetDate: Date, investmentAmountBeforeTax: number): ResultAsync<{ transactionAmount: number, monthlyDepreciation: number }, Error> => {
  return ResultAsync.fromPromise(
    (async () => {
    // 先月までの維持費の税別部分の合計額を取得
    const totalBillingAmountBeforeTax = await getBillingTotalBeforeTax(horse.horseId, createYearMonth(targetDate));
    if (totalBillingAmountBeforeTax.isErr()) {
      throw new Error('Billing total before tax error');
    }
    const runningCostBeforeTax = calculateInvestorShare(totalBillingAmountBeforeTax.value.totalBillingAmountBeforeTax, horse.sharesTotal, sharesNumber);
    const transactionAmount = sumAmounts([investmentAmountBeforeTax, runningCostBeforeTax]);
    // 月次償却額を作成
    const monthlyDepreciation = calculateMonthlyDepreciation(transactionAmount);

    // 取得価格と月次償却額を更新して保存する
    await updateInvestmentContractMonthlyDepreciationAndTransactionAmount(
      horse.horseId,
      memberId,
      monthlyDepreciation,
      transactionAmount
    );
    return {
      transactionAmount,
      monthlyDepreciation,
    };
    })(),
    (error) => new Error(`Failed to calculate transaction amount: ${error}`)
  );
};

export const calculateProgressedMonthFromFundStartDate = (fundStartDate: Date, targetDate: Date): number => {
  if (targetDate < fundStartDate) {
    return 0;
  } else {
    // 年月の差を計算
    const yearDiff = targetDate.getFullYear() - fundStartDate.getFullYear();
    const monthDiff = targetDate.getMonth() - fundStartDate.getMonth();
    return yearDiff * 12 + monthDiff + 1;
  }
};

// 対象月の馬の支出を取得する関数
export const getBilling = async (horseId: number, billingYearMonth?: number): Promise<BillingResult> => {
  const horseBillingsResult = await listHorseBillings({
    horseId,
    billingYearMonth,
    closing: false,
  });

  if (horseBillingsResult.isErr() || horseBillingsResult.value === undefined) {
    return {
      totalEntrustAmount: 0,
      totalInsuranceAmount: 0,
      totalOtherAmount: 0,
      totalSubsidyAmount: 0,
      totalTaxAmount: 0,
    };
  }

  const horseBillings = horseBillingsResult.value;
  if (horseBillings.billings.length === 0) {
    return {
      totalEntrustAmount: 0,
      totalInsuranceAmount: 0,
      totalOtherAmount: 0,
      totalSubsidyAmount: 0,
      totalTaxAmount: 0,
    };
  }

  // 金額を合計する（高精度計算）
  const entrustAmounts: number[] = [];
  const insuranceAmounts: number[] = [];
  const otherAmounts: number[] = [];
  const subsidyAmounts: number[] = [];
  const taxAmounts: number[] = [];

  for (const horseBilling of horseBillings.billings) {
    switch (horseBilling.itemType) {
      case HorseBillingItemType.INSURANCE:
        insuranceAmounts.push(horseBilling.billingAmount);
        break;
      case HorseBillingItemType.OTHER:
        otherAmounts.push(horseBilling.billingAmount);
        break;
      case HorseBillingItemType.ENTRUST:
        entrustAmounts.push(horseBilling.billingAmount);
        subsidyAmounts.push(horseBilling.subsidyAmount);
        break;
    }
    taxAmounts.push(horseBilling.taxAmount);
  }

  const totalEntrustAmount = sumAmounts(entrustAmounts);
  const totalInsuranceAmount = sumAmounts(insuranceAmounts);
  const totalOtherAmount = sumAmounts(otherAmounts);
  const totalSubsidyAmount = sumAmounts(subsidyAmounts);
  const totalTaxAmount = sumAmounts(taxAmounts);

  return {
    totalEntrustAmount,
    totalInsuranceAmount,
    totalOtherAmount,
    totalSubsidyAmount,
    totalTaxAmount,
  };
};

// 対象月の馬の収入を取得する関数
export const getIncome = async (horseId: number, incomeYearMonth: number): Promise<{ totalIncomeAmount: number }> => {
  const horseIncomesResult = await listHorseIncomes({horseId});
  if (horseIncomesResult.isErr() || horseIncomesResult.value === undefined) {
    console.log('継続：horseIncomesResult error');
    return {
      totalIncomeAmount: 0,
    };
  }
  // incomeYearMonthに絞り込む
  const horseIncomes = horseIncomesResult.value.incomes.filter((income) => income.incomeYearMonth === incomeYearMonth);
  if (horseIncomes.length === 0) {
    return {
      totalIncomeAmount: 0,
    };
  }
  // 金額を合計する（高精度計算）
  const incomeAmounts = horseIncomes.map((income) => income.amount);
  const totalIncomeAmount = sumAmounts(incomeAmounts);
  return {
    totalIncomeAmount,
  };
};

// 対象月までの馬の収入を取得する関数
export const getIncomeToTargetMonth = async (
  horseId: number,
  incomeYearMonth: number
): Promise<{ totalIncomeAmount: number }> => {
  const horseIncomesResult = await listHorseIncomes({horseId});
  
  if (horseIncomesResult.isErr() || horseIncomesResult.value === undefined) {
    return { totalIncomeAmount: 0 };
  }
  
  // incomeYearMonthに絞り込む
  const horseIncomes = horseIncomesResult.value.incomes.filter(
    (income) => income.incomeYearMonth <= incomeYearMonth
  );
  
  if (horseIncomes.length === 0) {
    return { totalIncomeAmount: 0 };
  }
  
  // 金額を合計する（高精度計算）
  const incomeAmounts = horseIncomes.map((income) => income.amount);
  const totalIncomeAmount = sumAmounts(incomeAmounts);
  
  return { totalIncomeAmount };
};

// 遡り維持費出資金を計算する関数
const calculateRetroactiveRunningCost = async (
  horseId: number,
  targetYearMonth: number,
  sharesTotal: number,
  sharesNumber: number
): Promise<number> => {
  const horseBillings = await listHorseBillings({
    horseId,
    itemType: HorseBillingItemType.ENTRUST,
  });
  
  if (horseBillings.isErr()) {
    throw new Error('Billing result error');
  }
  
  const beforeTargetYearMonthBillings = horseBillings.value.billings.filter(
    (billing) => billing.billingYearMonth < targetYearMonth
  );
  
  const targetYearMonthBillingsGrouped = new Map<number, number[]>();
  beforeTargetYearMonthBillings.forEach((billing) => {
    const key = billing.billingYearMonth;
    if (!targetYearMonthBillingsGrouped.has(key)) {
      targetYearMonthBillingsGrouped.set(key, []);
    }
    const existingBillings = targetYearMonthBillingsGrouped.get(key);
    if (existingBillings) {
      existingBillings.push(billing.billingAmount);
    }
  });
  
  let retroactiveRunningCost = 0;
  targetYearMonthBillingsGrouped.forEach((billings) => {
    retroactiveRunningCost += sumAmounts(
      billings.map((billing) => Math.floor(billing / sharesTotal) * sharesNumber)
    );
  });
  
  return retroactiveRunningCost;
};

// 遡り保険料出資金を計算する関数
const calculateRetroactiveInsuranceInvestment = async (
  horseId: number,
  targetYearMonth: number
): Promise<number> => {
  const horseInsuranceBillings = await listHorseBillings({
    horseId,
    itemType: HorseBillingItemType.INSURANCE,
  });
  
  if (horseInsuranceBillings.isErr()) {
    throw new Error('Billing result error');
  }
  
  const targetYearMonthInsuranceBillings = horseInsuranceBillings.value.billings.filter(
    (billing) => billing.billingYearMonth < targetYearMonth
  );
  
  let retroactiveInsuranceInvestment = 0;
  targetYearMonthInsuranceBillings.forEach((billing) => {
    retroactiveInsuranceInvestment += billing.billingAmount;
  });
  
  return retroactiveInsuranceInvestment;
};

// 対象月の馬の源泉所得税額を取得する関数
export const getWithholdingTax = async (
  horseId: number,
  sharesTotal: number,
  sharesNumber: number,
  incomeYearMonth: number
): Promise<{ totalWithholdingTaxAmount: number }> => {
  const horseIncomesResult = await listHorseIncomePrize({
    horseId,
    incomeYearMonth,
  });
  if (horseIncomesResult.isErr() || horseIncomesResult.value === undefined) {
    return {
      totalWithholdingTaxAmount: 0,
    };
  }
  const horseIncomes = horseIncomesResult.value;
  if (horseIncomes.length === 0) {
    return {
      totalWithholdingTaxAmount: 0,
    };
  }
  // 金額を合計する
  const withholdingTaxAmounts: number[] = [];
  for (const horseIncome of horseIncomes) {
    const investorShare = calculateInvestorShare(horseIncome.withholdingTax, sharesTotal, sharesNumber);
    withholdingTaxAmounts.push(investorShare);
  }
  const totalWithholdingTaxAmount = sumAmounts(withholdingTaxAmounts);
  return {
    totalWithholdingTaxAmount,
  };
};

// この契約のgteからの「xxxx源泉税累積額／うち当月加算分」の合計額を返却する
export const getTotalWithholdingTaxByInvestmentContractIdAndDate = (
  horseId: number,
  memberId: number,
  lteDate: Date,
  gteDate?: Date
): ResultAsync<{ organizerWithholdingTaxTotal: number; clubWithholdingTaxTotal: number }, Error> => {
  return ResultAsync.fromPromise(
    (async () => {
      const investmentAndReturnList = await listInvestmentAndReturnByHorseIdAndMemberId(horseId, memberId);
      if (investmentAndReturnList.isErr()) {
        throw new Error('Investment and return list error');
      }
      const investmentAndReturnListValue = investmentAndReturnList.value;
      if (investmentAndReturnListValue.length === 0) {
        return {
          organizerWithholdingTaxTotal: 0,
          clubWithholdingTaxTotal: 0,
        };
      }
      // 金額を合計する
      let organizerWithholdingTaxTotal = 0;
      let clubWithholdingTaxTotal = 0;
      for (const investmentAndReturn of investmentAndReturnListValue) {
        if ((gteDate === undefined || investmentAndReturn.createdDate >= gteDate) && investmentAndReturn.createdDate <= lteDate) {
          organizerWithholdingTaxTotal += investmentAndReturn.organizerWithholdingTaxCurrentMonthAddition;
          clubWithholdingTaxTotal += investmentAndReturn.clubWithholdingTaxCurrentMonthAddition;
        }
      }
      return {
        organizerWithholdingTaxTotal,
        clubWithholdingTaxTotal,
      };
    })(),
    (error) => new Error(`Failed to get total withholding tax: ${error}`)
  );
};
// メイン関数
export const createInvestmentAndReturnUsecase = async (horseId: number, targetYear: number, targetMonth: number, yearlyReturnTargetFlag: boolean, retirementFlag: boolean) => {
  const targetYearMonth = createYearMonth(new Date(targetYear, targetMonth - 1, INITIAL_DAY));

  const horseResult = await findHorseById({ horseId });
  if (horseResult.isErr()) {
    return horseResult;
  }
  const horse = horseResult.value;

  const horseInvestorsResult = await listHorseInvestors({ horseId });
  if (horseInvestorsResult.isErr()) {
    return horseInvestorsResult;
  }
  const horseInvestors = horseInvestorsResult.value;
  if (retirementFlag) {
    const billingResult = await getBilling(horseId);
    if (billingResult.totalTaxAmount === undefined) {
      throw new Error('Billing result error');
    }
    const taxSettlementAmount = billingResult.totalTaxAmount;
    await createHorseIncomeOther({
      horseId: horseId,
      incomeYearMonth: targetYearMonth,
      occurredYear: targetYear,
      occurredMonth: targetMonth,
      occurredDay: INITIAL_DAY,
      name: HorseIncomeOtherName.OTHER,
      nameOther: '消費税精算金',
      amount: taxSettlementAmount,
      salesCommission: 0,
      otherFeeName: '',
      otherFeeAmount: 0,
      taxRate: '0',
      taxAmount: 0,
      incomeAmount: taxSettlementAmount,
      note: '',
    });
  }
  // 各契約ごとの処理（並列処理）
  const processInvestor = async (investor: HorseInvestorFromRepository) => {
    try {
      const investmentAndReturnsResult = await listInvestmentAndReturnByHorseIdAndMemberId(horseId, investor.memberId);
      if (investmentAndReturnsResult.isErr()) {
        return null;
      }
      const investmentAndReturns = investmentAndReturnsResult.value;

      const investmentAndReturnData = {
        memberId: investor.memberId,
        memberName: investor.memberName,
        investmentAndReturns,
      };

      // HorseInvestorインターフェースに変換
      const horseInvestor: HorseInvestor = {
        investmentContractId: investor.investmentContractId,
        memberId: investor.memberId,
        memberName: investor.memberName,
        investmentAmount: investor.investmentAmount,
        investmentAmountBeforeTax: investor.investmentAmountBeforeTax,
        contractedAt: investor.contractedAt || new Date(),
        sharesNumber: investor.sharesNumber,
        taxRate: investor.taxRate,
        transactionAmount: investor.transactionAmount,
        monthlyDepreciation: investor.monthlyDepreciation,
      };

      if (investmentAndReturns.length === 0) {
        // 初回作成
        await createInitialInvestmentAndReturn(investor.memberId, horse, horseInvestor, targetYearMonth);
      } else {
        // 継続作成
        const lastInvestmentAndReturn = investmentAndReturns[investmentAndReturns.length - 1];

        await createContinuingInvestmentAndReturn(
          horseId,
          horse,
          horseInvestor,
          targetYearMonth,
          yearlyReturnTargetFlag,
          retirementFlag,
          lastInvestmentAndReturn
        );
      }

      return investmentAndReturnData;
    } catch (error) {
      console.error(`出資者 ${investor.memberId} のデータ取得エラー:`, error);
      return null;
    }
  };

  // 全ての投資家を並列処理
  const results = await Promise.allSettled(horseInvestors.map((investor) => processInvestor(investor)));

  // 成功した結果のみを収集
  const investmentAndReturnsData = results
    .filter(
      (
        result
      ): result is PromiseFulfilledResult<{ memberId: number; memberName: string; investmentAndReturns: InvestmentAndReturn[] } | null> =>
        result.status === 'fulfilled' && result.value !== null
    )
    .map((result) => result.value);

  return {
    success: true,
    horseId,
    horseName: horse.horseName || horse.recruitmentName,
    investorsCount: horseInvestors.length,
    investmentAndReturnsData,
  };
};

// 初回出資金・分配金作成
const createInitialInvestmentAndReturn = async (
  memberId: number,
  horse: Horse,
  investor: HorseInvestor,
  targetYearMonth: number,
): Promise<void> => {
  const memberFirstRacehorseInvestment = await getFirstMemberRacehorseInvestment(memberId, horse.horseId);
  if (memberFirstRacehorseInvestment === null) {
    throw new Error('Member racehorse investment not found');
  }
  const firstCreatedDate = memberFirstRacehorseInvestment.investmentDate;
  const targetDate = createDateFromYearMonth(targetYearMonth);
  if (targetDate < firstCreatedDate) {
    return;
  }

  const billingResult = await getBilling(horse.horseId, createYearMonth(firstCreatedDate));
  if (billingResult.totalEntrustAmount === undefined) {
    throw new Error('Billing result error');
  }

  // これまでの維持費出資金の合計額を計算する
  const retroactiveRunningCost = await calculateRetroactiveRunningCost(
    horse.horseId,
    targetYearMonth,
    horse.sharesTotal,
    investor.sharesNumber
  );

  // これまでの保険料出資金の合計額を計算する
  const retroactiveInsuranceInvestment = await calculateRetroactiveInsuranceInvestment(
    horse.horseId,
    targetYearMonth
  );
  
  const investmentAndReturnModel = createInvestmentAndReturnModel({
    horseId: horse.horseId,
    memberId: investor.memberId,
  });
  investmentAndReturnModel.createdDate = firstCreatedDate;
  investmentAndReturnModel.yearlyReturnTargetFlag = false;

  // ファンド開始日の直後の10日がprogressedMonth=1なので、それを基準にprogressedMonthを計算する
  const fundStartDate = new Date(horse.fundStartYear, horse.fundStartMonth - 1, horse.fundStartDay);
  const progressedMonth = calculateProgressedMonthFromFundStartDate(fundStartDate, firstCreatedDate);
  
  // 競走馬の前月末簿価を計算
  if (progressedMonth > 48) {
    investmentAndReturnModel.racehorseBookValueEndOfLastMonth = 1;
  } else if (progressedMonth > 0) {
    investmentAndReturnModel.progressedMonth = progressedMonth;
    const transactionAmountResult = await calculateTransactionAmount(
      horse,
      investor.memberId,
      investor.sharesNumber,
      fundStartDate,
      investor.investmentAmountBeforeTax
    );
    
    if (transactionAmountResult.isErr()) {
      throw new Error('Transaction amount result error');
    }
    
    const { transactionAmount, monthlyDepreciation } = transactionAmountResult.value;
    investmentAndReturnModel.racehorseBookValueEndOfLastMonth = calculateRacehorseBookValue(
      transactionAmount,
      monthlyDepreciation,
      investmentAndReturnModel.progressedMonth - 1
    );
  } else {
    investmentAndReturnModel.racehorseBookValueEndOfLastMonth = 0;
  }  


  const investmentAndReturnInvestmentModel = createInvestmentAndReturnInvestmentModel({
    sharesTotal: horse.sharesTotal,
    discountAllocation: 0,
    racehorseInvestment: investor.investmentAmount,
    runningCost: billingResult.totalEntrustAmount,
    subsidy: billingResult.totalSubsidyAmount,
    retroactiveRunningCost: retroactiveRunningCost,
    insuranceInvestment: retroactiveInsuranceInvestment,
    otherInvestment: billingResult.totalOtherAmount,
  });
  investmentAndReturnModel.runningCostInvestmentTotal = investmentAndReturnInvestmentModel.runningCostInvestment;
  investmentAndReturnModel.insuranceInvestmentTotal = investmentAndReturnInvestmentModel.insuranceInvestment;
  investmentAndReturnModel.otherInvestmentTotal = investmentAndReturnInvestmentModel.otherInvestment;
  investmentAndReturnModel.investmentTotal = memberFirstRacehorseInvestment.racehorseInvestment + investmentAndReturnModel.runningCostInvestmentTotal + investmentAndReturnModel.insuranceInvestmentTotal + investmentAndReturnModel.otherInvestmentTotal;
  investmentAndReturnModel.billingAmount = investmentAndReturnModel.investmentTotal;


  /// 分配金作成
  let refundableInvestmentAmount = 0;
  if (progressedMonth > 0) {
    refundableInvestmentAmount = subtractAmounts(
      investmentAndReturnModel.investmentTotal,
      sumAmounts([
        investmentAndReturnModel.racehorseBookValueEndOfLastMonth,
        0,
        0,
      ])
    );
    }
  // 収入を取得
  const incomeResult = await getIncomeToTargetMonth(horse.horseId, createYearMonth(targetDate));
  const incomeAmount = incomeResult.totalIncomeAmount;
  let distributionTargetAmount = calculateInvestorShare(incomeAmount, horse.sharesTotal, investor.sharesNumber);

  // クラブ法人から愛馬会法人への分配金
  const investmentAndReturnReturnModelClubToFundMonthly = createInvestmentAndReturnReturnModel({
    returnCategory: ReturnCategory.CLUB_TO_FUND_MONTHLY,
    investmentRefundPaidUpToLastMonth: 0,
    refundableInvestmentAmount: refundableInvestmentAmount,
    distributionTargetAmount: distributionTargetAmount,
    taxRate: TAX_RATE,
  });
  investmentAndReturnModel.clubWithholdingTaxCurrentMonthAddition =
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmountWithholdingTax;
  investmentAndReturnModel.clubWithholdingTaxTotal = sumAmounts([
    investmentAndReturnModel.clubWithholdingTaxTotal,
    investmentAndReturnModel.clubWithholdingTaxCurrentMonthAddition,
  ]);

  // 愛馬会法人から会員様への分配金
  const distributionTargetAmountRefundableFundToMemberMonthly =
    await getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate(
      horse.horseId,
      investor.memberId,
      targetDate,
      ReturnCategory.FUND_TO_MEMBER_MONTHLY
    );
  if (distributionTargetAmountRefundableFundToMemberMonthly.isErr()) {
    throw new Error('Distribution target amount refundable error');
  }
  const distributionTargetAmountRefundableValueFundToMemberMonthly = distributionTargetAmountRefundableFundToMemberMonthly.value;


  let refundableInvestmentAmountFundToMemberMonthly = 0;
  if (investmentAndReturnModel.progressedMonth > 0) {
    refundableInvestmentAmountFundToMemberMonthly =
      investmentAndReturnModel.investmentTotal -
      investmentAndReturnModel.racehorseBookValueEndOfLastMonth -
      distributionTargetAmountRefundableValueFundToMemberMonthly;
  }
  const investmentAndReturnReturnModelFundToMemberMonthly = createInvestmentAndReturnReturnModel({
    returnCategory: ReturnCategory.FUND_TO_MEMBER_MONTHLY,
    investmentRefundPaidUpToLastMonth:
      distributionTargetAmountRefundableValueFundToMemberMonthly,
    refundableInvestmentAmount: refundableInvestmentAmountFundToMemberMonthly,
    distributionTargetAmount: investmentAndReturnReturnModelClubToFundMonthly.distributionAmount,
    taxRate: TAX_RATE,
  });
  investmentAndReturnModel.paymentAmount = investmentAndReturnReturnModelFundToMemberMonthly.distributionAmount;

  /// 分配金作成
  const investmentAndReturnResult = await createInvestmentAndReturn(
    horse.horseId,
    memberId,
    investmentAndReturnModel.createdDate,
    progressedMonth,
    investmentAndReturnModel.yearlyReturnTargetFlag,
    investor.sharesNumber,
    investmentAndReturnModel.runningCostInvestmentTotal,
    investmentAndReturnModel.insuranceInvestmentTotal,
    investmentAndReturnModel.otherInvestmentTotal,
    investmentAndReturnModel.investmentTotal,
    investmentAndReturnModel.racehorseBookValueEndOfLastMonth,
    investmentAndReturnModel.investmentRefundPaidUpToLastMonth,
    investmentAndReturnModel.organizerWithholdingTaxTotal,
    investmentAndReturnModel.organizerWithholdingTaxCurrentMonthAddition,
    investmentAndReturnModel.clubWithholdingTaxTotal,
    investmentAndReturnModel.clubWithholdingTaxCurrentMonthAddition,
    investmentAndReturnModel.billingAmount,
    investmentAndReturnModel.paymentAmount
  );

  if (investmentAndReturnResult.isErr()) {
    throw new Error('Investment and return result error');
  }

  const investmentAndReturn = investmentAndReturnResult.value;
  await createInvestmentAndReturnReturn(
    investmentAndReturn.investmentAndReturnId,
    ReturnCategory.CLUB_TO_FUND_MONTHLY,
    investmentAndReturnReturnModelClubToFundMonthly.investmentRefundPaidUpToLastMonth,
    investmentAndReturnReturnModelClubToFundMonthly.refundableInvestmentAmount,
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmount,
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmountRefundable,
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmountProfit,
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmountWithholdingTax,
    investmentAndReturnReturnModelClubToFundMonthly.distributionAmount,
    investmentAndReturnReturnModelClubToFundMonthly.refundableInvestmentAmountCarriedForward
  );
  await createInvestmentAndReturnReturn(
    investmentAndReturn.investmentAndReturnId,
    ReturnCategory.FUND_TO_MEMBER_MONTHLY,
    investmentAndReturnReturnModelFundToMemberMonthly.investmentRefundPaidUpToLastMonth,
    investmentAndReturnReturnModelFundToMemberMonthly.refundableInvestmentAmount,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionTargetAmount,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionTargetAmountRefundable,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionTargetAmountProfit,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionTargetAmountWithholdingTax,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionAmount,
    investmentAndReturnReturnModelFundToMemberMonthly.refundableInvestmentAmountCarriedForward
  );

  const investmentAndReturnInvestmentResult = await createInvestmentAndReturnInvestment(
    investmentAndReturn.investmentAndReturnId,
    investmentAndReturnInvestmentModel.racehorseInvestment,
    investmentAndReturnInvestmentModel.runningCost,
    investmentAndReturnInvestmentModel.subsidy,
    investmentAndReturnInvestmentModel.insuranceInvestment,
    investmentAndReturnInvestmentModel.otherInvestment,
    investmentAndReturnInvestmentModel.discountAllocation,
    investmentAndReturnInvestmentModel.retroactiveRunningCost,
    investmentAndReturnInvestmentModel.runningCostInvestment,
    investmentAndReturnInvestmentModel.currentMonthInvestmentTotal,
    investmentAndReturnInvestmentModel.racehorseInvestmentEquivalent
  );

  if (investmentAndReturnInvestmentResult.isErr()) {
    throw new Error('Investment and return investment result error');
  }
};

// 継続出資金・分配金作成
const createContinuingInvestmentAndReturn = async (
  horseId: number,
  horse: Horse,
  investor: HorseInvestor,
  targetYearMonth: number,
  yearlyReturnTargetFlag: boolean,
  retirementFlag: boolean,
  lastInvestmentAndReturn: {
    progressedMonth: number;
    runningCostInvestmentTotal: number;
    insuranceInvestmentTotal: number;
    otherInvestmentTotal: number;
    billingAmount: number | null;
    paymentAmount: number | null;
  }
): Promise<void> => {
  const targetDate = createDateFromYearMonth(targetYearMonth);
  let memberRacehorseInvestment = 0;
  const memberRacehorseInvestmentRecord = await getMemberRacehorseInvestment(investor.memberId, horse.horseId, targetDate);
  if (memberRacehorseInvestmentRecord !== null) {
    memberRacehorseInvestment = memberRacehorseInvestmentRecord.racehorseInvestment;
  }

  const billingResult = await getBilling(horseId, createYearMonth(targetDate));

  if (billingResult.totalEntrustAmount === undefined) {
    throw new Error('Billing result error');
  }

  const investmentAndReturnModel = createInvestmentAndReturnModel({
    horseId,
    memberId: investor.memberId,
  });

  const totalRacehorseInvestment = await getTotalRacehorseInvestmentByHorseIdAndMemberIdAndDate(
    horseId,
    investor.memberId,
    targetDate
  );
  if (totalRacehorseInvestment.isErr()) {
    throw new Error('Total racehorse investment error');
  }
  const totalRacehorseInvestmentValue = totalRacehorseInvestment.value;

  // モデル更新
  investmentAndReturnModel.createdDate = targetDate;
  investmentAndReturnModel.yearlyReturnTargetFlag = yearlyReturnTargetFlag;

  const progressedMonth = calculateProgressedMonth(
    retirementFlag,
    horse.fundStartYear,
    horse.fundStartMonth,
    horse.fundStartDay,
    targetDate,
    lastInvestmentAndReturn.progressedMonth
  );
  investmentAndReturnModel.progressedMonth = progressedMonth;
  // 今月の維持費を計算（遡りは継続の中では考慮しない）
  const entrustAmount = calculateInvestorShare(billingResult.totalEntrustAmount, horse.sharesTotal, investor.sharesNumber);
  // 今月の維持費補助金を計算
  const subsidyAmount = calculateInvestorShare(billingResult.totalSubsidyAmount, horse.sharesTotal, investor.sharesNumber);
  // 今月の維持費出資金を計算
  const runningCostInvestment = calculateRunningCostInvestment(entrustAmount, subsidyAmount);
  let transactionAmount = investor.transactionAmount;
  if (investmentAndReturnModel.progressedMonth === 1) {
    // progressedMonthが1の場合は取得価格を計算する
    const transactionAmountResult = await calculateTransactionAmount(
      horse,
      investor.memberId,
      investor.sharesNumber,
      targetDate,
      investor.investmentAmountBeforeTax
    );
    
    if (transactionAmountResult.isErr()) {
      throw new Error('Transaction amount result error');
    }
  }

  // 今月の保険料出資金を計算
  const insuranceInvestment = calculateInvestorShare(billingResult.totalInsuranceAmount, horse.sharesTotal, investor.sharesNumber);
  // 今月のその他出資金を計算
  const otherInvestment = calculateInvestorShare(billingResult.totalOtherAmount, horse.sharesTotal, investor.sharesNumber);

  // 維持費出資金累計額を計算
  investmentAndReturnModel.runningCostInvestmentTotal = sumAmounts([
    lastInvestmentAndReturn.runningCostInvestmentTotal,
    runningCostInvestment,
  ]);
  // 保険料出資金累計額を計算
  investmentAndReturnModel.insuranceInvestmentTotal = sumAmounts([lastInvestmentAndReturn.insuranceInvestmentTotal, insuranceInvestment]);
  // その他出資金累計額を計算
  investmentAndReturnModel.otherInvestmentTotal = sumAmounts([lastInvestmentAndReturn.otherInvestmentTotal, otherInvestment]);

  investmentAndReturnModel.investmentTotal = sumAmounts([
    totalRacehorseInvestmentValue,
    memberRacehorseInvestment,
    investmentAndReturnModel.runningCostInvestmentTotal,
    investmentAndReturnModel.insuranceInvestmentTotal,
    investmentAndReturnModel.otherInvestmentTotal,
  ]);

  // 今月の源泉所得税額を計算
  const withholdingTax = await getWithholdingTax(horseId, horse.sharesTotal, investor.sharesNumber, createYearMonth(targetDate));
  investmentAndReturnModel.organizerWithholdingTaxCurrentMonthAddition = withholdingTax.totalWithholdingTaxAmount;
  // JRA/NAR源泉税累積額を計算
  const yearlyReturnTargetDateRange = getYearlyReturnTargetDateRange(horse, targetDate);

  const gteDate = yearlyReturnTargetDateRange.gteDate ?? undefined;
  const lteDate = yearlyReturnTargetDateRange.lteDate ?? undefined;

  const totalWithholdingTax = await getTotalWithholdingTaxByInvestmentContractIdAndDate(horseId, investor.memberId, lteDate, gteDate);
  if (totalWithholdingTax.isErr()) {
    throw new Error('Total withholding tax error');
  }
  const totalWithholdingTaxValue = totalWithholdingTax.value;
  investmentAndReturnModel.organizerWithholdingTaxTotal = sumAmounts([
    totalWithholdingTaxValue.organizerWithholdingTaxTotal,
    withholdingTax.totalWithholdingTaxAmount,
  ]);
  investmentAndReturnModel.clubWithholdingTaxTotal = totalWithholdingTaxValue.clubWithholdingTaxTotal;

  // 今月の出資金を計算
  investmentAndReturnModel.billingAmount = sumAmounts([runningCostInvestment, insuranceInvestment, otherInvestment]);

  // 競走馬の前月末簿価を計算
  if (investmentAndReturnModel.progressedMonth > 48) {
    investmentAndReturnModel.racehorseBookValueEndOfLastMonth = 1;
  } else if (investmentAndReturnModel.progressedMonth > 0) {
    investmentAndReturnModel.racehorseBookValueEndOfLastMonth = calculateRacehorseBookValue(
      transactionAmount,
      investor.monthlyDepreciation,
      investmentAndReturnModel.progressedMonth - 1
    );
  } else {
    investmentAndReturnModel.racehorseBookValueEndOfLastMonth = 0;
  }

  // 分配金を作成
  // 出資返戻可能額を計算
  const distributionTargetAmountRefundable = await getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate(
    horseId,
    investor.memberId,
    targetDate,
    ReturnCategory.CLUB_TO_FUND_MONTHLY
  );
  if (distributionTargetAmountRefundable.isErr()) {
    throw new Error('Distribution target amount refundable error');
  }
  const distributionTargetAmountRefundableValue = distributionTargetAmountRefundable.value;

  // 愛馬会法人から会員様への分配金（年次）
  const distributionTargetAmountRefundableYearly = await getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate(
    horseId,
    investor.memberId,
    targetDate,
    ReturnCategory.CLUB_TO_FUND_YEARLY
  );
  if (distributionTargetAmountRefundableYearly.isErr()) {
    throw new Error('Distribution target amount refundable error');
  }
  const distributionTargetAmountRefundableValueYearly = distributionTargetAmountRefundableYearly.value;

  let refundableInvestmentAmount = 0;
  if (retirementFlag) {
    refundableInvestmentAmount = 1;
  } else if (investmentAndReturnModel.progressedMonth > 0) {
    refundableInvestmentAmount = subtractAmounts(
      investmentAndReturnModel.investmentTotal,
      sumAmounts([
        investmentAndReturnModel.racehorseBookValueEndOfLastMonth,
        distributionTargetAmountRefundableValue,
        distributionTargetAmountRefundableValueYearly,
      ])
    );
  }

  investmentAndReturnModel.investmentRefundPaidUpToLastMonth = sumAmounts([
    distributionTargetAmountRefundableValue,
    distributionTargetAmountRefundableValueYearly,
  ]);


  // 収入を取得
  const incomeResult = await getIncome(horseId, createYearMonth(targetDate));
  const incomeAmount = incomeResult.totalIncomeAmount;
  let distributionTargetAmount = calculateInvestorShare(incomeAmount, horse.sharesTotal, investor.sharesNumber);
  if (retirementFlag) {
    distributionTargetAmount = sumAmounts([
      distributionTargetAmount,
      subtractAmounts(investor.investmentAmount, investor.investmentAmountBeforeTax),
    ]);
  }

  // クラブ法人から愛馬会法人への分配金
  const investmentAndReturnReturnModelClubToFundMonthly = createInvestmentAndReturnReturnModel({
    returnCategory: ReturnCategory.CLUB_TO_FUND_MONTHLY,
    investmentRefundPaidUpToLastMonth: distributionTargetAmountRefundableValue + distributionTargetAmountRefundableValueYearly,
    refundableInvestmentAmount: refundableInvestmentAmount,
    distributionTargetAmount: distributionTargetAmount,
    taxRate: TAX_RATE,
  });
  investmentAndReturnModel.clubWithholdingTaxCurrentMonthAddition =
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmountWithholdingTax;
  investmentAndReturnModel.clubWithholdingTaxTotal = sumAmounts([
    investmentAndReturnModel.clubWithholdingTaxTotal,
    investmentAndReturnModel.clubWithholdingTaxCurrentMonthAddition,
  ]);

  // 愛馬会法人から会員様への分配金
  const distributionTargetAmountRefundableFundToMemberMonthly =
    await getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate(
      horseId,
      investor.memberId,
      targetDate,
      ReturnCategory.FUND_TO_MEMBER_MONTHLY
    );
  if (distributionTargetAmountRefundableFundToMemberMonthly.isErr()) {
    throw new Error('Distribution target amount refundable error');
  }
  const distributionTargetAmountRefundableValueFundToMemberMonthly = distributionTargetAmountRefundableFundToMemberMonthly.value;

  const distributionTargetAmountRefundableFundToMemberYearlyOrganizer =
    await getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate(
      horseId,
      investor.memberId,
      targetDate,
      ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER
    );
  if (distributionTargetAmountRefundableFundToMemberYearlyOrganizer.isErr()) {
    throw new Error('Distribution target amount refundable error');
  }
  const distributionTargetAmountRefundableValueFundToMemberYearlyOrganizer =
    distributionTargetAmountRefundableFundToMemberYearlyOrganizer.value;

  const distributionTargetAmountRefundableFundToMemberYearlyClub =
    await getTotalDistributionTargetAmountRefundableByHorseIdAndMemberIdAndDate(
      horseId,
      investor.memberId,
      targetDate,
      ReturnCategory.FUND_TO_MEMBER_YEARLY_CLUB
    );
  if (distributionTargetAmountRefundableFundToMemberYearlyClub.isErr()) {
    throw new Error('Distribution target amount refundable error');
  }
  const distributionTargetAmountRefundableValueFundToMemberYearlyClub = distributionTargetAmountRefundableFundToMemberYearlyClub.value;

  let refundableInvestmentAmountFundToMemberMonthly = 0;
  if (retirementFlag) {
    refundableInvestmentAmountFundToMemberMonthly = 1;
  } else if (investmentAndReturnModel.progressedMonth > 0) {
    refundableInvestmentAmountFundToMemberMonthly =
      investmentAndReturnModel.investmentTotal -
      investmentAndReturnModel.racehorseBookValueEndOfLastMonth -
      distributionTargetAmountRefundableValueFundToMemberMonthly -
      distributionTargetAmountRefundableValueFundToMemberYearlyOrganizer -
      distributionTargetAmountRefundableValueFundToMemberYearlyClub;
  }
  const investmentAndReturnReturnModelFundToMemberMonthly = createInvestmentAndReturnReturnModel({
    returnCategory: ReturnCategory.FUND_TO_MEMBER_MONTHLY,
    investmentRefundPaidUpToLastMonth:
      distributionTargetAmountRefundableValueFundToMemberMonthly +
      distributionTargetAmountRefundableValueFundToMemberYearlyOrganizer +
      distributionTargetAmountRefundableValueFundToMemberYearlyClub,
    refundableInvestmentAmount: refundableInvestmentAmountFundToMemberMonthly,
    distributionTargetAmount: investmentAndReturnReturnModelClubToFundMonthly.distributionAmount,
    taxRate: TAX_RATE,
  });
  investmentAndReturnModel.paymentAmount = investmentAndReturnReturnModelFundToMemberMonthly.distributionAmount;

  // ここからDB登録
  const investmentAndReturnResult = await createInvestmentAndReturn(
    horseId,
    investor.memberId,
    investmentAndReturnModel.createdDate,
    investmentAndReturnModel.progressedMonth,
    investmentAndReturnModel.yearlyReturnTargetFlag,
    investor.sharesNumber,
    investmentAndReturnModel.runningCostInvestmentTotal,
    investmentAndReturnModel.insuranceInvestmentTotal,
    investmentAndReturnModel.otherInvestmentTotal,
    investmentAndReturnModel.investmentTotal,
    investmentAndReturnModel.racehorseBookValueEndOfLastMonth,
    investmentAndReturnModel.investmentRefundPaidUpToLastMonth,
    investmentAndReturnModel.organizerWithholdingTaxTotal,
    investmentAndReturnModel.organizerWithholdingTaxCurrentMonthAddition,
    investmentAndReturnModel.clubWithholdingTaxTotal,
    investmentAndReturnModel.clubWithholdingTaxCurrentMonthAddition,
    investmentAndReturnModel.billingAmount,
    investmentAndReturnModel.paymentAmount
  );

  if (investmentAndReturnResult.isErr()) {
    throw new Error('Investment and return result error');
  }

  const investmentAndReturn = investmentAndReturnResult.value;
  const investmentAndReturnInvestmentModel = createInvestmentAndReturnInvestmentModel({
    sharesTotal: horse.sharesTotal,
    discountAllocation: memberRacehorseInvestmentRecord?.discountAllocation ?? 0,
    racehorseInvestment: memberRacehorseInvestmentRecord?.racehorseInvestment ?? 0,
    runningCost: billingResult.totalEntrustAmount,
    subsidy: billingResult.totalSubsidyAmount,
    retroactiveRunningCost: 0,
    insuranceInvestment: billingResult.totalInsuranceAmount,
    otherInvestment: billingResult.totalOtherAmount,
  });

  const investmentAndReturnInvestmentResult = await createInvestmentAndReturnInvestment(
    investmentAndReturn.investmentAndReturnId,
    investmentAndReturnInvestmentModel.racehorseInvestment,
    investmentAndReturnInvestmentModel.runningCost,
    investmentAndReturnInvestmentModel.subsidy,
    investmentAndReturnInvestmentModel.insuranceInvestment,
    investmentAndReturnInvestmentModel.otherInvestment,
    investmentAndReturnInvestmentModel.discountAllocation,
    investmentAndReturnInvestmentModel.retroactiveRunningCost,
    investmentAndReturnInvestmentModel.runningCostInvestment,
    investmentAndReturnInvestmentModel.currentMonthInvestmentTotal,
    investmentAndReturnInvestmentModel.racehorseInvestmentEquivalent
  );
  if (investmentAndReturnInvestmentResult.isErr()) {
    throw new Error('Investment and return investment result error');
  }
  await createInvestmentAndReturnReturn(
    investmentAndReturn.investmentAndReturnId,
    ReturnCategory.CLUB_TO_FUND_MONTHLY,
    investmentAndReturnReturnModelClubToFundMonthly.investmentRefundPaidUpToLastMonth,
    investmentAndReturnReturnModelClubToFundMonthly.refundableInvestmentAmount,
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmount,
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmountRefundable,
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmountProfit,
    investmentAndReturnReturnModelClubToFundMonthly.distributionTargetAmountWithholdingTax,
    investmentAndReturnReturnModelClubToFundMonthly.distributionAmount,
    investmentAndReturnReturnModelClubToFundMonthly.refundableInvestmentAmountCarriedForward
  );
  await createInvestmentAndReturnReturn(
    investmentAndReturn.investmentAndReturnId,
    ReturnCategory.FUND_TO_MEMBER_MONTHLY,
    investmentAndReturnReturnModelFundToMemberMonthly.investmentRefundPaidUpToLastMonth,
    investmentAndReturnReturnModelFundToMemberMonthly.refundableInvestmentAmount,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionTargetAmount,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionTargetAmountRefundable,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionTargetAmountProfit,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionTargetAmountWithholdingTax,
    investmentAndReturnReturnModelFundToMemberMonthly.distributionAmount,
    investmentAndReturnReturnModelFundToMemberMonthly.refundableInvestmentAmountCarriedForward
  );
  // 年次分配金を作成
  if (yearlyReturnTargetFlag) {
    // 昨年分の源泉税累積額を計算
    const withholdingTaxTargetDate = new Date(
      targetDate.getFullYear() - (retirementFlag ? 0 : 1),
      targetDate.getMonth(),
      targetDate.getDate()
    );
    const _yearlyReturnTargetDateRange = getYearlyReturnTargetDateRange(horse, withholdingTaxTargetDate);

    const gteDate = _yearlyReturnTargetDateRange.gteDate ?? undefined;
    const lteDate = _yearlyReturnTargetDateRange.lteDate ?? undefined;

    const totalWithholdingTax = await getTotalWithholdingTaxByInvestmentContractIdAndDate(horseId, investor.memberId, lteDate, gteDate);
    if (totalWithholdingTax.isErr()) {
      throw new Error('Total withholding tax error');
    }
    const totalWithholdingTaxValue = totalWithholdingTax.value;

    // クラブ法人から愛馬会法人への分配金
    const investmentAndReturnReturnModelYearly = createInvestmentAndReturnReturnModel({
      returnCategory: ReturnCategory.CLUB_TO_FUND_YEARLY,
      refundableInvestmentAmount: investmentAndReturnReturnModelClubToFundMonthly.refundableInvestmentAmountCarriedForward,
      distributionTargetAmount: totalWithholdingTaxValue.organizerWithholdingTaxTotal,
      taxRate: TAX_RATE,
    });
    // キャロットの明細を見ると「クラブ法人から愛馬会法人への分配金」の分配金額が分配対象額と同じになっているため、分配金額を分配対象額と同じにする
    investmentAndReturnReturnModelYearly.distributionAmount = investmentAndReturnReturnModelYearly.distributionTargetAmount;
    await createInvestmentAndReturnReturn(
      investmentAndReturn.investmentAndReturnId,
      ReturnCategory.CLUB_TO_FUND_YEARLY,
      investmentAndReturnReturnModelYearly.investmentRefundPaidUpToLastMonth,
      investmentAndReturnReturnModelYearly.refundableInvestmentAmount,
      investmentAndReturnReturnModelYearly.distributionTargetAmount,
      investmentAndReturnReturnModelYearly.distributionTargetAmountRefundable,
      investmentAndReturnReturnModelYearly.distributionTargetAmountProfit,
      investmentAndReturnReturnModelYearly.distributionTargetAmountWithholdingTax,
      investmentAndReturnReturnModelYearly.distributionAmount,
      investmentAndReturnReturnModelYearly.refundableInvestmentAmountCarriedForward
    );

    // 愛馬会法人から会員様への分配金（JRA/NAR源泉税）
    const investmentAndReturnReturnModelFundToMemberYearlyOrganizer = createInvestmentAndReturnReturnModel({
      returnCategory: ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER,
      refundableInvestmentAmount: investmentAndReturnReturnModelFundToMemberMonthly.refundableInvestmentAmountCarriedForward,
      distributionTargetAmount: investmentAndReturnReturnModelYearly.distributionAmount,
      taxRate: TAX_RATE,
    });
    await createInvestmentAndReturnReturn(
      investmentAndReturn.investmentAndReturnId,
      ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER,
      investmentAndReturnReturnModelFundToMemberYearlyOrganizer.investmentRefundPaidUpToLastMonth,
      investmentAndReturnReturnModelFundToMemberYearlyOrganizer.refundableInvestmentAmount,
      investmentAndReturnReturnModelFundToMemberYearlyOrganizer.distributionTargetAmount,
      investmentAndReturnReturnModelFundToMemberYearlyOrganizer.distributionTargetAmountRefundable,
      investmentAndReturnReturnModelFundToMemberYearlyOrganizer.distributionTargetAmountProfit,
      investmentAndReturnReturnModelFundToMemberYearlyOrganizer.distributionTargetAmountWithholdingTax,
      investmentAndReturnReturnModelFundToMemberYearlyOrganizer.distributionAmount,
      investmentAndReturnReturnModelFundToMemberYearlyOrganizer.refundableInvestmentAmountCarriedForward
    );
    investmentAndReturnModel.paymentAmount += investmentAndReturnReturnModelFundToMemberYearlyOrganizer.distributionAmount;

    // 愛馬会法人から会員様への分配金（クラブ法人源泉税）
    const investmentAndReturnReturnModelFundToMemberYearlyClub = createInvestmentAndReturnReturnModel({
      returnCategory: ReturnCategory.FUND_TO_MEMBER_YEARLY_CLUB,
      refundableInvestmentAmount: investmentAndReturnReturnModelFundToMemberYearlyOrganizer.refundableInvestmentAmountCarriedForward,
      distributionTargetAmount: totalWithholdingTaxValue.clubWithholdingTaxTotal,
      taxRate: TAX_RATE,
    });
    await createInvestmentAndReturnReturn(
      investmentAndReturn.investmentAndReturnId,
      ReturnCategory.FUND_TO_MEMBER_YEARLY_CLUB,
      investmentAndReturnReturnModelFundToMemberYearlyClub.investmentRefundPaidUpToLastMonth,
      investmentAndReturnReturnModelFundToMemberYearlyClub.refundableInvestmentAmount,
      investmentAndReturnReturnModelFundToMemberYearlyClub.distributionTargetAmount,
      investmentAndReturnReturnModelFundToMemberYearlyClub.distributionTargetAmountRefundable,
      investmentAndReturnReturnModelFundToMemberYearlyClub.distributionTargetAmountProfit,
      investmentAndReturnReturnModelFundToMemberYearlyClub.distributionTargetAmountWithholdingTax,
      investmentAndReturnReturnModelFundToMemberYearlyClub.distributionAmount,
      investmentAndReturnReturnModelFundToMemberYearlyClub.refundableInvestmentAmountCarriedForward
    );
    investmentAndReturnModel.paymentAmount += investmentAndReturnReturnModelFundToMemberYearlyClub.distributionAmount;
  }
  await updatePaymentAmount(investmentAndReturn.investmentAndReturnId, investmentAndReturnModel.paymentAmount);
};

// horseIdに関して、birthYearから「年次分配の対象とするcreatedDate」の範囲を返却する
export function getYearlyReturnTargetDateRange(horseResult: Horse, createdDate: Date): { gteDate: Date | null; lteDate: Date } {
  let gteDate: Date | null = null;
  let lteDate: Date;
  // createdDateから馬の年齢を計算
  const horseAge = createdDate.getFullYear() - horseResult.birthYear;
  if (horseAge == 2) {
    // 2歳の場合は、今年末までの範囲を返却する
    lteDate = new Date(createdDate.getFullYear(), 11, 31);
  } else {
    // 3歳以上の場合は、昨年末から今年末までの範囲を返却する
    gteDate = new Date(createdDate.getFullYear() - 1, 11, 31);
    lteDate = new Date(createdDate.getFullYear(), 11, 31);
  }
  return { gteDate, lteDate };
}
