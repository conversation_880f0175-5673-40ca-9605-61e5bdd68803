import { ResultAsync, errAsync, ok } from 'neverthrow';
import type { MonthlyGmoProcessingBatch } from '@hami/prisma';
import { toString } from '@core-api/utils/decimal';
import { client } from '@core-api/utils/prisma';
import { config } from '../config/environment';
import {
  createGmoBankTransferClient,
  createGmoRemittanceClient,
  GmoApiError,
  GmoNetworkError,
  GmoTimeoutError,
} from '../external_apis/gmo_pg';
import { maskGmoSecrets } from '../external_apis/gmo_pg/utils';
import {
  getMonthlyGmoProcessingBatchByTargetDate,
  updateMonthlyGmoProcessingBatchStatus,
  updateMonthlyGmoProcessingRecordStatus,
  createMonthlyGmoProcessingLog,
  listPendingMonthlyGmoProcessingRecords,
  incrementMonthlyGmoProcessingRecordRetryCount,
  createMonthlyGmoProcessingBatchWithRecords,
  recalcMonthlyGmoProcessingBatchStats,
  type MonthlyGmoProcessingRecordWithRelations,
} from '../repositories/monthly_gmo_processing_repository';
import type { EntryTranBankaccountParams, ExecTranBankaccountParams } from '../external_apis/gmo_pg';

/**
 * 最大リトライ回数
 */
const MAX_RETRIES = 3;

/**
 * 月締めGMO処理の実行結果
 */
export interface MonthlyGmoProcessingResult {
  batchId: number;
  totalTransferCount: number;
  totalRemittanceCount: number;
  completedTransferCount: number;
  completedRemittanceCount: number;
  failedTransferCount: number;
  failedRemittanceCount: number;
  errors: string[];
}

/**
 * GMO振替クライアントを取得
 */
function getGmoBankTransferClient() {
  return createGmoBankTransferClient();
}

/**
 * GMO送金クライアントを取得
 */
function getGmoRemittanceClient() {
  return createGmoRemittanceClient();
}

/**
 * OrderIDを生成（月締め処理用）
 */
function generateOrderId(targetDate: Date, memberId: number, sequence: number): string {
  const dateStr = targetDate.toISOString().slice(0, 7).replace('-', ''); // YYYYMM
  const memberIdStr = memberId.toString().padStart(6, '0');
  const sequenceStr = sequence.toString().padStart(4, '0');
  return `MONTHLY_${dateStr}_${memberIdStr}_${sequenceStr}`;
}

/**
 * Bank IDを生成（送金処理用）
 * 13文字以内に収まるよう短縮形式を使用: B-YY-MM-mmmm-sss
 * B: Bank prefix (1文字)
 * YY: 年の下2桁 (2文字)
 * MM: 月 (2文字)
 * mmmm: memberIdの下4桁 (4文字)
 * sss: sequenceの下3桁 (3文字)
 * 最大長: 1+2+2+4+3 = 12文字
 */
function generateBankId(targetDate: Date, memberId: number, sequence: number): string {
  const year = targetDate.getFullYear().toString().slice(-2); // 年の下2桁
  const month = (targetDate.getMonth() + 1).toString().padStart(2, '0'); // 月（01-12）
  const memberIdStr = (memberId % 10000).toString().padStart(4, '0'); // memberIdの下4桁
  const sequenceStr = (sequence % 1000).toString().padStart(3, '0'); // sequenceの下3桁
  return `B${year}${month}${memberIdStr}${sequenceStr}`;
}

/**
 * Deposit IDを生成（送金処理用）
 */
function generateDepositId(targetDate: Date, memberId: number, sequence: number): string {
  const dateStr = targetDate.toISOString().slice(0, 7).replace('-', ''); // YYYYMM
  const memberIdStr = memberId.toString().padStart(6, '0');
  const sequenceStr = sequence.toString().padStart(4, '0');
  return `DEPOSIT_${dateStr}_${memberIdStr}_${sequenceStr}`;
}

/**
 * 振替指定日を取得（対象日以降で最も近い27日）
 */
function getNextTransferDate(targetDate: Date): Date {
  const targetYear = targetDate.getFullYear();
  const targetMonth = targetDate.getMonth();
  const targetDay = targetDate.getDate();

  // 同月内の27日が対象日以降であれば当月27日、そうでなければ翌月27日
  if (targetDay <= 27) {
    return new Date(targetYear, targetMonth, 27);
  }
  return new Date(targetYear, targetMonth + 1, 27);
}

/**
 * 月締めGMO処理を開始
 * 指定された対象月のMemberClaimAndPayデータを取得し、GMO処理バッチを作成
 */
export function startMonthlyGmoProcessing(targetDate: Date): ResultAsync<MonthlyGmoProcessingBatch, Error> {
  return ResultAsync.fromPromise(
    (async () => {
      // 既存のバッチが存在するかチェック
      const existingBatchResult = await getMonthlyGmoProcessingBatchByTargetDate(targetDate);
      if (existingBatchResult.isErr()) {
        throw new Error(`Failed to check existing batch: ${existingBatchResult.error}`);
      }

      const existingBatch = existingBatchResult.value;
      if (existingBatch && existingBatch.status !== 'FAILED' && existingBatch.status !== 'CANCELLED') {
        throw new Error(`Batch for ${targetDate.toISOString().split('T')[0]} already exists with status: ${existingBatch.status}`);
      }

      // 対象月のMemberClaimAndPayデータを取得
      const memberClaimAndPays = await client.memberClaimAndPay.findMany({
        where: {
          occurredDate: targetDate,
        },
        include: {
          member: {
            include: {
              bankAccountRegistrations: {
                where: {
                  isActive: true,
                  registrationStatus: 'SUCCESS',
                },
                take: 1,
              },
            },
          },
        },
      });

      // 振替対象（請求金額 > 0）と送金対象（支払い金額 > 0）を分類
      const transferTargets = memberClaimAndPays.filter((item) => item.claimAmount > 0 && item.member.bankAccountRegistrations.length > 0);

      const remittanceTargets = memberClaimAndPays.filter((item) => item.payAmount > 0 && item.member.bankAccountRegistrations.length > 0);

      // バッチと関連レコードを原子的に作成
      const batchResult = await createMonthlyGmoProcessingBatchWithRecords(
        targetDate,
        transferTargets.map((t) => ({ memberId: t.memberId, memberClaimAndPayId: t.memberClaimAndPayId, amount: t.claimAmount })),
        remittanceTargets.map((t) => ({ memberId: t.memberId, memberClaimAndPayId: t.memberClaimAndPayId, amount: t.payAmount }))
      );

      if (batchResult.isErr()) {
        throw batchResult.error;
      }

      return batchResult.value;
    })(),
    (error) => new Error(`Failed to start monthly GMO processing: ${error}`)
  );
}

/**
 * 月締めGMO処理を実行
 * バッチ内の処理待ち記録を順次処理
 */
export function executeMonthlyGmoProcessing(batchId: number): ResultAsync<MonthlyGmoProcessingResult, Error> {
  return ResultAsync.fromPromise(
    (async () => {
      try {
        // バッチを実行中状態に更新
        const updateResult = await updateMonthlyGmoProcessingBatchStatus(batchId, 'RUNNING');
        if (updateResult.isErr()) {
          throw new Error(`Failed to update batch status: ${updateResult.error}`);
        }

        const result: MonthlyGmoProcessingResult = {
          batchId,
          totalTransferCount: 0,
          totalRemittanceCount: 0,
          completedTransferCount: 0,
          completedRemittanceCount: 0,
          failedTransferCount: 0,
          failedRemittanceCount: 0,
          errors: [],
        };

        // 振替処理を実行
        const transferResult = await processTransfers(batchId);
        if (transferResult.isErr()) {
          result.errors.push(`Transfer processing failed: ${transferResult.error.message}`);
        } else {
          result.totalTransferCount = transferResult.value.total;
          result.completedTransferCount = transferResult.value.completed;
          result.failedTransferCount = transferResult.value.failed;
        }

        // 送金処理を実行
        const remittanceResult = await processRemittances(batchId);
        if (remittanceResult.isErr()) {
          result.errors.push(`Remittance processing failed: ${remittanceResult.error.message}`);
        } else {
          result.totalRemittanceCount = remittanceResult.value.total;
          result.completedRemittanceCount = remittanceResult.value.completed;
          result.failedRemittanceCount = remittanceResult.value.failed;
        }

        // バッチ統計を更新（安全のためDB集計で再計算）
        await recalcMonthlyGmoProcessingBatchStats(batchId);

        // バッチ状態を更新
        const finalStatus = result.errors.length > 0 ? 'FAILED' : 'COMPLETED';
        const errorMessage = result.errors.length > 0 ? result.errors.join('; ') : undefined;

        await updateMonthlyGmoProcessingBatchStatus(batchId, finalStatus, errorMessage);

        return result;
      } catch (error) {
        // バッチを失敗状態に更新
        await updateMonthlyGmoProcessingBatchStatus(batchId, 'FAILED', String(error));
        // ここでは再throwし、fromPromiseのエラーマッパーで最終的なErrorに整形する
        throw error;
      }
    })(),
    (error) => new Error(`Failed to execute monthly GMO processing: ${error}`)
  );
}

/**
 * 振替処理を実行
 */
async function processTransfers(batchId: number): Promise<ResultAsync<{ total: number; completed: number; failed: number }, Error>> {
  const recordsResult = await listPendingMonthlyGmoProcessingRecords(batchId, 'BANK_TRANSFER');
  if (recordsResult.isErr()) {
    return errAsync(new Error(`Failed to get transfer records: ${recordsResult.error}`));
  }

  const records = recordsResult.value;
  let completed = 0;
  let failed = 0;

  for (const record of records) {
    try {
      // 実際のGMO振替処理を実行
      const transferResult = await processSingleBankTransfer(record);

      if (transferResult.isOk()) {
        // リトライ中はPENDINGのままなので、最新の状態をDBから読み直して判定する
        const latest = await client.monthlyGmoProcessingRecord.findUnique({
          where: { monthlyGmoProcessingRecordId: record.monthlyGmoProcessingRecordId },
          select: { status: true },
        });
        if (latest?.status === 'COMPLETED') {
          completed++;
        } else if (latest?.status === 'FAILED') {
          failed++;
        } // PENDING/その他は件数に加算しない
      } else {
        failed++;
      }
    } catch (error) {
      await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'FAILED', { errorMessage: String(error) });
      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'ERROR',
        `Transfer processing failed: ${error}`,
        undefined,
        undefined,
        undefined,
        'PENDING',
        'FAILED'
      );
      failed++;
    }
  }

  return ok({ total: records.length, completed, failed });
}

/**
 * 送金処理を実行
 */
async function processRemittances(batchId: number): Promise<ResultAsync<{ total: number; completed: number; failed: number }, Error>> {
  const recordsResult = await listPendingMonthlyGmoProcessingRecords(batchId, 'REMITTANCE');
  if (recordsResult.isErr()) {
    return errAsync(new Error(`Failed to get remittance records: ${recordsResult.error}`));
  }

  const records = recordsResult.value;
  let completed = 0;
  let failed = 0;

  for (const record of records) {
    try {
      // 実際のGMO送金処理を実行
      const remittanceResult = await processSingleRemittance(record);

      if (remittanceResult.isOk()) {
        completed++;
      } else {
        failed++;
      }
    } catch (error) {
      await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'FAILED', { errorMessage: String(error) });
      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'ERROR',
        `Remittance processing failed: ${error}`,
        undefined,
        undefined,
        undefined,
        'PENDING',
        'FAILED'
      );
      failed++;
    }
  }

  return ok({ total: records.length, completed, failed });
}

/**
 * リトライ可能なエラーかどうかを判定
 */
function isRetryableError(error: unknown): boolean {
  if (error instanceof GmoApiError) {
    // システムエラー系はリトライ可能
    if (['E01', 'E91', 'E92'].includes(error.errorCode)) {
      return true;
    }
    // 2重送信エラーや排他エラーはリトライ不要（既に処理済み）
    if (['E90', 'E82'].includes(error.errorCode)) {
      return false;
    }
    // その他のAPIエラーはリトライしない
    return false;
  }

  // ネットワークエラーやタイムアウトはリトライ可能
  if (error instanceof GmoNetworkError || error instanceof GmoTimeoutError) {
    return true;
  }

  return false;
}

/**
 * 処理記録の現在の状態を確認して、継続可能かチェック
 */
async function checkProcessingContinuity(recordId: number): Promise<{
  canContinue: boolean;
  currentStatus: string;
  shouldSkip: boolean;
}> {
  const record = await client.monthlyGmoProcessingRecord.findUnique({
    where: { monthlyGmoProcessingRecordId: recordId },
  });

  if (!record) {
    return { canContinue: false, currentStatus: 'NOT_FOUND', shouldSkip: false };
  }

  // 既に完了している場合はスキップ
  if (record.status === 'COMPLETED') {
    return { canContinue: false, currentStatus: record.status, shouldSkip: true };
  }

  // 失敗状態で最大リトライ回数に達している場合は継続不可
  if (record.status === 'FAILED' && record.retryCount >= MAX_RETRIES) {
    return { canContinue: false, currentStatus: record.status, shouldSkip: false };
  }

  return { canContinue: true, currentStatus: record.status, shouldSkip: false };
}

/**
 * 単一の振替処理を実行（リトライ機能付き）
 */
async function processSingleBankTransfer(record: MonthlyGmoProcessingRecordWithRelations): Promise<ResultAsync<void, Error>> {
  // 処理継続可能性をチェック
  const continuityCheck = await checkProcessingContinuity(record.monthlyGmoProcessingRecordId);

  if (!continuityCheck.canContinue) {
    if (continuityCheck.shouldSkip) {
      // 既に完了している場合はスキップ
      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'INFO',
        `Transfer already completed, skipping (status: ${continuityCheck.currentStatus})`,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined
      );
      return ok(undefined);
    } else {
      // 最大リトライ回数に達している場合
      const error = new Error(`Transfer processing failed after maximum retries (status: ${continuityCheck.currentStatus})`);
      return ResultAsync.fromPromise(Promise.reject(error), () => error);
    }
  }

  return await processSingleBankTransferWithRetry(record, continuityCheck.currentStatus);
}

/**
 * リトライ機能付きの振替処理実行
 */
async function processSingleBankTransferWithRetry(
  record: MonthlyGmoProcessingRecordWithRelations,
  _currentStatus: string
): Promise<ResultAsync<void, Error>> {
  try {
    // 会員の有効な口座登録を取得
    const bankAccount = await client.bankAccountRegistration.findFirst({
      where: {
        memberId: record.memberId,
        isActive: true,
        registrationStatus: 'SUCCESS',
      },
    });

    if (!bankAccount) {
      const error = new Error(`No active bank account found for member ${record.memberId}`);
      await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'FAILED', { errorMessage: error.message });
      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'ERROR',
        error.message,
        undefined,
        undefined,
        undefined,
        'PENDING',
        'FAILED'
      );
      return ResultAsync.fromPromise(Promise.reject(error), () => error);
    }

    // GMOクライアントを取得
    const gmoClient = getGmoBankTransferClient();

    // OrderIDを生成
    const orderId = generateOrderId(record.memberClaimAndPay.occurredDate, record.memberId, record.monthlyGmoProcessingRecordId);

    // 振替指定日を取得
    const transferDate = getNextTransferDate(record.memberClaimAndPay.occurredDate);

    // 1. EntryTranBankaccount APIで取引登録
    const entryParams: EntryTranBankaccountParams = {
      ShopID: config.gmoPg.shopId,
      ShopPass: config.gmoPg.shopPass,
      OrderID: orderId,
      Amount: toString(record.amount),
      Tax: '0', // 税額は別途計算が必要な場合は修正
    };

    await createMonthlyGmoProcessingLog(
      record.monthlyGmoProcessingRecordId,
      'INFO',
      'Starting GMO bank transfer entry',
      '/payment/EntryTranBankaccount.idPass',
      maskGmoSecrets(JSON.stringify(entryParams))
    );

    const entryResponse = await gmoClient.entryTransaction(entryParams);

    // GMO APIからAccessIDとAccessPassが返されたか検証
    if (!entryResponse.AccessID || !entryResponse.AccessPass) {
      const errorMessage = 'GMO API did not return AccessID or AccessPass in EntryTranBankaccount response';

      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'ERROR',
        errorMessage,
        '/payment/EntryTranBankaccount.idPass',
        maskGmoSecrets(JSON.stringify(entryParams)),
        JSON.stringify(entryResponse),
        'PENDING',
        'FAILED'
      );

      await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'FAILED', {
        errorMessage,
      });

      throw new Error(`${errorMessage}. Response: ${JSON.stringify(entryResponse)}`);
    }

    // 取引登録成功時の状態更新
    await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'ENTRY_REGISTERED', {
      gmoOrderId: orderId,
      gmoAccessId: entryResponse.AccessID,
      gmoAccessPass: entryResponse.AccessPass,
    });

    await createMonthlyGmoProcessingLog(
      record.monthlyGmoProcessingRecordId,
      'INFO',
      'GMO bank transfer entry completed',
      '/payment/EntryTranBankaccount.idPass',
      maskGmoSecrets(JSON.stringify(entryParams)),
      JSON.stringify(entryResponse)
    );

    // 2. ExecTranBankaccount APIで請求依頼
    const execParams: ExecTranBankaccountParams = {
      SiteID: config.gmoPg.siteId,
      SitePass: config.gmoPg.sitePass,
      AccessID: entryResponse.AccessID,
      AccessPass: entryResponse.AccessPass,
      OrderID: orderId,
      MemberID: `member_${record.memberId}`,
      TargetDate: transferDate.toISOString().slice(0, 10).replace(/-/g, ''), // YYYYMMDD形式
    };

    await createMonthlyGmoProcessingLog(
      record.monthlyGmoProcessingRecordId,
      'INFO',
      'Starting GMO bank transfer execution',
      '/payment/ExecTranBankaccount.idPass',
      maskGmoSecrets(JSON.stringify(execParams))
    );

    const execResponse = await gmoClient.executeTransfer(execParams);

    // 請求依頼成功: 中間状態 EXEC_REQUESTED を記録
    await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'EXEC_REQUESTED', {
      gmoTransactionId: execResponse.AccessID || orderId,
    });

    // 最終的に完了状態を記録
    await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'COMPLETED', {
      gmoTransactionId: execResponse.AccessID || orderId,
      targetDate: transferDate,
    });

    await createMonthlyGmoProcessingLog(
      record.monthlyGmoProcessingRecordId,
      'INFO',
      'GMO bank transfer execution completed',
      '/payment/ExecTranBankaccount.idPass',
      maskGmoSecrets(JSON.stringify(execParams)),
      JSON.stringify(execResponse)
    );

    return ok(undefined);
  } catch (error) {
    return await handleBankTransferError(record, error);
  }
}

/**
 * 振替処理エラーのハンドリング（リトライ機能付き）
 */
async function handleBankTransferError(record: MonthlyGmoProcessingRecordWithRelations, error: unknown): Promise<ResultAsync<void, Error>> {
  // エラー情報を解析
  let errorMessage = 'Unknown error';
  let errorCode = 'UNKNOWN';
  let errorDetail = '';

  if (error instanceof GmoApiError) {
    errorMessage = `GMO API Error: ${error.errorCode} - ${error.errorDetail}`;
    errorCode = error.errorCode;
    errorDetail = error.errorDetail;
  } else if (error instanceof GmoNetworkError) {
    errorMessage = `GMO Network Error: ${error.message}`;
    errorCode = 'NETWORK_ERROR';
  } else if (error instanceof GmoTimeoutError) {
    errorMessage = `GMO Timeout Error: ${error.message}`;
    errorCode = 'TIMEOUT_ERROR';
  } else {
    errorMessage = String(error);
  }

  // リトライ可能かチェック
  const canRetry = isRetryableError(error);

  if (canRetry) {
    // リトライ回数を増加
    await incrementMonthlyGmoProcessingRecordRetryCount(record.monthlyGmoProcessingRecordId);

    // リトライ回数を確認
    const updatedRecord = await client.monthlyGmoProcessingRecord.findUnique({
      where: { monthlyGmoProcessingRecordId: record.monthlyGmoProcessingRecordId },
    });

    if (updatedRecord && updatedRecord.retryCount < MAX_RETRIES) {
      // リトライ可能：状態をPENDINGに戻す
      await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'PENDING', {
        errorCode,
        errorDetail,
        errorMessage: `${errorMessage} (will retry: ${updatedRecord.retryCount + 1}/${MAX_RETRIES})`,
      });

      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'WARN',
        `${errorMessage} - Retrying (${updatedRecord.retryCount + 1}/${MAX_RETRIES})`,
        undefined,
        undefined,
        undefined,
        undefined,
        'PENDING'
      );

      // リトライのため、エラーとして扱わない
      return ok(undefined);
    }
  }

  // リトライ不可またはリトライ回数上限：失敗として記録
  await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'FAILED', {
    errorCode,
    errorDetail,
    errorMessage,
  });

  await createMonthlyGmoProcessingLog(
    record.monthlyGmoProcessingRecordId,
    'ERROR',
    canRetry ? `${errorMessage} - Max retries exceeded` : `${errorMessage} - Not retryable`,
    undefined,
    undefined,
    undefined,
    undefined,
    'FAILED'
  );

  return ResultAsync.fromPromise(Promise.reject(new Error(errorMessage)), () => new Error(errorMessage));
}

/**
 * 単一の送金処理を実行（リトライ機能付き）
 */
async function processSingleRemittance(record: MonthlyGmoProcessingRecordWithRelations): Promise<ResultAsync<void, Error>> {
  // 処理継続可能性をチェック
  const continuityCheck = await checkProcessingContinuity(record.monthlyGmoProcessingRecordId);

  if (!continuityCheck.canContinue) {
    if (continuityCheck.shouldSkip) {
      // 既に完了している場合はスキップ
      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'INFO',
        `Remittance already completed, skipping (status: ${continuityCheck.currentStatus})`,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined
      );
      return ok(undefined);
    } else {
      // 最大リトライ回数に達している場合
      const error = new Error(`Remittance processing failed after maximum retries (status: ${continuityCheck.currentStatus})`);
      return ResultAsync.fromPromise(Promise.reject(error), () => error);
    }
  }

  return await processSingleRemittanceWithRetry(record, continuityCheck.currentStatus);
}

/**
 * リトライ機能付きの送金処理実行
 */
async function processSingleRemittanceWithRetry(
  record: MonthlyGmoProcessingRecordWithRelations,
  _currentStatus: string
): Promise<ResultAsync<void, Error>> {
  try {
    // 会員の有効な口座登録を取得
    const bankAccount = await client.bankAccountRegistration.findFirst({
      where: {
        memberId: record.memberId,
        isActive: true,
        registrationStatus: 'SUCCESS',
      },
    });

    if (!bankAccount) {
      const error = new Error(`No active bank account found for member ${record.memberId}`);
      await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'FAILED', { errorMessage: error.message });
      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'ERROR',
        error.message,
        undefined,
        undefined,
        undefined,
        'PENDING',
        'FAILED'
      );
      return ResultAsync.fromPromise(Promise.reject(error), () => error);
    }

    // 必須の口座情報が揃っているか検証
    if (!bankAccount.branchCode || !bankAccount.accountNumber || !bankAccount.accountName) {
      const missingFields = [];
      if (!bankAccount.branchCode) missingFields.push('branchCode');
      if (!bankAccount.accountNumber) missingFields.push('accountNumber');
      if (!bankAccount.accountName) missingFields.push('accountName');

      const errorMessage = `Bank account missing required fields: ${missingFields.join(', ')} for member ${record.memberId}`;

      await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'FAILED', {
        errorMessage,
      });

      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'ERROR',
        errorMessage,
        undefined,
        undefined,
        undefined,
        'PENDING',
        'FAILED'
      );

      return ResultAsync.fromPromise(Promise.reject(new Error(errorMessage)), () => new Error(errorMessage));
    }

    // GMO送金クライアントを取得
    const gmoClient = getGmoRemittanceClient();

    // Bank IDとDeposit IDを生成
    const bankId = generateBankId(record.memberClaimAndPay.occurredDate, record.memberId, record.monthlyGmoProcessingRecordId);

    const depositId = generateDepositId(record.memberClaimAndPay.occurredDate, record.memberId, record.monthlyGmoProcessingRecordId);

    // 1. AccountRegistration APIで口座登録
    const accountParams = {
      ShopID: config.gmoPg.shopId,
      ShopPass: config.gmoPg.shopPass,
      BankID: bankId,
      BankCode: bankAccount.bankCode,
      BranchCode: bankAccount.branchCode,
      AccountType: bankAccount.accountType === 1 ? ('1' as const) : ('2' as const), // 1:普通、2:当座
      AccountNumber: bankAccount.accountNumber,
      AccountName: bankAccount.accountName,
      Free: `月締め処理 ${record.memberClaimAndPay.occurredDate.toISOString().slice(0, 7)}`,
    };

    await createMonthlyGmoProcessingLog(
      record.monthlyGmoProcessingRecordId,
      'INFO',
      'Starting GMO account registration',
      '/api/AccountRegistration.idPass',
      JSON.stringify(accountParams)
    );

    const accountResponse = await gmoClient.registerAccount(accountParams);

    if (accountResponse.ErrCode) {
      throw new GmoApiError(accountResponse.ErrCode, accountResponse.ErrInfo || '');
    }

    // 口座登録成功時の状態更新
    await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'ENTRY_REGISTERED', {
      gmoBankId: bankId,
    });

    await createMonthlyGmoProcessingLog(
      record.monthlyGmoProcessingRecordId,
      'INFO',
      'GMO account registration completed',
      '/api/AccountRegistration.idPass',
      maskGmoSecrets(JSON.stringify(accountParams)),
      JSON.stringify(accountResponse)
    );
    // 送金金額の上限チェック（1,000,000円）
    const depositAmountNum = Number(toString(record.amount));
    if (depositAmountNum > 1000000) {
      const errorMessage = `Deposit amount exceeds the maximum allowed (amount=${depositAmountNum})`;
      await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'FAILED', {
        errorCode: 'VALIDATION_ERROR',
        errorDetail: 'Amount must be 1,000,000 or less',
        errorMessage,
      });
      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'ERROR',
        errorMessage,
        '/api/DepositRegistration.idPass',
        JSON.stringify({ DepositID: depositId, BankID: bankId, Amount: depositAmountNum }),
        undefined,
        'PENDING',
        'FAILED'
      );
      return ok(undefined);
    }

    // 2. DepositRegistration APIで送金指示登録
    const depositParams = {
      ShopID: config.gmoPg.shopId,
      ShopPass: config.gmoPg.shopPass,
      DepositID: depositId,
      BankID: bankId,
      Amount: toString(record.amount),
      SelectKey: `monthly_${record.memberClaimAndPay.occurredDate.toISOString().slice(0, 7)}`,
    };

    await createMonthlyGmoProcessingLog(
      record.monthlyGmoProcessingRecordId,
      'INFO',
      'Starting GMO deposit registration',
      '/api/DepositRegistration.idPass',
      maskGmoSecrets(JSON.stringify(depositParams))
    );

    const depositResponse = await gmoClient.registerDeposit(depositParams);

    if (depositResponse.ErrCode) {
      throw new GmoApiError(depositResponse.ErrCode, depositResponse.ErrInfo || '');
    }

    // 送金登録成功: 中間状態 DEPOSIT_REQUESTED を記録
    await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'DEPOSIT_REQUESTED', {
      gmoDepositId: depositId,
      gmoTransactionId: depositResponse.Deposit_ID || depositId,
    });

    // 最終的に完了状態を記録
    await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'COMPLETED', {
      gmoDepositId: depositId,
      gmoTransactionId: depositResponse.Deposit_ID || depositId,
    });

    await createMonthlyGmoProcessingLog(
      record.monthlyGmoProcessingRecordId,
      'INFO',
      'GMO deposit registration completed',
      '/api/DepositRegistration.idPass',
      maskGmoSecrets(JSON.stringify(depositParams)),
      JSON.stringify(depositResponse)
    );

    return ok(undefined);
  } catch (error) {
    return await handleRemittanceError(record, error);
  }
}

/**
 * 送金処理エラーのハンドリング（リトライ機能付き）
 */
async function handleRemittanceError(record: MonthlyGmoProcessingRecordWithRelations, error: unknown): Promise<ResultAsync<void, Error>> {
  // エラー情報を解析
  let errorMessage = 'Unknown error';
  let errorCode = 'UNKNOWN';
  let errorDetail = '';

  if (error instanceof GmoApiError) {
    errorMessage = `GMO API Error: ${error.errorCode} - ${error.errorDetail}`;
    errorCode = error.errorCode;
    errorDetail = error.errorDetail;
  } else if (error instanceof GmoNetworkError) {
    errorMessage = `GMO Network Error: ${error.message}`;
    errorCode = 'NETWORK_ERROR';
  } else if (error instanceof GmoTimeoutError) {
    errorMessage = `GMO Timeout Error: ${error.message}`;
    errorCode = 'TIMEOUT_ERROR';
  } else {
    errorMessage = String(error);
  }

  // リトライ可能かチェック
  const canRetry = isRetryableError(error);

  if (canRetry) {
    // リトライ回数を増加
    await incrementMonthlyGmoProcessingRecordRetryCount(record.monthlyGmoProcessingRecordId);

    // リトライ回数を確認
    const updatedRecord = await client.monthlyGmoProcessingRecord.findUnique({
      where: { monthlyGmoProcessingRecordId: record.monthlyGmoProcessingRecordId },
    });

    if (updatedRecord && updatedRecord.retryCount < MAX_RETRIES) {
      // リトライ可能：状態をPENDINGに戻す
      await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'PENDING', {
        errorCode,
        errorDetail,
        errorMessage: `${errorMessage} (will retry: ${updatedRecord.retryCount + 1}/${MAX_RETRIES})`,
      });

      await createMonthlyGmoProcessingLog(
        record.monthlyGmoProcessingRecordId,
        'WARN',
        `${errorMessage} - Retrying (${updatedRecord.retryCount + 1}/${MAX_RETRIES})`,
        undefined,
        undefined,
        undefined,
        undefined,
        'PENDING'
      );

      // リトライのため、エラーとして扱わない
      return ok(undefined);
    }
  }

  // リトライ不可またはリトライ回数上限：失敗として記録
  await updateMonthlyGmoProcessingRecordStatus(record.monthlyGmoProcessingRecordId, 'FAILED', {
    errorCode,
    errorDetail,
    errorMessage,
  });

  await createMonthlyGmoProcessingLog(
    record.monthlyGmoProcessingRecordId,
    'ERROR',
    canRetry ? `${errorMessage} - Max retries exceeded` : `${errorMessage} - Not retryable`,
    undefined,
    undefined,
    undefined,
    undefined,
    'FAILED'
  );

  return ResultAsync.fromPromise(Promise.reject(new Error(errorMessage)), () => new Error(errorMessage));
}
