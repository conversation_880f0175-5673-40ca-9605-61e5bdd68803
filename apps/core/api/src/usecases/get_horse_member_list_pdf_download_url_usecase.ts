import { ResultAsync } from 'neverthrow';
import { getPresignedDownloadUrl } from '@core-api/utils/s3';

/**
 * PDFダウンロードURL取得結果の型
 */
interface GetDownloadUrlResult {
  downloadUrl: string;
  expiresAt: bigint;
}

/**
 * ファイルキーの検証
 */
const validateFileKey = (fileKey: string): boolean => {
  // reports/horse-member-list-*.pdf の形式をチェック
  const pattern = /^reports\/horse-member-list-\d+\.pdf$/;
  return pattern.test(fileKey);
};

/**
 * クラブ所有馬別会員一覧PDFダウンロードURL取得usecase
 */
export const getHorseMemberListPdfDownloadUrlUsecase = (
  fileKey: string
): ResultAsync<GetDownloadUrlResult, Error> => {
  return ResultAsync.fromPromise(
    (async () => {
      // 1. ファイルキーの検証
      if (!validateFileKey(fileKey)) {
        throw new Error('Invalid file key format');
      }

      // 2. S3署名付きURL生成（1時間有効）
      const expiresIn = 3600; // 1時間
      const downloadUrl = await getPresignedDownloadUrl(fileKey, expiresIn);
      
      // 3. 結果返却
      return {
        downloadUrl,
        expiresAt: BigInt(Date.now() + expiresIn * 1000),
      };
    })(),
    (error) => {
      console.error('Failed to generate download URL:', error);
      return error instanceof Error ? error : new Error('Unknown error occurred during URL generation');
    }
  );
};
