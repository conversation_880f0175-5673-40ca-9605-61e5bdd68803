import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { ResultAsync } from 'neverthrow';
import PDFDocument from 'pdfkit-table';
import { InvestmentAndReturn, MemberClaimAndPay, MemberClaim, Horse, InvestmentAndReturnInvestment, InvestmentAndReturnReturn, ReturnCategory } from '@hami/prisma';
import { uploadToS3 } from '@core-api/utils/s3';
import { listHorseIncomeOther, listHorseIncomePrize } from '../repositories/horse_income_repository';
import { listInvestmentAndReturnByMemberIdAndOccurredDate } from '../repositories/investment_and_return_repository';
import { listMemberClaimAndPaysWithMember } from '../repositories/member_claim_and_pay_repository';
import { listMemberClaimByMemberIdAndOccurredDate } from '../repositories/member_claim_and_pay_repository';
import { updateMemberClaimAndPayStatement } from '../repositories/member_claim_and_pay_repository';

// ============================================================================
// 型定義
// ============================================================================

/**
 * 部分的なMember型
 */
type PartialMember = {
  memberId: number;
  memberNumber: number;
  lastName: string;
  firstName: string;
  lastNameKana: string;
  firstNameKana: string;
};

/**
 * PDF生成結果の型
 */
interface GeneratePdfResult {
  pdfFileKey: string;
  totalHorses: number;
}

/**
 * 複数のPDF生成結果の型
 */
type GeneratePdfResults = GeneratePdfResult[];

/**
 * InvestmentAndReturnの拡張型
 */
type InvestmentAndReturnWithDetails = InvestmentAndReturn & {
  horse: Horse;
  investment: InvestmentAndReturnInvestment | null;
  returns: InvestmentAndReturnReturn[];
};

// ============================================================================
// 定数
// ============================================================================

const FONT_SIZE_SMALL = 8;
const FONT_SIZE_MEDIUM = 10;
const FONT_SIZE_LARGE = 12;
const FONT_SIZE_HEADING = 20;

const COMPANY_CREDIT = '株式会社Blooming Horse Club\n東京都中央区日本橋箱崎町36-2\nDaiwaリバーゲート16階\nTEL 03-0000-0000\n登録番号 T0000000000000';

const ORDERED_RETURN_CATEGORIES = [
  ReturnCategory.CLUB_TO_FUND_MONTHLY,
  ReturnCategory.FUND_TO_MEMBER_MONTHLY,
  ReturnCategory.CLUB_TO_FUND_YEARLY,
  ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER,
  ReturnCategory.FUND_TO_MEMBER_YEARLY_CLUB,
];

// ============================================================================
// ESM環境での__dirname代替
// ============================================================================
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ============================================================================
// ユーティリティ関数
// ============================================================================

/**
 * 分配カテゴリを日本語に変換
 */
const formatReturnCategory = (category: ReturnCategory): string => {
  switch (category) {
    case ReturnCategory.CLUB_TO_FUND_MONTHLY:
      return 'クラブ法人から愛馬会法人への分配金（月次）';
    case ReturnCategory.CLUB_TO_FUND_YEARLY:
      return 'クラブ法人から愛馬会法人への分配金（年次）';
    case ReturnCategory.FUND_TO_MEMBER_MONTHLY:
      return '愛馬会法人から会員様への分配金（月次）';
    case ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER:
      return '愛馬会法人から会員様への分配金（主催者源泉税）';
    case ReturnCategory.FUND_TO_MEMBER_YEARLY_CLUB:
      return '愛馬会法人から会員様への分配金（クラブ法人源泉税）';
    default:
      return category;
  }
};

/**
 * 年月を数値形式で作成
 */
const createYearMonth = (date: Date): number => {
  return date.getFullYear() * 100 + date.getMonth() + 1;
};

/**
 * 募集番号を生成
 */
const createRecruitmentNumber = (horse: Horse): string => {
  const recruitmentYear = (horse.recruitmentYear - 2000).toString();
  const recruitmentNo = horse.recruitmentNo.toString().padStart(3, '0');
  return recruitmentYear + recruitmentNo;
};

// ============================================================================
// S3関連処理
// ============================================================================

/**
 * S3にPDFをアップロードしてダウンロードURLを生成
 */
const uploadPdfToS3 = async (pdfBuffer: Buffer, memberNumber: string, occurredDate: Date): Promise<{ fileKey: string }> => {
  // 4桁の年月なので2000引いて2桁0埋めする
  const year = (occurredDate.getFullYear() - 2000).toString().padStart(2, '0');
  const month = (occurredDate.getMonth() + 1).toString().padStart(2, '0');
  const fileKey = `statements/investment-and-return-${memberNumber}-${year}${month}.pdf`;

  // S3にアップロード
  await uploadToS3(fileKey, pdfBuffer, 'application/pdf');

  // ダウンロードURL生成（1時間有効）
  // 必要に応じてコメントアウトを外してテスト時に確認する
//   const downloadUrl = await getPresignedDownloadUrl(fileKey, 60 * 60);
//   console.log('🔥 Download URL:', downloadUrl);

  return { fileKey };
};

// ============================================================================
// PDF生成クラス
// ============================================================================

/**
 * PDF生成を担当するクラス
 */
class InvestmentAndReturnPdfGenerator {
  private doc: PDFDocument;
  private contentMargin: number;
  private contentWidth: number;

  constructor() {
    this.contentMargin = 25;
    this.doc = new PDFDocument({
      margin: this.contentMargin,
      size: 'A4',
      bufferPages: true,
      autoFirstPage: true,
    });
    this.contentWidth = this.doc.page.width - this.contentMargin * 2;
  }

  /**
   * 日本語フォントを登録
   */
  private registerJapaneseFont(): void {
    const fontPath = path.join(__dirname, '../assets/NotoSansJP-VariableFont_wght.ttf');
    if (fs.existsSync(fontPath)) {
      this.doc.registerFont('NotoSansJP', fontPath);
      this.doc.font('NotoSansJP');
    }
}

  /**
   * ヘッダー部分を生成
   */
  private generateHeader(occurredDate: Date): void {
    const issuedDate = `${occurredDate.getFullYear()}年${occurredDate.getMonth() + 1}月${occurredDate.getDate()}日`;
    
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text('発行 ' + issuedDate, { align: 'right' });
    this.doc.fontSize(FONT_SIZE_HEADING).font('NotoSansJP', 'bold').text(
      `出資と分配 （${occurredDate.getFullYear()}年 ${occurredDate.getMonth() + 1}月分）`, 
      { align: 'center' }
    );
  }

  /**
   * 宛名と会社情報を生成
   */
  private generateAddressSection(member: PartialMember): number {
    let currentY = this.doc.y;
    
    // 左側のテキスト
    const memberName = `会員番号：${member.memberNumber}\n${member.lastName} ${member.firstName} 様`;
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(memberName, this.doc.page.margins.left, currentY + 30);
    
    // 右側のテキスト
    const rightTextX = this.doc.page.width - this.doc.page.margins.right - 150;
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(COMPANY_CREDIT, rightTextX, currentY);
    
    // Y座標を次の行に移動
    this.doc.x = this.doc.page.margins.left;
    this.doc.y = currentY + this.doc.currentLineHeight() * 6;
    return this.doc.y;
  }

  /**
   * 請求・支払金額セクションを生成
   */
  private generateBillingSection(memberClaimAndPay: MemberClaimAndPay): number {
    let currentY = this.doc.y;
    
    // 左側のテキスト
    const claimAmount = `ご請求金額：${memberClaimAndPay.claimAmount.toLocaleString()} 円\n振替日：${memberClaimAndPay.occurredDate.toLocaleDateString('ja-JP')}`;
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(claimAmount, this.doc.page.margins.left, currentY);
    
    // 右側のテキスト
    const paymentAmount = `お支払い金額：${memberClaimAndPay.payAmount.toLocaleString()} 円\nお支払い日：${memberClaimAndPay.occurredDate.toLocaleDateString('ja-JP')}`;
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(paymentAmount, this.doc.page.width / 2, currentY);

    this.doc.x = this.doc.page.margins.left;
    this.doc.y = currentY + this.doc.currentLineHeight() * 2;
    this.doc.moveDown(2);
    
    return this.doc.y;
  }

  /**
   * サマリーテーブルを生成
   */
  private async generateSummaryTable(
    investmentAndReturnList: InvestmentAndReturnWithDetails[],
    memberClaimList: MemberClaim[],
    memberClaimAndPay: MemberClaimAndPay
  ): Promise<void> {
    const summaryTable = {
      headers: [
        { label: '募集番号', property: 'recruitmentNumber', width: this.contentWidth * 0.1 },
        { label: '馬名', property: 'horseName', width: this.contentWidth * 0.4 },
        { label: 'ご請求金額（円）', property: 'claimAmount', width: this.contentWidth * 0.15, align: 'right' },
        { label: 'お支払い金額（円）', property: 'payAmount', width: this.contentWidth * 0.15, align: 'right' },
      ],
      datas: [
        ...investmentAndReturnList.map((investmentAndReturn) => ({
          recruitmentNumber: createRecruitmentNumber(investmentAndReturn.horse),
          horseName: investmentAndReturn.horse.horseName || investmentAndReturn.horse.recruitmentName,
          claimAmount: investmentAndReturn.billingAmount ? investmentAndReturn.billingAmount.toLocaleString() : '0',
          payAmount: investmentAndReturn.paymentAmount ? investmentAndReturn.paymentAmount.toLocaleString() : '0',
        })),
        ...memberClaimList.map((memberClaim) => ({
          recruitmentNumber: '',
          horseName: memberClaim.title + '（' + memberClaim.taxRate + '%）',
          claimAmount: memberClaim.amount.toLocaleString(),
          payAmount: '',
        })),
        {
          recruitmentNumber: '',
          horseName: '合計',
          claimAmount: memberClaimAndPay.claimAmount.toLocaleString(),
          payAmount: memberClaimAndPay.payAmount.toLocaleString(),
        }
      ],
    };

    // テーブルを中央寄せにする
    const tableWidth = summaryTable.headers.reduce((total, header) => total + header.width, 0) + (summaryTable.headers.length - 1) * 10;
    this.doc.x = (this.doc.page.width - tableWidth) / 2;

    await this.doc.table(summaryTable, {
      prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
      prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
      padding: [4, 4],
      columnSpacing: 10,
    });
  }

  /**
   * 明細書ページを生成
   */
  private async generateDetailPages(
    investmentAndReturnList: InvestmentAndReturnWithDetails[],
    occurredDate: Date
  ): Promise<void> {
    for (const investmentAndReturn of investmentAndReturnList) {
      this.doc.addPage();
      
      const issuedDate = `${occurredDate.getFullYear()}年${occurredDate.getMonth() + 1}月${occurredDate.getDate()}日`;
      this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text('発行 ' + issuedDate, { align: 'right' });
      
      const horseName = investmentAndReturn.horse.horseName || investmentAndReturn.horse.recruitmentName;
      this.doc.fontSize(FONT_SIZE_HEADING).font('NotoSansJP', 'bold').text(`${horseName} 出資金・分配金の計算書`, { align: 'center' });
      this.doc.moveDown(0.5);
      
      let currentY = this.doc.y;
      this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text('出資口数：' + investmentAndReturn.sharesNumber.toLocaleString(), this.doc.page.margins.left, currentY);
      this.doc.moveDown(0.2);
      currentY = this.doc.y;

      // 出資金計算セクション
      currentY = await this.generateInvestmentSection(investmentAndReturn, currentY);
      
      // 分配金計算セクション
      currentY = await this.generateReturnSection(investmentAndReturn, currentY);
      
      // 年次分配セクション
      currentY = await this.generateYearlyReturnSection(investmentAndReturn, currentY);
    }
  }

  /**
   * 出資金計算セクションを生成
   */
  private async generateInvestmentSection(investmentAndReturn: InvestmentAndReturnWithDetails, currentY: number): Promise<number> {
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text('１．出資金ご請求金額の計算', this.doc.page.margins.left, currentY);
    this.doc.moveDown(1);
    
    if (investmentAndReturn.investment) {
      const investment = investmentAndReturn.investment;
      
      // 競走馬出資金テーブル
      const racehorseInvestmentTable = {
        headers: [
          { label: '競走馬出資金相当額', property: 'racehorseInvestmentEquivalent', width: this.contentWidth * 0.2, align: 'right'},
          { label: '割引充当額（▲）', property: 'discountAllocation', width: this.contentWidth * 0.2, align: 'right' },
          { label: '', property: 'blank', width: this.contentWidth * 0.2, align: 'right' },
          { label: '競走馬出資金', property: 'racehorseInvestment', width: this.contentWidth * 0.4, align: 'right' },
        ],
        datas: [{
          racehorseInvestmentEquivalent: investment.racehorseInvestmentEquivalent.toLocaleString(),
          discountAllocation: investment.discountAllocation.toLocaleString(),
          blank: '',
          racehorseInvestment: investment.racehorseInvestment.toLocaleString()
        }],
      };

      await this.doc.table(racehorseInvestmentTable, {
        prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
        prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
        padding: [2, 2],
        columnSpacing: 5,
      });

      // 維持費出資金テーブル
      const runningCostInvestmentTable = {
        headers: [
          { label: '維持費', property: 'runningCost', width: this.contentWidth * 0.2, align: 'right'},
          { label: '補助金（▲）', property: 'subsidy', width: this.contentWidth * 0.2, align: 'right' },
          { label: '遡り維持費', property: 'retroactiveRunningCost', width: this.contentWidth * 0.2, align: 'right' },
          { label: '維持費出資金', property: 'runningCostInvestment', width: this.contentWidth * 0.4, align: 'right' },
        ],
        datas: [{
          runningCost: investment.runningCost.toLocaleString(),
          subsidy: investment.subsidy.toLocaleString(),
          retroactiveRunningCost: investment.retroactiveRunningCost.toLocaleString(),
          runningCostInvestment: investment.runningCostInvestment.toLocaleString()
        }],
      };

      await this.doc.table(runningCostInvestmentTable, {
        prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
        prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
        padding: [2, 2],
        columnSpacing: 5,
      });

      // 合計出資金テーブル
      const totalInvestmentTable = {
        headers: [
          { label: '保険料出資金', property: 'insuranceInvestment', width: this.contentWidth * 0.2, align: 'right'},
          { label: 'その他出資金', property: 'otherInvestment', width: this.contentWidth * 0.2, align: 'right' },
          { label: '', property: 'blank', width: this.contentWidth * 0.2, align: 'right' },
          { label: '当月出資金合計額（ご請求額）', property: 'currentMonthInvestmentTotal', width: this.contentWidth * 0.4, align: 'right' },
        ],
        datas: [{
          insuranceInvestment: investment.insuranceInvestment.toLocaleString(),
          otherInvestment: investment.otherInvestment.toLocaleString(),
          blank: '',
          currentMonthInvestmentTotal: investment.currentMonthInvestmentTotal.toLocaleString()
        }],
      };

      await this.doc.table(totalInvestmentTable, {
        prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
        prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
        padding: [2, 2],
        columnSpacing: 5,
      });

      this.doc.moveDown(0.2);
    }
    
    // 現在のY座標を返す
    return this.doc.y;
  }

  /**
   * 分配金計算セクションを生成
   */
  private async generateReturnSection(investmentAndReturn: InvestmentAndReturnWithDetails, currentY: number): Promise<number> {
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text('２．分配金お支払金額の計算', this.doc.page.margins.left, currentY);
    this.doc.moveDown(0.3);
    currentY = this.doc.y;
    
    const additionalMargin = 15;
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(`出資金累計額：${investmentAndReturn.investmentTotal.toLocaleString()} 円`, this.doc.page.margins.left + additionalMargin, currentY);
    this.doc.moveDown(0.3);
    currentY = this.doc.y;
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(`競走馬の前月末簿価：${investmentAndReturn.racehorseBookValueEndOfLastMonth.toLocaleString()} 円`, this.doc.page.margins.left + additionalMargin, currentY);
    this.doc.moveDown(0.3);
    currentY = this.doc.y;

    // 表示順がある
    for (const category of ORDERED_RETURN_CATEGORIES) {
      const returns = investmentAndReturn.returns.find((returns) => returns.returnCategory === category);
      if (returns) {
        this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text('□ ' + formatReturnCategory(returns.returnCategory), this.doc.page.margins.left, currentY);
        this.doc.moveDown(1);
        currentY = this.doc.y;

        // 出資返戻テーブル
        const returnTable = {
          headers: [
            { label: '前月までの出資返戻済金額（源泉税年次分配含む）', property: 'investmentRefundPaidUpToLastMonth', width: this.contentWidth * 0.4, align: 'right'}, 
            { label: '出資返戻可能額', property: 'refundableInvestmentAmount', width: this.contentWidth * 0.2, align: 'right'}, 
          ],
          datas: [{
            investmentRefundPaidUpToLastMonth: returns.investmentRefundPaidUpToLastMonth ? returns.investmentRefundPaidUpToLastMonth.toLocaleString() : '-',
            refundableInvestmentAmount: returns.refundableInvestmentAmount.toLocaleString(),
          }],
        };

        await this.doc.table(returnTable, {
          prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
          prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
          padding: [2, 2],
          columnSpacing: 5,
        });

        // 分配テーブル
        const distributionTable = {
          headers: [
            { label: '分配対象額', property: 'distributionTargetAmount', width: this.contentWidth * 0.2, align: 'right'}, 
            { label: 'うち出資返戻可能額', property: 'distributionTargetAmountRefundable', width: this.contentWidth * 0.2, align: 'right'}, 
            { label: 'うち利益分配額', property: 'distributionTargetAmountProfit', width: this.contentWidth * 0.2, align: 'right'}, 
            { label: '源泉税（▲）', property: 'distributionTargetAmountWithholdingTax', width: this.contentWidth * 0.2, align: 'right'}, 
            { label: '分配額', property: 'distributionAmount', width: this.contentWidth * 0.2, align: 'right'}, 
          ],
          datas: [{
            distributionTargetAmount: returns.distributionTargetAmount.toLocaleString(),
            distributionTargetAmountRefundable: returns.distributionTargetAmountRefundable.toLocaleString(),
            distributionTargetAmountProfit: returns.distributionTargetAmountProfit.toLocaleString(),
            distributionTargetAmountWithholdingTax: returns.distributionTargetAmountWithholdingTax.toLocaleString(),
            distributionAmount: returns.distributionAmount.toLocaleString(),
          }],
        };

        await this.doc.table(distributionTable, {
          prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
          prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
          padding: [2, 2],
          columnSpacing: 5,
        });

        // 繰越残高テーブル
        const refundableTable = {
          headers: [
            { label: '出資返戻可能額', property: 'refundableInvestmentAmount', width: this.contentWidth * 0.2, align: 'right'}, 
            { label: 'うち当月使用額', property: 'distributionTargetAmountRefundable', width: this.contentWidth * 0.2, align: 'right'}, 
            { label: 'うち繰越残高', property: 'refundableInvestmentAmountCarriedForward', width: this.contentWidth * 0.2, align: 'right'}, 
          ],
          datas: [{
            refundableInvestmentAmount: returns.refundableInvestmentAmount.toLocaleString(),
            distributionTargetAmountRefundable: returns.distributionTargetAmountRefundable.toLocaleString(),
            refundableInvestmentAmountCarriedForward: returns.refundableInvestmentAmountCarriedForward.toLocaleString(),
          }],
        };

        await this.doc.table(refundableTable, {
          prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
          prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
          padding: [2, 2],
          columnSpacing: 5,
        });

        if (category === ReturnCategory.FUND_TO_MEMBER_MONTHLY && investmentAndReturn.yearlyReturnTargetFlag) {
          this.doc.addPage();
        }
        currentY = this.doc.y;
      }
    }
    
    // 現在のY座標を返す
    return this.doc.y;
  }

  /**
   * 年次分配セクションを生成
   */
  private async generateYearlyReturnSection(investmentAndReturn: InvestmentAndReturnWithDetails, currentY: number): Promise<number> {
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text('３．年次分配の対象となる金額', this.doc.page.margins.left, currentY);
    this.doc.moveDown(0.5);
    
    const refundableTable = {
      headers: [
        { label: 'JRA・地方競馬源泉税累積額', property: 'organizerWithholdingTaxTotal', width: this.contentWidth * 0.2, align: 'right'}, 
        { label: 'うち当月加算分', property: 'organizerWithholdingTaxCurrentMonthAddition', width: this.contentWidth * 0.2, align: 'right'}, 
        { label: 'クラブ法人源泉税累積額', property: 'clubWithholdingTaxTotal', width: this.contentWidth * 0.2, align: 'right'}, 
        { label: 'うち当月加算分', property: 'clubWithholdingTaxCurrentMonthAddition', width: this.contentWidth * 0.2, align: 'right'}, 
      ],
      datas: [{
        organizerWithholdingTaxTotal: investmentAndReturn.organizerWithholdingTaxTotal.toLocaleString(),
        organizerWithholdingTaxCurrentMonthAddition: investmentAndReturn.organizerWithholdingTaxCurrentMonthAddition.toLocaleString(),
        clubWithholdingTaxTotal: investmentAndReturn.clubWithholdingTaxTotal.toLocaleString(),
        clubWithholdingTaxCurrentMonthAddition: investmentAndReturn.clubWithholdingTaxCurrentMonthAddition.toLocaleString(),
      }],
    };

    await this.doc.table(refundableTable, {
      prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
      prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
      padding: [2, 2],
      columnSpacing: 5,
    });
    
    // 現在のY座標を返す
    return this.doc.y;
  }

  /**
   * 収入内訳ページを生成
   */
  private async generateIncomeDetailPages(
    investmentAndReturnList: InvestmentAndReturnWithDetails[],
    occurredDate: Date
  ): Promise<void> {
    const targetYearMonth = createYearMonth(occurredDate);
    
    for (const investmentAndReturn of investmentAndReturnList) {
      const prizeIncomes = await listHorseIncomePrize({ horseId: investmentAndReturn.horse.horseId, incomeYearMonth: targetYearMonth });
      const otherIncomes = await listHorseIncomeOther({ horseId: investmentAndReturn.horse.horseId, incomeYearMonth: targetYearMonth });
      
      if ((prizeIncomes.isOk() && prizeIncomes.value.length > 0) || (otherIncomes.isOk() && otherIncomes.value.length > 0)) {
        this.doc.addPage();
        this.doc.fontSize(FONT_SIZE_HEADING).font('NotoSansJP', 'bold').text(`匿名組合収入内訳書`, { align: 'center' });
        this.doc.moveDown(0.5);
        
        let currentY = this.doc.y;
        const horseName = investmentAndReturn.horse.horseName || investmentAndReturn.horse.recruitmentName;
        this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(`${horseName} 収入内訳`, this.doc.page.margins.left, currentY);
        this.doc.moveDown(1);
        currentY = this.doc.y;
        
        let allIncomeAmount = 0;

        // 賞金収入の処理
        if (prizeIncomes.isOk() && prizeIncomes.value.length > 0) {
          for (const prize of prizeIncomes.value) {
            allIncomeAmount += prize.incomeAmount;
            await this.generatePrizeIncomeSection(prize, currentY);
            currentY = this.doc.y;
          }
        }

        // その他収入の処理
        if (otherIncomes.isOk() && otherIncomes.value.length > 0) {
          for (const other of otherIncomes.value) {
            allIncomeAmount += other.incomeAmount;
            await this.generateOtherIncomeSection(other, currentY);
            currentY = this.doc.y;
          }
        }

        // 合計表示
        this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(`合計 ${allIncomeAmount.toLocaleString()} 円`, this.doc.page.margins.left, currentY);
        this.doc.moveDown(0.3);
        currentY = this.doc.y;
        this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(`一口あたり分配対象額 ${(Math.floor(allIncomeAmount / investmentAndReturn.horse.sharesTotal)).toLocaleString()} 円`, this.doc.page.margins.left, currentY);
        this.doc.moveDown(0.5);
      }
    }
  }

  /**
   * 賞金収入セクションを生成
   */
  private async generatePrizeIncomeSection(prize: { 
    racePlace: string; 
    occurredYear: number; 
    occurredMonth: number; 
    occurredDay: number; 
    raceName: string; 
    raceResult: string; 
    mainPrizeAmount: number; 
    appearanceFee: number; 
    allowances: Array<{ name: string; amount: number }>; 
    totalPrizeAmount: number; 
    withholdingTax: number; 
    commissionAmount: number; 
    clubFeeAmount: number; 
    taxAmount: number; 
    incomeAmount: number; 
  }, currentY: number): Promise<void> {
    const prizeText = `【${prize.racePlace}】 ${prize.occurredYear}年${prize.occurredMonth}月${prize.occurredDay}日 ${prize.raceName} ${prize.raceResult}`;
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(prizeText, this.doc.page.margins.left, currentY);
    this.doc.moveDown(1);
    currentY = this.doc.y;
    
    // 左側のテーブル（収入項目）
    const leftTable = {
      headers: [
        { label: '内訳', property: 'name', width: this.contentWidth * 0.3, align: 'left'}, 
        { label: '金額', property: 'amount', width: this.contentWidth * 0.17, align: 'right'}, 
      ],
      datas: [
        { name: '本賞金', amount: prize.mainPrizeAmount.toLocaleString() },
        { name: '出走手当', amount: prize.appearanceFee.toLocaleString() },
        ...prize.allowances.map((allowance: { name: string; amount: number }) => ({ name: allowance.name, amount: allowance.amount.toLocaleString() })),
        { name: '賞金合計', amount: prize.totalPrizeAmount.toLocaleString() },
      ],
    };
    
    // 右側のテーブル（控除項目）
    const rightTable = {
      headers: [
        { label: '内訳', property: 'name', width: this.contentWidth * 0.3, align: 'left'}, 
        { label: '金額', property: 'amount', width: this.contentWidth * 0.17, align: 'right'}, 
      ],
      datas: [
        { name: '賞金合計', amount: prize.totalPrizeAmount.toLocaleString() },
        { name: '源泉所得税（▲）', amount: "-"+prize.withholdingTax.toLocaleString() },
        { name: '進上金（▲）', amount: "-"+prize.commissionAmount.toLocaleString() },
        { name: 'クラブ法人手数料（▲）', amount: "-"+prize.clubFeeAmount.toLocaleString() },
        { name: '消費税（▲）', amount: "-"+prize.taxAmount.toLocaleString() },
        { name: '小計', amount: prize.incomeAmount.toLocaleString() },
      ],
    };
    
    // 左側テーブルを配置
    this.doc.x = this.doc.page.margins.left;
    await this.doc.table(leftTable, {
      prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
      prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
      padding: [2, 2],
      columnSpacing: 5,
    });
    
    // 右側テーブルを配置（左側テーブルの右側に配置）
    const leftTableWidth = leftTable.headers.reduce((total, header) => total + header.width, 0) + (leftTable.headers.length - 1) * 5;
    this.doc.x = this.doc.page.margins.left + leftTableWidth + this.contentWidth * 0.06;
    this.doc.y = currentY;
    await this.doc.table(rightTable, {
      prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
      prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
      padding: [2, 2],
      columnSpacing: 5,
    });
  }

  /**
   * その他収入セクションを生成
   */
  private async generateOtherIncomeSection(other: { 
    name: import('@hami/core-admin-api-schema/horse_income_service_pb').HorseIncomeOtherName; 
    occurredYear: number; 
    occurredMonth: number; 
    occurredDay: number; 
    nameOther: string; 
    amount: number; 
    salesCommission: number; 
    taxAmount: number; 
    otherFeeAmount: number; 
    otherFeeName: string; 
    incomeAmount: number; 
  }, currentY: number): Promise<void> {
    const otherText = `【${other.name}】 ${other.occurredYear}年${other.occurredMonth}月${other.occurredDay}日 ${other.nameOther}`;
    this.doc.fontSize(FONT_SIZE_MEDIUM).font('NotoSansJP').text(otherText, this.doc.page.margins.left, currentY);
    this.doc.moveDown(1);
    currentY = this.doc.y;
    
    const leftTable = {
      headers: [
        { label: '内訳', property: 'name', width: this.contentWidth * 0.3, align: 'left'}, 
        { label: '金額', property: 'amount', width: this.contentWidth * 0.17, align: 'right'}, 
      ],
      datas: [
        { name: other.nameOther, amount: other.amount.toLocaleString() },
        // salesCommissionが 0 の場合は表示しない
        ...(other.salesCommission > 0 ? [{ name: '賞品売却手数料（▲）', amount: "-"+other.salesCommission.toLocaleString() }] : []),
        ...(other.taxAmount > 0 ? [{ name: '消費税（▲）', amount: "-"+other.taxAmount.toLocaleString() }] : []),
        ...(other.otherFeeAmount > 0 ? [{ name: other.otherFeeName + '（▲）', amount: "-"+other.otherFeeAmount.toLocaleString() }] : []),
        { name: '小計', amount: other.incomeAmount.toLocaleString() },
      ],
    };
    
    await this.doc.table(leftTable, {
      prepareHeader: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_SMALL).fillColor('#333333'),
      prepareRow: () => this.doc.font('NotoSansJP').fontSize(FONT_SIZE_MEDIUM).fillColor('#000000'),
      padding: [2, 2],
      columnSpacing: 5,
    });
    this.doc.moveDown(1);
  }

  /**
   * PDFを生成してBufferとして返す
   */
  async generatePdf(
    occurredDate: Date,
    memberClaimAndPay: MemberClaimAndPay,
    member: PartialMember,
    investmentAndReturnList: InvestmentAndReturnWithDetails[],
    memberClaimList: MemberClaim[]
  ): Promise<Buffer> {
    return new Promise(async (resolve, reject) => {
      const buffers: Buffer[] = [];

      this.doc.on('data', buffers.push.bind(buffers));
      this.doc.on('end', () => {
        const pdfBuffer = Buffer.concat(buffers);
        resolve(pdfBuffer);
      });
      this.doc.on('error', reject);

      try {
        // 日本語フォントを登録
        this.registerJapaneseFont();

        // ヘッダー部分を生成
        this.generateHeader(occurredDate);
        
        // 宛名と会社情報を生成
        this.generateAddressSection(member);
        
        // 請求・支払金額セクションを生成
        this.generateBillingSection(memberClaimAndPay);
        
        // サマリーテーブルを生成
        await this.generateSummaryTable(investmentAndReturnList, memberClaimList, memberClaimAndPay);
        
        // 明細書ページを生成
        await this.generateDetailPages(investmentAndReturnList, occurredDate);
        
        // 収入内訳ページを生成
        await this.generateIncomeDetailPages(investmentAndReturnList, occurredDate);

      } catch (error) {
        console.error('Error generating PDF tables:', error);
        reject(error);
        return;
      }

      this.doc.end();
    });
  }
}

// ============================================================================
// メイン処理
// ============================================================================

/**
 * 出資と分配PDF生成usecase
 */
export const generateInvestmentAndReturnPdfUsecase = (occurredDate: Date): ResultAsync<GeneratePdfResults, Error> => {
  return ResultAsync.fromPromise(
    (async () => {
      // 1. 発生日に請求・送金がある対象会員の取得
      const memberClaimAndPayListResult = await listMemberClaimAndPaysWithMember(occurredDate);
      const results: GeneratePdfResult[] = [];
      
      if (memberClaimAndPayListResult.isOk()) {
        const memberClaimAndPayList = memberClaimAndPayListResult.value;
        
        for (const memberClaimAndPay of memberClaimAndPayList) {
          const member = memberClaimAndPay.member;
          
          // このmemberとoccurredDateに紐づくInvestmentAndReturnを取得
          const investmentAndReturnListResult = await listInvestmentAndReturnByMemberIdAndOccurredDate(memberClaimAndPay.memberId, occurredDate);
          
          // MemberClaimとMemberPayのリストをそれぞれ取得する
          const memberClaimListResult = await listMemberClaimByMemberIdAndOccurredDate(memberClaimAndPay.memberId, occurredDate);
          
          if (investmentAndReturnListResult.isOk() && memberClaimListResult.isOk()) {
            const investmentAndReturnList = investmentAndReturnListResult.value;
            const memberClaimList = memberClaimListResult.value;
            
            // PDFを生成する
            const pdfGenerator = new InvestmentAndReturnPdfGenerator();
            const pdfBuffer = await pdfGenerator.generatePdf(
              occurredDate,
              memberClaimAndPay,
              member,
              investmentAndReturnList,
              memberClaimList
            );
            
            // S3にアップロード
            const { fileKey } = await uploadPdfToS3(pdfBuffer, member.memberNumber.toString(), occurredDate);
            
            // 生成したPDFのfileKeyを保存する
            await updateMemberClaimAndPayStatement(memberClaimAndPay.memberClaimAndPayId, fileKey, occurredDate);
            
            const result: GeneratePdfResult = {
              pdfFileKey: fileKey,
              totalHorses: investmentAndReturnList.length,
            };
            
            results.push(result);
          }
        }
      }
      
      return results;
    })(),
    (error) => {
      console.error('PDF generation failed:', error);
      return error instanceof Error ? error : new Error('Unknown error occurred during PDF generation');
    }
  );
};