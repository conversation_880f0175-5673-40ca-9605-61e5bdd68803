import { HorseFactory, MemberFactory, InvestmentApplicationFactory, InvestmentContractFactory } from '@core-test/index';
import { InvestmentContractStatus } from '@hami/prisma';
import { HorseInvestmentSharesFactory } from '../../test_utils/factories/horse_investment_shares_factory';
import { completeInvestmentContractsUsecase } from '../usecases/complete_investment_contract_usecase';
import { TAX_RATE } from '../utils/const';
import { getInvestmentSealDate, getInstallmentDatesAndInvestmentAmounts, getInstallmentCount } from '../utils/investment_utils';


describe('CompleteInvestmentContractsUsecase', () => {
  describe('completeInvestmentContracts', () => {
    it('契約締結を正しく実行できる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        sharesTotal: 40,
        amountTotal: 4000000,
      });
      const member = await MemberFactory.create();

      const app = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 3,
        isWhole: false,
      });

      const horseInvestmentShares01 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse.horseId,
        },
      });
      expect(horseInvestmentShares01).toBeDefined();
      expect(horseInvestmentShares01?.memberInvestmentShares).toBe(undefined);

      // ===== Act =====
      await completeInvestmentContractsUsecase(horse.horseId, [app.investmentApplicationId], new Date());

      const horseInvestmentShares02 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse.horseId,
        },
      });
      expect(horseInvestmentShares02).toBeDefined();
      expect(horseInvestmentShares02?.memberInvestmentShares).toBe(3);
      // ===== Assert =====
      const updatedApp = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: app.investmentApplicationId },
      });
      expect(updatedApp?.contracted).toBe(true);

      const contract = await vPrisma.client.investmentContract.findFirst({
        where: {
          horseId: horse.horseId,
          memberId: member.memberId,
        },
      });
      expect(contract).toBeDefined();
      if (contract) {
        expect(contract.sharesNumber).toBe(3);
        expect(contract.investmentAmount).toBe(300000); // 4000000 / 40 * 3
        expect(contract.contractStatus).toBe(InvestmentContractStatus.COMPLETED);
      }

      // MemberRacehorseInvestmentレコードの作成を確認
      const memberRacehorseInvestment = await vPrisma.client.memberRacehorseInvestment.findFirst({
        where: {
          horseId: horse.horseId,
          memberId: member.memberId,
        },
      });
      expect(memberRacehorseInvestment).toBeDefined();
      if (memberRacehorseInvestment) {
        expect(memberRacehorseInvestment.horseId).toBe(horse.horseId);
        expect(memberRacehorseInvestment.memberId).toBe(member.memberId);
        expect(memberRacehorseInvestment.racehorseInvestmentEquivalent).toBe(300000); // 4000000 / 40 * 3
        expect(memberRacehorseInvestment.discountAllocation).toBe(0); // 現在値引きはない
        expect(memberRacehorseInvestment.racehorseInvestment).toBe(300000); // racehorseInvestmentEquivalent - discountAllocation
        expect(memberRacehorseInvestment.investmentDate).toBeDefined();
        expect(memberRacehorseInvestment.createdAt).toBeDefined();
        expect(memberRacehorseInvestment.updatedAt).toBeDefined();
      }

      // MemberClaimレコードが作成されていることを確認
      const memberClaim = await vPrisma.client.memberClaim.findFirst({
        where: {
          memberId: member.memberId,
        },
      });
      expect(memberClaim).toBeDefined();
      if (memberClaim) {
        expect(memberClaim.memberId).toBe(member.memberId);
        expect(memberClaim.occurredDate).toBeDefined();
        expect(memberClaim.title).toBe('入会金');
        expect(memberClaim.description).toBe('入会金');
        expect(memberClaim.taxRate.toNumber()).toBe(TAX_RATE);
        expect(memberClaim.amount).toBe(20000 + 20000 * TAX_RATE / 100);
        expect(memberClaim.taxAmount).toBe(20000 * TAX_RATE / 100);
      }
    });

    it('対象外の申込がある場合はエラーを投げる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create();

      // ===== Act & Assert =====
      await expect(completeInvestmentContractsUsecase(horse.horseId, [999999], new Date())).rejects.toThrow(
        'Some applications are not eligible for contract completion or do not belong to the specified horse'
      );
    });

    it('契約口数が募集口数を超える場合はエラーを投げる', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        sharesTotal: 50, // 募集口数: 50口
        amountTotal: 1000000,
        horseName: 'テストホース',
        recruitmentName: 'テスト募集',
      });
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 既存の契約（30口）
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        sharesNumber: 30,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });
      await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        rejected: false,
        contracted: true,
        allocatedNumber: 30,
        isWhole: false,
      });

      // 新規申込（合計25口）→ 既存30口 + 新規25口 = 55口 > 募集50口
      const app1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 15,
      });

      const app2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 10,
        isWhole: false,
      });

      // ===== Act & Assert =====
      const horseInvestmentShares01 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse.horseId,
        },
      });
      expect(horseInvestmentShares01).toBeDefined();
      expect(horseInvestmentShares01?.memberInvestmentShares).toBe(undefined);
      await expect(
        completeInvestmentContractsUsecase(horse.horseId, [app1.investmentApplicationId, app2.investmentApplicationId], new Date())
      ).rejects.toThrow('Contract shares limit exceeded for horse テストホース (テスト募集)');

      const horseInvestmentShares02 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse.horseId,
        },
      });
      expect(horseInvestmentShares02).toBeDefined();
      expect(horseInvestmentShares02?.memberInvestmentShares).toBe(undefined);
    });

    it('募集口数の範囲内では契約締結が正常に実行される', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        sharesTotal: 100, // 募集口数: 100口
        amountTotal: 2000000,
      });
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 既存の契約（40口）
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        sharesNumber: 40,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });
      await HorseInvestmentSharesFactory.create({
        horseId: horse.horseId,
        memberInvestmentShares: 40,
      });

      // 新規申込（合計50口）→ 既存40口 + 新規50口 = 90口 <= 募集100口
      const app1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member1.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 30,
        isWhole: false,
      });

      const app2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member2.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 20,
        isWhole: false,
      });

      // ===== Act =====
      const horseInvestmentShares01 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse.horseId,
        },
      });
      expect(horseInvestmentShares01).toBeDefined();
      expect(horseInvestmentShares01?.memberInvestmentShares).toBe(40);
      await completeInvestmentContractsUsecase(horse.horseId, [app1.investmentApplicationId, app2.investmentApplicationId], new Date());
      const horseInvestmentShares02 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse.horseId,
        },
      });
      expect(horseInvestmentShares02).toBeDefined();
      expect(horseInvestmentShares02?.memberInvestmentShares).toBe(90);
      // ===== Assert =====
      // 契約が正常に作成されたことを確認
      const contractsCount = await vPrisma.client.investmentContract.count({
        where: { horseId: horse.horseId },
      });
      expect(contractsCount).toBe(3); // 既存1件 + 新規2件

      // 総契約口数が90口であることを確認
      const totalContracted = await vPrisma.client.investmentContract.aggregate({
        where: { horseId: horse.horseId },
        _sum: { sharesNumber: true },
      });
      expect(totalContracted._sum.sharesNumber).toBe(90);
    });

    it('対象の馬の申込数と、契約口数が一致しない場合はエラーを投げる', async () => {
      // ===== Arrange =====
      const horse1 = await HorseFactory.create({
        sharesTotal: 40,
        amountTotal: 4000000,
      });
      const horse2 = await HorseFactory.create({
        sharesTotal: 50,
        amountTotal: 5000000,
      });
      const member = await MemberFactory.create();

      // horse2の申込を作成
      const app = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse2.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 5,
        isWhole: false,
      });

      // ===== Act & Assert =====
      // horse1のIDで、horse2の申込を契約しようとする
      await expect(completeInvestmentContractsUsecase(horse1.horseId, [app.investmentApplicationId], new Date())).rejects.toThrow(
        'Some applications are not eligible for contract completion or do not belong to the specified horse'
      );
    });

    it('特定の馬の契約締結が他の馬の申込に影響しないことを確認', async () => {
      // ===== Arrange =====
      const horse1 = await HorseFactory.create({
        sharesTotal: 40,
        amountTotal: 4000000,
        horseName: '契約対象馬',
        recruitmentName: '契約対象馬の募集',
      });
      const horse2 = await HorseFactory.create({
        sharesTotal: 50,
        amountTotal: 5000000,
        horseName: '影響確認馬',
        recruitmentName: '影響確認馬の募集',
      });
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // horse1の申込（契約締結対象）
      const horse1App = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse1.horseId } },
        member: { connect: { memberId: member1.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 10,
        isWhole: false,
      });

      // horse2の申込（影響を受けてはいけない）
      const horse2App1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse2.horseId } },
        member: { connect: { memberId: member1.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 15,
        isWhole: false,
      });

      const horse2App2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse2.horseId } },
        member: { connect: { memberId: member2.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 8,
        isWhole: false,
      });

      // 実行前のhorse2の申込状態を確認
      const beforeHorse2App1 = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: horse2App1.investmentApplicationId },
      });
      const beforeHorse2App2 = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: horse2App2.investmentApplicationId },
      });
      expect(beforeHorse2App1?.contracted).toBe(false);
      expect(beforeHorse2App2?.contracted).toBe(false);

      // ===== Act =====
      const horseInvestmentShares01 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse1.horseId,
        },
      });
      expect(horseInvestmentShares01).toBeDefined();
      expect(horseInvestmentShares01?.memberInvestmentShares).toBe(undefined);
      // horse1の申込のみを契約締結
      await completeInvestmentContractsUsecase(horse1.horseId, [horse1App.investmentApplicationId], new Date());

      // ===== Assert =====
      // horse1の申込が契約締結されていることを確認
      const updatedHorse1App = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: horse1App.investmentApplicationId },
      });
      expect(updatedHorse1App?.contracted).toBe(true);

      // horse1の契約が作成されていることを確認
      const horse1Contract = await vPrisma.client.investmentContract.findFirst({
        where: {
          horseId: horse1.horseId,
          memberId: member1.memberId,
        },
      });
      expect(horse1Contract).toBeDefined();
      expect(horse1Contract?.sharesNumber).toBe(10);

      // horseInvestmentSharesが更新されていることを確認
      const horseInvestmentShares02 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse1.horseId,
        },
      });
      expect(horseInvestmentShares02).toBeDefined();
      expect(horseInvestmentShares02?.memberInvestmentShares).toBe(10);

      // horse2のhorseInvestmentSharesが更新されていないことを確認
      const horseInvestmentShares03 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse2.horseId,
        },
      });
      expect(horseInvestmentShares03).toBeDefined();
      expect(horseInvestmentShares03?.memberInvestmentShares).toBe(undefined);

      // horse2の申込がcontracted=falseのまま変更されていないことを確認
      const afterHorse2App1 = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: horse2App1.investmentApplicationId },
      });
      const afterHorse2App2 = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: horse2App2.investmentApplicationId },
      });
      expect(afterHorse2App1?.contracted).toBe(false);
      expect(afterHorse2App1?.allocatedNumber).toBe(15); // allocatedNumberも変更されていない
      expect(afterHorse2App2?.contracted).toBe(false);
      expect(afterHorse2App2?.allocatedNumber).toBe(8); // allocatedNumberも変更されていない

      // horse2に対してContractが作成されていないことを確認
      const horse2Contracts = await vPrisma.client.investmentContract.findMany({
        where: { horseId: horse2.horseId },
      });
      expect(horse2Contracts.length).toBe(0);

      // horse1とhorse2の契約数を確認
      const horse1ContractsCount = await vPrisma.client.investmentContract.count({
        where: { horseId: horse1.horseId },
      });
      const horse2ContractsCount = await vPrisma.client.investmentContract.count({
        where: { horseId: horse2.horseId },
      });
      expect(horse1ContractsCount).toBe(1);
      expect(horse2ContractsCount).toBe(0);

      // horse1のMemberRacehorseInvestmentが作成されていることを確認
      const horse1MemberRacehorseInvestment = await vPrisma.client.memberRacehorseInvestment.findFirst({
        where: {
          horseId: horse1.horseId,
          memberId: member1.memberId,
        },
      });
      expect(horse1MemberRacehorseInvestment).toBeDefined();
      if (horse1MemberRacehorseInvestment) {
        expect(horse1MemberRacehorseInvestment.horseId).toBe(horse1.horseId);
        expect(horse1MemberRacehorseInvestment.memberId).toBe(member1.memberId);
        expect(horse1MemberRacehorseInvestment.racehorseInvestmentEquivalent).toBe(1000000); // 4000000 / 40 * 10
        expect(horse1MemberRacehorseInvestment.discountAllocation).toBe(0);
        expect(horse1MemberRacehorseInvestment.racehorseInvestment).toBe(1000000);
        expect(horse1MemberRacehorseInvestment.investmentDate).toBeDefined();
      }

      // horse2にはMemberRacehorseInvestmentが作成されていないことを確認
      const horse2MemberRacehorseInvestments = await vPrisma.client.memberRacehorseInvestment.findMany({
        where: { horseId: horse2.horseId },
      });
      expect(horse2MemberRacehorseInvestments.length).toBe(0);
    });

    it('複数馬が存在する状況で正しい馬のみが契約締結されることを確認', async () => {
      // ===== Arrange =====
      const horse1 = await HorseFactory.create({
        sharesTotal: 30,
        amountTotal: 3000000,
        horseName: '第1号馬',
        recruitmentName: '第1号馬の募集',
      });
      const horse2 = await HorseFactory.create({
        sharesTotal: 40,
        amountTotal: 4000000,
        horseName: '第2号馬',
        recruitmentName: '第2号馬の募集',
      });
      const horse3 = await HorseFactory.create({
        sharesTotal: 50,
        amountTotal: 5000000,
        horseName: '第3号馬',
        recruitmentName: '第3号馬の募集',
      });
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // 各馬に申込を作成
      const horse1App = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse1.horseId } },
        member: { connect: { memberId: member1.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 5,
        isWhole: false,
      });

      const horse2App1 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse2.horseId } },
        member: { connect: { memberId: member1.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 8,
        isWhole: false,
      });

      const horse2App2 = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse2.horseId } },
        member: { connect: { memberId: member2.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 12,
        isWhole: false,
      });

      const horse3App = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse3.horseId } },
        member: { connect: { memberId: member2.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 7,
        isWhole: false,
      });

      const horseInvestmentShares01 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse1.horseId,
        },
      });
      expect(horseInvestmentShares01).toBeDefined();
      expect(horseInvestmentShares01?.memberInvestmentShares).toBe(undefined);

      // ===== Act =====
      // horse2の申込のみを契約締結（複数申込を同時に処理）
      await completeInvestmentContractsUsecase(horse2.horseId, [horse2App1.investmentApplicationId, horse2App2.investmentApplicationId], new Date());

      // ===== Assert =====
      // horse2の申込が契約締結されていることを確認
      const updatedHorse2App1 = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: horse2App1.investmentApplicationId },
      });
      const updatedHorse2App2 = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: horse2App2.investmentApplicationId },
      });
      expect(updatedHorse2App1?.contracted).toBe(true);
      expect(updatedHorse2App2?.contracted).toBe(true);

      // horse1とhorse3の申込がcontracted=falseのまま変更されていないことを確認
      const horse1AppAfter = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: horse1App.investmentApplicationId },
      });
      const horse3AppAfter = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: horse3App.investmentApplicationId },
      });
      expect(horse1AppAfter?.contracted).toBe(false);
      expect(horse1AppAfter?.allocatedNumber).toBe(5);
      expect(horse3AppAfter?.contracted).toBe(false);
      expect(horse3AppAfter?.allocatedNumber).toBe(7);

      // horse2のhorseInvestmentSharesが更新されていることを確認
      const horseInvestmentShares02 = await vPrisma.client.horseInvestmentShares.findFirst({
        where: {
          horseId: horse2.horseId,
        },
      });
      expect(horseInvestmentShares02).toBeDefined();
      expect(horseInvestmentShares02?.memberInvestmentShares).toBe(20);

      // 契約数を確認
      const horse1ContractsCount = await vPrisma.client.investmentContract.count({
        where: { horseId: horse1.horseId },
      });
      const horse2ContractsCount = await vPrisma.client.investmentContract.count({
        where: { horseId: horse2.horseId },
      });
      const horse3ContractsCount = await vPrisma.client.investmentContract.count({
        where: { horseId: horse3.horseId },
      });
      expect(horse1ContractsCount).toBe(0);
      expect(horse2ContractsCount).toBe(2);
      expect(horse3ContractsCount).toBe(0);

      // horse2の契約内容を確認
      const horse2Contracts = await vPrisma.client.investmentContract.findMany({
        where: { horseId: horse2.horseId },
        orderBy: { sharesNumber: 'asc' },
      });
      expect(horse2Contracts[0].sharesNumber).toBe(8);
      expect(horse2Contracts[1].sharesNumber).toBe(12);
      expect(horse2Contracts[0].investmentAmount).toBe(800000); // 4000000 / 40 * 8
      expect(horse2Contracts[1].investmentAmount).toBe(1200000); // 4000000 / 40 * 12
      expect(horse2Contracts[0].investmentAmountBeforeTax).toBe(727273); // ceil(800000 / (1 + 10 / 100))
      expect(horse2Contracts[1].investmentAmountBeforeTax).toBe(1090910); // ceil(1200000 / (1 + 10 / 100))
      expect(horse2Contracts[0].transactionAmount).toBeNull();
      expect(horse2Contracts[1].transactionAmount).toBeNull();
      expect(horse2Contracts[0].monthlyDepreciation).toBeNull();
      expect(horse2Contracts[1].monthlyDepreciation).toBeNull();
      expect(horse2Contracts[0].taxRate.toNumber()).toBe(TAX_RATE);
      expect(horse2Contracts[1].taxRate.toNumber()).toBe(TAX_RATE);
      expect(horse2Contracts[0].discount).toBe(0);
      expect(horse2Contracts[1].discount).toBe(0);
      expect(horse2Contracts[0].contractedAt).toBeDefined();
      expect(horse2Contracts[1].contractedAt).toBeDefined();
      expect(horse2Contracts[0].contractStatus).toBe(InvestmentContractStatus.COMPLETED);
      expect(horse2Contracts[1].contractStatus).toBe(InvestmentContractStatus.COMPLETED);
      expect(horse2Contracts[0].createdAt).toBeDefined();
      expect(horse2Contracts[1].createdAt).toBeDefined();
      expect(horse2Contracts[0].updatedAt).toBeDefined();
      expect(horse2Contracts[1].updatedAt).toBeDefined();

      // MemberRacehorseInvestmentレコードの作成を確認
      const horse2MemberRacehorseInvestments = await vPrisma.client.memberRacehorseInvestment.findMany({
        where: { horseId: horse2.horseId },
        orderBy: { memberId: 'asc' },
      });
      expect(horse2MemberRacehorseInvestments.length).toBe(2);

      // member1のMemberRacehorseInvestmentを確認
      const member1Investment = horse2MemberRacehorseInvestments.find(r => r.memberId === member1.memberId);
      expect(member1Investment).toBeDefined();
      if (member1Investment) {
        expect(member1Investment.horseId).toBe(horse2.horseId);
        expect(member1Investment.memberId).toBe(member1.memberId);
        expect(member1Investment.racehorseInvestmentEquivalent).toBe(800000);
        expect(member1Investment.discountAllocation).toBe(0);
        expect(member1Investment.racehorseInvestment).toBe(800000); // 4000000 / 40 * 8
        expect(member1Investment.investmentDate).toBeDefined();
      }

      // member2のMemberRacehorseInvestmentを確認
      const member2Investment = horse2MemberRacehorseInvestments.find(r => r.memberId === member2.memberId);
      expect(member2Investment).toBeDefined();
      if (member2Investment) {
        expect(member2Investment.horseId).toBe(horse2.horseId);
        expect(member2Investment.memberId).toBe(member2.memberId);
        expect(member2Investment.racehorseInvestmentEquivalent).toBe(1200000); // 4000000 / 40 * 12
        expect(member2Investment.discountAllocation).toBe(0);
        expect(member2Investment.racehorseInvestment).toBe(1200000); // 4000000 / 40 * 12
        expect(member2Investment.investmentDate).toBeDefined();
      }

      // horse1とhorse3にはMemberRacehorseInvestmentが作成されていないことを確認
      const horse1MemberRacehorseInvestments = await vPrisma.client.memberRacehorseInvestment.findMany({
        where: { horseId: horse1.horseId },
      });
      const horse3MemberRacehorseInvestments = await vPrisma.client.memberRacehorseInvestment.findMany({
        where: { horseId: horse3.horseId },
      });
      expect(horse1MemberRacehorseInvestments.length).toBe(0);
      expect(horse3MemberRacehorseInvestments.length).toBe(0);
    });

    it('既存のinvestmentContractがある場合は入会金のmemberClaimが作成されない', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        sharesTotal: 40,
        amountTotal: 4000000,
      });
      const member = await MemberFactory.create();

      // 既存のinvestmentContractを作成
      await InvestmentContractFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        sharesNumber: 5,
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      const app = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 3,
        isWhole: false,
      });

      // ===== Act =====
      await completeInvestmentContractsUsecase(horse.horseId, [app.investmentApplicationId], new Date(2024, 9, 10));

      // ===== Assert =====
      // 契約が正常に作成されたことを確認
      const updatedApp = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: app.investmentApplicationId },
      });
      expect(updatedApp?.contracted).toBe(true);

      // 既存のinvestmentContractがあるため、入会金のmemberClaimが作成されていないことを確認
      const memberClaim = await vPrisma.client.memberClaim.findFirst({
        where: {
          memberId: member.memberId,
          title: '入会金',
        },
      });
      expect(memberClaim).toBeNull();
    });

    it('分割払いの場合、MemberRacehorseInvestmentレコードが複数作成される', async () => {
      // 2歳の6月まで分割払いになるので、5分割になるような馬を作成する
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        birthYear: 2023,
        birthMonth: 4,
        birthDay: 15,
        sharesTotal: 40,
        amountTotal: 4000000,
        horseName: '分割払いテスト馬',
        recruitmentName: '分割払いテスト馬の募集',
      });
      const member = await MemberFactory.create();

      const app = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 4,
        isWhole: false,
        installmentPayment: true, // 分割払いを有効にする
      });

      // ===== Act =====
      await completeInvestmentContractsUsecase(horse.horseId, [app.investmentApplicationId], new Date(2024, 9, 10));

      // ===== Assert =====
      // 申込が契約締結されていることを確認
      const updatedApp = await vPrisma.client.investmentApplication.findUnique({
        where: { investmentApplicationId: app.investmentApplicationId },
      });
      expect(updatedApp?.contracted).toBe(true);

      // 契約が作成されていることを確認
      const contract = await vPrisma.client.investmentContract.findFirst({
        where: {
          horseId: horse.horseId,
          memberId: member.memberId,
        },
      });
      expect(contract).toBeDefined();
      if (contract) {
        expect(contract.sharesNumber).toBe(4);
        expect(contract.investmentAmount).toBe(400000); // 4000000 / 40 * 4
        expect(contract.contractStatus).toBe(InvestmentContractStatus.COMPLETED);
      }

      // MemberRacehorseInvestmentレコードが5件作成されていることを確認（分割回数5回）
      const memberRacehorseInvestments = await vPrisma.client.memberRacehorseInvestment.findMany({
        where: {
          horseId: horse.horseId,
          memberId: member.memberId,
        },
        orderBy: { investmentDate: 'asc' },
      });
      expect(memberRacehorseInvestments).toHaveLength(5);

      // 各分割の金額を確認
      const expectedInvestmentAmount = 400000; // 4000000 / 40 * 4
      const expectedAmountPerInstallment = Math.floor(expectedInvestmentAmount / 5); // 80000
      const remainder = expectedInvestmentAmount % 5; // 0

      for (let i = 0; i < 5; i++) {
        const investment = memberRacehorseInvestments[i];
        expect(investment.horseId).toBe(horse.horseId);
        expect(investment.memberId).toBe(member.memberId);
        expect(investment.discountAllocation).toBe(0); // 現在値引きはない
        
        if (i === 0) {
          // 初回は余りを含む
          expect(investment.racehorseInvestmentEquivalent).toBe(expectedAmountPerInstallment + remainder);
          expect(investment.racehorseInvestment).toBe(expectedAmountPerInstallment + remainder);
        } else {
          // 2回目以降は均等
          expect(investment.racehorseInvestmentEquivalent).toBe(expectedAmountPerInstallment);
          expect(investment.racehorseInvestment).toBe(expectedAmountPerInstallment);
        }
        
        expect(investment.investmentDate).toBeDefined();
        expect(investment.createdAt).toBeDefined();
        expect(investment.updatedAt).toBeDefined();
      }

      // 合計金額が正しいことを確認
      const totalEquivalent = memberRacehorseInvestments.reduce((sum, item) => sum + item.racehorseInvestmentEquivalent, 0);
      const totalInvestment = memberRacehorseInvestments.reduce((sum, item) => sum + item.racehorseInvestment, 0);
      expect(totalEquivalent).toBe(expectedInvestmentAmount);
      expect(totalInvestment).toBe(expectedInvestmentAmount);
    });

    it('分割払いで割り切れない場合、余りは初回に加算される', async () => {
      // ===== Arrange =====
      const horse = await HorseFactory.create({
        birthYear: 2023,
        birthMonth: 4,
        birthDay: 15,
        sharesTotal: 40,
        amountTotal: 4000000,
        horseName: '分割余りテスト馬',
        recruitmentName: '分割余りテスト馬の募集',
      });
      const member = await MemberFactory.create();

      const app = await InvestmentApplicationFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        member: { connect: { memberId: member.memberId } },
        rejected: false,
        contracted: false,
        allocatedNumber: 3, // 3口で割り切れない金額にする
        isWhole: false,
        installmentPayment: true, // 分割払いを有効にする
      });

      // ===== Act =====
      await completeInvestmentContractsUsecase(horse.horseId, [app.investmentApplicationId], new Date(2024, 9, 10));

      // ===== Assert =====
      // MemberRacehorseInvestmentレコードが5件作成されていることを確認
      const memberRacehorseInvestments = await vPrisma.client.memberRacehorseInvestment.findMany({
        where: {
          horseId: horse.horseId,
          memberId: member.memberId,
        },
        orderBy: { investmentDate: 'asc' },
      });
      expect(memberRacehorseInvestments).toHaveLength(5);

      // 各分割の金額を確認
      const expectedInvestmentAmount = 300000; // 4000000 / 40 * 3
      const expectedAmountPerInstallment = Math.floor(expectedInvestmentAmount / 5); // 60000
      const remainder = expectedInvestmentAmount % 5; // 0

      for (let i = 0; i < 5; i++) {
        const investment = memberRacehorseInvestments[i];
        
        if (i === 0) {
          // 初回は余りを含む
          expect(investment.racehorseInvestmentEquivalent).toBe(expectedAmountPerInstallment + remainder);
          expect(investment.racehorseInvestment).toBe(expectedAmountPerInstallment + remainder);
        } else {
          // 2回目以降は均等
          expect(investment.racehorseInvestmentEquivalent).toBe(expectedAmountPerInstallment);
          expect(investment.racehorseInvestment).toBe(expectedAmountPerInstallment);
        }
      }

      // 合計金額が正しいことを確認
      const totalEquivalent = memberRacehorseInvestments.reduce((sum, item) => sum + item.racehorseInvestmentEquivalent, 0);
      const totalInvestment = memberRacehorseInvestments.reduce((sum, item) => sum + item.racehorseInvestment, 0);
      expect(totalEquivalent).toBe(expectedInvestmentAmount);
      expect(totalInvestment).toBe(expectedInvestmentAmount);
    });
  });

  describe('getInvestmentSealDate', () => {
    it('常に翌々月10日を返す（基本ケース）', () => {
      // ===== Arrange =====
      const contractDate = new Date(2024, 0, 15); // 2024年1月15日

      // ===== Act =====
      const result = getInvestmentSealDate(contractDate);

      // ===== Assert =====
      expect(result.getFullYear()).toBe(2024);
      expect(result.getMonth()).toBe(2); // 3月（0ベース）
      expect(result.getDate()).toBe(10);
    });

    it('年をまたぐ場合', () => {
      // ===== Arrange =====
      const contractDate = new Date(2024, 11, 20); // 2024年12月20日

      // ===== Act =====
      const result = getInvestmentSealDate(contractDate);

      // ===== Assert =====
      expect(result.getFullYear()).toBe(2025);
      expect(result.getMonth()).toBe(1); // 2月（0ベース）
      expect(result.getDate()).toBe(10);
    });

    it('うるう年の2月29日', () => {
      // ===== Arrange =====
      const contractDate = new Date(2024, 1, 29); // 2024年2月29日（うるう年）

      // ===== Act =====
      const result = getInvestmentSealDate(contractDate);

      // ===== Assert =====
      expect(result.getFullYear()).toBe(2024);
      expect(result.getMonth()).toBe(3); // 4月（0ベース）
      expect(result.getDate()).toBe(10);
    });
  });


  describe('getInstallmentDatesAndInvestmentAmounts', () => {
    it('分割回数が1の場合、1回の支払いのみを返す', () => {
      // ===== Arrange =====
      const firstInvestmentSealDate = new Date(2024, 3, 10); // 2024年4月10日
      const installmentCount = 1;
      const racehorseInvestmentEquivalent = 1000000;
      const discountAllocation = 0;
      const racehorseInvestment = 1000000;

      // ===== Act =====
      const result = getInstallmentDatesAndInvestmentAmounts(
        firstInvestmentSealDate,
        installmentCount,
        racehorseInvestmentEquivalent,
        discountAllocation,
        racehorseInvestment
      );

      // ===== Assert =====
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        investmentDate: firstInvestmentSealDate,
        racehorseInvestmentEquivalent: 1000000,
        discountAllocation: 0,
        racehorseInvestment: 1000000,
      });
    });

    it('分割回数が5で割り切れる場合、均等に分割される', () => {
      // ===== Arrange =====
      const firstInvestmentSealDate = new Date(2024, 3, 10); // 2024年4月10日
      const installmentCount = 5;
      const racehorseInvestmentEquivalent = 1000000;
      const discountAllocation = 0;
      const racehorseInvestment = 1000000;

      // ===== Act =====
      const result = getInstallmentDatesAndInvestmentAmounts(
        firstInvestmentSealDate,
        installmentCount,
        racehorseInvestmentEquivalent,
        discountAllocation,
        racehorseInvestment
      );

      // ===== Assert =====
      expect(result).toHaveLength(5);
      
      // 各分割の金額を確認
      for (let i = 0; i < 5; i++) {
        expect(result[i].racehorseInvestmentEquivalent).toBe(200000); // 1000000 / 5
        expect(result[i].discountAllocation).toBe(0);
        expect(result[i].racehorseInvestment).toBe(200000);
      }

      // 支払い日を確認
      expect(result[0].investmentDate).toEqual(new Date(2024, 3, 10)); // 4月10日
      expect(result[1].investmentDate).toEqual(new Date(2024, 4, 10)); // 5月10日
      expect(result[2].investmentDate).toEqual(new Date(2024, 5, 10)); // 6月10日
      expect(result[3].investmentDate).toEqual(new Date(2024, 6, 10)); // 7月10日
      expect(result[4].investmentDate).toEqual(new Date(2024, 7, 10)); // 8月10日
    });

    it('分割回数で割り切れない場合、余りは初回に加算される', () => {
      // ===== Arrange =====
      const firstInvestmentSealDate = new Date(2024, 3, 10); // 2024年4月10日
      const installmentCount = 3;
      const racehorseInvestmentEquivalent = 1000000; // 3で割ると余り1
      const discountAllocation = 0;
      const racehorseInvestment = 1000000;

      // ===== Act =====
      const result = getInstallmentDatesAndInvestmentAmounts(
        firstInvestmentSealDate,
        installmentCount,
        racehorseInvestmentEquivalent,
        discountAllocation,
        racehorseInvestment
      );

      // ===== Assert =====
      expect(result).toHaveLength(3);
      
      // 初回は余りを含む
      expect(result[0].racehorseInvestmentEquivalent).toBe(333334); // 333333 + 1 (余り)
      expect(result[0].discountAllocation).toBe(0);
      expect(result[0].racehorseInvestment).toBe(333334);
      
      // 2回目以降は均等
      expect(result[1].racehorseInvestmentEquivalent).toBe(333333);
      expect(result[1].discountAllocation).toBe(0);
      expect(result[1].racehorseInvestment).toBe(333333);
      
      expect(result[2].racehorseInvestmentEquivalent).toBe(333333);
      expect(result[2].discountAllocation).toBe(0);
      expect(result[2].racehorseInvestment).toBe(333333);

      // 合計が元の金額と一致することを確認
      const totalEquivalent = result.reduce((sum, item) => sum + item.racehorseInvestmentEquivalent, 0);
      const totalInvestment = result.reduce((sum, item) => sum + item.racehorseInvestment, 0);
      expect(totalEquivalent).toBe(1000000);
      expect(totalInvestment).toBe(1000000);
    });

    it('割引充当額がある場合、正しく分割される', () => {
      // ===== Arrange =====
      const firstInvestmentSealDate = new Date(2024, 3, 10); // 2024年4月10日
      const installmentCount = 3;
      const racehorseInvestmentEquivalent = 1000000;
      const discountAllocation = 100000; // 3で割ると余り1
      const racehorseInvestment = 900000; // 1000000 - 100000

      // ===== Act =====
      const result = getInstallmentDatesAndInvestmentAmounts(
        firstInvestmentSealDate,
        installmentCount,
        racehorseInvestmentEquivalent,
        discountAllocation,
        racehorseInvestment
      );

      // ===== Assert =====
      expect(result).toHaveLength(3);
      
      // 初回は余りを含む
      expect(result[0].racehorseInvestmentEquivalent).toBe(333334); // 333333 + 1 (余り)
      expect(result[0].discountAllocation).toBe(33334); // 33333 + 1 (余り)
      expect(result[0].racehorseInvestment).toBe(300000); // 333334 - 33334
      
      // 2回目以降は均等
      expect(result[1].racehorseInvestmentEquivalent).toBe(333333);
      expect(result[1].discountAllocation).toBe(33333);
      expect(result[1].racehorseInvestment).toBe(300000); // 333333 - 33333
      
      expect(result[2].racehorseInvestmentEquivalent).toBe(333333);
      expect(result[2].discountAllocation).toBe(33333);
      expect(result[2].racehorseInvestment).toBe(300000); // 333333 - 33333

      // 合計が元の金額と一致することを確認
      const totalEquivalent = result.reduce((sum, item) => sum + item.racehorseInvestmentEquivalent, 0);
      const totalDiscount = result.reduce((sum, item) => sum + item.discountAllocation, 0);
      const totalInvestment = result.reduce((sum, item) => sum + item.racehorseInvestment, 0);
      expect(totalEquivalent).toBe(1000000);
      expect(totalDiscount).toBe(100000);
      expect(totalInvestment).toBe(900000);
    });

    it('年をまたぐ場合、正しい日付が設定される', () => {
      // ===== Arrange =====
      const firstInvestmentSealDate = new Date(2024, 11, 10); // 2024年12月10日
      const installmentCount = 3;
      const racehorseInvestmentEquivalent = 300000;
      const discountAllocation = 0;
      const racehorseInvestment = 300000;

      // ===== Act =====
      const result = getInstallmentDatesAndInvestmentAmounts(
        firstInvestmentSealDate,
        installmentCount,
        racehorseInvestmentEquivalent,
        discountAllocation,
        racehorseInvestment
      );

      // ===== Assert =====
      expect(result).toHaveLength(3);
      
      // 支払い日を確認（年をまたぐ）
      expect(result[0].investmentDate).toEqual(new Date(2024, 11, 10)); // 12月10日
      expect(result[1].investmentDate).toEqual(new Date(2025, 0, 10)); // 1月10日
      expect(result[2].investmentDate).toEqual(new Date(2025, 1, 10)); // 2月10日
    });

    it('2月から開始する場合、正しい日付が設定される', () => {
      // ===== Arrange =====
      const firstInvestmentSealDate = new Date(2024, 1, 10); // 2024年2月10日
      const installmentCount = 3;
      const racehorseInvestmentEquivalent = 300000;
      const discountAllocation = 0;
      const racehorseInvestment = 300000;

      // ===== Act =====
      const result = getInstallmentDatesAndInvestmentAmounts(
        firstInvestmentSealDate,
        installmentCount,
        racehorseInvestmentEquivalent,
        discountAllocation,
        racehorseInvestment
      );

      // ===== Assert =====
      expect(result).toHaveLength(3);
      
      // 支払い日を確認
      expect(result[0].investmentDate).toEqual(new Date(2024, 1, 10)); // 2月10日
      expect(result[1].investmentDate).toEqual(new Date(2024, 2, 10)); // 3月10日
      expect(result[2].investmentDate).toEqual(new Date(2024, 3, 10)); // 4月10日
    });

    it('大きな金額でも正しく分割される', () => {
      // ===== Arrange =====
      const firstInvestmentSealDate = new Date(2024, 3, 10); // 2024年4月10日
      const installmentCount = 5;
      const racehorseInvestmentEquivalent = 10000000; // 1000万円
      const discountAllocation = 500000; // 50万円
      const racehorseInvestment = 9500000; // 950万円

      // ===== Act =====
      const result = getInstallmentDatesAndInvestmentAmounts(
        firstInvestmentSealDate,
        installmentCount,
        racehorseInvestmentEquivalent,
        discountAllocation,
        racehorseInvestment
      );

      // ===== Assert =====
      expect(result).toHaveLength(5);
      
      // 各分割の金額を確認
      for (let i = 0; i < 5; i++) {
        expect(result[i].racehorseInvestmentEquivalent).toBe(2000000); // 10000000 / 5
        expect(result[i].discountAllocation).toBe(100000); // 500000 / 5
        expect(result[i].racehorseInvestment).toBe(1900000); // 2000000 - 100000
      }

      // 合計が元の金額と一致することを確認
      const totalEquivalent = result.reduce((sum, item) => sum + item.racehorseInvestmentEquivalent, 0);
      const totalDiscount = result.reduce((sum, item) => sum + item.discountAllocation, 0);
      const totalInvestment = result.reduce((sum, item) => sum + item.racehorseInvestment, 0);
      expect(totalEquivalent).toBe(10000000);
      expect(totalDiscount).toBe(500000);
      expect(totalInvestment).toBe(9500000);
    });
  });

  describe('getInstallmentCount', () => {
    it('分割払いでない場合は1回を返す', () => {
      const horseBirthYear = 2022;
      const firstInvestmentSealDate = new Date(2024, 2, 10); // 2歳の3月10日
      
      const result = getInstallmentCount(false, horseBirthYear, firstInvestmentSealDate);
      
      expect(result).toBe(1);
    });

    it('0歳の場合は17回分割を返す', () => {
      const horseBirthYear = 2023;
      const firstInvestmentSealDate = new Date(2023, 11, 10); // 0歳の12月10日
      
      const result = getInstallmentCount(true, horseBirthYear, firstInvestmentSealDate);
      
      expect(result).toBe(17); // 12月から翌々年4月まで17ヶ月
    });

    it('1歳の場合は5回分割を返す', () => {
      const horseBirthYear = 2023;
      const firstInvestmentSealDate = new Date(2024, 11, 10); // 1歳の12月10日
      
      const result = getInstallmentCount(true, horseBirthYear, firstInvestmentSealDate);
      
      expect(result).toBe(5); // 12月から翌年4月まで5ヶ月
    });

    it('2歳の1月の場合は4回分割を返す', () => {
      const horseBirthYear = 2022;
      const firstInvestmentSealDate = new Date(2024, 0, 10); // 2歳の1月10日
      
      const result = getInstallmentCount(true, horseBirthYear, firstInvestmentSealDate);
      
      expect(result).toBe(4); // 1月、2月、3月、4月の4回
    });

    it('2歳の2月の場合は3回分割を返す', () => {
      const horseBirthYear = 2022;
      const firstInvestmentSealDate = new Date(2024, 1, 10); // 2歳の2月10日
      
      const result = getInstallmentCount(true, horseBirthYear, firstInvestmentSealDate);
      
      expect(result).toBe(3); // 2月、3月、4月の3回
    });

    it('2歳の3月の場合は2回分割を返す', () => {
      const horseBirthYear = 2022;
      const firstInvestmentSealDate = new Date(2024, 2, 10); // 2歳の3月10日
      
      const result = getInstallmentCount(true, horseBirthYear, firstInvestmentSealDate);
      
      expect(result).toBe(2); // 3月、4月の2回
    });

    it('2歳の4月の場合は1回分割を返す', () => {
      const horseBirthYear = 2022;
      const firstInvestmentSealDate = new Date(2024, 3, 10); // 2歳の4月10日
      
      const result = getInstallmentCount(true, horseBirthYear, firstInvestmentSealDate);
      
      expect(result).toBe(1); // 4月のみ1回
    });

    it('2歳の5月以降の場合は1回分割を返す', () => {
      const horseBirthYear = 2022;
      const firstInvestmentSealDate = new Date(2024, 4, 10); // 2歳の5月10日
      
      const result = getInstallmentCount(true, horseBirthYear, firstInvestmentSealDate);
      
      expect(result).toBe(1); // 5月は4月を過ぎているので1回
    });

    it('3歳以上の場合は1回分割を返す', () => {
      const horseBirthYear = 2021;
      const firstInvestmentSealDate = new Date(2024, 2, 10); // 3歳の3月10日
      
      const result = getInstallmentCount(true, horseBirthYear, firstInvestmentSealDate);
      
      expect(result).toBe(1);
    });
  });

});
