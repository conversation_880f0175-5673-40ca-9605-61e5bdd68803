import { HorseFactory, MemberFactory, InvestmentContractFactory } from '@core-test/index';

import { InvestmentContractStatus, Horse, Member } from '@hami/prisma';
import { createInvestmentAndReturn, listInvestmentAndReturnByCreatedDate } from '@core-api/repositories/investment_and_return_repository';
import { listMemberClaimByOccurredDate } from '@core-api/repositories/member_claim_and_pay_repository';
import { processMemberClaimAndPayByCreatedDate } from './member_claim_and_pay_usecase';

describe('MemberClaimAndPayUsecase', () => {
  let horse: Horse;
  let member: Member;
  let investmentContract: any;

  beforeEach(async () => {
    // テストデータの準備
    horse = await HorseFactory.create({
      recruitmentYear: 2024,
      recruitmentNo: 1,
      horseName: 'テスト馬',
      recruitmentName: 'テスト募集馬',
      sharesTotal: 400,
      amountTotal: 36000000,
      birthYear: 2023,
      birthMonth: 5,
      birthDay: 3,
      fundStartYear: 2024,
      fundStartMonth: 4,
      fundStartDay: 1,
    });

    member = await MemberFactory.create({
      memberNumber: 10000 + Math.floor(Math.random() * 10000),
    });

    investmentContract = await InvestmentContractFactory.create({
      member: {
        connect: { memberId: member.memberId },
      },
      horse: { connect: { horseId: horse.horseId } },
      sharesNumber: 1,
      investmentAmount: 100000,
      discount: 0,
      taxRate: 8,
      investmentAmountBeforeTax: 92593,
      transactionAmount: 92593,
      monthlyDepreciation: 520,
      contractedAt: new Date(),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });
  });

  describe('processMemberClaimAndPayByCreatedDate', () => {
    it('複数の会員と馬で正常に処理が完了する', async () => {
      // ===== Arrange =====
      const createdDate = new Date('2024-01-01');
      
      // 複数の会員を作成
      const member2 = await MemberFactory.create({
        memberNumber: 20000 + Math.floor(Math.random() * 10000),
      });
      
      const member3 = await MemberFactory.create({
        memberNumber: 30000 + Math.floor(Math.random() * 10000),
      });

      // 複数の馬を作成
      const horse2 = await HorseFactory.create({
        recruitmentYear: 2024,
        recruitmentNo: 2,
        horseName: 'テスト馬2',
        recruitmentName: 'テスト募集馬2',
        sharesTotal: 400,
        amountTotal: 36000000,
        birthYear: 2023,
        birthMonth: 6,
        birthDay: 15,
        fundStartYear: 2024,
        fundStartMonth: 5,
        fundStartDay: 1,
      });
      
      const horse3 = await HorseFactory.create({
        recruitmentYear: 2024,
        recruitmentNo: 3,
        horseName: 'テスト馬3',
        recruitmentName: 'テスト募集馬3',
        sharesTotal: 400,
        amountTotal: 36000000,
        birthYear: 2023,
        birthMonth: 7,
        birthDay: 20,
        fundStartYear: 2024,
        fundStartMonth: 6,
        fundStartDay: 1,
      });

      // 複数の投資契約を作成
      await InvestmentContractFactory.create({
        member: {
          connect: { memberId: member2.memberId },
        },
        horse: { connect: { horseId: horse2.horseId } },
        sharesNumber: 2,
        investmentAmount: 200000,
        discount: 0,
        taxRate: 8,
        investmentAmountBeforeTax: 185185,
        transactionAmount: 185185,
        monthlyDepreciation: 1040,
        contractedAt: new Date(),
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      await InvestmentContractFactory.create({
        member: {
          connect: { memberId: member3.memberId },
        },
        horse: { connect: { horseId: horse3.horseId } },
        sharesNumber: 3,
        investmentAmount: 300000,
        discount: 0,
        taxRate: 8,
        investmentAmountBeforeTax: 277778,
        transactionAmount: 277778,
        monthlyDepreciation: 1560,
        contractedAt: new Date(),
        contractStatus: InvestmentContractStatus.COMPLETED,
      });

      // 複数のInvestmentAndReturnデータを作成
      const investmentAndReturns = [
        // 会員1の馬1（通常）
        {
          horseId: horse.horseId,
          memberId: member.memberId,
          progressedMonth: 1,
          billingAmount: 100,
          paymentAmount: 50,
        },
        // 会員2の馬2（通常）
        {
          horseId: horse2.horseId,
          memberId: member2.memberId,
          progressedMonth: 2,
          billingAmount: 200,
          paymentAmount: 100,
        },
        // 会員3の馬3（引退精算）
        {
          horseId: horse3.horseId,
          memberId: member3.memberId,
          progressedMonth: 999,
          billingAmount: 300,
          paymentAmount: 150,
        },
        // 会員1の馬1（追加レコード）
        {
          horseId: horse.horseId,
          memberId: member.memberId,
          progressedMonth: 3,
          billingAmount: 150,
          paymentAmount: 75,
        },
        // 会員2の馬2（追加レコード）
        {
          horseId: horse2.horseId,
          memberId: member2.memberId,
          progressedMonth: 4,
          billingAmount: 250,
          paymentAmount: 125,
        },
      ];

      for (const data of investmentAndReturns) {
        await createInvestmentAndReturn(
          data.horseId,
          data.memberId,
          createdDate,
          data.progressedMonth,
          false, // yearlyReturnTargetFlag
          1, // sharesNumber
          1000, // runningCostInvestmentTotal
          500, // insuranceInvestmentTotal
          3000, // otherInvestmentTotal
          4500, // investmentTotal
          2000, // racehorseBookValueEndOfLastMonth
          0, // investmentRefundPaidUpToLastMonth
          100, // organizerWithholdingTaxTotal
          10, // organizerWithholdingTaxCurrentMonthAddition
          50, // clubWithholdingTaxTotal
          5, // clubWithholdingTaxCurrentMonthAddition
          data.billingAmount,
          data.paymentAmount
        );
      }

      // ===== Act =====
      const result = await processMemberClaimAndPayByCreatedDate(createdDate, 3000, 10);

      // ===== Assert =====
      if (result.isErr()) {
        console.error('Usecase failed:', result.error);
      }
      expect(result.isOk()).toBe(true);

      // 作成されたデータを検証
      const investmentAndReturnsResult = await listInvestmentAndReturnByCreatedDate(createdDate);
      expect(investmentAndReturnsResult.isOk()).toBe(true);
      if (investmentAndReturnsResult.isOk()) {
        expect(investmentAndReturnsResult.value.length).toBe(5);
        expect(investmentAndReturnsResult.value[0].memberId).toBe(member.memberId);
        expect(investmentAndReturnsResult.value[0].horseId).toBe(horse.horseId);
        expect(investmentAndReturnsResult.value[0].progressedMonth).toBe(1);
        expect(investmentAndReturnsResult.value[0].billingAmount).toBe(100);
        expect(investmentAndReturnsResult.value[0].paymentAmount).toBe(50);
        expect(investmentAndReturnsResult.value[1].memberId).toBe(member2.memberId);
        expect(investmentAndReturnsResult.value[1].horseId).toBe(horse2.horseId);
        expect(investmentAndReturnsResult.value[1].progressedMonth).toBe(2);
        expect(investmentAndReturnsResult.value[1].billingAmount).toBe(200);
        expect(investmentAndReturnsResult.value[1].paymentAmount).toBe(100);
        expect(investmentAndReturnsResult.value[2].memberId).toBe(member3.memberId);
        expect(investmentAndReturnsResult.value[2].horseId).toBe(horse3.horseId);
        expect(investmentAndReturnsResult.value[2].progressedMonth).toBe(999);
        expect(investmentAndReturnsResult.value[2].billingAmount).toBe(300);
        expect(investmentAndReturnsResult.value[2].paymentAmount).toBe(150);
        expect(investmentAndReturnsResult.value[3].memberId).toBe(member.memberId);
        expect(investmentAndReturnsResult.value[3].horseId).toBe(horse.horseId);
        expect(investmentAndReturnsResult.value[3].progressedMonth).toBe(3);
        expect(investmentAndReturnsResult.value[3].billingAmount).toBe(150);
        expect(investmentAndReturnsResult.value[3].paymentAmount).toBe(75);
        expect(investmentAndReturnsResult.value[4].memberId).toBe(member2.memberId);
        expect(investmentAndReturnsResult.value[4].horseId).toBe(horse2.horseId);
        expect(investmentAndReturnsResult.value[4].progressedMonth).toBe(4);
        expect(investmentAndReturnsResult.value[4].billingAmount).toBe(250);
        expect(investmentAndReturnsResult.value[4].paymentAmount).toBe(125);
      }

      // 作成されたMemberClaimを検証
      const memberClaimResult = await listMemberClaimByOccurredDate(createdDate);
      expect(memberClaimResult.isOk()).toBe(true);
      if (memberClaimResult.isOk()) {
        expect(memberClaimResult.value.length).toBe(2);
        expect(memberClaimResult.value[0].memberId).toBe(member.memberId);
        expect(memberClaimResult.value[0].title).toBe('月会費');
        expect(memberClaimResult.value[0].description).toBe('月会費（2024年1月）');
        expect(Number(memberClaimResult.value[0].taxRate)).toBe(10);
        expect(Number(memberClaimResult.value[0].taxAmount)).toBe(300);
        expect(Number(memberClaimResult.value[0].amount)).toBe(3300);
        expect(memberClaimResult.value[1].memberId).toBe(member2.memberId);
        expect(memberClaimResult.value[1].title).toBe('月会費');
        expect(memberClaimResult.value[1].description).toBe('月会費（2024年1月）');
        expect(Number(memberClaimResult.value[1].taxRate)).toBe(10);
        expect(Number(memberClaimResult.value[1].taxAmount)).toBe(300);
        expect(Number(memberClaimResult.value[1].amount)).toBe(3300);
      }

    });

    it('引退精算の馬（progressedMonth=999）のみの場合は会費請求しない', async () => {
      // ===== Arrange =====
      const createdDate = new Date('2024-01-01');
      
      // 複数の引退精算のInvestmentAndReturnデータを作成
      const investmentAndReturns = [
        {
          horseId: horse.horseId,
          memberId: member.memberId,
          progressedMonth: 999,
          billingAmount: 100,
          paymentAmount: 50,
        },
        {
          horseId: horse.horseId,
          memberId: member.memberId,
          progressedMonth: 999,
          billingAmount: 200,
          paymentAmount: 100,
        },
        {
          horseId: horse.horseId,
          memberId: member.memberId,
          progressedMonth: 999,
          billingAmount: 300,
          paymentAmount: 150,
        },
      ];

      for (const data of investmentAndReturns) {
        await createInvestmentAndReturn(
          data.horseId,
          data.memberId,
          createdDate,
          data.progressedMonth,
          false, // yearlyReturnTargetFlag
          1, // sharesNumber
          1000, // runningCostInvestmentTotal
          500, // insuranceInvestmentTotal
          3000, // otherInvestmentTotal
          4500, // investmentTotal
          2000, // racehorseBookValueEndOfLastMonth
          0, // investmentRefundPaidUpToLastMonth
          100, // organizerWithholdingTaxTotal
          10, // organizerWithholdingTaxCurrentMonthAddition
          50, // clubWithholdingTaxTotal
          5, // clubWithholdingTaxCurrentMonthAddition
          data.billingAmount,
          data.paymentAmount
        );
      }

      // ===== Act =====
      const result = await processMemberClaimAndPayByCreatedDate(createdDate, 3000, 10);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
    });

    it('複数の日付で処理が完了する', async () => {
      // ===== Arrange =====
      const dates = [
        new Date('2024-01-01'),
        new Date('2024-02-01'),
        new Date('2024-03-01'),
      ];

      // 各日付でInvestmentAndReturnデータを作成
      for (const date of dates) {
        await createInvestmentAndReturn(
          horse.horseId,
          member.memberId,
          date,
          1, // progressedMonth
          false, // yearlyReturnTargetFlag
          1, // sharesNumber
          1000, // runningCostInvestmentTotal
          500, // insuranceInvestmentTotal
          3000, // otherInvestmentTotal
          4500, // investmentTotal
          2000, // racehorseBookValueEndOfLastMonth
          0, // investmentRefundPaidUpToLastMonth
          100, // organizerWithholdingTaxTotal
          10, // organizerWithholdingTaxCurrentMonthAddition
          50, // clubWithholdingTaxTotal
          5, // clubWithholdingTaxCurrentMonthAddition
          100, // billingAmount
          50 // paymentAmount
        );
      }

      // ===== Act & Assert =====
      for (const date of dates) {
        const result = await processMemberClaimAndPayByCreatedDate(date, 3000, 10);
        if (result.isErr()) {
          console.error('Usecase failed for date:', date, 'Error:', result.error);
        }
        expect(result.isOk()).toBe(true);
      }
    });

    it('大量のレコードで処理が完了する', async () => {
      // ===== Arrange =====
      const createdDate = new Date('2024-01-01');
      
      // 10個の会員と馬を作成
      const members = [];
      const horses = [];
      const contracts = [];

      for (let i = 0; i < 10; i++) {
        const member = await MemberFactory.create({
          memberNumber: 40000 + i * 1000 + Math.floor(Math.random() * 1000),
        });
        members.push(member);

        const horse = await HorseFactory.create({
          recruitmentYear: 2024,
          recruitmentNo: 10 + i,
          horseName: `テスト馬${10 + i}`,
          recruitmentName: `テスト募集馬${10 + i}`,
          sharesTotal: 400,
          amountTotal: 36000000,
          birthYear: 2023,
          birthMonth: 8 + i,
          birthDay: 1 + i * 3,
          fundStartYear: 2024,
          fundStartMonth: 7 + i,
          fundStartDay: 1,
        });
        horses.push(horse);

        const contract = await InvestmentContractFactory.create({
          member: {
            connect: { memberId: member.memberId },
          },
          horse: { connect: { horseId: horse.horseId } },
          sharesNumber: 1 + i,
          investmentAmount: 100000 + i * 10000,
          discount: 0,
          taxRate: 8,
          investmentAmountBeforeTax: 92593 + i * 9259,
          transactionAmount: 92593 + i * 9259,
          monthlyDepreciation: 520 + i * 52,
          contractedAt: new Date(),
          contractStatus: InvestmentContractStatus.COMPLETED,
        });
        contracts.push(contract);
      }

      // 各契約で複数のInvestmentAndReturnデータを作成
      for (let i = 0; i < contracts.length; i++) {
        for (let j = 0; j < 5; j++) { // 各契約で5レコード
          await createInvestmentAndReturn(
            horses[i].horseId,
            members[i].memberId,
            createdDate,
            1 + j, // progressedMonth
            false, // yearlyReturnTargetFlag
            1, // sharesNumber
            1000 + i * 100, // runningCostInvestmentTotal
            500 + i * 50, // insuranceInvestmentTotal
            3000 + i * 300, // otherInvestmentTotal
            4500 + i * 450, // investmentTotal
            2000 + i * 200, // racehorseBookValueEndOfLastMonth
            0, // investmentRefundPaidUpToLastMonth
            100 + i * 10, // organizerWithholdingTaxTotal
            10 + i, // organizerWithholdingTaxCurrentMonthAddition
            50 + i * 5, // clubWithholdingTaxTotal
            5 + i, // clubWithholdingTaxCurrentMonthAddition
            100 + i * 10 + j * 5, // billingAmount
            50 + i * 5 + j * 2 // paymentAmount
          );
        }
      }

      // ===== Act =====
      const result = await processMemberClaimAndPayByCreatedDate(createdDate, 3000, 10);

      // ===== Assert =====
      if (result.isErr()) {
        console.error('Usecase failed:', result.error);
      }
      expect(result.isOk()).toBe(true);
    });
  });
});
