import { Readable } from 'stream';
import { S3Client, ListObjectsV2Command, GetObjectCommand, DeleteObjectsCommand } from '@aws-sdk/client-s3';
import { HorseFactory, MemberFactory, InvestmentContractFactory } from '@core-test/index';
import { <PERSON>, Member, InvestmentContractStatus } from '@hami/prisma';

// 実際のS3クライアントを使用
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'ap-northeast-1',
  endpoint: process.env.S3_ENDPOINT || 'http://localhost:9000',
  forcePathStyle: true,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'minioadmin',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'minioadmin',
  },
});

const BUCKET_NAME = process.env.S3_BUCKET_NAME || 'test-bucket';

/**
 * S3バケット内のオブジェクト一覧を取得
 */
async function listS3Objects(prefix?: string): Promise<string[]> {
  const command = new ListObjectsV2Command({
    Bucket: BUCKET_NAME,
    Prefix: prefix,
  });

  const response = await s3Client.send(command);
  return (response.Contents || []).map((obj) => obj.Key!).filter(Boolean);
}

/**
 * S3からオブジェクトを取得してBufferとして返す
 */
async function getS3Object(key: string): Promise<Buffer> {
  const command = new GetObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  });

  const response = await s3Client.send(command);
  const stream = response.Body as Readable;

  const chunks: Buffer[] = [];
  for await (const chunk of stream) {
    chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
  }

  return Buffer.concat(chunks);
}

/**
 * テスト用のS3オブジェクトをクリーンアップ
 */
async function cleanupS3Objects(prefix: string): Promise<void> {
  const objects = await listS3Objects(prefix);

  if (objects.length === 0) return;

  const deleteCommand = new DeleteObjectsCommand({
    Bucket: BUCKET_NAME,
    Delete: {
      Objects: objects.map((key) => ({ Key: key })),
    },
  });

  await s3Client.send(deleteCommand);
}

describe('generateHorseMemberListPdfUsecase', () => {
  let horse1: Horse;
  let horse2: Horse;
  let member1: Member;
  let member2: Member;

  beforeEach(async () => {
    // テスト用のS3オブジェクトをクリーンアップ
    await cleanupS3Objects('reports/horse-member-list-');

    // テストデータ作成
    horse1 = await HorseFactory.create({
      recruitmentYear: 2023,
      recruitmentNo: 1,
      horseName: 'テスト馬1',
      recruitmentName: 'テスト募集馬1',
      sharesTotal: 400,
      amountTotal: 36000000,
      birthYear: 2020,
    });

    horse2 = await HorseFactory.create({
      recruitmentYear: 2023,
      recruitmentNo: 2,
      horseName: 'テスト馬2',
      recruitmentName: 'テスト募集馬2',
      sharesTotal: 500,
      amountTotal: 45000000,
      birthYear: 2021,
    });

    member1 = await MemberFactory.create({
      memberNumber: 10001,
      firstName: '太郎',
      lastName: '田中',
    });

    member2 = await MemberFactory.create({
      memberNumber: 10002,
      firstName: '花子',
      lastName: '佐藤',
    });

    // 出資契約作成
    await InvestmentContractFactory.create({
      member: { connect: { memberId: member1.memberId } },
      horse: { connect: { horseId: horse1.horseId } },
      sharesNumber: 2,
      investmentAmount: 180000,
      contractedAt: new Date('2023-01-15'),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    await InvestmentContractFactory.create({
      member: { connect: { memberId: member2.memberId } },
      horse: { connect: { horseId: horse2.horseId } },
      sharesNumber: 1,
      investmentAmount: 90000,
      contractedAt: new Date('2023-02-20'),
      contractStatus: InvestmentContractStatus.COMPLETED,
    });
  });

  afterEach(async () => {
    // テスト後のクリーンアップ
    await cleanupS3Objects('reports/horse-member-list-');
  });

  describe('実際のPDF生成とS3アップロード', () => {
    it('PDFを生成してMinIOに正常にアップロードされる', async () => {
      // 実際のusecaseをimport
      const { generateHorseMemberListPdfUsecase } = await import('./generate_horse_member_list_pdf_usecase');

      // ===== Act =====
      const result = await generateHorseMemberListPdfUsecase();

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const data = result.value;

        // 1. レスポンスの基本構造をチェック
        expect(data.pdfFileKey).toMatch(/^reports\/horse-member-list-\d+\.pdf$/);
        expect(data.downloadUrl).toMatch(/^https?:\/\/.+/);
        expect(data.totalHorses).toBe(2);
        expect(data.totalMembers).toBe(2);
        expect(typeof data.generatedAt).toBe('bigint');

        // 2. 実際にS3にファイルがアップロードされているかチェック
        const uploadedObjects = await listS3Objects('reports/horse-member-list-');
        expect(uploadedObjects).toHaveLength(1);
        expect(uploadedObjects[0]).toBe(data.pdfFileKey);

        // 3. アップロードされたファイルの内容をチェック
        const uploadedPdfBuffer = await getS3Object(data.pdfFileKey);
        expect(uploadedPdfBuffer).toBeInstanceOf(Buffer);
        expect(uploadedPdfBuffer.length).toBeGreaterThan(0);

        // 4. PDFファイルの基本的な構造をチェック（PDFヘッダー）
        const pdfHeader = uploadedPdfBuffer.subarray(0, 4).toString();
        expect(pdfHeader).toBe('%PDF');

        // 5. PDFファイルサイズが妥当かチェック（最低限のサイズ）
        expect(uploadedPdfBuffer.length).toBeGreaterThan(1000); // 1KB以上

        console.log(`✅ PDF生成成功: ${data.pdfFileKey} (${uploadedPdfBuffer.length} bytes)`);
      }
    }, 30000); // 30秒のタイムアウト

    it('馬が存在しない場合でも空のPDFが生成される', async () => {
      // ===== Arrange =====
      // 全ての馬と出資契約を削除
      await vPrisma.client.investmentContract.deleteMany();
      await vPrisma.client.horse.deleteMany();

      const { generateHorseMemberListPdfUsecase } = await import('./generate_horse_member_list_pdf_usecase');

      // ===== Act =====
      const result = await generateHorseMemberListPdfUsecase();

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const data = result.value;

        expect(data.totalHorses).toBe(0);
        expect(data.totalMembers).toBe(0);

        // S3にファイルがアップロードされているかチェック
        const uploadedObjects = await listS3Objects('reports/horse-member-list-');
        expect(uploadedObjects).toHaveLength(1);

        // 空データでもPDFが生成されているかチェック
        const uploadedPdfBuffer = await getS3Object(data.pdfFileKey);
        expect(uploadedPdfBuffer.length).toBeGreaterThan(500); // 空でも基本構造は存在

        const pdfHeader = uploadedPdfBuffer.subarray(0, 4).toString();
        expect(pdfHeader).toBe('%PDF');

        console.log(`✅ 空PDF生成成功: ${data.pdfFileKey} (${uploadedPdfBuffer.length} bytes)`);
      }
    }, 30000);

    it('大量データでもPDF生成が正常に動作する', async () => {
      // ===== Arrange =====
      // 追加の馬と会員を作成（合計10頭、20名）
      const additionalHorses = [];
      const additionalMembers = [];

      for (let i = 3; i <= 10; i++) {
        const horse = await HorseFactory.create({
          recruitmentYear: 2023,
          recruitmentNo: i,
          horseName: `テスト馬${i}`,
          recruitmentName: `テスト募集馬${i}`,
          sharesTotal: 400,
          amountTotal: 36000000,
          birthYear: 2020,
        });
        additionalHorses.push(horse);

        // 各馬に2名の会員を追加
        for (let j = 1; j <= 2; j++) {
          const memberNumber = 10000 + (i - 1) * 2 + j;
          const member = await MemberFactory.create({
            memberNumber,
            firstName: `太郎${memberNumber}`,
            lastName: `田中${memberNumber}`,
          });
          additionalMembers.push(member);

          await InvestmentContractFactory.create({
            member: { connect: { memberId: member.memberId } },
            horse: { connect: { horseId: horse.horseId } },
            sharesNumber: 1,
            investmentAmount: 90000,
            contractedAt: new Date('2023-01-15'),
            contractStatus: InvestmentContractStatus.COMPLETED,
          });
        }
      }

      const { generateHorseMemberListPdfUsecase } = await import('./generate_horse_member_list_pdf_usecase');

      // ===== Act =====
      const result = await generateHorseMemberListPdfUsecase();

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const data = result.value;

        expect(data.totalHorses).toBe(10); // 2 + 8 = 10頭
        expect(data.totalMembers).toBe(18); // 2 + 16 = 18名

        // 大量データでもS3アップロードが成功
        const uploadedObjects = await listS3Objects('reports/horse-member-list-');
        expect(uploadedObjects).toHaveLength(1);

        const uploadedPdfBuffer = await getS3Object(data.pdfFileKey);
        expect(uploadedPdfBuffer.length).toBeGreaterThan(3000); // 大量データなので3KB以上

        console.log(`✅ 大量データPDF生成成功: ${data.pdfFileKey} (${uploadedPdfBuffer.length} bytes)`);
      }
    }, 60000); // 60秒のタイムアウト
  });
});
