/**
 * 出資金・分配金のモデル
 */
export interface InvestmentAndReturnModel {
    horseId: number;
    memberId: number;
    createdDate: Date;
    runningCostInvestmentTotal: number;
    insuranceInvestmentTotal: number;
    otherInvestmentTotal: number;
    investmentTotal: number;
    racehorseBookValueEndOfLastMonth: number;
    investmentRefundPaidUpToLastMonth: number;
    progressedMonth: number;
    yearlyReturnTargetFlag: boolean;
    organizerWithholdingTaxTotal: number;
    organizerWithholdingTaxCurrentMonthAddition: number;
    clubWithholdingTaxTotal: number;
    clubWithholdingTaxCurrentMonthAddition: number;
    billingAmount: number;
    paymentAmount: number;
}

type CreateInvestmentAndReturnModelParams = {
    horseId: number;
    memberId: number;
    createdDate?: Date;
    runningCostInvestmentTotal?: number;
    insuranceInvestmentTotal?: number;
    otherInvestmentTotal?: number;
    investmentTotal?: number;
    racehorseBookValueEndOfLastMonth?: number;
    investmentRefundPaidUpToLastMonth?: number;
    progressedMonth?: number;
    yearlyReturnTargetFlag?: boolean;
    organizerWithholdingTaxTotal?: number;
    organizerWithholdingTaxCurrentMonthAddition?: number;
    clubWithholdingTaxTotal?: number;
    clubWithholdingTaxCurrentMonthAddition?: number;
    billingAmount?: number;
    paymentAmount?: number;
};

export const createInvestmentAndReturnModel = ({
    horseId,
    memberId,
    createdDate = new Date(),
    runningCostInvestmentTotal = 0,
    insuranceInvestmentTotal = 0,
    otherInvestmentTotal = 0,
    investmentTotal = 0,
    racehorseBookValueEndOfLastMonth = 0,
    investmentRefundPaidUpToLastMonth = 0,
    progressedMonth = 0,
    yearlyReturnTargetFlag = false,
    organizerWithholdingTaxTotal = 0,
    organizerWithholdingTaxCurrentMonthAddition = 0,
    clubWithholdingTaxTotal = 0,
    clubWithholdingTaxCurrentMonthAddition = 0,
    billingAmount = 0,
    paymentAmount = 0,
}: CreateInvestmentAndReturnModelParams): InvestmentAndReturnModel => {
    return {
        horseId,
        memberId,
        createdDate,
        runningCostInvestmentTotal,
        insuranceInvestmentTotal,
        otherInvestmentTotal,
        investmentTotal,
        racehorseBookValueEndOfLastMonth,
        investmentRefundPaidUpToLastMonth,
        progressedMonth,
        yearlyReturnTargetFlag,
        organizerWithholdingTaxTotal,
        organizerWithholdingTaxCurrentMonthAddition,
        clubWithholdingTaxTotal,
        clubWithholdingTaxCurrentMonthAddition,
        billingAmount,
        paymentAmount,
    };
};
