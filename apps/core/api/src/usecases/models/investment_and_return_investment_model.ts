export interface InvestmentAndReturnInvestmentModel {
    racehorseInvestmentEquivalent: number;
    discountAllocation: number;
    racehorseInvestment: number;
    runningCost: number;
    subsidy: number;
    retroactiveRunningCost: number;
    runningCostInvestment: number;
    insuranceInvestment: number;
    otherInvestment: number;
    currentMonthInvestmentTotal: number;
}

type CreateInvestmentAndReturnInvestmentModelParams = {
    sharesTotal: number;
    discountAllocation: number;
    racehorseInvestment: number;
    runningCost: number;
    subsidy: number;
    retroactiveRunningCost: number;
    insuranceInvestment: number;
    otherInvestment: number;
};

export const createInvestmentAndReturnInvestmentModel = ({
    sharesTotal,
    discountAllocation,
    racehorseInvestment,
    runningCost,
    subsidy,
    retroactiveRunningCost,
    insuranceInvestment,
    otherInvestment,
}: CreateInvestmentAndReturnInvestmentModelParams): InvestmentAndReturnInvestmentModel => {
    const sharedRunningCost = Math.floor(runningCost / sharesTotal);
    const sharedSubsidy = Math.floor(subsidy / sharesTotal);
    const sharedInsuranceInvestment = Math.floor(insuranceInvestment / sharesTotal);
    const sharedOtherInvestment = Math.floor(otherInvestment / sharesTotal);


    const racehorseInvestmentEquivalent = racehorseInvestment + discountAllocation;
    const runningCostInvestment = sharedRunningCost + retroactiveRunningCost - sharedSubsidy;
    const currentMonthInvestmentTotal = racehorseInvestment + runningCostInvestment + sharedInsuranceInvestment + sharedOtherInvestment;

    return {
        racehorseInvestmentEquivalent: racehorseInvestmentEquivalent,
        discountAllocation,
        racehorseInvestment,
        runningCost: sharedRunningCost,
        subsidy: sharedSubsidy,
        retroactiveRunningCost: retroactiveRunningCost,
        runningCostInvestment,
        insuranceInvestment: sharedInsuranceInvestment,
        otherInvestment: sharedOtherInvestment,
        currentMonthInvestmentTotal,
    };
};