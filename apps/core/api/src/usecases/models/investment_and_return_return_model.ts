import { ReturnCategory } from '@hami/prisma';

/**
 * 出資金・分配金の分配モデル
 */
export interface InvestmentAndReturnReturnModel {
    returnCategory: ReturnCategory;
    investmentRefundPaidUpToLastMonth: number | null;
    refundableInvestmentAmount: number;
    distributionTargetAmount: number;
    distributionTargetAmountRefundable: number;
    distributionTargetAmountProfit: number;
    distributionTargetAmountWithholdingTax: number;
    distributionAmount: number;
    refundableInvestmentAmountCarriedForward: number;
}

type CreateInvestmentAndReturnReturnModelParams = {
    returnCategory: ReturnCategory;
    investmentRefundPaidUpToLastMonth?: number | null;
    refundableInvestmentAmount?: number;
    distributionTargetAmount?: number;
    taxRate: number;
};

export const createInvestmentAndReturnReturnModel = ({
    returnCategory,
    investmentRefundPaidUpToLastMonth = null,
    refundableInvestmentAmount = 0,
    distributionTargetAmount = 0,
    taxRate,
}: CreateInvestmentAndReturnReturnModelParams): InvestmentAndReturnReturnModel => {
    const distributionTargetAmountRefundable = (refundableInvestmentAmount > distributionTargetAmount) ? distributionTargetAmount : refundableInvestmentAmount;
    const distributionTargetAmountProfit = distributionTargetAmount - distributionTargetAmountRefundable;
    const distributionTargetAmountWithholdingTax = Math.floor(distributionTargetAmountProfit * taxRate);
    const distributionAmount = distributionTargetAmount - distributionTargetAmountWithholdingTax;
    const refundableInvestmentAmountCarriedForward = refundableInvestmentAmount - distributionTargetAmountRefundable;
    return {
        returnCategory,
        investmentRefundPaidUpToLastMonth,
        refundableInvestmentAmount,
        distributionTargetAmount,
        distributionTargetAmountRefundable,
        distributionTargetAmountProfit,
        distributionTargetAmountWithholdingTax,
        distributionAmount,
        refundableInvestmentAmountCarriedForward,
    };
};
