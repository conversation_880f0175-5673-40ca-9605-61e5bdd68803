import {
  AnnualBundleFactory,
  HorseFactory,
  MemberFactory,
  InvestmentApplicationFactory,
  InvestmentContractFactory,
  AnnualBundleHorseFactory,
} from '@core-test/index';
import { AnnualBundleRecruitmentStatus, AnnualBundlePublishStatus, InvestmentContractStatus } from '@hami/prisma';
import { completeAnnualBundleInvestmentApplicationsUsecase } from '../usecases/complete_annual_bundle_investment_applications_usecase';

describe.sequential('completeAnnualBundleInvestmentApplicationsUsecase', () => {
  it('年度バンドルに紐づく全馬の契約を作成できる', async () => {
    // ===== Arrange =====
    const contractDate = new Date('2025-04-01');

    const bundle = await AnnualBundleFactory.create({
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      shares: 500,
    });
    const horses = await HorseFactory.createList([
      { sharesTotal: 100, amountTotal: 10000000, birthYear: 2021 },
      { sharesTotal: 100, amountTotal: 24000000, birthYear: 2021 },
    ]);

    await AnnualBundleHorseFactory.createList(
      horses.map((horse) => ({ annualBundle: { connect: { annualBundleId: bundle.annualBundleId } }, horse: { connect: { horseId: horse.horseId } } }))
    );

    const member = await MemberFactory.create();

    const application = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
      requestedNumber: 3,
      allocatedNumber: 3,
      rejectPartialAllocation: false,
      isWhole: true,
      installmentPayment: false,
      appliedAt: contractDate,
    });


    // ===== Act =====
    const results = await completeAnnualBundleInvestmentApplicationsUsecase(
      [
        {
          annualBundleId: bundle.annualBundleId,
          investmentApplicationId: application.investmentApplicationId,
        },
      ],
      contractDate,
    );

    // ===== Assert =====
    expect(results).toHaveLength(1);
    expect(results[0]?.success).toBe(true);
    expect(results[0]?.status).toBe('contractCompleted');
    expect(results[0]?.contractedShares).toBe(3);

    const completedApplication = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: application.investmentApplicationId },
    });
    expect(completedApplication?.contracted).toBe(true);

    const horseInvestmentShares = await vPrisma.client.horseInvestmentShares.findFirst({
      where: { horseId: horses[0].horseId },
    });
    expect(horseInvestmentShares?.memberInvestmentShares).toBe(3);

    const contracts = await vPrisma.client.investmentContract.findMany({
      where: { memberId: member.memberId },
      orderBy: { horseId: 'asc' },
    });
    expect(contracts).toHaveLength(horses.length);
    expect(contracts.map((contract) => contract.sharesNumber)).toEqual(Array(horses.length).fill(3));

    const investments = await vPrisma.client.memberRacehorseInvestment.findMany({
      where: { memberId: member.memberId },
    });
    expect(investments).toBeDefined();
    expect(investments.length).toBe(horses.length);
    expect(investments).toEqual(
      expect.arrayContaining(
        [
          expect.objectContaining({
            horseId: horses[0].horseId,
            memberId: member.memberId,
            racehorseInvestmentEquivalent: 300000, // 10000000 / 100 * 3
            discountAllocation: 0,
            racehorseInvestment: 300000, // 10000000 / 100 * 3
            investmentDate: expect.any(Date),
            createdAt: expect.any(Date),
            updatedAt: expect.any(Date),
          }),
          expect.objectContaining({
            horseId: horses[1].horseId,
            memberId: member.memberId,
            racehorseInvestmentEquivalent: 720000, // 24000000 / 100 * 3
            discountAllocation: 0,
            racehorseInvestment: 720000, // 7200000 / 100 * 3
            investmentDate: expect.any(Date),
            createdAt: expect.any(Date),
            updatedAt: expect.any(Date),
          }),
        ]
      )
    );

    const memberClaims = await vPrisma.client.memberClaim.findMany({
      where: { memberId: member.memberId },
    });
    expect(memberClaims).toBeDefined();
    // 入会金はユーザーごとに1件しか存在しないことを確認
    expect(memberClaims.length).toBe(1);
    expect(memberClaims).toEqual(
      [
        expect.objectContaining({
          memberId: member.memberId,
          title: '入会金',
          description: '入会金',
          taxRate: expect.objectContaining({
            toString: expect.any(Function),
          }),
          amount: 22000,
          taxAmount: 2000,
          occurredDate: expect.any(Date),
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        }),
      ]
    );
  });

  it('年度バンドルに紐づく馬が存在しない場合はエラーになる', async () => {
    const bundle = await AnnualBundleFactory.create({ shares: 100, recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE, publishStatus: AnnualBundlePublishStatus.PUBLIC });
    const member = await MemberFactory.create();

    const application = await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: member.memberId } },
        annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
        requestedNumber: 2,
        allocatedNumber: 2,
        rejectPartialAllocation: false,
        isWhole: false,
        installmentPayment: false,
        appliedAt: new Date(),
      },
    });

    const results = await completeAnnualBundleInvestmentApplicationsUsecase(
      [
        {
          annualBundleId: bundle.annualBundleId,
          investmentApplicationId: application.investmentApplicationId,
        },
      ],
      new Date(),
    );

    expect(results).toHaveLength(1);
    expect(results[0]?.success).toBe(false);
    expect(results[0]?.errorMessage).toBe('年度バンドルに紐づく馬が存在しません');
  });

  it('馬の募集口数を超える場合はエラーになる', async () => {
    const bundle = await AnnualBundleFactory.create({ shares: 100, recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE, publishStatus: AnnualBundlePublishStatus.PUBLIC });
    const horse = await HorseFactory.create({ sharesTotal: 5, amountTotal: 500000, horseName: '超過テスト' });

    await vPrisma.client.annualBundleHorse.create({ data: { annualBundleId: bundle.annualBundleId, horseId: horse.horseId } });

    const existingMember = await MemberFactory.create();
    await InvestmentContractFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      member: { connect: { memberId: existingMember.memberId } },
      sharesNumber: 3,
      contractStatus: InvestmentContractStatus.COMPLETED,
    });

    const member = await MemberFactory.create();
    const application = await vPrisma.client.investmentApplication.create({
      data: {
        member: { connect: { memberId: member.memberId } },
        annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
        requestedNumber: 3,
        allocatedNumber: 3,
        rejectPartialAllocation: false,
        isWhole: false,
        installmentPayment: false,
        appliedAt: new Date(),
      },
    });

    const results = await completeAnnualBundleInvestmentApplicationsUsecase(
      [
        {
          annualBundleId: bundle.annualBundleId,
          investmentApplicationId: application.investmentApplicationId,
        },
      ],
      new Date(),
    );

    expect(results).toHaveLength(1);
    expect(results[0]?.success).toBe(false);
    expect(results[0]?.errorMessage).toBe('馬 超過テスト の募集口数を超えるため契約できません');
    expect(results[0]?.status).toBeUndefined();
  });

  it('既に契約済みの年度バンドル申込はエラーになる', async () => {
    const bundle = await AnnualBundleFactory.create({ shares: 300, recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE, publishStatus: AnnualBundlePublishStatus.PUBLIC });
    const horse = await HorseFactory.create({ sharesTotal: 100, amountTotal: 1000000 });
    await vPrisma.client.annualBundleHorse.create({ data: { annualBundleId: bundle.annualBundleId, horseId: horse.horseId } });
    const member = await MemberFactory.create();
    const application = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
      requestedNumber: 2,
      allocatedNumber: 2,
      rejected: false,
      contracted: true,
    });

    const results = await completeAnnualBundleInvestmentApplicationsUsecase([
      { annualBundleId: bundle.annualBundleId, investmentApplicationId: application.investmentApplicationId },
    ], new Date());

    expect(results).toHaveLength(1);
    expect(results[0]?.success).toBe(false);
    expect(results[0]?.status).toBe('contractCompleted');
    expect(results[0]?.errorMessage).toBe('既に契約済みの申込です');
  });

  it('割当口数が設定されていない年度バンドル申込はエラーになる', async () => {
    const bundle = await AnnualBundleFactory.create({ shares: 300, recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE, publishStatus: AnnualBundlePublishStatus.PUBLIC });
    const horse = await HorseFactory.create({ sharesTotal: 50, amountTotal: 500000 });
    await vPrisma.client.annualBundleHorse.create({ data: { annualBundleId: bundle.annualBundleId, horseId: horse.horseId } });
    const member = await MemberFactory.create();
    const application = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
      requestedNumber: 2,
      allocatedNumber: 0,
      rejected: false,
      contracted: false,
    });

    const results = await completeAnnualBundleInvestmentApplicationsUsecase([
      { annualBundleId: bundle.annualBundleId, investmentApplicationId: application.investmentApplicationId },
    ], new Date());

    expect(results).toHaveLength(1);
    expect(results[0]?.success).toBe(false);
    expect(results[0]?.errorMessage).toBe('割当口数が設定されていません');
  });

  it('退会済み会員の年度バンドル申込はエラーになる', async () => {
    const bundle = await AnnualBundleFactory.create({ shares: 400, recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE, publishStatus: AnnualBundlePublishStatus.PUBLIC });
    const horse = await HorseFactory.create({ sharesTotal: 80, amountTotal: 800000 });
    await vPrisma.client.annualBundleHorse.create({ data: { annualBundleId: bundle.annualBundleId, horseId: horse.horseId } });
    const member = await MemberFactory.create();
    await vPrisma.client.member.update({ where: { memberId: member.memberId }, data: { retirementDate: new Date() } });
    const application = await InvestmentApplicationFactory.create({
      member: { connect: { memberId: member.memberId } },
      annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
      requestedNumber: 1,
      allocatedNumber: 1,
      rejected: false,
      contracted: false,
    });

    const results = await completeAnnualBundleInvestmentApplicationsUsecase([
      { annualBundleId: bundle.annualBundleId, investmentApplicationId: application.investmentApplicationId },
    ], new Date());

    expect(results).toHaveLength(1);
    expect(results[0]?.success).toBe(false);
    expect(results[0]?.errorMessage).toBe('退会済み会員の申込は契約できません');
  });
});