import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getHorseMemberListPdfDownloadUrlUsecase } from './get_horse_member_list_pdf_download_url_usecase';

// 実際のS3クライアントを使用
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'ap-northeast-1',
  endpoint: process.env.S3_ENDPOINT || 'http://localhost:9000',
  forcePathStyle: true,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'minioadmin',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'minioadmin',
  },
});

const BUCKET_NAME = process.env.S3_BUCKET_NAME || 'test-bucket';

/**
 * テスト用PDFファイルをS3にアップロード
 */
async function uploadTestPdf(key: string): Promise<void> {
  const testPdfContent = Buffer.from(
    '%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000010 00000 n \n0000000079 00000 n \n0000000173 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n253\n%%EOF'
  );

  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: testPdfContent,
    ContentType: 'application/pdf',
  });

  await s3Client.send(command);
}

/**
 * S3からテストファイルを削除
 */
async function deleteTestPdf(key: string): Promise<void> {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  });

  try {
    await s3Client.send(command);
  } catch (error) {
    // ファイルが存在しない場合は無視
    if ((error as any).name !== 'NoSuchKey') {
      throw error;
    }
  }
}

describe('getHorseMemberListPdfDownloadUrlUsecase', () => {
  const testFileKey = 'reports/horse-member-list-1234567890.pdf';

  beforeEach(async () => {
    // テスト用PDFファイルをアップロード
    await uploadTestPdf(testFileKey);
  });

  afterEach(async () => {
    // テスト後のクリーンアップ
    await deleteTestPdf(testFileKey);
  });

  describe('実際のS3署名付きURL生成', () => {
    it('存在するファイルに対して有効な署名付きURLを生成できる', async () => {
      // ===== Act =====
      const result = await getHorseMemberListPdfDownloadUrlUsecase(testFileKey);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const data = result.value;

        // 1. レスポンスの基本構造をチェック
        expect(data.downloadUrl).toMatch(/^https?:\/\/.+/);
        expect(typeof data.expiresAt).toBe('bigint');

        // 2. 有効期限が現在時刻から約1時間後であることを確認
        const now = Date.now();
        const expiresAtMs = Number(data.expiresAt);
        const expectedExpiresAt = now + 3600 * 1000; // 1時間後

        // 誤差を考慮して±10秒の範囲で確認
        expect(expiresAtMs).toBeGreaterThanOrEqual(expectedExpiresAt - 10000);
        expect(expiresAtMs).toBeLessThanOrEqual(expectedExpiresAt + 10000);

        // 3. 実際にURLからファイルをダウンロードできるかテスト
        const response = await fetch(data.downloadUrl);
        expect(response.ok).toBe(true);
        expect(response.headers.get('content-type')).toContain('application/pdf');

        const downloadedContent = await response.arrayBuffer();
        const downloadedBuffer = Buffer.from(downloadedContent);

        // 4. ダウンロードしたファイルがPDFであることを確認
        const pdfHeader = downloadedBuffer.subarray(0, 4).toString();
        expect(pdfHeader).toBe('%PDF');

        console.log(`✅ 署名付きURL生成成功: ${data.downloadUrl}`);
        console.log(`✅ ファイルダウンロード成功: ${downloadedBuffer.length} bytes`);
      }
    }, 30000);

    it('無効なファイルキー形式の場合はS3にアクセスせずにエラーを返す', async () => {
      const invalidFileKeys = [
        'invalid-file-key.pdf',
        'reports/invalid-format.pdf',
        'reports/horse-member-list.pdf', // 数字がない
        'reports/horse-member-list-abc.pdf', // 数字以外の文字
        'reports/horse-member-list-123.txt', // 拡張子が違う
        'other/horse-member-list-123.pdf', // ディレクトリが違う
        '', // 空文字
        'reports/horse-member-list-.pdf', // 数字がない（ハイフンのみ）
      ];

      for (const invalidFileKey of invalidFileKeys) {
        // ===== Act =====
        const result = await getHorseMemberListPdfDownloadUrlUsecase(invalidFileKey);

        // ===== Assert =====
        expect(result.isErr()).toBe(true);

        if (result.isErr()) {
          expect(result.error.message).toBe('Invalid file key format');
        }

        console.log(`✅ 無効なファイルキー検証成功: ${invalidFileKey}`);
      }
    });

    it('正しい形式のファイルキーは受け入れられる', async () => {
      const validFileKeys = [
        'reports/horse-member-list-1.pdf',
        'reports/horse-member-list-123456789.pdf',
        'reports/horse-member-list-1234567890123.pdf',
        'reports/horse-member-list-9999999999999.pdf',
      ];

      for (const validFileKey of validFileKeys) {
        // ===== Act =====
        const result = await getHorseMemberListPdfDownloadUrlUsecase(validFileKey);

        // ===== Assert =====
        expect(result.isOk()).toBe(true);

        if (result.isOk()) {
          expect(result.value.downloadUrl).toMatch(/^https?:\/\/.+/);
        }

        console.log(`✅ 有効なファイルキー検証成功: ${validFileKey}`);
      }
    });
  });
});
