import { ResultAsync } from 'neverthrow';
import { listInvestmentAndReturnByCreatedDate } from '../repositories/investment_and_return_repository';
import { createMemberClaim, createMemberClaimAndPay, deleteMemberClaim, deleteMemberClaimAndPay, listMemberClaimByOccurredDate } from '../repositories/member_claim_and_pay_repository';

/**
 * 指定のcreatedDateについてInvestmentAndReturnテーブルの処理を実行
 * @param createdDate 処理対象の日付
 * @returns 処理結果
 */
export async function processMemberClaimAndPayByCreatedDate(createdDate: Date, membershipDues: number, taxRate: number): Promise<ResultAsync<void, Error>> {
  // 競走馬への出資に関して、会員に対して行う請求と支払いを計算していく

  // 指定のcreatedDateについてInvestmentAndReturnからデータを取得し、memberとhorseのリレーションを含める
  const investmentAndReturnsResult = await listInvestmentAndReturnByCreatedDate(createdDate);
  
  if (investmentAndReturnsResult.isErr()) {
    return ResultAsync.fromPromise(Promise.reject(investmentAndReturnsResult.error), (error) => new Error(`Failed to process member claim and pay: ${error}`));
  }

  const investmentAndReturns = investmentAndReturnsResult.value;
  
  // memberについてデータを蓄積し、horseごとのbillingAmountとpayAmountを合計する
  const _memberDataMap = new Map<number, {
    memberId: number;
    memberName: string;
    horseData: Map<number, {
      horseId: number;
      horseName: string;
      billingAmount: number;
      payAmount: number;
      progressedMonth: number;
    }>;
  }>();
  
  for (const investmentAndReturn of investmentAndReturns) {
    // horseのリレーションが含まれている場合の処理
    const horse = investmentAndReturn.horse;
    if (horse) {
      const member = investmentAndReturn.member;
      const horse = investmentAndReturn.horse;
      const memberId = member.memberId;
      const horseId = horse.horseId;
      const memberName = `${member.firstName} ${member.lastName}`;
      const horseName = horse.horseName;
      
      // memberのデータが存在しない場合は初期化
      if (!_memberDataMap.has(memberId)) {
        _memberDataMap.set(memberId, {
          memberId,
          memberName,
          horseData: new Map(),
        });
      }
      
      const memberData = _memberDataMap.get(memberId);
      if (!memberData) continue;
      
      // horseのデータが存在しない場合は初期化
      if (!memberData.horseData.has(horseId)) {
        memberData.horseData.set(horseId, {
          horseId,
          horseName,
          billingAmount: 0,
          payAmount: 0,
          progressedMonth: investmentAndReturn.progressedMonth,
        });
      }
      
      const horseData = memberData.horseData.get(horseId);
      if (!horseData) continue;
      
      // billingAmountとpayAmountを合計
      const billingAmount = investmentAndReturn.billingAmount || 0;
      const payAmount = investmentAndReturn.paymentAmount || 0;
      horseData.billingAmount += billingAmount;
      horseData.payAmount += payAmount;
      horseData.progressedMonth = investmentAndReturn.progressedMonth;
      
    } else {
      // investmentContractIdのみを使用する場合
      // 何もしない（将来の実装用）
    }
  }

  // memberDataMapから、memberごとに「会費が必要か」を判定する
  for (const memberData of _memberDataMap.values()) {
    // 指定のcreatedDateについてMemberClaimテーブルのデータを削除
    try {
      await deleteMemberClaim(memberData.memberId, createdDate);
    } catch (e) {
      console.error('deleteMemberClaimでエラー:', e);
      throw e;
    }
  
    const memberId = memberData.memberId;
    const horseData = memberData.horseData;

    // horseDataの中で、billingAmountが0でないものがあるかどうかを判定
    const hasNonRetirementProgressedMonth = Array.from(horseData.values()).some(horse => horse.progressedMonth != 999);
    if (hasNonRetirementProgressedMonth) {
      // 引退精算の馬以外がいる場合は、会費を支払う
      const taxAmount = membershipDues * taxRate / 100;
      const amount = membershipDues + taxAmount;
      // 月会費の表示テキストを作成する
      const description = `月会費（${createdDate.getFullYear()}年${createdDate.getMonth() + 1}月）`;
      try {
          await createMemberClaim(memberId, createdDate, "月会費", description, taxRate, taxAmount, amount);
      } catch (e) {
          console.error('createMemberClaimでエラー:', e, { memberId, createdDate, taxRate, taxAmount, amount });
          throw e;
      }
    }
  }

  // 今のところ会費しか追加費用が発生しないので、請求と支払いのデータを会員ごとに作成する
  const memberClaimAndPays = await listMemberClaimByOccurredDate(createdDate);
  if (memberClaimAndPays.isErr()) {
    return ResultAsync.fromPromise(Promise.reject(memberClaimAndPays.error), (error) => new Error(`Failed to process member claim and pay: ${error}`));
  }
  const memberClaimAndPaysData = memberClaimAndPays.value;

  for (const memberData of _memberDataMap.values()) {
    // 指定のcreatedDateについてMemberClaimAndPayテーブルのデータを削除
    await deleteMemberClaimAndPay(memberData.memberId, createdDate);

    const memberId = memberData.memberId;
    const horseData = memberData.horseData;

    // horseDataの中で、billingAmountの値を合計する
    const totalBillingAmount = Array.from(horseData.values()).reduce((sum, horse) => sum + horse.billingAmount, 0);
    const totalPayAmount = Array.from(horseData.values()).reduce((sum, horse) => sum + horse.payAmount, 0);

    // その他のmemberClaimのデータを取得する
    const memberClaims = memberClaimAndPaysData.filter(memberClaim => memberClaim.memberId === memberId);

    // 請求金額を合計
    const totalClaimAmount = memberClaims.reduce((sum, claim) => sum + claim.amount, 0) + totalBillingAmount;
    // 請求金額と支払い金額の差額を計算
    const difference = totalClaimAmount - totalPayAmount;
    // 差額が0でない場合は、請求と支払いのデータを作成する
    if (difference !== 0) {
      try {
        await createMemberClaimAndPay(memberId, createdDate, totalClaimAmount, totalPayAmount);
      } catch (e) {
        console.error('createMemberClaimAndPayでエラー:', e, { memberId, createdDate, totalClaimAmount, totalPayAmount });
        throw e;
      }
    }
  }

  // 結果をログ出力
  return ResultAsync.fromPromise(Promise.resolve(), (error) => new Error(`Failed to process member claim and pay: ${error}`));
}
