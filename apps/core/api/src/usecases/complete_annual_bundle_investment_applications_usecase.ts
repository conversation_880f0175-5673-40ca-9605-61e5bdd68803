import { InvestmentContractStatus, Prisma } from '@hami/prisma';
import { client } from '@core-api/utils/prisma';
import { MEMBERS_FIRST_CONTRACT_AMOUNT, TAX_RATE } from '../utils/const';
import { getInvestmentSealDate, getInstallmentCount, getInstallmentDatesAndInvestmentAmounts } from '../utils/investment_utils';

function buildMemberName(lastName?: string | null, firstName?: string | null) {
  const parts = [lastName ?? '', firstName ?? ''];
  return parts.join(' ').trim();
}


type CompleteAnnualBundleInvestmentApplicationCommand = {
  investmentApplicationId: number;
  annualBundleId: number;
};

type CompleteAnnualBundleInvestmentApplicationResult = {
  investmentApplicationId: number;
  success: boolean;
  errorMessage?: string;
  contractedShares?: number;
  memberName?: string;
  memberNumber?: number;
  status?: 'applied' | 'accepted' | 'contractCompleted' | 'rejected';
};

export async function completeAnnualBundleInvestmentApplicationsUsecase(
  commands: CompleteAnnualBundleInvestmentApplicationCommand[],
  contractDate: Date,
): Promise<CompleteAnnualBundleInvestmentApplicationResult[]> {
  const sorted = [...commands].sort((a, b) => a.investmentApplicationId - b.investmentApplicationId);
  const results: CompleteAnnualBundleInvestmentApplicationResult[] = [];

  for (const command of sorted) {
    try {
      const application = await client.investmentApplication.findUnique({
        where: { investmentApplicationId: command.investmentApplicationId },
        include: {
          member: {
            select: {
              memberId: true,
              memberNumber: true,
              lastName: true,
              firstName: true,
              retirementDate: true,
            },
          },
          annualBundle: {
            include: {
              horses: {
                include: {
                  horse: {
                    select: {
                      horseId: true,
                      horseName: true,
                      recruitmentName: true,
                      sharesTotal: true,
                      amountTotal: true,
                      birthYear: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!application || application.annualBundleId !== command.annualBundleId) {
        results.push({
          investmentApplicationId: command.investmentApplicationId,
          success: false,
          errorMessage: '対象の年度バンドル申込が存在しません',
        });
        continue;
      }

      if (application.rejected) {
        results.push({
          investmentApplicationId: command.investmentApplicationId,
          success: false,
          errorMessage: '申込は拒否済みのため契約できません',
          status: 'rejected',
        });
        continue;
      }

      if (application.contracted) {
        results.push({
          investmentApplicationId: command.investmentApplicationId,
          success: false,
          errorMessage: '既に契約済みの申込です',
          status: 'contractCompleted',
        });
        continue;
      }

      if (!application.allocatedNumber || application.allocatedNumber <= 0) {
        results.push({
          investmentApplicationId: command.investmentApplicationId,
          success: false,
          errorMessage: '割当口数が設定されていません',
        });
        continue;
      }

      if (application.member?.retirementDate) {
        results.push({
          investmentApplicationId: command.investmentApplicationId,
          success: false,
          errorMessage: '退会済み会員の申込は契約できません',
        });
        continue;
      }

      if (!application.annualBundle || application.annualBundle.horses.length === 0) {
        results.push({
          investmentApplicationId: command.investmentApplicationId,
          success: false,
          errorMessage: '年度バンドルに紐づく馬が存在しません',
        });
        continue;
      }

      const allocatedSharesPerHorse = application.allocatedNumber;
      const horses = application.annualBundle.horses;

      await client.$transaction(async (tx) => {
        for (const bundleHorse of horses) {
          const horse = bundleHorse.horse;
          if (!horse) {
            throw new Error('年度バンドルに紐づく馬情報の取得に失敗しました');
          }

          const currentContracts = await tx.investmentContract.aggregate({
            where: { horseId: horse.horseId },
            _sum: { sharesNumber: true },
          });
          const currentShares = Number(currentContracts._sum.sharesNumber ?? 0);
          if (currentShares + allocatedSharesPerHorse > horse.sharesTotal) {
            throw new Error(`馬 ${horse.horseName ?? horse.recruitmentName ?? ''} の募集口数を超えるため契約できません`);
          }

          const existingContract = await tx.investmentContract.findFirst({
            where: {
              memberId: application.memberId,
            },
          });
          const membersFirstContract = existingContract ? false : true;

          const investmentAmount = Math.floor((horse.amountTotal / horse.sharesTotal) * allocatedSharesPerHorse);
          const discount = 0;
          const investmentAmountBeforeTax = Math.ceil(investmentAmount / (1 + TAX_RATE / 100));

          await tx.investmentContract.create({
            data: {
              horseId: horse.horseId,
              memberId: application.memberId,
              sharesNumber: allocatedSharesPerHorse,
              investmentAmount,
              contractStatus: InvestmentContractStatus.COMPLETED,
              contractedAt: contractDate,
              discount,
              taxRate: TAX_RATE,
              investmentAmountBeforeTax,
            },
          });

          await tx.horseInvestmentShares.upsert({
            where: { horseId: horse.horseId },
            create: {
              horseId: horse.horseId,
              memberInvestmentShares: allocatedSharesPerHorse,
            },
            update: {
              memberInvestmentShares: { increment: allocatedSharesPerHorse },
            },
          });

          const firstInvestmentSealDate = getInvestmentSealDate(contractDate);
          const installmentCount = getInstallmentCount(
            application.installmentPayment,
            horse.birthYear ?? contractDate.getFullYear(),
            firstInvestmentSealDate,
          );
          const installmentDatesAndAmounts = getInstallmentDatesAndInvestmentAmounts(
            firstInvestmentSealDate,
            installmentCount,
            investmentAmount,
            discount,
            investmentAmount - discount,
          );

          for (const installment of installmentDatesAndAmounts) {
            await tx.memberRacehorseInvestment.create({
              data: {
                horseId: horse.horseId,
                memberId: application.memberId,
                investmentDate: installment.investmentDate,
                racehorseInvestmentEquivalent: installment.racehorseInvestmentEquivalent,
                discountAllocation: installment.discountAllocation,
                racehorseInvestment: installment.racehorseInvestment,
              },
            });
          }

          if (membersFirstContract) {
            const amountWithoutTax = MEMBERS_FIRST_CONTRACT_AMOUNT;
            const taxAmount = (amountWithoutTax * TAX_RATE) / 100;
            await tx.memberClaim.create({
              data: {
                memberId: application.memberId,
                occurredDate: firstInvestmentSealDate,
                title: '入会金',
                description: '入会金',
                taxRate: TAX_RATE,
                amount: amountWithoutTax + taxAmount,
                taxAmount,
              },
            });
          }
        }

        await tx.investmentApplication.update({
          where: { investmentApplicationId: application.investmentApplicationId },
          data: { contracted: true },
        });
      }, { isolationLevel: Prisma.TransactionIsolationLevel.Serializable });

      results.push({
        investmentApplicationId: application.investmentApplicationId,
        success: true,
        contractedShares: application.allocatedNumber ?? 0,
        memberName: buildMemberName(application.member?.lastName, application.member?.firstName) || undefined,
        memberNumber: application.member?.memberNumber ?? undefined,
        status: 'contractCompleted',
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : '不明なエラーが発生しました';
      results.push({
        investmentApplicationId: command.investmentApplicationId,
        success: false,
        errorMessage: message,
      });
    }
  }

  return results;
}
