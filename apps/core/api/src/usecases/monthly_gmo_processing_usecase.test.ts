import { MemberFactory } from '@core-test/index';
import { vi } from 'vitest';
import { Member, BankAccountRegistration, BankAccountRegistrationStatus } from '@hami/prisma';
import { startMonthlyGmoProcessing, executeMonthlyGmoProcessing } from './monthly_gmo_processing_usecase';

// GMO APIクライアントのモック
const mockEntryTransaction = vi.fn();
const mockExecuteTransfer = vi.fn();
const mockRegisterAccount = vi.fn();
const mockRegisterDeposit = vi.fn();

vi.mock('../external_apis/gmo_pg', () => ({
  createGmoBankTransferClient: vi.fn(() => ({
    entryTransaction: mockEntryTransaction,
    executeTransfer: mockExecuteTransfer,
  })),
  createGmoRemittanceClient: vi.fn(() => ({
    registerAccount: mockRegisterAccount,
    registerDeposit: mockRegisterDeposit,
  })),
  GmoApiError: class GmoApiError extends Error {
    constructor(
      public errorCode: string,
      public errorDetail: string
    ) {
      super(`GMO API Error: ${errorCode} - ${errorDetail}`);
      this.name = 'GmoApiError';
    }
  },
  GmoNetworkError: class GmoNetworkError extends Error {
    constructor(message: string) {
      super(`GMO Network Error: ${message}`);
      this.name = 'GmoNetworkError';
    }
  },
  GmoTimeoutError: class GmoTimeoutError extends Error {
    constructor(message: string) {
      super(`GMO Timeout Error: ${message}`);
      this.name = 'GmoTimeoutError';
    }
  },
}));

describe('MonthlyGmoProcessingUsecase', () => {
  let member: Member;
  let bankAccount: BankAccountRegistration;

  beforeEach(async () => {
    // モックをリセット
    vi.clearAllMocks();

    // テストデータの作成
    member = await MemberFactory.create({
      memberNumber: 10000 + Math.floor(Math.random() * 10000),
    });

    bankAccount = await vPrisma.client.bankAccountRegistration.create({
      data: {
        memberId: member.memberId,
        isActive: true,
        registrationStatus: BankAccountRegistrationStatus.SUCCESS,
        bankCode: '0009',
        branchCode: '015',
        accountType: 1, // 普通預金
        accountNumber: '1234567',
        accountName: 'テスト　タロウ',
      },
    });

    await vPrisma.client.memberClaimAndPay.create({
      data: {
        memberId: member.memberId,
        occurredDate: new Date('2024-01-01'), // targetDateと一致させる
        claimAmount: 50000,
        payAmount: 0,
      },
    });
  });

  describe('startMonthlyGmoProcessing', () => {
    it('新規バッチが正常に作成される', async () => {
      // ===== Arrange =====
      const targetDate = new Date('2024-01-01');

      // ===== Act =====
      const result = await startMonthlyGmoProcessing(targetDate);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const batch = result.value;
        expect(batch.targetDate).toEqual(targetDate);
        expect(batch.status).toBe('PENDING');
        expect(batch.totalTransferCount).toBe(1);
        expect(batch.totalRemittanceCount).toBe(0);
      }
    });

    it('既存のバッチが存在する場合はエラーになる', async () => {
      // ===== Arrange =====
      const targetDate = new Date('2024-01-01');

      // 最初のバッチを作成
      const firstResult = await startMonthlyGmoProcessing(targetDate);
      expect(firstResult.isOk()).toBe(true);

      // ===== Act =====
      const secondResult = await startMonthlyGmoProcessing(targetDate);

      // ===== Assert =====
      expect(secondResult.isErr()).toBe(true);
      if (secondResult.isErr()) {
        expect(secondResult.error.message).toContain('already exists');
      }
    });

    it('失敗したバッチは再作成できる', async () => {
      // ===== Arrange =====
      const targetDate = new Date('2024-01-01');

      // 最初のバッチを作成して失敗状態にする
      const firstResult = await startMonthlyGmoProcessing(targetDate);
      expect(firstResult.isOk()).toBe(true);

      if (firstResult.isOk()) {
        await vPrisma.client.monthlyGmoProcessingBatch.update({
          where: { monthlyGmoProcessingBatchId: firstResult.value.monthlyGmoProcessingBatchId },
          data: { status: 'FAILED' },
        });
      }

      // 既存のバッチを削除してから再作成をテスト
      await vPrisma.client.monthlyGmoProcessingLog.deleteMany();
      await vPrisma.client.monthlyGmoProcessingRecord.deleteMany();
      await vPrisma.client.monthlyGmoProcessingBatch.deleteMany();

      // ===== Act =====
      const secondResult = await startMonthlyGmoProcessing(targetDate);

      // ===== Assert =====
      expect(secondResult.isOk()).toBe(true);

      if (secondResult.isOk()) {
        const newBatch = secondResult.value;
        expect(newBatch.status).toBe('PENDING');
        expect(newBatch.totalTransferCount).toBe(1);
        expect(newBatch.totalRemittanceCount).toBe(0);
      }
    });

    it('送金処理のデータも正しく処理される', async () => {
      // ===== Arrange =====
      const targetDate = new Date('2024-01-01');

      // 送金処理用のデータを追加作成
      await vPrisma.client.memberClaimAndPay.create({
        data: {
          memberId: member.memberId,
          occurredDate: new Date('2024-01-01'), // targetDateと一致させる
          claimAmount: 0,
          payAmount: 30000, // 正の金額は送金処理
        },
      });

      // ===== Act =====
      const result = await startMonthlyGmoProcessing(targetDate);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const batch = result.value;
        expect(batch.totalTransferCount).toBe(1);
        expect(batch.totalRemittanceCount).toBe(1);
      }
    });
  });

  describe('executeMonthlyGmoProcessing', () => {
    let batchId: number;

    beforeEach(async () => {
      // テスト用バッチを作成
      const targetDate = new Date('2024-01-01');
      const batchResult = await startMonthlyGmoProcessing(targetDate);
      expect(batchResult.isOk()).toBe(true);

      if (batchResult.isOk()) {
        batchId = batchResult.value.monthlyGmoProcessingBatchId;
      }
    });

    it('振替処理が正常に実行される', async () => {
      // ===== Arrange =====
      // GMO APIの成功レスポンスをモック
      mockEntryTransaction.mockResolvedValue({
        AccessID: 'test_access_id',
        AccessPass: 'test_access_pass',
      });

      mockExecuteTransfer.mockResolvedValue({
        AccessID: 'test_access_id',
        TargetDate: '********',
        RequestAcceptEndDate: '20240204',
        TransferReturnDate: '20240206',
      });

      // ===== Act =====
      const result = await executeMonthlyGmoProcessing(batchId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const processingResult = result.value;
        expect(processingResult.batchId).toBe(batchId);
        expect(processingResult.completedTransferCount).toBe(1);
        expect(processingResult.failedTransferCount).toBe(0);
        expect(processingResult.errors).toHaveLength(0);

        // TargetDate が次の27日になっていることを確認（2024-01-27）
        const records = await vPrisma.client.monthlyGmoProcessingRecord.findMany({
          where: { monthlyGmoProcessingBatchId: batchId },
        });
        expect(records.length).toBeGreaterThan(0);
        const targetDateStr = records[0].targetDate?.toISOString().slice(0, 10);
        expect(targetDateStr).toBe('2024-01-27');
      }

      // GMO APIが正しく呼び出されたことを確認
      expect(mockEntryTransaction).toHaveBeenCalledTimes(1);
      expect(mockExecuteTransfer).toHaveBeenCalledTimes(1);
    });

    it('送金処理が正常に実行される', async () => {
      // ===== Arrange =====
      // 送金処理用のデータを追加
      await vPrisma.client.memberClaimAndPay.create({
        data: {
          memberId: member.memberId,
          occurredDate: new Date('2024-01-01'), // targetDateと一致させる
          claimAmount: 0,
          payAmount: 30000,
        },
      });

      // バッチを再作成して送金処理を含める（外部キー制約のため順序に注意）
      await vPrisma.client.monthlyGmoProcessingLog.deleteMany();
      await vPrisma.client.monthlyGmoProcessingRecord.deleteMany();
      await vPrisma.client.monthlyGmoProcessingBatch.deleteMany();

      const newBatchResult = await startMonthlyGmoProcessing(new Date('2024-01-01'));
      expect(newBatchResult.isOk()).toBe(true);

      if (newBatchResult.isOk()) {
        batchId = newBatchResult.value.monthlyGmoProcessingBatchId;
      }

      // GMO送金APIの成功レスポンスをモック
      mockRegisterAccount.mockResolvedValue({
        Bank_ID: 'test_bank_id',
        Method: '1',
      });

      mockRegisterDeposit.mockResolvedValue({
        Deposit_ID: 'test_deposit_id',
        Bank_ID: 'test_bank_id',
        Method: '1',
        Amount: '30000',
        Bank_Fee: '165',
      });

      // ===== Act =====
      const result = await executeMonthlyGmoProcessing(batchId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const processingResult = result.value;
        expect(processingResult.completedRemittanceCount).toBeGreaterThan(0);
        expect(processingResult.failedRemittanceCount).toBe(0);
      }

      // GMO送金APIが正しく呼び出されたことを確認
      expect(mockRegisterAccount).toHaveBeenCalled();
      expect(mockRegisterDeposit).toHaveBeenCalled();
    });

    it('GMO APIエラー時にリトライが実行される', async () => {
      // ===== Arrange =====
      // GMOクライアントレベルでのリトライをシミュレート
      // 最初の2回はエラー、3回目で成功（GMOクライアント内部でリトライされる）
      mockEntryTransaction.mockResolvedValueOnce({
        AccessID: 'test_access_id',
        AccessPass: 'test_access_pass',
      });

      mockExecuteTransfer.mockResolvedValue({
        AccessID: 'test_access_id',
        TargetDate: '********',
      });

      // ===== Act =====
      const result = await executeMonthlyGmoProcessing(batchId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const processingResult = result.value;
        expect(processingResult.completedTransferCount).toBe(1);
        expect(processingResult.failedTransferCount).toBe(0);
      }

      // GMO APIが呼び出されたことを確認（リトライはGMOクライアント内部で処理）
      expect(mockEntryTransaction).toHaveBeenCalledTimes(1);
      expect(mockExecuteTransfer).toHaveBeenCalledTimes(1);
    });

    it('最大リトライ回数を超えた場合は失敗になる', async () => {
      // ===== Arrange =====
      const { GmoApiError } = await import('../external_apis/gmo_pg');

      // 処理記録を事前に最大リトライ回数に設定
      const records = await vPrisma.client.monthlyGmoProcessingRecord.findMany({
        where: { monthlyGmoProcessingBatchId: batchId },
      });

      if (records.length > 0) {
        await vPrisma.client.monthlyGmoProcessingRecord.update({
          where: { monthlyGmoProcessingRecordId: records[0].monthlyGmoProcessingRecordId },
          data: {
            retryCount: 3, // 最大リトライ回数に設定
            status: 'FAILED',
          },
        });
      }

      // GMO APIは常にエラーを返す
      mockEntryTransaction.mockRejectedValue(new GmoApiError('E01', 'システムエラー'));

      // ===== Act =====
      const result = await executeMonthlyGmoProcessing(batchId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const processingResult = result.value;
        // 最大リトライ回数に達した記録は処理されない（スキップされる）
        expect(processingResult.failedTransferCount).toBe(0);
        expect(processingResult.completedTransferCount).toBe(0);
      }

      // データベースで失敗状態が維持されていることを確認
      const updatedRecords = await vPrisma.client.monthlyGmoProcessingRecord.findMany({
        where: { monthlyGmoProcessingBatchId: batchId },
      });
      expect(updatedRecords[0].status).toBe('FAILED');
      expect(updatedRecords[0].retryCount).toBe(3); // 最大リトライ回数
    });

    it('リトライ不可能なエラーの場合は即座に失敗になる', async () => {
      // ===== Arrange =====
      const { GmoApiError } = await import('../external_apis/gmo_pg');

      // パラメータエラー（リトライ不可）
      mockEntryTransaction.mockRejectedValue(new GmoApiError('M01', 'パラメータエラー'));

      // ===== Act =====
      const result = await executeMonthlyGmoProcessing(batchId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const processingResult = result.value;
        expect(processingResult.failedTransferCount).toBe(1);
        expect(processingResult.completedTransferCount).toBe(0);
      }

      // GMO APIが呼び出されたことを確認
      expect(mockEntryTransaction).toHaveBeenCalledTimes(1);

      // データベースでリトライ回数が0のまま失敗状態になっていることを確認
      const records = await vPrisma.client.monthlyGmoProcessingRecord.findMany({
        where: { monthlyGmoProcessingBatchId: batchId },
      });
      expect(records[0].status).toBe('FAILED');
      expect(records[0].retryCount).toBe(0);
    });

    it('口座登録が存在しない場合は失敗になる', async () => {
      // ===== Arrange =====
      // 口座登録を無効化
      await vPrisma.client.bankAccountRegistration.update({
        where: { bankAccountRegistrationId: bankAccount.bankAccountRegistrationId },
        data: { isActive: false },
      });

      // ===== Act =====
      const result = await executeMonthlyGmoProcessing(batchId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const processingResult = result.value;
        expect(processingResult.failedTransferCount).toBe(1);
        expect(processingResult.completedTransferCount).toBe(0);
      }

      // エラーメッセージが記録されていることを確認
      const records = await vPrisma.client.monthlyGmoProcessingRecord.findMany({
        where: { monthlyGmoProcessingBatchId: batchId },
      });
      expect(records[0].status).toBe('FAILED');
      expect(records[0].errorMessage).toContain('No active bank account found');
    });

    it('処理ログが正しく記録される', async () => {
      // ===== Arrange =====
      mockEntryTransaction.mockResolvedValue({
        AccessID: 'test_access_id',
        AccessPass: 'test_access_pass',
      });

      mockExecuteTransfer.mockResolvedValue({
        AccessID: 'test_access_id',
        TargetDate: '********',
      });

      // ===== Act =====
      const result = await executeMonthlyGmoProcessing(batchId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      // 処理ログが記録されていることを確認
      const records = await vPrisma.client.monthlyGmoProcessingRecord.findMany({
        where: { monthlyGmoProcessingBatchId: batchId },
      });

      const logs = await vPrisma.client.monthlyGmoProcessingLog.findMany({
        where: {
          monthlyGmoProcessingRecordId: {
            in: records.map((r) => r.monthlyGmoProcessingRecordId),
          },
        },
        orderBy: { createdAt: 'asc' },
      });

      expect(logs.length).toBeGreaterThan(0);

      // 各処理ステップのログが存在することを確認
      const logMessages = logs.map((log) => log.message);
      expect(logMessages.some((msg) => msg.includes('Starting GMO bank transfer entry'))).toBe(true);
      expect(logMessages.some((msg) => msg.includes('GMO bank transfer entry completed'))).toBe(true);
      expect(logMessages.some((msg) => msg.includes('Starting GMO bank transfer execution'))).toBe(true);
      expect(logMessages.some((msg) => msg.includes('GMO bank transfer execution completed'))).toBe(true);
    });
  });
});
