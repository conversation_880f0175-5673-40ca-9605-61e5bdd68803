import { MailVerificationFactory } from '@core-test/factories/mail_verification_factory';
import { MemberFactory } from '@core-test/factories/member_factory';
import { MembershipApplicationComplianceDocumentGroupReviewLogFactory } from '@core-test/factories/membership_application_compliance_document_group_review_log_factory';
import { MembershipApplicationComplianceReviewLogFactory } from '@core-test/factories/membership_application_compliance_review_log_factory';
import { MembershipApplicationDocumentGroupReviewLogFactory } from '@core-test/factories/membership_application_document_group_review_log_factory';
import { MembershipApplicationFactory } from '@core-test/factories/membership_application_factory';
import { MembershipApplicationReviewLogFactory } from '@core-test/factories/membership_application_review_log_factory';
import { UserFactory } from '@core-test/factories/user_factory';
import { vi } from 'vitest';
import { ReviewType as ReviewTypePrisma } from '@hami/prisma';
import { exportApprovedMembershipApplicationsCsvUsecase } from './export_approved_membership_applications_csv_usecase';

// S3のモックは削除（実際のS3を使用）

describe('exportApprovedMembershipApplicationsCsvUsecase', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('承認済み申し込みのCSVエクスポートが成功する', async () => {
    // ===== Arrange =====
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: {
        connect: { mailVerificationId: mailVerification.mailVerificationId },
      },
    });

    // 承認ログを作成
    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    await MembershipApplicationDocumentGroupReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    await MembershipApplicationComplianceReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    await MembershipApplicationComplianceDocumentGroupReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    // 会員データを作成
    const user = await UserFactory.create({ email: mailVerification.email });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      temporaryPassword: 'abcd1234',
    });

    // ===== Act =====
    const result = await exportApprovedMembershipApplicationsCsvUsecase();

    // ===== Assert =====
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      const data = result.value;
      expect(data.fileKey).toMatch(/^exports\/membership-applications\/approved-\d{14}\.csv$/);
      expect(data.downloadUrl).toContain('exports/membership-applications/approved-');
      expect(data.total).toBeGreaterThan(0);
      
      // CSVファイルをダウンロードして内容を確認
      try {
        const response = await fetch(data.downloadUrl);
        const csvContent = await response.text();
        
        // ヘッダーを確認
        const lines = csvContent.split('\n');
        
        // データ行を確認
        if (lines.length > 1) {
          const csvData = lines[1].split(',');
          expect(csvData[0]).toBe(application.membershipApplicationId.toString());
          expect(csvData[1]).toBe(member.memberId.toString());
          expect(csvData[2]).toBe(member.memberNumber.toString());
          expect(csvData[3]).toBe('個人');
          expect(csvData[4]).toBe(''); // 個人の場合はcorporate_nameは空
          expect(csvData[5]).toBe(`${member.lastName} ${member.firstName}`); // nameカラムに個人名
          expect(csvData[6]).toBe(member.postalCode); // 会員データの郵便番号
          expect(csvData[7]).toBe(member.prefecture); // 会員データの都道府県
          expect(csvData[8]).toBe(member.address); // 会員データの住所
          expect(csvData[9]).toBe(member.apartment || ''); // 会員データのマンション名
          expect(csvData[10]).toBe(member.phoneNumber); // 会員データの電話番号
          expect(csvData[11]).toBe(user.email);
          expect(csvData[12]).toBe(member.temporaryPassword);
        }
      } catch (error) {
        console.error('CSVダウンロードエラー:', error);
      }
    }
  });

  it('法人の承認済み申し込みのCSVエクスポートが成功する（会員データなし）', async () => {
    // ===== Arrange =====
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: {
        connect: { mailVerificationId: mailVerification.mailVerificationId },
      },
      applicantType: 'CORPORATE',
      corporateName: '株式会社テスト',
      representativeName: '代表太郎',
    });

    // 承認ログを作成
    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    await MembershipApplicationDocumentGroupReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    await MembershipApplicationComplianceReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    await MembershipApplicationComplianceDocumentGroupReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    // ===== Act =====
    const result = await exportApprovedMembershipApplicationsCsvUsecase();

    // ===== Assert =====
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      const data = result.value;
      expect(data.fileKey).toMatch(/^exports\/membership-applications\/approved-\d{14}\.csv$/);
      expect(data.downloadUrl).toContain('exports/membership-applications/approved-');
      expect(data.total).toBeGreaterThan(0);
      
      // CSVファイルをダウンロードして内容を確認
      try {
        const response = await fetch(data.downloadUrl);
        const csvContent = await response.text();
        
        // ヘッダーを確認
        const lines = csvContent.split('\n');
        
        // データ行を確認
        if (lines.length > 1) {
          const csvData = lines[1].split(',');
          expect(csvData[0]).toBe(application.membershipApplicationId.toString());
          expect(csvData[1]).toBe(''); // 会員データがない場合は空
          expect(csvData[2]).toBe(''); // 会員データがない場合は空
          expect(csvData[3]).toBe('法人');
          expect(csvData[4]).toBe(application.corporateName); // corporate_nameカラムに会社名
          expect(csvData[5]).toBe(application.representativeName); // nameカラムに代表者名
          expect(csvData[6]).toBe(application.postalCode); // 申し込みデータの郵便番号
          expect(csvData[7]).toBe(application.prefecture); // 申し込みデータの都道府県
          expect(csvData[8]).toBe(application.address); // 申し込みデータの住所
          expect(csvData[9]).toBe(application.apartment || ''); // 申し込みデータのマンション名
          expect(csvData[10]).toBe(application.phoneNumber); // 申し込みデータの電話番号
          expect(csvData[11]).toBe(mailVerification.email);
          expect(csvData[12]).toBe(''); // 会員データがない場合は空
        }
      } catch (error) {
        console.error('CSVダウンロードエラー:', error);
      }
    }
  });

  it('法人の承認済み申し込みのCSVエクスポートが成功する（会員データあり）', async () => {
    // ===== Arrange =====
    const mailVerification = await MailVerificationFactory.create();
    const application = await MembershipApplicationFactory.create({
      mailVerification: {
        connect: { mailVerificationId: mailVerification.mailVerificationId },
      },
      applicantType: 'CORPORATE',
      corporateName: '株式会社テスト',
      representativeName: '代表太郎',
    });

    // 承認ログを作成
    await MembershipApplicationReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    await MembershipApplicationDocumentGroupReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    await MembershipApplicationComplianceReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    await MembershipApplicationComplianceDocumentGroupReviewLogFactory.create({
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      reviewType: ReviewTypePrisma.APPROVE,
    });

    // 会員データを作成（法人）
    const user = await UserFactory.create({ email: mailVerification.email });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
      membershipApplication: {
        connect: { membershipApplicationId: application.membershipApplicationId },
      },
      temporaryPassword: 'corp1234',
      corporate: {
        create: {
          corporateName: '株式会社会員データ',
          corporateNameKana: 'カブシキガイシャカイインデータ',
          corporateNumber: '1234567890123',
          representativeName: '会員太郎',
          representativeNameKana: 'カイインタロウ',
          representativePosition: '代表取締役',
        },
      },
    });

    // ===== Act =====
    const result = await exportApprovedMembershipApplicationsCsvUsecase();

    // ===== Assert =====
    expect(result.isOk()).toBe(true);
    if (result.isOk()) {
      const data = result.value;
      expect(data.fileKey).toMatch(/^exports\/membership-applications\/approved-\d{14}\.csv$/);
      expect(data.downloadUrl).toContain('exports/membership-applications/approved-');
      expect(data.total).toBeGreaterThan(0);
      
      // CSVファイルをダウンロードして内容を確認
      try {
        const response = await fetch(data.downloadUrl);
        const csvContent = await response.text();
        
        // ヘッダーを確認
        const lines = csvContent.split('\n');
        
        // データ行を確認
        if (lines.length > 1) {
          const csvData = lines[1].split(',');
          expect(csvData[0]).toBe(application.membershipApplicationId.toString());
          expect(csvData[1]).toBe(member.memberId.toString());
          expect(csvData[2]).toBe(member.memberNumber.toString());
          expect(csvData[3]).toBe('法人');
          expect(csvData[4]).toBe('株式会社会員データ'); // corporate_nameカラムに会社名
          expect(csvData[5]).toBe('会員太郎'); // nameカラムに代表者名
          expect(csvData[6]).toBe(member.postalCode); // 会員データの郵便番号
          expect(csvData[7]).toBe(member.prefecture); // 会員データの都道府県
          expect(csvData[8]).toBe(member.address); // 会員データの住所
          expect(csvData[9]).toBe(member.apartment || ''); // 会員データのマンション名
          expect(csvData[10]).toBe(member.phoneNumber); // 会員データの電話番号
          expect(csvData[11]).toBe(user.email);
          expect(csvData[12]).toBe(member.temporaryPassword);
        }
      } catch (error) {
        console.error('CSVダウンロードエラー:', error);
      }
    }
  });

});
