import { Readable } from 'stream';
import { S3Client, ListObjectsV2Command, GetObjectCommand, DeleteObjectsCommand } from '@aws-sdk/client-s3';
import { HorseFactory, MemberFactory } from '@core-test/index';
import { HorseIncomeOtherName, HorseIncomePrizeOrganizer } from '@hami/core-admin-api-schema/horse_income_service_pb';
import { Horse, Member, InvestmentAndReturn, MemberClaimAndPay, MemberClaim, ReturnCategory } from '@hami/prisma';
import { generateInvestmentAndReturnPdfUsecase } from './generate_investment_and_return_pdf_usecase';
import { createHorseIncomeOther, createHorseIncomePrize } from '../repositories/horse_income_repository';
import { createInvestmentAndReturn, createInvestmentAndReturnInvestment, createInvestmentAndReturnReturn } from '../repositories/investment_and_return_repository';
import { createMemberClaim, createMemberClaimAndPay } from '../repositories/member_claim_and_pay_repository';

// 実際のS3クライアントを使用
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'ap-northeast-1',
  endpoint: process.env.S3_ENDPOINT || 'http://localhost:9000',
  forcePathStyle: true,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'minioadmin',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'minioadmin',
  },
});

const BUCKET_NAME = process.env.S3_BUCKET_NAME || 'test-bucket';

/**
 * S3バケット内のオブジェクト一覧を取得
 */
async function listS3Objects(prefix?: string): Promise<string[]> {
  const command = new ListObjectsV2Command({
    Bucket: BUCKET_NAME,
    Prefix: prefix,
  });

  const response = await s3Client.send(command);
  return (response.Contents || []).map((obj) => obj.Key!).filter(Boolean);
}

/**
 * S3からオブジェクトを取得してBufferとして返す
 */
async function getS3Object(key: string): Promise<Buffer> {
  const command = new GetObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  });

  const response = await s3Client.send(command);
  const stream = response.Body as Readable;

  const chunks: Buffer[] = [];
  for await (const chunk of stream) {
    chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
  }

  return Buffer.concat(chunks);
}

/**
 * テスト用のS3オブジェクトをクリーンアップ
 */
async function cleanupS3Objects(prefix: string): Promise<void> {
  const objects = await listS3Objects(prefix);

  if (objects.length === 0) return;

  const deleteCommand = new DeleteObjectsCommand({
    Bucket: BUCKET_NAME,
    Delete: {
      Objects: objects.map((key) => ({ Key: key })),
    },
  });

  await s3Client.send(deleteCommand);
}

describe('generateInvestmentAndReturnPdfUsecase', () => {
  let horse1: Horse;
  let horse2: Horse;
  let member1: Member;
  let member2: Member;
  let occurredDate: Date;

  beforeEach(async () => {
    // テスト用のS3オブジェクトをクリーンアップ
    await cleanupS3Objects('statements/investment-and-return-');

    // テスト用の発生日を設定
    occurredDate = new Date('2024-03-15');

    // テストデータ作成
    horse1 = await HorseFactory.create({
        horseName: '',
        recruitmentYear: 2025,
        recruitmentNo: 1,
        recruitmentName: 'オパールムーンの24',
        sharesTotal: 400,
        amountTotal: 36000000,
    });
      horse2 = await HorseFactory.create({
        horseName: 'イクイノックス',
        recruitmentYear: 2020,
        recruitmentNo: 2,
        recruitmentName: 'シャトーブランシュの19',
        sharesTotal: 400,
        amountTotal: 36000000,
    });
    member1 = await MemberFactory.create({
        memberNumber: 10001,
        lastName: '織田',
        firstName: '信長',
        lastNameKana: 'オダ',
        firstNameKana: 'ノブナガ',
    });

    member2 = await MemberFactory.create({
      memberNumber: 10002,
      firstName: '花子',
      lastName: '佐藤',
    });

    // デバッグ: メンバーが正しく作成されたか確認
    console.log('Created members:', { member1: member1.memberId, member2: member2.memberId });

    // MemberClaimAndPay作成
    await vPrisma.client.memberClaimAndPay.create({
      data: {
        memberId: member1.memberId,
        occurredDate: occurredDate,
        claimAmount: 50000,
        payAmount: 30000,
      },
    });

    await vPrisma.client.memberClaimAndPay.create({
      data: {
        memberId: member2.memberId,
        occurredDate: occurredDate,
        claimAmount: 75000,
        payAmount: 45000,
      },
    });

    // MemberClaim作成
    await vPrisma.client.memberClaim.create({
      data: {
        memberId: member1.memberId,
        occurredDate: occurredDate,
        title: '源泉所得税',
        description: '源泉所得税の請求',
        taxRate: '0.1021',
        taxAmount: 5105,
        amount: 50000,
      },
    });

    await vPrisma.client.memberClaim.create({
      data: {
        memberId: member2.memberId,
        occurredDate: occurredDate,
        title: '源泉所得税',
        description: '源泉所得税の請求',
        taxRate: '0.1021',
        taxAmount: 7658,
        amount: 75000,
      },
    });
  });

  afterEach(async () => {
    // テスト後のクリーンアップ
    await cleanupS3Objects('statements/investment-and-return-');
  });

  describe('実際のPDF生成とS3アップロード', () => {
    it('PDFを生成してMinIOに正常にアップロードされる', async () => {
      // 実際のusecaseをimport
      const { generateInvestmentAndReturnPdfUsecase } = await import('./generate_investment_and_return_pdf_usecase');

      // ===== Act =====
      const result = await generateInvestmentAndReturnPdfUsecase(occurredDate);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const data = result.value;

        // 1. レスポンスの基本構造をチェック（配列であることを確認）
        expect(Array.isArray(data)).toBe(true);
        // InvestmentAndReturnがない場合、PDFは生成されない可能性があるため、0以上をチェック
        expect(data.length).toBeGreaterThanOrEqual(0);

        // 2. 各PDFの基本構造をチェック
        for (const pdfResult of data) {
          expect(pdfResult.pdfFileKey).toMatch(/^statements\/investment-and-return-\d+-\d{4}\.pdf$/);
          expect(pdfResult.totalHorses).toBeGreaterThanOrEqual(0);
        }

        // 3. 実際にS3にファイルがアップロードされているかチェック
        const uploadedObjects = await listS3Objects('statements/investment-and-return-');
        expect(uploadedObjects).toHaveLength(data.length); // 生成されたPDFの数

        // 4. 各アップロードされたファイルの内容をチェック
        for (const pdfResult of data) {
          const uploadedPdfBuffer = await getS3Object(pdfResult.pdfFileKey);
          expect(uploadedPdfBuffer).toBeInstanceOf(Buffer);
          expect(uploadedPdfBuffer.length).toBeGreaterThan(0);

          // 5. PDFファイルの基本的な構造をチェック（PDFヘッダー）
          const pdfHeader = uploadedPdfBuffer.subarray(0, 4).toString();
          expect(pdfHeader).toBe('%PDF');

          // 6. PDFファイルサイズが妥当かチェック（最低限のサイズ）
          expect(uploadedPdfBuffer.length).toBeGreaterThan(1000); // 1KB以上

          console.log(`✅ PDF生成成功: ${pdfResult.pdfFileKey} (${uploadedPdfBuffer.length} bytes)`);
        }
      }
    }, 30000); // 30秒のタイムアウト

    it('データが存在しない場合でも空の配列が返される', async () => {
      // ===== Arrange =====
      // 全てのデータを削除
      await vPrisma.client.memberClaim.deleteMany();
      await vPrisma.client.investmentAndReturn.deleteMany();
      await vPrisma.client.memberClaimAndPay.deleteMany();
      await vPrisma.client.member.deleteMany();
      await vPrisma.client.horse.deleteMany();

      const { generateInvestmentAndReturnPdfUsecase } = await import('./generate_investment_and_return_pdf_usecase');

      // ===== Act =====
      const result = await generateInvestmentAndReturnPdfUsecase(occurredDate);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const data = result.value;

        // 空の配列が返されることを確認
        expect(Array.isArray(data)).toBe(true);
        expect(data.length).toBe(0);

        // S3にファイルがアップロードされていないことを確認
        const uploadedObjects = await listS3Objects('statements/investment-and-return-');
        expect(uploadedObjects).toHaveLength(0);

        console.log('✅ 空データの場合の処理成功');
      }
    }, 30000);

    it('異なる発生日のデータは処理されない', async () => {
      // ===== Arrange =====
      const differentDate = new Date('2024-04-15'); // 異なる発生日

      const { generateInvestmentAndReturnPdfUsecase } = await import('./generate_investment_and_return_pdf_usecase');

      // ===== Act =====
      const result = await generateInvestmentAndReturnPdfUsecase(differentDate);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const data = result.value;

        // 異なる発生日のデータは存在しないため、空の配列が返される
        expect(Array.isArray(data)).toBe(true);
        expect(data.length).toBe(0);

        // S3にファイルがアップロードされていないことを確認
        const uploadedObjects = await listS3Objects('statements/investment-and-return-');
        expect(uploadedObjects).toHaveLength(0);

        console.log('✅ 異なる発生日の処理成功');
      }
    }, 30000);
  });

  describe('generateInvestmentAndReturnPdfUsecase_fileDownload', () => {
    it('正常に実行できる', async () => {
      // 2024-10-10に発生した請求・送金を作成
      const memberClaimAndPayResult = await createMemberClaimAndPay(member1.memberId, new Date('2024-10-10'), 2671+1376+3300, 20391);
      let memberClaimAndPayId = 1;
      if (memberClaimAndPayResult.isOk()) {
        const memberClaimAndPay = memberClaimAndPayResult.value;
        memberClaimAndPayId = memberClaimAndPay.memberClaimAndPayId;
      }

      // 2024-10-10に発生したInvestmentAndReturnを作成
      const investmentAndReturnResult = await createInvestmentAndReturn(horse1.horseId, member1.memberId, new Date('2024-10-10'), 1, false, 1, 29261, 5940, 0, 121308, 47821, 46009, 2067, 0, 0, 0, 2671, 0);
      if (investmentAndReturnResult.isOk()) {
        const investmentAndReturn = investmentAndReturnResult.value;
        await createInvestmentAndReturnInvestment(investmentAndReturn.investmentAndReturnId, 0, 1326, 5, 1350, 0, 0, 0, 1321, 2671, 0);
        await createInvestmentAndReturnReturn(investmentAndReturn.investmentAndReturnId, ReturnCategory.CLUB_TO_FUND_MONTHLY, 46009, 27478, 0, 0, 0, 0, 0, 27478);
        await createInvestmentAndReturnReturn(investmentAndReturn.investmentAndReturnId, ReturnCategory.FUND_TO_MEMBER_MONTHLY, 46009, 27478, 0, 0, 0, 0, 0, 27478);
      }

      const investmentAndReturnResult2 = await createInvestmentAndReturn(horse2.horseId, member1.memberId, new Date('2024-10-10'), 85, true, 1, 113806, 12690, 0, 212603, 1, 211226, 5285, 999, 8242, 1707, 1376, 20391);
      if (investmentAndReturnResult2.isOk()) {
        const investmentAndReturn2 = investmentAndReturnResult2.value;
        await createInvestmentAndReturnInvestment(investmentAndReturn2.investmentAndReturnId, 0, 1376, 0, 0, 0, 0, 0, 1376, 0, 1376);
        await createInvestmentAndReturnReturn(investmentAndReturn2.investmentAndReturnId, ReturnCategory.CLUB_TO_FUND_MONTHLY, 211226, 1376, 9737, 1376, 8361, 1707, 8030, 0);
        await createInvestmentAndReturnReturn(investmentAndReturn2.investmentAndReturnId, ReturnCategory.FUND_TO_MEMBER_MONTHLY, 211226, 1376, 8030, 1376, 6654, 1358, 6672, 0);
        await createInvestmentAndReturnReturn(investmentAndReturn2.investmentAndReturnId, ReturnCategory.CLUB_TO_FUND_YEARLY, null, 0, 6805, 0, 6805, 1389, 6805, 0);
        await createInvestmentAndReturnReturn(investmentAndReturn2.investmentAndReturnId, ReturnCategory.FUND_TO_MEMBER_YEARLY_ORGANIZER, null, 0, 6805, 0, 6805, 1389, 5416, 0);
        await createInvestmentAndReturnReturn(investmentAndReturn2.investmentAndReturnId, ReturnCategory.FUND_TO_MEMBER_YEARLY_CLUB, null, 0, 10433, 0, 10433, 2130, 8303, 0);
      }

      const memberClaimResult = await createMemberClaim(member1.memberId, new Date('2024-10-10'), '会費', '会費', 10, 300, 3300);

      // 収入の内訳を作成
      const horseIncomePrizeData = {
        horseId: horse2.horseId,
        incomeYearMonth: 202410,
        occurredYear: 2024,
        occurredMonth: 9,
        occurredDay: 23,
        racePlace: '金沢',
        raceName: '第44回白山大賞典',
        raceResult: '4着',
        organizer: HorseIncomePrizeOrganizer.OTHER,
        mainPrizeAmount: 4000000,
        appearanceFee: 1644000,
        withholdingTax: 399741,
        commissionAmount: 800000,
        clubFeeRate: '3',
        taxRate: '10',
        totalPrizeAmount: 5644000,
        clubFeeAmount: 120000,
        taxAmount: 429454,
        incomeAmount: 3894805,
        note: 'テスト',
        allowances: [
          { name: '賞金', amount: 1000000 },
          { name: '賞金', amount: 1000000 },
        ],
      };
      await createHorseIncomePrize(horseIncomePrizeData);

      const horseIncomeOtherData = {
        horseId: horse1.horseId,
        incomeYearMonth: 202410,
        occurredYear: 2024,
        occurredMonth: 9,
        occurredDay: 23,
        name: HorseIncomeOtherName.GRANT,
        nameOther: '商品売却分配金',
        amount: 1279909,
        salesCommission: 22000,
        otherFeeName: '',
        otherFeeAmount: 0,
        taxRate: '10',
        taxAmount: 114355,
        incomeAmount: 1143554,
        note: 'テスト',
      };
      await createHorseIncomeOther(horseIncomeOtherData);
      
      const result = await generateInvestmentAndReturnPdfUsecase(new Date('2024-10-10'));
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const data = result.value;
        expect(Array.isArray(data)).toBe(true);
        expect(data.length).toBeGreaterThan(0);
        const fileKey = result.value[0].pdfFileKey;
        expect(fileKey).toBeDefined();
        expect(fileKey).toMatch(/^statements\/investment-and-return-\d+-\d{4}\.pdf$/);
        // fileKeyがS3にアップロードされているか確認
        const uploadedObjects = await listS3Objects('statements/investment-and-return-');
        expect(uploadedObjects).toContain(fileKey);

        // DBに保存されているfileKeyと一致するか確認
        const memberClaimAndPay = await vPrisma.client.memberClaimAndPay.findFirst({
          where: {
            memberClaimAndPayId: memberClaimAndPayId,
          },
        });
        expect(memberClaimAndPay?.statementFileKey).toBe(fileKey);
      }
      
    });
  });
});
