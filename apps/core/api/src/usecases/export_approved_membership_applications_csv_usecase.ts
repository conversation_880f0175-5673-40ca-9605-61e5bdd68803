import { ResultAsync } from 'neverthrow';
import { MembershipApplicationWithMember, listApprovedMembershipApplicationsWithMembers, markMembershipApplicationsAsPrinted } from '@core-api/repositories/membership_application_repository';
import { getPresignedDownloadUrl, uploadToS3 } from '@core-api/utils/s3';

function toCsvValue(value: unknown): string {
  if (value === null || value === undefined) return '';
  const str = String(value);
  if (str.includes('"') || str.includes(',') || str.includes('\n')) {
    return '"' + str.replace(/"/g, '""') + '"';
  }
  return str;
}

function buildCsv(applicationsWithMembers: MembershipApplicationWithMember[]): Buffer {
  const headers = [
    'membership_application_id',
    'member_id',
    'member_number',
    'applicant_type',
    'corporate_name',
    'name',
    'postal_code',
    'prefecture',
    'address',
    'apartment',
    'phone_number',
    'email',
    'temporary_password',
  ];

  const lines: string[] = [headers.join(',')];

  for (const application of applicationsWithMembers) {
    const applicantType = application.applicantType;
    const member = application.member;
    
    // 会社名と個人名/代表者名を分ける
    const corporateName = applicantType === 'CORPORATE'
      ? member?.corporate?.corporateName ?? application.corporateName ?? ''
      : '';
    
    const name = applicantType === 'CORPORATE'
      ? member?.corporate?.representativeName ?? application.representativeName ?? ''
      : member
        ? `${member.lastName} ${member.firstName}`
        : `${application.lastName ?? ''} ${application.firstName ?? ''}`;

    // 会員データがある場合は会員データを優先、ない場合は申し込みデータを使用
    const memberId = member?.memberId ?? null;
    const memberNumber = member?.memberNumber ?? null;
    const postalCode = member?.postalCode ?? application.postalCode;
    const prefecture = member?.prefecture ?? application.prefecture;
    const address = member?.address ?? application.address;
    const apartment = member?.apartment ?? application.apartment;
    const phoneNumber = member?.phoneNumber ?? application.phoneNumber;
    const email = member?.user.email ?? application.mailVerification.email;
    const temporaryPassword = member?.temporaryPassword ?? null;

    const row = [
      toCsvValue(application.membershipApplicationId),
      toCsvValue(memberId),
      toCsvValue(memberNumber),
      toCsvValue(applicantType === 'INDIVIDUAL' ? "個人" : applicantType === 'CORPORATE' ? "法人" : "不明"),
      toCsvValue(corporateName),
      toCsvValue(name),
      toCsvValue(postalCode),
      toCsvValue(prefecture),
      toCsvValue(address),
      toCsvValue(apartment),
      toCsvValue(phoneNumber),
      toCsvValue(email),
      toCsvValue(temporaryPassword),
    ];

    lines.push(row.join(','));
  }

  // BOM付きUTF-8で出力（Excelが自動認識しやすくなる）
  const bom = Buffer.from([0xEF, 0xBB, 0xBF]);
  const csvContent = Buffer.from(lines.join('\n'), 'utf-8');
  return Buffer.concat([bom, csvContent]);
}

export const exportApprovedMembershipApplicationsCsvUsecase = () => {
  return ResultAsync.fromPromise(
    (async () => {
      // 1) 承認済み申し込みと会員データをジョインして取得（送付済みを除く）
      const applicationsWithMembers = await listApprovedMembershipApplicationsWithMembers({ includePrinted: false }).unwrapOr([]);

      // 2) CSVを生成
      const csvBuffer = buildCsv(applicationsWithMembers);

      // 3) S3にアップロードし、ダウンロードURLを作成
      const timestamp = new Date();
      const fileKey = `exports/membership-applications/approved-${timestamp.getFullYear()}${String(timestamp.getMonth() + 1).padStart(2, '0')}${String(timestamp.getDate()).padStart(2, '0')}${String(timestamp.getHours()).padStart(2, '0')}${String(timestamp.getMinutes()).padStart(2, '0')}${String(timestamp.getSeconds()).padStart(2, '0')}.csv`;
      await uploadToS3(fileKey, csvBuffer, 'text/csv');
      const downloadUrl = await getPresignedDownloadUrl(fileKey, 60 * 60);

      // 4) 副作用: CSVに含めた申込を送付済みに更新
      const ids = applicationsWithMembers.map((a) => a.membershipApplicationId);
      if (ids.length > 0) {
        await markMembershipApplicationsAsPrinted(ids).unwrapOr(0);
      }

      return {
        fileKey,
        downloadUrl,
        total: applicationsWithMembers.length,
      };
    })(),
    (e) => (e instanceof Error ? e : new Error('Failed to export approved applications CSV'))
  );
};


