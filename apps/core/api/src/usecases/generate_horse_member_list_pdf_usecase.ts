import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { ResultAsync } from 'neverthrow';
import PDFDocument from 'pdfkit-table';
import { listHorsesWithInvestors, HorseInvestor } from '@core-api/repositories/horse_repository';
import { uploadToS3, getPresignedDownloadUrl } from '@core-api/utils/s3';

// ESモジュールで__dirnameを取得
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 馬と会員の情報を含むデータ型
 */
interface HorseMemberData {
  horseId: number;
  horseName: string;
  recruitmentName: string;
  recruitmentYear: number;
  recruitmentNo: number;
  birthYear: number;
  sharesTotal: number;
  amountTotal: number;
  members: {
    memberId: number;
    memberNumber: number;
    memberName: string;
    sharesNumber: number;
    investmentAmount: number;
    contractedAt: Date | null;
  }[];
}

/**
 * PDF生成結果の型
 */
interface GeneratePdfResult {
  pdfFileKey: string;
  downloadUrl: string;
  totalHorses: number;
  totalMembers: number;
  generatedAt: bigint;
}

/**
 * 全馬の会員データを取得
 */
const fetchAllHorseMemberData = async (): Promise<HorseMemberData[]> => {
  // 全馬とその出資者情報を一括取得
  const horsesWithInvestorsResult = await listHorsesWithInvestors();
  if (horsesWithInvestorsResult.isErr()) {
    throw new Error('Failed to fetch horses with investors');
  }

  const { horsesWithInvestors } = horsesWithInvestorsResult.value;
  const horseMemberDataList: HorseMemberData[] = [];

  // マップから馬と出資者情報を取得
  for (const [, horseData] of horsesWithInvestors) {
    const members = horseData.investors.map((investor: HorseInvestor) => ({
      memberId: investor.memberId,
      memberNumber: investor.memberNumber,
      memberName: investor.memberName,
      sharesNumber: investor.sharesNumber,
      investmentAmount: investor.investmentAmount,
      contractedAt: new Date(), // 新しいリポジトリでは契約日時は含まれていないため、現在時刻を設定
    }));

    horseMemberDataList.push({
      horseId: horseData.horse.horseId,
      horseName: horseData.horse.horseName || '',
      recruitmentName: horseData.horse.recruitmentName,
      recruitmentYear: horseData.horse.recruitmentYear,
      recruitmentNo: horseData.horse.recruitmentNo,
      birthYear: horseData.horse.birthYear,
      sharesTotal: horseData.horse.sharesTotal,
      amountTotal: horseData.horse.amountTotal,
      members,
    });
  }

  return horseMemberDataList;
};

/**
 * PDFを生成してBufferとして返す（表形式）
 */
const generatePdfBuffer = async (data: HorseMemberData[]): Promise<Buffer> => {
  return new Promise(async (resolve, reject) => {
    const doc = new PDFDocument({
      margin: 30,
      size: 'A4',
      bufferPages: true, // ページバッファリングを有効化
      autoFirstPage: true,
    });
    const buffers: Buffer[] = [];

    doc.on('data', buffers.push.bind(buffers));
    doc.on('end', () => {
      const pdfBuffer = Buffer.concat(buffers);
      resolve(pdfBuffer);
    });
    doc.on('error', reject);

    // 日本語フォントを登録
    try {
      // ローカルフォントファイルを使用
      const fontPath = path.join(__dirname, '../assets/NotoSansJP-VariableFont_wght.ttf');

      if (fs.existsSync(fontPath)) {
        doc.registerFont('NotoSansJP', fontPath);
        doc.font('NotoSansJP');
        console.log('Japanese font loaded successfully from:', fontPath);
      } else {
        console.warn('Japanese font not found at:', fontPath);
        console.warn('Using default font. Some characters may not display correctly.');
      }
    } catch (error) {
      console.warn('Failed to load Japanese font, using default font:', error);
    }

    try {
      // タイトルヘッダー
      doc.fontSize(20).font('NotoSansJP').text('クラブ所有馬別会員一覧', { align: 'center' });

      doc.moveDown(0.5);
      doc
        .fontSize(10)
        .fillColor('#666666')
        .text(`生成日時: ${new Date().toLocaleString('ja-JP')}`, { align: 'center' });

      doc.moveDown(2);
      doc.fillColor('#000000'); // 色を黒に戻す

      // サマリー情報を表示
      const summaryTable = {
        headers: [
          { label: '項目', property: 'item', width: 200 },
          { label: '件数', property: 'count', width: 150, align: 'right' },
        ],
        datas: [
          { item: '対象馬数', count: `${data.length}頭` },
          { item: '総会員数', count: `${data.reduce((sum, horse) => sum + horse.members.length, 0)}名` },
        ],
      };

      await doc.table(summaryTable, {
        prepareHeader: () => doc.font('NotoSansJP').fontSize(11).fillColor('#333333'),
        prepareRow: () => doc.font('NotoSansJP').fontSize(10).fillColor('#000000'),
        padding: [6, 8],
        columnSpacing: 15,
      });

      doc.moveDown(2);

      // 馬ごとの詳細情報
      for (let i = 0; i < data.length; i++) {
        const horse = data[i];

        // ページの最大高さを計算（マージンを考慮）
        const pageMaxHeight = doc.page.height - doc.page.margins.bottom;

        // 馬の基本情報表の推定高さ
        const horseInfoEstimatedHeight = 200; // 馬名 + タイトル + 5行のデータ

        // 新しい馬の情報を始める前に改ページチェック
        if (i > 0) {
          // 現在のY座標と必要な高さを確認
          if (doc.y + horseInfoEstimatedHeight > pageMaxHeight) {
            doc.addPage();
          } else {
            doc.moveDown(3);
          }
        }

        // 馬名
        doc
          .fontSize(16)
          .font('NotoSansJP')
          .fillColor('#1a1a1a')
          .text(`${horse.horseName || horse.recruitmentName}`, { underline: false });

        doc.moveDown(1);
        doc.fillColor('#000000'); // 色を黒に戻す

        // 馬の基本情報表
        const horseInfoTable = {
          headers: [
            { label: '項目', property: 'item', width: 130 },
            { label: '内容', property: 'value', width: 220 },
          ],
          datas: [
            { item: '募集年度', value: `${horse.recruitmentYear}年` },
            { item: '募集番号', value: `${horse.recruitmentNo}` },
            { item: '生年', value: `${horse.birthYear}年` },
            { item: '総口数', value: `${horse.sharesTotal}口` },
            { item: '総出資金額', value: `¥${horse.amountTotal.toLocaleString()}` },
          ],
        };

        // 馬の基本情報表を描画
        await doc.table(horseInfoTable, {
          prepareHeader: () => doc.font('NotoSansJP').fontSize(10).fillColor('#555555'),
          prepareRow: () => doc.font('NotoSansJP').fontSize(10).fillColor('#000000'),
          padding: [6, 8],
          columnSpacing: 12,
        });

        // 出資会員一覧表
        if (horse.members.length > 0) {
          doc.moveDown(1.5); // 表間の間隔

          // 小見出し
          doc.fontSize(12).font('NotoSansJP').fillColor('#333333').text(`出資会員一覧 (${horse.members.length}名)`, { underline: false });

          doc.moveDown(1);
          doc.fillColor('#000000');

          // 会員が多い場合は分割して表示
          const maxRowsPerTable = 25; // 1テーブルあたりの最大行数を増やす
          const memberChunks = [];
          for (let j = 0; j < horse.members.length; j += maxRowsPerTable) {
            memberChunks.push(horse.members.slice(j, j + maxRowsPerTable));
          }

          for (let chunkIndex = 0; chunkIndex < memberChunks.length; chunkIndex++) {
            const chunk = memberChunks[chunkIndex];

            // 各チャンクの推定高さ（ヘッダー + データ行）
            const chunkEstimatedHeight = 60 + chunk.length * 20;

            // 現在のY座標を確認し、必要に応じて改ページ
            if (doc.y + chunkEstimatedHeight > pageMaxHeight) {
              doc.addPage();
              // 改ページ後に馬名を再表示（続き）
              doc
                .fontSize(14)
                .font('NotoSansJP')
                .fillColor('#666666')
                .text(`${horse.horseName || horse.recruitmentName} - 続き`, { align: 'left' });
              doc.moveDown(1);
              doc.fillColor('#000000');
            } else if (chunkIndex > 0) {
              // 前のチャンクとの間に少し間隔を追加
              doc.moveDown(1);
            }

            const membersTable = {
              headers: [
                { label: 'No.', property: 'index', width: 40 },
                { label: '会員番号', property: 'memberNumber', width: 80 },
                { label: '氏名', property: 'memberName', width: 140 },
                { label: '口数', property: 'sharesNumber', width: 60, align: 'right' },
                { label: '出資金額', property: 'investmentAmount', width: 100, align: 'right' },
              ],
              datas: chunk.map((member, idx) => ({
                index: (chunkIndex * maxRowsPerTable + idx + 1).toString(),
                memberNumber: member.memberNumber.toString(),
                memberName: member.memberName,
                sharesNumber: `${member.sharesNumber}`,
                investmentAmount: `¥${member.investmentAmount.toLocaleString()}`,
              })),
            };

            // 会員一覧表を描画
            await doc.table(membersTable, {
              prepareHeader: () => doc.font('NotoSansJP').fontSize(9).fillColor('#555555'),
              prepareRow: () => doc.font('NotoSansJP').fontSize(9).fillColor('#000000'),
              padding: [4, 6],
              columnSpacing: 8,
            });
          }
        } else {
          // 出資会員がいない場合
          doc.moveDown(1.5);

          // 改ページチェック
          const noMembersEstimatedHeight = 80;
          if (doc.y + noMembersEstimatedHeight > pageMaxHeight) {
            doc.addPage();
          }

          doc.fontSize(11).font('NotoSansJP').fillColor('#999999').text('出資会員一覧', { underline: false });

          doc.moveDown(0.5);

          doc.fontSize(10).fillColor('#666666').text('現在、出資会員はいません', { indent: 20 });

          doc.fillColor('#000000');
        }
      }
    } catch (error) {
      console.error('Error generating PDF tables:', error);
      reject(error);
      return;
    }

    doc.end();
  });
};

/**
 * S3にPDFをアップロードしてダウンロードURLを生成
 */
const uploadPdfToS3 = async (pdfBuffer: Buffer): Promise<{ fileKey: string; downloadUrl: string }> => {
  const timestamp = Date.now();
  const fileKey = `reports/horse-member-list-${timestamp}.pdf`;

  // S3にアップロード
  await uploadToS3(fileKey, pdfBuffer, 'application/pdf');

  // ダウンロードURL生成（1時間有効）
  const downloadUrl = await getPresignedDownloadUrl(fileKey, 3600);

  return { fileKey, downloadUrl };
};

/**
 * クラブ所有馬別会員一覧PDF生成usecase
 */
export const generateHorseMemberListPdfUsecase = (): ResultAsync<GeneratePdfResult, Error> => {
  return ResultAsync.fromPromise(
    (async () => {
      // 1. データ取得
      const horseMemberData = await fetchAllHorseMemberData();

      // 2. PDF生成
      const pdfBuffer = await generatePdfBuffer(horseMemberData);

      // 3. S3アップロード
      const { fileKey, downloadUrl } = await uploadPdfToS3(pdfBuffer);

      // 4. 結果返却
      const totalMembers = horseMemberData.reduce((sum, horse) => sum + horse.members.length, 0);

      return {
        pdfFileKey: fileKey,
        downloadUrl,
        totalHorses: horseMemberData.length,
        totalMembers,
        generatedAt: BigInt(Date.now()),
      };
    })(),
    (error) => {
      console.error('PDF generation failed:', error);
      return error instanceof Error ? error : new Error('Unknown error occurred during PDF generation');
    }
  );
};
