import { InvestmentContractStatus } from '@hami/prisma';
import { getContractTargetApplications, getExistingContracts } from '@core-api/repositories/investment_application_repository';
import { client } from '@core-api/utils/prisma';
import { MEMBERS_FIRST_CONTRACT_AMOUNT, TAX_RATE } from '../utils/const';
import { getInvestmentSealDate, getInstallmentCount, getInstallmentDatesAndInvestmentAmounts } from '../utils/investment_utils';

/**
 * 出資申込契約締結実施
 * contractedフラグをtrueに更新し、InvestmentContractレコードを作成
 * 指定された馬の契約口数が募集口数を超えないことを検証
 * 各申込を個別のトランザクションで処理
 */
export async function completeInvestmentContractsUsecase(horseId: number, investmentApplicationIds: number[], contractDate: Date) {
  const contracts: Array<{
    investmentApplicationId: number;
    memberNumber: number;
    memberName: string;
    contractedShares: number;
  }> = [];

  // 馬の情報を取得
  const horse = await client.horse.findUnique({
    where: { horseId },
  });

  if (!horse) {
    throw new Error('Horse not found');
  }

  // 既存の契約口数を取得
  const existingContracts = await getExistingContracts(horseId);

  const totalContractedShares = existingContracts.reduce((sum, contract) => sum + (contract.allocatedNumber || 0), 0);

  // 契約対象の申込を取得
  const applications = await getContractTargetApplications(horseId, investmentApplicationIds);

  // 申込IDの件数チェック（不正なIDや馬ID不一致が含まれていればエラー）
  if (applications.length !== investmentApplicationIds.length) {
    throw new Error('Some applications are not eligible for contract completion or do not belong to the specified horse');
  }

  // 契約口数の合計を計算
  const newContractShares = applications.reduce((sum, app) => sum + (app.allocatedNumber || 0), 0);

  // 募集口数を超えていないかチェック（forループ前に必ず行う）
  if (totalContractedShares + newContractShares > horse.sharesTotal) {
    throw new Error(`Contract shares limit exceeded for horse ${horse.horseName} (${horse.recruitmentName})`);
  }

  let currentContractedShares = totalContractedShares;
  for (const application of applications) {
    // 追加後の契約口数が超過しないかチェック
    const addShares = application.allocatedNumber || 0;
    if (currentContractedShares + addShares > horse.sharesTotal) {
      throw new Error(`Contract shares limit exceeded for horse ${horse.horseName} (${horse.recruitmentName})`);
    }
    try {
      await client.$transaction(async (tx) => {
        // 申込を更新
        const updatedApplication = await tx.investmentApplication.update({
          where: {
            investmentApplicationId: application.investmentApplicationId,
          },
          data: {
            contracted: true,
          },
          include: {
            member: {
              select: {
                memberId: true,
                memberNumber: true,
                lastName: true,
                firstName: true,
              },
            },
          },
        });

        // investmentContractのcreateの前に、memberIdに関するinvestmentContractが存在するかどうかを確認
        const existingContract = await tx.investmentContract.findFirst({
          where: {
            memberId: updatedApplication.memberId,
            horseId: horse.horseId,
          },
        });
        const membersFirstContract = existingContract ? false : true;

        // InvestmentContractレコードを作成
        // transactionAmountとmonthlyDepreciationは後で更新する
        const investmentAmount = Math.floor((horse.amountTotal / horse.sharesTotal) * (updatedApplication.allocatedNumber || 0));
        const discount = 0; // 現在値引きはない
        const investmentAmountBeforeTax = Math.ceil(investmentAmount / (1 + (TAX_RATE / 100)));
        await tx.investmentContract.create({
          data: {
            horseId: horse.horseId,
            memberId: updatedApplication.memberId,
            sharesNumber: updatedApplication.allocatedNumber || 0,
            investmentAmount: investmentAmount,
            contractStatus: InvestmentContractStatus.COMPLETED,
            contractedAt: contractDate,
            discount: discount,
            taxRate: TAX_RATE,
            investmentAmountBeforeTax: investmentAmountBeforeTax,
          },
        });

        // HorseInvestmentSharesのmemberInvestmentSharesを更新
        await tx.horseInvestmentShares.upsert({
          where: {
            horseId: horse.horseId,
          },
          create: {
            horseId: horse.horseId,
            memberInvestmentShares: updatedApplication.allocatedNumber || 0,
          },
          update: {
            memberInvestmentShares: {
              increment: updatedApplication.allocatedNumber || 0,
            },
          },
        });

        // 最初の出資の締め日を計算
        const firstInvestmentSealDate = getInvestmentSealDate(contractDate);

        // MemberRacehorseInvestmentレコードを作成
        // 分割回数を設定
        let installmentCount = getInstallmentCount(updatedApplication.installmentPayment, horse.birthYear, firstInvestmentSealDate);
        const installmentDatesAndInvestmentAmounts = getInstallmentDatesAndInvestmentAmounts(firstInvestmentSealDate, installmentCount, investmentAmount, discount, investmentAmount - discount);
        
        for (const installmentDateAndInvestmentAmount of installmentDatesAndInvestmentAmounts) {
          await tx.memberRacehorseInvestment.create({
            data: {
              horseId: horse.horseId,
              memberId: updatedApplication.memberId,
              investmentDate: installmentDateAndInvestmentAmount.investmentDate,
              racehorseInvestmentEquivalent: installmentDateAndInvestmentAmount.racehorseInvestmentEquivalent,
              discountAllocation: installmentDateAndInvestmentAmount.discountAllocation,
              racehorseInvestment: installmentDateAndInvestmentAmount.racehorseInvestment,
            },
          });
        }

        // membersFirstContractがtrueの場合、入会金情報をclaimに追加
        if (membersFirstContract) {
          const amountWithoutTax = MEMBERS_FIRST_CONTRACT_AMOUNT;
          const taxAmount = amountWithoutTax * TAX_RATE / 100;
          await tx.memberClaim.create({
            data: {
              memberId: updatedApplication.memberId,
              occurredDate: firstInvestmentSealDate,
              title: '入会金',
              description: '入会金',
              taxRate: TAX_RATE,
              amount: amountWithoutTax + taxAmount,
              taxAmount: taxAmount,
            },
          });
        }
        

        // 契約情報を追加
        contracts.push({
          investmentApplicationId: updatedApplication.investmentApplicationId,
          memberNumber: updatedApplication.member.memberNumber,
          memberName: `${updatedApplication.member.lastName} ${updatedApplication.member.firstName}`,
          contractedShares: updatedApplication.allocatedNumber || 0,
        });
      });
      currentContractedShares += addShares;
    } catch (error) {
      console.error(`Failed to complete contract for application ${application.investmentApplicationId}:`, error);
      // エラーが発生しても処理を継続
    }
  }

  return contracts;
}
