import { complianceApprovalTemplate } from './compliance_approval';

describe('complianceApprovalTemplate', () => {
  const testParams = {
    firstName: '太郎',
    lastName: '山田',
  };

  describe('subject', () => {
    it('正しい件名を返す', () => {
      expect(complianceApprovalTemplate.subject).toBe('BHC | 【重要】入会審査結果のご連絡');
    });
  });

  describe('getText', () => {
    it('正しいテキストメールを生成する', () => {
      const text = complianceApprovalTemplate.getText(testParams);

      expect(text).toContain('山田 太郎 様');
      expect(text).toContain('この度は「Blooming Horse Club」へのお申し込み、誠にありがとうございました。');
      expect(text).toContain('厳正なる審査の結果、入会を承認いたしました');
      expect(text).toContain('マイページにログインするためのIDとパスワードを記載した書類を転送不要郵便にて郵送いたします');
      expect(text).toContain('https://bloominghorseclub.co.jp/signin');
      expect(text).toContain('Blooming Horse Clubの一員として、一緒に競馬ライフを楽しみましょう！');
      expect(text).toContain('書類が届くまで、今しばらくお待ちください。');
    });

    it('名前が空の場合でも正常に動作する', () => {
      const emptyParams = { firstName: '', lastName: '' };
      const text = complianceApprovalTemplate.getText(emptyParams);

      expect(text).toContain(' 様'); // 空の名前でも「様」は含まれる
      expect(text).toContain('厳正なる審査の結果、入会を承認いたしました');
    });
  });

  describe('getHtml', () => {
    it('正しいHTMLメールを生成する', () => {
      const html = complianceApprovalTemplate.getHtml(testParams);

      expect(html).toContain('山田 太郎 様');
      expect(html).toContain('BHC | 【重要】入会審査結果のご連絡');
      expect(html).toContain('この度は「Blooming Horse Club」へのお申し込み、誠にありがとうございました。');
      expect(html).toContain('厳正なる審査の結果、入会を承認いたしました');
      expect(html).toContain('https://bloominghorseclub.co.jp/signin');
      expect(html).toContain('Blooming Horse Clubの一員として、一緒に競馬ライフを楽しみましょう！');
    });

    it('HTMLエスケープが適用される', () => {
      const maliciousParams = {
        firstName: '<script>alert("xss")</script>',
        lastName: '&lt;test&gt;',
      };

      const html = complianceApprovalTemplate.getHtml(maliciousParams);

      expect(html).not.toContain('<script>');
      expect(html).toContain('&lt;script&gt;');
      expect(html).toContain('&amp;lt;test&amp;gt;');
    });

    it('HTMLの構造が正しい', () => {
      const html = complianceApprovalTemplate.getHtml(testParams);

      expect(html).toContain('<div style="font-family: Arial, sans-serif');
      expect(html).toContain('<h2 style="color: #333;">');
      expect(html).toContain('<a href="https://bloominghorseclub.co.jp/signin"');
    });
  });
});
