import { MailTemplate } from './index';
import { escapeHtml } from '../utils/html_escape';

export interface MembershipRejectionParams {
  firstName?: string;
  lastName?: string;
}

/**
 * 入会拒否通知用のテンプレート
 */
export const membershipRejectionTemplate: MailTemplate<MembershipRejectionParams> = {
  subject: 'BHC | 【重要】入会審査結果のご連絡',

  getText: ({ firstName = '', lastName = '' }: MembershipRejectionParams) => {
    const displayName = [lastName, firstName].filter(Boolean).join(' ');
    return `${displayName ? `${displayName} 様\n\n` : ''}この度は「Blooming Horse Club」へのお申し込み、誠にありがとうございました。\n\n厳正なる審査の結果、誠に恐縮ではございますが、この度のご入会につきましては、ご期待に沿うことができないという判断に至りました。\n\nご希望に沿えず大変申し訳ございませんが、何卒ご理解いただけますようお願い申し上げます。\nなお、審査基準に基づく判断のため、個別の拒否理由についてお答えいたしかねます。\n何卒ご了承くださいますようお願い申し上げます。\n\nこの度はお申し込みいただき、誠にありがとうございました。`;
  },

  getHtml: ({ firstName = '', lastName = '' }: MembershipRejectionParams) => {
    const displayName = [lastName, firstName].filter(Boolean).join(' ');
    const escapedName = escapeHtml(displayName);
    return `
      ${displayName ? `<p>${escapedName} 様</p>` : ''}
      <p>この度は「Blooming Horse Club」へのお申し込み、誠にありがとうございました。</p>
      <p>厳正なる審査の結果、誠に恐縮ではございますが、この度のご入会につきましては、ご期待に沿うことができないという判断に至りました。</p>
      <p>ご希望に沿えず大変申し訳ございませんが、何卒ご理解いただけますようお願い申し上げます。<br>
      なお、審査基準に基づく判断のため、個別の拒否理由についてお答えいたしかねます。<br>
      何卒ご了承くださいますようお願い申し上げます。</p>
      <p>この度はお申し込みいただき、誠にありがとうございました。</p>
    `;
  },
};
