import { MailTemplate } from './index';
import { escapeHtml } from '../utils/html_escape';

export interface AdminAccountCreationParams {
  name: string;
  email: string;
  tempPassword: string;
}

export const adminAccountCreationTemplate: MailTemplate<AdminAccountCreationParams> = {
  subject: '管理者アカウント作成のお知らせ',
  getText: ({ name, email, tempPassword }) =>
    `${name} 様\n\n管理者アカウントを作成しました。\n\nメールアドレス: ${email}\n仮パスワード: ${tempPassword}\n\n初回ログイン後に必ずパスワードを変更してください。`,
  getHtml: ({ name, email, tempPassword }) => {
    const n = escapeHtml(name);
    const e = escapeHtml(email);
    const p = escapeHtml(tempPassword);
    return `<p>${n} 様</p><p>管理者アカウントを作成しました。</p><p><strong>メールアドレス:</strong> ${e}</p><p><strong>仮パスワード:</strong> ${p}</p><p>初回ログイン後に必ずパスワードを変更してください。</p>`;
  },
};

