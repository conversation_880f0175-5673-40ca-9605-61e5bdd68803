import { MailTemplate } from './index';
import { escapeHtml } from '../utils/html_escape';

/**
 * コンプライアンス室審査承認通知用のパラメータ型
 */
export interface ComplianceApprovalParams {
  firstName: string;
  lastName: string;
}

/**
 * コンプライアンス室審査承認通知用のテンプレート
 */
export const complianceApprovalTemplate: MailTemplate<ComplianceApprovalParams> = {
  subject: 'BHC | 【重要】入会審査結果のご連絡',

  getText: (params: ComplianceApprovalParams) => {
    const { firstName, lastName } = params;
    const memberName = `${lastName} ${firstName}`;
    
    return `${memberName} 様

この度は「Blooming Horse Club」へのお申し込み、誠にありがとうございました。

厳正なる審査の結果、入会を承認いたしましたので、お知らせいたします。
この後、運営より、マイページにログインするためのIDとパスワードを記載した書類を転送不要郵便にて郵送いたします。

書類がお手元に届きましたら、記載のIDとパスワードを使用して、以下のURLよりログインしてください。

＜ログインページ＞
[https://bloominghorseclub.co.jp/signin]

Blooming Horse Clubの一員として、一緒に競馬ライフを楽しみましょう！
スタッフ一同、心を込めてクラブを運営してまいりますので、どうぞよろしくお願いいたします。

書類が届くまで、今しばらくお待ちください。`;
  },

  getHtml: (params: ComplianceApprovalParams) => {
    const { firstName, lastName } = params;
    
    // HTMLエスケープを適用
    const escapedFirstName = escapeHtml(firstName);
    const escapedLastName = escapeHtml(lastName);
    const memberName = `${escapedLastName} ${escapedFirstName}`;

    return `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #333;">BHC | 【重要】入会審査結果のご連絡</h2>

  <p>${memberName} 様</p>

  <p>この度は「Blooming Horse Club」へのお申し込み、誠にありがとうございました。</p>

  <p>厳正なる審査の結果、入会を承認いたしましたので、お知らせいたします。<br>
  この後、運営より、マイページにログインするためのIDとパスワードを記載した書類を転送不要郵便にて郵送いたします。</p>

  <p>書類がお手元に届きましたら、記載のIDとパスワードを使用して、以下のURLよりログインしてください。</p>

  <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3 style="margin-top: 0; color: #333;">＜ログインページ＞</h3>
    <p><a href="https://bloominghorseclub.co.jp/signin" style="color: #007bff; text-decoration: none;">https://bloominghorseclub.co.jp/signin</a></p>
  </div>

  <p>Blooming Horse Clubの一員として、一緒に競馬ライフを楽しみましょう！<br>
  スタッフ一同、心を込めてクラブを運営してまいりますので、どうぞよろしくお願いいたします。</p>

  <p>書類が届くまで、今しばらくお待ちください。</p>
</div>
`;
  },
};
