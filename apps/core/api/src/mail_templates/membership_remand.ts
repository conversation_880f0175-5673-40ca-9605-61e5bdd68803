import { MailTemplate } from './index';
import { escapeHtml } from '../utils/html_escape';

/**
 * 入会申込差し戻し通知用のパラメータ型
 */
export interface MembershipRemandParams {
  /** 申込者名（名） */
  firstName?: string;
  /** 申込者名（姓） */
  lastName?: string;
  /** 修正が必要な項目（既存互換: remandReason を流用可） */
  fieldsToFix?: string;
  /** 具体的な不備の内容 */
  deficiencyDetails?: string;
  /** 既存互換用: 旧テンプレで使っていた差し戻し理由 */
  remandReason?: string;
  /** 既存互換用: 旧テンプレで使っていた再提出リンク（今回の本文では固定URLを使用） */
  resubmitLink?: string;
}

/**
 * 入会申込差し戻し通知用のテンプレート
 */
export const membershipRemandTemplate: MailTemplate<MembershipRemandParams> = {
  subject: 'BHC | 【重要】入会申し込み情報の確認・修正のお願い',

  getText: (params: MembershipRemandParams) => {
    const displayName = [params.lastName ?? '', params.firstName ?? ''].filter(Boolean).join(' ');
    const fieldsToFix = (params.fieldsToFix ?? params.remandReason ?? '').trim();
    const deficiencyDetails = (params.deficiencyDetails ?? '').trim();

    const header = displayName ? `${displayName} 様\n\n` : '';
    const link = params.resubmitLink ?? 'https://bloominghorseclub.co.jp/update-entry';

    return (
      header +
      `この度は「Blooming Horse Club」へのお申し込み、誠にありがとうございます。\n\n` +
      `ご入力いただいた会員情報に、以下の不備がございましたため、内容のご確認と修正をお願いしたく、ご連絡いたしました。\n\n` +
      `＜修正が必要な項目＞\n\n` +
      `${fieldsToFix}\n\n` +
      `${deficiencyDetails ? `${deficiencyDetails}\n\n` : ''}` +
      `お手数をおかけいたしますが、以下のURLにアクセスしていただき、ご登録内容をご確認・修正いただけますようお願い申し上げます。\n\n` +
      `＜入会申し込み情報の更新ページ＞\n` +
      `[${link}]\n\n` +
      `修正完了後、再度審査を進めさせていただきます。\n` +
      `ご不明な点がございましたら、お気軽にお問い合わせください。\n\n` +
      `何卒よろしくお願い申し上げます。`
    );
  },

  getHtml: (params: MembershipRemandParams) => {
    const displayName = [params.lastName ?? '', params.firstName ?? ''].filter(Boolean).join(' ');
    const escapedName = escapeHtml(displayName);
    const fieldsToFix = escapeHtml((params.fieldsToFix ?? params.remandReason ?? '').trim());
    const deficiencyDetails = escapeHtml((params.deficiencyDetails ?? '').trim());
    const link = params.resubmitLink ?? 'https://bloominghorseclub.co.jp/update-entry';

    return `
      ${displayName ? `<p>${escapedName} 様</p>` : ''}
      <p>この度は「Blooming Horse Club」へのお申し込み、誠にありがとうございます。</p>
      <p>ご入力いただいた会員情報に、以下の不備がございましたため、内容のご確認と修正をお願いしたく、ご連絡いたしました。</p>

      <p><strong>＜修正が必要な項目＞</strong></p>
      <p>${fieldsToFix}</p>
      ${deficiencyDetails ? `<p>${deficiencyDetails}</p>` : ''}

      <p>お手数をおかけいたしますが、以下のURLにアクセスしていただき、ご登録内容をご確認・修正いただけますようお願い申し上げます。</p>
      <p><strong>＜入会申し込み情報の更新ページ＞</strong><br>
      <a href="${link}">${link}</a></p>

      <p>修正完了後、再度審査を進めさせていただきます。<br>
      ご不明な点がございましたら、お気軽にお問い合わせください。</p>

      <p>何卒よろしくお願い申し上げます。</p>
    `;
  },
};
