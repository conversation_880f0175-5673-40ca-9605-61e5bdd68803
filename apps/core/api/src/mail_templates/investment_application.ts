import { MailTemplate } from './index';
import { escapeHtml } from '../utils/html_escape';

/**
 * 出資申込受付通知用のパラメータ型
 */
export interface InvestmentApplicationParams {
  firstName: string;
  lastName: string;
  horseName: string;
  requestedNumber: number;
  rejectPartialAllocation: boolean;
  investmentApplicationId: number;
  appliedAt: string; // YYYY-MM-DD HH:mm:ss 形式
}

/**
 * 出資申込受付通知用のテンプレート
 */
export const investmentApplicationTemplate: MailTemplate<InvestmentApplicationParams> = {
  subject: '出資申込受付のお知らせ',

  getText: (params: InvestmentApplicationParams) => {
    const { firstName, lastName, horseName, requestedNumber, rejectPartialAllocation, investmentApplicationId, appliedAt } = params;
    
    return `${firstName} ${lastName} 様

この度は、出資申込をいただき、誠にありがとうございます。

以下の内容で出資申込を受け付けいたしました。

■申込内容
申込番号: ${investmentApplicationId}
申込日時: ${appliedAt}
対象馬: ${horseName}
希望口数: ${requestedNumber}口
希望口数に満たない場合の取扱い: ${rejectPartialAllocation ? '出資を希望しない' : '部分出資を希望する'}

■今後の流れ
1. 申込内容の確認・審査を行います
2. 出資の可否および出資受入口数を決定いたします
3. 結果については、後日メールにてお知らせいたします

※抽選となる場合がございますので、希望口数での出資をお約束するものではございません。
※出資受入口数は申込内容や抽選結果により決定されます。

ご不明な点がございましたら、お気軽にお問い合わせください。

今後ともよろしくお願いいたします。

運営チーム`;
  },

  getHtml: (params: InvestmentApplicationParams) => {
    const { firstName, lastName, horseName, requestedNumber, rejectPartialAllocation, investmentApplicationId, appliedAt } = params;

    // HTMLエスケープを適用
    const escapedFirstName = escapeHtml(firstName);
    const escapedLastName = escapeHtml(lastName);
    const escapedHorseName = escapeHtml(horseName);
    const escapedAppliedAt = escapeHtml(appliedAt);

    return `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; line-height: 1.6;">
  <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">出資申込受付のお知らせ</h2>

  <p>${escapedFirstName} ${escapedLastName} 様</p>

  <p>この度は、出資申込をいただき、誠にありがとうございます。</p>

  <p>以下の内容で出資申込を受け付けいたしました。</p>

  <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff;">
    <h3 style="margin-top: 0; color: #333; font-size: 18px;">■申込内容</h3>
    <table style="width: 100%; border-collapse: collapse;">
      <tr>
        <td style="padding: 8px 0; font-weight: bold; color: #555; width: 40%;">申込番号:</td>
        <td style="padding: 8px 0; color: #333;">${investmentApplicationId}</td>
      </tr>
      <tr>
        <td style="padding: 8px 0; font-weight: bold; color: #555;">申込日時:</td>
        <td style="padding: 8px 0; color: #333;">${escapedAppliedAt}</td>
      </tr>
      <tr>
        <td style="padding: 8px 0; font-weight: bold; color: #555;">対象馬:</td>
        <td style="padding: 8px 0; color: #333;">${escapedHorseName}</td>
      </tr>
      <tr>
        <td style="padding: 8px 0; font-weight: bold; color: #555;">希望口数:</td>
        <td style="padding: 8px 0; color: #333;">${requestedNumber}口</td>
      </tr>
      <tr>
        <td style="padding: 8px 0; font-weight: bold; color: #555;">希望口数に満たない場合:</td>
        <td style="padding: 8px 0; color: #333;">${rejectPartialAllocation ? '出資を希望しない' : '部分出資を希望する'}</td>
      </tr>
    </table>
  </div>

  <div style="background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;">
    <h3 style="margin-top: 0; color: #333; font-size: 18px;">■今後の流れ</h3>
    <ol style="margin: 10px 0; padding-left: 20px; color: #555;">
      <li style="margin-bottom: 8px;">申込内容の確認・審査を行います</li>
      <li style="margin-bottom: 8px;">出資の可否および出資受入口数を決定いたします</li>
      <li style="margin-bottom: 8px;">結果については、後日メールにてお知らせいたします</li>
    </ol>
  </div>

  <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
    <p style="margin: 5px 0; color: #856404; font-size: 14px;"><strong>※抽選となる場合がございますので、希望口数での出資をお約束するものではございません。</strong></p>
    <p style="margin: 5px 0; color: #856404; font-size: 14px;"><strong>※出資受入口数は申込内容や抽選結果により決定されます。</strong></p>
  </div>

  <p>ご不明な点がございましたら、お気軽にお問い合わせください。</p>

  <p>今後ともよろしくお願いいたします。</p>

  <p style="margin-top: 30px; color: #666; border-top: 1px solid #eee; padding-top: 20px;">運営チーム</p>
</div>
`;
  },
};
