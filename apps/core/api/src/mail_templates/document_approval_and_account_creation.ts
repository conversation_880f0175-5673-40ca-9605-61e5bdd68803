import { MailTemplate } from './index';
import { escapeHtml } from '../utils/html_escape';

/**
 * 書類承認・アカウント作成通知用のパラメータ型
 */
export interface DocumentApprovalAndAccountCreationParams {
  firstName: string;
  lastName: string;
  email: string;
  tempPassword: string;
  loginUrl: string;
}

/**
 * 書類承認・アカウント作成通知用のテンプレート
 */
export const documentApprovalAndAccountCreationTemplate: MailTemplate<DocumentApprovalAndAccountCreationParams> = {
  subject: '書類審査完了・アカウント作成のお知らせ',

  getText: (params: DocumentApprovalAndAccountCreationParams) => {
    const { firstName, lastName, email, tempPassword, loginUrl } = params;
    return `
${firstName} ${lastName} 様

この度は、ご入会申し込みをいただき、誠にありがとうございます。

提出いただいた本人確認書類の審査が完了し、ご入会が承認されましたので、アカウントを作成いたしました。

以下の情報でログインしていただけます：

■ログイン情報
メールアドレス: ${email}
仮パスワード: ${tempPassword}

■ログインURL
${loginUrl}

※セキュリティのため、初回ログイン後に必ずパスワードを変更してください。

ご不明な点がございましたら、お気軽にお問い合わせください。

今後ともよろしくお願いいたします。

運営チーム
`;
  },

  getHtml: (params: DocumentApprovalAndAccountCreationParams) => {
    const { firstName, lastName, email, tempPassword, loginUrl } = params;

    // HTMLエスケープを適用
    const escapedFirstName = escapeHtml(firstName);
    const escapedLastName = escapeHtml(lastName);
    const escapedEmail = escapeHtml(email);
    const escapedTempPassword = escapeHtml(tempPassword);
    const escapedLoginUrl = escapeHtml(loginUrl);

    return `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #333;">書類審査完了・アカウント作成のお知らせ</h2>

  <p>${escapedFirstName} ${escapedLastName} 様</p>

  <p>この度は、ご入会申し込みをいただき、誠にありがとうございます。</p>

  <p>提出いただいた本人確認書類の審査が完了し、ご入会が承認されましたので、アカウントを作成いたしました。</p>

  <p>以下の情報でログインしていただけます：</p>

  <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3 style="margin-top: 0; color: #333;">■ログイン情報</h3>
    <p><strong>メールアドレス:</strong> ${escapedEmail}</p>
    <p><strong>仮パスワード:</strong> ${escapedTempPassword}</p>
  </div>

  <div style="background-color: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3 style="margin-top: 0; color: #333;">■ログインURL</h3>
    <p><a href="${escapedLoginUrl}" style="color: #1976d2; text-decoration: none;">${escapedLoginUrl}</a></p>
  </div>

  <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
    <p style="margin: 0;"><strong>※セキュリティのため、初回ログイン後に必ずパスワードを変更してください。</strong></p>
  </div>

  <p>ご不明な点がございましたら、お気軽にお問い合わせください。</p>

  <p>今後ともよろしくお願いいたします。</p>

  <p style="margin-top: 30px; color: #666;">運営チーム</p>
</div>
`;
  },
};
