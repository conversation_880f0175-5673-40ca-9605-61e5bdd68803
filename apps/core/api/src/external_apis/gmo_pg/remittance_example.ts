/**
 * GMO 送金サービスAPI クライアントの使用例
 *
 * このファイルは実装例を示すためのものです。
 * 実際のプロダクションコードでは、適切なエラーハンドリングと
 * ログ記録を実装してください。
 *
 * 【重要】送金サービスの推奨フロー:
 * 1. AccountRegistration APIで口座登録（Method=1）
 * 2. DepositRegistration APIで送金指示登録（Method=1）
 * 3. DepositSearch APIで送金状況を確認
 * 4. 必要に応じてDepositRegistration APIで送金取消（Method=2）
 */

import { createGmoRemittanceClient, GmoApiError, GmoNetworkError, GmoValidationError, GmoTimeoutError } from './index';

/**
 * 送金サービスの完全なフロー例
 */
export async function remittanceServiceFlow() {
  const client = createGmoRemittanceClient();

  const bankId = 'bank_' + Date.now();
  const depositId = 'deposit_' + Date.now();

  try {
    // 1. 口座登録
    console.log('口座登録を開始...');
    const accountParams = {
      ShopID: 'test_shop_id', // 実際の値は環境変数から取得
      ShopPass: 'test_shop_pass', // 実際の値は環境変数から取得
      BankID: bankId,
      BankCode: '0009', // 三井住友銀行
      BranchCode: '015', // 本店営業部
      AccountType: '1' as const, // 普通預金
      AccountNumber: '1234567',
      AccountName: 'テスト　タロウ',
      Free: '月締め処理用口座',
    };

    const accountResponse = await client.registerAccount(accountParams);

    if (accountResponse.ErrCode) {
      console.error('❌ 口座登録エラー:', accountResponse.ErrCode, accountResponse.ErrInfo);
      return;
    }

    if (accountResponse.Bank_ID && accountResponse.Method) {
      console.log('✅ 口座登録成功');
      console.log('Bank ID:', accountResponse.Bank_ID);
      console.log('Method:', accountResponse.Method);

      // 2. 送金指示登録
      console.log('送金指示登録を開始...');
      const depositParams = {
        ShopID: 'test_shop_id', // 実際の値は環境変数から取得
        ShopPass: 'test_shop_pass', // 実際の値は環境変数から取得
        DepositID: depositId,
        BankID: bankId,
        Amount: '50000', // 50,000円
        SelectKey: 'monthly_settlement_2024_01',
      };

      const depositResponse = await client.registerDeposit(depositParams);

      if (depositResponse.ErrCode) {
        console.error('❌ 送金指示登録エラー:', depositResponse.ErrCode, depositResponse.ErrInfo);
        return;
      }

      if (depositResponse.Deposit_ID && depositResponse.Method) {
        console.log('✅ 送金指示登録成功');
        console.log('Deposit ID:', depositResponse.Deposit_ID);
        console.log('Bank ID:', depositResponse.Bank_ID);
        console.log('Amount:', depositResponse.Amount);
        console.log('Bank Fee:', depositResponse.Bank_Fee);

        // 3. 送金状況確認
        console.log('送金状況確認を開始...');
        const searchResponse = await client.searchDeposit({
          ShopID: 'test_shop_id', // 実際の値は環境変数から取得
          ShopPass: 'test_shop_pass', // 実際の値は環境変数から取得
          DepositID: depositId,
        });

        if (searchResponse.ErrCode) {
          console.error('❌ 送金状況確認エラー:', searchResponse.ErrCode, searchResponse.ErrInfo);
          return;
        }

        console.log('✅ 送金状況確認成功');
        console.log('Deposit ID:', searchResponse.Deposit_ID);
        console.log('Amount:', searchResponse.Amount);
        console.log('Select Key:', searchResponse.Select_Key);

        if (searchResponse.bank) {
          console.log('Bank Info:');
          console.log('  Bank ID:', searchResponse.bank.Bank_ID);
          console.log('  Bank Name:', searchResponse.bank.Bank_Name);
          console.log('  Result:', searchResponse.bank.Result);
          console.log('  Result Detail:', searchResponse.bank.Result_Detail);
          console.log('  Deposit Date:', searchResponse.bank.Deposit_Date);
        }

        // 4. 送金取消の例（必要に応じて）
        console.log('送金取消の例...');
        const cancelResponse = await client.cancelDeposit({
          ShopID: 'test_shop_id', // 実際の値は環境変数から取得
          ShopPass: 'test_shop_pass', // 実際の値は環境変数から取得
          DepositID: depositId,
        });

        if (cancelResponse.ErrCode) {
          console.error('❌ 送金取消エラー:', cancelResponse.ErrCode, cancelResponse.ErrInfo);
        } else {
          console.log('✅ 送金取消成功');
          console.log('Deposit ID:', cancelResponse.Deposit_ID);
          console.log('Method:', cancelResponse.Method);
        }
      }
    }
  } catch (error) {
    if (error instanceof GmoValidationError) {
      console.error('❌ バリデーションエラー:', error.message);
    } else if (error instanceof GmoApiError) {
      console.error('❌ GMO APIエラー:', error.message);
      console.error('エラーコード:', error.errorCode);
      console.error('エラー詳細:', error.message);
    } else if (error instanceof GmoNetworkError) {
      console.error('❌ ネットワークエラー:', error.message);
    } else if (error instanceof GmoTimeoutError) {
      console.error('❌ タイムアウトエラー:', error.message);
    } else {
      console.error('❌ 予期しないエラー:', error);
    }
  }
}

/**
 * 口座更新の例
 */
export async function updateAccountExample() {
  const client = createGmoRemittanceClient();

  try {
    const updateParams = {
      ShopID: 'test_shop_id',
      ShopPass: 'test_shop_pass',
      BankID: 'existing_bank_id',
      BankCode: '0009',
      BranchCode: '016', // 支店変更
      AccountType: '1' as const,
      AccountNumber: '7654321', // 口座番号変更
      AccountName: 'テスト　ハナコ', // 名義変更
      Free: '更新された口座情報',
    };

    const response = await client.updateAccount(updateParams);

    if (response.ErrCode) {
      console.error('❌ 口座更新エラー:', response.ErrCode, response.ErrInfo);
    } else {
      console.log('✅ 口座更新成功');
      console.log('Bank ID:', response.Bank_ID);
      console.log('Method:', response.Method);
    }
  } catch (error) {
    console.error('❌ 口座更新中にエラーが発生:', error);
  }
}

/**
 * 口座停止の例
 */
export async function stopAccountExample() {
  const client = createGmoRemittanceClient();

  try {
    const stopParams = {
      ShopID: 'test_shop_id',
      ShopPass: 'test_shop_pass',
      BankID: 'existing_bank_id',
    };

    const response = await client.stopAccount(stopParams);

    if (response.ErrCode) {
      console.error('❌ 口座停止エラー:', response.ErrCode, response.ErrInfo);
    } else {
      console.log('✅ 口座停止成功');
      console.log('Bank ID:', response.Bank_ID);
      console.log('Method:', response.Method);
    }
  } catch (error) {
    console.error('❌ 口座停止中にエラーが発生:', error);
  }
}

/**
 * ゆうちょ銀行口座登録の例
 */
export async function registerJapanPostBankAccount() {
  const client = createGmoRemittanceClient();

  try {
    const jpBankParams = {
      ShopID: 'test_shop_id',
      ShopPass: 'test_shop_pass',
      BankID: 'jpbank_' + Date.now(),
      BankCode: '9900', // ゆうちょ銀行
      BranchCode: '000', // ゆうちょ銀行の場合は000固定
      AccountType: '1' as const, // 普通預金
      AccountNumber: '0000000', // ゆうちょ銀行の場合は0000000固定
      AccountName: 'ユウチョ　タロウ',
      BranchCodeJpbank: '12345', // ゆうちょ記号
      AccountNumberJpbank: '********', // ゆうちょ番号
      Free: 'ゆうちょ銀行口座',
    };

    const response = await client.registerAccount(jpBankParams);

    if (response.ErrCode) {
      console.error('❌ ゆうちょ銀行口座登録エラー:', response.ErrCode, response.ErrInfo);
    } else {
      console.log('✅ ゆうちょ銀行口座登録成功');
      console.log('Bank ID:', response.Bank_ID);
      console.log('Method:', response.Method);
    }
  } catch (error) {
    console.error('❌ ゆうちょ銀行口座登録中にエラーが発生:', error);
  }
}

// 使用例の実行（開発時のテスト用）
if (require.main === module) {
  console.log('=== GMO 送金サービス使用例 ===');
  remittanceServiceFlow()
    .then(() => console.log('完了'))
    .catch((error) => console.error('エラー:', error));
}
