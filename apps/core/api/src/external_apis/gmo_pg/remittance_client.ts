/**
 * GMO ペイメントゲートウェイ 送金サービスAPI クライアント
 */

import { GmoValidationError } from './errors';
import { convertAccountRegistrationToApiParams, convertDepositRegistrationToApiParams, convertDepositSearchToApiParams } from './utils';
import type { GmoPgClient } from './client';
import type {
  AccountRegistrationParams,
  AccountRegistrationResponse,
  DepositRegistrationParams,
  DepositRegistrationResponse,
  DepositSearchParams,
  DepositSearchResponse,
} from './types';

/**
 * GMO 送金サービスクライアント関数群の型定義
 */
/**
 * GMO 送金サービスクライアント関数群の型定義
 *
 * - 引数は内部用 PascalCase 型（AccountRegistrationParams / DepositRegistrationParams / DepositSearchParams）
 * - メソッド内部で API 送信用 snake_case に変換
 * - register/cancel/stop/update/search では Method を呼び出し側で省略可能（固定値を内部で付与）
 */
export interface GmoRemittanceClient {
  /** 口座登録（AccountRegistration, Method='1'） */
  registerAccount: (_params: Omit<AccountRegistrationParams, 'Method'> & { Method?: '1' }) => Promise<AccountRegistrationResponse>;
  /** 口座更新（AccountRegistration, Method='2'） */
  updateAccount: (_params: Omit<AccountRegistrationParams, 'Method'> & { Method?: '2' }) => Promise<AccountRegistrationResponse>;
  /** 口座停止（AccountRegistration, Method='3'） */
  stopAccount: (_params: Omit<AccountRegistrationParams, 'Method'> & { Method?: '3' }) => Promise<AccountRegistrationResponse>;
  /** 送金指示登録（DepositRegistration, Method='1'） */
  registerDeposit: (
    _params: Omit<Extract<DepositRegistrationParams, { Method: '1' }>, 'Method'> & { Method?: '1' }
  ) => Promise<DepositRegistrationResponse>;
  /** 送金指示取消（DepositRegistration, Method='2'） */
  cancelDeposit: (
    _params: Omit<Extract<DepositRegistrationParams, { Method: '2' }>, 'Method' | 'BankID' | 'Amount'> & { Method?: '2' }
  ) => Promise<DepositRegistrationResponse>;
  /** 送金指示照会（DepositSearch） */
  searchDeposit: (_params: DepositSearchParams) => Promise<DepositSearchResponse>;
}

/**
 * AccountRegistrationパラメータのバリデーション
 */
const validateAccountRegistrationParams = (params: AccountRegistrationParams): void => {
  if (!params.ShopID || params.ShopID.length === 0 || params.ShopID.length > 13) {
    throw new GmoValidationError('ShopID must be 1-13 characters');
  }

  if (!params.ShopPass || params.ShopPass.length === 0 || params.ShopPass.length > 10) {
    throw new GmoValidationError('ShopPass must be 1-10 characters');
  }

  if (!['1', '2', '3'].includes(params.Method)) {
    throw new GmoValidationError('Method must be 1, 2, or 3');
  }

  if (!params.BankID || params.BankID.length === 0 || params.BankID.length > 60) {
    throw new GmoValidationError('BankID must be 1-60 characters');
  }

  // Method=1(新規)またはMethod=2(更新)の場合の条件付き必須チェック
  if (params.Method === '1' || params.Method === '2') {
    if (!params.BankCode || !/^\d{4}$/.test(params.BankCode)) {
      throw new GmoValidationError('BankCode must be 4 digits for new/update registration');
    }

    if (!params.BranchCode || !/^\d{3}$/.test(params.BranchCode)) {
      throw new GmoValidationError('BranchCode must be 3 digits for new/update registration');
    }

    if (!params.AccountType || !['1', '2', '4'].includes(params.AccountType)) {
      throw new GmoValidationError('AccountType must be 1, 2, or 4 for new/update registration');
    }

    if (!params.AccountNumber || !/^\d{1,7}$/.test(params.AccountNumber)) {
      throw new GmoValidationError('AccountNumber must be 1-7 digits for new/update registration');
    }

    if (!params.AccountName || params.AccountName.length === 0 || params.AccountName.length > 60) {
      throw new GmoValidationError('AccountName must be 1-60 characters for new/update registration');
    }

    // ゆうちょ銀行(9900)の場合の特別チェック
    if (params.BankCode === '9900') {
      if (!params.BranchCodeJpbank || !/^\d{5}$/.test(params.BranchCodeJpbank)) {
        throw new GmoValidationError('BranchCodeJpbank must be 5 digits for Japan Post Bank');
      }

      if (!params.AccountNumberJpbank || !/^\d{1,8}$/.test(params.AccountNumberJpbank)) {
        throw new GmoValidationError('AccountNumberJpbank must be 1-8 digits for Japan Post Bank');
      }
    }
  }

  if (params.Free && params.Free.length > 40) {
    throw new GmoValidationError('Free must be 40 characters or less');
  }
};

/**
 * DepositRegistrationパラメータのバリデーション
 */
const validateDepositRegistrationParams = (params: DepositRegistrationParams): void => {
  if (!params.ShopID || params.ShopID.length === 0 || params.ShopID.length > 13) {
    throw new GmoValidationError('ShopID must be 1-13 characters');
  }

  if (!params.ShopPass || params.ShopPass.length === 0 || params.ShopPass.length > 10) {
    throw new GmoValidationError('ShopPass must be 1-10 characters');
  }

  if (!['1', '2'].includes(params.Method)) {
    throw new GmoValidationError('Method must be 1 or 2');
  }

  if (!params.DepositID || params.DepositID.length === 0 || params.DepositID.length > 27) {
    throw new GmoValidationError('DepositID must be 1-27 characters');
  }

  // Method=1(登録)の場合の必須チェック
  if (params.Method === '1') {
    if (!params.BankID || params.BankID.length === 0 || params.BankID.length > 60) {
      throw new GmoValidationError('BankID is required for deposit registration');
    }

    if (!params.Amount || !/^\d{1,7}$/.test(params.Amount)) {
      throw new GmoValidationError('Amount must be 1-7 digits for deposit registration');
    }

    // 金額上限チェック（1,000,000円）
    const amountNum = parseInt(params.Amount, 10);
    if (amountNum > 1000000) {
      throw new GmoValidationError('Amount must be 1,000,000 or less');
    }
  }

  if (params.BankFee && !/^\d{1,6}$/.test(params.BankFee)) {
    throw new GmoValidationError('BankFee must be 1-6 digits');
  }

  if (params.SelectKey && params.SelectKey.length > 20) {
    throw new GmoValidationError('SelectKey must be 20 characters or less');
  }
};

/**
 * DepositSearchパラメータのバリデーション
 */
const validateDepositSearchParams = (params: DepositSearchParams): void => {
  if (!params.ShopID || params.ShopID.length === 0 || params.ShopID.length > 13) {
    throw new GmoValidationError('ShopID must be 1-13 characters');
  }

  if (!params.ShopPass || params.ShopPass.length === 0 || params.ShopPass.length > 10) {
    throw new GmoValidationError('ShopPass must be 1-10 characters');
  }

  if (!params.DepositID || params.DepositID.length === 0 || params.DepositID.length > 27) {
    throw new GmoValidationError('DepositID must be 1-27 characters');
  }
};

/**
 * パラメータをRecord<string, unknown>に変換
 */
const toRecord = (params: object): Record<string, unknown> => {
  return { ...params };
};

/**
 * GMO 送金サービスクライアントファクトリー関数
 */
export const createGmoRemittanceClient = (client: GmoPgClient): GmoRemittanceClient => {
  /**
   * 口座新規登録
   * AccountRegistration APIの呼び出し（Method=1）
   */
  const registerAccount = async (
    params: Omit<AccountRegistrationParams, 'Method'> & { Method?: '1' }
  ): Promise<AccountRegistrationResponse> => {
    const registrationParams: AccountRegistrationParams = { ...params, Method: '1' as const };
    validateAccountRegistrationParams(registrationParams);
    const apiParams = convertAccountRegistrationToApiParams(registrationParams);
    const response = await client.post('/api/AccountRegistration.idPass', toRecord(apiParams));
    return response;
  };

  /**
   * 口座更新
   * AccountRegistration APIの呼び出し（Method=2）
   */
  const updateAccount = async (
    params: Omit<AccountRegistrationParams, 'Method'> & { Method?: '2' }
  ): Promise<AccountRegistrationResponse> => {
    const updateParams: AccountRegistrationParams = { ...params, Method: '2' as const };
    validateAccountRegistrationParams(updateParams);
    const apiParams = convertAccountRegistrationToApiParams(updateParams);
    const response = await client.post('/api/AccountRegistration.idPass', toRecord(apiParams));
    return response;
  };

  /**
   * 口座停止
   * AccountRegistration APIの呼び出し（Method=3）
   */
  const stopAccount = async (
    params: Omit<AccountRegistrationParams, 'Method'> & { Method?: '3' }
  ): Promise<AccountRegistrationResponse> => {
    const stopParams: AccountRegistrationParams = { ...params, Method: '3' as const };
    validateAccountRegistrationParams(stopParams);
    const apiParams = convertAccountRegistrationToApiParams(stopParams);
    const response = await client.post('/api/AccountRegistration.idPass', toRecord(apiParams));
    return response;
  };

  /**
   * 送金指示登録
   * DepositRegistration APIの呼び出し（Method=1）。
   *
   * 呼び出し側はMethodを省略可能です（本メソッド内部で'1'固定を付与）。
   * 受け付ける引数は DepositRegistrationParams の Method='1' バリアントから Method を除いたものです。
   */
  const registerDeposit = async (
    params: Omit<Extract<DepositRegistrationParams, { Method: '1' }>, 'Method'> & { Method?: '1' }
  ): Promise<DepositRegistrationResponse> => {
    const registrationParams: DepositRegistrationParams = { ...params, Method: '1' as const };
    validateDepositRegistrationParams(registrationParams);
    const apiParams = convertDepositRegistrationToApiParams(registrationParams);
    const response = await client.post('/api/DepositRegistration.idPass', toRecord(apiParams));
    return response;
  };

  /**
   * 送金指示取消
   * DepositRegistration APIの呼び出し（Method=2）。
   *
   * 呼び出し側はMethodを省略可能です（本メソッド内部で'2'固定を付与）。
   * 受け付ける引数は DepositRegistrationParams の Method='2' バリアントから Method/BankID/Amount を除いたものです。
   */
  const cancelDeposit = async (
    params: Omit<Extract<DepositRegistrationParams, { Method: '2' }>, 'Method' | 'BankID' | 'Amount'> & { Method?: '2' }
  ): Promise<DepositRegistrationResponse> => {
    const cancelParams: DepositRegistrationParams = { ...params, Method: '2' as const };
    validateDepositRegistrationParams(cancelParams);
    const apiParams = convertDepositRegistrationToApiParams(cancelParams);
    const response = await client.post('/api/DepositRegistration.idPass', toRecord(apiParams));
    return response;
  };

  /**
   * 送金指示照会
   * DepositSearch APIの呼び出し
   */
  const searchDeposit = async (params: DepositSearchParams): Promise<DepositSearchResponse> => {
    validateDepositSearchParams(params);
    const apiParams = convertDepositSearchToApiParams(params);
    const response = await client.post('/api/shop/DepositSearch.idPass', toRecord(apiParams));
    return response;
  };

  return {
    registerAccount,
    updateAccount,
    stopAccount,
    registerDeposit,
    cancelDeposit,
    searchDeposit,
  };
};
