# GMO ペイメントゲートウェイ API クライアント

GMOペイメントゲートウェイの口座振替依頼APIと送金サービスAPIと通信するためのクライアントライブラリです。

## 特徴

- **Shift_JIS文字コード対応**: GMO APIの特殊な文字コード要件に対応
- **型安全**: TypeScriptによる完全な型定義
- **エラーハンドリング**: GMO固有エラーの統一的処理
- **リトライ機能**: 一時的なエラーに対する自動リトライ
- **バリデーション**: リクエストパラメータの事前検証
- **複数API対応**: 口座振替依頼APIと送金サービスAPIの両方をサポート

## インストール

必要な依存関係は既にインストール済みです：

```bash
pnpm add iconv-lite
```

このクライアントはNode.js組み込みの`fetch`を使用するため、追加のHTTPクライアントライブラリは不要です。

## 環境変数設定

`.env`ファイルに以下の環境変数を設定してください：

```env
# GMO ペイメントゲートウェイ設定
# 口座振替依頼API用
GMO_PG_BASE_URL=https://pt01.mul-pay.jp  # 本番環境
# GMO_PG_BASE_URL=https://test-pt01.mul-pay.jp  # テスト環境

# 送金サービスAPI用
# GMO_PG_BASE_URL=https://remittance.gmopg.jp  # 本番環境
# GMO_PG_BASE_URL=https://test-remittance.gmopg.jp  # テスト環境

GMO_PG_SHOP_ID=your_shop_id
GMO_PG_SHOP_PASS=your_shop_password
GMO_PG_SITE_ID=your_site_id
GMO_PG_SITE_PASS=your_site_password
GMO_PG_TIMEOUT=30000
GMO_PG_RETRY_COUNT=3
```

## 基本的な使用方法

### 1. クライアントの作成

#### 口座振替依頼API用クライアント

```typescript
import { createGmoBankTransferClient } from './index';

// デフォルト設定でクライアントを作成（環境変数から設定を読み込み）
const bankTransferClient = createGmoBankTransferClient();

// カスタム設定でクライアントを作成
const customBankTransferClient = createGmoBankTransferClient({
  timeout: 60000,
  retryCount: 5,
});
```

#### 送金サービスAPI用クライアント

```typescript
import { createGmoRemittanceClient } from './index';

// デフォルト設定でクライアントを作成（環境変数から設定を読み込み）
const remittanceClient = createGmoRemittanceClient();

// カスタム設定でクライアントを作成（baseUrl は内部で自動切替のため通常は不要）
const customRemittanceClient = createGmoRemittanceClient({
  timeout: 60000,
  retryCount: 5,
});
```

### 2. 口座振替依頼API の使用

#### 取引登録

```typescript
import { createGmoBankTransferClient, type EntryTranBankaccountParams, GmoApiError } from './index';

const client = createGmoBankTransferClient();

const params: EntryTranBankaccountParams = {
  ShopID: 'your_shop_id',
  ShopPass: 'your_shop_password',
  OrderID: 'ORDER001',
  Amount: '1000',
  Tax: '100',
};

try {
  const response = await client.entryTransaction(params);

  if (response.AccessID && response.AccessPass) {
    console.log('取引登録成功:', response.AccessID);
    // 次のステップで使用するためにAccessIDとAccessPassを保存
  } else {
    console.error('取引登録失敗:', response.ErrCode, response.ErrInfo);
  }
} catch (error) {
  if (error instanceof GmoApiError) {
    console.error('GMO APIエラー:', error.errorCode, error.errorDetail);
  }
}
```

#### 請求依頼

```typescript
import { type ExecTranBankaccountParams } from './index';

const execParams: ExecTranBankaccountParams = {
  AccessID: 'access_id_from_entry',
  AccessPass: 'access_pass_from_entry',
  OrderID: 'ORDER001',
  SiteID: 'your_site_id',
  SitePass: 'your_site_password',
  MemberID: 'member001',
  TargetDate: '********', // 振替指定日（5、6、23、27日のいずれか）
  Remarks: 'セイキュウナイヨウ',
  ClientField1: '加盟店自由項目1',
  ClientField2: '加盟店自由項目2',
  ClientField3: '加盟店自由項目3',
};

try {
  const response = await client.executeTransfer(execParams);

  if (response.AccessID) {
    console.log('請求依頼成功');
    console.log('振替指定日:', response.TargetDate);
    console.log('請求依頼受付終了日:', response.RequestAcceptEndDate);
    console.log('振替結果反映予定日:', response.TransferReturnDate);
  } else {
    console.error('請求依頼失敗:', response.ErrCode, response.ErrInfo);
  }
} catch (error) {
  console.error('請求依頼エラー:', error);
}
```

### 3. 送金サービスAPI の使用

注: 本クライアントでは、リクエストの内部表現は PascalCase 型で扱い、API送信直前に snake_case へ自動変換します。README のコード例も PascalCase を使用しています。

#### 口座登録

```typescript
import { createGmoRemittanceClient, type AccountRegistrationParams, GmoApiError } from './index';

const remittanceClient = createGmoRemittanceClient();

// 内部型（PascalCase）で指定し、API送信時にsnake_caseへ自動変換されます
const accountParams: Omit<AccountRegistrationParams, 'Method'> = {
  ShopID: 'your_shop_id',
  ShopPass: 'your_shop_password',
  BankID: 'unique_bank_id_001',
  BankCode: '0009', // 三井住友銀行
  BranchCode: '015', // 本店営業部
  AccountType: '1', // 普通預金
  AccountNumber: '1234567',
  AccountName: 'テスト　タロウ',
  Free: '月締め処理用口座',
};

try {
  const response = await remittanceClient.registerAccount(accountParams);

  if (response.Bank_ID && response.Method) {
    console.log('口座登録成功:', response.Bank_ID);
  } else {
    console.error('口座登録失敗:', response.ErrCode, response.ErrInfo);
  }
} catch (error) {
  if (error instanceof GmoApiError) {
    console.error('GMO APIエラー:', error.errorCode, error.errorDetail);
  }
}
```

#### 送金指示登録

```typescript
// 内部型（PascalCase）で指定し、API送信時にsnake_caseへ自動変換されます
const depositParams = {
  ShopID: 'your_shop_id',
  ShopPass: 'your_shop_password',
  DepositID: 'unique_deposit_id_001',
  BankID: 'unique_bank_id_001',
  Amount: '50000', // 50,000円
  SelectKey: 'monthly_settlement_2024_01',
};

try {
  const response = await remittanceClient.registerDeposit(depositParams);

  if (response.Deposit_ID && response.Method) {
    console.log('送金指示登録成功:', response.Deposit_ID);
    console.log('送金金額:', response.Amount);
    console.log('振込手数料:', response.Bank_Fee);
  } else {
    console.error('送金指示登録失敗:', response.ErrCode, response.ErrInfo);
  }
} catch (error) {
  console.error('送金指示登録エラー:', error);
}
```

#### 送金状況確認

```typescript
// 内部型（PascalCase）で指定し、API送信時にsnake_caseへ自動変換されます
import { type DepositSearchParams } from './index';

const searchParams: DepositSearchParams = {
  ShopID: 'your_shop_id',
  ShopPass: 'your_shop_password',
  DepositID: 'unique_deposit_id_001',
};

try {
  const response = await remittanceClient.searchDeposit(searchParams);

  if (response.Deposit_ID) {
    console.log('送金状況確認成功');
    console.log('送金ID:', response.Deposit_ID);
    console.log('金額:', response.Amount);

    if (response.bank) {
      console.log('処理結果:', response.bank.Result);
      console.log('送金実行日:', response.bank.Deposit_Date);

      // 処理結果の判定
      switch (response.bank.Result) {
        case '0':
          console.log('ステータス: 登録完了');
          break;
        case '1':
          console.log('ステータス: 送金データ作成完了');
          break;
        case '2':
          console.log('ステータス: 送金データ作成失敗');
          break;
        case '3':
          console.log('ステータス: 送金完了');
          break;
        case '4':
          console.log('ステータス: 送金失敗');
          break;
        case '9':
          console.log('ステータス: 取消済');
          break;
      }
    }
  } else {
    console.error('送金状況確認失敗:', response.ErrCode, response.ErrInfo);
  }
} catch (error) {
  console.error('送金状況確認エラー:', error);
}
```

### 4. 取引状態参照

```typescript
import { type SearchTradeMultiParams } from './index';

const searchParams: SearchTradeMultiParams = {
  ShopID: 'your_shop_id',
  ShopPass: 'your_shop_password',
  OrderID: 'ORDER001',
  PayType: '28', // 口座振替(セレクト)
};

try {
  const response = await client.searchTransaction(searchParams);

  console.log('取引状態:', response.Status);
  console.log('処理日時:', response.ProcessDate);

  // 振替結果の確認
  if (response.BaResultCode) {
    console.log('振替結果コード:', response.BaResultCode);
  }
} catch (error) {
  console.error('取引状態参照エラー:', error);
}
```

### 5. 請求依頼取消

```typescript
import { type BankaccountCancelParams } from './index';

const cancelParams: BankaccountCancelParams = {
  ShopID: 'your_shop_id',
  ShopPass: 'your_shop_password',
  AccessID: 'access_id',
  AccessPass: 'access_pass',
  OrderID: 'ORDER001',
};

try {
  const response = await client.cancelTransfer(cancelParams);

  if (response.Status === 'CANCEL') {
    console.log('請求依頼取消成功');
  } else {
    console.error('請求依頼取消失敗:', response.ErrCode, response.ErrInfo);
  }
} catch (error) {
  console.error('請求依頼取消エラー:', error);
}
```

### 6. 請求依頼変更

```typescript
import { type BankaccountChangeParams } from './index';

const changeParams: BankaccountChangeParams = {
  ShopID: 'your_shop_id',
  ShopPass: 'your_shop_password',
  AccessID: 'access_id',
  AccessPass: 'access_pass',
  OrderID: 'ORDER001',
  Amount: '1500', // 変更後の金額
  Tax: '150', // 変更後の税額
  Remarks: 'ヘンコウゴ', // 変更後の請求内容
};

try {
  const response = await client.changeTransfer(changeParams);

  if (response.OrderID) {
    console.log('請求依頼変更成功');
    console.log('変更後金額:', response.Amount);
    console.log('変更後税額:', response.Tax);
  } else {
    console.error('請求依頼変更失敗:', response.ErrCode, response.ErrInfo);
  }
} catch (error) {
  console.error('請求依頼変更エラー:', error);
}
```

## エラーハンドリング

```typescript
import { GmoApiError, GmoNetworkError, GmoValidationError, GmoTimeoutError, GmoConfigError } from './index';

try {
  const response = await client.entryTransaction(params);
} catch (error) {
  if (error instanceof GmoApiError) {
    // GMO APIからのエラーレスポンス
    console.error('GMO APIエラー:', error.errorCode, error.errorDetail);
  } else if (error instanceof GmoNetworkError) {
    // ネットワーク関連のエラー
    console.error('ネットワークエラー:', error.message);
  } else if (error instanceof GmoValidationError) {
    // パラメータバリデーションエラー
    console.error('バリデーションエラー:', error.message);
  } else if (error instanceof GmoTimeoutError) {
    // タイムアウトエラー
    console.error('タイムアウトエラー:', error.message);
  } else if (error instanceof GmoConfigError) {
    // 設定エラー
    console.error('設定エラー:', error.message);
  } else {
    console.error('不明なエラー:', error);
  }
}
```

## API仕様

### 利用可能なAPI

1. **EntryTranBankaccount** - 取引登録
2. **ExecTranBankaccount** - 請求依頼
3. **BankaccountCancel** - 請求依頼取消
4. **BankaccountChange** - 請求依頼変更
5. **SearchTradeMulti** - 取引状態参照

### 振替指定日について

振替指定日（TargetDate）は以下の日のみ指定可能です：

- 5日
- 6日
- 23日
- 27日

### 取引状態について

SearchTradeMulti APIで取得できる取引状態：

- `UNPROCESSED`: 未決済
- `REQSUCCESS`: 請求登録
- `SEND`: 請求処理中
- `PAYSUCCESS`: 請求成功
- `CANCEL`: 請求取消
- `PAYFAIL`: 請求失敗

### 振替結果コードについて

請求失敗時の振替結果コード：

- `1`: 資金不足
- `2`: 預金取引なし
- `3`: 預金者都合による振替停止
- `4`: 預金口座振替依頼書未着もしくは不備
- `8`: 委託者の都合による振替停止
- `9`: その他
- `E`: 請求不能
- `N`: 振替結果未着

## 注意事項

1. **文字コード**: GMO APIはShift_JIS（windows-31j）を使用します
2. **リクエスト形式**: `application/x-www-form-urlencoded` 形式でのみ通信
3. **HTTPクライアント**: Node.js組み込みの`fetch`を使用（Node.js 18以降が必要）
4. **エラーコード**: GMO固有のエラーコードについては仕様書を参照
5. **タイムアウト**: 金融機関との通信のため、適切なタイムアウト設定が重要
6. **セキュリティ**: ShopID、ShopPass、SiteID、SitePassは機密情報として適切に管理
7. **関数ベース**: クライアントは関数ベースで実装されており、クラスインスタンスではありません

## 推奨フロー

口座振替依頼の推奨フローは以下の通りです：

1. **EntryTranBankaccount API**で取引登録
2. **ExecTranBankaccount API**で請求依頼
3. 必要に応じて**BankaccountCancel API**で取消
4. 必要に応じて**BankaccountChange API**で変更
5. **SearchTradeMulti API**で取引状態を確認

## 実装例

完全な実装例については、`example.ts`ファイルを参照してください。

## 関連ドキュメント

- `docs/external_api/GMO_PG_口座振替振替依頼API仕様.md`
- `docs/external_api/GMO_PG_口座振替オンライン申し込みAPI仕様_エラーコード一覧.md`
- `example.ts` - 実装例
