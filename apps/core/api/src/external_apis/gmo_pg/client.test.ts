/**
 * GMO ペイメントゲートウェイ 口座振替依頼API クライアントのテスト
 */

import { vi } from 'vitest';
import { GmoApiError, GmoValidationError } from './errors';
import { createGmoBankTransferClient, createGmoPgClient } from './index';

// fetchのモック
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('GMO Bank Transfer Client', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // テスト用環境変数を設定
    process.env.NODE_ENV = 'test';
    process.env.GMO_PG_BASE_URL = 'https://test-pt01.mul-pay.jp';
    process.env.GMO_PG_SHOP_ID = 'test_shop_id';
    process.env.GMO_PG_SHOP_PASS = 'test_shop_pass';
    process.env.GMO_PG_SITE_ID = 'test_site_id';
    process.env.GMO_PG_SITE_PASS = 'test_site_pass';
    process.env.GMO_PG_TIMEOUT = '30000';
    process.env.GMO_PG_RETRY_COUNT = '3';
  });

  describe('環境変数からの設定読み込み', () => {
    it('環境変数から正しく設定を読み込めること', () => {
      expect(() => createGmoPgClient()).not.toThrow();
      expect(() => createGmoBankTransferClient()).not.toThrow();
    });

    it('クライアントが正しく作成されること', () => {
      const client = createGmoBankTransferClient();

      expect(client).toHaveProperty('entryTransaction');
      expect(client).toHaveProperty('executeTransfer');
      expect(client).toHaveProperty('cancelTransfer');
      expect(client).toHaveProperty('changeTransfer');
      expect(client).toHaveProperty('searchTransaction');
    });
  });

  describe('entryTransaction', () => {
    it('正常なレスポンスを処理できること', async () => {
      const mockResponse = new Response(
        Buffer.from('AccessID=12345678901234567890123456789012&AccessPass=12345678901234567890123456789012', 'utf-8'),
        {
          status: 200,
          headers: { 'Content-Type': 'text/plain;charset=windows-31j' },
        }
      );
      mockFetch.mockResolvedValueOnce(mockResponse);

      const client = createGmoBankTransferClient();
      const result = await client.entryTransaction({
        ShopID: 'test_shop_id',
        ShopPass: 'test_shop_pass',
        OrderID: 'ORDER001',
        Amount: '1000',
      });

      expect(result.AccessID).toBe('12345678901234567890123456789012');
      expect(result.AccessPass).toBe('12345678901234567890123456789012');
    });

    it('エラーレスポンスを処理できること', async () => {
      const mockResponse = new Response(Buffer.from('ErrCode=E01&ErrInfo=E01040001', 'utf-8'), {
        status: 200,
        headers: { 'Content-Type': 'text/plain;charset=windows-31j' },
      });
      mockFetch.mockResolvedValueOnce(mockResponse);

      const client = createGmoBankTransferClient();

      await expect(
        client.entryTransaction({
          ShopID: 'test_shop_id',
          ShopPass: 'test_shop_pass',
          OrderID: 'ORDER001',
          Amount: '1000',
        })
      ).rejects.toThrow(GmoApiError);
    });

    it('バリデーションエラーが発生すること', async () => {
      const client = createGmoBankTransferClient();

      await expect(
        client.entryTransaction({
          ShopID: 'test_shop_id',
          ShopPass: 'test_shop_pass',
          OrderID: '', // 空のOrderID
          Amount: '1000',
        })
      ).rejects.toThrow(GmoValidationError);
    });
  });

  describe('executeTransfer', () => {
    it('正常なレスポンスを処理できること', async () => {
      const mockResponse = new Response(
        Buffer.from(
          'AccessID=12345678901234567890123456789012&TargetDate=********&RequestAcceptEndDate=********&TransferReturnDate=********',
          'utf-8'
        ),
        {
          status: 200,
          headers: { 'Content-Type': 'text/plain;charset=windows-31j' },
        }
      );
      mockFetch.mockResolvedValueOnce(mockResponse);

      const client = createGmoBankTransferClient();
      const result = await client.executeTransfer({
        AccessID: '12345678901234567890123456789012',
        AccessPass: '12345678901234567890123456789012',
        OrderID: 'ORDER001',
        SiteID: 'test_site_id',
        SitePass: 'test_site_pass',
        MemberID: 'member001',
        TargetDate: '********',
      });

      expect(result.AccessID).toBe('12345678901234567890123456789012');
      expect(result.TargetDate).toBe('********');
      expect(result.RequestAcceptEndDate).toBe('********');
      expect(result.TransferReturnDate).toBe('********');
    });

    it('無効なTargetDateでバリデーションエラーが発生すること', async () => {
      const client = createGmoBankTransferClient();

      await expect(
        client.executeTransfer({
          AccessID: '12345678901234567890123456789012',
          AccessPass: '12345678901234567890123456789012',
          OrderID: 'ORDER001',
          SiteID: 'test_site_id',
          SitePass: 'test_site_pass',
          MemberID: 'member001',
          TargetDate: '********', // 無効な日（1日は指定不可）
        })
      ).rejects.toThrow('TargetDate day must be 5, 6, 23, or 27');
    });
  });

  describe('searchTransaction', () => {
    it('正常なレスポンスを処理できること', async () => {
      const mockResponse = new Response(
        Buffer.from('Status=PAYSUCCESS&ProcessDate=**************&JobCd=RECEIVE&OrderID=ORDER001&PayType=28', 'utf-8'),
        {
          status: 200,
          headers: { 'Content-Type': 'text/plain;charset=windows-31j' },
        }
      );
      mockFetch.mockResolvedValueOnce(mockResponse);

      const client = createGmoBankTransferClient();
      const result = await client.searchTransaction({
        ShopID: 'test_shop_id',
        ShopPass: 'test_shop_pass',
        OrderID: 'ORDER001',
        PayType: '28',
      });

      expect(result.Status).toBe('PAYSUCCESS');
      expect(result.ProcessDate).toBe('**************');
      expect(result.JobCd).toBe('RECEIVE');
      expect(result.OrderID).toBe('ORDER001');
      expect(result.PayType).toBe('28');
    });

    it('無効なPayTypeでバリデーションエラーが発生すること', async () => {
      const client = createGmoBankTransferClient();

      await expect(
        client.searchTransaction({
          ShopID: 'test_shop_id',
          ShopPass: 'test_shop_pass',
          OrderID: 'ORDER001',
          PayType: '27' as '28', // 無効なPayType
        })
      ).rejects.toThrow('PayType must be 28 for bank transfer');
    });
  });

  describe('cancelTransfer', () => {
    it('正常なレスポンスを処理できること', async () => {
      const mockResponse = new Response(Buffer.from('OrderID=ORDER001&Status=CANCEL', 'utf-8'), {
        status: 200,
        headers: { 'Content-Type': 'text/plain;charset=windows-31j' },
      });
      mockFetch.mockResolvedValueOnce(mockResponse);

      const client = createGmoBankTransferClient();
      const result = await client.cancelTransfer({
        ShopID: 'test_shop_id',
        ShopPass: 'test_shop_pass',
        AccessID: '12345678901234567890123456789012',
        AccessPass: '12345678901234567890123456789012',
        OrderID: 'ORDER001',
      });

      expect(result.OrderID).toBe('ORDER001');
      expect(result.Status).toBe('CANCEL');
    });
  });

  describe('changeTransfer', () => {
    it('正常なレスポンスを処理できること', async () => {
      const mockResponse = new Response(Buffer.from('OrderID=ORDER001&Status=REQSUCCESS&Amount=1500&Tax=150&Remarks=CHANGED', 'utf-8'), {
        status: 200,
        headers: { 'Content-Type': 'text/plain;charset=windows-31j' },
      });
      mockFetch.mockResolvedValueOnce(mockResponse);

      const client = createGmoBankTransferClient();
      const result = await client.changeTransfer({
        ShopID: 'test_shop_id',
        ShopPass: 'test_shop_pass',
        AccessID: '12345678901234567890123456789012',
        AccessPass: '12345678901234567890123456789012',
        OrderID: 'ORDER001',
        Amount: '1500',
        Tax: '150',
        Remarks: 'CHANGED',
      });

      expect(result.OrderID).toBe('ORDER001');
      expect(result.Status).toBe('REQSUCCESS');
      expect(result.Amount).toBe('1500');
      expect(result.Tax).toBe('150');
      expect(result.Remarks).toBe('CHANGED');
    });
  });
});
