/**
 * GMO ペイメントゲートウェイ API クライアントのエントリーポイント
 */

import { createGmoBankTransferClient as createGmoBankTransferClientCore, type GmoBankTransferClient } from './bank_transfer_client';
import { createGmoRemittanceClient as createGmoRemittanceClientCore, type GmoRemittanceClient } from './remittance_client';
import { createGmoPgClient as createGmoPgClientCore, type GmoPgClient } from './client';
import { config } from '../../config/environment';
import type { GmoPgConfig } from './types';

/**
 * GMO PG設定を作成
 */
export function createGmoPgConfig(): GmoPgConfig {
  return {
    baseUrl: config.gmoPg.baseUrl,
    shopId: config.gmoPg.shopId,
    shopPass: config.gmoPg.shopPass,
    siteId: config.gmoPg.siteId,
    sitePass: config.gmoPg.sitePass,
    timeout: config.gmoPg.timeout,
    retryCount: config.gmoPg.retryCount,
  };
}

/**
 * GMO PG基本クライアントを作成
 */
export function createGmoPgClient(customConfig?: Partial<GmoPgConfig>): GmoPgClient {
  const defaultConfig = createGmoPgConfig();
  const finalConfig = { ...defaultConfig, ...customConfig };
  return createGmoPgClientCore(finalConfig);
}

/**
 * GMO口座振替依頼クライアントを作成
 */
export function createGmoBankTransferClient(customConfig?: Partial<GmoPgConfig>): GmoBankTransferClient {
  const client = createGmoPgClient(customConfig);
  return createGmoBankTransferClientCore(client);
}

/**
 * GMO送金サービスクライアントを作成
 */
export function createGmoRemittanceClient(customConfig?: Partial<GmoPgConfig>): GmoRemittanceClient {
  const client = createGmoPgClient(customConfig);
  return createGmoRemittanceClientCore(client);
}

// 型定義とエラークラスをエクスポート
export * from './types';
export * from './errors';
export * from './utils';
// 低レベルクライアント関数を別名でエクスポート
export { createGmoPgClient as createGmoPgClientCore, type GmoPgClient } from './client';
export { createGmoBankTransferClient as createGmoBankTransferClientCore, type GmoBankTransferClient } from './bank_transfer_client';
export { createGmoRemittanceClient as createGmoRemittanceClientCore, type GmoRemittanceClient } from './remittance_client';
