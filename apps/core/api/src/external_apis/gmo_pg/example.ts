/**
 * GMO 口座振替依頼API クライアントの使用例
 *
 * このファイルは実装例を示すためのものです。
 * 実際のプロダクションコードでは、適切なエラーハンドリングと
 * ログ記録を実装してください。
 *
 * 【重要】口座振替依頼の推奨フロー:
 * 1. EntryTranBankaccount APIで取引登録
 * 2. ExecTranBankaccount APIで請求依頼
 * 3. 必要に応じてBankaccountCancel APIで取消
 * 4. 必要に応じてBankaccountChange APIで変更
 * 5. SearchTradeMulti APIで取引状態を確認
 */

import {
  createGmoBankTransferClient,
  type EntryTranBankaccountParams,
  type ExecTranBankaccountParams,
  type GmoBankTransferClient,
  GmoApiError,
  GmoNetworkError,
  GmoValidationError,
  GmoTimeoutError,
} from './index';

/**
 * 口座振替依頼の完全なフロー例
 */
export async function bankTransferRequestFlow() {
  const client = createGmoBankTransferClient();

  const orderId = 'ORDER' + Date.now();
  const memberId = 'member_' + Date.now();

  try {
    // 1. 取引登録
    console.log('取引登録を開始...');
    const entryParams: EntryTranBankaccountParams = {
      ShopID: 'test_shop_id', // 実際の値は設定から取得される
      ShopPass: 'test_shop_pass', // 実際の値は設定から取得される
      OrderID: orderId,
      Amount: '1000',
      Tax: '100',
    };

    const entryResponse = await client.entryTransaction(entryParams);

    if (entryResponse.AccessID && entryResponse.AccessPass) {
      console.log('✅ 取引登録成功');
      console.log('Access ID:', entryResponse.AccessID);
      console.log('Access Pass:', entryResponse.AccessPass);

      // 2. 請求依頼
      console.log('請求依頼を開始...');
      const execParams: ExecTranBankaccountParams = {
        AccessID: entryResponse.AccessID,
        AccessPass: entryResponse.AccessPass,
        OrderID: orderId,
        SiteID: 'test_site_id', // 実際の値は環境変数から取得
        SitePass: 'test_site_pass', // 実際の値は環境変数から取得
        MemberID: memberId,
        TargetDate: '********', // 振替指定日（5、6、23、27日のいずれか）
        Remarks: 'テスト請求',
        ClientField1: '加盟店自由項目1',
        ClientField2: '加盟店自由項目2',
        ClientField3: '加盟店自由項目3',
      };

      const execResponse = await client.executeTransfer(execParams);

      if (execResponse.AccessID) {
        console.log('✅ 請求依頼成功');
        console.log('Target Date:', execResponse.TargetDate);
        console.log('Request Accept End Date:', execResponse.RequestAcceptEndDate);
        console.log('Transfer Return Date:', execResponse.TransferReturnDate);

        // 3. 取引状態確認
        await checkTransactionStatus(client, orderId);
      } else {
        console.error('❌ 請求依頼失敗');
        console.error('Error Code:', execResponse.ErrCode);
        console.error('Error Info:', execResponse.ErrInfo);
      }
    } else {
      console.error('❌ 取引登録失敗');
      console.error('Error Code:', entryResponse.ErrCode);
      console.error('Error Info:', entryResponse.ErrInfo);
    }
  } catch (error) {
    console.error('口座振替依頼フローエラー:', error);
    handleGmoError(error);
  }
}

/**
 * 取引状態確認の例
 */
export async function checkTransactionStatus(client: GmoBankTransferClient, orderId: string) {
  try {
    console.log('取引状態確認を開始...');
    const searchResponse = await client.searchTransaction({
      ShopID: 'test_shop_id', // 実際の値は環境変数から取得
      ShopPass: 'test_shop_pass', // 実際の値は環境変数から取得
      OrderID: orderId,
      PayType: '28',
    });

    console.log('📊 取引状態:', searchResponse.Status);
    console.log('処理日時:', searchResponse.ProcessDate);
    console.log('処理区分:', searchResponse.JobCd);

    if (searchResponse.Status) {
      switch (searchResponse.Status) {
        case 'UNPROCESSED':
          console.log('⏳ 未決済');
          break;
        case 'REQSUCCESS':
          console.log('📝 請求登録');
          break;
        case 'SEND':
          console.log('🔄 請求処理中');
          break;
        case 'PAYSUCCESS':
          console.log('✅ 請求成功');
          break;
        case 'CANCEL':
          console.log('❌ 請求取消');
          break;
        case 'PAYFAIL':
          console.log('💥 請求失敗');
          if (searchResponse.BaResultCode) {
            console.log('振替結果コード:', searchResponse.BaResultCode);
            interpretResultCode(searchResponse.BaResultCode);
          }
          break;
        default:
          console.log('❓ 不明な状態:', searchResponse.Status);
      }
    }

    return searchResponse;
  } catch (error) {
    console.error('取引状態確認エラー:', error);
    throw error;
  }
}

/**
 * 振替結果コードの解釈
 */
function interpretResultCode(resultCode: string) {
  const codeMap: Record<string, string> = {
    '1': '資金不足',
    '2': '預金取引なし',
    '3': '預金者都合による振替停止',
    '4': '預金口座振替依頼書未着もしくは不備',
    '8': '委託者の都合による振替停止',
    '9': 'その他',
    E: '請求不能',
    N: '振替結果未着',
  };

  const meaning = codeMap[resultCode] || '不明なコード';
  console.log(`振替結果の詳細: ${resultCode} - ${meaning}`);
}

/**
 * 請求依頼取消の例
 */
export async function cancelTransferExample(accessId: string, accessPass: string, orderId: string) {
  const client = createGmoBankTransferClient();

  try {
    console.log('請求依頼取消を開始...');
    const cancelResponse = await client.cancelTransfer({
      ShopID: 'test_shop_id', // 実際の値は環境変数から取得
      ShopPass: 'test_shop_pass', // 実際の値は環境変数から取得
      AccessID: accessId,
      AccessPass: accessPass,
      OrderID: orderId,
    });

    if (cancelResponse.Status === 'CANCEL') {
      console.log('✅ 請求依頼取消成功');
      console.log('Order ID:', cancelResponse.OrderID);
      console.log('Status:', cancelResponse.Status);
    } else {
      console.error('❌ 請求依頼取消失敗');
      console.error('Error Code:', cancelResponse.ErrCode);
      console.error('Error Info:', cancelResponse.ErrInfo);
    }

    return cancelResponse;
  } catch (error) {
    console.error('請求依頼取消エラー:', error);
    throw error;
  }
}

/**
 * 請求依頼変更の例
 */
export async function changeTransferExample(accessId: string, accessPass: string, orderId: string) {
  const client = createGmoBankTransferClient();

  try {
    console.log('請求依頼変更を開始...');
    const changeResponse = await client.changeTransfer({
      ShopID: 'test_shop_id', // 実際の値は環境変数から取得
      ShopPass: 'test_shop_pass', // 実際の値は環境変数から取得
      AccessID: accessId,
      AccessPass: accessPass,
      OrderID: orderId,
      Amount: '1500', // 変更後の金額
      Tax: '150', // 変更後の税額
      Remarks: 'ヘンコウゴ', // 変更後の請求内容
    });

    if (changeResponse.OrderID) {
      console.log('✅ 請求依頼変更成功');
      console.log('Order ID:', changeResponse.OrderID);
      console.log('Status:', changeResponse.Status);
      console.log('Amount:', changeResponse.Amount);
      console.log('Tax:', changeResponse.Tax);
      console.log('Remarks:', changeResponse.Remarks);
    } else {
      console.error('❌ 請求依頼変更失敗');
      console.error('Error Code:', changeResponse.ErrCode);
      console.error('Error Info:', changeResponse.ErrInfo);
    }

    return changeResponse;
  } catch (error) {
    console.error('請求依頼変更エラー:', error);
    throw error;
  }
}

/**
 * GMOエラーハンドリングの例
 */
function handleGmoError(error: unknown) {
  if (error instanceof GmoApiError) {
    console.error('🔴 GMO APIエラー:', error.errorCode, error.errorDetail);
  } else if (error instanceof GmoNetworkError) {
    console.error('🌐 ネットワークエラー:', error.message);
  } else if (error instanceof GmoValidationError) {
    console.error('📝 バリデーションエラー:', error.message);
  } else if (error instanceof GmoTimeoutError) {
    console.error('⏰ タイムアウトエラー:', error.message);
  } else {
    console.error('❓ 不明なエラー:', error);
  }
}
