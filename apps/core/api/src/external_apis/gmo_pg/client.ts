/**
 * GMO ペイメントゲートウェイ 口座振替依頼API 基本クライアント
 */

import { GmoApiError, GmoNetworkError, GmoConfigError, GmoTimeoutError } from './errors';
import {
  encodeToShiftJIS,
  decodeFromShiftJIS,
  encodeFormData,
  parseGmoResponse,
  hasGmoError,
  extractGmoError,
  sanitizeRequestParams,
} from './utils';
import type {
  GmoPgConfig,
  EntryTranBankaccountResponse,
  ExecTranBankaccountResponse,
  BankaccountCancelResponse,
  BankaccountChangeResponse,
  SearchTradeMultiResponse,
  AccountRegistrationResponse,
  DepositRegistrationResponse,
  DepositSearchResponse,
} from './types';

/**
 * GMO PG クライアント関数群の型定義
 */
export interface GmoPgClient {
  post(_endpoint: '/payment/EntryTranBankaccount.idPass', _data: Record<string, unknown>): Promise<EntryTranBankaccountResponse>;
  post(_endpoint: '/payment/ExecTranBankaccount.idPass', _data: Record<string, unknown>): Promise<ExecTranBankaccountResponse>;
  post(_endpoint: '/payment/BankaccountCancel.idPass', _data: Record<string, unknown>): Promise<BankaccountCancelResponse>;
  post(_endpoint: '/payment/BankaccountChange.idPass', _data: Record<string, unknown>): Promise<BankaccountChangeResponse>;
  post(_endpoint: '/payment/SearchTradeMulti.idPass', _data: Record<string, unknown>): Promise<SearchTradeMultiResponse>;
  post(_endpoint: '/api/AccountRegistration.idPass', _data: Record<string, unknown>): Promise<AccountRegistrationResponse>;
  post(_endpoint: '/api/DepositRegistration.idPass', _data: Record<string, unknown>): Promise<DepositRegistrationResponse>;
  post(_endpoint: '/api/shop/DepositSearch.idPass', _data: Record<string, unknown>): Promise<DepositSearchResponse>;
  post(_endpoint: string, _data: Record<string, unknown>): Promise<Record<string, string>>;
}

/**
 * 設定の検証
 */
const validateConfig = (config: GmoPgConfig): void => {
  if (!config.baseUrl) {
    throw new GmoConfigError('baseUrl is required');
  }
  if (!config.shopId) {
    throw new GmoConfigError('shopId is required');
  }
  if (!config.shopPass) {
    throw new GmoConfigError('shopPass is required');
  }
  if (!config.siteId) {
    throw new GmoConfigError('siteId is required');
  }
  if (!config.sitePass) {
    throw new GmoConfigError('sitePass is required');
  }

  try {
    new URL(config.baseUrl);
  } catch {
    throw new GmoConfigError('baseUrl must be a valid URL');
  }
};

/**
 * リクエストログの出力
 */
const logRequest = (endpoint: string): void => {
  console.log(`GMO API Request: POST ${endpoint}`);
};

/**
 * レスポンスログの出力
 */
const logResponse = (status: number): void => {
  console.log(`GMO API Response: ${status}`);
};

/**
 * 指定時間待機
 */
const delay = (ms: number): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

/**
 * リトライ可能なエラーかどうかを判定
 */
const isRetryableError = (error: unknown): boolean => {
  if (error instanceof GmoTimeoutError) {
    return true;
  }

  if (error instanceof GmoNetworkError) {
    return true;
  }

  if (error instanceof GmoApiError) {
    // 一時的なシステムエラーの場合はリトライ
    return error.errorCode === 'E91' || error.errorCode === 'E92';
  }

  return false;
};

/**
 * GMO PG クライアントファクトリー関数
 */
export const createGmoPgClient = (config: GmoPgConfig): GmoPgClient => {
  // 設定の検証
  validateConfig(config);

  const baseUrl = config.baseUrl;
  const timeout = config.timeout || 30000;
  const retryCount = config.retryCount || 3;

  /**
   * リクエストデータの準備
   */
  const prepareRequestData = (data: Record<string, unknown>): Buffer => {
    // リクエストパラメータをサニタイズ
    const requestParams = sanitizeRequestParams(data);

    // フォームデータにエンコード
    const formData = encodeFormData(requestParams);

    // Shift_JISエンコーディング
    return encodeToShiftJIS(formData);
  };

  /**
   * HTTPリクエストの実行
   */
  const executeRequest = async (endpoint: string, data: Buffer): Promise<Response> => {
    const url = `${baseUrl}${endpoint}`;
    logRequest(endpoint);

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded;charset=windows-31j',
          Accept: 'text/plain;charset=windows-31j',
        },
        body: data,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      logResponse(response.status);

      if (!response.ok) {
        throw new GmoNetworkError(`HTTP Error: ${response.status}`, new Error(`HTTP ${response.status}`));
      }

      return response;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new GmoTimeoutError('Request timeout');
        }
        if (error.message.includes('fetch')) {
          throw new GmoNetworkError('Network error', error);
        }
      }

      throw new GmoNetworkError('Network error', error instanceof Error ? error : new Error(String(error)));
    }
  };

  /**
   * レスポンスのパーシング
   */
  const parseResponse = async (response: Response): Promise<Record<string, string>> => {
    // レスポンスをArrayBufferとして取得
    const arrayBuffer = await response.arrayBuffer();

    // Shift_JISからUTF-8に変換
    const responseText = decodeFromShiftJIS(Buffer.from(arrayBuffer));

    // GMOレスポンス形式をパース
    const parsedResponse = parseGmoResponse(responseText);

    // エラーチェック
    if (hasGmoError(parsedResponse)) {
      const errorInfo = extractGmoError(parsedResponse);
      throw new GmoApiError(errorInfo.errorCode || 'UNKNOWN', errorInfo.errorDetail || errorInfo.errorInfo || 'Unknown error');
    }

    return parsedResponse;
  };

  /**
   * POSTリクエストの実行
   */
  const post = async (endpoint: string, data: Record<string, unknown>): Promise<Record<string, string>> => {
    const requestData = prepareRequestData(data);

    for (let attempt = 1; attempt <= retryCount; attempt++) {
      try {
        const response = await executeRequest(endpoint, requestData);
        return await parseResponse(response);
      } catch (error) {
        if (attempt === retryCount) {
          throw error;
        }

        // リトライ可能なエラーの場合のみリトライ
        if (isRetryableError(error)) {
          console.log(`GMO API Request retry ${attempt}/${retryCount}`);
          await delay(1000 * attempt); // 指数バックオフ
          continue;
        }

        throw error;
      }
    }

    throw new Error('Unexpected error in retry loop');
  };

  // クライアント関数群を返す
  return {
    post,
  };
};
