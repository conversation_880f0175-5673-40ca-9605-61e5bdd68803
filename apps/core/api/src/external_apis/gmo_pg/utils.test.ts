/**
 * GMO ペイメントゲートウェイ ユーティリティ関数のテスト
 */

import { describe, it, expect } from 'vitest';
import {
  encodeToShiftJIS,
  decodeFromShiftJIS,
  encodeFormData,
  parseGmoResponse,
  hasGmoError,
  extractGmoError,
  sanitizeRequestParams,
  getByteLength,
  validateOrderId,
  validateMemberId,
  validateAmount,
  validateTargetDate,
  validateAccessId,
  validateAccessPass,
} from './utils';
import { GmoValidationError } from './errors';

describe('GMO Utils', () => {
  describe('Shift_JIS エンコーディング', () => {
    describe('encodeToShiftJIS', () => {
      it('ASCII文字列を正しくエンコードできること', () => {
        const text = 'Hello World 123';
        const encoded = encodeToShiftJIS(text);

        expect(encoded).toBeInstanceOf(Buffer);
        expect(encoded.length).toBeGreaterThan(0);
      });

      it('日本語文字列を正しくエンコードできること', () => {
        const text = 'こんにちは世界';
        const encoded = encodeToShiftJIS(text);

        expect(encoded).toBeInstanceOf(Buffer);
        expect(encoded.length).toBeGreaterThan(text.length); // マルチバイト文字のため
      });

      it('空文字列をエンコードできること', () => {
        const encoded = encodeToShiftJIS('');

        expect(encoded).toBeInstanceOf(Buffer);
        expect(encoded.length).toBe(0);
      });

      it('特殊文字を含む文字列をエンコードできること', () => {
        const text = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        const encoded = encodeToShiftJIS(text);

        expect(encoded).toBeInstanceOf(Buffer);
        expect(encoded.length).toBe(text.length); // ASCII文字は1バイト
      });
    });

    describe('decodeFromShiftJIS', () => {
      it('Shift_JISバッファを正しくデコードできること', () => {
        const originalText = 'Test String';
        const encoded = encodeToShiftJIS(originalText);
        const decoded = decodeFromShiftJIS(encoded);

        expect(decoded).toBe(originalText);
      });

      it('日本語を含むバッファを正しくデコードできること', () => {
        const originalText = 'テスト文字列';
        const encoded = encodeToShiftJIS(originalText);
        const decoded = decodeFromShiftJIS(encoded);

        expect(decoded).toBe(originalText);
      });

      it('空のバッファをデコードできること', () => {
        const emptyBuffer = Buffer.alloc(0);
        const decoded = decodeFromShiftJIS(emptyBuffer);

        expect(decoded).toBe('');
      });
    });

    describe('エンコード・デコードの往復変換', () => {
      it('様々な文字列で往復変換が正しく動作すること', () => {
        const testStrings = ['Hello World', 'こんにちは', 'テスト太郎', '銀行口座', '!@#$%^&*()', '123456789', 'Mixed文字列123', ''];

        for (const text of testStrings) {
          const encoded = encodeToShiftJIS(text);
          const decoded = decodeFromShiftJIS(encoded);
          expect(decoded).toBe(text);
        }
      });
    });
  });

  describe('フォームデータ処理', () => {
    describe('encodeFormData', () => {
      it('基本的なオブジェクトをフォームデータに変換できること', () => {
        const params = {
          key1: 'value1',
          key2: 'value2',
          key3: 123,
        };

        const result = encodeFormData(params);

        expect(result).toBe('key1=value1&key2=value2&key3=123');
      });

      it('undefined値を除外すること', () => {
        const params = {
          key1: 'value1',
          key2: undefined,
          key3: 'value3',
        };

        const result = encodeFormData(params);

        expect(result).toBe('key1=value1&key3=value3');
      });

      it('null値を除外すること', () => {
        const params = {
          key1: 'value1',
          key2: null,
          key3: 'value3',
        };

        const result = encodeFormData(params);

        expect(result).toBe('key1=value1&key3=value3');
      });

      it('空のオブジェクトで空文字列を返すこと', () => {
        const result = encodeFormData({});

        expect(result).toBe('');
      });

      it('数値、真偽値を文字列に変換すること', () => {
        const params = {
          number: 42,
          boolean: true,
          zero: 0,
          false_val: false,
        };

        const result = encodeFormData(params);

        expect(result).toBe('number=42&boolean=true&zero=0&false_val=false');
      });

      it('日本語文字列を含むパラメータを処理できること', () => {
        const params = {
          name: 'テスト太郎',
          address: '東京都',
          memo: 'テスト用メモ',
        };

        const result = encodeFormData(params);

        expect(result).toBe('name=テスト太郎&address=東京都&memo=テスト用メモ');
      });
    });
  });

  describe('GMOレスポンス処理', () => {
    describe('parseGmoResponse', () => {
      it('基本的なレスポンス文字列をパースできること', () => {
        const responseText = 'AccessID=12345&AccessPass=abcde&Status=SUCCESS';
        const result = parseGmoResponse(responseText);

        expect(result).toEqual({
          AccessID: '12345',
          AccessPass: 'abcde',
          Status: 'SUCCESS',
        });
      });

      it('空文字列で空オブジェクトを返すこと', () => {
        const result = parseGmoResponse('');

        expect(result).toEqual({});
      });

      it('空白のみの文字列で空オブジェクトを返すこと', () => {
        const result = parseGmoResponse('   \n\t  ');

        expect(result).toEqual({});
      });

      it('値が空の場合も正しく処理すること', () => {
        const responseText = 'key1=value1&key2=&key3=value3';
        const result = parseGmoResponse(responseText);

        expect(result).toEqual({
          key1: 'value1',
          key2: '',
          key3: 'value3',
        });
      });

      it('通常のGMOレスポンス形式を正しく処理すること', () => {
        const responseText = 'AccessID=abc123def456&AccessPass=xyz789uvw012&Status=SUCCESS';
        const result = parseGmoResponse(responseText);

        expect(result).toEqual({
          AccessID: 'abc123def456',
          AccessPass: 'xyz789uvw012',
          Status: 'SUCCESS',
        });
      });

      it('キーのみの場合は無視すること', () => {
        const responseText = 'key1=value1&invalidkey&key2=value2';
        const result = parseGmoResponse(responseText);

        expect(result).toEqual({
          key1: 'value1',
          key2: 'value2',
        });
      });
    });

    describe('hasGmoError', () => {
      it('ErrCodeが存在する場合はtrueを返すこと', () => {
        const response = { ErrCode: 'E01', ErrInfo: 'Error occurred' };

        expect(hasGmoError(response)).toBe(true);
      });

      it('ErrCodeが存在しない場合はfalseを返すこと', () => {
        const response = { AccessID: '12345', Status: 'SUCCESS' };

        expect(hasGmoError(response)).toBe(false);
      });

      it('空のオブジェクトでfalseを返すこと', () => {
        expect(hasGmoError({})).toBe(false);
      });
    });

    describe('extractGmoError', () => {
      it('エラー情報を正しく抽出できること', () => {
        const response = {
          ErrCode: 'E01',
          ErrDetail: 'Invalid parameter',
          ErrInfo: 'Parameter validation failed',
        };

        const result = extractGmoError(response);

        expect(result).toEqual({
          errorCode: 'E01',
          errorDetail: 'Invalid parameter',
          errorInfo: 'Parameter validation failed',
        });
      });

      it('一部のエラー情報が欠けている場合も処理できること', () => {
        const response = {
          ErrCode: 'E02',
          ErrInfo: 'System error',
        };

        const result = extractGmoError(response);

        expect(result).toEqual({
          errorCode: 'E02',
          errorDetail: undefined,
          errorInfo: 'System error',
        });
      });

      it('エラー情報が全くない場合も処理できること', () => {
        const response = { AccessID: '12345' };

        const result = extractGmoError(response);

        expect(result).toEqual({
          errorCode: undefined,
          errorDetail: undefined,
          errorInfo: undefined,
        });
      });
    });
  });

  describe('パラメータサニタイズ', () => {
    describe('sanitizeRequestParams', () => {
      it('基本的なパラメータをサニタイズできること', () => {
        const params = {
          string: 'test',
          number: 123,
          boolean: true,
        };

        const result = sanitizeRequestParams(params);

        expect(result).toEqual({
          string: 'test',
          number: '123',
          boolean: 'true',
        });
      });

      it('undefined値を除外すること', () => {
        const params = {
          valid: 'value',
          invalid: undefined,
        };

        const result = sanitizeRequestParams(params);

        expect(result).toEqual({
          valid: 'value',
        });
      });

      it('null値を除外すること', () => {
        const params = {
          valid: 'value',
          invalid: null,
        };

        const result = sanitizeRequestParams(params);

        expect(result).toEqual({
          valid: 'value',
        });
      });

      it('空のオブジェクトを処理できること', () => {
        const result = sanitizeRequestParams({});

        expect(result).toEqual({});
      });

      it('複雑なオブジェクトを文字列に変換すること', () => {
        const params = {
          object: { nested: 'value' },
          array: [1, 2, 3],
          date: new Date('2024-01-01'),
        };

        const result = sanitizeRequestParams(params);

        expect(result.object).toBe('[object Object]');
        expect(result.array).toBe('1,2,3');
        expect(typeof result.date).toBe('string');
      });
    });
  });

  describe('バイト長計算', () => {
    describe('getByteLength', () => {
      it('ASCII文字列のバイト長を正しく計算すること', () => {
        const text = 'Hello';
        const byteLength = getByteLength(text);

        expect(byteLength).toBe(5);
      });

      it('日本語文字列のバイト長を正しく計算すること', () => {
        const text = 'こんにちは'; // ひらがな5文字
        const byteLength = getByteLength(text);

        expect(byteLength).toBe(10); // Shift_JISでは1文字2バイト
      });

      it('混在文字列のバイト長を正しく計算すること', () => {
        const text = 'Hello世界'; // ASCII5文字 + 漢字2文字
        const byteLength = getByteLength(text);

        expect(byteLength).toBe(9); // 5 + 4 = 9バイト
      });

      it('空文字列のバイト長が0であること', () => {
        const byteLength = getByteLength('');

        expect(byteLength).toBe(0);
      });

      it('特殊文字のバイト長を正しく計算すること', () => {
        const text = '!@#$%';
        const byteLength = getByteLength(text);

        expect(byteLength).toBe(5);
      });
    });
  });

  describe('バリデーション関数', () => {
    describe('validateOrderId', () => {
      it('有効なOrderIDを受け入れること', () => {
        const validOrderIds = ['ORDER001', 'order-123', 'order_456', 'order.789', 'a1b2c3d4e5f6g7h8i9j0k1l2m3n'];

        for (const orderId of validOrderIds) {
          expect(() => validateOrderId(orderId)).not.toThrow();
        }
      });

      it('空のOrderIDでエラーを投げること', () => {
        expect(() => validateOrderId('')).toThrow(GmoValidationError);
        expect(() => validateOrderId('')).toThrow('OrderID is required');
      });

      it('長すぎるOrderIDでエラーを投げること', () => {
        const longOrderId = 'a'.repeat(28); // 28文字（制限は27文字）

        expect(() => validateOrderId(longOrderId)).toThrow(GmoValidationError);
        expect(() => validateOrderId(longOrderId)).toThrow('OrderID must be 27 characters or less');
      });

      it('無効な文字を含むOrderIDでエラーを投げること', () => {
        const invalidOrderIds = ['order@123', 'order#456', 'order 789', 'order+abc', 'order/def'];

        for (const orderId of invalidOrderIds) {
          expect(() => validateOrderId(orderId)).toThrow(GmoValidationError);
          expect(() => validateOrderId(orderId)).toThrow('OrderID contains invalid characters');
        }
      });
    });

    describe('validateMemberId', () => {
      it('有効なMemberIDを受け入れること', () => {
        const validMemberIds = [
          'member001',
          'member-123',
          'member_456',
          'member.789',
          'a'.repeat(60), // 最大60文字
        ];

        for (const memberId of validMemberIds) {
          expect(() => validateMemberId(memberId)).not.toThrow();
        }
      });

      it('空のMemberIDでエラーを投げること', () => {
        expect(() => validateMemberId('')).toThrow(GmoValidationError);
        expect(() => validateMemberId('')).toThrow('MemberID is required');
      });

      it('長すぎるMemberIDでエラーを投げること', () => {
        const longMemberId = 'a'.repeat(61); // 61文字（制限は60文字）

        expect(() => validateMemberId(longMemberId)).toThrow(GmoValidationError);
        expect(() => validateMemberId(longMemberId)).toThrow('MemberID must be 60 characters or less');
      });

      it('無効な文字を含むMemberIDでエラーを投げること', () => {
        const invalidMemberIds = ['member@123', 'member#456', 'member 789', 'member+abc'];

        for (const memberId of invalidMemberIds) {
          expect(() => validateMemberId(memberId)).toThrow(GmoValidationError);
          expect(() => validateMemberId(memberId)).toThrow('MemberID contains invalid characters');
        }
      });
    });

    describe('validateAmount', () => {
      it('有効な金額を受け入れること', () => {
        const validAmounts = [
          '1',
          '100',
          '1000',
          '99999999', // 8桁
        ];

        for (const amount of validAmounts) {
          expect(() => validateAmount(amount)).not.toThrow();
        }
      });

      it('空の金額でエラーを投げること', () => {
        expect(() => validateAmount('')).toThrow(GmoValidationError);
        expect(() => validateAmount('')).toThrow('Amount is required');
      });

      it('0以下の金額でエラーを投げること', () => {
        // '0'は正規表現にマッチするが、数値チェックでエラーになる
        expect(() => validateAmount('0')).toThrow(GmoValidationError);
        expect(() => validateAmount('0')).toThrow('Amount must be greater than 0');

        // 負の数は正規表現チェックでエラーになる
        expect(() => validateAmount('-1')).toThrow(GmoValidationError);
        expect(() => validateAmount('-1')).toThrow('Amount must be 1-8 digits');

        expect(() => validateAmount('-100')).toThrow(GmoValidationError);
        expect(() => validateAmount('-100')).toThrow('Amount must be 1-8 digits');
      });

      it('9桁以上の金額でエラーを投げること', () => {
        const longAmount = '123456789'; // 9桁（制限は8桁）

        expect(() => validateAmount(longAmount)).toThrow(GmoValidationError);
        expect(() => validateAmount(longAmount)).toThrow('Amount must be 1-8 digits');
      });

      it('数字以外を含む金額でエラーを投げること', () => {
        const invalidAmounts = ['100.50', '1,000', '100円', 'abc', '1a2b'];

        for (const amount of invalidAmounts) {
          expect(() => validateAmount(amount)).toThrow(GmoValidationError);
          expect(() => validateAmount(amount)).toThrow('Amount must be 1-8 digits');
        }
      });
    });

    describe('validateTargetDate', () => {
      it('有効な振替指定日を受け入れること', () => {
        const validDates = [
          '20240305', // 5日
          '20240306', // 6日
          '20240323', // 23日
          '20240327', // 27日
          '20241205', // 12月5日
        ];

        for (const date of validDates) {
          expect(() => validateTargetDate(date)).not.toThrow();
        }
      });

      it('空の日付でエラーを投げること', () => {
        expect(() => validateTargetDate('')).toThrow(GmoValidationError);
        expect(() => validateTargetDate('')).toThrow('TargetDate is required');
      });

      it('YYYYMMDD形式でない日付でエラーを投げること', () => {
        const invalidFormats = ['2024-03-05', '24/03/05', '20240305123', '2024305', 'abcd0305'];

        for (const date of invalidFormats) {
          expect(() => validateTargetDate(date)).toThrow(GmoValidationError);
          expect(() => validateTargetDate(date)).toThrow('TargetDate must be YYYYMMDD format');
        }
      });

      it('無効な日付でエラーを投げること', () => {
        const invalidDates = [
          '20240230', // 2月30日（存在しない）
          '20240431', // 4月31日（存在しない）
          '20241301', // 13月（存在しない）
          '20240001', // 0日（存在しない）
        ];

        for (const date of invalidDates) {
          expect(() => validateTargetDate(date)).toThrow(GmoValidationError);
          expect(() => validateTargetDate(date)).toThrow('TargetDate is not a valid date');
        }
      });

      it('指定不可能な日でエラーを投げること', () => {
        const invalidDays = [
          '20240301', // 1日
          '20240302', // 2日
          '20240304', // 4日
          '20240307', // 7日
          '20240315', // 15日
          '20240331', // 31日
        ];

        for (const date of invalidDays) {
          expect(() => validateTargetDate(date)).toThrow(GmoValidationError);
          expect(() => validateTargetDate(date)).toThrow('TargetDate day must be 5, 6, 23, or 27');
        }
      });
    });

    describe('validateAccessId', () => {
      it('有効なAccessIDを受け入れること', () => {
        const validAccessIds = [
          '12345678901234567890123456789012', // 32文字の英数字
          'abcdefghijklmnopqrstuvwxyz123456', // 32文字の英数字
          'ABCDEFGHIJKLMNOPQRSTUVWXYZ123456', // 32文字の英数字（大文字）
        ];

        for (const accessId of validAccessIds) {
          expect(() => validateAccessId(accessId)).not.toThrow();
        }
      });

      it('空のAccessIDでエラーを投げること', () => {
        expect(() => validateAccessId('')).toThrow(GmoValidationError);
        expect(() => validateAccessId('')).toThrow('AccessID is required');
      });

      it('32文字でないAccessIDでエラーを投げること', () => {
        const invalidLengthIds = [
          '1234567890123456789012345678901', // 31文字
          '123456789012345678901234567890123', // 33文字
          '12345', // 5文字
        ];

        for (const accessId of invalidLengthIds) {
          expect(() => validateAccessId(accessId)).toThrow(GmoValidationError);
          expect(() => validateAccessId(accessId)).toThrow('AccessID must be 32 characters');
        }
      });

      it('無効な文字を含むAccessIDでエラーを投げること', () => {
        const invalidAccessIds = [
          '1234567890123456789012345678901@', // 記号を含む
          '1234567890123456789012345678901-', // ハイフンを含む
          '1234567890123456789012345678901 ', // スペースを含む
          '123456789012345678901234567890１２', // 全角数字を含む
        ];

        for (const accessId of invalidAccessIds) {
          expect(() => validateAccessId(accessId)).toThrow(GmoValidationError);
          expect(() => validateAccessId(accessId)).toThrow('AccessID contains invalid characters');
        }
      });
    });

    describe('validateAccessPass', () => {
      it('有効なAccessPassを受け入れること', () => {
        const validAccessPasses = [
          '12345678901234567890123456789012', // 32文字の英数字
          'abcdefghijklmnopqrstuvwxyz123456', // 32文字の英数字
          'ABCDEFGHIJKLMNOPQRSTUVWXYZ123456', // 32文字の英数字（大文字）
        ];

        for (const accessPass of validAccessPasses) {
          expect(() => validateAccessPass(accessPass)).not.toThrow();
        }
      });

      it('空のAccessPassでエラーを投げること', () => {
        expect(() => validateAccessPass('')).toThrow(GmoValidationError);
        expect(() => validateAccessPass('')).toThrow('AccessPass is required');
      });

      it('32文字でないAccessPassでエラーを投げること', () => {
        const invalidLengthPasses = [
          '1234567890123456789012345678901', // 31文字
          '123456789012345678901234567890123', // 33文字
          '12345', // 5文字
        ];

        for (const accessPass of invalidLengthPasses) {
          expect(() => validateAccessPass(accessPass)).toThrow(GmoValidationError);
          expect(() => validateAccessPass(accessPass)).toThrow('AccessPass must be 32 characters');
        }
      });

      it('無効な文字を含むAccessPassでエラーを投げること', () => {
        const invalidAccessPasses = [
          '1234567890123456789012345678901@', // 記号を含む
          '1234567890123456789012345678901-', // ハイフンを含む
          '1234567890123456789012345678901 ', // スペースを含む
          '123456789012345678901234567890１２', // 全角数字を含む
        ];

        for (const accessPass of invalidAccessPasses) {
          expect(() => validateAccessPass(accessPass)).toThrow(GmoValidationError);
          expect(() => validateAccessPass(accessPass)).toThrow('AccessPass contains invalid characters');
        }
      });
    });
  });
});
