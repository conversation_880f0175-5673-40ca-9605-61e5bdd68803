/**
 * GMO ペイメントゲートウェイ 口座振替依頼API クライアント
 */

import { validateOrderId, validateMemberId, validateAmount, validateTargetDate, validateAccessId, validateAccessPass } from './utils';
import { GmoValidationError } from './errors';
import type { GmoPgClient } from './client';
import type {
  EntryTranBankaccountParams,
  EntryTranBankaccountResponse,
  ExecTranBankaccountParams,
  ExecTranBankaccountResponse,
  BankaccountCancelParams,
  BankaccountCancelResponse,
  BankaccountChangeParams,
  BankaccountChangeResponse,
  SearchTradeMultiParams,
  SearchTradeMultiResponse,
} from './types';

/**
 * GMO 口座振替依頼クライアント関数群の型定義
 */
export interface GmoBankTransferClient {
  entryTransaction: (_params: EntryTranBankaccountParams) => Promise<EntryTranBankaccountResponse>;
  executeTransfer: (_params: ExecTranBankaccountParams) => Promise<ExecTranBankaccountResponse>;
  cancelTransfer: (_params: BankaccountCancelParams) => Promise<BankaccountCancelResponse>;
  changeTransfer: (_params: BankaccountChangeParams) => Promise<BankaccountChangeResponse>;
  searchTransaction: (_params: SearchTradeMultiParams) => Promise<SearchTradeMultiResponse>;
}

/**
 * EntryTranBankaccountパラメータのバリデーション
 */
const validateEntryTranBankaccountParams = (params: EntryTranBankaccountParams): void => {
  validateOrderId(params.OrderID);
  validateAmount(params.Amount);

  if (params.Tax && !/^\d{1,7}$/.test(params.Tax)) {
    throw new GmoValidationError('Tax must be 1-7 digits');
  }
};

/**
 * ExecTranBankaccountパラメータのバリデーション
 */
const validateExecTranBankaccountParams = (params: ExecTranBankaccountParams): void => {
  validateAccessId(params.AccessID);
  validateAccessPass(params.AccessPass);
  validateOrderId(params.OrderID);
  validateMemberId(params.MemberID);
  validateTargetDate(params.TargetDate);

  if (params.Remarks && params.Remarks.length > 15) {
    throw new GmoValidationError('Remarks must be 15 characters or less');
  }

  if (params.ClientField1 && params.ClientField1.length > 100) {
    throw new GmoValidationError('ClientField1 must be 100 bytes or less');
  }

  if (params.ClientField2 && params.ClientField2.length > 100) {
    throw new GmoValidationError('ClientField2 must be 100 bytes or less');
  }

  if (params.ClientField3 && params.ClientField3.length > 100) {
    throw new GmoValidationError('ClientField3 must be 100 bytes or less');
  }

  if (params.CheckMode && params.CheckMode !== 'NOCHECK_ACCOUNT') {
    throw new GmoValidationError('CheckMode must be NOCHECK_ACCOUNT if specified');
  }
};

/**
 * BankaccountCancelパラメータのバリデーション
 */
const validateBankaccountCancelParams = (params: BankaccountCancelParams): void => {
  validateAccessId(params.AccessID);
  validateAccessPass(params.AccessPass);
  validateOrderId(params.OrderID);
};

/**
 * BankaccountChangeパラメータのバリデーション
 */
const validateBankaccountChangeParams = (params: BankaccountChangeParams): void => {
  validateAccessId(params.AccessID);
  validateAccessPass(params.AccessPass);
  validateOrderId(params.OrderID);

  if (params.Amount && !/^\d{1,8}$/.test(params.Amount)) {
    throw new GmoValidationError('Amount must be 1-8 digits');
  }

  if (params.Tax && !/^\d{1,7}$/.test(params.Tax)) {
    throw new GmoValidationError('Tax must be 1-7 digits');
  }

  if (params.Remarks && params.Remarks.length > 15) {
    throw new GmoValidationError('Remarks must be 15 characters or less');
  }
};

/**
 * SearchTradeMultiパラメータのバリデーション
 */
const validateSearchTradeMultiParams = (params: SearchTradeMultiParams): void => {
  validateOrderId(params.OrderID);

  if (params.PayType !== '28') {
    throw new GmoValidationError('PayType must be 28 for bank transfer');
  }
};

/**
 * パラメータをRecord<string, unknown>に変換
 */
const toRecord = (params: object): Record<string, unknown> => {
  return { ...params };
};

/**
 * GMO 口座振替依頼クライアントファクトリー関数
 */
export const createGmoBankTransferClient = (client: GmoPgClient): GmoBankTransferClient => {
  /**
   * 取引登録
   * EntryTranBankaccount APIの呼び出し
   */
  const entryTransaction = async (params: EntryTranBankaccountParams): Promise<EntryTranBankaccountResponse> => {
    validateEntryTranBankaccountParams(params);
    const response = await client.post('/payment/EntryTranBankaccount.idPass', toRecord(params));
    return response;
  };

  /**
   * 請求依頼
   * ExecTranBankaccount APIの呼び出し
   */
  const executeTransfer = async (params: ExecTranBankaccountParams): Promise<ExecTranBankaccountResponse> => {
    validateExecTranBankaccountParams(params);
    const response = await client.post('/payment/ExecTranBankaccount.idPass', toRecord(params));
    return response;
  };

  /**
   * 請求依頼取消
   * BankaccountCancel APIの呼び出し
   */
  const cancelTransfer = async (params: BankaccountCancelParams): Promise<BankaccountCancelResponse> => {
    validateBankaccountCancelParams(params);
    const response = await client.post('/payment/BankaccountCancel.idPass', toRecord(params));
    return response;
  };

  /**
   * 請求依頼変更
   * BankaccountChange APIの呼び出し
   */
  const changeTransfer = async (params: BankaccountChangeParams): Promise<BankaccountChangeResponse> => {
    validateBankaccountChangeParams(params);
    const response = await client.post('/payment/BankaccountChange.idPass', toRecord(params));
    return response;
  };

  /**
   * 取引状態参照
   * SearchTradeMulti APIの呼び出し
   */
  const searchTransaction = async (params: SearchTradeMultiParams): Promise<SearchTradeMultiResponse> => {
    validateSearchTradeMultiParams(params);
    const response = await client.post('/payment/SearchTradeMulti.idPass', toRecord(params));
    return response;
  };

  return {
    entryTransaction,
    executeTransfer,
    cancelTransfer,
    changeTransfer,
    searchTransaction,
  };
};
