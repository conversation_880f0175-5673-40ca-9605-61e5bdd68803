/**
 * GMO ペイメントゲートウェイ API ユーティリティ関数
 */

import * as iconv from 'iconv-lite';
import { GmoValidationError } from './errors';

/**
 * 文字列をShift_JISエンコーディングでエンコード
 */
export const encodeToShiftJIS = (text: string): Buffer => {
  return iconv.encode(text, 'shift_jis');
};

/**
 * Shift_JISエンコーディングからデコード
 */
export const decodeFromShiftJIS = (buffer: Buffer): string => {
  return iconv.decode(buffer, 'shift_jis');
};

/**
 * オブジェクトをURLエンコードされたフォームデータに変換
 */
export const encodeFormData = (params: Record<string, unknown>): string => {
  const pairs: string[] = [];

  for (const [key, value] of Object.entries(params)) {
    if (value !== undefined && value !== null) {
      // URLエンコードは行わず、生の文字列として結合
      pairs.push(`${key}=${String(value)}`);
    }
  }

  return pairs.join('&');
};

/**
 * GMOレスポンス文字列をパース
 */
export const parseGmoResponse = (responseText: string): Record<string, string> => {
  const result: Record<string, string> = {};

  if (!responseText.trim()) {
    return result;
  }

  const pairs = responseText.split('&');

  for (const pair of pairs) {
    const [key, value] = pair.split('=', 2);
    if (key && value !== undefined) {
      result[key] = value;
    }
  }

  return result;
};

/**
 * GMOエラーレスポンスかどうかを判定
 */
export const hasGmoError = (response: Record<string, string>): boolean => {
  return 'ErrCode' in response;
};

/**
 * GMOエラー情報を抽出
 */
export const extractGmoError = (
  response: Record<string, string>
): {
  errorCode?: string;
  errorDetail?: string;
  errorInfo?: string;
} => {
  return {
    errorCode: response.ErrCode,
    errorDetail: response.ErrDetail,
    errorInfo: response.ErrInfo,
  };
};

/**
 * リクエストパラメータのサニタイズ
 */
export const sanitizeRequestParams = (params: Record<string, unknown>): Record<string, unknown> => {
  const sanitized: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(params)) {
    if (value !== undefined && value !== null) {
      sanitized[key] = String(value);
    }
  }

  return sanitized;
};

/**
 * 文字列のバイト長を取得（Shift_JIS）
 * GMO仕様に合わせてShift_JISエンコーディングでバイト長を計算
 */
export const getByteLength = (str: string): number => {
  const shiftJisBuffer = iconv.encode(str, 'shift_jis');
  return shiftJisBuffer.length;
};

/**
 * OrderIDのバリデーション
 */
export const validateOrderId = (orderId: string): void => {
  if (!orderId) {
    throw new GmoValidationError('OrderID is required');
  }
  if (orderId.length > 27) {
    throw new GmoValidationError('OrderID must be 27 characters or less');
  }
  if (!/^[a-zA-Z0-9\-_.]+$/.test(orderId)) {
    throw new GmoValidationError('OrderID contains invalid characters');
  }
};

/**
 * MemberIDのバリデーション
 */
export const validateMemberId = (memberId: string): void => {
  if (!memberId) {
    throw new GmoValidationError('MemberID is required');
  }
  if (memberId.length > 60) {
    throw new GmoValidationError('MemberID must be 60 characters or less');
  }
  if (!/^[a-zA-Z0-9\-_.]+$/.test(memberId)) {
    throw new GmoValidationError('MemberID contains invalid characters');
  }
};

/**
 * Amountのバリデーション
 */
export const validateAmount = (amount: string): void => {
  if (!amount) {
    throw new GmoValidationError('Amount is required');
  }
  if (!/^\d{1,8}$/.test(amount)) {
    throw new GmoValidationError('Amount must be 1-8 digits');
  }
  const numAmount = parseInt(amount, 10);
  if (numAmount <= 0) {
    throw new GmoValidationError('Amount must be greater than 0');
  }
};

/**
 * TargetDateのバリデーション
 */
export const validateTargetDate = (targetDate: string): void => {
  if (!targetDate) {
    throw new GmoValidationError('TargetDate is required');
  }
  if (!/^\d{8}$/.test(targetDate)) {
    throw new GmoValidationError('TargetDate must be YYYYMMDD format');
  }

  const year = parseInt(targetDate.substring(0, 4), 10);
  const month = parseInt(targetDate.substring(4, 6), 10);
  const day = parseInt(targetDate.substring(6, 8), 10);

  // 基本的な日付妥当性チェック
  const date = new Date(year, month - 1, day);
  if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
    throw new GmoValidationError('TargetDate is not a valid date');
  }

  // 指定可能な日のチェック（5、6、23、27日）
  if (![5, 6, 23, 27].includes(day)) {
    throw new GmoValidationError('TargetDate day must be 5, 6, 23, or 27');
  }
};

/**
 * AccessIDのバリデーション
 */
export const validateAccessId = (accessId: string): void => {
  if (!accessId) {
    throw new GmoValidationError('AccessID is required');
  }
  if (accessId.length !== 32) {
    throw new GmoValidationError('AccessID must be 32 characters');
  }
  if (!/^[a-zA-Z0-9]+$/.test(accessId)) {
    throw new GmoValidationError('AccessID contains invalid characters');
  }
};

/**
 * AccessPassのバリデーション
 */
export const validateAccessPass = (accessPass: string): void => {
  if (!accessPass) {
    throw new GmoValidationError('AccessPass is required');
  }
  if (accessPass.length !== 32) {
    throw new GmoValidationError('AccessPass must be 32 characters');
  }
  if (!/^[a-zA-Z0-9]+$/.test(accessPass)) {
    throw new GmoValidationError('AccessPass contains invalid characters');
  }
};

/**
 * DepositID（送金ID）のバリデーション
 */
export const validateDepositId = (depositId: string): void => {
  if (!depositId) {
    throw new GmoValidationError('DepositID is required');
  }
  if (depositId.length > 27) {
    throw new GmoValidationError('DepositID must be 27 characters or less');
  }
  if (!/^[a-zA-Z0-9\-_.]+$/.test(depositId)) {
    throw new GmoValidationError('DepositID contains invalid characters');
  }
};

/**
 * BankID（口座ID）のバリデーション
 */
export const validateBankId = (bankId: string): void => {
  if (!bankId) {
    throw new GmoValidationError('BankID is required');
  }
  if (bankId.length > 60) {
    throw new GmoValidationError('BankID must be 60 characters or less');
  }
  if (!/^[a-zA-Z0-9\-_.]+$/.test(bankId)) {
    throw new GmoValidationError('BankID contains invalid characters');
  }
};

/**
 * AccountRegistrationParams（PascalCase）をAPI用（snake_case）に変換
 */
export const convertAccountRegistrationToApiParams = (
  params: import('./types').AccountRegistrationParams
): import('./types').AccountRegistrationApiParams => {
  const result: import('./types').AccountRegistrationApiParams = {
    Shop_ID: params.ShopID,
    Shop_Pass: params.ShopPass,
    Method: params.Method,
    Bank_ID: params.BankID,
  };

  if (params.BankCode !== undefined) result.Bank_Code = params.BankCode;
  if (params.BranchCode !== undefined) result.Branch_Code = params.BranchCode;
  if (params.AccountType !== undefined) result.Account_Type = params.AccountType;
  if (params.AccountNumber !== undefined) result.Account_Number = params.AccountNumber;
  if (params.AccountName !== undefined) result.Account_Name = params.AccountName;
  if (params.BranchCodeJpbank !== undefined) result.Branch_Code_Jpbank = params.BranchCodeJpbank;
  if (params.AccountNumberJpbank !== undefined) result.Account_Number_Jpbank = params.AccountNumberJpbank;
  if (params.Free !== undefined) result.Free = params.Free;

  return result;
};

/**
 * DepositRegistrationParams（PascalCase）をAPI用（snake_case）に変換
 */
export const convertDepositRegistrationToApiParams = (
  params: import('./types').DepositRegistrationParams
): import('./types').DepositRegistrationApiParams => {
  const baseParams = {
    Shop_ID: params.ShopID,
    Shop_Pass: params.ShopPass,
    Deposit_ID: params.DepositID,
    Method: params.Method,
  };

  const result: import('./types').DepositRegistrationApiParams =
    params.Method === '1'
      ? {
          ...baseParams,
          Method: '1' as const,
          Bank_ID: params.BankID,
          Amount: params.Amount,
          Bank_Fee: params.BankFee,
          Select_Key: params.SelectKey,
        }
      : {
          ...baseParams,
          Method: '2' as const,
          Bank_Fee: params.BankFee,
          Select_Key: params.SelectKey,
        };

  return result;
};

/**
 * DepositSearchParams（PascalCase）をAPI用（snake_case）に変換
 */
export const convertDepositSearchToApiParams = (
  params: import('./types').DepositSearchParams
): import('./types').DepositSearchApiParams => {
  return {
    Shop_ID: params.ShopID,
    Shop_Pass: params.ShopPass,
    Deposit_ID: params.DepositID,
  };
};

/**
 * GMOログ用の機微情報マスキング
 * JSON文字列を受け取り、ShopPass/SitePass/AccessPassなどの秘密情報をマスクする
 */
export const maskGmoSecrets = (jsonString?: string): string | undefined => {
  if (!jsonString) return jsonString;
  try {
    const data = JSON.parse(jsonString);
    const secretKeys = new Set(['ShopPass', 'SitePass', 'AccessPass']);
    return JSON.stringify(data, (key, value) => (secretKeys.has(key) ? '***' : value));
  } catch (ignore) {
    // JSONでなければそのまま返す
    return jsonString;
  }
};
