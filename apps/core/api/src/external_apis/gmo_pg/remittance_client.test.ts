/**
 * GMO ペイメントゲートウェイ 送金サービスAPI クライアントのテスト
 */

import { vi } from 'vitest';
import { createGmoPgClient } from './client';
import { createGmoRemittanceClient } from './remittance_client';

// fetchのモック
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('GMO Remittance Client', () => {
  const testConfig = {
    baseUrl: 'https://test-remittance.gmopg.jp',
    shopId: 'testshop01',
    shopPass: 'testpass1',
    siteId: 'testsite01',
    sitePass: 'testpass2',
    timeout: 30000,
    retryCount: 3,
  };

  const validAccountParams = {
    ShopID: 'testshop01',
    ShopPass: 'testpass1',
    BankID: 'bank123456789012345678901234567890123456789012345678901234',
    BankCode: '0009',
    BranchCode: '015',
    AccountType: '1' as const,
    AccountNumber: '1234567',
    AccountName: 'テスト　タロウ',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('クライアント作成', () => {
    it('クライアントが正しく作成されること', () => {
      const pgClient = createGmoPgClient(testConfig);
      const client = createGmoRemittanceClient(pgClient);

      expect(client).toHaveProperty('registerAccount');
      expect(client).toHaveProperty('updateAccount');
      expect(client).toHaveProperty('stopAccount');
      expect(client).toHaveProperty('registerDeposit');
      expect(client).toHaveProperty('cancelDeposit');
      expect(client).toHaveProperty('searchDeposit');
    });
  });

  describe('バリデーション', () => {
    it('無効なShop_Passでバリデーションエラーが発生すること', async () => {
      const pgClient = createGmoPgClient(testConfig);
      const client = createGmoRemittanceClient(pgClient);

      await expect(
        client.registerAccount({
          ...validAccountParams,
          ShopPass: 'toolongpassword', // 10文字を超える
        })
      ).rejects.toThrow('ShopPass must be 1-10 characters');
    });

    it('無効なBank_Codeでバリデーションエラーが発生すること', async () => {
      const pgClient = createGmoPgClient(testConfig);
      const client = createGmoRemittanceClient(pgClient);

      await expect(
        client.registerAccount({
          ...validAccountParams,
          BankCode: '123', // 3桁は無効
        })
      ).rejects.toThrow('BankCode must be 4 digits for new/update registration');
    });
  });

  describe('API呼び出し', () => {
    it('口座登録が正常に動作すること', async () => {
      const mockResponse = new Response(
        Buffer.from('Bank_ID=bank123456789012345678901234567890123456789012345678901234&Method=1', 'utf-8'),
        {
          status: 200,
          headers: { 'Content-Type': 'text/plain;charset=windows-31j' },
        }
      );
      mockFetch.mockResolvedValueOnce(mockResponse);

      const pgClient = createGmoPgClient(testConfig);
      const client = createGmoRemittanceClient(pgClient);
      const result = await client.registerAccount(validAccountParams);

      expect(result.Bank_ID).toBe('bank123456789012345678901234567890123456789012345678901234');
      expect(result.Method).toBe('1');
    });

    it('GMO APIエラーレスポンスを正しく処理できること', async () => {
      const mockResponse = new Response(Buffer.from('ErrCode=BA1&ErrInfo=BA1010001', 'utf-8'), {
        status: 200,
        headers: { 'Content-Type': 'text/plain;charset=windows-31j' },
      });
      mockFetch.mockResolvedValueOnce(mockResponse);

      const pgClient = createGmoPgClient(testConfig);
      const client = createGmoRemittanceClient(pgClient);

      await expect(client.registerAccount(validAccountParams)).rejects.toThrow('GMO API Error: BA1 - BA1010001');
    });
  });
});
