/**
 * GMO ペイメントゲートウェイ API エラークラス
 */

/**
 * GMO API エラーの基底クラス
 */
export abstract class GmoError extends Error {
  abstract readonly name: string;
  
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.cause = cause;
  }
}

/**
 * GMO API からのエラーレスポンス
 */
export class GmoApiError extends GmoError {
  readonly name = 'GmoApiError';
  
  constructor(
    public readonly errorCode: string,
    public readonly errorDetail: string,
    cause?: Error
  ) {
    super(`GMO API Error: ${errorCode} - ${errorDetail}`, cause);
  }
}

/**
 * ネットワーク関連のエラー
 */
export class GmoNetworkError extends GmoError {
  readonly name = 'GmoNetworkError';
  
  constructor(message: string, cause?: Error) {
    super(`GMO Network Error: ${message}`, cause);
  }
}

/**
 * タイムアウトエラー
 */
export class GmoTimeoutError extends GmoError {
  readonly name = 'GmoTimeoutError';
  
  constructor(message: string = 'Request timeout', cause?: Error) {
    super(`GMO Timeout Error: ${message}`, cause);
  }
}

/**
 * 設定エラー
 */
export class GmoConfigError extends GmoError {
  readonly name = 'GmoConfigError';
  
  constructor(message: string, cause?: Error) {
    super(`GMO Config Error: ${message}`, cause);
  }
}

/**
 * バリデーションエラー
 */
export class GmoValidationError extends GmoError {
  readonly name = 'GmoValidationError';
  
  constructor(message: string, cause?: Error) {
    super(`GMO Validation Error: ${message}`, cause);
  }
}
