/**
 * GMO ペイメントゲートウェイ 口座振替依頼API の型定義
 */

// GMO API設定
export interface GmoPgConfig {
  baseUrl: string;
  shopId: string;
  shopPass: string;
  siteId: string;
  sitePass: string;
  timeout?: number;
  retryCount?: number;
}

// EntryTranBankaccount 取引登録パラメータ
export interface EntryTranBankaccountParams {
  ShopID: string;
  ShopPass: string;
  OrderID: string;
  Amount: string;
  Tax?: string;
}

// EntryTranBankaccount 取引登録レスポンス
export interface EntryTranBankaccountResponse {
  AccessID?: string;
  AccessPass?: string;
  ErrCode?: string;
  ErrInfo?: string;
}

// ExecTranBankaccount 請求依頼パラメータ
export interface ExecTranBankaccountParams {
  AccessID: string;
  AccessPass: string;
  OrderID: string;
  SiteID: string;
  SitePass: string;
  MemberID: string;
  TargetDate: string;
  Remarks?: string;
  ClientField1?: string;
  ClientField2?: string;
  ClientField3?: string;
  CheckMode?: 'NOCHECK_ACCOUNT';
}

// ExecTranBankaccount 請求依頼レスポンス
export interface ExecTranBankaccountResponse {
  AccessID?: string;
  TargetDate?: string;
  RequestAcceptEndDate?: string;
  TransferReturnDate?: string;
  ErrCode?: string;
  ErrInfo?: string;
}

// BankaccountCancel 請求依頼取消パラメータ
export interface BankaccountCancelParams {
  ShopID: string;
  ShopPass: string;
  AccessID: string;
  AccessPass: string;
  OrderID: string;
}

// BankaccountCancel 請求依頼取消レスポンス
export interface BankaccountCancelResponse {
  OrderID?: string;
  Status?: string;
  ErrCode?: string;
  ErrInfo?: string;
}

// BankaccountChange 請求依頼変更パラメータ
export interface BankaccountChangeParams {
  ShopID: string;
  ShopPass: string;
  AccessID: string;
  AccessPass: string;
  OrderID: string;
  Amount?: string;
  Tax?: string;
  Remarks?: string;
}

// BankaccountChange 請求依頼変更レスポンス
export interface BankaccountChangeResponse {
  OrderID?: string;
  Status?: string;
  Amount?: string;
  Tax?: string;
  Remarks?: string;
  ErrCode?: string;
  ErrInfo?: string;
}

// SearchTradeMulti 取引状態参照パラメータ
export interface SearchTradeMultiParams {
  ShopID: string;
  ShopPass: string;
  OrderID: string;
  PayType: '28'; // 口座振替(セレクト)
}

// SearchTradeMulti 取引状態参照レスポンス
export interface SearchTradeMultiResponse {
  Status?: string;
  ProcessDate?: string;
  JobCd?: string;
  AccessID?: string;
  AccessPass?: string;
  OrderID?: string;
  Amount?: string;
  Tax?: string;
  SiteID?: string;
  MemberID?: string;
  BaTargetDate?: string;
  BaRequestAcceptEndDate?: string;
  BaTransferReturnDate?: string;
  BaWithdrawalDate?: string;
  BaResultCode?: string;
  ClientField1?: string;
  ClientField2?: string;
  ClientField3?: string;
  PayType?: string;
  ErrCode?: string;
  ErrInfo?: string;
}

// ===== 送金サービスAPI 型定義 =====

// AccountRegistration 口座登録・更新・停止パラメータ（内部用・PascalCase）
export interface AccountRegistrationParams {
  ShopID: string;
  ShopPass: string;
  Method: '1' | '2' | '3'; // 1:新規、2:更新、3:停止
  BankID: string;
  BankCode?: string;
  BranchCode?: string;
  AccountType?: '1' | '2' | '4'; // 1:普通、2:当座、4:貯蓄
  AccountNumber?: string;
  AccountName?: string;
  BranchCodeJpbank?: string;
  AccountNumberJpbank?: string;
  Free?: string;
}

// AccountRegistration 口座登録・更新・停止パラメータ（API送信用・snake_case）
export interface AccountRegistrationApiParams {
  Shop_ID: string;
  Shop_Pass: string;
  Method: '1' | '2' | '3'; // 1:新規、2:更新、3:停止
  Bank_ID: string;
  Bank_Code?: string;
  Branch_Code?: string;
  Account_Type?: '1' | '2' | '4'; // 1:普通、2:当座、4:貯蓄
  Account_Number?: string;
  Account_Name?: string;
  Branch_Code_Jpbank?: string;
  Account_Number_Jpbank?: string;
  Free?: string;
}

// AccountRegistration 口座登録・更新・停止レスポンス
export interface AccountRegistrationResponse {
  Bank_ID?: string;
  Method?: string;
  ErrCode?: string;
  ErrInfo?: string;
}

// DepositRegistration 送金指示登録・取消パラメータ - 共通フィールド（内部用・PascalCase）
interface DepositRegistrationParamsBase {
  ShopID: string;
  ShopPass: string;
  DepositID: string;
  BankFee?: string;
  SelectKey?: string;
}

// DepositRegistration 送金指示登録・取消パラメータ - 判別共用体型（内部用・PascalCase）
export type DepositRegistrationParams =
  | (DepositRegistrationParamsBase & {
      Method: '1'; // 登録
      BankID: string; // Method=1の場合必須
      Amount: string; // Method=1の場合必須
    })
  | (DepositRegistrationParamsBase & {
      Method: '2'; // 取消
      BankID?: never; // Method=2の場合は存在しない
      Amount?: never; // Method=2の場合は存在しない
    });

// DepositRegistration 送金指示登録・取消パラメータ - 共通フィールド（API送信用・snake_case）
interface DepositRegistrationApiParamsBase {
  Shop_ID: string;
  Shop_Pass: string;
  Deposit_ID: string;
  Bank_Fee?: string;
  Select_Key?: string;
}

// DepositRegistration 送金指示登録・取消パラメータ - 判別共用体型（API送信用・snake_case）
export type DepositRegistrationApiParams =
  | (DepositRegistrationApiParamsBase & {
      Method: '1'; // 登録
      Bank_ID: string; // Method=1の場合必須
      Amount: string; // Method=1の場合必須
    })
  | (DepositRegistrationApiParamsBase & {
      Method: '2'; // 取消
      Bank_ID?: never; // Method=2の場合は存在しない
      Amount?: never; // Method=2の場合は存在しない
    });

// DepositRegistration 送金指示登録・取消レスポンス
export interface DepositRegistrationResponse {
  Deposit_ID?: string;
  Bank_ID?: string;
  Method?: string;
  Amount?: string;
  Bank_Fee?: string;
  ErrCode?: string;
  ErrInfo?: string;
}

// DepositSearch 送金指示照会パラメータ（内部用・PascalCase）
export interface DepositSearchParams {
  ShopID: string;
  ShopPass: string;
  DepositID: string;
}

// DepositSearch 送金指示照会パラメータ（API送信用・snake_case）
export interface DepositSearchApiParams {
  Shop_ID: string;
  Shop_Pass: string;
  Deposit_ID: string;
}

// DepositSearch 送金指示照会レスポンス
export interface DepositSearchResponse {
  Deposit_ID?: string;
  Amount?: string;
  Free?: string;
  Select_Key?: string;
  bank?: {
    Bank_ID?: string;
    Bank_Name?: string;
    Bank_Code?: string;
    Branch_Name?: string;
    Branch_Code?: string;
    Account_Type?: string;
    Account_Number?: string;
    Account_Name?: string;
    Bank_Fee?: string;
    Result?: string; // 0:登録完了、1:送金データ作成完了、2:送金データ作成失敗、3:送金完了、4:送金失敗、9:取消済
    Branch_Code_Jpbank?: string;
    Account_Number_Jpbank?: string;
    Deposit_Date?: string;
    Result_Detail?: string;
    Client_Name?: string;
    Account_Name_Correction?: string;
  };
  ErrCode?: string;
  ErrInfo?: string;
}
