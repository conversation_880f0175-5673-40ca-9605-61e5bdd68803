import { Code, ConnectError, HandlerContext, type Interceptor } from '@connectrpc/connect';
import { err, ok } from 'neverthrow';
import { findAdminUserBySessionToken } from '@core-api/repositories/admin_user_repository';
import { kAdminUser } from './user';

export const adminUserAuthenticator: Interceptor = (next) => async (req) => {
  const sessionToken = req.header.get('sessionToken');
  if (!sessionToken) {
    throw new ConnectError('Unauthorized', Code.Unauthenticated);
  }
  const result = await findAdminUserBySessionToken({ sessionToken });

  return result.match(
    ({ user }) => {
      req.contextValues.set(kAdminUser, user);
      return next(req);
    },
    () => {
      throw new ConnectError('Unauthorized', Code.Unauthenticated);
    }
  );
};

export class UnauthorizedError extends Error {
  name = 'UnauthorizedError';
}

export const checkAdminUserExists = (ctx: HandlerContext) => {
  const user = ctx.values.get(kAdminUser);
  if (!user) {
    return err(new UnauthorizedError());
  }

  return ok(user);
};
