import crypto from 'crypto';
import { Code } from '@connectrpc/connect';
import { AdminUserFactory } from '@core-test/factories/admin_user_factory';
import { AdminUserSessionFactory } from '@core-test/factories/admin_user_session_factory';
import { adminUserAuthenticator } from './interceptors';
import { kAdminUser } from './user';

describe('adminUserAuthenticator', () => {
  it('sessionTokenが提供されていない場合はUnauthorizedエラーを投げる', async () => {
    // ===== Arrange =====
    const req = {
      header: {
        get: vi.fn().mockReturnValue(null),
      },
    };
    const next = vi.fn();

    // ===== Act & Assert =====
    await expect(adminUserAuthenticator(next)(req as any)).rejects.toThrow(
      expect.objectContaining({
        code: Code.Unauthenticated,
        rawMessage: 'Unauthorized',
      })
    );
    expect(next).not.toHaveBeenCalled();
  });

  it('有効なトークンの場合はユーザーをコンテキストに設定して次のハンドラーに進む', async () => {
    // ===== Arrange =====
    const adminUser = await AdminUserFactory.create();
    const session = await AdminUserSessionFactory.create({
      adminUser: {
        connect: {
          adminUserId: adminUser.adminUserId,
        },
      },
    });

    const req = {
      header: {
        get: vi.fn().mockReturnValue(session.sessionToken),
      },
      contextValues: {
        set: vi.fn(),
      },
    };
    const next = vi.fn().mockResolvedValue('next result');

    // ===== Act =====
    const result = await adminUserAuthenticator(next)(req as any);

    // ===== Assert =====
    expect(next).toHaveBeenCalledWith(req);
    expect(result).toBe('next result');
    expect(req.contextValues.set).toHaveBeenCalledWith(
      kAdminUser,
      expect.objectContaining({
        adminUserId: adminUser.adminUserId,
        email: adminUser.email,
      })
    );
  });

  it('無効なトークンの場合はUnauthorizedエラーを投げる', async () => {
    // ===== Arrange =====
    const req = {
      header: {
        get: vi.fn().mockReturnValue(crypto.randomUUID()),
      },
    };
    const next = vi.fn();

    // ===== Act & Assert =====
    await expect(adminUserAuthenticator(next)(req as any)).rejects.toThrow(
      expect.objectContaining({
        code: Code.Unauthenticated,
        rawMessage: 'Unauthorized',
      })
    );
    expect(next).not.toHaveBeenCalled();
  });

  it('トークンが存在するがadmin userが見つからない場合はエラーを返す', async () => {
    // ===== Arrange =====
    const sessionToken = crypto.randomUUID();

    // 一時的に外部キー制約を無効にしてセッションだけを作成
    const nonExistentAdminUserId = crypto.randomUUID();
    await vPrisma.client.$executeRaw`SET session_replication_role = replica;`;
    try {
      await vPrisma.client.$executeRaw`
        INSERT INTO "AdminUserSession" ("adminUserSessionId", "adminUserId", "sessionToken", "expiresAt", "createdAt", "updatedAt")
        VALUES (${crypto.randomUUID()}, ${nonExistentAdminUserId}, ${sessionToken}, ${new Date(Date.now() + 3600 * 1000)}, ${new Date()}, ${new Date()})
      `;
    } finally {
      await vPrisma.client.$executeRaw`SET session_replication_role = DEFAULT;`;
    }

    const req = {
      header: {
        get: vi.fn().mockReturnValue(sessionToken),
      },
    };
    const next = vi.fn();

    // ===== Act & Assert =====
    await expect(adminUserAuthenticator(next)(req as any)).rejects.toThrow(
      expect.objectContaining({
        code: Code.Unauthenticated,
        rawMessage: 'Unauthorized',
      })
    );
    expect(next).not.toHaveBeenCalled();
  });
});
