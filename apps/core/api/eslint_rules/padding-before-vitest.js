const rule = {
  meta: {
    type: 'layout',
    docs: {
      description: '`if (import.meta.vitest)` の直前に `// ===== Test =====` コメントを要求',
    },
    fixable: 'whitespace',
    schema: [],
    messages: {
      missing: '`// ===== Test =====` コメントを直前に追加してください。',
    },
  },

  create(context) {
    const sourceCode = context.getSourceCode();

    return {
      IfStatement(node) {
        // 対象が `if (import.meta.vitest)` か判定
        const t = node.test;
        const isVitest =
          t?.type === 'MemberExpression' &&
          t.object.type === 'MetaProperty' &&
          t.object.meta.name === 'import' &&
          t.object.property.name === 'meta' &&
          t.property.name === 'vitest';

        if (!isVitest) return;

        // 直前のコメントを取得
        const comments = sourceCode.getCommentsBefore(node);
        const last = comments.at(-1);

        const hasTestComment =
          last &&
          last.type === 'Line' &&
          last.value.trim() === '===== Test =====' &&
          last.loc.end.line === node.loc.start.line - 1;

        if (hasTestComment) return; // 既に OK

        context.report({
          node,
          messageId: 'missing',
          fix: (fixer) => {
            const indent = ' '.repeat(node.loc.start.column);
            return fixer.insertTextBefore(node, `${indent}// ===== Test =====\n`);
          },
        });
      },
    };
  },
};

export default rule;
