services:
  web-api:
    build:
      context: ../../../
      dockerfile: ./apps/web/api/docker/Dockerfile
      target: installer
    user: root
    volumes:
      - ../../../:/app
      - api_node_modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/hami
      - FRONTEND_ENDPOINT=http://localhost:3000
      - SMTP_HOST=mailpit
      - SMTP_PORT=1025
      - MAILPIT_URL=http://mailpit:8025
      - JWT_SECRET=dev-secret
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin
      - AWS_REGION=ap-northeast-1
      - S3_BUCKET_NAME=test-bucket
      - S3_ENDPOINT=http://minio:9000
      - S3_PUBLIC_ENDPOINT=https://minio.hami-${WORKTREE_NAME}.orb.local
      - NODE_TLS_REJECT_UNAUTHORIZED=0
      - GMO_PG_BASE_URL=https://kt01.mul-pay.jp
      - GMO_PG_SITE_ID=tsite00058016
      - GMO_PG_SITE_PASS=f5nzdyhf
      - GMO_PG_TIMEOUT=10000
      - GMO_PG_RETRY_COUNT=3
      - SENTRY_DSN=
      - SENTRY_ENABLED=false
    command: pnpm --filter @hami/web-api dev
    depends_on:
      - db
      - minio
    networks:
      - hami-network

  minio:
    image: minio/minio:latest
    entrypoint: /bin/sh
    command:
      - -c
      - |
        mkdir -p /data &&
        minio server /data --console-address ":9001" &
        MINIO_PID=$$! &&
        sleep 5 &&
        mc alias set myminio http://minio.hami-${WORKTREE_NAME}.orb.local minioadmin minioadmin &&
        mc mb myminio/test-bucket --ignore-existing &&
        mc anonymous set public myminio/test-bucket &&
        wait $$MINIO_PID
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
      - MINIO_BROWSER_REDIRECT_URL=http://minio.hami-${WORKTREE_NAME}.orb.local:9001
      - MINIO_API_CORS_ALLOW_ORIGIN=*
      - MINIO_API_CORS_ALLOW_METHODS=GET,PUT,POST,DELETE,OPTIONS
      - MINIO_API_CORS_ALLOW_HEADERS=*
      - MINIO_API_CORS_EXPOSE_HEADERS=ETag,Content-Length
      - MINIO_API_CORS_ALLOW_CREDENTIALS=off

    volumes:
      - minio-data:/data
    networks:
      - hami-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/ready']
      interval: 5s
      timeout: 5s
      retries: 5

networks:
  hami-network:
    external: true
    name: hami-${WORKTREE_NAME}-network

volumes:
  api_node_modules:
    driver: local
    name: api_node_modules_${WORKTREE_NAME}
  minio-data:
    driver: local
    name: minio_data_${WORKTREE_NAME}
