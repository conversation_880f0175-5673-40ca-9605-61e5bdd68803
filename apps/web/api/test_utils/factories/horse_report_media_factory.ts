import { defineHorseReportMediaFactory } from '@hami/prisma';
import { horseReportFactory } from './horse_report_factory';

export const horseReportMediaFactory = defineHorseReportMediaFactory({
  defaultData: ({ seq }) => ({
    horseReport: horseReportFactory,
    mediaFileUrl: `https://example.com/media/file${seq}.jpg`,
    thumbnailUrl: `https://example.com/media/file${seq}_thumb.jpg`,
    mediaType: 'image',
    displayOrder: seq,
  }),
});
