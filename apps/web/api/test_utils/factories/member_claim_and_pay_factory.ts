import { faker } from '@faker-js/faker/locale/ja';
import type { Prisma } from '@hami/prisma';
import { client as prismaClient } from '@web-api/utils/prisma';

export class MemberClaimAndPayFactory {
  static async create(data?: Partial<Prisma.MemberClaimAndPayCreateInput>) {
    const defaultData: Prisma.MemberClaimAndPayCreateInput = {
      member: {
        connect: {
          memberId: 1, // デフォルトのmemberId
        },
      },
      occurredDate: faker.date.past(),
      claimAmount: faker.number.int({ min: 1000, max: 100000 }),
      payAmount: faker.number.int({ min: 1000, max: 100000 }),
    };

    return await prismaClient.memberClaimAndPay.create({
      data: {
        ...defaultData,
        ...data,
      },
    });
  }

  static async createMany(count: number, data?: Partial<Prisma.MemberClaimAndPayCreateInput>) {
    const promises = Array.from({ length: count }, () => this.create(data));
    return await Promise.all(promises);
  }
}
