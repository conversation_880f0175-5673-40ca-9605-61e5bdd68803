import { PublishStatus } from '@hami/prisma';
import { defineHorseReportFactory } from '@hami/prisma';
import { horseFactory } from './horse_factory';

export const horseReportFactory = defineHorseReportFactory({
  defaultData: ({ seq }) => {
    const today = new Date();
    return {
      horse: horseFactory,
      reportYear: today.getFullYear(),
      reportMonth: today.getMonth() + 1,
      reportDay: today.getDate(),
      publishStatus: PublishStatus.DRAFT,
      location: '美浦トレーニングセンター',
      content: `テスト近況レポート${seq}`,
    } as const;
  },
});
