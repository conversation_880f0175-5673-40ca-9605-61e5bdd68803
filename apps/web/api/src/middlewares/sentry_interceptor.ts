import { Code, ConnectError, type Interceptor } from '@connectrpc/connect';
import * as Sentry from '@sentry/node';
import { kAdminUser, kUser } from './user';

export const sentryErrorReporter: Interceptor = (next) => async (req) => {
  try {
    return await next(req);
  } catch (err) {
    if (err instanceof ConnectError && err.code === Code.Internal) {
      const user = req.contextValues.get(kUser);
      const admin = req.contextValues.get(kAdminUser);

      Sentry.withScope((scope) => {
        scope.setTag('service', req.service.typeName);
        scope.setTag('method', req.method.name);
        scope.setTag('auth', admin ? 'admin' : user ? 'user' : 'none');
        if (admin?.adminUserId) {
          scope.setUser({ id: String(admin.adminUserId), segment: 'admin' });
        } else if (user?.userId) {
          scope.setUser({ id: String(user.userId), segment: 'user' });
        }
        Sentry.captureException(err);
      });
    }
    throw err;
  }
};
