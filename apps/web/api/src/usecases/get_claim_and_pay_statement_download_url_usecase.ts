import { ResultAsync } from 'neverthrow';
import { findMemberClaimAndPayByFileKeyAndMemberId } from '@web-api/repositories/member_claim_and_pay_repository';
import { getPresignedDownloadUrl } from '@web-api/utils/s3';

/**
 * PDFダウンロードURL取得結果の型
 */
interface GetDownloadUrlResult {
  downloadUrl: string;
}

/**
 * ファイルキーの検証
 */
const validateFileKey = (fileKey: string): boolean => {
  // statements/investment-and-return-*.pdf の形式をチェック
  const pattern = /^statements\/investment-and-return-.*\.pdf$/;
  return pattern.test(fileKey);
};

/**
 * 請求・支払い明細書PDFダウンロードURL取得usecase
 */
export const getClaimAndPayStatementDownloadUrlUsecase = (
  statementFileKey: string,
  memberId: number
): ResultAsync<GetDownloadUrlResult, Error> => {
  return ResultAsync.fromPromise(
    (async () => {
      // 1. ファイルキーの検証
      if (!validateFileKey(statementFileKey)) {
        throw new Error('Invalid file key format');
      }

      // 2. statementFileKeyとmemberIdでMemberClaimAndPayレコードを検索
      const memberClaimAndPay = await findMemberClaimAndPayByFileKeyAndMemberId(statementFileKey, memberId);
      
      if (memberClaimAndPay.isErr()) {
        throw new Error('Failed to find member claim and pay record');
      }

      if (!memberClaimAndPay.value) {
        throw new Error('Member claim and pay record not found');
      }

      // 3. S3署名付きURL生成（5分有効）
      const expiresIn = 300; // 5分
      const downloadUrl = await getPresignedDownloadUrl(statementFileKey, expiresIn);
      
      // 4. 結果返却
      return {
        downloadUrl,
      };
    })(),
    (error) => {
      console.error('Failed to generate download URL:', error);
      return error instanceof Error ? error : new Error('Unknown error occurred during URL generation');
    }
  );
};
