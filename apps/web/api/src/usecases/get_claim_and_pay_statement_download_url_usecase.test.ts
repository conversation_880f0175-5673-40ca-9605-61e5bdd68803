import { MemberClaimAndPayFactory } from '@web-test/factories/member_claim_and_pay_factory';
import { MemberFactory } from '@web-test/factories/member_factory';
import { getClaimAndPayStatementDownloadUrlUsecase } from './get_claim_and_pay_statement_download_url_usecase';

describe('get_claim_and_pay_statement_download_url_usecase', () => {

  describe('正常系', () => {
    it('有効なファイルキーとmemberIdでダウンロードURLを取得できる', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      
      // 請求・支払いレコードを作成
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
      });

      // ===== Act =====
      const result = await getClaimAndPayStatementDownloadUrlUsecase(
        'statements/investment-and-return-10001-20241.pdf',
        member.memberId
      );

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.downloadUrl).toBeDefined();
        expect(result.value.downloadUrl).toMatch(/^https:\/\/.*$/);
      }
    });
  });

  describe('異常系 - ファイルキー検証', () => {
    it('無効なファイルキーフォーマットの場合はエラーを返す', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const invalidFileKey = 'invalid-file-key.txt';

      // ===== Act =====
      const result = await getClaimAndPayStatementDownloadUrlUsecase(invalidFileKey, member.memberId);

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toBe('Invalid file key format');
      }
    });

    it('空のファイルキーの場合はエラーを返す', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();

      // ===== Act =====
      const result = await getClaimAndPayStatementDownloadUrlUsecase('', member.memberId);

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toBe('Invalid file key format');
      }
    });

    it('statements/で始まらないファイルキーの場合はエラーを返す', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const invalidFileKey = 'other/investment-and-return-10001-20241.pdf';

      // ===== Act =====
      const result = await getClaimAndPayStatementDownloadUrlUsecase(invalidFileKey, member.memberId);

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toBe('Invalid file key format');
      }
    });

    it('.pdfで終わらないファイルキーの場合はエラーを返す', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const invalidFileKey = 'statements/investment-and-return-10001-20241.txt';

      // ===== Act =====
      const result = await getClaimAndPayStatementDownloadUrlUsecase(invalidFileKey, member.memberId);

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toBe('Invalid file key format');
      }
    });
  });

  describe('異常系 - データベース', () => {
    it('存在しないレコードの場合はエラーを返す', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      
      // 異なるファイルキーのレコードを作成
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
      });

      // ===== Act =====
      const result = await getClaimAndPayStatementDownloadUrlUsecase(
        'statements/investment-and-return-10001-20242.pdf', // 存在しないファイルキー
        member.memberId
      );

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toBe('Member claim and pay record not found');
      }
    });

    it('存在しないmemberIdの場合はエラーを返す', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const nonExistentMemberId = 99999;
      
      // 存在するレコードを作成
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
      });

      // ===== Act =====
      const result = await getClaimAndPayStatementDownloadUrlUsecase(
        'statements/investment-and-return-10001-20241.pdf',
        nonExistentMemberId
      );

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toBe('Member claim and pay record not found');
      }
    });

    it('statementFileKeyがnullのレコードは取得されない', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      
      // statementFileKeyがnullのレコードのみを作成
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: null,
      });

      // ===== Act =====
      const result = await getClaimAndPayStatementDownloadUrlUsecase(
        'statements/investment-and-return-10001-20241.pdf',
        member.memberId
      );

      // ===== Assert =====
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toBe('Member claim and pay record not found');
      }
    });
  });

});
