import { checkFileExists, getPresignedDownloadUrl } from "./s3";

/**
 * 画像URLからファイルキーを抽出
 */
const extractFileKeyFromUrl = (imageUrl: string): string => {
  try {
    const url = new URL(imageUrl);
    // パスから先頭の '/' を除去してファイルキーとして使用
    return url.pathname.substring(1);
  } catch {
    // URLが無効な場合は、そのまま返す（後方互換性のため）
    return imageUrl;
  }
};

/**
 * 単一画像の署名付きURLを生成
 */
export const generateSignedUrlForImage = async <T extends { imageUrl: string }>(image: T) => {
  try {
    // S3にファイルが存在するかチェック
    const fileKey = extractFileKeyFromUrl(image.imageUrl);
    const fileExists = await checkFileExists(fileKey);
    if (!fileExists) {
      // ファイルが存在しない場合は元のURLを返す
      return {
        ...image,
        imageUrl: image.imageUrl,
      };
    }

    // 署名付きダウンロードURLを生成（1時間有効）
    const expiresIn = 60 * 60; // 1時間
    const signedUrl = await getPresignedDownloadUrl(fileKey, expiresIn);

    return {
      ...image,
      imageUrl: signedUrl,
    };
  } catch (error) {
    console.error('Failed to generate signed URL for image:', error);
    // エラーが発生した場合は元のURLを返す
    return {
      ...image,
      imageUrl: image.imageUrl,
    };
  }
};
