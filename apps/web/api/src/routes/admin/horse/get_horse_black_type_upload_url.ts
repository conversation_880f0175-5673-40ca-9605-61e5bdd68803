import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok, err, ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetHorseBlackTypeUploadUrlResponseSchema } from '@hami/admin-api-schema/horse_service_pb';
import { DatabaseError } from '@web-api/repositories/';
import { findHorseByIdForValidation, HorseNotFoundError } from '@web-api/repositories/horse_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { getPresignedUploadUrl } from '@web-api/utils/s3';
import { ValidationError } from '@web-api/utils/validate_request';

// 許可されるファイルタイプ
const ALLOWED_FILE_TYPES = ['application/pdf'] as const;
type AllowedFileType = (typeof ALLOWED_FILE_TYPES)[number];

// リクエストスキーマ
const schema = z.object({
  horseId: z.number().int().positive(),
  fileType: z.string().min(1),
  filename: z
    .string()
    .min(1)
    .max(255) // ファイル名の最大長制限
    .regex(
      /^[a-zA-Z0-9._-]+\.(pdf)$/i,
      'filenameは英数字、ピリオド、ハイフン、アンダースコアのみを含む有効なPDFファイル名である必要があります'
    )
    .refine(
      (filename) => !filename.includes('..') && !filename.startsWith('.') && !filename.includes('/'),
      'filenameにディレクトリトラバーサル文字列を含めることはできません'
    ),
});


/**
 * ファイルタイプの検証
 */
const isAllowedFileType = (fileType: string): fileType is AllowedFileType => {
  return ALLOWED_FILE_TYPES.some((allowedType) => allowedType === fileType);
};

const validateFileType = (fileType: string) => {
  if (!isAllowedFileType(fileType)) {
    return err(new ValidationError(`サポートされていないファイルタイプです: ${fileType}`));
  }
  return ok(fileType);
};

/**
 * ファイル名をサニタイズして安全にする
 */
const sanitizeFilename = (filename: string): string => {
  // パス区切り文字を削除してベースネームのみを取得
  const basename = filename.split('/').pop() || filename;

  // 安全な文字のみを残す（英数字、ピリオド、ハイフン、アンダースコア）
  const sanitized = basename.replace(/[^A-Za-z0-9._-]/g, '_');

  // 先頭がピリオドの場合は削除
  return sanitized.startsWith('.') ? sanitized.substring(1) : sanitized;
};

/**
 * 馬ブラックタイプ用のS3キーを生成
 */
const generateHorseImageKey = (horseId: number, filename: string): string => {
  const sanitizedFilename = sanitizeFilename(filename);
  return `horse-black-types/${horseId}/${Date.now()}_${sanitizedFilename}`;
};

/**
 * S3署名付きURLの生成
 */
const generatePresignedUrl = (horseId: number, filename: string, fileType: string) => {
  const key = generateHorseImageKey(horseId, filename);
  return ResultAsync.fromPromise(getPresignedUploadUrl(key, fileType), (error) => {
    console.error('Failed to generate presigned URL:', error);
    return new DatabaseError('S3署名付きURLの生成に失敗しました');
  }).map(({ url, key, expiresIn }) => ({ url, key, expiresIn }));
};

/**
 * ビジネスロジック
 */
const getHorseBlackTypeUploadUrlBusiness = ({ horseId, fileType, filename }: z.infer<typeof schema>) =>
  ok({ horseId, fileType, filename })
    .andThen(({ fileType }) => validateFileType(fileType).map(() => ({ horseId, fileType, filename })))
    .asyncAndThen(({ horseId, fileType, filename }) =>
      findHorseByIdForValidation({ horseId }).andThen(() => generatePresignedUrl(horseId, filename, fileType))
    );

export const getHorseBlackTypeUploadUrlHandler = createHandler({
  schema,
  business: getHorseBlackTypeUploadUrlBusiness,
  toResponse: ({ url, key, expiresIn }) =>
    create(GetHorseBlackTypeUploadUrlResponseSchema, {
      uploadUrl: url,
      fileKey: key,
      expiresIn,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseNotFoundError), () => new ConnectError('指定された馬が見つかりません', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
