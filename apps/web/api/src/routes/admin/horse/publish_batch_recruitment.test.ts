import { create } from '@bufbuild/protobuf';
import { horseFactory } from '@web-test/factories/horse_factory';
import { horseProfileFactory } from '@web-test/factories/horse_profile_factory';
import { RecruitmentStatus as ProtoRecruitmentStatus } from '@hami/admin-api-schema/common_enums_pb';
import { HorseService, BatchUpdateHorseRecruitmentStatusRequestSchema, GetHorseRequestSchema } from '@hami/admin-api-schema/horse_service_pb';
import { AdminUserSessionFactory } from '../../../../test_utils/factories/admin_user_session_factory';

describe('publish_batch_recruitment', () => {
  describe('batchUpdateHorseRecruitmentStatusHandler', () => {
    it('指定した複数馬の募集ステータスを一括で更新できる', async ({ getClient }) => {
      const adminUserSession = await AdminUserSessionFactory.create();

      const horseA = await horseFactory.create({ recruitmentYear: 2025 });
      await horseProfileFactory.create({ horse: { connect: { horseId: horseA.horseId } }, recruitmentStatus: (await import('@hami/prisma')).RecruitmentStatus.UPCOMING });

      const horseB = await horseFactory.create({ recruitmentYear: 2025 });
      await horseProfileFactory.create({ horse: { connect: { horseId: horseB.horseId } }, recruitmentStatus: (await import('@hami/prisma')).RecruitmentStatus.UPCOMING });

      const client = getClient(HorseService);
      const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

      const response = await client.batchUpdateHorseRecruitmentStatus(
        create(BatchUpdateHorseRecruitmentStatusRequestSchema, {
          horseIds: [horseA.horseId, horseB.horseId],
          recruitmentStatus: ProtoRecruitmentStatus.ACTIVE,
        }),
        { headers }
      );

      expect(response).toBeDefined();

      const updatedA = await client.getHorse(create(GetHorseRequestSchema, { horseId: horseA.horseId }), { headers });
      const updatedB = await client.getHorse(create(GetHorseRequestSchema, { horseId: horseB.horseId }), { headers });
      expect(updatedA.horse?.recruitmentStatus).toBe(ProtoRecruitmentStatus.ACTIVE);
      expect(updatedB.horse?.recruitmentStatus).toBe(ProtoRecruitmentStatus.ACTIVE);
    });

    it('horseIdsが空だとバリデーションエラー', async ({ getClient }) => {
      const adminUserSession = await AdminUserSessionFactory.create();
      const client = getClient(HorseService);
      const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

      await expect(async () => {
        await client.batchUpdateHorseRecruitmentStatus(
          create(BatchUpdateHorseRecruitmentStatusRequestSchema, {
            horseIds: [],
            recruitmentStatus: ProtoRecruitmentStatus.ACTIVE,
          }),
          { headers }
        );
      }).rejects.toThrow();
    });
  });
});


