import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok, ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetHorseFaceImageResponseSchema, HorseFaceImageSchema } from '@hami/admin-api-schema/horse_service_pb';
import { DatabaseError } from '@web-api/repositories';
import { findHorseByIdForValidation, getHorseFaceImage, HorseNotFoundError } from '@web-api/repositories/horse_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { generateSignedUrlForImage } from '@web-api/utils/horse_image';
import { ValidationError } from '@web-api/utils/validate_request';

const schema = z.object({
  horseId: z.number().int().positive(),
});

type GetHorseFaceImageInput = z.infer<typeof schema>;

const getHorseFaceImageBusiness = ({ horseId }: GetHorseFaceImageInput) =>
  findHorseByIdForValidation({ horseId }).andThen(() =>
    getHorseFaceImage({ horseId }).andThen((image) => {
      if (!image) {
        return ok(null);
      }
      return ResultAsync.fromPromise(
        generateSignedUrlForImage(image),
        (error) => new DatabaseError(`Failed to generate signed URL for horse face image: ${String(error)}`)
      );
    })
  );

export const getHorseFaceImageHandler = createHandler({
  schema,
  business: getHorseFaceImageBusiness,
  toResponse: (image) =>
    create(GetHorseFaceImageResponseSchema, {
      image: image
        ? create(HorseFaceImageSchema, {
            horseFaceImageId: image.horseFaceImageId,
            horseId: image.horseId,
            imageUrl: image.imageUrl,
            description: image.description ?? undefined,
            createdAt: image.createdAt.toISOString(),
            updatedAt: image.updatedAt.toISOString(),
          })
        : undefined,
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseNotFoundError), () => new ConnectError('指定された馬が見つかりません', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
