import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { UpdateHorseBlackTypeResponseSchema } from '@hami/admin-api-schema/horse_service_pb';
import { config } from '@web-api/config/environment';
import { DatabaseError } from '@web-api/repositories/';
import { HorseBlackTypeNotFoundError, HorseNotFoundError, updateHorseBlackType } from '@web-api/repositories/horse_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';

// リクエストスキーマ
const schema = z.object({
  horseId: z.number().int().positive(),
  fileKey: z
    .string()
    .min(1)
    .regex(/^.+\.(pdf)$/i, 'fileKeyは有効なPDFファイル（.pdf）である必要があります')
    .refine(
      (fileKey) => !fileKey.includes('..') && !fileKey.includes('//') && fileKey.length < 500,
      'fileKeyに不正な文字列を含めることはできません'
    )
    .optional(),
  description: z.string().optional(),
});

const sanitizeFileKey = (fileKey: string): string => {
  return encodeURIComponent(fileKey).replace(/%2F/g, '/');
};

const generatePdfUrl = (fileKey: string): string => {
  const sanitizedFileKey = sanitizeFileKey(fileKey);
  const baseUrl = config.s3.publicEndpoint || `https://${config.s3.bucketName}.s3.${config.s3.region}.amazonaws.com`;
  return `${baseUrl}/${sanitizedFileKey}`;
};

const updateHorseBlackTypeBusiness = ({ horseId, fileKey, description }: z.infer<typeof schema>) => {
  const pdfUrl = fileKey ? generatePdfUrl(fileKey) : undefined;
  return ok({ horseId, pdfUrl, description }).asyncAndThen(({ horseId, pdfUrl, description }) => updateHorseBlackType({ horseId, pdfUrl, description }));
};

export const updateHorseBlackTypeHandler = createHandler({
  schema,
  business: updateHorseBlackTypeBusiness,
  toResponse: () => create(UpdateHorseBlackTypeResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseNotFoundError), () => new ConnectError('指定された馬が見つかりません', Code.NotFound))
      .with(P.instanceOf(HorseBlackTypeNotFoundError), () => new ConnectError('ブラックタイプが見つかりません', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
