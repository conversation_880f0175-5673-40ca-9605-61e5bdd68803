import { AdminUserSessionFactory } from '@web-test/factories/admin_user_session_factory';
import { horseFactory } from '@web-test/factories/horse_factory';
import { HorseService } from '@hami/admin-api-schema/horse_service_pb';

// 既存ブラックタイプ作成用のヘルパ（テスト用）
const createInitialBlackType = async (horseId: number) => {
  return vPrisma.client.horseBlackType.create({
    data: {
      horse: { connect: { horseId } },
      pdfUrl: `https://example.com/horse-black-types/${horseId}/initial.pdf`,
      description: '初期説明',
    },
  });
};

describe('updateHorseBlackType', () => {
  it('PDFと説明を更新できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();
    await createInitialBlackType(horse.horseId);

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    const request = {
      horseId: horse.horseId,
      fileKey: `horse-black-types/${horse.horseId}/updated.pdf`,
      description: '更新後の説明',
    };

    // ===== Act =====
    await client.updateHorseBlackType(request, { headers });

    // ===== Assert =====
    const bt = await vPrisma.client.horseBlackType.findFirst({ where: { horseId: horse.horseId } });
    expect(bt).not.toBeNull();
    expect(bt!.pdfUrl).toContain('updated.pdf');
    expect(bt!.description).toBe('更新後の説明');
  });

  it('説明のみを更新できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();
    await createInitialBlackType(horse.horseId);

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    const request = {
      horseId: horse.horseId,
      description: '説明のみ更新',
    };

    // ===== Act =====
    await client.updateHorseBlackType(request, { headers });

    // ===== Assert =====
    const bt = await vPrisma.client.horseBlackType.findFirst({ where: { horseId: horse.horseId } });
    expect(bt).not.toBeNull();
    expect(bt!.pdfUrl).toContain('initial.pdf'); // 変更されていない
    expect(bt!.description).toBe('説明のみ更新');
  });

  it('存在しない馬IDでリクエストした場合、NotFoundエラーが返される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    const request = {
      horseId: 999999,
      description: '更新',
    };

    // ===== Act & Assert =====
    await expect(client.updateHorseBlackType(request, { headers })).rejects.toThrow('指定された馬が見つかりません');
  });

  it('ブラックタイプが存在しない場合、NotFoundエラーが返される', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    const request = {
      horseId: horse.horseId,
      description: '更新',
    };

    // ===== Act & Assert =====
    await expect(client.updateHorseBlackType(request, { headers })).rejects.toThrow('ブラックタイプが見つかりません');
  });
});

