import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetHorseImageDownloadUrlResponseSchema } from '@hami/admin-api-schema/horse_service_pb';
import { DatabaseError } from '@web-api/repositories';
import { createHandler } from '@web-api/utils/handler_factory';
import { getPresignedDownloadUrl } from '@web-api/utils/s3';
import { ValidationError } from '@web-api/utils/validate_request';

const schema = z.object({
  fileKey: z
    .string()
    .min(1)
    .regex(/^.+\.(jpe?g|png|gif)$/i, 'fileKeyは有効な画像ファイル（.jpg, .jpeg, .png, .gif）である必要があります')
    .refine(
      (fileKey) => !fileKey.includes('..') && !fileKey.includes('//') && fileKey.length < 500,
      'fileKeyに不正な文字列を含めることはできません'
    ),
});

type Input = z.infer<typeof schema>;

const business = ({ fileKey }: Input) =>
  ResultAsync.fromPromise(getPresignedDownloadUrl(fileKey, 60 * 60), (error) => {
    console.error('Failed to generate presigned download URL:', error);
    return new DatabaseError('S3署名付きURLの生成に失敗しました');
  }).map((downloadUrl) => ({ downloadUrl, expiresIn: 60 * 60 }));

export const getHorseImageDownloadUrlHandler = createHandler({
  schema,
  business,
  toResponse: ({ downloadUrl, expiresIn }) =>
    create(GetHorseImageDownloadUrlResponseSchema, { downloadUrl, expiresIn }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});

