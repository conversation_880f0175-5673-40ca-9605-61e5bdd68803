import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok, ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  UpsertHorseFaceImageResponseSchema,
  HorseFaceImageSchema,
} from '@hami/admin-api-schema/horse_service_pb';
import { config } from '@web-api/config/environment';
import { DatabaseError } from '@web-api/repositories';
import { upsertHorseFaceImage, HorseNotFoundError } from '@web-api/repositories/horse_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { generateSignedUrlForImage } from '@web-api/utils/horse_image';
import { ValidationError } from '@web-api/utils/validate_request';

const schema = z.object({
  horseId: z.number().int().positive(),
  fileKey: z
    .string()
    .min(1)
    .regex(/^.+\.(jpe?g|png|gif)$/i, 'fileKeyは有効な画像ファイル（.jpg, .jpeg, .png, .gif）である必要があります')
    .refine(
      (fileKey) => !fileKey.includes('..') && !fileKey.includes('//') && fileKey.length < 500,
      'fileKeyに不正な文字列を含めることはできません'
    ),
  description: z.string().optional(),
});

type UpsertHorseFaceImageInput = z.infer<typeof schema>;

const sanitizeFileKey = (fileKey: string): string => {
  return encodeURIComponent(fileKey).replace(/%2F/g, '/');
};

const generateImageUrl = (fileKey: string): string => {
  const sanitized = sanitizeFileKey(fileKey);
  const baseUrl = config.s3.publicEndpoint || `https://${config.s3.bucketName}.s3.${config.s3.region}.amazonaws.com`;
  return `${baseUrl}/${sanitized}`;
};

const upsertHorseFaceImageBusiness = ({ horseId, fileKey, description }: UpsertHorseFaceImageInput) => {
  const imageUrl = generateImageUrl(fileKey);

  return ok({ horseId, imageUrl, description })
    .asyncAndThen(({ horseId, imageUrl, description }) => upsertHorseFaceImage({ horseId, imageUrl, description }))
    .andThen((image) =>
      ResultAsync.fromPromise(
        generateSignedUrlForImage(image),
        (error) => new DatabaseError(`Failed to generate signed URL for horse face image: ${String(error)}`)
      )
    );
};

export const upsertHorseFaceImageHandler = createHandler({
  schema,
  business: upsertHorseFaceImageBusiness,
  toResponse: (image) =>
    create(UpsertHorseFaceImageResponseSchema, {
      image: create(HorseFaceImageSchema, {
        horseFaceImageId: image.horseFaceImageId,
        horseId: image.horseId,
        imageUrl: image.imageUrl,
        description: image.description ?? undefined,
        createdAt: image.createdAt.toISOString(),
        updatedAt: image.updatedAt.toISOString(),
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseNotFoundError), () => new ConnectError('指定された馬が見つかりません', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
