import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetHorseBlackTypeResponseSchema } from '@hami/admin-api-schema/horse_service_pb';
import type { HorseBlackType as PrismaHorseBlackType } from '@hami/prisma';
import { DatabaseError } from '@web-api/repositories/';
import { findHorseById, HorseNotFoundError } from '@web-api/repositories/horse_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { generateSignedUrlForBlackType } from '@web-api/utils/horse_black_type';

class BlackTypeNotFoundError extends Error {
  readonly name = 'BlackTypeNotFoundError';
}

const schema = z.object({
  horseId: z.number().int().positive(),
});

const business = ({ horseId }: z.infer<typeof schema>) =>
  findHorseById({ horseId }).andThen((horse) => {
    if (!horse) {
      return ResultAsync.fromSafePromise(Promise.reject(new HorseNotFoundError()));
    }
    if (!horse.blackType) {
      return ResultAsync.fromSafePromise(Promise.reject(new BlackTypeNotFoundError()));
    }
    return ResultAsync.fromPromise(
      generateSignedUrlForBlackType(horse.blackType),
      (e) => new DatabaseError('Failed to generate signed URL', { cause: e })
    );
  });

export const getHorseBlackTypeHandler = createHandler({
  schema,
  business,
  toResponse: (blackType: PrismaHorseBlackType) =>
    create(GetHorseBlackTypeResponseSchema, {
      blackType: {
        horseId: blackType.horseId,
        blackTypeUrl: blackType.pdfUrl,
        description: blackType.description ?? undefined,
        createdAt: blackType.createdAt.toISOString(),
        updatedAt: blackType.updatedAt.toISOString(),
      },
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(BlackTypeNotFoundError), () => new ConnectError('ブラックタイプが見つかりません', Code.NotFound))
      .with(P.instanceOf(HorseNotFoundError), () => new ConnectError('指定された馬が見つかりません', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
