import { AdminUserSessionFactory } from '@web-test/factories/admin_user_session_factory';
import { horseFactory } from '@web-test/factories/horse_factory';
import { HorseService } from '@hami/admin-api-schema/horse_service_pb';

/**
 * getHorseBlackTypeUploadUrl のテスト
 * - 正常系: PDFの署名付きURLが返る
 * - 異常系: 不正な馬ID
 * - 異常系: サポート外ファイルタイプ
 */
describe('getHorseBlackTypeUploadUrl', () => {
  it('正常なブラックタイプ(PDF)アップロードURL取得リクエストが成功する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    const request = {
      horseId: horse.horseId,
      fileType: 'application/pdf',
      filename: 'test-blacktype.pdf',
    };

    // ===== Act =====
    const response = await client.getHorseBlackTypeUploadUrl(request, { headers });

    // ===== Assert =====
    expect(response.uploadUrl).toContain('horse-black-types');
    expect(response.uploadUrl).toContain(horse.horseId.toString());
    expect(response.fileKey).toContain('horse-black-types');
    expect(response.fileKey).toContain(horse.horseId.toString());
    expect(response.fileKey).toContain('test-blacktype.pdf');
    expect(response.expiresIn).toBe(300); // 5分
  });

  it('存在しない馬IDでリクエストするとエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    const request = {
      horseId: 99999,
      fileType: 'application/pdf',
      filename: 'test-blacktype.pdf',
    };

    // ===== Act & Assert =====
    await expect(client.getHorseBlackTypeUploadUrl(request, { headers })).rejects.toThrow();
  });

  it('サポートされていないファイルタイプでリクエストするとエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({
      sessionToken: adminUserSession.sessionToken,
    });

    const request = {
      horseId: horse.horseId,
      fileType: 'image/jpeg',
      filename: 'test-blacktype.pdf',
    };

    // ===== Act & Assert =====
    await expect(client.getHorseBlackTypeUploadUrl(request, { headers })).rejects.toThrow();
  });
});
