import { AdminUserSessionFactory } from '@web-test/factories/admin_user_session_factory';
import { horseFactory } from '@web-test/factories/horse_factory';
import { horseFaceImageFactory } from '@web-test/factories/horse_face_image_factory';
import { HorseService } from '@hami/admin-api-schema/horse_service_pb';

vi.mock('@web-api/utils/s3', () => ({
  checkFileExists: vi.fn().mockResolvedValue(false),
  getPresignedDownloadUrl: vi.fn(),
}));

describe('upsertHorseFaceImage', () => {
  it('顔写真を新規登録できる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    const request = {
      horseId: horse.horseId,
      fileKey: `horse-face-images/${horse.horseId}/face-test.jpg`,
      description: 'テスト用の顔写真',
    };

    const response = await client.upsertHorseFaceImage(request, { headers });

    expect(response.image).toBeDefined();
    expect(response.image?.imageUrl).toContain('face-test.jpg');
    expect(response.image?.horseId).toBe(horse.horseId);

    const saved = await vPrisma.client.horseFaceImage.findUnique({
      where: { horseId: horse.horseId },
    });
    expect(saved).not.toBeNull();
    expect(saved?.description).toBe('テスト用の顔写真');
  });

  it('既存の顔写真を上書きできる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();
    const existing = await horseFaceImageFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      imageUrl: 'https://example.com/horse-face-images/old-face.jpg',
      description: '古い顔写真',
    });

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    const request = {
      horseId: horse.horseId,
      fileKey: `horse-face-images/${horse.horseId}/new-face.jpg`,
      description: '新しい顔写真',
    };

    const response = await client.upsertHorseFaceImage(request, { headers });

    expect(response.image?.horseFaceImageId).toBe(existing.horseFaceImageId);
    expect(response.image?.imageUrl).toContain('new-face.jpg');
    expect(response.image?.description).toBe('新しい顔写真');

    const updated = await vPrisma.client.horseFaceImage.findUnique({
      where: { horseFaceImageId: existing.horseFaceImageId },
    });
    expect(updated?.imageUrl).toContain('new-face.jpg');
    expect(updated?.description).toBe('新しい顔写真');
  });

  it('存在しない馬IDの場合はエラーになる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    await expect(
      client.upsertHorseFaceImage(
        {
          horseId: 999999,
          fileKey: 'horse-face-images/999999/new-face.jpg',
        },
        { headers }
      )
    ).rejects.toThrow('指定された馬が見つかりません');
  });
});
