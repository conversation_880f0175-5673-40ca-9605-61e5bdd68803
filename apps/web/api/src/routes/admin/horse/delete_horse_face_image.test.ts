import { AdminUserSessionFactory } from '@web-test/factories/admin_user_session_factory';
import { horseFactory } from '@web-test/factories/horse_factory';
import { horseFaceImageFactory } from '@web-test/factories/horse_face_image_factory';
import { HorseService } from '@hami/admin-api-schema/horse_service_pb';

vi.mock('@web-api/utils/s3', () => ({
  deleteFromS3: vi.fn().mockResolvedValue(true),
}));

describe('deleteHorseFaceImage', () => {
  it('顔写真を削除できる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const faceImage = await horseFaceImageFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    await client.deleteHorseFaceImage({ horseId: faceImage.horseId }, { headers });

    const deleted = await vPrisma.client.horseFaceImage.findUnique({
      where: { horseFaceImageId: faceImage.horseFaceImageId },
    });
    expect(deleted).toBeNull();
  });

  it('顔写真が存在しない場合はエラーになる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    await expect(
      client.deleteHorseFaceImage({ horseId: horse.horseId }, { headers })
    ).rejects.toThrow('顔写真が見つかりません');
  });

  it('存在しない馬IDの場合はエラーになる', async ({ getClient }) => {
    const adminUserSession = await AdminUserSessionFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    await expect(
      client.deleteHorseFaceImage({ horseId: 999999 }, { headers })
    ).rejects.toThrow('指定された馬が見つかりません');
  });
});
