import { AdminUserSessionFactory } from '@web-test/factories/admin_user_session_factory';
import { horseFactory } from '@web-test/factories/horse_factory';
import { HorseService } from '@hami/admin-api-schema/horse_service_pb';

// S3 依存を避けるため、存在確認は常に false を返す（元URLをそのまま返す分岐を通す）
vi.mock('@web-api/utils/s3', async () => {
  const viImport = await import('vitest');
  return {
    checkFileExists: viImport.vi.fn().mockResolvedValue(false),
    getPresignedDownloadUrl: viImport.vi.fn().mockResolvedValue('https://signed.example.com/dummy'),
  };
});

describe('getHorseBlackType', () => {
  it('ブラックタイプが存在する馬の情報を取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();

    // ブラックタイプを作成
    await vPrisma.client.horseBlackType.create({
      data: {
        horse: { connect: { horseId: horse.horseId } },
        pdfUrl: `https://example.com/horse-black-types/${horse.horseId}/bt.pdf`,
        description: 'サンプルのブラックタイプPDF',
      },
    });

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    // ===== Act =====
    const res = await client.getHorseBlackType({ horseId: horse.horseId }, { headers });

    // ===== Assert =====
    expect(res.blackType).toBeDefined();
    const bt = res.blackType!;
    expect(bt.horseId).toBe(horse.horseId);
    expect(bt.blackTypeUrl).toBe(`https://example.com/horse-black-types/${horse.horseId}/bt.pdf`);
    expect(bt.description).toBe('サンプルのブラックタイプPDF');
    // createdAt / updatedAt はISO文字列
    expect(typeof bt.createdAt).toBe('string');
    expect(typeof bt.updatedAt).toBe('string');
    expect(() => new Date(bt.createdAt)).not.toThrow();
    expect(() => new Date(bt.updatedAt)).not.toThrow();
  });

  it('ブラックタイプが存在しない場合はエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    // ===== Act & Assert =====
    await expect(
      client.getHorseBlackType({ horseId: horse.horseId }, { headers })
    ).rejects.toThrow();
  });

  it('存在しない馬IDを指定するとエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    // ===== Act & Assert =====
    await expect(
      client.getHorseBlackType({ horseId: 999999 }, { headers })
    ).rejects.toThrow();
  });

  it('無効なhorseId（0）でエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    // ===== Act & Assert =====
    await expect(
      client.getHorseBlackType({ horseId: 0 }, { headers })
    ).rejects.toThrow();
  });

  it('無効なhorseId（負の数）でエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    // ===== Act & Assert =====
    await expect(
      client.getHorseBlackType({ horseId: -1 }, { headers })
    ).rejects.toThrow();
  });
});
