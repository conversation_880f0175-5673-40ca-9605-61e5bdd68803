import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok, ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { DeleteHorseFaceImageResponseSchema } from '@hami/admin-api-schema/horse_service_pb';
import { DatabaseError } from '@web-api/repositories';
import {
  deleteHorseFaceImage,
  HorseNotFoundError,
  HorseFaceImageNotFoundError,
} from '@web-api/repositories/horse_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { deleteFromS3 } from '@web-api/utils/s3';
import { ValidationError } from '@web-api/utils/validate_request';

const schema = z.object({
  horseId: z.number().int().positive(),
});

type DeleteHorseFaceImageInput = z.infer<typeof schema>;

const extractS3KeyFromUrl = (imageUrl: string): string | null => {
  try {
    const url = new URL(imageUrl);
    return url.pathname.substring(1);
  } catch {
    return null;
  }
};

const deleteImageFromS3 = (imageUrl: string) => {
  const key = extractS3KeyFromUrl(imageUrl);
  if (!key) {
    return ResultAsync.fromSafePromise(Promise.resolve(false));
  }

  return ResultAsync.fromPromise(deleteFromS3(key), (error) => {
    console.error('Failed to delete face image from S3:', error);
    return new DatabaseError('S3ファイル削除に失敗しましたが、データベースの削除は完了しました');
  }).orElse(() => ok(false));
};

const deleteHorseFaceImageBusiness = ({ horseId }: DeleteHorseFaceImageInput) =>
  ok(horseId)
    .asyncAndThen((id) => deleteHorseFaceImage({ horseId: id }))
    .andThen((image) => deleteImageFromS3(image.imageUrl).andThen(() => ok(image)));

export const deleteHorseFaceImageHandler = createHandler({
  schema,
  business: deleteHorseFaceImageBusiness,
  toResponse: () => create(DeleteHorseFaceImageResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(HorseNotFoundError), () => new ConnectError('指定された馬が見つかりません', Code.NotFound))
      .with(P.instanceOf(HorseFaceImageNotFoundError), () => new ConnectError('顔写真が見つかりません', Code.NotFound))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
