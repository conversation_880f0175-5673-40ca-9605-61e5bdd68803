import { create } from '@bufbuild/protobuf';
import { horseFactory } from '@web-test/factories/horse_factory';
import { horseProfileFactory } from '@web-test/factories/horse_profile_factory';
import { PublishStatus as ProtoPublishStatus } from '@hami/admin-api-schema/common_enums_pb';
import { HorseService, BatchUpdateHorsePublishStatusRequestSchema, GetHorseRequestSchema } from '@hami/admin-api-schema/horse_service_pb';
import { getClient } from '../../../../test_utils';
import { AdminUserSessionFactory } from '../../../../test_utils/factories/admin_user_session_factory';

describe('publish_batch', () => {
  describe('batchUpdateHorsePublishStatusHandler', () => {
    it('指定した複数馬の公開ステータスを一括で更新できる', async () => {
      const adminUserSession = await AdminUserSessionFactory.create();

      const horseA = await horseFactory.create({ recruitmentYear: 2025 });
      await horseProfileFactory.create({ horse: { connect: { horseId: horseA.horseId } }, publishStatus: (await import('@hami/prisma')).PublishStatus.DRAFT });

      const horseB = await horseFactory.create({ recruitmentYear: 2025 });
      await horseProfileFactory.create({ horse: { connect: { horseId: horseB.horseId } }, publishStatus: (await import('@hami/prisma')).PublishStatus.DRAFT });

      const client = getClient(HorseService);
      const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

      const response = await client.batchUpdateHorsePublishStatus(
        create(BatchUpdateHorsePublishStatusRequestSchema, {
          horseIds: [horseA.horseId, horseB.horseId],
          publishStatus: ProtoPublishStatus.PUBLISHED,
        }),
        { headers }
      );

      expect(response).toBeDefined();

      const updatedA = await client.getHorse(create(GetHorseRequestSchema, { horseId: horseA.horseId }), { headers });
      const updatedB = await client.getHorse(create(GetHorseRequestSchema, { horseId: horseB.horseId }), { headers });
      expect(updatedA.horse?.publishStatus).toBe(ProtoPublishStatus.PUBLISHED);
      expect(updatedB.horse?.publishStatus).toBe(ProtoPublishStatus.PUBLISHED);
    });

    it('horseIdsが空だとバリデーションエラー', async () => {
      const adminUserSession = await AdminUserSessionFactory.create();
      const client = getClient(HorseService);
      const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

      await expect(async () => {
        await client.batchUpdateHorsePublishStatus(
          create(BatchUpdateHorsePublishStatusRequestSchema, {
            horseIds: [],
            publishStatus: ProtoPublishStatus.PUBLISHED,
          }),
          { headers }
        );
      }).rejects.toThrow();
    });
  });
});


