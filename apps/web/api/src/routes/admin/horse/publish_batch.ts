import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { BatchUpdateHorsePublishStatusResponseSchema, BatchUpdateHorseRecruitmentStatusResponseSchema } from '@hami/admin-api-schema/horse_service_pb';
import { DatabaseError } from '@web-api/repositories';
import { batchUpdateHorsePublishStatus, batchUpdateHorseRecruitmentStatus } from '@web-api/repositories/horse_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';
import { convertProtoPublishStatusToPrismaPublishStatus, convertProtoRecruitmentStatusToPrismaRecruitmentStatus } from './utils/convert_utils';

const schema = z.object({
  horseIds: z.array(z.number().int().positive()).min(1),
  publishStatus: z.number().int().min(0),
});

type RequestType = z.infer<typeof schema>;

function business({ horseIds, publishStatus }: RequestType) {
  const prismaStatus = convertProtoPublishStatusToPrismaPublishStatus(publishStatus);
  if (prismaStatus === null) {
    throw new ValidationError('invalid publish status');
  }
  return batchUpdateHorsePublishStatus(horseIds, prismaStatus);
}

export const batchUpdateHorsePublishStatusHandler = createHandler({
  schema,
  business: ({ horseIds, publishStatus }) => business({ horseIds, publishStatus }),
  toResponse: () => create(BatchUpdateHorsePublishStatusResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});

// 募集ステータス一括更新
const recruitmentSchema = z.object({
  horseIds: z.array(z.number().int().positive()).min(1),
  recruitmentStatus: z.number().int().min(0),
});

type RecruitmentRequestType = z.infer<typeof recruitmentSchema>;

function recruitmentBusiness({ horseIds, recruitmentStatus }: RecruitmentRequestType) {
  // 0: UNSPECIFIEDも来る可能性があるが、convert側はnumberでも動く
  const prismaStatus = convertProtoRecruitmentStatusToPrismaRecruitmentStatus(recruitmentStatus);
  if (prismaStatus === null) {
    throw new ValidationError('invalid recruitment status');
  }
  return batchUpdateHorseRecruitmentStatus(horseIds, prismaStatus);
}

export const batchUpdateHorseRecruitmentStatusHandler = createHandler({
  schema: recruitmentSchema,
  business: ({ horseIds, recruitmentStatus }) => recruitmentBusiness({ horseIds, recruitmentStatus }),
  toResponse: () => create(BatchUpdateHorseRecruitmentStatusResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});


