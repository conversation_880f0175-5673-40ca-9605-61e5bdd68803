import { AdminUserSessionFactory } from '@web-test/factories/admin_user_session_factory';
import { horseFactory } from '@web-test/factories/horse_factory';
import { HorseService } from '@hami/admin-api-schema/horse_service_pb';

describe('createHorseBlackType', () => {
  it('正常なブラックタイプ作成リクエストが成功する', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const horse = await horseFactory.create();

    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    const fileKey = `horse-black-types/${horse.horseId}/1234567890_blacktype.pdf`;
    const request = { horseId: horse.horseId, fileKey };

    // ===== Act =====
    await client.createHorseBlackType(request, { headers });

    // ===== Assert =====
    const bt = await vPrisma.client.horseBlackType.findFirst({ where: { horseId: horse.horseId } });
    expect(bt).not.toBeNull();
    expect(bt!.pdfUrl).toContain('blacktype.pdf');
  });

  it('存在しない馬IDでリクエストするとエラーになる', async ({ getClient }) => {
    // ===== Arrange =====
    const adminUserSession = await AdminUserSessionFactory.create();
    const client = getClient(HorseService);
    const headers = new Headers({ sessionToken: adminUserSession.sessionToken });

    const request = { horseId: 999999, fileKey: 'horse-black-types/999999/blacktype.pdf' };

    // ===== Act & Assert =====
    await expect(client.createHorseBlackType(request, { headers })).rejects.toThrow('指定された馬が見つかりません');
  });
});

