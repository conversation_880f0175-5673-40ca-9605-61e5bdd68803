import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { PublishStatus } from '@hami/admin-api-schema/common_enums_pb';
import { ListHorseVideosResponseSchema } from '@hami/admin-api-schema/horse_video_service_pb';
import { HorseListItemSchema } from '@hami/admin-api-schema/models/horse_pb';
import { HorseVideoItemSchema } from '@hami/admin-api-schema/models/horse_video_pb';
import { DatabaseError } from '@web-api/repositories';
import { listHorseVideos, ListHorseVideosParams, ListHorseVideosResult } from '@web-api/repositories/horse_video_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';
import {
  convertPrismaPublishStatusToProtoPublishStatus,
  convertProtoPublishStatusToPrismaPublishStatus,
} from '../horse_report/utils/convert_utils';

const listHorseVideosSchema = z.object({
  horseId: z.number().int().positive().optional(),
  recruitmentYear: z.number().int().min(1900).max(2100).optional(),
  recruitmentNo: z.number().int().min(1).optional(),
  publishStatus: z.nativeEnum(PublishStatus).optional(),
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().max(100).default(20),
  sortBy: z.string().default('video_date_desc'),
});

type ListHorseVideosRequestType = z.infer<typeof listHorseVideosSchema>;

function business(req: ListHorseVideosRequestType) {
  const params: ListHorseVideosParams = {
    horseId: req.horseId,
    recruitmentYear: req.recruitmentYear,
    recruitmentNo: req.recruitmentNo,
    publishStatus: req.publishStatus !== undefined ? convertProtoPublishStatusToPrismaPublishStatus(req.publishStatus) : undefined,
    page: req.page,
    pageSize: req.pageSize,
    sortBy: req.sortBy,
  };
  return listHorseVideos(params);
}

export const listHorseVideosHandler = createHandler({
  schema: listHorseVideosSchema,
  business,
  toResponse: (result: ListHorseVideosResult) => {
    return create(ListHorseVideosResponseSchema, {
      videos: result.videos.map((v) =>
        create(HorseVideoItemSchema, {
          horseVideoId: v.horseVideoId,
          horseId: v.horseId,
          horseName: v.horseName,
          videoYear: v.videoYear,
          videoMonth: v.videoMonth,
          videoDay: v.videoDay,
          publishStatus: convertPrismaPublishStatusToProtoPublishStatus(v.publishStatus),
          title: v.title,
          description: v.description ?? undefined,
          youtubeVideoId: v.youtubeVideoId,
          startAtSeconds: v.startAtSeconds ?? undefined,
          thumbnailImagePath: v.thumbnailImagePath ?? undefined,
          horse: create(HorseListItemSchema, {
            horseId: v.horseId,
            recruitmentYear: v.horse?.recruitmentYear ?? 0,
            recruitmentNo: v.horse?.recruitmentNo ?? 0,
            recruitmentName: v.horse?.recruitmentName ?? '',
            horseName: v.horse?.horseName ?? v.horseName,
            birthYear: v.horse?.birthYear ?? 0,
          }),
        })
      ),
      totalCount: result.totalCount,
      currentPage: result.currentPage,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPreviousPage: result.hasPreviousPage,
    });
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e: ValidationError) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
