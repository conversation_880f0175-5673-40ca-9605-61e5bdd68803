import { create } from '@bufbuild/protobuf';
import { ConnectError } from '@connectrpc/connect';
import { getClient } from '@web-test/index';
import { horseFactory } from '@web-test/factories/horse_factory';
import { HorseVideoService, CreateHorseVideoRequestSchema } from '@hami/admin-api-schema/horse_video_service_pb';
import { PublishStatus as PrismaPublishStatus } from '@hami/prisma';

// Prismaクライアントの直接インポートは禁止。検証には vPrisma.client を使用。

describe('createHorseVideo API', () => {
  const api = getClient(HorseVideoService);

  it('動画を正常に作成できる', async () => {
    // Arrange
    const horse = await horseFactory.create();
    const req = create(CreateHorseVideoRequestSchema, {
      horseId: horse.horseId,
      videoYear: 2025,
      videoMonth: 9,
      videoDay: 1,
      title: '調教動画 1',
      description: '坂路での追い切り',
      youtubeVideoId: 'yt_abc123',
      startAtSeconds: 10,
    });

    // Act
    const res = await api.createHorseVideo(req);

    // Assert
    expect(res.video?.horseId).toBe(horse.horseId);
    expect(res.video?.title).toBe('調教動画 1');

    const saved = await vPrisma.client.horseVideo.findFirst({
      where: { horseId: horse.horseId, youtubeVideoId: 'yt_abc123' },
    });
    expect(saved).toBeTruthy();
    expect(saved?.publishStatus).toBe(PrismaPublishStatus.DRAFT);
  });

  it('存在しない馬IDならエラーになる', async () => {
    const req = create(CreateHorseVideoRequestSchema, {
      horseId: 999999,
      videoYear: 2025,
      videoMonth: 9,
      videoDay: 1,
      title: 'NG',
      youtubeVideoId: 'x',
    });
    await expect(api.createHorseVideo(req)).rejects.toThrow(ConnectError);
  });
});

