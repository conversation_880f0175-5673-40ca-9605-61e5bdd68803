import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { DeleteHorseVideoResponseSchema } from '@hami/admin-api-schema/horse_video_service_pb';
import { DatabaseError } from '@web-api/repositories';
import { deleteHorseVideo, HorseVideoNotFoundError } from '@web-api/repositories/horse_video_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';

const schema = z.object({ horseVideoId: z.number().int().positive() });

type Req = z.infer<typeof schema>;

export const deleteHorseVideoHandler = createHandler({
  schema,
  business: (req: Req) => deleteHorseVideo(req.horseVideoId),
  toResponse: () => create(DeleteHorseVideoResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e: ValidationError) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), (e: DatabaseError) => new ConnectError(e.message, Code.Internal))
      .with(P.instanceOf(HorseVideoNotFoundError), (e: HorseVideoNotFoundError) => new ConnectError(e.message, Code.NotFound))
      .exhaustive(),
});
