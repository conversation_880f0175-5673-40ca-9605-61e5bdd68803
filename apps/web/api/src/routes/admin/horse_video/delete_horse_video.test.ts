import { create } from '@bufbuild/protobuf';
import { getClient } from '@web-test/index';
import { horseFactory } from '@web-test/factories/horse_factory';
import { HorseVideoService, DeleteHorseVideoRequestSchema } from '@hami/admin-api-schema/horse_video_service_pb';

// 削除APIの基本動作を確認

describe('deleteHorseVideo API', () => {
  const api = getClient(HorseVideoService);

  it('論理削除できる', async () => {
    const horse = await horseFactory.create();
    const v = await vPrisma.client.horseVideo.create({
      data: {
        horseId: horse.horseId,
        videoYear: 2025,
        videoMonth: 9,
        videoDay: 1,
        title: '削除対象',
        youtubeVideoId: 'yt_del',
        publishStatus: 'DRAFT',
      },
    });

    const req = create(DeleteHorseVideoRequestSchema, { horseVideoId: v.horseVideoId });
    const res = await api.deleteHorseVideo(req);
    expect(res.message).toBeDefined();

    const saved = await vPrisma.client.horseVideo.findUnique({ where: { horseVideoId: v.horseVideoId } });
    expect(saved?.deletedAt).not.toBeNull();
  });
});
