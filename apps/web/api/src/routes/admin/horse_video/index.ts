export { listHorseVideosHandler } from './list_horse_videos';
export { getHorseVideoHandler } from './get_horse_video';
export { createHorseVideoHandler } from './create_horse_video';
export { updateHorseVideoHandler } from './update_horse_video';
export { deleteHorseVideoHandler } from './delete_horse_video';

import { HorseVideoService } from '@hami/admin-api-schema/horse_video_service_pb';
import { createHorseVideoHandler } from './create_horse_video';
import { deleteHorseVideoHandler } from './delete_horse_video';
import { getHorseVideoHandler } from './get_horse_video';
import { listHorseVideosHandler } from './list_horse_videos';
import { updateHorseVideoHandler } from './update_horse_video';
import { unwrapResult } from '../../../utils/unwrap_handler';
import type { ConnectRouter } from '@connectrpc/connect';

export const implHorseVideoService = (router: ConnectRouter) => {
  router.service(HorseVideoService, {
    listHorseVideos: unwrapResult(listHorseVideosHandler),
    getHorseVideo: unwrapResult(getHorseVideoHandler),
    createHorseVideo: unwrapResult(createHorseVideoHandler),
    updateHorseVideo: unwrapResult(updateHorseVideoHandler),
    deleteHorseVideo: unwrapResult(deleteHorseVideoHandler),
  });
};
