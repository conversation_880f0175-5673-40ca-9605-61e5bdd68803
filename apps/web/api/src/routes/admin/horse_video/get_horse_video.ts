import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetHorseVideoResponseSchema } from '@hami/admin-api-schema/horse_video_service_pb';
import { HorseListItemSchema } from '@hami/admin-api-schema/models/horse_pb';
import { HorseVideoItemSchema } from '@hami/admin-api-schema/models/horse_video_pb';
import { DatabaseError } from '@web-api/repositories';
import { getHorseVideo } from '@web-api/repositories/horse_video_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';
import { convertPrismaPublishStatusToProtoPublishStatus } from '../horse_report/utils/convert_utils';

const schema = z.object({ horseVideoId: z.number().int().positive() });

type Req = z.infer<typeof schema>;

export const getHorseVideoHandler = createHandler({
  schema,
  business: (req: Req) => getHorseVideo(req.horseVideoId),
  toResponse: (v) =>
    create(GetHorseVideoResponseSchema, {
      video: create(HorseVideoItemSchema, {
        horseVideoId: v.horseVideoId,
        horseId: v.horseId,
        horseName: v.horseName,
        videoYear: v.videoYear,
        videoMonth: v.videoMonth,
        videoDay: v.videoDay,
        publishStatus: convertPrismaPublishStatusToProtoPublishStatus(v.publishStatus),
        title: v.title,
        description: v.description ?? undefined,
        youtubeVideoId: v.youtubeVideoId,
        startAtSeconds: v.startAtSeconds ?? undefined,
        thumbnailImagePath: v.thumbnailImagePath ?? undefined,
        horse: create(HorseListItemSchema, {
          horseId: v.horseId,
          recruitmentYear: v.horse?.recruitmentYear ?? 0,
          recruitmentNo: v.horse?.recruitmentNo ?? 0,
          recruitmentName: v.horse?.recruitmentName ?? '',
          horseName: v.horse?.horseName ?? v.horseName,
          birthYear: v.horse?.birthYear ?? 0,
        }),
      }),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e: ValidationError) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Not found', Code.NotFound)),
});
