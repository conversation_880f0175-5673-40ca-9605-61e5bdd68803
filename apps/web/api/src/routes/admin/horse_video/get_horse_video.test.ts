import { create } from '@bufbuild/protobuf';
import { getClient } from '@web-test/index';
import { horseFactory } from '@web-test/factories/horse_factory';
import { HorseVideoService, GetHorseVideoRequestSchema } from '@hami/admin-api-schema/horse_video_service_pb';

describe('getHorseVideo API', () => {
  const api = getClient(HorseVideoService);

  it('動画詳細を取得できる', async () => {
    const horse = await horseFactory.create();
    const v = await vPrisma.client.horseVideo.create({
      data: {
        horseId: horse.horseId,
        videoYear: 2025,
        videoMonth: 9,
        videoDay: 1,
        title: 'テスト動画',
        youtubeVideoId: 'yt_test',
        publishStatus: 'DRAFT',
      },
    });

    const req = create(GetHorseVideoRequestSchema, { horseVideoId: v.horseVideoId });
    const res = await api.getHorseVideo(req);

    expect(res.video?.horseVideoId).toBe(v.horseVideoId);
    expect(res.video?.title).toBeTruthy();
  });
});
