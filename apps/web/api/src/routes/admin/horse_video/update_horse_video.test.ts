import { create } from '@bufbuild/protobuf';
import { getClient } from '@web-test/index';
import { horseFactory } from '@web-test/factories/horse_factory';
import { HorseVideoService, UpdateHorseVideoRequestSchema } from '@hami/admin-api-schema/horse_video_service_pb';
import { PublishStatus } from '@hami/admin-api-schema/common_enums_pb';

// 更新APIの基本挙動

describe('updateHorseVideo API', () => {
  const api = getClient(HorseVideoService);

  it('タイトルと公開ステータスを更新できる', async () => {
    const horse = await horseFactory.create();
    const v = await vPrisma.client.horseVideo.create({
      data: {
        horseId: horse.horseId,
        videoYear: 2025,
        videoMonth: 9,
        videoDay: 1,
        title: '更新前',
        youtubeVideoId: 'yt_before',
        publishStatus: 'DRAFT',
      },
    });

    const req = create(UpdateHorseVideoRequestSchema, {
      horseVideoId: v.horseVideoId,
      title: '更新後タイトル',
      publishStatus: PublishStatus.PUBLISHED,
    });

    const res = await api.updateHorseVideo(req);
    expect(res.video?.title).toBe('更新後タイトル');
    expect(res.video?.publishStatus).toBe(PublishStatus.PUBLISHED);

    const saved = await vPrisma.client.horseVideo.findUnique({ where: { horseVideoId: v.horseVideoId } });
    expect(saved?.title).toBe('更新後タイトル');
  });
});
