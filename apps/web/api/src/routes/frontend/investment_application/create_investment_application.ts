import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync, errAsync, okAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { User, Member, InvestmentApplication, Horse, AnnualBundle } from '@hami/prisma';
import { CreateInvestmentApplicationResponseSchema } from '@hami/web-api-schema/investment_application_service_pb';
import { investmentApplicationTemplate } from '@web-api/mail_templates/investment_application';
import { checkUserExists, UnauthorizedError } from '@web-api/middlewares/interceptors';
import { findAnnualBundleById, AnnualBundleNotFoundError as AnnualBundleRepoNotFoundError } from '@web-api/repositories/annual_bundle_repository';
import { findHorseById } from '@web-api/repositories/horse_repository';
import {
  createInvestmentApplication,
  validateHorseExists,
  validateAnnualBundleExists,
  DatabaseError,
  HorseNotFoundError,
  AnnualBundleNotFoundError,
} from '@web-api/repositories/investment_application_repository';
import { findMemberById } from '@web-api/repositories/member_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { sendMail } from '@web-api/utils/mail';
import { client } from '@web-api/utils/prisma';
import { ValidationError } from '@web-api/utils/validate_request';

// Type for User with Member relation
type UserWithMember = User & {
  member: Member | null;
};

// Type guard to check if user has member
const hasValidMember = (user: User): user is UserWithMember & { member: Member } => {
  return 'member' in user && user.member !== null;
};

const itemSchema = z.object({
  recruitmentYear: z.number().int().positive('募集年は正の整数である必要があります').optional(),
  recruitmentNo: z.number().int().positive('募集番号は正の整数である必要があります').optional(),
  annualBundleId: z.number().int().positive('年度バンドルIDは正の整数である必要があります').optional(),
  requestedNumber: z.number().int().min(1, '希望口数は1以上である必要があります'),
  rejectPartialAllocation: z.boolean(),
  installmentPayment: z.boolean(),
}).refine(
  (data) => {
    // 馬の出資申し込み（recruitment_year + recruitment_no）と一括出資パッケージ（annual_bundle_id）のどちらか一方が必須
    const hasHorseInfo = data.recruitmentYear !== undefined && data.recruitmentNo !== undefined;
    const hasAnnualBundle = data.annualBundleId !== undefined;
    return hasHorseInfo !== hasAnnualBundle; // XOR: どちらか一方のみ
  },
  {
    message: '馬の出資申し込み（募集年・募集番号）または年度バンドルIDのどちらか一方を指定してください',
  }
);

const schema = z.object({
  items: z.array(itemSchema).min(1, '少なくとも1つの申込アイテムが必要です'),
  isWhole: z.boolean(),
});

export const createInvestmentApplicationHandler = createHandler({
  schema,
  business: ({ items, isWhole }, ctx) =>
    ResultAsync.fromPromise(
      (async () => {
        const userResult = checkUserExists(ctx);
        if (userResult.isErr()) return errAsync(userResult.error);
        const user = userResult.value;
        if (!hasValidMember(user)) {
          return errAsync(new UnauthorizedError('会員情報が見つかりません'));
        }
        const memberId = user.member.memberId;

        // トランザクション内で複数申込を処理
        return await client.$transaction(async (_tx) => {
          const applications: Array<{
            application: InvestmentApplication;
            horse?: Horse;
            annualBundle?: AnnualBundle;
          }> = [];

          for (const item of items) {
            let horseId: number | undefined;
            let annualBundleId: number | undefined;
            let horse: Horse | undefined;
            let annualBundle: AnnualBundle | undefined;

            if (item.recruitmentYear !== undefined && item.recruitmentNo !== undefined) {
              // 馬の出資申し込み
              const horseExistsResult = await validateHorseExists(item.recruitmentYear, item.recruitmentNo);
              if (horseExistsResult.isErr() || !horseExistsResult.value || horseExistsResult.value === null) {
                throw new HorseNotFoundError();
              }
              horseId = horseExistsResult.value;

              // 馬情報を取得
              const horseResult = await findHorseById({ horseId });
              if (horseResult.isErr() || !horseResult.value) {
                throw new HorseNotFoundError();
              }
              horse = horseResult.value;
            } else if (item.annualBundleId !== undefined) {
              // 一括出資パッケージの申し込み
              const bundleExistsResult = await validateAnnualBundleExists(item.annualBundleId);
              if (bundleExistsResult.isErr() || !bundleExistsResult.value || bundleExistsResult.value === null) {
                throw new AnnualBundleNotFoundError();
              }
              annualBundleId = bundleExistsResult.value;

              // 一括出資パッケージ情報を取得
              const bundleResult = await findAnnualBundleById({ annualBundleId });
              if (bundleResult.isErr() || !bundleResult.value) {
                throw new AnnualBundleRepoNotFoundError();
              }
              annualBundle = bundleResult.value;
            }

            // 申込作成（トランザクション内）
            const appResult = await createInvestmentApplication({
              memberId,
              horseId,
              annualBundleId,
              requestedNumber: item.requestedNumber,
              rejectPartialAllocation: item.rejectPartialAllocation,
              isWhole: isWhole,
              installmentPayment: item.installmentPayment,
            });
            if (appResult.isErr()) throw appResult.error;

            applications.push({
              application: appResult.value,
              horse,
              annualBundle,
            });
          }

          return applications;
        }, {
          maxWait: 10000, // トランザクション取得の最大待機時間
          timeout: 30000, // トランザクションの最大実行時間
        }).then(async (applications) => {
          // トランザクション成功後、メール送信を実行
          const memberResult = await findMemberById({ memberId });
          if (memberResult.isErr()) {
            throw new DatabaseError('Failed to get member information');
          }

          // まとめてメール送信
          const applicationsForMail = applications.map(({ application, horse, annualBundle }) => ({
            horseName: horse?.recruitmentName || annualBundle?.name || '不明',
            requestedNumber: application.requestedNumber,
            rejectPartialAllocation: application.rejectPartialAllocation,
            investmentApplicationId: application.investmentApplicationId,
            appliedAt: application.appliedAt.toLocaleString('ja-JP', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              timeZone: 'Asia/Tokyo',
            }),
          }));

          sendMail({
            to: user.email,
            template: investmentApplicationTemplate,
            params: {
              firstName: memberResult.value.firstName,
              lastName: memberResult.value.lastName,
              applications: applicationsForMail,
            },
          }).mapErr((mailError) => {
            console.warn('Failed to send investment application email:', mailError);
          });

          return okAsync(undefined);
        }).catch((error) => {
          if (error instanceof HorseNotFoundError) {
            throw error;
          }
          if (error instanceof AnnualBundleNotFoundError) {
            throw error;
          }
          if (error instanceof AnnualBundleRepoNotFoundError) {
            throw error;
          }
          if (error instanceof DatabaseError) {
            throw error;
          }
          throw new DatabaseError('不明なエラー');
        });
      })(),
      (e) => e instanceof Error ? e : new DatabaseError('不明なエラー')
    ).andThen((r) => r),
  toResponse: () => create(CreateInvestmentApplicationResponseSchema, {}),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), (e) => new ConnectError(e.message, Code.Unauthenticated))
      .with(
        P.instanceOf(HorseNotFoundError),
        () => new ConnectError('指定された馬が見つからないか、現在出資申込を受け付けていません', Code.NotFound)
      )
      .with(
        P.instanceOf(AnnualBundleNotFoundError),
        () => new ConnectError('指定された年度バンドルが見つからないか、現在出資申込を受け付けていません', Code.NotFound)
      )
      .with(
        P.instanceOf(AnnualBundleRepoNotFoundError),
        () => new ConnectError('指定された年度バンドルが見つからないか、現在出資申込を受け付けていません', Code.NotFound)
      )
      .with(P.instanceOf(DatabaseError), () => new ConnectError('データベースエラーが発生しました', Code.Internal))
      .exhaustive(),
});
