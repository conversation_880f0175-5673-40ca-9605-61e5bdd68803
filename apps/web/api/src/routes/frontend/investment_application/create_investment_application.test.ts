import { Code, ConnectError } from '@connectrpc/connect';
import { annualBundleFactory } from '@web-test/factories/annual_bundle_factory';
import { horseFactory } from '@web-test/factories/horse_factory';
import { horseProfileFactory } from '@web-test/factories/horse_profile_factory';
import { MemberFactory } from '@web-test/factories/member_factory';
import { UserFactory } from '@web-test/factories/user_factory';
import { UserSessionFactory } from '@web-test/factories/user_session_factory';
import { PublishStatus, RecruitmentStatus, AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@hami/prisma';
import { InvestmentApplicationService } from '@hami/web-api-schema/investment_application_service_pb';

describe('createInvestmentApplication', () => {
  it('正常に出資申込を作成できる', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horse = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });

    const requestData = {
      items: [{
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        requestedNumber: 5,
        rejectPartialAllocation: false,
      }],
      isWhole: false,
    };

    // ===== Act =====
    const investmentClient = getClient(InvestmentApplicationService);
    const response = await investmentClient.createInvestmentApplication(requestData, {
      headers: {
        sessionToken: session.sessionToken,
      },
    });

    // ===== Assert =====
    // DBに保存されていることを確認
    const savedApplication = await vPrisma.client.investmentApplication.findFirst({
      where: { 
        memberId: member.memberId,
        horseId: horse.horseId,
      },
    });
    expect(savedApplication).toBeDefined();
    expect(savedApplication?.memberId).toBe(member.memberId);
    expect(savedApplication?.horseId).toBe(horse.horseId);
    expect(savedApplication?.requestedNumber).toBe(5);
    expect(savedApplication?.rejectPartialAllocation).toBe(false);
    expect(savedApplication?.allocatedNumber).toBeNull();
    expect(savedApplication?.rejected).toBe(false);
    expect(savedApplication?.isWhole).toBe(false);
  });

  it('複数の出資申込を一括作成できる', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horse1 = await horseFactory.create();
    const horse2 = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse2.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });

    const requestData = {
      items: [
        {
          recruitmentYear: horse1.recruitmentYear,
          recruitmentNo: horse1.recruitmentNo,
          requestedNumber: 5,
          rejectPartialAllocation: false,
        },
        {
          recruitmentYear: horse2.recruitmentYear,
          recruitmentNo: horse2.recruitmentNo,
          requestedNumber: 3,
          rejectPartialAllocation: true,
        },
      ],
      isWhole: true,
    };

    // ===== Act =====
    const investmentClient = getClient(InvestmentApplicationService);
    const response = await investmentClient.createInvestmentApplication(requestData, {
      headers: {
        sessionToken: session.sessionToken,
      },
    });

    // ===== Assert =====
    // DBに2つの申込が保存されていることを確認
    const savedApplications = await vPrisma.client.investmentApplication.findMany({
      where: { memberId: member.memberId },
      orderBy: { createdAt: 'asc' },
    });
    expect(savedApplications).toHaveLength(2);

    const application1 = savedApplications.find((app: any) => app.horseId === horse1.horseId);
    const application2 = savedApplications.find((app: any) => app.horseId === horse2.horseId);

    expect(application1?.requestedNumber).toBe(5);
    expect(application1?.rejectPartialAllocation).toBe(false);
    expect(application1?.isWhole).toBe(true);
    expect(application2?.requestedNumber).toBe(3);
    expect(application2?.rejectPartialAllocation).toBe(true);
    expect(application2?.isWhole).toBe(true);
  });

  it('2頭分の申込を同時に行うと2件のレコードが作成される', async ({ getClient }) => {
    // Arrange
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horseA = await horseFactory.create();
    const horseB = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horseA.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });
    await horseProfileFactory.create({
      horse: { connect: { horseId: horseB.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });

    const requestData = {
      items: [
        {
          recruitmentYear: horseA.recruitmentYear,
          recruitmentNo: horseA.recruitmentNo,
          requestedNumber: 2,
          rejectPartialAllocation: false,
        },
        {
          recruitmentYear: horseB.recruitmentYear,
          recruitmentNo: horseB.recruitmentNo,
          requestedNumber: 3,
          rejectPartialAllocation: true,
        },
      ],
      isWhole: false,
    };

    // Act
    const investmentClient = getClient(InvestmentApplicationService);
    const response = await investmentClient.createInvestmentApplication(requestData, {
      headers: {
        sessionToken: session.sessionToken,
      },
    });

    // Assert
    const applications = await vPrisma.client.investmentApplication.findMany({
      where: { memberId: member.memberId },
      orderBy: { createdAt: 'asc' },
    });
    expect(applications).toHaveLength(2);
    const appA = applications.find(app => app.horseId === horseA.horseId);
    const appB = applications.find(app => app.horseId === horseB.horseId);
    expect(appA).toBeDefined();
    expect(appA?.requestedNumber).toBe(2);
    expect(appA?.rejectPartialAllocation).toBe(false);
    expect(appB).toBeDefined();
    expect(appB?.requestedNumber).toBe(3);
    expect(appB?.rejectPartialAllocation).toBe(true);
  });

  it('既存の未処理申込がある場合は更新する', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horse = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });

    // 既存の未処理申込を作成
    const existingApplication = await vPrisma.client.investmentApplication.create({
      data: {
        memberId: member.memberId,
        horseId: horse.horseId,
        requestedNumber: 3,
        rejectPartialAllocation: true,
        isWhole: false,
        allocatedNumber: null,
        rejected: false,
      },
    });

    const requestData = {
      items: [{
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        requestedNumber: 10,
        rejectPartialAllocation: false,
      }],
      isWhole: false,
    };

    // ===== Act =====
    const investmentClient = getClient(InvestmentApplicationService);
    const response = await investmentClient.createInvestmentApplication(requestData, {
      headers: {
        sessionToken: session.sessionToken,
      },
    });

    // ===== Assert =====
    // DBで更新されていることを確認
    const updatedApplication = await vPrisma.client.investmentApplication.findUnique({
      where: { investmentApplicationId: existingApplication.investmentApplicationId },
    });
    expect(updatedApplication?.requestedNumber).toBe(10);
    expect(updatedApplication?.rejectPartialAllocation).toBe(false);
    expect(updatedApplication?.isWhole).toBe(false);
    expect(updatedApplication?.appliedAt).toBeInstanceOf(Date);

    // レコード数は変わらない
    const applications = await vPrisma.client.investmentApplication.findMany({
      where: { memberId: member.memberId, horseId: horse.horseId },
    });
    expect(applications).toHaveLength(1);
  });

  it('処理済み申込がある場合は新規作成する', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horse = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });

    // 処理済み申込を作成
    await vPrisma.client.investmentApplication.create({
      data: {
        memberId: member.memberId,
        horseId: horse.horseId,
        requestedNumber: 3,
        rejectPartialAllocation: false,
        isWhole: false,
        allocatedNumber: 2, // 処理済み
        rejected: false,
      },
    });

    const requestData = {
      items: [{
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        requestedNumber: 7,
        rejectPartialAllocation: true,
      }],
      isWhole: false,
    };

    // ===== Act =====
    const investmentClient = getClient(InvestmentApplicationService);
    const response = await investmentClient.createInvestmentApplication(requestData, {
      headers: {
        sessionToken: session.sessionToken,
      },
    });

    // ===== Assert =====
    // 新規レコードが作成されている
    const applications = await vPrisma.client.investmentApplication.findMany({
      where: { memberId: member.memberId, horseId: horse.horseId },
      orderBy: { createdAt: 'asc' },
    });
    expect(applications).toHaveLength(2);

    // 新しい申込の内容確認
    const newApplication = applications[1];
    expect(newApplication.requestedNumber).toBe(7);
    expect(newApplication.rejectPartialAllocation).toBe(true);
    expect(newApplication.isWhole).toBe(false);
    expect(newApplication.allocatedNumber).toBeNull();
  });

  it('認証されていない場合は401エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const horse = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });

    const requestData = {
      items: [{
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        requestedNumber: 5,
        rejectPartialAllocation: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData);
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
    }
  });

  it('会員でない場合は401エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create(); // 会員ではないユーザー
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horse = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });

    const requestData = {
      items: [{
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        requestedNumber: 5,
        rejectPartialAllocation: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.Unauthenticated);
      expect(error.message).toContain('会員情報が見つかりません');
    }
  });

  it('存在しない馬IDの場合は404エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    const requestData = {
      items: [{
        recruitmentYear: 9999, // 存在しない馬ID
        recruitmentNo: 9999, // 存在しない馬ID
        requestedNumber: 5,
        rejectPartialAllocation: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
      expect(error.message).toContain('指定された馬が見つからないか、現在出資申込を受け付けていません');
    }
  });

  it('PublishStatusがDRAFTの馬の場合は404エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horse = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      publishStatus: PublishStatus.DRAFT,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });

    const requestData = {
      items: [{
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        requestedNumber: 5,
        rejectPartialAllocation: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
      expect(error.message).toContain('指定された馬が見つからないか、現在出資申込を受け付けていません');
    }
  });

  it('RecruitmentStatusがCLOSEDの馬の場合は404エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horse = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.CLOSED,
    });

    const requestData = {
      items: [{
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        requestedNumber: 5,
        rejectPartialAllocation: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
      expect(error.message).toContain('指定された馬が見つからないか、現在出資申込を受け付けていません');
    }
  });

  it('不正な希望口数の場合は400エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horse = await horseFactory.create();
    await horseProfileFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      publishStatus: PublishStatus.PUBLISHED,
      recruitmentStatus: RecruitmentStatus.ACTIVE,
    });

    const requestData = {
      items: [{
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        requestedNumber: 0, // 不正な値
        rejectPartialAllocation: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('負の馬IDの場合は400エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    const requestData = {
      items: [{
        recruitmentYear: -1, // 負の値
        recruitmentNo: -1, // 負の値
        requestedNumber: 5,
        rejectPartialAllocation: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('空のitems配列の場合は400エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    const requestData = {
      items: [], // 空の配列
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('一括出資パッケージの申し込みができる', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const annualBundle = await annualBundleFactory.create({
      publishStatus: AnnualBundlePublishStatus.PUBLIC,
      recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
    });

    const requestData = {
      items: [{
        annualBundleId: annualBundle.annualBundleId,
        requestedNumber: 5,
        rejectPartialAllocation: false,
        installmentPayment: false,
      }],
      isWhole: false,
    };

    // ===== Act =====
    const investmentClient = getClient(InvestmentApplicationService);
    const response = await investmentClient.createInvestmentApplication(requestData, {
      headers: {
        sessionToken: session.sessionToken,
      },
    });

    // ===== Assert =====
    expect(response).toBeDefined();

    // DBに申込が保存されていることを確認
    const savedApplication = await vPrisma.client.investmentApplication.findFirst({
      where: {
        memberId: member.memberId,
        annualBundleId: annualBundle.annualBundleId,
      },
    });
    expect(savedApplication).toBeDefined();
    expect(savedApplication?.requestedNumber).toBe(5);
    expect(savedApplication?.rejectPartialAllocation).toBe(false);
    expect(savedApplication?.installmentPayment).toBe(false);
    expect(savedApplication?.horseId).toBeNull();
  });

  it('存在しない一括出資パッケージIDの場合は404エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    const requestData = {
      items: [{
        annualBundleId: 99999, // 存在しないID
        requestedNumber: 5,
        rejectPartialAllocation: false,
        installmentPayment: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.NotFound);
    }
  });

  it('馬と一括出資パッケージの両方を指定した場合は400エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const horse = await horseFactory.create();
    const annualBundle = await annualBundleFactory.create();

    const requestData = {
      items: [{
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        annualBundleId: annualBundle.annualBundleId, // 両方指定
        requestedNumber: 5,
        rejectPartialAllocation: false,
        installmentPayment: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('馬も一括出資パッケージも指定しない場合は400エラー', async ({ getClient }) => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    const requestData = {
      items: [{
        // recruitmentYear, recruitmentNo, annualBundleIdのいずれも指定しない
        requestedNumber: 5,
        rejectPartialAllocation: false,
        installmentPayment: false,
      }],
      isWhole: false,
    };

    // ===== Act/Assert =====
    const investmentClient = getClient(InvestmentApplicationService);
    let error;
    try {
      await investmentClient.createInvestmentApplication(requestData, {
        headers: {
          sessionToken: session.sessionToken,
        },
      });
    } catch (err) {
      error = err;
    }
    expect(error).toBeDefined();
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });
});
