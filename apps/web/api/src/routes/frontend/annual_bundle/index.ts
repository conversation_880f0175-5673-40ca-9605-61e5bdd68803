import { AnnualBundleService } from '@hami/web-api-schema/annual_bundle_service_pb';
import { unwrapResult } from '@web-api/utils/unwrap_handler';
import { listAnnualBundlesByFiscalYearHandler } from './list_annual_bundles_by_fiscal_year';

import type { ConnectRouter } from '@connectrpc/connect';

export const implAnnualBundleService = (router: ConnectRouter) => {
  router.service(AnnualBundleService, {
    listAnnualBundlesByFiscalYear: unwrapResult(listAnnualBundlesByFiscalYearHandler),
  });
};


