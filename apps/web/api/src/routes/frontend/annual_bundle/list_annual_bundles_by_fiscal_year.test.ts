import { annualBundleFactory } from '@web-test/factories/annual_bundle_factory';
import { horseFactory } from '@web-test/factories/horse_factory';
import { PublishStatus, AnnualBundleRecruitmentStatus as PrismaRecruitmentStatus, AnnualBundlePublishStatus as PrismaPublishStatus } from '@hami/prisma';
import { AnnualBundleService } from '@hami/web-api-schema/annual_bundle_service_pb';

describe('年度バンドル一覧取得API', () => {
  it('同一年度の公開バンドルを一覧取得し、各perShareAmountが計算される', async ({ getClient }) => {
    const year = 2033;

    // 同年度の公開馬データを作成
    const h1 = await horseFactory.create({ recruitmentYear: year, recruitmentNo: 1, horseName: 'H1', sharesTotal: 100, amountTotal: 10000000, profile: { create: { publishStatus: PublishStatus.PUBLISHED } } });
    const h2 = await horseFactory.create({ recruitmentYear: year, recruitmentNo: 2, horseName: 'H2', sharesTotal: 200, amountTotal: 40000000, profile: { create: { publishStatus: PublishStatus.PUBLISHED } } });
    const h3 = await horseFactory.create({ recruitmentYear: year, recruitmentNo: 3, horseName: 'H3', sharesTotal: 100, amountTotal: 9000000, profile: { create: { publishStatus: PublishStatus.PUBLISHED } } });

    // バンドルを2つ作成（どちらも公開）
    await annualBundleFactory.create({ fiscalYear: year, name: 'パッケージA', shares: 50, publishStatus: PrismaPublishStatus.PUBLIC, recruitmentStatus: PrismaRecruitmentStatus.ACTIVE, horses: { create: [{ horse: { connect: { horseId: h1.horseId } } }, { horse: { connect: { horseId: h2.horseId } } }] } });
    await annualBundleFactory.create({ fiscalYear: year, name: 'パッケージB', shares: 30, publishStatus: PrismaPublishStatus.PUBLIC, recruitmentStatus: PrismaRecruitmentStatus.UPCOMING, horses: { create: [{ horse: { connect: { horseId: h3.horseId } } }] } });

    const client = getClient(AnnualBundleService);
    const res = await client.listAnnualBundlesByFiscalYear({ fiscalYear: year });

    expect(res.bundles.length).toBe(2);
    // perShareAmount の集合を検証（順不同）
    const amounts = res.bundles.map((b) => b.perShareAmount).sort((a, b) => a - b);
    expect(amounts).toEqual([90000, 300000]);
    // 募集ステータスが設定されている
    res.bundles.forEach((b) => expect(b.recruitmentStatus).toBeDefined());
  });

  it('非公開のみの年度は0件で返る', async ({ getClient }) => {
    const year = 2034;
    await annualBundleFactory.create({ fiscalYear: year, name: '非公開パッケージ', shares: 10, publishStatus: PrismaPublishStatus.PRIVATE, recruitmentStatus: PrismaRecruitmentStatus.UPCOMING });

    const client = getClient(AnnualBundleService);
    const res = await client.listAnnualBundlesByFiscalYear({ fiscalYear: year });
    expect(res.bundles.length).toBe(0);
  });

  it('存在しない年度は0件で返る', async ({ getClient }) => {
    const client = getClient(AnnualBundleService);
    const res = await client.listAnnualBundlesByFiscalYear({ fiscalYear: 2099 });
    expect(res.bundles.length).toBe(0);
  });
});


