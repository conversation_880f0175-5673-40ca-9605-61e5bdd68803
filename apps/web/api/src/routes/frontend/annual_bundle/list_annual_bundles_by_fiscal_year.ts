import { create } from '@bufbuild/protobuf';
import { Code, ConnectError, HandlerContext } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  AnnualBundleRecruitmentStatus as ProtoRecruitment,
  AnnualBundlePublishStatus as ProtoPublish,
  ListAnnualBundlesByFiscalYearResponseSchema,
  AnnualBundleSchema,
  AnnualBundleHorseRelationSchema,
} from '@hami/web-api-schema/annual_bundle_service_pb';
import { listPublicAnnualBundlesByFiscalYear } from '@web-api/repositories/annual_bundle_repository';
import { DatabaseError } from '@web-api/repositories/index';
import { createHandler } from '@web-api/utils/handler_factory';

const schema = z.object({ fiscalYear: z.number().int().min(2000).max(2100) });

export const listAnnualBundlesByFiscalYearHandler = createHandler({
  schema,
  business: ({ fiscalYear }, _ctx: HandlerContext) => listPublicAnnualBundlesByFiscalYear({ fiscalYear }),
  toResponse: (bundles) =>
    create(ListAnnualBundlesByFiscalYearResponseSchema, {
      bundles: bundles.map((bundle) =>
        create(AnnualBundleSchema, {
          annualBundleId: bundle.annualBundleId,
          fiscalYear: bundle.fiscalYear,
          name: bundle.name,
          shares: bundle.shares,
          perShareAmount: bundle.horses.reduce((sum, rel) => {
            const amount = rel.horse.amountTotal ?? 0;
            const shares = rel.horse.sharesTotal ?? 1;
            if (shares <= 0) return sum;
            return sum + Math.floor(amount / shares);
          }, 0),
          publishStatus: bundle.publishStatus === 'PUBLIC' ? ProtoPublish.AB_PUBLIC : ProtoPublish.AB_PRIVATE,
          recruitmentStatus:
            bundle.recruitmentStatus === 'UPCOMING'
              ? ProtoRecruitment.AB_UPCOMING
              : bundle.recruitmentStatus === 'ACTIVE'
              ? ProtoRecruitment.AB_ACTIVE
              : bundle.recruitmentStatus === 'CLOSED'
              ? ProtoRecruitment.AB_CLOSED
              : ProtoRecruitment.AB_FULL,
          horses: bundle.horses.map((h) =>
            create(AnnualBundleHorseRelationSchema, {
              horseId: h.horseId,
              horseName: h.horse.horseName,
              recruitmentName: h.horse.recruitmentName,
              recruitmentNo: h.horse.recruitmentNo,
            })
          ),
        })
      ),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
