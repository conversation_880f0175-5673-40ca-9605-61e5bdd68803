import { MemberClaimAndPayFactory } from '@web-test/factories/member_claim_and_pay_factory';
import { MemberFactory } from '@web-test/factories/member_factory';
import { UserFactory } from '@web-test/factories/user_factory';
import { UserSessionFactory } from '@web-test/factories/user_session_factory';
import { getClient } from '@web-test/index';
import { MemberClaimAndPayService } from '@hami/web-api-schema/member_claim_and_pay_service_pb';

describe('会員請求・支払いAPI', () => {
  const apiClient = getClient(MemberClaimAndPayService);

  it('会員の請求・支払い一覧を取得できる（statementFileKeyがnullではないもののみ）', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // 請求・支払いレコードを作成（statementFileKeyあり）
    const claimAndPay1 = await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-01-15'),
      claimAmount: 50000,
      payAmount: 30000,
      statementFileKey: 'statement1.pdf',
    });

    const claimAndPay2 = await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-02-20'),
      claimAmount: 75000,
      payAmount: 45000,
      statementFileKey: 'statement2.pdf',
    });

    // statementFileKeyがnullのレコードを作成（取得されないことを確認するため）
    await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-03-01'),
      claimAmount: 100000,
      payAmount: 60000,
      statementFileKey: null,
    });

    // 他の会員のレコード（取得されないことを確認するため）
    const otherMember = await MemberFactory.create();
    await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: otherMember.memberId } },
      occurredDate: new Date('2024-01-10'),
      claimAmount: 100000,
      payAmount: 60000,
      statementFileKey: 'statement3.pdf',
    });
    

    // ===== Act =====
    const response = await apiClient.listMemberClaimAndPays(
      {},
      {
        headers: {
          sessionToken: session.sessionToken,
        },
      }
    );

    // ===== Assert =====
    expect(response.memberClaimAndPays).toHaveLength(2);
    
    // statementFileKeyがnullのレコードは含まれていないことを確認
    expect(response.memberClaimAndPays.every(record => 
      record.memberClaimAndPayId === claimAndPay1.memberClaimAndPayId || 
      record.memberClaimAndPayId === claimAndPay2.memberClaimAndPayId
    )).toBe(true);
    
    // 発生日の降順でソートされていることを確認
    expect(response.memberClaimAndPays[0].memberClaimAndPayId).toBe(claimAndPay2.memberClaimAndPayId);
    expect(response.memberClaimAndPays[1].memberClaimAndPayId).toBe(claimAndPay1.memberClaimAndPayId);
    
    // 最初のレコードの詳細を確認
    const firstRecord = response.memberClaimAndPays[0];
    expect(firstRecord.memberId).toBe(member.memberId);
    expect(firstRecord.occurredYear).toBe(2024);
    expect(firstRecord.occurredMonth).toBe(2);
    expect(firstRecord.occurredDay).toBe(20);
    expect(firstRecord.claimAmount).toBe(75000);
    expect(firstRecord.payAmount).toBe(45000);
  });

  it('会員に請求・支払いレコードがない場合は空配列を返す', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // 他の会員のレコードのみ作成
    const otherMember = await MemberFactory.create();
    await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: otherMember.memberId } },
    });

    // ===== Act =====
    const response = await apiClient.listMemberClaimAndPays(
      {},
      {
        headers: {
          sessionToken: session.sessionToken,
        },
      }
    );

    // ===== Assert =====
    expect(response.memberClaimAndPays).toHaveLength(0);
  });

  it('認証されていない場合はエラーを返す', async () => {
    // ===== Act/Assert =====
    await expect(apiClient.listMemberClaimAndPays({})).rejects.toThrow();
  });

  it('存在しないユーザーの場合はエラーを返す', async () => {
    // ===== Arrange =====
    // 無効なセッショントークンを使用

    // ===== Act & Assert =====
    await expect(
      apiClient.listMemberClaimAndPays(
        {},
        {
          headers: {
            sessionToken: 'invalid-token',
          },
        }
      )
    ).rejects.toThrow();
  });

  it('statementFileKeyがnullのレコードは除外される', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // statementFileKeyがnullのレコードを作成
    await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-01-15'),
      claimAmount: 50000,
      payAmount: 30000,
      statementFileKey: null,
    });

    // statementFileKeyが空文字のレコードを作成（現在のクエリでは取得される）
    const emptyStringRecord = await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-02-20'),
      claimAmount: 75000,
      payAmount: 45000,
      statementFileKey: '',
    });

    // statementFileKeyが設定されているレコードを作成
    const validRecord = await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-03-01'),
      claimAmount: 100000,
      payAmount: 60000,
      statementFileKey: 'valid-statement.pdf',
    });

    // ===== Act =====
    const response = await apiClient.listMemberClaimAndPays(
      {},
      {
        headers: {
          sessionToken: session.sessionToken,
        },
      }
    );

    // ===== Assert =====
    expect(response.memberClaimAndPays).toHaveLength(2);
    // nullのレコードは除外され、空文字と有効な値のレコードが取得される
    expect(response.memberClaimAndPays.map(r => r.memberClaimAndPayId)).toContain(emptyStringRecord.memberClaimAndPayId);
    expect(response.memberClaimAndPays.map(r => r.memberClaimAndPayId)).toContain(validRecord.memberClaimAndPayId);
  });
});
