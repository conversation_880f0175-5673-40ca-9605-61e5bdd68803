import { MemberClaimAndPayService } from '@hami/web-api-schema/member_claim_and_pay_service_pb';
import { userAuthenticator } from '@web-api/middlewares/interceptors';
import { unwrapResult } from '@web-api/utils/unwrap_handler';
import { requestStatementPdfDownloadUrlHandler } from './get_claim_and_pay_statement_download_url';
import { listMemberClaimAndPaysHandler } from './list_member_claim_and_pay_statements';
import type { ConnectRouter } from '@connectrpc/connect';

export const implMemberClaimAndPayService = (router: ConnectRouter) => {
  router.service(MemberClaimAndPayService, {
    listMemberClaimAndPays: unwrapResult(listMemberClaimAndPaysHandler),
    requestStatementPdfDownloadUrl: unwrapResult(requestStatementPdfDownloadUrlHandler),
  }, { interceptors: [userAuthenticator] });
};
