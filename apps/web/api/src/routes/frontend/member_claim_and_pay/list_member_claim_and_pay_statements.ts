import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { okAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';

import {
  ListMemberClaimAndPaysResponseSchema,
  MemberClaimAndPaySchema,
} from '@hami/web-api-schema/member_claim_and_pay_service_pb';
import { checkUserExists, UnauthorizedError } from '@web-api/middlewares/interceptors';
import { DatabaseError } from '@web-api/repositories';
import { listMemberClaimAndPays } from '@web-api/repositories/member_claim_and_pay_repository';
import { getMember } from '@web-api/repositories/member_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';
import type { HandlerContext } from '@connectrpc/connect';

// リクエストパラメータなしなので空のスキーマ
const schema = z.object({});

type ListMemberClaimAndPaysRequest = z.infer<typeof schema>;

const listMemberClaimAndPaysBusiness = (request: ListMemberClaimAndPaysRequest, ctx: HandlerContext) => {
  return okAsync(request)
    .andThen(() => checkUserExists(ctx))
    .andThen((user) => getMember({ userId: user.userId }))
    .andThen((member) =>
      listMemberClaimAndPays({
        memberId: member.memberId,
      })
    );
};

export const listMemberClaimAndPaysHandler = createHandler({
  schema,
  business: listMemberClaimAndPaysBusiness,
  toResponse: (result) => {
    const memberClaimAndPays = result.map((item) =>
      create(MemberClaimAndPaySchema, {
        memberClaimAndPayId: item.memberClaimAndPayId,
        memberId: item.memberId,
        occurredYear: item.occurredDate.getFullYear(),
        occurredMonth: item.occurredDate.getMonth() + 1, // getMonth()は0ベースなので+1
        occurredDay: item.occurredDate.getDate(),
        claimAmount: item.claimAmount,
        payAmount: item.payAmount,
        statementFileKey: item.statementFileKey ?? undefined,
      })
    );

    return create(ListMemberClaimAndPaysResponseSchema, {
      memberClaimAndPays,
    });
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(UnauthorizedError), () => new ConnectError('認証が必要です', Code.Unauthenticated))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
