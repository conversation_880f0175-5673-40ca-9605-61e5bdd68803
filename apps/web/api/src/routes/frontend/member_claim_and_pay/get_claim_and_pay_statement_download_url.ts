import { create } from '@bufbuild/protobuf';
import { Code, ConnectError, HandlerContext } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { z } from 'zod';

import {
  RequestStatementPdfDownloadUrlRequest,
  RequestStatementPdfDownloadUrlResponse,
  RequestStatementPdfDownloadUrlResponseSchema,
} from '@hami/web-api-schema/member_claim_and_pay_service_pb';
import { checkUserExists, UnauthorizedError } from '@web-api/middlewares/interceptors';
import { DatabaseError } from '@web-api/repositories/index';
import { getClaimAndPayStatementDownloadUrlUsecase } from '@web-api/usecases/get_claim_and_pay_statement_download_url_usecase';
import { ValidationError } from '@web-api/utils/validate_request';

// Type guard to check if user has member
const hasValidMember = (user: unknown): user is { member: { memberId: number } } => {
  return typeof user === 'object' && user !== null && 'member' in user && user.member !== null;
};

// リクエストバリデーションスキーマ
const RequestStatementPdfDownloadUrlValidationSchema = z.object({
  statementFileKey: z.string().min(1, '明細書ファイルキーは必須です'),
});

// protobuf型を直接受け取るハンドラー
export const requestStatementPdfDownloadUrlHandler = (
  req: RequestStatementPdfDownloadUrlRequest,
  ctx: HandlerContext
): ResultAsync<RequestStatementPdfDownloadUrlResponse, ConnectError> => {
  return ResultAsync.fromPromise(
    (async () => {
      // 1. バリデーション
      const validationResult = RequestStatementPdfDownloadUrlValidationSchema.safeParse({
        statementFileKey: req.statementFileKey,
      });
      if (!validationResult.success) {
        throw new ValidationError(validationResult.error.errors[0].message);
      }

      // 2. ユーザー認証とメンバー情報取得
      const user = await checkUserExists(ctx);
      if (user.isErr()) {
        throw new UnauthorizedError('User not found');
      }

      if (!hasValidMember(user.value)) {
        throw new UnauthorizedError('Member not found');
      }

      // 3. ダウンロードURL生成
      const result = await getClaimAndPayStatementDownloadUrlUsecase(req.statementFileKey, user.value.member.memberId);
      if (result.isErr()) {
        throw result.error;
      }

      return create(RequestStatementPdfDownloadUrlResponseSchema, {
        downloadUrl: result.value.downloadUrl,
      });
    })(),
    (error) => {
      console.error('Failed to generate download URL:', error);
      if (error instanceof ValidationError) {
        return new ConnectError(error.message, Code.InvalidArgument);
      }
      if (error instanceof UnauthorizedError) {
        return new ConnectError(error.message, Code.Unauthenticated);
      }
      if (error instanceof DatabaseError) {
        return new ConnectError('Internal server error', Code.Internal);
      }
      return new ConnectError('Internal server error', Code.Internal);
    }
  );
};
