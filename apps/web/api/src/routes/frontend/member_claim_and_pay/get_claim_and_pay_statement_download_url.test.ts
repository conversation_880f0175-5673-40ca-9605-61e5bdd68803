import { MemberClaimAndPayFactory } from '@web-test/factories/member_claim_and_pay_factory';
import { MemberFactory } from '@web-test/factories/member_factory';
import { UserFactory } from '@web-test/factories/user_factory';
import { UserSessionFactory } from '@web-test/factories/user_session_factory';
import { getClient } from '@web-test/index';
import { MemberClaimAndPayService } from '@hami/web-api-schema/member_claim_and_pay_service_pb';

describe('明細書PDFダウンロードURL取得API', () => {
  const apiClient = getClient(MemberClaimAndPayService);

  it('正常にダウンロードURLを取得できる', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // 請求・支払いレコードを作成
    const claimAndPay = await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-01-15'),
      claimAmount: 50000,
      payAmount: 30000,
      statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
    });

    // ===== Act =====
    const response = await apiClient.requestStatementPdfDownloadUrl(
      {
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
      },
      {
        headers: {
          sessionToken: session.sessionToken,
        },
      }
    );

    // ===== Assert =====
    expect(response.downloadUrl).toBeDefined();
    // テスト環境では実際のS3プリサインドURLが生成されない可能性があるため、
    // ダウンロードURLが正しく返されることを確認する
    expect(response.downloadUrl).toBeTruthy();
    // URLが有効な形式であることを確認
    expect(response.downloadUrl).toMatch(/^https:\/\/.*$/);
  });

  it('存在しないstatementFileKeyの場合はエラーを返す', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // 異なるstatementFileKeyのレコードを作成
    await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-01-15'),
      claimAmount: 50000,
      payAmount: 30000,
      statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
    });

    // ===== Act & Assert =====
    await expect(
      apiClient.requestStatementPdfDownloadUrl(
        {
          statementFileKey: 'statements/investment-and-return-10001-20242.pdf', // 存在しないファイルキー
        },
        {
          headers: {
            sessionToken: session.sessionToken,
          },
        }
      )
    ).rejects.toThrow();
  });

  it('他の会員のstatementFileKeyにはアクセスできない', async () => {
    // ===== Arrange =====
    const user1 = await UserFactory.create();
    const session1 = await UserSessionFactory.create({
      user: { connect: { userId: user1.userId } },
    });
    const member1 = await MemberFactory.create({
      user: { connect: { userId: user1.userId } },
    });

    const user2 = await UserFactory.create();
    const member2 = await MemberFactory.create({
      user: { connect: { userId: user2.userId } },
    });

    // member2のレコードを作成
    await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member2.memberId } },
      occurredDate: new Date('2024-01-15'),
      claimAmount: 50000,
      payAmount: 30000,
      statementFileKey: 'statements/investment-and-return-10002-20241.pdf',
    });

    // ===== Act & Assert =====
    await expect(
      apiClient.requestStatementPdfDownloadUrl(
        {
          statementFileKey: 'statements/investment-and-return-10002-20241.pdf', // member2のファイルキー
        },
        {
          headers: {
            sessionToken: session1.sessionToken, // member1のセッション
          },
        }
      )
    ).rejects.toThrow();
  });

  it('statementFileKeyがnullのレコードにはアクセスできない', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // statementFileKeyがnullのレコードを作成
    await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-01-15'),
      claimAmount: 50000,
      payAmount: 30000,
      statementFileKey: null,
    });

    // ===== Act & Assert =====
    await expect(
      apiClient.requestStatementPdfDownloadUrl(
        {
          statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
        },
        {
          headers: {
            sessionToken: session.sessionToken,
          },
        }
      )
    ).rejects.toThrow();
  });

  it('空のstatementFileKeyの場合はバリデーションエラーを返す', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // ===== Act & Assert =====
    await expect(
      apiClient.requestStatementPdfDownloadUrl(
        {
          statementFileKey: '',
        },
        {
          headers: {
            sessionToken: session.sessionToken,
          },
        }
      )
    ).rejects.toThrow();
  });

  it('statementFileKeyが未定義の場合はバリデーションエラーを返す', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // ===== Act & Assert =====
    await expect(
      apiClient.requestStatementPdfDownloadUrl(
        {
          statementFileKey: undefined as any,
        },
        {
          headers: {
            sessionToken: session.sessionToken,
          },
        }
      )
    ).rejects.toThrow();
  });

  it('認証されていない場合はエラーを返す', async () => {
    // ===== Act & Assert =====
    await expect(
      apiClient.requestStatementPdfDownloadUrl({
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
      })
    ).rejects.toThrow();
  });

  it('無効なセッショントークンの場合はエラーを返す', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // ===== Act & Assert =====
    await expect(
      apiClient.requestStatementPdfDownloadUrl(
        {
          statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
        },
        {
          headers: {
            sessionToken: 'invalid-token',
          },
        }
      )
    ).rejects.toThrow();
  });

  it('会員情報が存在しないユーザーの場合はエラーを返す', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    // 会員情報は作成しない

    // ===== Act & Assert =====
    await expect(
      apiClient.requestStatementPdfDownloadUrl(
        {
          statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
        },
        {
          headers: {
            sessionToken: session.sessionToken,
          },
        }
      )
    ).rejects.toThrow();
  });

  it('無効なファイルキーフォーマットの場合はエラーを返す', async () => {
    // ===== Arrange =====
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // 無効なファイルキーのレコードを作成
    await MemberClaimAndPayFactory.create({
      member: { connect: { memberId: member.memberId } },
      occurredDate: new Date('2024-01-15'),
      claimAmount: 50000,
      payAmount: 30000,
      statementFileKey: 'invalid-file-key.txt',
    });

    // ===== Act & Assert =====
    await expect(
      apiClient.requestStatementPdfDownloadUrl(
        {
          statementFileKey: 'invalid-file-key.txt',
        },
        {
          headers: {
            sessionToken: session.sessionToken,
          },
        }
      )
    ).rejects.toThrow();
  });
});
