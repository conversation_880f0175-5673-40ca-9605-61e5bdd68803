import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  ListRecruitmentHorseVideosResponseSchema,
  RecruitmentHorseVideoSchema,
} from '@hami/web-api-schema/recruitment_horse_service_pb';
import { DatabaseError } from '@web-api/repositories';
import { listHorseVideos } from '@web-api/repositories/owned_horse_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';

const schema = z.object({
  recruitmentYear: z.number().int().min(1),
  recruitmentNo: z.number().int().min(1),
  limit: z.number().int().min(1).optional(),
});

export const listRecruitmentHorseVideosHandler = createHandler({
  schema,
  business: (request) =>
    listHorseVideos({
      recruitmentYear: request.recruitmentYear,
      recruitmentNo: request.recruitmentNo,
      limit: request.limit,
    }),
  toResponse: (videos) =>
    create(ListRecruitmentHorseVideosResponseSchema, {
      videos: videos.map((v) =>
        create(RecruitmentHorseVideoSchema, {
          videoId: v.videoId,
          videoYear: v.videoYear,
          videoMonth: v.videoMonth,
          videoDay: v.videoDay,
          title: v.title,
          description: v.description ?? undefined,
          youtubeVideoId: v.youtubeVideoId,
          startAtSeconds: v.startAtSeconds ?? undefined,
          thumbnailUrl: undefined,
        })
      ),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});

