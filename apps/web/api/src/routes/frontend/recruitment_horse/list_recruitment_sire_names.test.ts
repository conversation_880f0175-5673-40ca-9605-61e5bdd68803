import { createPublishedRecruitmentHorse } from '@web-test/factories/recruitment_horse_factory';
import { RecruitmentHorseService } from '@hami/web-api-schema/recruitment_horse_service_pb';

describe('募集馬の父馬名一覧API', () => {
  it('対象年度の父馬名一覧が重複なく昇順で取得できる', async ({ getClient }) => {
    // ===== Arrange =====
    await createPublishedRecruitmentHorse({
      horse: { recruitmentYear: 2024 },
      profile: { sireName: 'ディープインパクト' },
    });
    await createPublishedRecruitmentHorse({
      horse: { recruitmentYear: 2024 },
      profile: { sireName: 'オルフェーヴル' },
    });
    await createPublishedRecruitmentHorse({
      horse: { recruitmentYear: 2024 },
      profile: { sireName: 'ディープインパクト' },
    });
    // 別年
    await createPublishedRecruitmentHorse({
      horse: { recruitmentYear: 2023 },
      profile: { sireName: 'ハーツクライ' },
    });

    const client = getClient(RecruitmentHorseService);

    // ===== Act =====
    const res = await client.listRecruitmentSireNames({ targetYear: 2024 });

    // ===== Assert =====
    expect(res.sireNames).toEqual(['オルフェーヴル', 'ディープインパクト']);
    expect(res.sireNames.includes('ハーツクライ')).toBe(false);
  });
});


