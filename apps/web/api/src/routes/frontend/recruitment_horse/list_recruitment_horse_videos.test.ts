import { Code, ConnectError } from '@connectrpc/connect';
import { createPublishedRecruitmentHorse } from '@web-test/factories/recruitment_horse_factory';
import { PublishStatus } from '@hami/prisma';
import { RecruitmentHorseService } from '@hami/web-api-schema/recruitment_horse_service_pb';
import { client as prismaClient } from '@web-api/utils/prisma';

describe('募集馬: 動画一覧取得API', () => {
  it('動画一覧を取得できる', async ({ getClient }) => {
    const { horse } = await createPublishedRecruitmentHorse({
      horse: {
        horseName: '募集馬動画テスト',
        recruitmentYear: 2023,
        birthYear: 2020,
      },
    });

    const videoData = [
      { year: 2024, month: 3, day: 25, title: '2024/3/25の動画', youtubeId: 'vid_325' },
      { year: 2024, month: 2, day: 15, title: '2024/2/15の動画', youtubeId: 'vid_215' },
      { year: 2024, month: 3, day: 10, title: '2024/3/10の動画', youtubeId: 'vid_310' },
      { year: 2023, month: 12, day: 30, title: '2023/12/30の動画', youtubeId: 'vid_1230' },
      { year: 2024, month: 3, day: 15, title: '2024/3/15の動画', youtubeId: 'vid_315' },
      { year: 2024, month: 1, day: 5, title: '2024/1/5の動画', youtubeId: 'vid_105' },
    ];

    for (const data of videoData) {
      await prismaClient.horseVideo.create({
        data: {
          horseId: horse.horseId,
          videoYear: data.year,
          videoMonth: data.month,
          videoDay: data.day,
          title: data.title,
          youtubeVideoId: data.youtubeId,
          publishStatus: PublishStatus.PUBLISHED,
        },
      });
    }

    const client = getClient(RecruitmentHorseService);
    const response = await client.listRecruitmentHorseVideos({
      recruitmentYear: horse.recruitmentYear,
      recruitmentNo: horse.recruitmentNo,
    });

    expect(response.videos.length).toBe(6);

    const expectedOrder = [
      '2024/3/25の動画',
      '2024/3/15の動画',
      '2024/3/10の動画',
      '2024/2/15の動画',
      '2024/1/5の動画',
      '2023/12/30の動画',
    ];
    for (let i = 0; i < expectedOrder.length; i++) {
      expect(response.videos[i].title).toBe(expectedOrder[i]);
    }
  });

  it('件数制限付きで動画一覧を取得できる', async ({ getClient }) => {
    const { horse } = await createPublishedRecruitmentHorse({
      horse: {
        horseName: 'limit募集動画テスト',
        recruitmentYear: 2023,
        birthYear: 2020,
      },
    });

    const videoData = [
      { year: 2024, month: 3, day: 20, title: '2024/3/20の動画', youtubeId: 'vid_320' },
      { year: 2024, month: 3, day: 5, title: '2024/3/5の動画', youtubeId: 'vid_35' },
      { year: 2024, month: 3, day: 15, title: '2024/3/15の動画', youtubeId: 'vid_315' },
      { year: 2024, month: 3, day: 25, title: '2024/3/25の動画', youtubeId: 'vid_325' },
      { year: 2024, month: 3, day: 10, title: '2024/3/10の動画', youtubeId: 'vid_310' },
    ];

    for (const data of videoData) {
      await prismaClient.horseVideo.create({
        data: {
          horseId: horse.horseId,
          videoYear: data.year,
          videoMonth: data.month,
          videoDay: data.day,
          title: data.title,
          youtubeVideoId: data.youtubeId,
          publishStatus: PublishStatus.PUBLISHED,
        },
      });
    }

    const client = getClient(RecruitmentHorseService);
    const response = await client.listRecruitmentHorseVideos({
      recruitmentYear: horse.recruitmentYear,
      recruitmentNo: horse.recruitmentNo,
      limit: 3,
    });

    expect(response.videos.length).toBe(3);
    const expectedOrder = ['2024/3/25の動画', '2024/3/20の動画', '2024/3/15の動画'];
    for (let i = 0; i < expectedOrder.length; i++) {
      expect(response.videos[i].title).toBe(expectedOrder[i]);
    }
  });

  it('公開されていない動画は表示されない', async ({ getClient }) => {
    const { horse } = await createPublishedRecruitmentHorse({});

    await prismaClient.horseVideo.create({
      data: {
        horseId: horse.horseId,
        videoYear: 2024,
        videoMonth: 3,
        videoDay: 15,
        title: '公開済み動画',
        youtubeVideoId: 'published_vid',
        publishStatus: PublishStatus.PUBLISHED,
      },
    });

    await prismaClient.horseVideo.create({
      data: {
        horseId: horse.horseId,
        videoYear: 2024,
        videoMonth: 3,
        videoDay: 10,
        title: '非公開動画',
        youtubeVideoId: 'draft_vid',
        publishStatus: PublishStatus.DRAFT,
      },
    });

    const client = getClient(RecruitmentHorseService);
    const response = await client.listRecruitmentHorseVideos({
      recruitmentYear: horse.recruitmentYear,
      recruitmentNo: horse.recruitmentNo,
    });

    expect(response.videos.length).toBe(1);
    expect(response.videos[0].title).toBe('公開済み動画');
  });

  it('存在しない馬を指定した場合は空の配列を返す', async ({ getClient }) => {
    const client = getClient(RecruitmentHorseService);
    const response = await client.listRecruitmentHorseVideos({
      recruitmentYear: 999999,
      recruitmentNo: 999999,
    });
    expect(response.videos.length).toBe(0);
  });

  it('無効なパラメータは400エラー', async ({ getClient }) => {
    const client = getClient(RecruitmentHorseService);

    let error: any;
    try {
      await client.listRecruitmentHorseVideos({ recruitmentYear: -1, recruitmentNo: -1 });
    } catch (e) {
      error = e;
    }
    expect(error).toBeInstanceOf(ConnectError);
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }

    error = undefined;
    try {
      await client.listRecruitmentHorseVideos({ recruitmentYear: 2023, recruitmentNo: 1, limit: 0 });
    } catch (e) {
      error = e;
    }
    expect(error).toBeInstanceOf(ConnectError);
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });
});

