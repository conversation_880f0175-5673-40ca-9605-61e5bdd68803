import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { z } from 'zod';
import { ListRecruitmentSireNamesResponseSchema } from '@hami/web-api-schema/recruitment_horse_service_pb';
import { DatabaseError } from '@web-api/repositories/index';
import { listRecruitmentSireNames } from '@web-api/repositories/recruitment_horse_repository';
import { createHandler } from '@web-api/utils/handler_factory';

const schema = z.object({
  targetYear: z.number().int().min(2000).max(2100),
});

type Request = z.infer<typeof schema>;

const business = ({ targetYear }: Request) =>
  listRecruitmentSireNames({ targetYear });

export const listRecruitmentSireNamesHandler = createHandler({
  schema,
  business,
  toResponse: (names) =>
    create(ListRecruitmentSireNamesResponseSchema, {
      sireNames: names,
    }),
  toError: (error) =>
    error instanceof DatabaseError
      ? new ConnectError('Internal server error', Code.Internal)
      : new ConnectError('Invalid request', Code.InvalidArgument),
});


