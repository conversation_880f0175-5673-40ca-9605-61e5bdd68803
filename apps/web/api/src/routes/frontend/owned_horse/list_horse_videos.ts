import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { okAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  ListHorseVideosResponseSchema,
  HorseVideoSchema,
} from '@hami/web-api-schema/owned_horse_service_pb';
import { DatabaseError } from '@web-api/repositories';
import { listHorseVideos } from '@web-api/repositories/owned_horse_repository';
import { findUserBySessionToken } from '@web-api/repositories/user_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';
import type { HandlerContext } from '@connectrpc/connect';

const schema = z.object({
  recruitmentYear: z.number().int().min(1),
  recruitmentNo: z.number().int().min(1),
  limit: z.number().int().min(1).optional(),
});

type ListHorseVideosRequest = z.infer<typeof schema>;

const listHorseVideosBusiness = (request: ListHorseVideosRequest, ctx: HandlerContext) => {
  // セッショントークンを取得
  const sessionToken = ctx.requestHeader.get('sessionToken');

  // セッショントークンがない場合は空の配列を返す
  if (!sessionToken) {
    return okAsync([]);
  }

  // セッショントークンでユーザー認証を行う
  return findUserBySessionToken({ sessionToken })
    .andThen(() =>
      listHorseVideos({
        recruitmentYear: request.recruitmentYear,
        recruitmentNo: request.recruitmentNo,
        limit: request.limit,
      })
    )
    .orElse(() => okAsync([])); // 認証失敗時は空配列
};

export const listHorseVideosHandler = createHandler({
  schema,
  business: listHorseVideosBusiness,
  toResponse: (videos) => {
    const items = videos.map((v) =>
      create(HorseVideoSchema, {
        videoId: v.videoId,
        videoYear: v.videoYear,
        videoMonth: v.videoMonth,
        videoDay: v.videoDay,
        title: v.title,
        description: v.description ?? undefined,
        youtubeVideoId: v.youtubeVideoId,
        startAtSeconds: v.startAtSeconds ?? undefined,
        // 署名付きURL化は要件次第。現状はDBのパスをそのまま通知しないため undefined にする
        thumbnailUrl: undefined,
      })
    );

    return create(ListHorseVideosResponseSchema, {
      videos: items,
    });
  },
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('データベースエラーが発生しました', Code.Internal))
      .exhaustive(),
});

