import { Code, ConnectError } from '@connectrpc/connect';
import { MemberFactory } from '@web-test/factories/member_factory';
import { createPublishedRecruitmentHorse } from '@web-test/factories/recruitment_horse_factory';
import { UserFactory } from '@web-test/factories/user_factory';
import { UserSessionFactory } from '@web-test/factories/user_session_factory';
import { PublishStatus } from '@hami/prisma';
import { OwnedHorseService } from '@hami/web-api-schema/owned_horse_service_pb';

describe('動画一覧取得API', () => {
  it('動画一覧を取得できる', async ({ getClient }) => {
    // ユーザーとセッション作成
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // 公開済みの募集馬を作成
    const { horse } = await createPublishedRecruitmentHorse({
      horse: {
        horseName: '動画テスト馬',
        recruitmentYear: 2023,
        birthYear: 2020,
      },
      profile: {
        publishStatus: PublishStatus.PUBLISHED,
      },
    });

    // 複数年月日の動画を作成（作成順はランダム）
    const videoData = [
      { year: 2024, month: 3, day: 25, title: '2024/3/25の動画', youtubeId: 'vid_325' },
      { year: 2024, month: 2, day: 15, title: '2024/2/15の動画', youtubeId: 'vid_215' },
      { year: 2024, month: 3, day: 10, title: '2024/3/10の動画', youtubeId: 'vid_310' },
      { year: 2023, month: 12, day: 30, title: '2023/12/30の動画', youtubeId: 'vid_1230' },
      { year: 2024, month: 3, day: 15, title: '2024/3/15の動画', youtubeId: 'vid_315' },
      { year: 2024, month: 1, day: 5, title: '2024/1/5の動画', youtubeId: 'vid_105' },
    ];

    for (const data of videoData) {
      await vPrisma.client.horseVideo.create({
        data: {
          horseId: horse.horseId,
          videoYear: data.year,
          videoMonth: data.month,
          videoDay: data.day,
          title: data.title,
          description: data.title + ' の説明',
          youtubeVideoId: data.youtubeId,
          publishStatus: PublishStatus.PUBLISHED,
        },
      });
    }

    // APIの実行
    const client = getClient(OwnedHorseService);
    const response = await client.listHorseVideos(
      {
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
      },
      {
        headers: {
          sessionToken: session.sessionToken,
        },
      }
    );

    // 結果の検証
    expect(response.videos.length).toBe(6);

    const expectedOrder = [
      '2024/3/25の動画',
      '2024/3/15の動画',
      '2024/3/10の動画',
      '2024/2/15の動画',
      '2024/1/5の動画',
      '2023/12/30の動画',
    ];

    for (let i = 0; i < expectedOrder.length; i++) {
      expect(response.videos[i].title).toBe(expectedOrder[i]);
    }

    const latest = response.videos[0];
    expect(latest.videoYear).toBe(2024);
    expect(latest.videoMonth).toBe(3);
    expect(latest.videoDay).toBe(25);
    expect(latest.youtubeVideoId).toBe('vid_325');
  });

  it('件数制限付きで動画一覧を取得できる', async ({ getClient }) => {
    // ユーザーとセッション作成
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    // 公開済みの募集馬を作成
    const { horse } = await createPublishedRecruitmentHorse({
      horse: {
        horseName: 'limit動画テスト馬',
        recruitmentYear: 2023,
        birthYear: 2020,
      },
      profile: {
        publishStatus: PublishStatus.PUBLISHED,
      },
    });

    // 5件の動画を作成
    const videoData = [
      { year: 2024, month: 3, day: 20, title: '2024/3/20の動画', youtubeId: 'vid_320' },
      { year: 2024, month: 3, day: 5, title: '2024/3/5の動画', youtubeId: 'vid_35' },
      { year: 2024, month: 3, day: 15, title: '2024/3/15の動画', youtubeId: 'vid_315' },
      { year: 2024, month: 3, day: 25, title: '2024/3/25の動画', youtubeId: 'vid_325' },
      { year: 2024, month: 3, day: 10, title: '2024/3/10の動画', youtubeId: 'vid_310' },
    ];

    for (const data of videoData) {
      await vPrisma.client.horseVideo.create({
        data: {
          horseId: horse.horseId,
          videoYear: data.year,
          videoMonth: data.month,
          videoDay: data.day,
          title: data.title,
          youtubeVideoId: data.youtubeId,
          publishStatus: PublishStatus.PUBLISHED,
        },
      });
    }

    // 件数制限付きでAPI実行
    const client = getClient(OwnedHorseService);
    const response = await client.listHorseVideos(
      {
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
        limit: 3,
      },
      {
        headers: {
          sessionToken: session.sessionToken,
        },
      }
    );

    expect(response.videos.length).toBe(3);

    const expectedOrder = ['2024/3/25の動画', '2024/3/20の動画', '2024/3/15の動画'];
    for (let i = 0; i < expectedOrder.length; i++) {
      expect(response.videos[i].title).toBe(expectedOrder[i]);
    }
  });

  it('公開されていない動画は表示されない', async ({ getClient }) => {
    // ユーザーとセッション作成
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    const { horse } = await createPublishedRecruitmentHorse({
      horse: {
        horseName: '公開状態動画テスト馬',
        recruitmentYear: 2023,
        birthYear: 2020,
      },
      profile: {
        publishStatus: PublishStatus.PUBLISHED,
      },
    });

    await vPrisma.client.horseVideo.create({
      data: {
        horseId: horse.horseId,
        videoYear: 2024,
        videoMonth: 3,
        videoDay: 15,
        title: '公開済み動画',
        youtubeVideoId: 'published_vid',
        publishStatus: PublishStatus.PUBLISHED,
      },
    });

    await vPrisma.client.horseVideo.create({
      data: {
        horseId: horse.horseId,
        videoYear: 2024,
        videoMonth: 3,
        videoDay: 10,
        title: '非公開動画',
        youtubeVideoId: 'draft_vid',
        publishStatus: PublishStatus.DRAFT,
      },
    });

    const client = getClient(OwnedHorseService);
    const response = await client.listHorseVideos(
      {
        recruitmentYear: horse.recruitmentYear,
        recruitmentNo: horse.recruitmentNo,
      },
      {
        headers: {
          sessionToken: session.sessionToken,
        },
      }
    );

    expect(response.videos.length).toBe(1);
    expect(response.videos[0].title).toBe('公開済み動画');
  });

  it('存在しない馬を指定した場合は空の配列を返す', async ({ getClient }) => {
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    const client = getClient(OwnedHorseService);
    const response = await client.listHorseVideos(
      {
        recruitmentYear: 999999,
        recruitmentNo: 999999,
      },
      {
        headers: { sessionToken: session.sessionToken },
      }
    );

    expect(response.videos.length).toBe(0);
  });

  it('無効なパラメータは400エラー', async ({ getClient }) => {
    const user = await UserFactory.create();
    const session = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    const client = getClient(OwnedHorseService);

    // recruitmentYear / recruitmentNo が不正
    let error: any;
    try {
      await client.listHorseVideos({ recruitmentYear: -1, recruitmentNo: -1 }, { headers: { sessionToken: session.sessionToken } });
    } catch (e) {
      error = e;
    }
    expect(error).toBeInstanceOf(ConnectError);
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }

    // limit が不正
    error = undefined;
    try {
      await client.listHorseVideos(
        { recruitmentYear: 2023, recruitmentNo: 1, limit: 0 },
        { headers: { sessionToken: session.sessionToken } }
      );
    } catch (e) {
      error = e;
    }
    expect(error).toBeInstanceOf(ConnectError);
    if (error instanceof ConnectError) {
      expect(error.code).toBe(Code.InvalidArgument);
    }
  });

  it('認証なし（sessionTokenなし）の場合は0件を返す', async ({ getClient }) => {
    const { horse } = await createPublishedRecruitmentHorse({
      horse: {
        horseName: '認証なし動画テスト馬',
        recruitmentYear: 2023,
        birthYear: 2020,
      },
      profile: {
        publishStatus: PublishStatus.PUBLISHED,
      },
    });

    await vPrisma.client.horseVideo.create({
      data: {
        horseId: horse.horseId,
        videoYear: 2024,
        videoMonth: 3,
        videoDay: 15,
        title: '認証なしでは見えない動画',
        youtubeVideoId: 'public_vid',
        publishStatus: PublishStatus.PUBLISHED,
      },
    });

    const client = getClient(OwnedHorseService);
    const response = await client.listHorseVideos({
      recruitmentYear: horse.recruitmentYear,
      recruitmentNo: horse.recruitmentNo,
    });

    expect(response.videos.length).toBe(0);
  });

  it('無効/期限切れのsessionTokenの場合は0件を返す', async ({ getClient }) => {
    const user = await UserFactory.create();
    const expiredSession = await UserSessionFactory.create({
      user: { connect: { userId: user.userId } },
      expiresAt: new Date(Date.now() - 1000),
    });
    const member = await MemberFactory.create({
      user: { connect: { userId: user.userId } },
    });

    const { horse } = await createPublishedRecruitmentHorse({
      horse: {
        horseName: '期限切れ動画テスト馬',
        recruitmentYear: 2023,
        birthYear: 2020,
      },
      profile: {
        publishStatus: PublishStatus.PUBLISHED,
      },
    });

    await vPrisma.client.horseVideo.create({
      data: {
        horseId: horse.horseId,
        videoYear: 2024,
        videoMonth: 3,
        videoDay: 15,
        title: '期限切れトークンでは見えない動画',
        youtubeVideoId: 'public_vid',
        publishStatus: PublishStatus.PUBLISHED,
      },
    });

    const client = getClient(OwnedHorseService);

    // 無効なトークン
    const responseInvalid = await client.listHorseVideos(
      { recruitmentYear: horse.recruitmentYear, recruitmentNo: horse.recruitmentNo },
      { headers: { sessionToken: 'invalid-session-token' } }
    );
    expect(responseInvalid.videos.length).toBe(0);

    // 期限切れトークン
    const responseExpired = await client.listHorseVideos(
      { recruitmentYear: horse.recruitmentYear, recruitmentNo: horse.recruitmentNo },
      { headers: { sessionToken: expiredSession.sessionToken } }
    );
    expect(responseExpired.videos.length).toBe(0);
  });
});
