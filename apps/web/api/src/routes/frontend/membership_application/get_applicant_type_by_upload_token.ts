import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetApplicantTypeByUploadTokenResponseSchema, ApplicantType } from '@hami/web-api-schema/membership_application_service_pb';
import { DatabaseError } from '@web-api/repositories/index';
import {
  findDocumentGroupByUploadToken,
  MembershipApplicationNotFoundError,
} from '@web-api/repositories/membership_application_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';

const schema = z.object({
  uploadToken: z.string().min(1),
});

export const getApplicantTypeByUploadToken = createHandler({
  schema,
  business: ({ uploadToken }) =>
    findDocumentGroupByUploadToken({ uploadToken }).andThen((dg) => {
      const type = dg.membershipApplication.applicantType;
      const applicantType = type === 'CORPORATE' ? ApplicantType.CORPORATE : ApplicantType.INDIVIDUAL;
      return ok({ applicantType, isCompleted: dg.isCompleted });
    }),
  toResponse: ({ applicantType, isCompleted }: { applicantType: ApplicantType; isCompleted: boolean }) =>
    create(GetApplicantTypeByUploadTokenResponseSchema, { applicantType, isCompleted }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(
        P.instanceOf(MembershipApplicationNotFoundError),
        () => new ConnectError('無効なアップロードトークンです', Code.InvalidArgument)
      )
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
