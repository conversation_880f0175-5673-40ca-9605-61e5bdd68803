import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetUploadedDocumentsResponseSchema, UploadedDocumentSchema } from '@hami/web-api-schema/membership_application_service_pb';
import { DatabaseError } from '@web-api/repositories/index';
import {
  findDocumentGroupByUploadToken,
  MembershipApplicationNotFoundError,
} from '@web-api/repositories/membership_application_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { getPresignedDownloadUrl } from '@web-api/utils/s3';
import { ValidationError } from '@web-api/utils/validate_request';

const schema = z.object({
  uploadToken: z.string().min(1, 'アップロードトークンは必須です'),
});

export const getUploadedDocuments = createHandler({
  schema,
  business: ({ uploadToken }) =>
    findDocumentGroupByUploadToken({ uploadToken }).andThen((group) => {
      const expiresIn = 60 * 60; // 1時間（秒）
      return ResultAsync.fromPromise(
        Promise.all(
          group.documents.map(async (doc) => {
            const url = await getPresignedDownloadUrl(doc.fileKey, expiresIn);
            return {
              identityDocumentId: doc.membershipApplicationIdentityDocumentId,
              imageUrl: url,
              // epoch seconds
              expiresAt: Math.floor(Date.now() / 1000) + expiresIn,
              personalIndex: doc.personalIndex ?? undefined,
            };
          })
        ),
        (e) => {
          console.error('Failed to generate presigned download URLs:', e);
          return new DatabaseError('ダウンロードURLの生成に失敗しました');
        }
      );
    }),
  toResponse: (
    docs: Array<{
      identityDocumentId: number;
      imageUrl: string;
      expiresAt: number;
      personalIndex?: number;
    }>
  ) =>
    create(GetUploadedDocumentsResponseSchema, {
      documents: docs.map((d) =>
        create(UploadedDocumentSchema, {
          identityDocumentId: d.identityDocumentId,
          imageUrl: d.imageUrl,
          expiresAt: BigInt(d.expiresAt),
          personalIndex: d.personalIndex,
        })
      ),
    }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(
        P.instanceOf(MembershipApplicationNotFoundError),
        () => new ConnectError('無効なアップロードトークンです', Code.InvalidArgument)
      )
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
