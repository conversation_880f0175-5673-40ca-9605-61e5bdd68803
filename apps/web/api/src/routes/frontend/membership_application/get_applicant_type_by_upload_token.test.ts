import { ConnectError } from '@connectrpc/connect';
import { MailVerificationFactory } from '@web-test/factories/mail_verification_factory';
import { MembershipApplicationDocumentGroupFactory } from '@web-test/factories/membership_application_document_group_factory';
import { MembershipApplicationFactory } from '@web-test/factories/membership_application_factory';
import { MembershipApplicationService, ApplicantType } from '@hami/web-api-schema/membership_application_service_pb';

describe('getApplicantTypeByUploadToken', () => {
  describe('正常系', () => {
    it('個人申込の場合、INDIVIDUAL と isCompleted=false を返す', async ({ getClient }) => {
      // Arrange
      const mailVerification = await MailVerificationFactory.create();
      const membershipApplication = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
        applicantType: 'INDIVIDUAL',
      });

      const documentGroup = await MembershipApplicationDocumentGroupFactory.create({
        membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
        uploadToken: 'test-upload-token-individual',
        groupKey: 'identity_verification',
        isCompleted: false,
      });

      // Act
      const client = getClient(MembershipApplicationService);
      const res = await client.getApplicantTypeByUploadToken({
        uploadToken: documentGroup.uploadToken,
      });

      // Assert
      expect(res.applicantType).toBe(ApplicantType.INDIVIDUAL);
      expect(res.isCompleted).toBe(false);
    });

    it('法人申込の場合、CORPORATE と isCompleted=false を返す', async ({ getClient }) => {
      // Arrange
      const mailVerification = await MailVerificationFactory.create();
      const membershipApplication = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
        applicantType: 'CORPORATE',
      });

      const documentGroup = await MembershipApplicationDocumentGroupFactory.create({
        membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
        uploadToken: 'test-upload-token-corporate',
        groupKey: 'identity_verification',
        isCompleted: false,
      });

      // Act
      const client = getClient(MembershipApplicationService);
      const res = await client.getApplicantTypeByUploadToken({
        uploadToken: documentGroup.uploadToken,
      });

      // Assert
      expect(res.applicantType).toBe(ApplicantType.CORPORATE);
      expect(res.isCompleted).toBe(false);
    });

    it('アップロード完了済みの場合、isCompleted=true を返す', async ({ getClient }) => {
      // Arrange
      const mailVerification = await MailVerificationFactory.create();
      const membershipApplication = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
        applicantType: 'CORPORATE',
      });

      const documentGroup = await MembershipApplicationDocumentGroupFactory.create({
        membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
        uploadToken: 'test-upload-token-completed',
        groupKey: 'identity_verification',
        isCompleted: true,
      });

      // Act
      const client = getClient(MembershipApplicationService);
      const res = await client.getApplicantTypeByUploadToken({
        uploadToken: documentGroup.uploadToken,
      });

      // Assert
      expect(res.applicantType).toBe(ApplicantType.CORPORATE);
      expect(res.isCompleted).toBe(true);
    });
  });

  describe('異常系', () => {
    it('無効なuploadTokenの場合はエラーを返す', async ({ getClient }) => {
      const request = {
        uploadToken: 'invalid-token',
      };
      const client = getClient(MembershipApplicationService);
      let error: unknown;
      try {
        await client.getApplicantTypeByUploadToken(request);
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
      if (error instanceof ConnectError) {
        expect(error.message).toContain('無効なアップロードトークンです');
      }
    });
  });
});
