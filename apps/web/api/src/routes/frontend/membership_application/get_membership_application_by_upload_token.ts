import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import {
  GetMembershipApplicationByUploadTokenResponseSchema,
  ApplicantType,
  BeneficialOwnerDeclarationSchema,
} from '@hami/web-api-schema/membership_application_service_pb';
import { DatabaseError } from '@web-api/repositories/index';
import {
  findDocumentGroupByUploadToken,
  MembershipApplicationNotFoundError,
} from '@web-api/repositories/membership_application_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { ValidationError } from '@web-api/utils/validate_request';

const schema = z.object({
  uploadToken: z.string().min(1, 'アップロードトークンは必須です'),
});

export const getMembershipApplicationByUploadToken = createHandler({
  schema,
  business: ({ uploadToken }) =>
    findDocumentGroupByUploadToken({ uploadToken }).andThen((documentGroup) => {
      const app = documentGroup.membershipApplication;
      const applicantType = app.applicantType === 'CORPORATE' ? ApplicantType.CORPORATE : ApplicantType.INDIVIDUAL;

      return ok({
        applicantType,
        lastName: app.lastName ?? undefined,
        firstName: app.firstName ?? undefined,
        lastNameKana: app.lastNameKana ?? undefined,
        firstNameKana: app.firstNameKana ?? undefined,
        birthYear: app.birthYear ?? undefined,
        birthMonth: app.birthMonth ?? undefined,
        birthDay: app.birthDay ?? undefined,
        occupation: app.occupation ?? undefined,
        // 金融情報
        annualIncome: app.annualIncome ?? undefined,
        depositAmount: app.depositAmount ?? undefined,
        financialAssets: app.financialAssets ?? undefined,
        // 勤務先情報
        companyName: app.companyName ?? undefined,
        companyAddress: app.companyAddress ?? undefined,
        companyPhoneNumber: app.companyPhoneNumber ?? undefined,
        // 法人用フィールド
        corporateName: app.corporateName ?? undefined,
        corporateNameKana: app.corporateNameKana ?? undefined,
        representativeName: app.representativeName ?? undefined,
        representativeNameKana: app.representativeNameKana ?? undefined,
        representativePosition: app.representativePosition ?? undefined,
        corporateNumber: app.corporateNumber ?? undefined,
        establishedYear: app.establishedYear ?? undefined,
        establishedMonth: app.establishedMonth ?? undefined,
        establishedDay: app.establishedDay ?? undefined,
        // 共通フィールド
        postalCode: app.postalCode ?? undefined,
        prefecture: app.prefecture ?? undefined,
        address: app.address ?? undefined,
        apartment: app.apartment ?? undefined,
        phoneNumber: app.phoneNumber ?? undefined,
        // 実質的支配者申告
        beneficialOwnerDeclarations: app.beneficialOwnerDeclarations.map((decl) =>
          create(BeneficialOwnerDeclarationSchema, {
            beneficialOwnerName: decl.beneficialOwnerName,
            beneficialOwnerBirthYear: decl.beneficialOwnerBirthYear,
            beneficialOwnerBirthMonth: decl.beneficialOwnerBirthMonth,
            beneficialOwnerBirthDay: decl.beneficialOwnerBirthDay,
            beneficialOwnerPostalCode: decl.beneficialOwnerPostalCode,
            beneficialOwnerPrefecture: decl.beneficialOwnerPrefecture,
            beneficialOwnerAddress: decl.beneficialOwnerAddress,
            beneficialOwnerApartment: decl.beneficialOwnerApartment ?? undefined,
            declarantName: decl.declarantName,
            declarantPosition: decl.declarantPosition,
            isConfirmed: decl.isConfirmed,
          })
        ),
      });
    }),
  toResponse: (data) => create(GetMembershipApplicationByUploadTokenResponseSchema, data),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(
        P.instanceOf(MembershipApplicationNotFoundError),
        () => new ConnectError('無効なアップロードトークンです', Code.InvalidArgument)
      )
      .with(P.instanceOf(DatabaseError), () => new ConnectError('データベースエラーが発生しました', Code.Internal))
      .otherwise(() => new ConnectError('Internal server error', Code.Internal)),
});
