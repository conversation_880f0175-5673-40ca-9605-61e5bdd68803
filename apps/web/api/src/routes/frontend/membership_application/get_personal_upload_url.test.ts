import { ConnectError } from '@connectrpc/connect';
import { MailVerificationFactory } from '@web-test/factories/mail_verification_factory';
import { MembershipApplicationDocumentGroupFactory } from '@web-test/factories/membership_application_document_group_factory';
import { MembershipApplicationFactory } from '@web-test/factories/membership_application_factory';
import { clearTestBucket, ensureTestBucket } from '@web-test/s3';
import { MembershipApplicationService } from '@hami/web-api-schema/membership_application_service_pb';

describe('getPersonalUploadUrl', () => {
  beforeAll(async () => {
    await ensureTestBucket();
  });

  beforeEach(async () => {
    await clearTestBucket();
  });

  describe('正常系', () => {
    it('有効なuploadTokenとfileTypeでS3署名付きURLが取得できる（個人: personalIndex=1）', async ({ getClient }) => {
      // Arrange
      const mailVerification = await MailVerificationFactory.create();
      const membershipApplication = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: {
            mailVerificationId: mailVerification.mailVerificationId,
          },
        },
        // applicantTypeのデフォルトはINDIVIDUAL
      });

      const documentGroup = await MembershipApplicationDocumentGroupFactory.create({
        membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
        uploadToken: '12345678-1234-1234-1234-123456789abc',
        groupKey: 'identity_verification',
      });

      const request = {
        fileType: 'image/jpeg',
        uploadToken: documentGroup.uploadToken,
        personalIndex: 1,
      };

      // Act
      const client = getClient(MembershipApplicationService);
      const res = await client.getPersonalUploadUrl(request);

      // Assert
      expect(res.url).toMatch(/^https?:\/\/.+/);
      expect(res.url).toContain('identity/');
    });

    it('PDFファイルタイプでも正常に動作する（個人: personalIndex=2）', async ({ getClient }) => {
      // Arrange
      const mailVerification = await MailVerificationFactory.create();
      const membershipApplication = await MembershipApplicationFactory.create({
        mailVerification: {
          connect: { mailVerificationId: mailVerification.mailVerificationId },
        },
      });

      const documentGroup = await MembershipApplicationDocumentGroupFactory.create({
        membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
        uploadToken: '87654321-4321-4321-4321-cba987654321',
        groupKey: 'identity_verification',
      });

      const request = {
        fileType: 'application/pdf',
        uploadToken: documentGroup.uploadToken,
        personalIndex: 2,
      };

      // Act
      const client = getClient(MembershipApplicationService);
      const res = await client.getPersonalUploadUrl(request);

      // Assert
      expect(res.url).toMatch(/^https?:\/\/.+/);
      expect(res.url).toContain('identity/');
      expect(res.url).toContain('.pdf');
    });
  });

  describe('異常系', () => {
    it('無効なuploadTokenの場合はエラーを返す', async ({ getClient }) => {
      const request = {
        fileType: 'image/jpeg',
        uploadToken: 'invalid-token',
        personalIndex: 1,
      };

      const client = getClient(MembershipApplicationService);
      let error: unknown;
      try {
        await client.getPersonalUploadUrl(request);
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
      if (error instanceof ConnectError) {
        expect(error.message).toContain('無効な認証トークンです');
      }
    });

    it('サポートされていないファイルタイプの場合はエラーを返す', async ({ getClient }) => {
      const mailVerification = await MailVerificationFactory.create();
      const membershipApplication = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
      });

      const documentGroup = await MembershipApplicationDocumentGroupFactory.create({
        membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
        uploadToken: '11111111-2222-3333-4444-555555555555',
        groupKey: 'identity_verification',
      });

      const request = {
        fileType: 'text/plain',
        uploadToken: documentGroup.uploadToken,
        personalIndex: 3,
      };

      const client = getClient(MembershipApplicationService);
      let error: unknown;
      try {
        await client.getPersonalUploadUrl(request);
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
      if (error instanceof ConnectError) {
        expect(error.message).toContain('サポートされていないファイルタイプです');
      }
    });

    it('fileTypeが空文字の場合はバリデーションエラーを返す', async ({ getClient }) => {
      const request = { fileType: '', uploadToken: 'some-token', personalIndex: 1 };
      const client = getClient(MembershipApplicationService);
      let error: unknown;
      try {
        await client.getPersonalUploadUrl(request);
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
    });

    it('uploadTokenが空文字の場合はバリデーションエラーを返す', async ({ getClient }) => {
      const request = { fileType: 'image/jpeg', uploadToken: '', personalIndex: 1 };
      const client = getClient(MembershipApplicationService);
      let error: unknown;
      try {
        await client.getPersonalUploadUrl(request);
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
    });
  });
});
