import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok, err, ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { GetUploadUrlResponseSchema } from '@hami/web-api-schema/membership_application_service_pb';
import { DatabaseError } from '@web-api/repositories/index';
import {
  findDocumentGroupByUploadToken,
  createMembershipApplicationIdentityDocument,
  MembershipApplicationNotFoundError,
} from '@web-api/repositories/membership_application_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { getPresignedUploadUrl } from '@web-api/utils/s3';
import { ValidationError } from '@web-api/utils/validate_request';

// 許可されたファイルタイプ
const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'] as const;
type AllowedFileType = (typeof ALLOWED_FILE_TYPES)[number];

const schema = z.object({
  fileType: z.string().min(1),
  uploadToken: z.string().min(1),
  personalIndex: z.number().int().min(1).max(4),
});

const isAllowedFileType = (fileType: string): fileType is AllowedFileType => ALLOWED_FILE_TYPES.some((t) => t === fileType);

const validateFileType = (fileType: string) =>
  isAllowedFileType(fileType) ? ok(fileType) : err(new ValidationError(`サポートされていないファイルタイプです: ${fileType}`));

export const getPersonalUploadUrl = createHandler({
  schema,
  business: (req) =>
    ok(req)
      .andThen(({ fileType }) => validateFileType(fileType).map(() => req))
      .asyncAndThen((req) =>
        findDocumentGroupByUploadToken({ uploadToken: req.uploadToken }).andThen((documentGroup) => {
          const applicantType = documentGroup.membershipApplication.applicantType;
          if (applicantType !== 'INDIVIDUAL') {
            return err(new ValidationError('このAPIは個人申込のみ利用できます'));
          }
          return ok({ req, documentGroup });
        })
      )
      .andThen(({ req, documentGroup }) => {
        const timestamp = Date.now();
        const extension = req.fileType === 'application/pdf' ? 'pdf' : 'jpg';
        const filename = `${timestamp}.${extension}`;
        const key = `identity/${filename}`;

        return ResultAsync.fromPromise(getPresignedUploadUrl(key, req.fileType), () => {
          return new DatabaseError('S3署名付きURLの生成に失敗しました');
        }).andThen(({ url, key }) =>
          createMembershipApplicationIdentityDocument({
            membershipApplicationId: documentGroup.membershipApplicationId,
            fileKey: key,
            personalIndex: req.personalIndex,
            documentGroupId: documentGroup.membershipApplicationDocumentGroupId,
          }).map(() => ({ url }))
        );
      }),
  toResponse: ({ url }: { url: string }) => create(GetUploadUrlResponseSchema, { url }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(MembershipApplicationNotFoundError), () => new ConnectError('無効な認証トークンです', Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
