import { create } from '@bufbuild/protobuf';
import { Code, ConnectError } from '@connectrpc/connect';
import { ok, err, ResultAsync } from 'neverthrow';
import { match, P } from 'ts-pattern';
import { z } from 'zod';
import { FileType } from '@hami/prisma';
import { GetUploadUrlResponseSchema, DocumentType } from '@hami/web-api-schema/membership_application_service_pb';
import { DatabaseError } from '@web-api/repositories/index';
import {
  findDocumentGroupByUploadToken,
  createMembershipApplicationIdentityDocument,
  MembershipApplicationNotFoundError,
} from '@web-api/repositories/membership_application_repository';
import { createHandler } from '@web-api/utils/handler_factory';
import { getPresignedUploadUrl } from '@web-api/utils/s3';
import { ValidationError } from '@web-api/utils/validate_request';

// 許可されたファイルタイプ
const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'] as const;
type AllowedFileType = (typeof ALLOWED_FILE_TYPES)[number];

// DocumentType -> Prisma.FileType マッピング（法人用）
const DOCUMENT_TYPE_MAPPING: Partial<Record<DocumentType, FileType>> = {
  [DocumentType.CORP_SEAL_CERT]: FileType.CORP_SEAL_CERT,
  [DocumentType.CORP_REGISTRY_CERT]: FileType.CORP_REGISTRY_CERT,
};

const schema = z.object({
  fileType: z.string().min(1),
  uploadToken: z.string().min(1),
  documentType: z.nativeEnum(DocumentType),
});

const isAllowedFileType = (fileType: string): fileType is AllowedFileType => ALLOWED_FILE_TYPES.some((t) => t === fileType);

const validateFileType = (fileType: string) =>
  isAllowedFileType(fileType) ? ok(fileType) : err(new ValidationError(`サポートされていないファイルタイプです: ${fileType}`));

const validateCorporateDocumentType = (documentType: DocumentType) => {
  const isCorporate = documentType === DocumentType.CORP_SEAL_CERT || documentType === DocumentType.CORP_REGISTRY_CERT;
  return isCorporate ? ok(documentType) : err(new ValidationError('法人の本人確認書類アップロードでは CORP_* の documentType が必要です'));
};

export const getCorporateUploadUrl = createHandler({
  schema,
  business: (req) =>
    ok(req)
      .andThen(({ fileType }) => validateFileType(fileType).map(() => req))
      .andThen(({ documentType }) => validateCorporateDocumentType(documentType).map(() => req))
      .asyncAndThen((req) =>
        findDocumentGroupByUploadToken({ uploadToken: req.uploadToken }).andThen((documentGroup) => {
          const applicantType = documentGroup.membershipApplication.applicantType;
          if (applicantType !== 'CORPORATE') {
            return err(new ValidationError('このAPIは法人申込のみ利用できます'));
          }
          return ok({ req, documentGroup });
        })
      )
      .andThen(({ req, documentGroup }) => {
        const timestamp = Date.now();
        const extension = req.fileType === 'application/pdf' ? 'pdf' : 'jpg';
        const filename = `${timestamp}.${extension}`;
        const key = `identity/${filename}`;

        return ResultAsync.fromPromise(getPresignedUploadUrl(key, req.fileType), (error) => {
          console.error('Failed to generate presigned URL:', error);
          return new DatabaseError('S3署名付きURLの生成に失敗しました');
        }).andThen(({ url, key }) => {
          const prismaFileType = DOCUMENT_TYPE_MAPPING[req.documentType] || FileType.CORP_SEAL_CERT;
          return createMembershipApplicationIdentityDocument({
            membershipApplicationId: documentGroup.membershipApplicationId,
            fileKey: key,
            fileType: prismaFileType,
            documentGroupId: documentGroup.membershipApplicationDocumentGroupId,
          }).map(() => ({ url }));
        });
      }),
  toResponse: ({ url }: { url: string }) => create(GetUploadUrlResponseSchema, { url }),
  toError: (error) =>
    match(error)
      .with(P.instanceOf(ValidationError), (e) => new ConnectError(e.message, Code.InvalidArgument))
      .with(P.instanceOf(MembershipApplicationNotFoundError), () => new ConnectError('無効な認証トークンです', Code.InvalidArgument))
      .with(P.instanceOf(DatabaseError), () => new ConnectError('Internal server error', Code.Internal))
      .exhaustive(),
});
