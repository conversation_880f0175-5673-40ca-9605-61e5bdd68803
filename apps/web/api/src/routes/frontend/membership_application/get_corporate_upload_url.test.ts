import { ConnectError } from '@connectrpc/connect';
import { MailVerificationFactory } from '@web-test/factories/mail_verification_factory';
import { MembershipApplicationDocumentGroupFactory } from '@web-test/factories/membership_application_document_group_factory';
import { MembershipApplicationFactory } from '@web-test/factories/membership_application_factory';
import { clearTestBucket, ensureTestBucket } from '@web-test/s3';
import { MembershipApplicationService, DocumentType } from '@hami/web-api-schema/membership_application_service_pb';

describe('getCorporateUploadUrl', () => {
  beforeAll(async () => {
    await ensureTestBucket();
  });

  beforeEach(async () => {
    await clearTestBucket();
  });

  describe('正常系', () => {
    it('有効なuploadTokenとfileTypeでS3署名付きURLが取得できる（法人: CORP_SEAL_CERT）', async ({ getClient }) => {
      // Arrange
      const mailVerification = await MailVerificationFactory.create();
      const membershipApplication = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
        applicantType: 'CORPORATE',
        corporateName: 'テスト株式会社',
        corporateNameKana: 'テストカブシキガイシャ',
        representativeName: '代表 太郎',
        representativeNameKana: 'ダイヒョウ タロウ',
        representativePosition: '代表取締役',
        corporateNumber: '1234567890123',
        establishedYear: 2000,
        establishedMonth: 1,
        establishedDay: 1,
      });

      const documentGroup = await MembershipApplicationDocumentGroupFactory.create({
        membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
        uploadToken: 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
        groupKey: 'identity_verification',
      });

      const request = {
        fileType: 'application/pdf',
        uploadToken: documentGroup.uploadToken,
        documentType: DocumentType.CORP_SEAL_CERT,
      };

      // Act
      const client = getClient(MembershipApplicationService);
      const res = await client.getCorporateUploadUrl(request);

      // Assert
      expect(res.url).toMatch(/^https?:\/\/.+/);
      expect(res.url).toContain('identity/');
      expect(res.url).toContain('.pdf');
    });
  });

  describe('異常系', () => {
    it('無効なuploadTokenの場合はエラーを返す', async ({ getClient }) => {
      const request = {
        fileType: 'application/pdf',
        uploadToken: 'invalid-token',
        documentType: DocumentType.CORP_SEAL_CERT,
      };
      const client = getClient(MembershipApplicationService);
      let error: unknown;
      try {
        await client.getCorporateUploadUrl(request);
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
      if (error instanceof ConnectError) {
        expect(error.message).toContain('無効な認証トークンです');
      }
    });

    it('サポートされていないファイルタイプの場合はエラーを返す', async ({ getClient }) => {
      const mailVerification = await MailVerificationFactory.create();
      const membershipApplication = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
        applicantType: 'CORPORATE',
        corporateName: 'テスト株式会社',
      });
      const documentGroup = await MembershipApplicationDocumentGroupFactory.create({
        membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
        uploadToken: 'bbbbbbbb-cccc-dddd-eeee-ffffffffffff',
        groupKey: 'identity_verification',
      });

      const request = {
        fileType: 'text/plain',
        uploadToken: documentGroup.uploadToken,
        documentType: DocumentType.CORP_REGISTRY_CERT,
      };
      const client = getClient(MembershipApplicationService);
      let error: unknown;
      try {
        await client.getCorporateUploadUrl(request);
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
      if (error instanceof ConnectError) {
        expect(error.message).toContain('サポートされていないファイルタイプです');
      }
    });

    it('個人申込に対して法人APIを呼び出すとエラー', async ({ getClient }) => {
      const mailVerification = await MailVerificationFactory.create();
      const membershipApplication = await MembershipApplicationFactory.create({
        mailVerification: { connect: { mailVerificationId: mailVerification.mailVerificationId } },
        // applicantType はデフォルトで INDIVIDUAL
      });
      const documentGroup = await MembershipApplicationDocumentGroupFactory.create({
        membershipApplication: { connect: { membershipApplicationId: membershipApplication.membershipApplicationId } },
        uploadToken: 'cccccccc-dddd-eeee-ffff-000000000000',
        groupKey: 'identity_verification',
      });

      const request = {
        fileType: 'application/pdf',
        uploadToken: documentGroup.uploadToken,
        documentType: DocumentType.CORP_SEAL_CERT,
      };
      const client = getClient(MembershipApplicationService);
      let error: unknown;
      try {
        await client.getCorporateUploadUrl(request);
      } catch (err) {
        error = err;
      }
      expect(error).toBeDefined();
      if (error instanceof ConnectError) {
        expect(error.message).toContain('このAPIは法人申込のみ利用できます');
      }
    });
  });
});
