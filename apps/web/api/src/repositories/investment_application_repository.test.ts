import { annualBundleFactory } from '@web-test/factories/annual_bundle_factory';
import { horseFactory } from '@web-test/factories/horse_factory';
import { horseProfileFactory } from '@web-test/factories/horse_profile_factory';
import { MemberFactory } from '@web-test/factories/member_factory';
import { PublishStatus, RecruitmentStatus, AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@hami/prisma';
import {
  createInvestmentApplication,
  validateHorseExists,
  validateAnnualBundleExists,
} from './investment_application_repository';

describe('investment_application_repository', () => {
  describe('createInvestmentApplication', () => {
    it('新規出資申込を作成できる', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });
      
      const input = {
        memberId: member.memberId,
        horseId: horse.horseId,
        requestedNumber: 5,
        rejectPartialAllocation: false,
        isWhole: false,
        installmentPayment: false,
      };

      // ===== Act =====
      const result = await createInvestmentApplication(input);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.memberId).toBe(member.memberId);
        expect(result.value.horseId).toBe(horse.horseId);
        expect(result.value.requestedNumber).toBe(5);
        expect(result.value.rejectPartialAllocation).toBe(false);
        expect(result.value.isWhole).toBe(false);
        expect(result.value.allocatedNumber).toBeNull();
        expect(result.value.rejected).toBe(false);
        expect(result.value.appliedAt).toBeInstanceOf(Date);
      }
    });

    it('既存の未処理申込がある場合は更新する', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });
      
      // 既存の未処理申込を作成
      const existingApplication = await vPrisma.client.investmentApplication.create({
        data: {
          memberId: member.memberId,
          horseId: horse.horseId,
          requestedNumber: 3,
          rejectPartialAllocation: true,
          allocatedNumber: null,
          rejected: false,
        },
      });

      const input = {
        memberId: member.memberId,
        horseId: horse.horseId,
        requestedNumber: 7,
        rejectPartialAllocation: false,
        isWhole: true,
        installmentPayment: false,
      };

      // ===== Act =====
      const result = await createInvestmentApplication(input);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        // 同じIDのレコードが更新されている
        expect(result.value.investmentApplicationId).toBe(existingApplication.investmentApplicationId);
        expect(result.value.requestedNumber).toBe(7);
        expect(result.value.rejectPartialAllocation).toBe(false);
        expect(result.value.isWhole).toBe(true);
        expect(result.value.appliedAt).toBeInstanceOf(Date);
        
        // データベースで確認
        const updatedApplication = await vPrisma.client.investmentApplication.findUnique({
          where: { investmentApplicationId: existingApplication.investmentApplicationId },
        });
        expect(updatedApplication?.requestedNumber).toBe(7);
        expect(updatedApplication?.rejectPartialAllocation).toBe(false);
        expect(updatedApplication?.isWhole).toBe(true);
      }
    });

    it('処理済み申込がある場合は新規作成する', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });
      
      // 処理済み申込を作成
      await vPrisma.client.investmentApplication.create({
        data: {
          memberId: member.memberId,
          horseId: horse.horseId,
          requestedNumber: 3,
          rejectPartialAllocation: false,
          allocatedNumber: 2, // 処理済み
          rejected: false,
        },
      });

      const input = {
        memberId: member.memberId,
        horseId: horse.horseId,
        requestedNumber: 5,
        rejectPartialAllocation: false,
        isWhole: false,
        installmentPayment: false,
      };

      // ===== Act =====
      const result = await createInvestmentApplication(input);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.requestedNumber).toBe(5);
        expect(result.value.allocatedNumber).toBeNull();
        
        // 新規レコードが作成されている（2件目）
        const applications = await vPrisma.client.investmentApplication.findMany({
          where: { memberId: member.memberId, horseId: horse.horseId },
        });
        expect(applications).toHaveLength(2);
      }
    });

    it('拒否済み申込がある場合は新規作成する', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });
      
      // 拒否済み申込を作成
      await vPrisma.client.investmentApplication.create({
        data: {
          memberId: member.memberId,
          horseId: horse.horseId,
          requestedNumber: 3,
          rejectPartialAllocation: false,
          allocatedNumber: null,
          rejected: true, // 拒否済み
        },
      });

      const input = {
        memberId: member.memberId,
        horseId: horse.horseId,
        requestedNumber: 5,
        rejectPartialAllocation: false,
        isWhole: false,
        installmentPayment: false,
      };

      // ===== Act =====
      const result = await createInvestmentApplication(input);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.requestedNumber).toBe(5);
        expect(result.value.rejected).toBe(false);
        
        // 新規レコードが作成されている（2件目）
        const applications = await vPrisma.client.investmentApplication.findMany({
          where: { memberId: member.memberId, horseId: horse.horseId },
        });
        expect(applications).toHaveLength(2);
      }
    });

    it('別の会員が同じ馬に申込する場合は新規作成する', async () => {
      // ===== Arrange =====
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });
      
      // 会員1の未処理申込を作成
      await vPrisma.client.investmentApplication.create({
        data: {
          memberId: member1.memberId,
          horseId: horse.horseId,
          requestedNumber: 3,
          rejectPartialAllocation: false,
          allocatedNumber: null,
          rejected: false,
        },
      });

      const input = {
        memberId: member2.memberId, // 別の会員
        horseId: horse.horseId,
        requestedNumber: 5,
        rejectPartialAllocation: true,
        isWhole: false,
        installmentPayment: false,
      };

      // ===== Act =====
      const result = await createInvestmentApplication(input);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.memberId).toBe(member2.memberId);
        expect(result.value.requestedNumber).toBe(5);
        expect(result.value.rejectPartialAllocation).toBe(true);
        
        // 各会員に1件ずつレコードが存在する
        const member1Applications = await vPrisma.client.investmentApplication.findMany({
          where: { memberId: member1.memberId, horseId: horse.horseId },
        });
        const member2Applications = await vPrisma.client.investmentApplication.findMany({
          where: { memberId: member2.memberId, horseId: horse.horseId },
        });
        expect(member1Applications).toHaveLength(1);
        expect(member2Applications).toHaveLength(1);
        
        // 会員1の申込は影響を受けない
        expect(member1Applications[0].requestedNumber).toBe(3);
        expect(member1Applications[0].allocatedNumber).toBeNull();
      }
    });

    it('同じ会員が別の馬に申込する場合は新規作成する', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const horse1 = await horseFactory.create();
      const horse2 = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse1.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse2.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });
      
      // 馬1への未処理申込を作成
      await vPrisma.client.investmentApplication.create({
        data: {
          memberId: member.memberId,
          horseId: horse1.horseId,
          requestedNumber: 3,
          rejectPartialAllocation: false,
          allocatedNumber: null,
          rejected: false,
          isWhole: false,
        },
      });

      const input = {
        memberId: member.memberId,
        horseId: horse2.horseId, // 別の馬
        requestedNumber: 7,
        rejectPartialAllocation: true,
        isWhole: false,
        installmentPayment: false,
      };

      // ===== Act =====
      const result = await createInvestmentApplication(input);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.horseId).toBe(horse2.horseId);
        expect(result.value.requestedNumber).toBe(7);
        expect(result.value.rejectPartialAllocation).toBe(true);
        
        // 各馬に1件ずつレコードが存在する
        const horse1Applications = await vPrisma.client.investmentApplication.findMany({
          where: { memberId: member.memberId, horseId: horse1.horseId },
        });
        const horse2Applications = await vPrisma.client.investmentApplication.findMany({
          where: { memberId: member.memberId, horseId: horse2.horseId },
        });
        expect(horse1Applications).toHaveLength(1);
        expect(horse2Applications).toHaveLength(1);
        
        // 馬1への申込は影響を受けない
        expect(horse1Applications[0].requestedNumber).toBe(3);
        expect(horse1Applications[0].allocatedNumber).toBeNull();
      }
    });

    it('同じ会員が別の馬に未処理申込がある状態で既存の馬に再申込する場合は更新する', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const horse1 = await horseFactory.create();
      const horse2 = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse1.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse2.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });
      
      // 馬1への未処理申込を作成
      const horse1Application = await vPrisma.client.investmentApplication.create({
        data: {
          memberId: member.memberId,
          horseId: horse1.horseId,
          requestedNumber: 3,
          rejectPartialAllocation: false,
          allocatedNumber: null,
          rejected: false,
        },
      });

      // 馬2への未処理申込を作成
      await vPrisma.client.investmentApplication.create({
        data: {
          memberId: member.memberId,
          horseId: horse2.horseId,
          requestedNumber: 5,
          rejectPartialAllocation: true,
          allocatedNumber: null,
          rejected: false,
          isWhole: false,
        },
      });

      const input = {
        memberId: member.memberId,
        horseId: horse1.horseId, // 既存の未処理申込がある馬
        requestedNumber: 10,
        rejectPartialAllocation: true,
        isWhole: false,
        installmentPayment: false,
      };

      // ===== Act =====
      const result = await createInvestmentApplication(input);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        // 馬1の申込が更新されている
        expect(result.value.investmentApplicationId).toBe(horse1Application.investmentApplicationId);
        expect(result.value.requestedNumber).toBe(10);
        expect(result.value.rejectPartialAllocation).toBe(true);
        
        // 各馬に1件ずつレコードが存在する（数は変わらず）
        const horse1Applications = await vPrisma.client.investmentApplication.findMany({
          where: { memberId: member.memberId, horseId: horse1.horseId },
        });
        const horse2Applications = await vPrisma.client.investmentApplication.findMany({
          where: { memberId: member.memberId, horseId: horse2.horseId },
        });
        expect(horse1Applications).toHaveLength(1);
        expect(horse2Applications).toHaveLength(1);
        
        // 馬2への申込は影響を受けない
        expect(horse2Applications[0].requestedNumber).toBe(5);
        expect(horse2Applications[0].rejectPartialAllocation).toBe(true);
      }
    });
  });

  describe('validateHorseExists', () => {
    it('存在し出資申込可能な馬の場合はhorseIdを返す', async () => {
      // ===== Arrange =====
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });

      // ===== Act =====
      const result = await validateHorseExists(horse.recruitmentYear, horse.recruitmentNo);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(horse.horseId);
      }
    });

    it('存在しない馬の場合はnullを返す', async () => {
      // ===== Act =====
      const result = await validateHorseExists(9999, 9999);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });

    it('馬は存在するがHorseProfileが存在しない場合はnullを返す', async () => {
      // ===== Arrange =====
      const horse = await horseFactory.create();
      // HorseProfileは作成しない

      // ===== Act =====
      const result = await validateHorseExists(horse.recruitmentYear, horse.recruitmentNo);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });

    it('PublishStatusがDRAFTの場合はnullを返す', async () => {
      // ===== Arrange =====
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.DRAFT,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });

      // ===== Act =====
      const result = await validateHorseExists(horse.recruitmentYear, horse.recruitmentNo);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });

    it('PublishStatusがARCHIVEDの場合はnullを返す', async () => {
      // ===== Arrange =====
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.ARCHIVED,
        recruitmentStatus: RecruitmentStatus.ACTIVE,
      });

      // ===== Act =====
      const result = await validateHorseExists(horse.recruitmentYear, horse.recruitmentNo);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });

    it('RecruitmentStatusがUPCOMINGの場合はnullを返す', async () => {
      // ===== Arrange =====
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.UPCOMING,
      });

      // ===== Act =====
      const result = await validateHorseExists(horse.recruitmentYear, horse.recruitmentNo);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });

    it('RecruitmentStatusがFULLの場合はnullを返す', async () => {
      // ===== Arrange =====
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.FULL,
      });

      // ===== Act =====
      const result = await validateHorseExists(horse.recruitmentYear, horse.recruitmentNo);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });

    it('RecruitmentStatusがCLOSEDの場合はnullを返す', async () => {
      // ===== Arrange =====
      const horse = await horseFactory.create();
      await horseProfileFactory.create({
        horse: { connect: { horseId: horse.horseId } },
        publishStatus: PublishStatus.PUBLISHED,
        recruitmentStatus: RecruitmentStatus.CLOSED,
      });

      // ===== Act =====
      const result = await validateHorseExists(horse.recruitmentYear, horse.recruitmentNo);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });
  });

  describe('createInvestmentApplication (一括出資パッケージ)', () => {
    it('一括出資パッケージの新規申込を作成できる', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const annualBundle = await annualBundleFactory.create({
        publishStatus: AnnualBundlePublishStatus.PUBLIC,
        recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      });

      const input = {
        memberId: member.memberId,
        annualBundleId: annualBundle.annualBundleId,
        requestedNumber: 5,
        rejectPartialAllocation: false,
        isWhole: false,
        installmentPayment: false,
      };

      // ===== Act =====
      const result = await createInvestmentApplication(input);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        const application = result.value;
        expect(application.memberId).toBe(member.memberId);
        expect(application.annualBundleId).toBe(annualBundle.annualBundleId);
        expect(application.horseId).toBeNull();
        expect(application.requestedNumber).toBe(5);
        expect(application.rejectPartialAllocation).toBe(false);
        expect(application.isWhole).toBe(false);
        expect(application.installmentPayment).toBe(false);
      }
    });
  });

  describe('validateAnnualBundleExists', () => {
    it('存在し出資申込可能な一括出資パッケージの場合はannualBundleIdを返す', async () => {
      // ===== Arrange =====
      const annualBundle = await annualBundleFactory.create({
        publishStatus: AnnualBundlePublishStatus.PUBLIC,
        recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      });

      // ===== Act =====
      const result = await validateAnnualBundleExists(annualBundle.annualBundleId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(annualBundle.annualBundleId);
      }
    });

    it('存在しない一括出資パッケージの場合はnullを返す', async () => {
      // ===== Act =====
      const result = await validateAnnualBundleExists(99999);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });

    it('PublishStatusがPRIVATEの場合はnullを返す', async () => {
      // ===== Arrange =====
      const annualBundle = await annualBundleFactory.create({
        publishStatus: AnnualBundlePublishStatus.PRIVATE,
        recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
      });

      // ===== Act =====
      const result = await validateAnnualBundleExists(annualBundle.annualBundleId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });

    it('RecruitmentStatusがUPCOMINGの場合はnullを返す', async () => {
      // ===== Arrange =====
      const annualBundle = await annualBundleFactory.create({
        publishStatus: AnnualBundlePublishStatus.PUBLIC,
        recruitmentStatus: AnnualBundleRecruitmentStatus.UPCOMING,
      });

      // ===== Act =====
      const result = await validateAnnualBundleExists(annualBundle.annualBundleId);

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBe(null);
      }
    });
  });
});
