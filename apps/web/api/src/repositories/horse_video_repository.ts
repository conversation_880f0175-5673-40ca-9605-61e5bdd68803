import { ResultAsync, ok, err } from 'neverthrow';
import { Prisma, PublishStatus } from '@hami/prisma';
import { client } from '@web-api/utils/prisma';
import { DatabaseError } from './index';

export class HorseVideoNotFoundError extends Error {
  readonly name = 'HorseVideoNotFoundError';
  constructor(message = 'Horse video not found') {
    super(message);
  }
}

export class HorseNotFoundError extends Error {
  readonly name = 'HorseNotFoundError';
  constructor(message = 'Horse not found') {
    super(message);
  }
}

export interface ListHorseVideosParams {
  horseId?: number;
  recruitmentYear?: number;
  recruitmentNo?: number;
  publishStatus?: PublishStatus;
  page: number;
  pageSize: number;
  sortBy?: string; // e.g. "video_date_desc" (default)
}

export interface CreateHorseVideoParams {
  horseId: number;
  videoYear: number;
  videoMonth: number;
  videoDay: number;
  title: string;
  description?: string;
  youtubeVideoId: string;
  startAtSeconds?: number;
  publishStatus?: PublishStatus; // default: DRAFT
  thumbnailImagePath?: string;
}

export interface UpdateHorseVideoParams {
  horseVideoId: number;
  videoYear?: number;
  videoMonth?: number;
  videoDay?: number;
  title?: string;
  description?: string;
  youtubeVideoId?: string;
  startAtSeconds?: number;
  publishStatus?: PublishStatus;
  thumbnailImagePath?: string;
}

export interface ListHorseVideosResultItem {
  horseVideoId: number;
  horseId: number;
  horseName: string;
  videoYear: number;
  videoMonth: number;
  videoDay: number;
  publishStatus: PublishStatus;
  title: string;
  description?: string | null;
  youtubeVideoId: string;
  startAtSeconds?: number | null;
  thumbnailImagePath?: string | null;
  horse?: {
    recruitmentYear: number;
    recruitmentNo: number;
    recruitmentName: string;
    horseName: string;
    birthYear: number;
  };
}

export interface ListHorseVideosResult {
  videos: ListHorseVideosResultItem[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export const listHorseVideos = (params: ListHorseVideosParams) => {
  return ResultAsync.fromPromise(
    (async () => {
      const where: Prisma.HorseVideoWhereInput = { deletedAt: null };

      if (params.horseId !== undefined) {
        where.horseId = params.horseId;
      } else if (params.recruitmentYear !== undefined && params.recruitmentNo !== undefined) {
        where.horse = { recruitmentYear: params.recruitmentYear, recruitmentNo: params.recruitmentNo };
      }

      if (params.publishStatus !== undefined) {
        where.publishStatus = params.publishStatus;
      }

      const skip = (params.page - 1) * params.pageSize;

      // Sort
      const orderBy: Prisma.HorseVideoOrderByWithRelationInput[] = (() => {
        switch (params.sortBy) {
          case 'video_date_asc':
            return [{ videoYear: 'asc' }, { videoMonth: 'asc' }, { videoDay: 'asc' }];
          case 'created_at_desc':
            return [{ createdAt: 'desc' }];
          case 'created_at_asc':
            return [{ createdAt: 'asc' }];
          case 'video_date_desc':
          default:
            return [{ videoYear: 'desc' }, { videoMonth: 'desc' }, { videoDay: 'desc' }];
        }
      })();

      const [rows, totalCount] = await Promise.all([
        client.horseVideo.findMany({
          where,
          include: { horse: true },
          orderBy,
          skip,
          take: params.pageSize,
        }),
        client.horseVideo.count({ where }),
      ]);

      const totalPages = Math.max(1, Math.ceil(totalCount / params.pageSize));

      const videos: ListHorseVideosResultItem[] = rows.map((v) => ({
        horseVideoId: v.horseVideoId,
        horseId: v.horseId,
        horseName: v.horse.horseName,
        videoYear: v.videoYear,
        videoMonth: v.videoMonth,
        videoDay: v.videoDay,
        publishStatus: v.publishStatus,
        title: v.title,
        description: v.description,
        youtubeVideoId: v.youtubeVideoId,
        startAtSeconds: v.startAtSeconds ?? null,
        thumbnailImagePath: v.thumbnailImagePath ?? null,
        horse: {
          recruitmentYear: v.horse.recruitmentYear,
          recruitmentNo: v.horse.recruitmentNo,
          recruitmentName: v.horse.recruitmentName,
          horseName: v.horse.horseName,
          birthYear: v.horse.birthYear,
        },
      }));

      return {
        videos,
        totalCount,
        currentPage: params.page,
        totalPages,
        hasNextPage: params.page < totalPages,
        hasPreviousPage: params.page > 1,
      } satisfies ListHorseVideosResult;
    })(),
    (error) => new DatabaseError(`Failed to list horse videos: ${error}`)
  );
};

export const listLatestPublishedVideoByHorseIds = (horseIds: number[]) => {
  if (horseIds.length === 0) return ok(new Map<number, { youtubeVideoId: string; startAtSeconds: number | null }>());
  return ResultAsync.fromPromise(
    client.horseVideo.findMany({
      where: { deletedAt: null, publishStatus: PublishStatus.PUBLISHED, horseId: { in: horseIds } },
      orderBy: [{ horseId: 'asc' }, { videoYear: 'desc' }, { videoMonth: 'desc' }, { videoDay: 'desc' }, { horseVideoId: 'desc' }],
      select: { horseId: true, youtubeVideoId: true, startAtSeconds: true },
    }),
    (error) => new DatabaseError(`Failed to batch fetch latest horse videos: ${error}`)
  ).map((rows) => {
    const map = new Map<number, { youtubeVideoId: string; startAtSeconds: number | null }>();
    for (const row of rows) {
      if (!map.has(row.horseId)) {
        map.set(row.horseId, { youtubeVideoId: row.youtubeVideoId, startAtSeconds: row.startAtSeconds ?? null });
      }
    }
    return map;
  });
};

export const getHorseVideo = (horseVideoId: number) => {
  return ResultAsync.fromPromise(
    client.horseVideo.findFirst({
      where: { horseVideoId, deletedAt: null },
      include: { horse: true },
    }),
    (error) => new DatabaseError(`Failed to get horse video: ${error}`)
  ).andThen((v) => {
    if (!v) return err(new HorseVideoNotFoundError());
    return ok<ListHorseVideosResultItem>({
      horseVideoId: v.horseVideoId,
      horseId: v.horseId,
      horseName: v.horse.horseName,
      videoYear: v.videoYear,
      videoMonth: v.videoMonth,
      videoDay: v.videoDay,
      publishStatus: v.publishStatus,
      title: v.title,
      description: v.description,
      youtubeVideoId: v.youtubeVideoId,
      startAtSeconds: v.startAtSeconds ?? null,
      thumbnailImagePath: v.thumbnailImagePath ?? null,
      horse: {
        recruitmentYear: v.horse.recruitmentYear,
        recruitmentNo: v.horse.recruitmentNo,
        recruitmentName: v.horse.recruitmentName,
        horseName: v.horse.horseName,
        birthYear: v.horse.birthYear,
      },
    });
  });
};

export const createHorseVideo = (params: CreateHorseVideoParams) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      const horse = await tx.horse.findUnique({ where: { horseId: params.horseId } });
      if (!horse) throw new HorseNotFoundError();

      const created = await tx.horseVideo.create({
        data: {
          horseId: params.horseId,
          videoYear: params.videoYear,
          videoMonth: params.videoMonth,
          videoDay: params.videoDay,
          title: params.title,
          description: params.description,
          youtubeVideoId: params.youtubeVideoId,
          startAtSeconds: params.startAtSeconds,
          publishStatus: params.publishStatus ?? PublishStatus.DRAFT,
          thumbnailImagePath: params.thumbnailImagePath,
        },
        include: { horse: true },
      });

      return created.horseVideoId;
    }),
    (error) => (error instanceof HorseNotFoundError ? error : new DatabaseError(`Failed to create horse video: ${error}`))
  ).mapErr((e) => (e instanceof HorseNotFoundError ? e : new DatabaseError(`Failed to create horse video: ${e}`)));
};

export const updateHorseVideo = (params: UpdateHorseVideoParams) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      const existing = await tx.horseVideo.findFirst({ where: { horseVideoId: params.horseVideoId, deletedAt: null } });
      if (!existing) throw new HorseVideoNotFoundError();

      const data: Prisma.HorseVideoUpdateInput = {};
      if (params.videoYear !== undefined) data.videoYear = params.videoYear;
      if (params.videoMonth !== undefined) data.videoMonth = params.videoMonth;
      if (params.videoDay !== undefined) data.videoDay = params.videoDay;
      if (params.title !== undefined) data.title = params.title;
      if (params.description !== undefined) data.description = params.description;
      if (params.youtubeVideoId !== undefined) data.youtubeVideoId = params.youtubeVideoId;
      if (params.startAtSeconds !== undefined) data.startAtSeconds = params.startAtSeconds;
      if (params.publishStatus !== undefined) data.publishStatus = params.publishStatus;
      if (params.thumbnailImagePath !== undefined) data.thumbnailImagePath = params.thumbnailImagePath;

      await tx.horseVideo.update({ where: { horseVideoId: params.horseVideoId }, data });
      return true;
    }),
    (error) => (error instanceof HorseVideoNotFoundError ? error : new DatabaseError(`Failed to update horse video: ${error}`))
  ).mapErr((e) => (e instanceof HorseVideoNotFoundError ? e : new DatabaseError(`Failed to update horse video: ${e}`)));
};

export const deleteHorseVideo = (horseVideoId: number) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      const existing = await tx.horseVideo.findFirst({ where: { horseVideoId, deletedAt: null } });
      if (!existing) throw new HorseVideoNotFoundError();
      await tx.horseVideo.update({ where: { horseVideoId }, data: { deletedAt: new Date() } });
      return true;
    }),
    (error) => (error instanceof HorseVideoNotFoundError ? error : new DatabaseError(`Failed to delete horse video: ${error}`))
  ).mapErr((e) => (e instanceof HorseVideoNotFoundError ? e : new DatabaseError(`Failed to delete horse video: ${e}`)));
};
