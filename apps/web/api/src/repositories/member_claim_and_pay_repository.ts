import { ResultAsync } from 'neverthrow';
import { client } from '@web-api/utils/prisma';
import { DatabaseError } from './index';

export class MemberClaimAndPayNotFoundError extends Error {
  readonly name = 'MemberClaimAndPayNotFoundError';
}

/**
 * 指定のmemberIdのMemberClaimAndPayテーブルのレコードリストを取得
 * @param memberId 会員ID
 * @returns MemberClaimAndPayレコードの配列
 */
export const listMemberClaimAndPays = ({ memberId }: { memberId: number }) => {
  return ResultAsync.fromPromise(
    client.memberClaimAndPay.findMany({
      where: {
        memberId,
        statementFileKey: {
          not: null,
        },
      },
      select: {
        memberClaimAndPayId: true,
        memberId: true,
        occurredDate: true,
        claimAmount: true,
        payAmount: true,
        statementFileKey: true,
      },
      orderBy: {
        occurredDate: 'desc',
      },
    }),
    () => new DatabaseError('Failed to query member claim and pays')
  );
};

/**
 * statementFileKeyとmemberIdでMemberClaimAndPayを検索する（statementFileKeyがnullのものは除外）
 */
export const findMemberClaimAndPayByFileKeyAndMemberId = (
  statementFileKey: string,
  memberId: number
) => {
  return ResultAsync.fromPromise(
    client.memberClaimAndPay.findFirst({
      where: {
        statementFileKey: {
          equals: statementFileKey,
          not: null,
        },
        memberId,
      },
    }),
    () => new DatabaseError('Failed to find member claim and pay by file key and member id')
  );
};
