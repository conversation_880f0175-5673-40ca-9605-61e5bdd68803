import { ResultAsync } from 'neverthrow';
import { PublishStatus, RecruitmentStatus, AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@hami/prisma';
import { client } from '@web-api/utils/prisma';
import { DatabaseError } from './index';

export { DatabaseError };

export class InvestmentApplicationNotFoundError extends Error {
  readonly name = 'InvestmentApplicationNotFoundError';
}

export class HorseNotFoundError extends Error {
  readonly name = 'HorseNotFoundError';
}

export class HorseNotAvailableError extends Error {
  readonly name = 'HorseNotAvailableError';
}

export class AnnualBundleNotFoundError extends Error {
  readonly name = 'AnnualBundleNotFoundError';
}

export interface CreateInvestmentApplicationInput {
  memberId: number;
  horseId?: number;
  annualBundleId?: number;
  requestedNumber: number;
  rejectPartialAllocation: boolean;
  isWhole: boolean;
  installmentPayment: boolean;
}

/**
 * 出資申込を作成または更新
 * rejected=false かつ allocatedNumber=null の未処理レコードがあれば更新、なければ新規作成
 * @param input 出資申込作成データ
 * @returns 成功した場合はInvestmentApplicationレコード、DBエラー時はResultAsyncでエラー
 */
export const createInvestmentApplication = (input: CreateInvestmentApplicationInput) => {
  return ResultAsync.fromPromise(
    client.$transaction(async (tx) => {
      // 未処理の申込を検索
      const existingApplication = await tx.investmentApplication.findFirst({
        where: {
          memberId: input.memberId,
          horseId: input.horseId,
          annualBundleId: input.annualBundleId,
          rejected: false,
          allocatedNumber: null,
        },
      });

      if (existingApplication) {
        // 既存の未処理申込を更新
        return tx.investmentApplication.update({
          where: {
            investmentApplicationId: existingApplication.investmentApplicationId,
          },
          data: {
            requestedNumber: input.requestedNumber,
            rejectPartialAllocation: input.rejectPartialAllocation,
            isWhole: input.isWhole,
            installmentPayment: input.installmentPayment,
            appliedAt: new Date(),
          },
        });
      } else {
        // 新規作成
        return tx.investmentApplication.create({
          data: {
            memberId: input.memberId,
            horseId: input.horseId,
            annualBundleId: input.annualBundleId,
            requestedNumber: input.requestedNumber,
            rejectPartialAllocation: input.rejectPartialAllocation,
            isWhole: input.isWhole,
            installmentPayment: input.installmentPayment,
          },
        });
      }
    }),
    () => new DatabaseError('Failed to create investment application')
  );
};

/**
 * 馬の存在と出資申込可能状態を確認
 * @param recruitmentYear 募集年
 * @param recruitmentNo 募集番号
 * @returns 馬が存在し出資申込可能な場合はtrue、存在しない場合やDBエラー時はResultAsyncでエラー
 */
export const validateHorseExists = (recruitmentYear: number, recruitmentNo: number) => {
  return ResultAsync.fromPromise(
    client.horse.findFirst({
      where: { recruitmentYear, recruitmentNo, deletedAt: null },
      include: {
        profile: {
          select: {
            publishStatus: true,
            recruitmentStatus: true,
          },
        },
      },
    }),
    () => new DatabaseError('Failed to validate horse existence')
  ).map((horse) => {
    if (!horse) {
      return null;
    }
    
    // HorseProfileが存在し、publishStatus=PUBLISHED かつ recruitmentStatus=ACTIVE である必要がある
    if (!horse.profile || horse.profile.publishStatus !== PublishStatus.PUBLISHED || horse.profile.recruitmentStatus !== RecruitmentStatus.ACTIVE) {
      return null;
    }
    return horse.horseId;
  });
};

/**
 * 一括出資パッケージの存在と出資申込可能状態を確認
 * @param annualBundleId 年度バンドルID
 * @returns パッケージが存在し出資申込可能な場合はtrue、存在しない場合やDBエラー時はResultAsyncでエラー
 */
export const validateAnnualBundleExists = (annualBundleId: number) => {
  return ResultAsync.fromPromise(
    client.annualBundle.findFirst({
      where: { annualBundleId },
    }),
    () => new DatabaseError('Failed to validate annual bundle existence')
  ).map((bundle) => {
    if (!bundle) {
      return null;
    }

    // publishStatus=PUBLIC かつ recruitmentStatus=ACTIVE である必要がある
    if (bundle.publishStatus !== AnnualBundlePublishStatus.PUBLIC || bundle.recruitmentStatus !== AnnualBundleRecruitmentStatus.ACTIVE) {
      return null;
    }
    return bundle.annualBundleId;
  });
};