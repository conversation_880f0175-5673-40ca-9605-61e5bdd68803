import { MemberClaimAndPayFactory } from '@web-test/factories/member_claim_and_pay_factory';
import { MemberFactory } from '@web-test/factories/member_factory';
import { listMemberClaimAndPays, findMemberClaimAndPayByFileKeyAndMemberId } from './member_claim_and_pay_repository';

describe('member_claim_and_pay_repository', () => {
  describe('listMemberClaimAndPays', () => {
    it('指定のmemberIdの請求・支払いレコード一覧を取得できる（statementFileKeyがnullではないもののみ）', async () => {
      // ===== Arrange =====
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // member1の請求・支払いレコードを作成（statementFileKeyあり）
      const claimAndPay1 = await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member1.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: 'statement1.pdf',
      });

      const claimAndPay2 = await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member1.memberId } },
        occurredDate: new Date('2024-02-20'),
        claimAmount: 75000,
        payAmount: 45000,
        statementFileKey: 'statement2.pdf',
      });

      // member1のstatementFileKeyがnullのレコードを作成（取得されないことを確認するため）
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member1.memberId } },
        occurredDate: new Date('2024-03-01'),
        claimAmount: 100000,
        payAmount: 60000,
        statementFileKey: null,
      });

      // member2の請求・支払いレコードを作成（取得されないことを確認するため）
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member2.memberId } },
        occurredDate: new Date('2024-01-10'),
        claimAmount: 100000,
        payAmount: 60000,
        statementFileKey: 'statement3.pdf',
      });

      // ===== Act =====
      const result = await listMemberClaimAndPays({ memberId: member1.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(2);
        
        // statementFileKeyがnullのレコードは含まれていないことを確認
        expect(result.value.every(record => record.memberClaimAndPayId !== claimAndPay1.memberClaimAndPayId || record.memberClaimAndPayId !== claimAndPay2.memberClaimAndPayId)).toBe(true);
        
        // 発生日の降順でソートされていることを確認
        expect(result.value[0].memberClaimAndPayId).toBe(claimAndPay2.memberClaimAndPayId);
        expect(result.value[1].memberClaimAndPayId).toBe(claimAndPay1.memberClaimAndPayId);
        
        // 最初のレコードの詳細を確認
        const firstRecord = result.value[0];
        expect(firstRecord.memberId).toBe(member1.memberId);
        expect(firstRecord.occurredDate).toEqual(new Date('2024-02-20'));
        expect(firstRecord.claimAmount).toBe(75000);
        expect(firstRecord.payAmount).toBe(45000);
        
        // 2番目のレコードの詳細を確認
        const secondRecord = result.value[1];
        expect(secondRecord.memberId).toBe(member1.memberId);
        expect(secondRecord.occurredDate).toEqual(new Date('2024-01-15'));
        expect(secondRecord.claimAmount).toBe(50000);
        expect(secondRecord.payAmount).toBe(30000);
      }
    });

    it('指定のmemberIdに請求・支払いレコードがない場合は空配列を返す', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      
      // 他のmemberのレコードのみ作成
      const otherMember = await MemberFactory.create();
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: otherMember.memberId } },
        occurredDate: new Date('2024-01-10'),
        claimAmount: 100000,
        payAmount: 60000,
      });

      // ===== Act =====
      const result = await listMemberClaimAndPays({ memberId: member.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(0);
      }
    });

    it('存在しないmemberIdの場合は空配列を返す', async () => {
      // ===== Act =====
      const result = await listMemberClaimAndPays({ memberId: 99999 });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(0);
      }
    });

    it('複数のレコードが発生日の降順でソートされる（statementFileKeyがnullではないもののみ）', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();

      const claimAndPay1 = await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: 'statement1.pdf',
      });

      const claimAndPay2 = await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-03-10'),
        claimAmount: 80000,
        payAmount: 50000,
        statementFileKey: 'statement2.pdf',
      });

      const claimAndPay3 = await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-02-20'),
        claimAmount: 75000,
        payAmount: 45000,
        statementFileKey: 'statement3.pdf',
      });

      // statementFileKeyがnullのレコードを作成（取得されないことを確認するため）
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-04-01'),
        claimAmount: 120000,
        payAmount: 80000,
        statementFileKey: null,
      });

      // ===== Act =====
      const result = await listMemberClaimAndPays({ memberId: member.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(3);
        
        // 発生日の降順でソートされていることを確認
        expect(result.value[0].memberClaimAndPayId).toBe(claimAndPay2.memberClaimAndPayId); // 2024-03-10
        expect(result.value[1].memberClaimAndPayId).toBe(claimAndPay3.memberClaimAndPayId); // 2024-02-20
        expect(result.value[2].memberClaimAndPayId).toBe(claimAndPay1.memberClaimAndPayId); // 2024-01-15
      }
    });

    it('必要なフィールドのみが選択される（statementFileKeyがnullではないもののみ）', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: 'statement1.pdf',
      });

      // ===== Act =====
      const result = await listMemberClaimAndPays({ memberId: member.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(1);
        const record = result.value[0];
        
        // 必要なフィールドが含まれていることを確認
        expect(record).toHaveProperty('memberClaimAndPayId');
        expect(record).toHaveProperty('memberId');
        expect(record).toHaveProperty('occurredDate');
        expect(record).toHaveProperty('claimAmount');
        expect(record).toHaveProperty('payAmount');
        expect(record).toHaveProperty('statementFileKey');
        
        // 不要なフィールドが含まれていないことを確認
        expect(record).not.toHaveProperty('statementIssuedAt');
      }
    });

    it('statementFileKeyがnullのレコードは除外される', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();

      // statementFileKeyがnullのレコードを作成
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: null,
      });

      // statementFileKeyが空文字のレコードを作成（現在のクエリでは取得される）
      const emptyStringRecord = await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-02-20'),
        claimAmount: 75000,
        payAmount: 45000,
        statementFileKey: '',
      });

      // statementFileKeyが設定されているレコードを作成
      const validRecord = await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-03-01'),
        claimAmount: 100000,
        payAmount: 60000,
        statementFileKey: 'valid-statement.pdf',
      });

      // ===== Act =====
      const result = await listMemberClaimAndPays({ memberId: member.memberId });

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toHaveLength(2);
        // nullのレコードは除外され、空文字と有効な値のレコードが取得される
        expect(result.value.map(r => r.memberClaimAndPayId)).toContain(emptyStringRecord.memberClaimAndPayId);
        expect(result.value.map(r => r.memberClaimAndPayId)).toContain(validRecord.memberClaimAndPayId);
      }
    });
  });

  describe('findMemberClaimAndPayByFileKeyAndMemberId', () => {
    it('指定のstatementFileKeyとmemberIdに一致するレコードを取得できる', async () => {
      // ===== Arrange =====
      const member1 = await MemberFactory.create();
      const member2 = await MemberFactory.create();

      // member1のレコードを作成
      const claimAndPay1 = await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member1.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
      });

      // member2のレコードを作成（異なるmemberId）
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member2.memberId } },
        occurredDate: new Date('2024-02-20'),
        claimAmount: 75000,
        payAmount: 45000,
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf', // 同じファイルキー
      });

      // member1の別のレコードを作成（異なるファイルキー）
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member1.memberId } },
        occurredDate: new Date('2024-03-01'),
        claimAmount: 100000,
        payAmount: 60000,
        statementFileKey: 'statements/investment-and-return-10001-20242.pdf', // 異なるファイルキー
      });

      // ===== Act =====
      const result = await findMemberClaimAndPayByFileKeyAndMemberId(
        'statements/investment-and-return-10001-20241.pdf',
        member1.memberId
      );

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).not.toBeNull();
        expect(result.value?.memberClaimAndPayId).toBe(claimAndPay1.memberClaimAndPayId);
        expect(result.value?.memberId).toBe(member1.memberId);
        expect(result.value?.statementFileKey).toBe('statements/investment-and-return-10001-20241.pdf');
        expect(result.value?.claimAmount).toBe(50000);
        expect(result.value?.payAmount).toBe(30000);
      }
    });

    it('指定のstatementFileKeyとmemberIdに一致するレコードがない場合はnullを返す', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();

      // 異なるファイルキーのレコードを作成
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
      });

      // ===== Act =====
      const result = await findMemberClaimAndPayByFileKeyAndMemberId(
        'statements/investment-and-return-10001-20242.pdf', // 存在しないファイルキー
        member.memberId
      );

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBeNull();
      }
    });

    it('存在しないmemberIdの場合はnullを返す', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();
      const nonExistentMemberId = 99999;

      // 存在するレコードを作成
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
      });

      // ===== Act =====
      const result = await findMemberClaimAndPayByFileKeyAndMemberId(
        'statements/investment-and-return-10001-20241.pdf',
        nonExistentMemberId
      );

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).toBeNull();
      }
    });

    it('statementFileKeyがnullのレコードは取得されない', async () => {
      // ===== Arrange =====
      const member = await MemberFactory.create();

      // statementFileKeyがnullのレコードを作成
      await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-01-15'),
        claimAmount: 50000,
        payAmount: 30000,
        statementFileKey: null,
      });

      // statementFileKeyが設定されているレコードを作成
      const validRecord = await MemberClaimAndPayFactory.create({
        member: { connect: { memberId: member.memberId } },
        occurredDate: new Date('2024-02-20'),
        claimAmount: 75000,
        payAmount: 45000,
        statementFileKey: 'statements/investment-and-return-10001-20241.pdf',
      });

      // ===== Act =====
      const result = await findMemberClaimAndPayByFileKeyAndMemberId(
        'statements/investment-and-return-10001-20241.pdf',
        member.memberId
      );

      // ===== Assert =====
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value).not.toBeNull();
        expect(result.value?.memberClaimAndPayId).toBe(validRecord.memberClaimAndPayId);
        expect(result.value?.statementFileKey).toBe('statements/investment-and-return-10001-20241.pdf');
      }
    });

  });
});
