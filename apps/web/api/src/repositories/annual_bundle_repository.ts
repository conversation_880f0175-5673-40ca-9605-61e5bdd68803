import { ResultAsync, err, okAsync } from 'neverthrow';
import { AnnualBundlePublishStatus } from '@hami/prisma';
import { client } from '@web-api/utils/prisma';
import { DatabaseError } from './index';

export class AnnualBundleNotFoundError extends Error {
  readonly name = 'AnnualBundleNotFoundError';
}

export const listPublicAnnualBundlesByFiscalYear = ({ fiscalYear }: { fiscalYear: number }) => {
  return ResultAsync.fromPromise(
    client.annualBundle.findMany({
      where: {
        fiscalYear,
        publishStatus: AnnualBundlePublishStatus.PUBLIC,
      },
      include: {
        horses: {
          select: {
            horseId: true,
            horse: { select: { horseName: true, recruitmentName: true, recruitmentNo: true, amountTotal: true, sharesTotal: true } },
          },
          orderBy: { horse: { recruitmentNo: 'asc' } },
        },
      },
      orderBy: { annualBundleId: 'asc' },
    }),
    () => new DatabaseError('年度バンドルの一覧取得に失敗しました')
  );
};


