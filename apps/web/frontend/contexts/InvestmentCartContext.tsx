'use client';

import { createContext, useCallback, useContext, useEffect, useMemo, useReducer, type ReactNode } from 'react';

export type HorseCartItem = {
  kind: 'horse';
  id: string;
  recruitmentYear: number;
  recruitmentNo: number;
  horseName: string;
  requestedNumber: number;
  rejectPartialAllocation: boolean;
  installmentPayment: boolean;
  maxSharesPerOrder?: number;
  sharePriceManYen?: number;
};

export type BundleCartItem = {
  kind: 'bundle';
  id: string;
  annualBundleId: number;
  fiscalYear: number;
  name: string;
  requestedNumber: number;
  shares: number;
  perShareAmountManYen?: number;
  horses: Array<{
    recruitmentYear: number;
    recruitmentNo: number;
    horseName: string;
    recruitmentName: string;
  }>;
};

export type InvestmentCartItem = HorseCartItem | BundleCartItem;

interface InvestmentCartState {
  items: InvestmentCartItem[];
}

const STORAGE_KEY = 'investment-cart:v1';

const initialState: InvestmentCartState = { items: [] };

type InvestmentCartAction =
  | { type: 'HYDRATE'; payload: InvestmentCartItem[] }
  | { type: 'UPSERT_ITEM'; payload: InvestmentCartItem }
  | { type: 'REMOVE_ITEM'; payload: { id: string } }
  | { type: 'CLEAR' }
  | { type: 'SET_REQUESTED_NUMBER'; payload: { id: string; requestedNumber: number } }
  | { type: 'SET_REJECT_PARTIAL'; payload: { id: string; rejectPartialAllocation: boolean } }
  | { type: 'SET_INSTALLMENT_PAYMENT'; payload: { id: string; installmentPayment: boolean } };

function investmentCartReducer(state: InvestmentCartState, action: InvestmentCartAction): InvestmentCartState {
  switch (action.type) {
    case 'HYDRATE': {
      return { items: action.payload };
    }
    case 'UPSERT_ITEM': {
      const existingIndex = state.items.findIndex((item) => item.id === action.payload.id);
      if (existingIndex === -1) {
        return { items: [...state.items, action.payload] };
      }
      const nextItems = state.items.slice();
      nextItems[existingIndex] = action.payload;
      return { items: nextItems };
    }
    case 'REMOVE_ITEM': {
      return { items: state.items.filter((item) => item.id !== action.payload.id) };
    }
    case 'CLEAR': {
      return { items: [] };
    }
    case 'SET_REQUESTED_NUMBER': {
      const nextItems = state.items
        .map((item) => {
          if (item.id !== action.payload.id) return item;
          if (action.payload.requestedNumber <= 0) {
            return null;
          }
          return {
            ...item,
            requestedNumber: action.payload.requestedNumber,
          } as InvestmentCartItem;
        })
        .filter((item): item is InvestmentCartItem => item !== null);
      return { items: nextItems };
    }
    case 'SET_REJECT_PARTIAL': {
      const nextItems = state.items.map((item) => {
        if (item.id !== action.payload.id || item.kind !== 'horse') {
          return item;
        }
        return { ...item, rejectPartialAllocation: action.payload.rejectPartialAllocation };
      });
      return { items: nextItems };
    }
    case 'SET_INSTALLMENT_PAYMENT': {
      const nextItems = state.items.map((item) => {
        if (item.id !== action.payload.id || item.kind !== 'horse') {
          return item;
        }
        return { ...item, installmentPayment: action.payload.installmentPayment };
      });
      return { items: nextItems };
    }
    default:
      return state;
  }
}

interface InvestmentCartContextValue {
  items: InvestmentCartItem[];
  horseItems: HorseCartItem[];
  bundleItems: BundleCartItem[];
  upsertHorse: (input: Omit<HorseCartItem, 'id' | 'kind'>) => void;
  upsertBundle: (input: Omit<BundleCartItem, 'id' | 'kind'>) => void;
  removeItem: (id: string) => void;
  clear: () => void;
  setRequestedNumber: (id: string, requestedNumber: number) => void;
  setRejectPartialAllocation: (id: string, reject: boolean) => void;
  setInstallmentPayment: (id: string, installmentPayment: boolean) => void;
  findHorseItem: (recruitmentYear: number, recruitmentNo: number) => HorseCartItem | undefined;
  findBundleItem: (annualBundleId: number, fiscalYear: number) => BundleCartItem | undefined;
}

const InvestmentCartContext = createContext<InvestmentCartContextValue | undefined>(undefined);

export function InvestmentCartProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(investmentCartReducer, initialState);
  const isBrowser = typeof window !== 'undefined';

  useEffect(() => {
    if (!isBrowser) return;
    try {
      const stored = window.localStorage.getItem(STORAGE_KEY);
      if (!stored) return;
      const parsed = JSON.parse(stored) as InvestmentCartItem[];
      dispatch({ type: 'HYDRATE', payload: parsed.filter((item) => typeof item?.id === 'string') });
    } catch (error) {
      console.warn('Failed to hydrate investment cart from storage', error);
    }
  }, [isBrowser]);

  useEffect(() => {
    if (!isBrowser) return;
    try {
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(state.items));
    } catch (error) {
      console.warn('Failed to persist investment cart to storage', error);
    }
  }, [state.items, isBrowser]);

  const upsertHorse = useCallback((input: Omit<HorseCartItem, 'id' | 'kind'>) => {
    const id = `horse-${input.recruitmentYear}-${input.recruitmentNo}`;
    const item: HorseCartItem = {
      kind: 'horse',
      id,
      ...input,
    };
    if (item.requestedNumber <= 0) {
      dispatch({ type: 'REMOVE_ITEM', payload: { id } });
      return;
    }
    dispatch({ type: 'UPSERT_ITEM', payload: item });
  }, []);

  const upsertBundle = useCallback((input: Omit<BundleCartItem, 'id' | 'kind'>) => {
    const id = `bundle-${input.fiscalYear}-${input.annualBundleId}`;
    const item: BundleCartItem = {
      kind: 'bundle',
      id,
      ...input,
    };
    if (item.requestedNumber <= 0) {
      dispatch({ type: 'REMOVE_ITEM', payload: { id } });
      return;
    }
    dispatch({ type: 'UPSERT_ITEM', payload: item });
  }, []);

  const removeItem = useCallback((id: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: { id } });
  }, []);

  const clear = useCallback(() => {
    dispatch({ type: 'CLEAR' });
  }, []);

  const setRequestedNumber = useCallback((id: string, requestedNumber: number) => {
    dispatch({ type: 'SET_REQUESTED_NUMBER', payload: { id, requestedNumber } });
  }, []);

  const setRejectPartialAllocation = useCallback((id: string, reject: boolean) => {
    dispatch({ type: 'SET_REJECT_PARTIAL', payload: { id, rejectPartialAllocation: reject } });
  }, []);

  const setInstallmentPayment = useCallback((id: string, installmentPayment: boolean) => {
    dispatch({ type: 'SET_INSTALLMENT_PAYMENT', payload: { id, installmentPayment } });
  }, []);

  const findHorseItem = useCallback(
    (recruitmentYear: number, recruitmentNo: number) => {
      return state.items.find(
        (item): item is HorseCartItem =>
          item.kind === 'horse' && item.recruitmentYear === recruitmentYear && item.recruitmentNo === recruitmentNo,
      );
    },
    [state.items],
  );

  const findBundleItem = useCallback(
    (annualBundleId: number, fiscalYear: number) => {
      return state.items.find(
        (item): item is BundleCartItem =>
          item.kind === 'bundle' && item.annualBundleId === annualBundleId && item.fiscalYear === fiscalYear,
      );
    },
    [state.items],
  );

  const value: InvestmentCartContextValue = useMemo(
    () => ({
      items: state.items,
      horseItems: state.items.filter((item): item is HorseCartItem => item.kind === 'horse'),
      bundleItems: state.items.filter((item): item is BundleCartItem => item.kind === 'bundle'),
      upsertHorse,
      upsertBundle,
      removeItem,
      clear,
      setRequestedNumber,
      setRejectPartialAllocation,
      setInstallmentPayment,
      findHorseItem,
      findBundleItem,
    }),
    [state.items, upsertHorse, upsertBundle, removeItem, clear, setRequestedNumber, setRejectPartialAllocation, setInstallmentPayment, findHorseItem, findBundleItem],
  );

  return <InvestmentCartContext.Provider value={value}>{children}</InvestmentCartContext.Provider>;
}

export function useInvestmentCart() {
  const context = useContext(InvestmentCartContext);
  if (!context) {
    throw new Error('useInvestmentCart must be used within an InvestmentCartProvider');
  }
  return context;
}
