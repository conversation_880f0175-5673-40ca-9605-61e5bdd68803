import { Page, Locator, expect } from '@playwright/test';
import path from 'path';

export class UploadIdentityPage {
  readonly page: Page;
  readonly pageTitle: Locator;
  readonly identityDocumentSection: Locator;
  readonly identityFileInput: Locator;
  readonly identityFileInput2: Locator;
  readonly identityDropZone: Locator;
  readonly identityPreview: Locator;
  readonly completeButton: Locator;
  readonly errorMessage: Locator;
  readonly progressBar: Locator;

  constructor(page: Page) {
    this.page = page;
    // レイアウト（デスクトップ/モバイル）でタイトル文言が異なるため、共通部分でマッチさせる
    // デスクトップ: 「本人確認書類アップロード」 / モバイル: 「新規入会 本人確認書類アップロード」
    // 両方に含まれる「本人確認書類アップロード」でマッチし、最初の表示要素を検証する
    this.pageTitle = page.locator('[data-testid="page-title"]').first();

    // Identity document section - 複数の要素がある場合は最初の表示されている要素を選択
    this.identityDocumentSection = page.locator('[data-testid="identity-document-section"]').first();
    this.identityFileInput = page.locator('input[type="file"][name="identity-document"]').first();
    this.identityFileInput2 = page.locator('input[type="file"][name="identity-document"]').last();
    this.identityDropZone = page.locator('[data-testid="identity-drop-zone"]').first();
    this.identityPreview = page.locator('[data-testid="identity-preview"]').first();

    // Common elements - 複数の要素がある場合は最初の表示されている要素を選択
    this.completeButton = page.locator('[data-testid="submit-button"]').first();
    this.errorMessage = page.locator('p.mt-2.text-sm.text-red-600'); // エラーメッセージセレクタ
    this.progressBar = page.locator('[role="progressbar"]');
  }

  async goto(uploadToken: string) {
    await this.page.goto(`/join/application/upload_identity?uploadToken=${uploadToken}`);
  }

  async expectPageLoaded() {
    await expect(this.pageTitle).toBeVisible();
    await expect(this.identityDocumentSection).toBeVisible();
  }

  async uploadIdentityDocument(filePath: string) {
    await this.identityFileInput.setInputFiles(filePath);
    await this.identityFileInput2.setInputFiles(filePath);
  }

  async dragAndDropIdentityDocument(filePath: string) {
    const dataTransfer = await this.page.evaluateHandle(() => new DataTransfer());

    // ファイル名を事前に取得
    const fileName = path.basename(filePath);

    await this.page.evaluateHandle(
      async ({ dataTransfer, fileName }) => {
        const file = new File(['test'], fileName, { type: 'image/jpeg' });
        dataTransfer.items.add(file);
      },
      { dataTransfer, fileName }
    );

    await this.identityDropZone.dispatchEvent('drop', { dataTransfer });
  }

  async expectIdentityPreviewVisible() {
    await expect(this.identityPreview).toBeVisible();
  }

  async expectCompleteButtonEnabled() {
    await expect(this.completeButton).toBeEnabled();
  }

  async expectCompleteButtonDisabled() {
    await expect(this.completeButton).toBeDisabled();
  }

  async clickCompleteButton() {
    await this.completeButton.click();
  }

  async expectErrorMessage(message: string) {
    // エラーメッセージはページ上部の統一エラー表示エリアに表示される（複数ある場合は最初の要素を選択）
    const errorContainer = this.page.locator('div.bg-red-100.border.border-red-400.text-red-700').first();
    await expect(errorContainer).toContainText(message);
  }

  async expectIdentityErrorMessage(message: string) {
    // 統一エラー表示エリアでidentityエラーを確認（複数ある場合は最初の要素を選択）
    const errorContainer = this.page.locator('div.bg-red-100.border.border-red-400.text-red-700').first();
    await expect(errorContainer).toContainText(message);
  }

  async expectProgressBarVisible() {
    await expect(this.progressBar).toBeVisible();
  }

  async expectSuccessRedirect() {
    await this.page.waitForURL('/join/sent');
  }

  async removeIdentityDocument() {
    const removeButton = this.identityPreview.locator('button[aria-label="削除"]').first();
    const removeButton2 = this.identityPreview.locator('button[aria-label="削除"]').last();
    await removeButton.click();
    await removeButton2.click();
  }
}
