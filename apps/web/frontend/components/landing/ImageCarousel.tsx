'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

type CarouselImage = {
  src: string;
  alt?: string;
  href?: string;
  target?: '_blank' | '_self';
};

interface ImageCarouselProps {
  className?: string;
  images?: CarouselImage[];
  autoPlay?: boolean;
  intervalMs?: number;
  // height は4:3固定時は未使用
  height?: number;
}

export function ImageCarousel({ className = '', images, autoPlay = true, intervalMs = 3000 }: ImageCarouselProps) {
  const defaultImages = useMemo<CarouselImage[]>(() => [], []);

  const slides = images && images.length > 0 ? images : defaultImages;
  const [index, setIndex] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const touchStartX = useRef<number | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  const slideCount = slides.length;

  const goTo = useCallback(
    (i: number) => {
      const next = (i + slideCount) % slideCount;
      setIndex(next);
    },
    [slideCount]
  );

  const next = useCallback(() => goTo(index + 1), [goTo, index]);
  const prev = useCallback(() => goTo(index - 1), [goTo, index]);

  // パフォーマンス最適化: 現在・前後のスライドのみ画像を描画（仮想化）
  const prevIndex = (index - 1 + slideCount) % slideCount;
  const nextIndex = (index + 1) % slideCount;

  useEffect(() => {
    if (!autoPlay || slideCount <= 1) return;
    if (isHovering) return;

    const id = setInterval(() => {
      setIndex((v) => (v + 1) % slideCount);
    }, intervalMs);
    return () => clearInterval(id);
  }, [autoPlay, intervalMs, isHovering, slideCount]);

  const onTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
  };
  const onTouchEnd = (e: React.TouchEvent) => {
    if (touchStartX.current === null) return;
    const diff = e.changedTouches[0].clientX - touchStartX.current;
    touchStartX.current = null;
    const threshold = 50; // px
    if (diff > threshold) prev();
    else if (diff < -threshold) next();
  };

  return (
    <section
      className={`w-full ${className}`}
      aria-label="プロモーションカルーセル"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <div className="relative w-full">
        {/* インナーは左右の余白をTopNavigationに合わせる */}
        <div className="px-5">
          <div
            ref={containerRef}
            className="relative overflow-hidden rounded-lg border border-[#e7e6e6] bg-white aspect-[4/3] md:aspect-[16/9]"
            onTouchStart={onTouchStart}
            onTouchEnd={onTouchEnd}
          >
            {/* スライドトラック */}
            <div
              className="flex h-full w-full transition-transform duration-500 ease-out"
              style={{ transform: `translateX(-${index * 100}%)` }}
            >
              {slides.map((img, i) => {
                const isVisible = i === index || i === prevIndex || i === nextIndex;
                const imageElement = isVisible && (
                  <Image
                    src={img.src}
                    alt={img.alt || `Carousel ${i + 1}`}
                    fill
                    sizes="(min-width: 1600px) 1600px, 100vw"
                    className="object-contain"
                    priority={i === 0}
                    loading={i === 0 ? 'eager' : 'lazy'}
                    quality={70}
                  />
                );

                return (
                  <div key={i} className="relative h-full w-full shrink-0 grow-0 basis-full">
                    {img.href ? (
                      <Link
                        href={img.href}
                        target={img.target || '_self'}
                        rel={img.target === '_blank' ? 'noopener noreferrer' : undefined}
                        className="block h-full w-full"
                      >
                        {imageElement}
                      </Link>
                    ) : (
                      imageElement
                    )}
                  </div>
                );
              })}
            </div>

            {/* ナビゲーションボタン */}
            {slideCount > 1 && (
              <>
                <button
                  aria-label="前の画像"
                  onClick={prev}
                  className="absolute left-3 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all duration-200 cursor-pointer"
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M15.41 16.59 10.83 12l4.58-4.59L14 6l-6 6 6 6z" />
                  </svg>
                </button>
                <button
                  aria-label="次の画像"
                  onClick={next}
                  className="absolute right-3 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full transition-all duration-200 cursor-pointer"
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.59 16.59 13.17 12 8.59 7.41 10 6l6 6-6 6z" />
                  </svg>
                </button>
              </>
            )}

            {/* インジケーター */}
            {slideCount > 1 && (
              <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-2">
                {slides.map((_, i) => (
                  <button
                    key={i}
                    aria-label={`スライド ${i + 1} へ`}
                    onClick={() => goTo(i)}
                    className={`h-2.5 w-2.5 rounded-full transition-colors ${i === index ? 'bg-white' : 'bg-white/50 hover:bg-white/80'}`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
