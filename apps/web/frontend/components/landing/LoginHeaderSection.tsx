'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Logo } from '@web/components/logo';
import { login } from '@web/api_clients/user_client';
import { setSessionToken } from '@web/utils/cookie';

interface LoginHeaderSectionProps {
  className?: string;
}

export function LoginHeaderSection({ className = '' }: LoginHeaderSectionProps) {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim() || !password.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const res = await login({
        email,
        password,
      });

      if (res.sessionToken) {
        // セッショントークンをセキュアなクッキーとして保存
        setSessionToken(res.sessionToken);

        // パスワード変更が必要な場合は専用ページにリダイレクト
        if (res.mustChangePassword) {
          router.push('/change-password');
        } else {
          // 通常ログインの場合はメンバーページにリダイレクト
          // 確実に認証状態を反映するためページを完全にリロード
          window.location.href = '/member/';
        }
      }
    } catch (err) {
      console.error('ログインエラー:', err);
      setError('メールアドレスまたはパスワードが正しくありません');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className={`backdrop-blur-2xl backdrop-filter bg-white content-stretch flex items-center justify-between relative w-full min-h-[64px] ${className}`}
    >
      {/* カラーバー（上部） */}
      <div className="absolute bg-[#a51b2f] content-stretch flex h-1 items-start justify-start left-0 overflow-clip right-0 top-0">
        <div className="basis-0 bg-[#0e311e] grow h-1 min-h-px min-w-px shrink-0" />
        <div className="bg-[#f9d96d] h-1 shrink-0 w-8" />
        <div className="bg-[#a51b2f] h-1 shrink-0 w-8" />
      </div>

      <div className="basis-0 content-stretch flex grow h-full items-center justify-between max-w-[1600px] min-h-px min-w-px relative shrink-0 mx-auto">
        {/* ロゴセクション */}
        <div className="box-border content-stretch flex gap-2 h-full items-center justify-start px-4 py-0 relative shrink-0">
          <div className="hidden md:block">
            <Logo size="medium" variant="horizontal" />
          </div>
          {/* モバイル用ロゴ */}
          <div className="md:hidden">
            <Logo size="medium" variant="horizontal" />
          </div>
        </div>

        {/* ログインフォームセクション */}
        <div className="box-border content-stretch flex gap-4 h-full items-center justify-start px-4 py-0 relative shrink-0">
          {/* エラー表示 */}
          {error && (
            <div className="absolute top-full right-4 mt-2 p-2 bg-red-100 border border-red-400 text-red-700 rounded text-xs z-10">
              {error}
            </div>
          )}
          {/* デスクトップ用フォーム */}
          <form onSubmit={handleSubmit} className="content-stretch hidden md:flex gap-2 items-center justify-start relative shrink-0">
            {/* メールアドレスフィールド */}
            <div className="bg-[#f6f5f5] h-10 relative rounded-[2px] shrink-0 w-60">
              <div className="box-border content-stretch flex gap-2 h-10 items-center justify-start overflow-clip px-4 py-0 relative w-60">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="メールアドレス"
                  className="basis-0 bg-transparent font-['Inter:Regular',_'Noto_Sans_JP:Regular',_sans-serif] font-normal grow leading-[0] min-h-px min-w-px not-italic outline-none text-[#333] text-[12px] tracking-[0.24px] placeholder:text-[#b1afb0]"
                  disabled={isLoading}
                />
              </div>
              <div aria-hidden="true" className="absolute border border-[#dad9de] border-solid inset-0 pointer-events-none rounded-[2px]" />
            </div>

            {/* パスワードフィールド */}
            <div className="bg-[#f6f5f5] h-10 relative rounded-[2px] shrink-0 w-60">
              <div className="box-border content-stretch flex gap-2 h-10 items-center justify-start overflow-clip px-4 py-0 relative w-60">
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="パスワード"
                  className="basis-0 bg-transparent font-['Inter:Regular',_'Noto_Sans_JP:Regular',_sans-serif] font-normal grow leading-[0] min-h-px min-w-px not-italic outline-none text-[#333] text-[12px] tracking-[0.24px] placeholder:text-[#b1afb0]"
                  disabled={isLoading}
                />
              </div>
              <div aria-hidden="true" className="absolute border border-[#dad9de] border-solid inset-0 pointer-events-none rounded-[2px]" />
            </div>

            {/* ログインボタン */}
            <button
              type="submit"
              disabled={isLoading || !email.trim() || !password.trim()}
              className="bg-[#0e311e] box-border content-stretch flex flex-col gap-1 h-10 items-center justify-center overflow-clip px-3 py-2 relative rounded-[2px] shrink-0 hover:bg-[#0a2518] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="font-['Inter:Regular',_'Noto_Sans_JP:Regular',_sans-serif] font-normal leading-[0] not-italic relative shrink-0 text-[#f2fbf5] text-[14px] text-nowrap">
                <p className="leading-none whitespace-pre">{isLoading ? 'ログイン中...' : 'ログイン'}</p>
              </div>
            </button>
          </form>

          {/* モバイル用ログインボタン */}
          <div className="md:hidden">
            <button
              onClick={() => router.push('/login')}
              className="bg-[#0e311e] text-[#f2fbf5] px-4 py-2 rounded-[2px] text-sm hover:bg-[#0a2518] transition-colors"
            >
              ログイン
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
