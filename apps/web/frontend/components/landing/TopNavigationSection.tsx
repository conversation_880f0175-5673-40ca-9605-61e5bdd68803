'use client';

import Link from 'next/link';
import ChevronRightIcon from '@web/icons/chevron_right.svg';
import ClubGuideIcon from '@web/icons/club_guide.svg';
import JoinGuideIcon from '@web/icons/join_guide.svg';
import ImgUnion from '@web/icons/imgUnion.svg';
import RaceResultsIcon from '@web/icons/race_results.svg';
import RaceScheduleIcon from '@web/icons/race_schedule.svg';
import OfferedHorsesIcon from '@web/icons/offered_horses.svg';
import ColumnIcon from '@web/icons/column.svg';

// 定数
const MASK_BASE_URL = '/icons/img.svg';
const COLORS = {
  primary: '#0e311e',
  primaryHover: '#0a2518',
  primaryText: '#0e311e',
  white: 'white',
  grayBg: 'gray-100',
  grayText: 'gray-400',
  grayBorder: 'gray-300',
  primaryBorder: '#0e311e',
} as const;

const FONT_CLASSES = "font-['Inter:Medium',_'Noto_Sans_JP:Regular',_sans-serif] font-medium leading-[0] not-italic" as const;

// 共通のスタイルクラス
const COMMON_CLASSES = {
  container: 'box-border content-stretch flex gap-2 items-center justify-center overflow-clip p-4 relative size-full',
  containerVertical: 'box-border content-stretch flex flex-col gap-2 items-center justify-center overflow-clip p-4 relative size-full',
  containerSpecial: 'box-border content-stretch flex gap-3 items-center justify-start overflow-clip px-5 py-0 relative size-full',
  text: `${FONT_CLASSES} relative shrink-0 text-nowrap`,
  textPrimary: `${FONT_CLASSES} relative shrink-0 text-[${COLORS.primaryText}] text-nowrap`,
  textWhite: `${FONT_CLASSES} relative shrink-0 text-white text-nowrap`,
  textGray: `${FONT_CLASSES} relative shrink-0 text-${COLORS.grayText} text-nowrap`,
  textSpecial: `basis-0 ${FONT_CLASSES} grow min-h-px min-w-px relative shrink-0 text-white`,
  border: `absolute border border-[${COLORS.primaryBorder}] border-solid inset-0 pointer-events-none`,
  borderGray: `absolute border border-${COLORS.grayBorder} border-solid inset-0 pointer-events-none`,
} as const;

// アイコンコンポーネント
function HorsesIcon() {
  return (
    <div className="relative size-full">
      <div className="absolute inset-[4.89%_0.58%_-9.63%_-27.73%]">
        <ImgUnion className="block w-full h-full" />
      </div>
    </div>
  );
}

function MaskedIcon({ children, inset, maskPosition }: { children: React.ReactNode; inset: string; maskPosition: string }) {
  return (
    <div className="relative shrink-0 size-7">
      <div
        className={`absolute ${inset} mask-alpha mask-intersect mask-no-clip mask-no-repeat ${maskPosition} mask-size-[24px_24px]`}
        style={{ maskImage: `url('${MASK_BASE_URL}')` }}
      >
        {children}
      </div>
    </div>
  );
}

interface NavigationItem {
  id: string;
  title: string;
  icon?: React.ReactNode;
  href: string;
  gridArea: string;
  isSpecial?: boolean;
  isDisabled?: boolean;
}

interface TopNavigationSectionProps {
  className?: string;
}

export function TopNavigationSection({ className = '' }: TopNavigationSectionProps) {
  const navigationItems: NavigationItem[] = [
    {
      id: 'offered-horses',
      title: '募集馬',
      icon: (
        <MaskedIcon inset="inset-[10.42%_11.46%_12.5%_11.46%]" maskPosition="mask-position-[-2.75px_-2.5px]">
          <OfferedHorsesIcon className="block max-w-none w-full h-full" />
        </MaskedIcon>
      ),
      href: '/offered_horses',
      gridArea: '1 / 1 / auto / 5',
    },
    {
      id: 'horses-info',
      title: '現役馬情報',
      icon: (
        <div className="overflow-clip relative shrink-0 size-7">
          <HorsesIcon />
        </div>
      ),
      href: '#',
      gridArea: '1 / 5 / auto / 8',
      isDisabled: true,
    },
    {
      id: 'race-results',
      title: '競走結果',
      icon: (
        <MaskedIcon inset="inset-[14.58%_10.42%]" maskPosition="mask-position-[-2.5px_-3.5px]">
          <RaceResultsIcon className="block max-w-none w-full h-full" />
        </MaskedIcon>
      ),
      href: '#',
      gridArea: '1 / 8 / auto / 8',
      isDisabled: true,
    },
    {
      id: 'race-schedule',
      title: '出走予定',
      icon: (
        <MaskedIcon inset="inset-[9.94%_14.58%_10.42%_14.58%]" maskPosition="mask-position-[-3.5px_-2.385px]">
          <RaceScheduleIcon className="block max-w-none w-full h-full" />
        </MaskedIcon>
      ),
      href: '#',
      gridArea: '1 / 9 / auto / 9',
      isDisabled: true,
    },
    {
      id: 'club-guide',
      title: 'クラブ案内',
      icon: (
        <div className="relative shrink-0 size-7">
          <ClubGuideIcon width={28} height={28} />
        </div>
      ),
      href: '/club-guide',
      gridArea: '2 / 1 / auto / span 2',
    },
    {
      id: 'admission-guide',
      title: '入会案内',
      icon: (
        <div className="relative shrink-0 size-7">
          <JoinGuideIcon width={28} height={28} />
        </div>
      ),
      href: '/join',
      gridArea: '2 / 3 / auto / span 2',
    },
    {
      id: 'column',
      title: 'コラム',
      icon: (
        <div className="relative shrink-0 size-7">
          <ColumnIcon width={28} height={28} />
        </div>
      ),
      href: '/column',
      gridArea: '2 / 5 / auto / span 1',
    },
    {
      id: 'special-page',
      title: '2025年度 新規入会希望者 特設ページ',
      href: '/special/2025',
      gridArea: '2 / 6 / auto / span 4',
      isSpecial: true,
    },
  ];

  return (
    <div className={`flex flex-col items-center w-full ${className}`}>
      <div className="w-full max-w-[1600px] mx-auto">
        {/* モバイル版特設ページバナー */}
        <Link
          href="/special/2025"
          className={`md:hidden bg-[${COLORS.primary}] flex gap-3 items-center px-5 py-4 w-full mb-5 transition-all duration-200 hover:bg-[${COLORS.primaryHover}]`}
        >
          <p className={`${COMMON_CLASSES.textSpecial} text-sm`}>2025年度 新規入会希望者 特設ページ</p>
          <ChevronRightIcon className="shrink-0 size-6" />
        </Link>

        {/* タブレット版 (md:768px - lg:1024px) */}
        <div className="hidden md:grid lg:hidden gap-2 grid-cols-[repeat(6,_minmax(0px,_1fr))] grid-rows-[64px_64px_64px] px-5 py-5 w-full">
          {/* 募集馬 - 左半分（1-3列目） */}
          <Link
            href="/offered_horses"
            className="[grid-area:1_/_1_/_auto_/_span_3] bg-white relative transition-all duration-200 hover:bg-gray-50 hover:shadow-md flex gap-2 items-center justify-center p-4"
          >
            <MaskedIcon inset="inset-[10.42%_11.46%_12.5%_11.46%]" maskPosition="mask-position-[-2.75px_-2.5px]">
              <OfferedHorsesIcon className="block w-full h-full" />
            </MaskedIcon>
            <p className={`${COMMON_CLASSES.textPrimary} text-lg`}>募集馬</p>
            <div aria-hidden="true" className={COMMON_CLASSES.border} />
          </Link>
          {/* 特設ページ - 右半分（4-6列目） */}
          <Link
            href="/special/2025"
            className={`[grid-area:1_/_4_/_auto_/_span_3] bg-[${COLORS.primary}] relative transition-all duration-200 hover:bg-[${COLORS.primaryHover}] flex items-center justify-center p-2`}
          >
            <p className={`${COMMON_CLASSES.textWhite} text-lg text-center`}>2025年度 新規入会希望者 特設ページ</p>
          </Link>

          {/* 現役馬情報 - 無効化 */}
          <div
            className={`[grid-area:2_/_1_/_auto_/_span_2] bg-${COLORS.grayBg} relative cursor-not-allowed opacity-50 flex gap-2 items-center justify-center p-4`}
          >
            <div className="overflow-clip relative shrink-0 size-7">
              <HorsesIcon />
            </div>
            <p className={`${COMMON_CLASSES.textGray} text-base`}>現役馬情報</p>
            <div aria-hidden="true" className={COMMON_CLASSES.borderGray} />
          </div>

          {/* 競走結果 - 無効化 */}
          <div
            className={`[grid-area:2_/_3_/_auto_/_span_2] bg-${COLORS.grayBg} relative cursor-not-allowed opacity-50 flex gap-2 items-center justify-center p-4`}
          >
            <MaskedIcon inset="inset-[14.58%_10.42%]" maskPosition="mask-position-[-2.5px_-3.5px]">
              <RaceResultsIcon className="block w-full h-full" />
            </MaskedIcon>
            <p className={`${COMMON_CLASSES.textGray} text-base`}>競走結果</p>
            <div aria-hidden="true" className={COMMON_CLASSES.borderGray} />
          </div>

          {/* 出走予定 - 無効化 */}
          <div
            className={`[grid-area:2_/_5_/_auto_/_span_2] bg-${COLORS.grayBg} relative cursor-not-allowed opacity-50 flex gap-2 items-center justify-center p-4`}
          >
            <MaskedIcon inset="inset-[9.94%_14.58%_10.42%_14.58%]" maskPosition="mask-position-[-3.5px_-2.385px]">
              <RaceScheduleIcon className="block w-full h-full" />
            </MaskedIcon>
            <p className={`${COMMON_CLASSES.textGray} text-base`}>出走予定</p>
            <div aria-hidden="true" className={COMMON_CLASSES.borderGray} />
          </div>

          {/* クラブ案内 */}
          <Link
            href="/club-guide"
            className="[grid-area:3_/_1_/_auto_/_span_2] bg-white relative transition-all duration-200 hover:bg-gray-50 hover:shadow-md flex gap-2 items-center justify-center p-4"
          >
            <div className="relative shrink-0 size-7">
              <ClubGuideIcon width={28} height={28} />
            </div>
            <p className={`${COMMON_CLASSES.textPrimary} text-base`}>クラブ案内</p>
            <div aria-hidden="true" className={COMMON_CLASSES.border} />
          </Link>

          {/* 入会案内 */}
          <Link
            href="/join"
            className="[grid-area:3_/_3_/_auto_/_span_2] bg-white relative transition-all duration-200 hover:bg-gray-50 hover:shadow-md flex gap-2 items-center justify-center p-4"
          >
            <div className="relative shrink-0 size-7">
              <JoinGuideIcon width={28} height={28} />
            </div>
            <p className={`${COMMON_CLASSES.textPrimary} text-base`}>入会案内</p>
            <div aria-hidden="true" className={COMMON_CLASSES.border} />
          </Link>

          {/* コラム */}
          <Link
            href="/column"
            className="[grid-area:3_/_5_/_auto_/_span_2] bg-white relative transition-all duration-200 hover:bg-gray-50 hover:shadow-md flex gap-2 items-center justify-center p-4"
          >
            <div className="relative shrink-0 size-7">
              <ColumnIcon width={28} height={28} />
            </div>
            <p className={`${COMMON_CLASSES.textPrimary} text-base`}>コラム</p>
            <div aria-hidden="true" className={COMMON_CLASSES.border} />
          </Link>
        </div>

        {/* デスクトップ版 (lg:1024px以上) */}
        <div className="hidden lg:grid gap-2 grid-cols-[repeat(9,_minmax(0px,_1fr))] grid-rows-[64px_64px] px-5 py-5 w-full">
          {navigationItems.map((item) => {
            if (item.isDisabled) {
              return (
                <div
                  key={item.id}
                  style={{ gridArea: item.gridArea }}
                  className={`relative bg-${COLORS.grayBg} cursor-not-allowed opacity-50 flex gap-2 items-center justify-center p-4`}
                >
                  {item.icon}
                  <p className={`${COMMON_CLASSES.textGray} text-lg`}>{item.title}</p>
                  <div aria-hidden="true" className={COMMON_CLASSES.borderGray} />
                </div>
              );
            }

            return (
              <Link
                key={item.id}
                href={item.href}
                className={`relative transition-all duration-200 ${
                  item.isSpecial
                    ? `bg-[${COLORS.primary}] hover:bg-[${COLORS.primaryHover}] flex gap-3 items-center justify-start px-5 py-0`
                    : `bg-white hover:bg-gray-50 hover:shadow-md flex gap-2 items-center justify-center p-4`
                }`}
                style={{ gridArea: item.gridArea }}
              >
                {item.isSpecial ? (
                  <>
                    <p className={`${COMMON_CLASSES.textSpecial} text-lg`}>{item.title}</p>
                    <ChevronRightIcon className="shrink-0 size-6" />
                  </>
                ) : (
                  <>
                    {item.icon}
                    <p className={`${COMMON_CLASSES.textPrimary} text-lg`}>{item.title}</p>
                    <div aria-hidden="true" className={COMMON_CLASSES.border} />
                  </>
                )}
              </Link>
            );
          })}
        </div>

        {/* モバイル版 */}
        <div className="md:hidden gap-2 grid grid-cols-[repeat(3,_minmax(0px,_1fr))] grid-rows-[80px_80px_80px] px-5 w-full">
          {/* 募集馬 - 上部全幅 */}
          <Link
            href="/offered_horses"
            className="[grid-area:1_/_1_/_auto_/_span_3] bg-white relative transition-all duration-200 hover:bg-gray-50 hover:shadow-md flex flex-col gap-4 items-center justify-items-center p-4"
          >
            <MaskedIcon inset="inset-[10.42%_11.46%_12.5%_11.46%]" maskPosition="mask-position-[-2.75px_-2.5px]">
              <OfferedHorsesIcon className="block w-full h-full" />
            </MaskedIcon>
            <p className={`${COMMON_CLASSES.textPrimary} text-lg`}>募集馬</p>
            <div aria-hidden="true" className={COMMON_CLASSES.border} />
          </Link>

          {/* 現役馬情報 - 無効化 */}
          <div
            className={`[grid-area:2_/_1] bg-${COLORS.grayBg} relative cursor-not-allowed opacity-50 flex flex-col gap-4 items-center justify-items-center p-4`}
          >
            <div className="overflow-clip relative shrink-0 size-7">
              <HorsesIcon />
            </div>
            <p className={`${COMMON_CLASSES.textGray} text-lg`}>現役馬情報</p>
            <div aria-hidden="true" className={COMMON_CLASSES.borderGray} />
          </div>

          {/* 競走結果 - 無効化 */}
          <div
            className={`[grid-area:2_/_2] bg-${COLORS.grayBg} relative cursor-not-allowed opacity-50 flex flex-col gap-4 items-center justify-items-center p-4`}
          >
            <MaskedIcon inset="inset-[14.58%_10.42%]" maskPosition="mask-position-[-2.5px_-3.5px]">
              <RaceResultsIcon className="block w-full h-full" />
            </MaskedIcon>
            <p className={`${COMMON_CLASSES.textGray} text-lg`}>競走結果</p>
            <div aria-hidden="true" className={COMMON_CLASSES.borderGray} />
          </div>

          {/* 出走予定 - 無効化 */}
          <div
            className={`[grid-area:2_/_3] bg-${COLORS.grayBg} relative cursor-not-allowed opacity-50 flex flex-col gap-4 items-center justify-items-center p-4`}
          >
            <MaskedIcon inset="inset-[9.94%_14.58%_10.42%_14.58%]" maskPosition="mask-position-[-3.5px_-2.385px]">
              <RaceScheduleIcon className="block w-full h-full" />
            </MaskedIcon>
            <p className={`${COMMON_CLASSES.textGray} text-lg`}>出走予定</p>
            <div aria-hidden="true" className={COMMON_CLASSES.borderGray} />
          </div>

          {/* クラブ案内 */}
          <Link
            href="/club-guide"
            className="[grid-area:3_/_1_/_auto_/_span_1] bg-white relative transition-all duration-200 hover:bg-gray-50 hover:shadow-md flex flex-col gap-4 items-center justify-items-center p-4"
          >
            <div className="relative shrink-0 size-7">
              <ClubGuideIcon width={28} height={28} />
            </div>
            <p className={`${COMMON_CLASSES.textPrimary} text-lg`}>クラブ案内</p>
            <div aria-hidden="true" className={COMMON_CLASSES.border} />
          </Link>

          {/* 入会案内 */}
          <Link
            href="/join"
            className="[grid-area:3_/_2_/_auto_/_span_1] bg-white relative transition-all duration-200 hover:bg-gray-50 hover:shadow-md flex flex-col gap-4 items-center justify-items-center p-4"
          >
            <div className="relative shrink-0 size-7">
              <JoinGuideIcon width={28} height={28} />
            </div>
            <p className={`${COMMON_CLASSES.textPrimary} text-lg`}>入会案内</p>
            <div aria-hidden="true" className={COMMON_CLASSES.border} />
          </Link>

          {/* コラム */}
          <Link
            href="/column"
            className="[grid-area:3_/_3_/_auto_/_span_1] bg-white relative transition-all duration-200 hover:bg-gray-50 hover:shadow-md flex flex-col gap-4 items-center justify-items-center p-4"
          >
            <div className="relative shrink-0 size-7">
              <ColumnIcon width={28} height={28} />
            </div>
            <p className={`${COMMON_CLASSES.textPrimary} text-lg`}>コラム</p>
            <div aria-hidden="true" className={COMMON_CLASSES.border} />
          </Link>
        </div>
      </div>
    </div>
  );
}
