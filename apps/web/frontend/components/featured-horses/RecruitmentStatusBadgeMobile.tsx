import { RecruitmentStatus } from '@hami/web-api-schema/recruitment_horse_service_pb';

interface RecruitmentStatusBadgeMobileProps {
  status?: RecruitmentStatus;
  className?: string;
}

// モバイル用 募集ステータスバッジ（募集中 / 満口 のみ）
// Desktop 同様、absolute/relative は使わず、入れ子構造で重ね表現
export default function RecruitmentStatusBadgeMobile({ status, className = '' }: RecruitmentStatusBadgeMobileProps) {
  if (status !== RecruitmentStatus.ACTIVE && status !== RecruitmentStatus.FULL) return null;

  const isActive = status === RecruitmentStatus.ACTIVE;
  const baseColor = isActive ? '#266f44' : '#b61a2b';
  const borderOuter = isActive ? '#013115' : '#82010f';
  const textColor = '#f6f5f5';

  return (
    <div className={`inline-block ${className}`} aria-label={isActive ? '募集中' : '満口'}>
      {/* 外側ブロック（濃色背景＋外枠＋ドロップ影） */}
      <div
        className="rounded-[12px] border border-solid"
        style={{ backgroundColor: baseColor, borderColor: borderOuter, boxShadow: '0px 7px 3px -6px rgba(12,18,57,0.6)' }}
      >
        {/* 内側ブロック（2px マージン、白ボーダー、角丸10px） */}
        <div
          className="m-[2px] rounded-[10px] border border-white px-3 py-1 flex items-center justify-center"
          style={{ backgroundColor: baseColor }}
        >
          <span className="text-[11px] leading-none font-semibold" style={{ color: textColor }}>
            {isActive ? '募集中' : '満口'}
          </span>
        </div>
      </div>
    </div>
  );
}


