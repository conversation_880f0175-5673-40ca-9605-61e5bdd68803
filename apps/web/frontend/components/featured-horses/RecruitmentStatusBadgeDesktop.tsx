import { RecruitmentStatus } from '@hami/web-api-schema/recruitment_horse_service_pb';

interface RecruitmentStatusBadgeProps {
  status?: RecruitmentStatus;
  className?: string;
}

// デスクトップ用 募集ステータスバッジ（募集中 / 満口 のみ）
export default function RecruitmentStatusBadgeDesktop({ status, className = '' }: RecruitmentStatusBadgeProps) {
  if (status !== RecruitmentStatus.ACTIVE && status !== RecruitmentStatus.FULL) return null;

  const isActive = status === RecruitmentStatus.ACTIVE;

  const baseColor = isActive ? '#266f44' : '#b61a2b';
  const borderOuter = isActive ? '#013115' : '#82010f';
  const textColor = '#f6f5f5';

  return (
    <div className={`inline-block ${className}`} aria-label={isActive ? '募集中' : '満口'}>
      <div
        className="rounded-[24px] border border-solid"
        style={{
          backgroundColor: baseColor,
          borderColor: borderOuter,
          boxShadow: '0px 14px 6px -12px rgba(12,18,57,0.6)'
        }}
      >
        <div
          className="m-1 rounded-[20px] border border-white px-6 py-3 flex items-center justify-center"
          style={{ backgroundColor: baseColor }}
        >
          <span className="font-semibold tracking-[0.2px]" style={{ color: textColor }}>
            {isActive ? '募集中' : '満口'}
          </span>
        </div>
      </div>
    </div>
  );
}


