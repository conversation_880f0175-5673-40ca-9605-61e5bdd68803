import Link from 'next/link';
import { getHeadlineAnnouncements } from '@web/api_clients/announcement_client';
import type { PublicAnnouncement } from '@hami/web-api-schema/announcement_service_pb';
import { HeadlineCard } from './HeadlineCard';
import ChevronDownIcon from '@web/icons/chevron_down.svg';

interface Props {
  limit?: number;
  className?: string;
}

export default async function HeadlineAnnouncementsServer({ limit = 10, className = '' }: Props) {
  try {
    const { announcements } = await getHeadlineAnnouncements({ limit });

    return (
      <section className={`mx-auto px-5 py-10 ${className}`}>
        {/* デスクトップ版 */}
        <div className="hidden md:block">
          <div className="flex items-center justify-between border-b border-[#d1d0d0] pb-3">
            <h2 className="text-[#0e311e] text-2xl font-normal">お知らせ</h2>
            <Link href="/announcements" className="text-hami-text-primary text-sm hover:underline">
              すべて見る
            </Link>
          </div>
          {announcements.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-[#b1afb0]">現在、お知らせはありません。</p>
            </div>
          ) : (
            <div>
              {announcements.map((a: PublicAnnouncement) => (
                <HeadlineCard key={a.announcementId} announcement={a} />
              ))}
            </div>
          )}
        </div>

        {/* モバイル版（SSR: 初期表示は展開状態）*/}
        <div className="md:hidden">
          <div className="bg-[#f6f5f5] box-border flex gap-2 items-center justify-start px-4 py-3 relative w-full">
            <div className="basis-0 font-['Inter:Regular','Noto_Sans_JP:Regular',sans-serif] font-normal grow leading-[0]">
              <p className="leading-[1.4]">お知らせ</p>
            </div>
            <div className="relative shrink-0 size-6">
              <ChevronDownIcon width={24} height={24} />
            </div>
          </div>
          <div className="bg-white border-t border-[#d1d0d0] space-y-4">
            {announcements.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-[#b1afb0]">現在、お知らせはありません。</p>
              </div>
            ) : (
              announcements.map((a: PublicAnnouncement) => <HeadlineCard key={a.announcementId} announcement={a} />)
            )}
            <div className="mt-4">
              <Link href="/announcements" className="text-hami-text-primary text-sm hover:underline">
                すべて見る
              </Link>
            </div>
          </div>
        </div>
      </section>
    );
  } catch (_e) {
    return (
      <section className={`mx-auto px-5 py-10 ${className}`}>
        <div className="hidden md:block">
          <div className="flex items-center justify-between border-b border-[#d1d0d0] pb-3">
            <h2 className="text-[#0e311e] text-2xl font-normal">お知らせ</h2>
          </div>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
            <p className="text-sm text-red-800">お知らせの取得に失敗しました。</p>
          </div>
        </div>
        <div className="md:hidden">
          <div className="bg-[#f6f5f5] box-border flex gap-2 items-center justify-start px-4 py-3 relative w-full">
            <div className="basis-0 font-['Inter:Regular','Noto_Sans_JP:Regular',sans-serif] font-normal grow leading-[0]">
              <p className="leading-[1.4]">お知らせ</p>
            </div>
            <div className="relative shrink-0 size-6">
              <ChevronDownIcon width={24} height={24} />
            </div>
          </div>
          <div className="bg-white border-t border-[#d1d0d0] p-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-800">お知らせの取得に失敗しました。</p>
            </div>
          </div>
        </div>
      </section>
    );
  }
}
