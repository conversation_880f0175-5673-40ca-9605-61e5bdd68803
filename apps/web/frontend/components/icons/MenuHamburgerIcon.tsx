import React from 'react';

interface MenuHamburgerIconProps {
  width?: number;
  height?: number;
  className?: string;
}

export function MenuHamburgerIcon({ width = 24, height = 24, className = '' }: MenuHamburgerIconProps) {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <mask id="mask0_hamburger" style={{ maskType: 'alpha' }} maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
        <rect width="24" height="24" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_hamburger)">
        <path d="M3 18V16H21V18H3ZM3 13V11H21V13H3ZM3 8V6H21V8H3Z" fill="currentColor" />
      </g>
    </svg>
  );
}
