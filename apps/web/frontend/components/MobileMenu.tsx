'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import HomeIcon from '@web/icons/icon_home.svg';
import OfferedHorsesIcon from '@web/icons/offered_horses.svg';
import ImgUnion from '@web/icons/imgUnion.svg';
import RaceResultsIcon from '@web/icons/race_results.svg';
import RaceScheduleIcon from '@web/icons/race_schedule.svg';
import ClubGuideIcon from '@web/icons/club_guide.svg';
import JoinGuideIcon from '@web/icons/join_guide.svg';

// MaskedIconコンポーネント（TopNavigationSectionから移植）
const MASK_BASE_URL = '/icons/img.svg';

function MaskedIcon({ children, inset, maskPosition }: {
  children: React.ReactNode;
  inset: string;
  maskPosition: string;
}) {
  return (
    <div className="relative shrink-0 size-7">
      <div
        className={`absolute ${inset} mask-alpha mask-intersect mask-no-clip mask-no-repeat ${maskPosition} mask-size-[24px_24px]`}
        style={{ maskImage: `url('${MASK_BASE_URL}')` }}
      >
        {children}
      </div>
    </div>
  );
}

// HorsesIconコンポーネント（TopNavigationSectionから移植）
function HorsesIcon() {
  return (
    <div className="relative size-full">
      <div className="absolute inset-[4.89%_0.58%_-9.63%_-27.73%]">
        <ImgUnion className="block w-full h-full" />
      </div>
    </div>
  );
}

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileMenu({ isOpen, onClose }: MobileMenuProps) {
  // React Hooksは早期リターンの前に呼び出す必要がある
  const menuItems = useMemo(() => [
    {
      title: 'ホーム',
      href: '/',
      disabled: false,
      icon: (
        <div className="relative shrink-0 size-7 flex items-center justify-center">
          <HomeIcon width={26} height={19} />
        </div>
      ),
    },
    {
      title: '募集馬',
      href: '/offered_horses',
      disabled: false,
      icon: (
        <MaskedIcon
          inset="inset-[10.42%_11.46%_12.5%_11.46%]"
          maskPosition="mask-position-[-2.75px_-2.5px]"
        >
          <OfferedHorsesIcon className="block max-w-none w-full h-full" />
        </MaskedIcon>
      ),
    },
    {
      title: '現役馬情報',
      href: '/horses',
      disabled: true,
      icon: (
        <div className="overflow-clip relative shrink-0 size-7">
          <HorsesIcon />
        </div>
      ),
    },
    {
      title: '競走結果',
      href: '/race-results',
      disabled: true,
      icon: (
        <MaskedIcon
          inset="inset-[14.58%_10.42%]"
          maskPosition="mask-position-[-2.5px_-3.5px]"
        >
          <RaceResultsIcon className="block max-w-none w-full h-full" />
        </MaskedIcon>
      ),
    },
    {
      title: '出走予定',
      href: '/race-schedule',
      disabled: true,
      icon: (
        <MaskedIcon
          inset="inset-[9.94%_14.58%_10.42%_14.58%]"
          maskPosition="mask-position-[-3.5px_-2.385px]"
        >
          <RaceScheduleIcon className="block max-w-none w-full h-full" />
        </MaskedIcon>
      ),
    },
    {
      title: 'クラブ案内',
      href: '/club-guide',
      disabled: false,
      icon: (
        <div className="relative shrink-0 size-7">
          <ClubGuideIcon width={28} height={28} />
        </div>
      ),
    },
    {
      title: '入会案内',
      href: '/join',
      disabled: false,
      icon: (
        <div className="relative shrink-0 size-7">
          <JoinGuideIcon width={28} height={28} />
        </div>
      ),
    },
  ], []); // 空の依存配列 - メニュー項目は静的なので初回のみ作成

  // メニューが閉じている時は表示しない
  if (!isOpen) return null;

  // アニメーション用のクラス（梯子のように上から下にスライド）
  const animationClasses = 'translate-y-0 pointer-events-auto';

  return (
    <div className={`fixed top-[64px] left-0 right-0 bottom-0 bg-white z-[60] border-t border-[#e7e6e6] backdrop-blur-none transform transition-transform duration-400 ease-out sm:hidden ${animationClasses}`} style={{ backgroundColor: '#ffffff' }}>
      <div className="flex flex-col h-full bg-white">
        {menuItems.map((item, index) => {
          if (item.disabled) {
            return (
              <div
                key={index}
                className="relative w-full bg-white cursor-not-allowed"
              >
                <div className="box-border content-stretch flex gap-[8px] items-center overflow-clip px-0 py-[12px] relative w-full bg-white opacity-50">
                  <div className="relative shrink-0 size-[28px] ml-4">
                    {item.icon}
                  </div>
                  <p className="basis-0 font-['Inter:Regular',_'Noto_Sans_JP:Regular',_sans-serif] font-normal grow leading-none min-h-px min-w-px not-italic relative shrink-0 text-[#0e311e] text-[18px] tracking-[2.88px]">
                    {item.title}
                  </p>
                </div>
                <div aria-hidden="true" className="absolute border-[#e7e6e6] border-[0px_0px_1px] border-solid inset-0 pointer-events-none" />
              </div>
            );
          }

          return (
            <Link
              key={index}
              href={item.href}
              onClick={onClose}
              className="relative w-full bg-white"
            >
              <div className="box-border content-stretch flex gap-[8px] items-center overflow-clip px-0 py-[12px] relative w-full bg-white">
                <div className="relative shrink-0 size-[28px] ml-4">
                  {item.icon}
                </div>
                <p className="basis-0 font-['Inter:Regular',_'Noto_Sans_JP:Regular',_sans-serif] font-normal grow leading-none min-h-px min-w-px not-italic relative shrink-0 text-[#0e311e] text-[18px] tracking-[2.88px]">
                  {item.title}
                </p>
              </div>
              <div aria-hidden="true" className="absolute border-[#e7e6e6] border-[0px_0px_1px] border-solid inset-0 pointer-events-none" />
            </Link>
          );
        })}
      </div>
    </div>
  );
}
