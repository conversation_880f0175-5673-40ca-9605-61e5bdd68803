'use client';

import IconDocument from '@web/icons/icon_document.svg';

export interface PdfButtonProps {
  /** PDFファイルのパス */
  filePath: string;
  /** 表示するファイル名 */
  fileName: string;
  /** ボタンのスタイルクラス（オプション） */
  className?: string;
  /** アイコンの色（デフォルト: #0e311e） */
  iconColor?: string;
  /** ボタンのサイズ（デフォルト: medium） */
  size?: 'small' | 'medium' | 'large';
}

export function PdfButton({ filePath, fileName, className = '', iconColor = '#0e311e', size = 'medium' }: PdfButtonProps) {
  // サイズに応じたスタイル
  const sizeStyles = {
    small: {
      button: 'px-2 py-1 text-sm gap-1',
      icon: 'w-4 h-4',
      iconSize: { width: 16, height: 16 },
    },
    medium: {
      button: 'px-2 py-1 text-base gap-1',
      icon: 'w-6 h-6',
      iconSize: { width: 24, height: 24 },
    },
    large: {
      button: 'px-3 py-2 text-lg gap-2',
      icon: 'w-8 h-8',
      iconSize: { width: 32, height: 32 },
    },
  };

  const currentSize = sizeStyles[size];

  const defaultClassName = `
    bg-white
    border border-[#0e311e] border-opacity-50
    hover:border-opacity-100
    transition-colors duration-200
    inline-flex items-center
    text-[#0e311e]
    font-normal
    leading-none
    group
    ${currentSize.button}
  `
    .trim()
    .replace(/\s+/g, ' ');

  return (
    <a href={filePath} target="_blank" rel="noopener noreferrer" className={`${defaultClassName} ${className}`}>
      <div className={`flex-shrink-0 ${currentSize.icon}`}>
        <IconDocument width={currentSize.iconSize.width} height={currentSize.iconSize.height} color={iconColor} />
      </div>
      <span className="flex-grow text-left">{fileName}</span>
    </a>
  );
}
