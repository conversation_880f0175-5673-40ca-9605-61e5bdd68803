import React, { useState, useEffect } from 'react';
import { RequiredBadge } from './badges';

interface ControlledBirthDateInputProps {
  id: string;
  name: string;
  label: string;
  description?: string;
  value: string | undefined;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
}

export const ControlledBirthDateInput: React.FC<ControlledBirthDateInputProps> = ({
  id,
  name,
  label,
  description,
  value,
  onChange,
  placeholder = "例: 19901225",
  required = false,
  className = '',
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [warning, setWarning] = useState<string | null>(null);

  // valueが変更された時にinputValueを更新
  useEffect(() => {
    if (value) {
      // YYYY-MM-DD形式からYYYYMMDD形式に変換
      const formatted = value.replace(/-/g, '');
      setInputValue(formatted);
    } else {
      setInputValue('');
    }
  }, [value]);

  const formatDate = (input: string): string => {
    // 数字のみを抽出
    const numbers = input.replace(/\D/g, '');

    if (numbers.length === 0) return '';
    if (numbers.length <= 4) return numbers;
    if (numbers.length <= 6) return `${numbers.slice(0, 4)}-${numbers.slice(4)}`;
    return `${numbers.slice(0, 4)}-${numbers.slice(4, 6)}-${numbers.slice(6, 8)}`;
  };

  const validateDate = (dateString: string): { isValid: boolean; error?: string; warning?: string } => {
    if (!dateString) return { isValid: true };

    const numbers = dateString.replace(/\D/g, '');

    // 入力桁数に応じた詳細なバリデーション
    if (numbers.length < 4) {
      return { isValid: true, warning: '年を4桁で入力してください（例: 1991）' };
    }

    if (numbers.length < 6) {
      const year = parseInt(numbers.slice(0, 4));
      const currentYear = new Date().getFullYear();
      if (year < 1900 || year > currentYear) {
        return { isValid: false, error: `年は1900年から${currentYear}年の間で入力してください` };
      }
      return { isValid: true, warning: '月を2桁で入力してください（例: 01）' };
    }

    if (numbers.length < 8) {
      const year = parseInt(numbers.slice(0, 4));
      const month = parseInt(numbers.slice(4, 6));
      const currentYear = new Date().getFullYear();

      if (year < 1900 || year > currentYear) {
        return { isValid: false, error: `年は1900年から${currentYear}年の間で入力してください` };
      }

      if (month < 1 || month > 12) {
        return { isValid: false, error: '月は01から12の間で入力してください' };
      }

      return { isValid: true, warning: '日を2桁で入力してください（例: 01）' };
    }

    const year = parseInt(numbers.slice(0, 4));
    const month = parseInt(numbers.slice(4, 6));
    const day = parseInt(numbers.slice(6, 8));

    // 年の範囲チェック
    const currentYear = new Date().getFullYear();
    if (year < 1900 || year > currentYear) {
      return { isValid: false, error: `年は1900年から${currentYear}年の間で入力してください` };
    }

    // 月の範囲チェック
    if (month < 1 || month > 12) {
      return { isValid: false, error: '月は01から12の間で入力してください' };
    }

    // 日の範囲チェック
    if (day < 1 || day > 31) {
      return { isValid: false, error: '日は01から31の間で入力してください' };
    }

    // 実際の日付として有効かチェック
    const date = new Date(year, month - 1, day);
    if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
      return { isValid: false, error: `${year}年${month}月${day}日は存在しない日付です` };
    }

    // 未来の日付チェック
    if (date > new Date()) {
      return { isValid: false, error: '未来の日付は入力できません' };
    }

    // 年齢チェック（あまりに古い日付の警告）
    const age = currentYear - year;
    if (age > 120) {
      return { isValid: false, error: '120歳を超える生年月日は入力できません' };
    }

    return { isValid: true };
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;

    // 数字のみを抽出して8桁まで制限
    const numbers = input.replace(/\D/g, '').slice(0, 8);

    setInputValue(numbers);

    // バリデーション
    const validation = validateDate(numbers);
    if (!validation.isValid) {
      setError(validation.error || null);
      setWarning(null);
    } else {
      setError(null);
      setWarning(validation.warning || null);
    }

    // 8桁入力完了時にフォーマットしてonChangeを呼び出し
    if (numbers.length === 8) {
      const formatted = formatDate(numbers);
      onChange(formatted);
    } else if (numbers.length === 0) {
      onChange('');
    }
  };

  const handleBlur = () => {
    // フォーカスが外れた時に最終的なフォーマットを適用
    if (inputValue.length === 8) {
      const formatted = formatDate(inputValue);
      const validation = validateDate(inputValue);

      if (validation.isValid) {
        onChange(formatted);
        setWarning(null);
      }
    } else if (inputValue.length > 0) {
      // 入力途中でフォーカスが外れた場合の警告
      const validation = validateDate(inputValue);
      if (validation.warning) {
        setWarning(validation.warning);
      }
    }
  };

  const displayValue = inputValue.length === 8 ? formatDate(inputValue) : inputValue;

  return (
    <div className={className}>
      <label htmlFor={id} className="flex items-center text-sm font-medium text-gray-700 mb-1">
        <span className="text-hami-glyph-subtle font-bold">{label}</span>
        {required && <RequiredBadge />}
      </label>
      {description && <div className="text-sm text-hami-glyph-modest mb-2">{description}</div>}
      <input
        id={id}
        name={name}
        type="text"
        value={displayValue}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        maxLength={10} // YYYY-MM-DD形式の最大長
        className={`mt-1 block w-full rounded-md border ${
          error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' :
          warning ? 'border-yellow-400 focus:border-yellow-400 focus:ring-yellow-400' :
          'border-hami-stroke-border focus:border-indigo-500 focus:ring-indigo-500'
        } bg-hami-topaz-100 sm:text-sm px-3 py-2 transition-colors ${
          disabled ? 'bg-gray-100 text-gray-500' : ''
        }`}
      />

      {/* エラーメッセージ */}
      {error && (
        <div className="mt-2 flex items-start space-x-2">
          <div className="flex-shrink-0 mt-0.5">
            <svg className="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="text-sm text-red-600 font-medium">{error}</div>
        </div>
      )}

      {/* 警告メッセージ */}
      {warning && !error && (
        <div className="mt-2 flex items-start space-x-2">
          <div className="flex-shrink-0 mt-0.5">
            <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="text-sm text-yellow-600 font-medium">{warning}</div>
        </div>
      )}
    </div>
  );
};
