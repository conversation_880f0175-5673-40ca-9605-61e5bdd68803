import React from 'react';
import { OptionalBadge, RequiredBadge } from './badges';

interface ControlledPostalCodeInputProps {
  id: string;
  name: string;
  label: string;
  value: string | number | undefined;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  className?: string;
  maxLength?: number;
  min?: number | string;
  max?: number | string;
  disabled?: boolean;
  optional?: boolean;
}

export const PATTERN_POSTAL_CODE = '[0-9]{3}-[0-9]{4}';

export const ControlledPostalCodeInput: React.FC<ControlledPostalCodeInputProps> = ({
  id,
  name,
  label,
  value,
  onChange,
  placeholder,
  required = false,
  className = '',
  maxLength,
  min,
  max,
  disabled = false,
  optional = false,
}) => {
  const formatPostalCode = (inputValue: string) => {
    const numbers = inputValue.replace(/\D/g, '').slice(0, 7);
    if (numbers.length < 3) return numbers;
    if (numbers.length === 3) return `${numbers}-`;
    return `${numbers.slice(0, 3)}-${numbers.slice(3)}`;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    onChange(formatPostalCode(rawValue));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const currentValue = String(value ?? '');
    const isHyphenAtIndex3 = currentValue.charAt(3) === '-';
    const inputEl = e.target as HTMLInputElement;
    const selectionStart = inputEl.selectionStart ?? 0;
    const selectionEnd = inputEl.selectionEnd ?? 0;

    if (e.key === 'Backspace' && selectionStart === selectionEnd && selectionStart === 4 && isHyphenAtIndex3) {
      e.preventDefault();
      const digits = currentValue.replace(/\D/g, '');
      const newDigits = digits.slice(0, 2) + digits.slice(3);
      onChange(formatPostalCode(newDigits));
      return;
    }

    if (e.key === 'Delete' && selectionStart === selectionEnd && selectionStart === 3 && isHyphenAtIndex3) {
      e.preventDefault();
      const digits = currentValue.replace(/\D/g, '');
      const newDigits = digits.slice(0, 2) + digits.slice(3);
      onChange(formatPostalCode(newDigits));
      return;
    }
  };

  const effectiveMaxLength = maxLength ?? 8;

  return (
    <div className={className}>
      <label htmlFor={id} className="flex items-center text-sm font-medium text-gray-700 mb-1">
        <span className="text-hami-glyph-subtle font-bold">{label}</span>
        {optional && <OptionalBadge />}
        {required && <RequiredBadge />}
      </label>
      <input
        id={id}
        name={name}
        type="text"
        value={value ?? ''}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        required={required}
        inputMode="numeric"
        pattern={PATTERN_POSTAL_CODE}
        maxLength={effectiveMaxLength}
        min={min}
        max={max}
        disabled={disabled}
        className="mt-1 block w-full rounded-md border border-hami-stroke-border bg-hami-topaz-100 focus:border-indigo-500 focus:ring-indigo-500 text-hami-glyph-base sm:text-sm px-3 py-2"
      />
    </div>
  );
};


