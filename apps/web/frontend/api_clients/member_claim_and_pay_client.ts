'use server';

import { MemberClaimAndPayService,
  ListMemberClaimAndPaysRequestSchema,
  RequestStatementPdfDownloadUrlRequestSchema,
} from '@hami/web-api-schema/member_claim_and_pay_service_pb';
import { getClient } from '@web/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@web/utils/api_error_handlers';

const client = getClient(MemberClaimAndPayService);

export const listMemberClaimAndPays = async (req: MessageInitShape<typeof ListMemberClaimAndPaysRequestSchema>) => withAuthErrorHandling(() => client.listMemberClaimAndPays(create(ListMemberClaimAndPaysRequestSchema, req)));

export const requestStatementPdfDownloadUrl = async (req: MessageInitShape<typeof RequestStatementPdfDownloadUrlRequestSchema>) => withAuthErrorHandling(() => client.requestStatementPdfDownloadUrl(create(RequestStatementPdfDownloadUrlRequestSchema, req)));

