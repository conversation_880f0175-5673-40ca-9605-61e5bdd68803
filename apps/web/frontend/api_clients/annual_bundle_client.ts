'use server';

import { AnnualBundleService,
  ListAnnualBundlesByFiscalYearRequestSchema,
} from '@hami/web-api-schema/annual_bundle_service_pb';
import { getClient } from '@web/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@web/utils/api_error_handlers';

const client = getClient(AnnualBundleService);

export const listAnnualBundlesByFiscalYear = async (req: MessageInitShape<typeof ListAnnualBundlesByFiscalYearRequestSchema>) => withAuthErrorHandling(() => client.listAnnualBundlesByFiscalYear(create(ListAnnualBundlesByFiscalYearRequestSchema, req)));

