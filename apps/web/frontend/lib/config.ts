/**
 * Frontend configuration and environment validation.
 *
 * Note:
 * - We avoid hardcoded fallbacks.
 * - In production, FRONTEND_ENDPOINT must be provided and valid.
 */

export function getFrontendEndpoint(): URL | undefined {
  const raw = process.env.FRONTEND_ENDPOINT;

  if (!raw) {
    const isProd = process.env.NODE_ENV === 'production';
    const isBuildPhase = process.env.NEXT_PHASE === 'phase-production-build';
    // ビルド時（phase-production-build）は落とさずundefinedで進める。
    // ランタイム本番時に未設定ならエラーとする。
    if (isProd && !isBuildPhase) {
      throw new Error('FRONTEND_ENDPOINT is required in production');
    }
    return undefined;
  }

  try {
    const url = new URL(raw);
    if (!/^https?:$/.test(url.protocol)) {
      throw new Error('FRONTEND_ENDPOINT must be http(s) URL');
    }
    return url;
  } catch (e) {
    throw new Error(`Invalid FRONTEND_ENDPOINT: ${(e as Error).message}`);
  }
}
