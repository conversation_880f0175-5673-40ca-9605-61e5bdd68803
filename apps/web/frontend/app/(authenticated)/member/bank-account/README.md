# 銀行口座登録機能

GMO口座振替APIを使用した銀行口座登録フォームの実装です。

## 機能概要

- 金融機関選択（主要銀行 + ネット銀行）
- 口座情報入力（支店コード、口座番号、名義など）
- リアルタイムバリデーション
- GMO APIとの連携
- コールバック処理

## ファイル構成

```
app/(authenticated)/bank-account/
├── register/
│   ├── page.tsx                    # メインページ
│   └── components/
│       ├── BankAccountForm.tsx     # メインフォーム
│       ├── BankSelectionForm.tsx   # 金融機関選択
│       ├── AccountInfoForm.tsx     # 口座情報入力
│       ├── BankAccountConfirm.tsx  # 確認画面
│       └── __tests__/
│           └── BankAccountForm.test.tsx
├── callback/
│   └── page.tsx                    # GMOコールバック処理
└── README.md
```

## 関連ファイル

```
constants/
└── bank-codes.ts                   # 銀行コード定数

types/
└── bank-account.ts                 # 型定義

utils/
└── bank-account-validation.ts      # バリデーション関数

api_clients/
└── bank_account_client.ts          # APIクライアント
```

## 使用方法

### 1. 口座登録フォーム

`/bank-account/register` にアクセスして口座登録を開始します。

#### ステップ1: 金融機関選択
- 主要銀行から選択
- 検索機能で絞り込み
- 金融機関コード直接入力も可能

#### ステップ2: 口座情報入力
- 支店コード（3桁）
- 預金区分（普通/当座）
- 口座番号（7-8桁）
- 口座名義（カナ・漢字）

#### ステップ3: 確認・申請
- 入力内容の確認
- 利用規約への同意
- GMO APIへの申請

### 2. コールバック処理

GMOでの手続き完了後、`/bank-account/callback` にリダイレクトされ、結果を処理します。

## バリデーション

### 金融機関コード
- 4桁の数字
- 有効な金融機関コードかチェック

### 支店コード
- 3桁の数字

### 口座番号
- 7桁または8桁の数字

### 口座名義（カナ）
- カタカナのみ
- 60文字以内

### 口座名義（漢字）
- 日本語文字（ひらがな・カタカナ・漢字）
- 60文字以内

## エラーハンドリング

- リアルタイムバリデーション
- APIエラーの適切な表示
- ユーザーフレンドリーなエラーメッセージ
- ネットワークエラーの処理

## セキュリティ

- 認証必須（authenticated ルート）
- 入力データのサニタイズ
- CSRFトークン（フレームワーク標準）
- HTTPSでの通信

## テスト

```bash
# 単体テスト実行
npm test BankAccountForm.test.tsx
```

## 技術スタック

- **フレームワーク**: Next.js 15 (App Router)
- **UI**: React + TypeScript
- **スタイリング**: Tailwind CSS
- **バリデーション**: Zod + カスタム関数
- **状態管理**: React useState
- **API通信**: gRPC (Connect-Web)

## 今後の改善点

- [ ] 支店名の自動補完
- [ ] 口座名義の自動変換（ひらがな→カタカナ）
- [ ] より詳細な金融機関情報
- [ ] オフライン対応
- [ ] アクセシビリティの向上
