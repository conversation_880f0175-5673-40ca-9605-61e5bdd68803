import React, { useEffect, useMemo, useState } from 'react';

// かな正規化: ひらがな→カタカナ、互換正規化 + 小書きカナを大書きへ展開（トップレベルで定義して安定化）
const toKatakana = (input: string) => input.replace(/[\u3041-\u3096]/g, (ch) => String.fromCharCode(ch.charCodeAt(0) + 0x60));
const expandSmallKana = (input: string) =>
  input
    .replace(/ァ/g, 'ア')
    .replace(/ィ/g, 'イ')
    .replace(/ゥ/g, 'ウ')
    .replace(/ェ/g, 'エ')
    .replace(/ォ/g, 'オ')
    .replace(/ャ/g, 'ヤ')
    .replace(/ュ/g, 'ユ')
    .replace(/ョ/g, 'ヨ')
    .replace(/ッ/g, 'ツ')
    .replace(/ヮ/g, 'ワ')
    .replace(/ヵ/g, 'カ')
    .replace(/ヶ/g, 'ケ');
const normalizeKana = (input: string) => expandSmallKana(toKatakana(input).normalize('NFKC'));
import { ControlledInput, ControlledSelect } from '@web/components/forms';
import { ACCOUNT_TYPES, getBankByCode } from '@web/constants/bank-codes';
import type { BankAccountFormData, BankAccountFormErrors } from '@web/types/bank-account';

interface AccountInfoFormProps {
  formData: BankAccountFormData;
  onChange: (field: keyof BankAccountFormData, value: string | number) => void;
  errors: BankAccountFormErrors;
}

export const AccountInfoForm: React.FC<AccountInfoFormProps> = ({ formData, onChange, errors }) => {
  const selectedBank = getBankByCode(formData.bankCode);

  type Branch = {
    branch_code: string;
    branch_name: string;
    branch_name_kana: string;
  };

  const [branches, setBranches] = useState<Branch[]>([]);
  const [branchesLoading, setBranchesLoading] = useState(false);
  const [branchesError, setBranchesError] = useState<string | null>(null);
  const [branchInput, setBranchInput] = useState('');
  const [branchSelected, setBranchSelected] = useState(false);
  const [branchCodeLookupError, setBranchCodeLookupError] = useState<string | null>(null);

  // 銀行コードが選択されたら支店一覧を取得
  useEffect(() => {
    setBranches([]);
    setBranchInput('');
    setBranchesError(null);

    if (!selectedBank?.code) return;

    const controller = new AbortController();
    const fetchBranches = async () => {
      try {
        setBranchesLoading(true);
        const url = `/api/bank-branches/${selectedBank.code}`;
        const res = await fetch(url, { signal: controller.signal, cache: 'no-store' });
        if (!res.ok) throw new Error(`Failed to fetch branches: ${res.status}`);
        const data: Branch[] = await res.json();
        setBranches(data ?? []);
        // 既存の支店コードがある場合は表示値を「コード 支店名」にする
        if (formData.branchCode) {
          const found = (data ?? []).find((b) => b.branch_code === formData.branchCode);
          if (found) {
            setBranchInput(`${found.branch_code} ${found.branch_name}`);
            setBranchSelected(true);
            setBranchCodeLookupError(null);
          } else {
            setBranchCodeLookupError('入力した支店コードが見つかりません');
          }
        }
      } catch {
        setBranchesError('支店一覧の取得に失敗しました。時間をおいて再度お試しください。');
      } finally {
        setBranchesLoading(false);
      }
    };
    fetchBranches();

    return () => controller.abort();
  }, [selectedBank?.code, formData.branchCode]);

  // 銀行が切り替わったら支店コードをリセット
  useEffect(() => {
    if (!selectedBank?.code) return;
    if (formData.branchCode) {
      onChange('branchCode', '');
    }
  }, [selectedBank?.code, formData.branchCode, onChange]);

  // 検索フィルタ（漢字 or カナ or コード）
  const filteredBranches = useMemo(() => {
    const q = branchInput.trim();
    if (!q) return [] as Branch[];
    const qKana = normalizeKana(q);
    return branches.filter((b) => {
      if (b.branch_code.includes(q)) return true;
      if (b.branch_name.includes(q)) return true;
      const nameKana = normalizeKana(b.branch_name_kana);
      return nameKana.includes(qKana);
    });
  }, [branchInput, branches]);

  // 入力変更ハンドラ（単一フィールド）
  const handleBranchFieldChange = (value: string) => {
    setBranchInput(value);
    setBranchSelected(false);

    const onlyDigits = value.replace(/\D/g, '');
    if (onlyDigits.length === 3) {
      // 3桁数値が入力されたら支店コード反映し、名前も付与（候補が取得済みの場合）
      onChange('branchCode', onlyDigits);
      if (branches.length > 0) {
        const found = branches.find((b) => b.branch_code === onlyDigits);
        if (found) {
          setBranchInput(`${found.branch_code} ${found.branch_name}`);
          setBranchSelected(true);
          setBranchCodeLookupError(null);
        } else {
          setBranchCodeLookupError('入力した支店コードが見つかりません');
          onChange('branchCode', '');
        }
      } else {
        setBranchCodeLookupError(null);
      }
    } else if (onlyDigits.length < 3) {
      // 不完全な数値や日本語入力中は支店コードを未確定にする
      if (formData.branchCode) onChange('branchCode', '');
      setBranchCodeLookupError(null);
    }
  };

  const showOverlay = selectedBank && !branchSelected && branchInput.trim().length > 0 && !/^\d{3}$/.test(branchInput.trim());

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">口座情報を入力してください</h3>

        {/* 選択された銀行の確認 */}
        {selectedBank && (
          <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                />
              </svg>
              <div>
                <span className="font-medium text-gray-900">{selectedBank.name}</span>
                <span className="text-sm text-gray-500 ml-2">({selectedBank.code})</span>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 支店検索・入力（統合フィールド） */}
          <div className="md:col-span-2 relative">
            <label htmlFor="branch-field" className="block text-sm font-medium text-gray-700 mb-1">
              支店（コードまたは名称で検索）
            </label>
            <div className="relative">
              <input
                id="branch-field"
                type="text"
                value={branchInput}
                onChange={(e) => handleBranchFieldChange(e.target.value)}
                placeholder={selectedBank ? '例：001 / 東京 / ﾄｳｷﾖｳ' : '銀行を選択すると検索できます'}
                disabled={!selectedBank || branchesLoading}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-3 py-2 pr-9"
              />
              {(branchSelected || branchInput) && (
                <button
                  type="button"
                  aria-label="選択をクリア"
                  onClick={() => {
                    setBranchInput('');
                    setBranchSelected(false);
                    if (formData.branchCode) onChange('branchCode', '');
                    setBranchCodeLookupError(null);
                  }}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              )}
            </div>
            {branchCodeLookupError && (
              <div className="mt-1">
                <p className="text-sm text-red-600">{branchCodeLookupError}</p>
              </div>
            )}
            {errors.branchCode && (
              <div className="-mt-1 mb-2">
                <p className="text-sm text-red-600">{errors.branchCode}</p>
              </div>
            )}

            {/* ロード/エラー表示 */}
            {selectedBank && branchesLoading && <p className="mt-2 text-xs text-gray-500">支店一覧を読み込み中です...</p>}
            {selectedBank && branchesError && <p className="mt-2 text-xs text-red-600">{branchesError}</p>}

            {/* 候補オーバーレイ */}
            {showOverlay && !branchesLoading && !branchesError && (
              <div className="absolute z-10 mt-1 w-full max-h-60 overflow-auto border border-gray-200 bg-white rounded-md shadow-lg divide-y">
                {filteredBranches.length === 0 && <div className="px-3 py-2 text-sm text-gray-500">該当する支店が見つかりませんでした</div>}
                {filteredBranches.slice(0, 50).map((b) => (
                  <button
                    key={b.branch_code}
                    type="button"
                    onClick={() => {
                      onChange('branchCode', b.branch_code);
                      setBranchInput(`${b.branch_code} ${b.branch_name}`);
                      setBranchSelected(true);
                      setBranchCodeLookupError(null);
                    }}
                    className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 ${
                      formData.branchCode === b.branch_code ? 'bg-blue-50' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-gray-900">{b.branch_name}</div>
                        <div className="text-xs text-gray-500">{b.branch_name_kana}</div>
                      </div>
                      <div className="text-xs text-gray-600">{b.branch_code}</div>
                    </div>
                  </button>
                ))}
                {filteredBranches.length > 50 && (
                  <div className="px-3 py-2 text-xs text-gray-500">他 {filteredBranches.length - 50} 件。検索語を絞り込んでください。</div>
                )}
              </div>
            )}
          </div>

          {/* 預金区分 */}
          <ControlledSelect
            id="accountType"
            name="accountType"
            label="預金区分"
            value={formData.accountType}
            onChange={(value) => onChange('accountType', parseInt(value))}
            options={ACCOUNT_TYPES}
            required
            className={errors.accountType ? 'mb-1' : ''}
          />
          {errors.accountType && (
            <div className="md:col-span-2 -mt-5 mb-4">
              <p className="text-sm text-red-600">{errors.accountType}</p>
            </div>
          )}
        </div>

        {/* 口座番号 */}
        <ControlledInput
          id="accountNumber"
          name="accountNumber"
          label="口座番号"
          type="text"
          value={formData.accountNumber}
          onChange={(value) => onChange('accountNumber', value)}
          placeholder="例：1234567"
          required
          maxLength={8}
          pattern="[0-9]{7,8}"
          inputMode="numeric"
          className={`max-w-md ${errors.accountNumber ? 'mb-1' : ''}`}
        />
        {errors.accountNumber && (
          <div className="mb-4">
            <p className="text-sm text-red-600">{errors.accountNumber}</p>
          </div>
        )}
        <p className="text-xs text-gray-500 mt-1">7桁または8桁の数字を入力してください</p>

        {/* 口座名義（カナ） */}
        <ControlledInput
          id="accountName"
          name="accountName"
          label="口座名義（カナ）"
          type="text"
          value={formData.accountName}
          onChange={(value) => onChange('accountName', value)}
          placeholder="例：ヤマダタロウ"
          required
          maxLength={60}
          className={errors.accountName ? 'mb-1' : ''}
        />
        {errors.accountName && (
          <div className="mb-4">
            <p className="text-sm text-red-600">{errors.accountName}</p>
          </div>
        )}
        <p className="text-xs text-gray-500 mt-1">カタカナで入力してください（60文字以内）</p>

        {/* 口座名義（漢字） */}
        <ControlledInput
          id="accountNameKanji"
          name="accountNameKanji"
          label="口座名義（漢字）"
          type="text"
          value={formData.accountNameKanji}
          onChange={(value) => onChange('accountNameKanji', value)}
          placeholder="例：山田太郎"
          required
          maxLength={60}
          className={errors.accountNameKanji ? 'mb-1' : ''}
        />
        {errors.accountNameKanji && (
          <div className="mb-4">
            <p className="text-sm text-red-600">{errors.accountNameKanji}</p>
          </div>
        )}
        <p className="text-xs text-gray-500 mt-1">漢字・ひらがな・カタカナで入力してください（60文字以内）</p>

        {/* 注意事項 */}
        <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-yellow-800">ご注意</h4>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>口座名義は通帳・キャッシュカードに記載されている名義と完全に一致させてください</li>
                  <li>法人口座はご利用いただけません</li>
                  <li>一部の金融機関では口座振替サービスをご利用いただけない場合があります</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
