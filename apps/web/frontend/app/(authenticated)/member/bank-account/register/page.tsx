'use client';

import { useState } from 'react';
import BankAccountForm from './components/BankAccountForm';
import BankAccountConfirm from './components/BankAccountConfirm';
import type { BankAccountFormData, FormStep, BankAccountRegistrationResult } from '@web/types/bank-account';
import { registerBankAccount } from '@web/api_clients/bank_account_client';

export default function BankAccountRegisterPage() {
  const [step, setStep] = useState<FormStep>('bank-selection');
  const [formData, setFormData] = useState<BankAccountFormData | null>(null);
  const [registrationResult, setRegistrationResult] = useState<BankAccountRegistrationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFormSubmit = async (data: BankAccountFormData) => {
    setFormData(data);
    setStep('confirm');
  };

  const handleConfirmSubmit = async () => {
    if (!formData) return;

    setLoading(true);
    setError(null);

    try {
      // APIリクエストデータを構築
      const requestData = {
        bankCode: formData.bankCode,
        branchCode: formData.branchCode,
        accountType: formData.accountType,
        accountNumber: formData.accountNumber,
        accountName: formData.accountName,
        accountNameKanji: formData.accountNameKanji,
        returnUrl: `${window.location.origin}/bank-account/redirect`, // コールバックURL
      };

      // API呼び出し
      const result = await registerBankAccount(requestData);

      setRegistrationResult({
        message: result.message,
        gmoTransactionId: result.gmoTransactionId,
        gmoToken: result.gmoToken,
        gmoStartUrl: result.gmoStartUrl,
        bankAccountRegistrationId: result.bankAccountRegistrationId,
      });

      setStep('complete');
    } catch (err) {
      console.error('口座登録申請に失敗しました:', err);
      setError('口座登録申請に失敗しました。もう一度お試しください。');
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (step === 'confirm') {
      setStep('bank-selection');
    }
  };

  const _handleReset = () => {
    setStep('bank-selection');
    setFormData(null);
    setRegistrationResult(null);
    setError(null);
  };

  const handleRedirectToGmo = () => {
    if (registrationResult?.gmoStartUrl && registrationResult?.gmoTransactionId && registrationResult?.gmoToken) {
      // 新しい遷移ページを使用してGMOに遷移
      const params = new URLSearchParams({
        tranId: registrationResult.gmoTransactionId,
        token: registrationResult.gmoToken,
        startUrl: registrationResult.gmoStartUrl,
      });

      // 遷移ページにリダイレクト
      window.location.href = `/bank-account/transition?${params.toString()}`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          {/* ヘッダー */}
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">銀行口座登録</h1>
            <p className="mt-1 text-sm text-gray-600">口座振替のための銀行口座情報を登録してください。</p>
          </div>

          {/* ステップインジケーター */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    step === 'bank-selection' || step === 'account-info'
                      ? 'border-blue-600 bg-blue-600 text-white'
                      : step === 'confirm' || step === 'complete'
                        ? 'border-green-600 bg-green-600 text-white'
                        : 'border-gray-300'
                  }`}
                >
                  1
                </div>
                <span className="ml-2 text-sm font-medium">入力</span>
              </div>
              <div className="flex-1 mx-4">
                <div className={`h-1 rounded ${step === 'confirm' || step === 'complete' ? 'bg-green-600' : 'bg-gray-300'}`}></div>
              </div>
              <div className="flex items-center">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    step === 'confirm'
                      ? 'border-blue-600 bg-blue-600 text-white'
                      : step === 'complete'
                        ? 'border-green-600 bg-green-600 text-white'
                        : 'border-gray-300'
                  }`}
                >
                  2
                </div>
                <span className="ml-2 text-sm font-medium">確認</span>
              </div>
              <div className="flex-1 mx-4">
                <div className={`h-1 rounded ${step === 'complete' ? 'bg-green-600' : 'bg-gray-300'}`}></div>
              </div>
              <div className="flex items-center">
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    step === 'complete' ? 'border-green-600 bg-green-600 text-white' : 'border-gray-300'
                  }`}
                >
                  3
                </div>
                <span className="ml-2 text-sm font-medium">完了</span>
              </div>
            </div>
          </div>

          {/* エラー表示 */}
          {error && (
            <div className="px-6 py-4 bg-red-50 border-l-4 border-red-400">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* コンテンツ */}
          <div className="px-6 py-6">
            {(step === 'bank-selection' || step === 'account-info') && (
              <BankAccountForm onSubmit={handleFormSubmit} initialData={formData} loading={loading} />
            )}

            {step === 'confirm' && formData && (
              <BankAccountConfirm data={formData} onSubmit={handleConfirmSubmit} onBack={handleBack} loading={loading} />
            )}

            {step === 'complete' && registrationResult && (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">口座登録申請が完了しました</h2>
                <p className="text-gray-600 mb-6">
                  {registrationResult.message}
                  <br />
                  続いて、金融機関での手続きを行ってください。
                </p>
                <div className="space-y-4">
                  <button
                    onClick={handleRedirectToGmo}
                    className="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    金融機関サイトで手続きを続ける
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
