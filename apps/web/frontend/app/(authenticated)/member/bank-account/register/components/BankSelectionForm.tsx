import React, { useState } from 'react';
import { MAJOR_BANKS, ONLINE_BANKS, getBankByCode } from '@web/constants/bank-codes';
import type { BankAccountFormErrors } from '@web/types/bank-account';

interface BankSelectionFormProps {
  bankCode: string;
  onBankCodeChange: (bankCode: string) => void;
  errors: BankAccountFormErrors;
}

export const BankSelectionForm: React.FC<BankSelectionFormProps> = ({ bankCode, onBankCodeChange, errors }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);

  // 検索フィルタリング
  const filteredMajorBanks = MAJOR_BANKS.filter(
    (bank) => bank.name.includes(searchTerm) || bank.kana.includes(searchTerm) || bank.code.includes(searchTerm)
  );

  const filteredOnlineBanks = ONLINE_BANKS.filter(
    (bank) => bank.name.includes(searchTerm) || bank.kana.includes(searchTerm) || bank.code.includes(searchTerm)
  );

  const selectedBank = getBankByCode(bankCode);

  const handleBankSelect = (code: string) => {
    onBankCodeChange(code);
    setShowCustomInput(false);
  };

  const handleCustomCodeChange = (code: string) => {
    onBankCodeChange(code);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">金融機関を選択してください</h3>

        {/* 検索ボックス */}
        <div className="mb-4">
          <label htmlFor="bank-search" className="block text-sm font-medium text-gray-700 mb-1">
            金融機関名で検索
          </label>
          <input
            id="bank-search"
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="例：みずほ、三菱UFJ、0001"
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-3 py-2"
          />
        </div>

        {/* 選択された銀行の表示 */}
        {selectedBank && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-sm font-medium text-blue-900">
                選択中: {selectedBank.name} ({selectedBank.code})
              </span>
            </div>
          </div>
        )}

        {/* エラー表示 */}
        {errors.bankCode && <div className="mb-4 text-sm text-red-600">{errors.bankCode}</div>}

        {/* メジャー銀行リスト */}
        {filteredMajorBanks.length > 0 && (
          <div className="mb-6">
            <h4 className="text-md font-medium text-gray-800 mb-3">主要銀行</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
              {filteredMajorBanks.map((bank) => (
                <button
                  key={bank.code}
                  onClick={() => handleBankSelect(bank.code)}
                  className={`text-left p-3 rounded-md border transition-colors ${
                    bankCode === bank.code
                      ? 'border-blue-500 bg-blue-50 text-blue-900'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="font-medium text-sm">{bank.name}</div>
                  <div className="text-xs text-gray-500">{bank.code}</div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* ネット銀行リスト */}
        {filteredOnlineBanks.length > 0 && (
          <div className="mb-6">
            <h4 className="text-md font-medium text-gray-800 mb-3">ネット銀行・その他</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
              {filteredOnlineBanks.map((bank) => (
                <button
                  key={bank.code}
                  onClick={() => handleBankSelect(bank.code)}
                  className={`text-left p-3 rounded-md border transition-colors ${
                    bankCode === bank.code
                      ? 'border-blue-500 bg-blue-50 text-blue-900'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="font-medium text-sm">{bank.name}</div>
                  <div className="text-xs text-gray-500">{bank.code}</div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* カスタム入力オプション */}
        <div className="border-t pt-4">
          <button type="button" onClick={() => setShowCustomInput(!showCustomInput)} className="text-sm text-blue-600 hover:text-blue-800">
            {showCustomInput ? '一覧から選択' : '金融機関コードを直接入力'}
          </button>

          {showCustomInput && (
            <div className="mt-3">
              <label htmlFor="custom-bank-code" className="block text-sm font-medium text-gray-700 mb-1">
                金融機関コード（4桁）
              </label>
              <input
                id="custom-bank-code"
                type="text"
                value={bankCode}
                onChange={(e) => handleCustomCodeChange(e.target.value)}
                placeholder="例：0001"
                maxLength={4}
                pattern="[0-9]{4}"
                className="block w-full max-w-xs rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-3 py-2"
              />
              <p className="mt-1 text-xs text-gray-500">4桁の金融機関コードを入力してください</p>
            </div>
          )}
        </div>

        {/* 検索結果なしの場合 */}
        {searchTerm && filteredMajorBanks.length === 0 && filteredOnlineBanks.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>「{searchTerm}」に一致する金融機関が見つかりませんでした。</p>
            <p className="text-sm mt-1">金融機関コードを直接入力してください。</p>
          </div>
        )}
      </div>
    </div>
  );
};
