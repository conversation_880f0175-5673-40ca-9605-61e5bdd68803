import React, { useEffect } from 'react';
import type { BankAccountFormData } from '@web/types/bank-account';
import { getBankByCode, ACCOUNT_TYPES } from '@web/constants/bank-codes';

interface BankAccountConfirmProps {
  data: BankAccountFormData;
  onSubmit: () => void;
  onBack: () => void;
  loading?: boolean;
}

const BankAccountConfirm: React.FC<BankAccountConfirmProps> = ({ data, onSubmit, onBack, loading = false }) => {
  useEffect(() => {
    // 確認画面表示時にトップへ即時スクロール（SSR安全）
    if (typeof window !== 'undefined') {
      window.scrollTo(0, 0);
    }
  }, []);
  const selectedBank = getBankByCode(data.bankCode);
  const accountTypeLabel = ACCOUNT_TYPES.find((type) => type.value === data.accountType)?.label || '';

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">入力内容をご確認ください</h3>
        <p className="text-sm text-gray-600 mb-6">以下の内容で口座登録申請を行います。内容に間違いがないかご確認ください。</p>

        {/* 確認内容 */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            {/* 金融機関 */}
            <div>
              <dt className="text-sm font-medium text-gray-500">金融機関</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {selectedBank ? (
                  <div>
                    <div className="font-medium">{selectedBank.name}</div>
                    <div className="text-gray-500">コード: {selectedBank.code}</div>
                  </div>
                ) : (
                  <div>
                    <div className="font-medium">金融機関コード: {data.bankCode}</div>
                  </div>
                )}
              </dd>
            </div>

            {/* 支店コード */}
            <div>
              <dt className="text-sm font-medium text-gray-500">支店コード</dt>
              <dd className="mt-1 text-sm text-gray-900">{data.branchCode}</dd>
            </div>

            {/* 預金区分 */}
            <div>
              <dt className="text-sm font-medium text-gray-500">預金区分</dt>
              <dd className="mt-1 text-sm text-gray-900">{accountTypeLabel}</dd>
            </div>

            {/* 口座番号 */}
            <div>
              <dt className="text-sm font-medium text-gray-500">口座番号</dt>
              <dd className="mt-1 text-sm text-gray-900">{data.accountNumber}</dd>
            </div>

            {/* 口座名義（カナ） */}
            <div>
              <dt className="text-sm font-medium text-gray-500">口座名義（カナ）</dt>
              <dd className="mt-1 text-sm text-gray-900">{data.accountName}</dd>
            </div>

            {/* 口座名義（漢字） */}
            <div>
              <dt className="text-sm font-medium text-gray-500">口座名義（漢字）</dt>
              <dd className="mt-1 text-sm text-gray-900">{data.accountNameKanji}</dd>
            </div>
          </dl>
        </div>

        {/* 重要な注意事項 */}
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-yellow-800">重要なお知らせ</h4>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>申請後、金融機関のサイトで口座振替の手続きを行っていただきます</li>
                  <li>手続きが完了するまで、口座振替はご利用いただけません</li>
                  <li>一度申請した内容は変更できませんので、内容をよくご確認ください</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* 同意チェックボックス */}
        <div className="mt-6">
          <label className="flex items-start">
            <input type="checkbox" required className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
            <span className="ml-2 text-sm text-gray-700">
              上記の内容で口座登録申請を行うことに同意します。また、
              <a href="/terms" target="_blank" className="text-blue-600 hover:text-blue-800 underline">
                利用規約
              </a>
              および
              <a href="/privacy" target="_blank" className="text-blue-600 hover:text-blue-800 underline">
                プライバシーポリシー
              </a>
              に同意します。
            </span>
          </label>
        </div>
      </div>

      {/* ボタン */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onBack}
          disabled={loading}
          className="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50"
        >
          戻る
        </button>

        <button
          type="button"
          onClick={onSubmit}
          disabled={loading}
          className="bg-blue-600 text-white px-8 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
        >
          {loading ? (
            <div className="flex items-center">
              <svg
                className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              申請中...
            </div>
          ) : (
            '申請する'
          )}
        </button>
      </div>
    </div>
  );
};

export default BankAccountConfirm;
