import React, { useState, useEffect } from 'react';
import { BankSelectionForm } from './BankSelectionForm';
import { AccountInfoForm } from './AccountInfoForm';
import type { BankAccountFormData, BankAccountFormErrors } from '@web/types/bank-account';
import { validateBankAccountForm, hasErrors } from '@web/utils/bank-account-validation';

interface BankAccountFormProps {
  onSubmit: (data: BankAccountFormData) => void;
  initialData?: BankAccountFormData | null;
  loading?: boolean;
}

const BankAccountForm: React.FC<BankAccountFormProps> = ({ onSubmit, initialData, loading = false }) => {
  const [currentStep, setCurrentStep] = useState<'bank-selection' | 'account-info'>('bank-selection');
  const [formData, setFormData] = useState<BankAccountFormData>({
    bankCode: '',
    branchCode: '',
    accountType: 1,
    accountNumber: '',
    accountName: '',
    accountNameKanji: '',
  });
  const [errors, setErrors] = useState<BankAccountFormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // 初期データがある場合は設定
  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
      if (initialData.bankCode) {
        setCurrentStep('account-info');
      }
    }
  }, [initialData]);

  const handleFieldChange = (field: keyof BankAccountFormData, value: string | number) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // フィールドがタッチされたことを記録
    setTouched((prev) => ({
      ...prev,
      [field]: true,
    }));

    // リアルタイムバリデーション（タッチされたフィールドのみ）
    if (touched[field]) {
      const newFormData = { ...formData, [field]: value };
      const newErrors = validateBankAccountForm(newFormData);
      setErrors(newErrors);
    }
  };

  const handleNextStep = () => {
    if (currentStep === 'bank-selection') {
      // 銀行選択のバリデーション
      const bankErrors = validateBankAccountForm({ ...formData });
      if (bankErrors.bankCode) {
        setErrors({ bankCode: bankErrors.bankCode });
        setTouched((prev) => ({ ...prev, bankCode: true }));
        return;
      }
      setCurrentStep('account-info');
    }
  };

  const handlePrevStep = () => {
    if (currentStep === 'account-info') {
      setCurrentStep('bank-selection');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // 全フィールドのバリデーション
    const validationErrors = validateBankAccountForm(formData);
    setErrors(validationErrors);

    // 全フィールドをタッチ済みにする
    const allFields = Object.keys(formData) as (keyof BankAccountFormData)[];
    const allTouched: Record<string, boolean> = {};
    allFields.forEach((field) => {
      allTouched[field] = true;
    });
    setTouched(allTouched);

    if (!hasErrors(validationErrors)) {
      onSubmit(formData);
    }
  };

  const canProceedToAccountInfo = formData.bankCode && !errors.bankCode;
  const canSubmit =
    !hasErrors(errors) &&
    formData.bankCode &&
    formData.branchCode &&
    formData.accountNumber &&
    formData.accountName &&
    formData.accountNameKanji;

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* ステップ内インジケーター */}
      <div className="flex items-center justify-center space-x-4 mb-8">
        <div className="flex items-center">
          <div
            className={`flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm ${
              currentStep === 'bank-selection'
                ? 'border-blue-600 bg-blue-600 text-white'
                : canProceedToAccountInfo
                  ? 'border-green-600 bg-green-600 text-white'
                  : 'border-gray-300'
            }`}
          >
            1
          </div>
          <span className="ml-2 text-sm font-medium">金融機関選択</span>
        </div>
        <div className="flex-1 max-w-20">
          <div className={`h-1 rounded ${currentStep === 'account-info' ? 'bg-blue-600' : 'bg-gray-300'}`}></div>
        </div>
        <div className="flex items-center">
          <div
            className={`flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm ${
              currentStep === 'account-info' ? 'border-blue-600 bg-blue-600 text-white' : 'border-gray-300'
            }`}
          >
            2
          </div>
          <span className="ml-2 text-sm font-medium">口座情報入力</span>
        </div>
      </div>

      {/* フォームコンテンツ */}
      {currentStep === 'bank-selection' && (
        <BankSelectionForm
          bankCode={formData.bankCode}
          onBankCodeChange={(value) => handleFieldChange('bankCode', value)}
          errors={errors}
        />
      )}

      {currentStep === 'account-info' && <AccountInfoForm formData={formData} onChange={handleFieldChange} errors={errors} />}

      {/* ナビゲーションボタン */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <div>
          {currentStep === 'account-info' && (
            <button
              type="button"
              onClick={handlePrevStep}
              className="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              disabled={loading}
            >
              戻る
            </button>
          )}
        </div>

        <div>
          {currentStep === 'bank-selection' && (
            <button
              type="button"
              onClick={handleNextStep}
              disabled={!canProceedToAccountInfo || loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              次へ
            </button>
          )}

          {currentStep === 'account-info' && (
            <button
              type="submit"
              disabled={!canSubmit || loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? '処理中...' : '確認画面へ'}
            </button>
          )}
        </div>
      </div>
    </form>
  );
};

export default BankAccountForm;
