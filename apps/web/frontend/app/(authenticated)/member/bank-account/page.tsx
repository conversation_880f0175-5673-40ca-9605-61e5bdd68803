'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { create } from '@bufbuild/protobuf';
import { getBankAccountInfo } from '@web/api_clients/bank_account_client';
import { GetBankAccountInfoResponseSchema, BankAccountRegistrationStatus } from '@hami/web-api-schema/bank_account_service_pb';
import type { GetBankAccountInfoResponse } from '@hami/web-api-schema/bank_account_service_pb';
import { BankAccountInfo } from './components/BankAccountInfo';
import { AccountActions } from './components/AccountActions';

export default function BankAccountPage() {
  const [accountInfo, setAccountInfo] = useState<GetBankAccountInfoResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAccountInfo = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getBankAccountInfo({});
        setAccountInfo(response);
      } catch (err) {
        console.error('口座情報の取得に失敗しました:', err);
        setError('口座情報の取得に失敗しました');
        // エラーが発生した場合は、口座が登録されていない状態として扱う
        setAccountInfo(
          create(GetBankAccountInfoResponseSchema, {
            hasActiveAccount: false,
            bankAccountRegistrationId: 0,
            registrationStatus: BankAccountRegistrationStatus.BANK_ACCOUNT_REGISTRATION_STATUS_UNKNOWN,
            resultBankCode: '',
            resultBranchCode: '',
            resultAccountType: 0,
            resultAccountNumber: '',
            resultAccountName: '',
            completedAt: '',
          })
        );
      } finally {
        setLoading(false);
      }
    };

    fetchAccountInfo();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* ヘッダー */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">銀行口座情報</h1>
          <p className="mt-2 text-gray-600">登録済みの銀行口座情報を確認・管理できます</p>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">エラーが発生しました</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 口座情報表示 */}
        {accountInfo?.hasActiveAccount ? (
          <div className="space-y-6">
            <BankAccountInfo accountInfo={accountInfo} />
            <AccountActions />
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">口座が登録されていません</h3>
              <p className="mt-1 text-sm text-gray-500">投資を開始するには、銀行口座の登録が必要です。</p>
              <div className="mt-6">
                <Link
                  href="/bank-account/register"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  口座を登録する
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* ナビゲーションリンク */}
        <div className="mt-8 flex justify-center space-x-4">
          <Link
            href="/bank-account/history"
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            登録履歴を見る
          </Link>
          {!accountInfo?.hasActiveAccount && (
            <Link
              href="/bank-account/register"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              新しい口座を登録
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}
