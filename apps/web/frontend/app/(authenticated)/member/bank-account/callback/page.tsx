'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { handleGmoCallback, getRegistrationStatus } from '@web/api_clients/bank_account_client';

function CallbackContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const processCallback = async () => {
      try {
        // URLパラメータからコールバックデータを取得
        const callbackData = {
          transactionId: searchParams.get('TransactionID') || '',
          siteId: searchParams.get('SiteID') || '',
          memberId: searchParams.get('MemberID') || '',
          result: searchParams.get('Result') || '',
          bankCode: searchParams.get('BankCode') || '',
          branchCode: searchParams.get('BranchCode') || '',
          accountType: searchParams.get('AccountType') || '',
          accountNumber: searchParams.get('AccountNumber') || '',
          accountName: searchParams.get('AccountName') || '',
        };

        if (!callbackData.transactionId) {
          throw new Error('トランザクションIDが見つかりません');
        }

        // バックエンドAPIにコールバックデータを送信
        await handleGmoCallback(callbackData);

        // 登録状況を確認
        const statusResult = await getRegistrationStatus({
          gmoTransactionId: callbackData.transactionId,
        });

        if (statusResult.registrationStatus === 4) {
          // SUCCESS
          setStatus('success');
          setMessage('口座登録が正常に完了しました。');
        } else if (statusResult.registrationStatus === 5) {
          // FAIL
          setStatus('error');
          setMessage('口座登録に失敗しました。入力内容をご確認の上、再度お試しください。');
        } else {
          setStatus('processing');
          setMessage('口座登録の処理中です。しばらくお待ちください。');
        }
      } catch (error) {
        console.error('コールバック処理エラー:', error);
        setStatus('error');
        setMessage('処理中にエラーが発生しました。');
      }
    };

    processCallback();
  }, [searchParams]);

  const handleReturnToForm = () => {
    router.push('/bank-account/register');
  };

  const handleGoToMemberPage = () => {
    router.push('/member');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-8">
            {status === 'processing' && (
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-blue-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">処理中</h2>
                <p className="text-gray-600 mb-6">{message}</p>
                <div className="text-sm text-gray-500">このページを閉じずにお待ちください...</div>
              </div>
            )}

            {status === 'success' && (
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">登録完了</h2>
                <p className="text-gray-600 mb-6">{message}</p>
                <div className="space-y-3">
                  <button
                    onClick={handleGoToMemberPage}
                    className="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    マイページに戻る
                  </button>
                  <button
                    onClick={handleReturnToForm}
                    className="w-full bg-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  >
                    新しい口座を登録
                  </button>
                </div>
              </div>
            )}

            {status === 'error' && (
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">エラー</h2>
                <p className="text-gray-600 mb-6">{message}</p>
                <div className="space-y-3">
                  <button
                    onClick={handleReturnToForm}
                    className="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    再度登録する
                  </button>
                  <button
                    onClick={handleGoToMemberPage}
                    className="w-full bg-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  >
                    マイページに戻る
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function BankAccountCallbackPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-8 text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-blue-600 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-2">読み込み中</h2>
                <p className="text-gray-600">しばらくお待ちください...</p>
              </div>
            </div>
          </div>
        </div>
      }
    >
      <CallbackContent />
    </Suspense>
  );
}
