'use client';

import { ReactNode } from 'react';

interface BankAccountCardProps {
  children: ReactNode;
  className?: string;
}

export function BankAccountCard({ children, className = '' }: BankAccountCardProps) {
  return (
    <div className={`bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 shadow-lg ${className}`}>
      <div className="bg-white rounded-lg p-6">{children}</div>
    </div>
  );
}
