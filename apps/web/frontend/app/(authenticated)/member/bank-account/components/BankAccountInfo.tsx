'use client';

import type { GetBankAccountInfoResponse } from '@hami/web-api-schema/bank_account_service_pb';
import { BankAccountRegistrationStatus } from '@hami/web-api-schema/bank_account_service_pb';
import { BankAccountCard } from './BankAccountCard';

interface BankAccountInfoProps {
  accountInfo: GetBankAccountInfoResponse;
}

export function BankAccountInfo({ accountInfo }: BankAccountInfoProps) {
  const getStatusText = (status: BankAccountRegistrationStatus) => {
    switch (status) {
      case BankAccountRegistrationStatus.ENTRY:
        return { text: '申請完了', color: 'blue' };
      case BankAccountRegistrationStatus.START:
        return { text: '金融機関画面遷移', color: 'yellow' };
      case BankAccountRegistrationStatus.TERM:
        return { text: '結果確認中', color: 'yellow' };
      case BankAccountRegistrationStatus.SUCCESS:
        return { text: '登録完了', color: 'green' };
      case BankAccountRegistrationStatus.FAIL:
        return { text: '登録失敗', color: 'red' };
      case BankAccountRegistrationStatus.UNPROCESSED:
        return { text: '処理失敗', color: 'red' };
      default:
        return { text: '状態不明', color: 'gray' };
    }
  };

  // 実際に登録が完了しているかチェック
  const isActuallyCompleted =
    accountInfo.registrationStatus === BankAccountRegistrationStatus.SUCCESS &&
    accountInfo.resultBankCode &&
    accountInfo.resultAccountNumber &&
    accountInfo.completedAt;

  const getAccountTypeText = (type: number) => {
    switch (type) {
      case 1:
        return '普通預金';
      case 2:
        return '当座預金';
      default:
        return '不明';
    }
  };

  const status = getStatusText(accountInfo.registrationStatus);

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">登録済み口座情報</h2>
      </div>

      <div className="p-6">
        <BankAccountCard>
          {/* ステータス表示 */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">メイン口座</h3>
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                status.color === 'green'
                  ? 'bg-green-100 text-green-800'
                  : status.color === 'blue'
                    ? 'bg-blue-100 text-blue-800'
                    : status.color === 'yellow'
                      ? 'bg-yellow-100 text-yellow-800'
                      : status.color === 'red'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-gray-100 text-gray-800'
              }`}
            >
              {status.text}
            </span>
          </div>

          {/* 口座情報 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <dt className="text-sm font-medium text-gray-500">金融機関コード</dt>
              <dd className="mt-1 text-sm text-gray-900">{accountInfo.resultBankCode || '-'}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">支店コード</dt>
              <dd className="mt-1 text-sm text-gray-900">{accountInfo.resultBranchCode || '-'}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">預金区分</dt>
              <dd className="mt-1 text-sm text-gray-900">{getAccountTypeText(accountInfo.resultAccountType)}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">口座番号</dt>
              <dd className="mt-1 text-sm text-gray-900 font-mono">{accountInfo.resultAccountNumber || '-'}</dd>
            </div>
            <div className="md:col-span-2">
              <dt className="text-sm font-medium text-gray-500">口座名義</dt>
              <dd className="mt-1 text-sm text-gray-900">{accountInfo.resultAccountName || '-'}</dd>
            </div>
            {accountInfo.completedAt && (
              <div className="md:col-span-2">
                <dt className="text-sm font-medium text-gray-500">登録完了日時</dt>
                <dd className="mt-1 text-sm text-gray-900">{new Date(accountInfo.completedAt).toLocaleString('ja-JP')}</dd>
              </div>
            )}
          </div>

          {/* 状態に応じた注意事項 */}
          {isActuallyCompleted ? (
            <div className="mt-6 p-4 bg-green-50 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">口座登録完了</h3>
                  <div className="mt-2 text-sm text-green-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>口座登録が正常に完了しています</li>
                      <li>投資の引き落としはこの口座から行われます</li>
                      <li>口座情報の変更は新しい口座の登録が必要です</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="mt-6 p-4 bg-yellow-50 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">口座登録処理中</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <ul className="list-disc list-inside space-y-1">
                      <li>口座登録の処理が進行中です</li>
                      <li>完了まで数営業日かかる場合があります</li>
                      <li>詳細は登録履歴ページでご確認ください</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </BankAccountCard>
      </div>
    </div>
  );
}
