'use client';

import Link from 'next/link';

export function AccountActions() {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">管理メニュー</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* 新しい口座登録 */}
        <Link
          href="/bank-account/register"
          className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
          </div>
          <div className="ml-4">
            <h4 className="text-sm font-medium text-gray-900">新しい口座を登録</h4>
            <p className="text-sm text-gray-500">別の銀行口座を追加登録</p>
          </div>
        </Link>

        {/* 登録履歴 */}
        <Link
          href="/bank-account/history"
          className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="ml-4">
            <h4 className="text-sm font-medium text-gray-900">登録履歴</h4>
            <p className="text-sm text-gray-500">過去の登録試行を確認</p>
          </div>
        </Link>

        {/* サポート */}
        <a
          href="mailto:<EMAIL>"
          className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div className="ml-4">
            <h4 className="text-sm font-medium text-gray-900">サポートに連絡</h4>
            <p className="text-sm text-gray-500">口座登録でお困りの場合</p>
          </div>
        </a>
      </div>

      {/* 注意事項 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-md">
        <h4 className="text-sm font-medium text-gray-900 mb-2">重要なお知らせ</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• 口座情報の変更には新しい口座の登録が必要です</li>
          <li>• 登録完了まで数営業日かかる場合があります</li>
          <li>• 投資の引き落としは最新の有効な口座から行われます</li>
        </ul>
      </div>
    </div>
  );
}
