'use client';

import type { BankAccountRegistrationItem } from '@hami/web-api-schema/bank_account_service_pb';
import { BankAccountRegistrationStatus } from '@hami/web-api-schema/bank_account_service_pb';

interface RegistrationHistoryProps {
  registrations: BankAccountRegistrationItem[];
}

export function RegistrationHistory({ registrations }: RegistrationHistoryProps) {
  const getStatusText = (status: BankAccountRegistrationStatus) => {
    switch (status) {
      case BankAccountRegistrationStatus.ENTRY:
        return { text: '申請完了', color: 'blue', bgColor: 'bg-blue-100', textColor: 'text-blue-800' };
      case BankAccountRegistrationStatus.START:
        return { text: '金融機関画面遷移', color: 'yellow', bgColor: 'bg-yellow-100', textColor: 'text-yellow-800' };
      case BankAccountRegistrationStatus.TERM:
        return { text: '結果確認中', color: 'yellow', bgColor: 'bg-yellow-100', textColor: 'text-yellow-800' };
      case BankAccountRegistrationStatus.SUCCESS:
        return { text: '登録完了', color: 'green', bgColor: 'bg-green-100', textColor: 'text-green-800' };
      case BankAccountRegistrationStatus.FAIL:
        return { text: '登録失敗', color: 'red', bgColor: 'bg-red-100', textColor: 'text-red-800' };
      case BankAccountRegistrationStatus.UNPROCESSED:
        return { text: '処理失敗', color: 'red', bgColor: 'bg-red-100', textColor: 'text-red-800' };
      default:
        return { text: '不明', color: 'gray', bgColor: 'bg-gray-100', textColor: 'text-gray-800' };
    }
  };

  const getAccountTypeText = (type: number) => {
    switch (type) {
      case 1:
        return '普通預金';
      case 2:
        return '当座預金';
      default:
        return '不明';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString('ja-JP');
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">登録履歴一覧</h2>
        <p className="mt-1 text-sm text-gray-500">{registrations.length}件の登録履歴があります</p>
      </div>

      <div className="divide-y divide-gray-200">
        {registrations.map((registration) => {
          const status = getStatusText(registration.registrationStatus);

          return (
            <div key={registration.bankAccountRegistrationId} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <h3 className="text-lg font-medium text-gray-900">登録ID: {registration.bankAccountRegistrationId}</h3>
                  {registration.isActive && (
                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      アクティブ
                    </span>
                  )}
                </div>
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${status.bgColor} ${status.textColor}`}
                >
                  {status.text}
                </span>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* 申請時の情報 */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-900">申請時の情報</h4>
                  <div className="text-sm text-gray-600">
                    <div>金融機関コード: {registration.bankCode || '-'}</div>
                    <div>支店コード: {registration.branchCode || '-'}</div>
                    <div>預金区分: {getAccountTypeText(registration.accountType)}</div>
                    <div>口座番号: {registration.accountNumber || '-'}</div>
                    <div>口座名義: {registration.accountName || '-'}</div>
                  </div>
                </div>

                {/* 結果情報 */}
                {(registration.resultBankCode || registration.resultAccountNumber) && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900">登録結果</h4>
                    <div className="text-sm text-gray-600">
                      <div>金融機関コード: {registration.resultBankCode || '-'}</div>
                      <div>支店コード: {registration.resultBranchCode || '-'}</div>
                      <div>預金区分: {getAccountTypeText(registration.resultAccountType)}</div>
                      <div>口座番号: {registration.resultAccountNumber || '-'}</div>
                      <div>口座名義: {registration.resultAccountName || '-'}</div>
                    </div>
                  </div>
                )}

                {/* 日時情報 */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-900">日時情報</h4>
                  <div className="text-sm text-gray-600">
                    <div>申請日時: {formatDate(registration.createdAt)}</div>
                    <div>更新日時: {formatDate(registration.updatedAt)}</div>
                    {registration.completedAt && <div>完了日時: {formatDate(registration.completedAt)}</div>}
                  </div>
                </div>
              </div>

              {/* エラー情報 */}
              {(registration.errorCode || registration.errorMessage) && (
                <div className="mt-4 p-4 bg-red-50 rounded-md">
                  <h4 className="text-sm font-medium text-red-800 mb-2">エラー詳細</h4>
                  <div className="text-sm text-red-700">
                    {registration.errorCode && <div>エラーコード: {registration.errorCode}</div>}
                    {registration.errorDetail && <div>エラー詳細: {registration.errorDetail}</div>}
                    {registration.errorMessage && <div>エラーメッセージ: {registration.errorMessage}</div>}
                  </div>
                </div>
              )}

              {/* GMO情報 */}
              {registration.gmoTransactionId && (
                <div className="mt-4 p-4 bg-gray-50 rounded-md">
                  <h4 className="text-sm font-medium text-gray-800 mb-2">GMO取引情報</h4>
                  <div className="text-sm text-gray-600">
                    <div>トランザクションID: {registration.gmoTransactionId}</div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
