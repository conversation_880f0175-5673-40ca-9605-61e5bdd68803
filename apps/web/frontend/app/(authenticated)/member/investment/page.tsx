'use client';

import { useEffect, useState } from 'react';
import { listMemberClaimAndPays, requestStatementPdfDownloadUrl } from '@web/api_clients/member_claim_and_pay_client';
import type { MemberClaimAndPay } from '@hami/web-api-schema/member_claim_and_pay_service_pb';

export default function InvestmentPage() {
  const [claimAndPays, setClaimAndPays] = useState<MemberClaimAndPay[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClaimAndPays = async () => {
      try {
        setLoading(true);
        const response = await listMemberClaimAndPays({});
        setClaimAndPays(response.memberClaimAndPays);
        setError(null);
      } catch (err) {
        console.error('請求・支払いデータの取得に失敗しました:', err);
        setError('データの取得に失敗しました。しばらく時間をおいて再度お試しください。');
      } finally {
        setLoading(false);
      }
    };

    fetchClaimAndPays();
  }, []);

  const formatDate = (year: number, month: number, day: number) => {
    return `${year}年${month}月${day}日`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ja-JP').format(amount);
  };

  const handleDownloadStatement = async (fileKey: string) => {
    try {
      // RequestStatementPdfDownloadUrlを使用してダウンロードURLを取得
      const response = await requestStatementPdfDownloadUrl({
        statementFileKey: fileKey,
      });

      // ダウンロードURLに遷移してPDFダウンロードを実行
      if (response.downloadUrl) {
        window.open(response.downloadUrl, '_blank');
      } else {
        throw new Error('ダウンロードURLが取得できませんでした');
      }
    } catch (error) {
      console.error('明細書ダウンロードに失敗しました:', error);
      alert('明細書のダウンロードに失敗しました。しばらく時間をおいて再度お試しください。');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">投資・請求・支払い</h1>
            <p className="mt-2 text-gray-600">投資に関する請求・支払いの履歴を確認できます。</p>
          </div>
          <div className="bg-white rounded-lg shadow-md p-8">
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">読み込み中...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">投資・請求・支払い</h1>
            <p className="mt-2 text-gray-600">投資に関する請求・支払いの履歴を確認できます。</p>
          </div>
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              <p className="text-red-800 font-medium">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* ヘッダー */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">投資・請求・支払い</h1>
          <p className="mt-2 text-gray-600">投資に関する請求・支払いの履歴を確認できます。</p>
        </div>

        {/* メインコンテンツ */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {claimAndPays.length === 0 ? (
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">明細書が発行された請求・支払い履歴がありません</h3>
              <p className="text-gray-600">現在、明細書が発行された請求・支払いの履歴はありません。</p>
            </div>
          ) : (
            <>
              {/* テーブルヘッダー */}
              <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">請求・支払い履歴（明細書発行済み）</h2>
                <p className="text-sm text-gray-600 mt-1">発生日の降順で表示されています</p>
              </div>

              {/* テーブル */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left py-4 px-6 text-sm font-medium text-gray-900">発生日</th>
                      <th className="text-left py-4 px-6 text-sm font-medium text-gray-900">請求金額</th>
                      <th className="text-left py-4 px-6 text-sm font-medium text-gray-900">支払い金額</th>
                      <th className="text-left py-4 px-6 text-sm font-medium text-gray-900">差額</th>
                      <th className="text-left py-4 px-6 text-sm font-medium text-gray-900">明細書</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {claimAndPays.map((item) => {
                      const difference = item.payAmount - item.claimAmount;
                      const isOverpaid = difference > 0;
                      const isUnderpaid = difference < 0;

                      return (
                        <tr key={item.memberClaimAndPayId} className="hover:bg-gray-50">
                          <td className="py-4 px-6 text-sm text-gray-900">
                            {formatDate(item.occurredYear, item.occurredMonth, item.occurredDay)}
                          </td>
                          <td className="py-4 px-6 text-sm font-medium text-gray-900">¥{formatCurrency(item.claimAmount)}</td>
                          <td className="py-4 px-6 text-sm font-medium text-gray-900">¥{formatCurrency(item.payAmount)}</td>
                          <td className="py-4 px-6 text-sm font-medium">
                            <span className={isOverpaid ? 'text-green-600' : isUnderpaid ? 'text-red-600' : 'text-gray-900'}>
                              {isOverpaid ? '+' : ''}¥{formatCurrency(Math.abs(difference))}
                            </span>
                          </td>
                          <td className="py-4 px-6 text-sm">
                            {/* 明細書列はすべてPDFダウンロードボタン */}
                            <button
                              onClick={() => item.statementFileKey && handleDownloadStatement(item.statementFileKey)}
                              disabled={!item.statementFileKey}
                              className="inline-flex items-center px-3 py-1 border border-blue-600 text-blue-600 text-sm font-medium rounded-md hover:bg-blue-600 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                              </svg>
                              PDFダウンロード
                            </button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {/* サマリー */}
              <div className="bg-green-50 px-6 py-4 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-600">明細書発行済み件数</p>
                    <p className="text-lg font-semibold text-gray-900">{claimAndPays.length}件</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">最終更新</p>
                    <p className="text-sm text-gray-600">
                      {claimAndPays.length > 0
                        ? formatDate(claimAndPays[0].occurredYear, claimAndPays[0].occurredMonth, claimAndPays[0].occurredDay)
                        : '-'}
                    </p>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* 補足情報 */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">ご利用上の注意</h3>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p>明細書が発行された請求・支払いの履歴のみ表示されています。</p>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p>請求・支払いの履歴は発生日の降順で表示されています。</p>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p>差額が正の値（+）の場合は支払い超過、負の値（-）の場合は支払い不足を示します。</p>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p>PDFダウンロードボタンをクリックすると、明細書が新しいタブで開きます。</p>
            </div>
            <div className="flex items-start">
              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <p>ご不明な点がございましたら、お気軽にお問い合わせください。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
