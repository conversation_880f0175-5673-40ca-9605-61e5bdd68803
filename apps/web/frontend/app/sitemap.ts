import type { MetadataRoute } from 'next';
import { getFrontendEndpoint } from '@web/lib/config';
import { listAnnouncements } from '@web/api_clients/announcement_client';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const endpoint = getFrontendEndpoint();
  const base = endpoint ? endpoint.toString().replace(/\/$/, '') : '';

  const staticPaths: string[] = [
    '/',
    '/announcements',
    '/news',
    '/contact',
    '/club',
    '/club-guide',
    '/company',
    '/privacy_policy',
    '/terms',
    '/solicitation_policy',
    '/race_results',
    '/race_schedules',
    '/horses',
    '/offered_horses',
    '/membership_fees_and_requirements',
    '/club_point_system',
    '/conflict_of_interest',
    '/copyright',
    '/faq',
    '/special/2025',
    '/join',
  ];

  const staticEntries: MetadataRoute.Sitemap = staticPaths.map((p) => ({
    url: `${base}${p}`,
  }));

  // お知らせ詳細（最新100件）
  let announcementEntries: MetadataRoute.Sitemap = [];
  try {
    const res = await listAnnouncements({
      page: 1,
      pageSize: 100,
      sortBy: 'published_at',
      sortOrder: 'desc',
    });

    announcementEntries = (res.announcements ?? []).map((a) => {
      const lastModified: Date | undefined = (a.publishedAt as unknown as { toDate?: () => Date } | undefined)?.toDate?.();
      return {
        url: `${base}/announcements/${a.announcementId}`,
        ...(lastModified ? { lastModified } : {}),
      };
    });
  } catch {
    // API失敗時は静的部分のみ返す（ステージングや開発で未設定の場合など）
    announcementEntries = [];
  }

  return [...staticEntries, ...announcementEntries];
}
