import { NextRequest, NextResponse } from 'next/server';

export async function GET(_req: NextRequest, context: { params: { bankCode: string } }) {
  const { bankCode } = context.params;
  if (!/^\d{4}$/.test(bankCode)) {
    return NextResponse.json({ error: 'Invalid bank code' }, { status: 400 });
  }

  const upstreamUrl = `https://static.mul-pay.jp/assets/direct/bank/${bankCode}`;
  try {
    const res = await fetch(upstreamUrl, {
      headers: { Accept: 'application/json' },
      cache: 'no-store',
    });

    if (!res.ok) {
      return NextResponse.json({ error: `Upstream error: ${res.status}` }, { status: 502 });
    }

    const data = await res.json();
    return NextResponse.json(data, {
      status: 200,
    });
  } catch {
    return NextResponse.json({ error: 'Failed to fetch branches' }, { status: 502 });
  }
}


