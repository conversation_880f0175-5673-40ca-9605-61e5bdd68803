import type { MetadataRoute } from 'next';
import { getFrontendEndpoint } from '@web/lib/config';

export default function robots(): MetadataRoute.Robots {
  const basicAuthEnabled =
    process.env.BASIC_AUTH_ENABLED !== 'false' && <PERSON><PERSON><PERSON>(process.env.BASIC_AUTH_USERNAME) && Bo<PERSON>an(process.env.BASIC_AUTH_PASSWORD);

  // ステージング/開発では noindex（Basic認証が有効な場合も含む）
  if (basicAuthEnabled || process.env.NODE_ENV !== 'production') {
    return {
      rules: [
        {
          userAgent: '*',
          disallow: '/',
        },
      ],
    };
  }

  // 本番は index を許可し、sitemap の場所を明示
  const site = getFrontendEndpoint();
  const baseUrl = site ? site.toString().replace(/\/$/, '') : undefined;

  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
      },
    ],
    ...(baseUrl ? { sitemap: `${baseUrl}/sitemap.xml` } : {}),
  };
}
