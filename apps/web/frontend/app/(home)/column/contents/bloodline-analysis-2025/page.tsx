import Image from 'next/image';
import Link from 'next/link';

export const metadata = {
  title: '血統博士の2025年注目募集馬の血統・配合分析 ストロベリームーンの24 | Blooming Horse Club',
  description: '血統の専門家が2025年の注目募集馬、ストロベリームーンの24について詳細な血統・配合分析を行います。',
};

export default function BloodlineAnalysis2025Page() {
  return (
    <main className="min-h-screen bg-white">
      {/* パンくず */}
      <nav aria-label="パンくずナビゲーション" className="w-full max-w-[1600px] mx-auto px-5 py-5">
        <ol className="flex items-center gap-0.5 text-base">
          <li className="shrink-0">
            <Link href="/" className="text-hami-glyph-modest font-normal hover:text-hami-glyph-base">
              ホーム
            </Link>
          </li>
          <li aria-hidden="true" className="shrink-0">
            <span className="text-hami-glyph-modest font-normal">/</span>
          </li>
          <li className="shrink-0">
            <Link href="/column" className="text-hami-glyph-modest font-normal hover:text-hami-glyph-base">
              コラム
            </Link>
          </li>
          <li aria-hidden="true" className="shrink-0">
            <span className="text-hami-glyph-modest font-normal">/</span>
          </li>
          <li aria-current="page" className="min-w-0">
            <span className="text-hami-glyph-base font-normal truncate block">血統博士の2025年注目募集馬の血統・配合分析 ストロベリームーンの24</span>
          </li>
        </ol>
      </nav>

      <div className="w-full p-4 flex justify-center">
        <Image
          src="https://bhc-prd-public-files.s3.ap-northeast-1.amazonaws.com/column/%E8%A1%80%E7%B5%B1%E5%88%86%E6%9E%90_%E3%82%B9%E3%83%88%E3%83%AD%E3%83%98%E3%82%99%E3%83%AA%E3%83%BC%E3%83%A0%E3%83%BC%E3%83%B324_%E6%9B%B4%E6%96%B0%E7%89%88.webp"
          alt="血統配合分析 - ストロベリームーン24"
          width={1200}
          height={1600}
          className="max-w-xl w-full h-auto"
          priority
        />
      </div>

      {/* 募集馬詳細へのリンク */}
      <div className="w-full flex justify-center">
        <div className="max-w-xl w-full">
          <div className="bg-hami-primary text-hami-glyph-base p-6 rounded-lg text-center">
            <h3 className="text-lg font-semibold mb-3">ストロベリームーンの24</h3>
            <p className="text-sm mb-4 opacity-90">
              ストロベリームーンの24の詳細、馬体写真、募集動画をご覧いただけます。
            </p>
            <Link
              href="https://bloominghorseclub.co.jp/offered_horses/2025/4"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-6 py-3 bg-[#0e311e] text-hami-glyph-reverse rounded-lg font-medium hover:bg-gray-400 transition-colors duration-200"
            >
              募集馬詳細を見る
              <svg
                className="ml-2 w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}
