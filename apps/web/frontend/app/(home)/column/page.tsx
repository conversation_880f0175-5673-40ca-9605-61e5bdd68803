import Link from 'next/link';
import Image from 'next/image';

export const metadata = {
  title: 'コラム | Blooming Horse Club',
  description: '競馬に関するコラムや最新情報をお届けします',
};

interface ColumnItem {
  id: string | number;
  title: string;
  excerpt?: string;
  date: string;
  image: string;
  href?: string;
}

export default function ColumnPage() {
  // サンプルコラム記事データ
  const columns: ColumnItem[] = [
    {
      id: 'bloodline-analysis-2025-2',
      title: '血統博士の2025年注目募集馬の血統・配合分析 第2弾',
      date: '2025/10/05',
      image: 'https://bhc-prd-public-files.s3.ap-northeast-1.amazonaws.com/top/carousel/doctor_analytics_2.webp',
      href: '/column/contents/bloodline-analysis-2025-2'
    },
    {
      id: 'bloodline-analysis-2025',
      title: '血統博士の2025年注目募集馬の血統・配合分析 第1弾',
      date: '2025/10/04',
      image: 'https://bhc-prd-public-files.s3.ap-northeast-1.amazonaws.com/top/carousel/doctor_analytics_1.webp',
      href: '/column/contents/bloodline-analysis-2025'
    }
  ];


  return (
    <div className="flex flex-col items-center min-h-screen bg-hami-bg-main">
      {/* パンくず */}
      <nav aria-label="パンくずナビゲーション" className="w-full max-w-[1600px] px-5 py-5">
        <ol className="flex items-center gap-0.5 text-base">
          <li>
            <Link href="/" className="text-hami-glyph-modest font-normal hover:text-hami-glyph-base">
              ホーム
            </Link>
          </li>
          <li aria-hidden="true">
            <span className="text-hami-glyph-modest font-normal">/</span>
          </li>
          <li aria-current="page">
            <span className="text-hami-glyph-base font-normal">コラム</span>
          </li>
        </ol>
      </nav>

      {/* メインコンテンツ */}
      <div className="flex flex-col items-center w-full max-w-[1200px] px-5 pb-10">
        {/* タイトル */}
        <div className="w-full px-5 py-5">
          <h1
            className="text-hami-glyph-base text-[28px] lg:text-[32px] md:text-[28px] sm:text-[24px] font-normal leading-[1.4] tracking-[1.12px]"
            style={{ fontFamily: 'Noto Sans JP, sans-serif' }}
          >
            コラム
          </h1>
        </div>

        {/* コラム記事一覧 */}
        <div className="w-full sm:px-5">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
            {columns.map((column) => (
              <article
                key={column.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200"
              >
                {/* 記事画像 */}
                <div className="aspect-[16/9] bg-gray-200 relative">
                  {column.image ? (
                    <Image
                      src={column.image}
                      alt={column.title}
                      fill
                      className="object-contain"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                      <span className="text-sm">画像準備中</span>
                    </div>
                  )}
                </div>

                {/* 記事内容 */}
                <div className="p-6">
                  {/* カテゴリと日付 */}
                  <div className="flex items-center justify-between mb-3">
                    <time className="text-hami-glyph-modest text-sm">
                      {new Date(column.date).toLocaleDateString('ja-JP')}
                    </time>
                  </div>

                  {/* タイトル */}
                  <h2 className="text-hami-glyph-base text-lg font-medium mb-3 leading-tight">
                    <Link
                      href={column.href || `/column/${column.id}`}
                      className="hover:text-hami-primary transition-colors duration-200"
                    >
                      {column.title}
                    </Link>
                  </h2>

                  {/* 抜粋 */}
                  {column.excerpt && (
                    <p className="text-hami-glyph-modest text-sm leading-relaxed mb-4">
                      {column.excerpt}
                    </p>
                  )}

                  {/* 続きを読むリンク */}
                  <Link
                    href={column.href || `/column/${column.id}`}
                    className="inline-flex items-center text-hami-primary text-sm font-medium hover:underline"
                  >
                    続きを読む
                    <svg
                      className="ml-1 w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </Link>
                </div>
              </article>
            ))}
          </div>

          {/* ページネーション（将来的に実装） */}
          <div className="flex justify-center mt-12">
            <div className="flex items-center space-x-2">
              <button
                disabled
                className="px-4 py-2 text-sm font-medium text-gray-400 bg-white border border-gray-300 rounded-md cursor-not-allowed"
              >
                前へ
              </button>
              <span className="px-4 py-2 text-sm font-medium text-white bg-hami-primary rounded-md">
                1
              </span>
              <button
                disabled
                className="px-4 py-2 text-sm font-medium text-gray-400 bg-white border border-gray-300 rounded-md cursor-not-allowed"
              >
                次へ
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
