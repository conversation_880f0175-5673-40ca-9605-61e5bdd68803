import { Gender } from '@hami/web-api-schema/common_enums_pb';

interface HorseGenderProps {
  gender: Gender;
}

const getGenderLabel = (gender: Gender) => {
  switch (gender) {
    case Gender.STALLION:
      return '牡';
    case Gender.MARE:
      return '牝';
    case Gender.GELDING:
      return 'セ';
    default:
      return '';
  }
}

const getGenderColor = (gender: Gender) => {
  switch (gender) {
    case Gender.STALLION:
      return 'bg-[#345AE2] text-hami-glyph-reverse';
    case Gender.MARE:
      return 'bg-hami-coral-red text-hami-glyph-reverse';
    case Gender.GELDING:
      return 'bg-[#6B7280] text-hami-glyph-reverse';
    default:
      return '';
  }
}

export const genderTextColor = (gender?: Gender) => {
  switch (gender) {
    case Gender.STALLION:
      return 'text-[#345AE2]';
    case Gender.MARE:
      return 'text-hami-coral-red';
    case Gender.GELDING:
      return 'text-[#6B7280]';
    default:
      return '';
  }
};

export function HorseGender({ gender }: HorseGenderProps) {
  return <div>
    <div className={`rounded-xs w-5 h-5 flex items-center justify-center ${getGenderColor(gender)}`}>
      <span className="text-sm font-normal leading-none">{getGenderLabel(gender)}</span>
    </div>
  </div>;
}