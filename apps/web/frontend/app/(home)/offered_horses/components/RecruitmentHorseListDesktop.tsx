'use client';

import type { RecruitmentHorseItem } from '@hami/web-api-schema/recruitment_horse_service_pb';
import type { AnnualBundle } from '@hami/web-api-schema/annual_bundle_service_pb';
import type { GetMeResponse } from '@hami/web-api-schema/user_service_pb';
import { RecruitmentHorseListDesktopRow } from './RecruitmentHorseListDesktopRow';
import { AnnualBundleListDesktopRow } from './AnnualBundleListDesktopRow';

interface Props {
  horses: RecruitmentHorseItem[];
  bundles?: AnnualBundle[] | null;
  userInfo: GetMeResponse | null;
}

export function RecruitmentHorseListDesktop({ horses, bundles, userInfo }: Props) {
  return (
    <div className="overflow-x-auto bg-white border border-[color:var(--color-hami-stroke-border)]">
      <table className="min-w-full table-fixed">
        <thead>
          <tr className="text-left text-sm text-hami-glyph-reverse border-b bg-hami-primary-dark border-[color:var(--color-hami-stroke-separator)]">
            <th className="px-3 py-3 w-14">No.</th>
            <th className="px-3 py-3 w-28">募集状況</th>
            <th className="px-3 py-3">募集馬名</th>
            <th className="px-3 py-3 w-32 text-center">総額</th>
            <th className="px-3 py-3 w-28 text-end">1口</th>
            <th className="px-3 py-3 w-28 text-end">募集口数</th>
            <th className="px-3 py-3 w-32 text-center">動画</th>
            {userInfo && <th className="px-2 py-3 w-[98px] text-center">申込口数</th>}
          </tr>
        </thead>
        <tbody className="text-sm text-[color:var(--color-hami-text-primary)]">
          {bundles?.map((b) => <AnnualBundleListDesktopRow key={b.annualBundleId} bundle={b} userInfo={userInfo} />)}
          {horses.map((horse) => (
            <RecruitmentHorseListDesktopRow key={horse.horseId} horse={horse} userInfo={userInfo} />
          ))}
        </tbody>
      </table>
    </div>
  );
}
