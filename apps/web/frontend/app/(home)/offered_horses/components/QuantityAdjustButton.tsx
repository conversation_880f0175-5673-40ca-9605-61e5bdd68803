'use client';

import type { ButtonHTMLAttributes } from 'react';
import IconPlus from '@web/icons/icon_plus.svg';
import IconMinus from '@web/icons/icon_minus.svg';

type QuantityAdjustButtonProps = {
  variant: 'increment' | 'decrement';
} & Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'type'>;


export function QuantityAdjustButton({ variant, disabled, ...rest }: QuantityAdjustButtonProps) {
  return (
    <button
      type="button"
      className='flex items-center justify-center text-hami-glyph-base w-8 h-8 leading-none disabled:opacity-40 disabled:cursor-not-allowed'
      disabled={disabled}
      {...rest}
    >
      {variant === 'increment' ? (
        <IconPlus className="w-4 h-4" aria-hidden />
      ) : (
        <IconMinus className="w-4 h-4" aria-hidden />
      )}
    </button>
  );
}
