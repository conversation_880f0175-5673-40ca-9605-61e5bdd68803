'use client';

import { useMemo } from 'react';
import { QuantityAdjustButton } from './QuantityAdjustButton';

type QuantityControlProps = {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  className?: string;
};

export function QuantityControl({ value, onChange, min = 0, max, className = '' }: QuantityControlProps) {
  const isDecrementDisabled = useMemo(() => value <= min, [value, min]);
  const isIncrementDisabled = useMemo(() => (max === undefined ? false : value >= max), [value, max]);

  const handleDecrement = () => {
    if (isDecrementDisabled) {
      return;
    }

    onChange(value - 1);
  };

  const handleIncrement = () => {
    if (isIncrementDisabled) {
      return;
    }

    onChange(value + 1);
  };

  return (
    <div
      className={`flex items-center h-8 rounded-full bg-hami-bg-main px-1 py-2 w-full sm:w-auto ${className}`.trim()}
    >
      <QuantityAdjustButton
        variant="decrement"
        aria-label="希望口数を減らす"
        disabled={isDecrementDisabled}
        onClick={handleDecrement}
      />
      <span className="min-w-[28px] text-center text-sm font-medium text-[color:var(--color-hami-text-primary)]">{value}</span>
      <QuantityAdjustButton
        variant="increment"
        aria-label="希望口数を増やす"
        disabled={isIncrementDisabled}
        onClick={handleIncrement}
      />
    </div>
  );
}
