'use client';

export interface DownloadButtonProps {
  /** ダウンロードファイルのURL */
  downloadUrl?: string | null;
  /** ボタンに表示するテキスト */
  label: string;
  /** ファイル名（ダウンロード時に使用） */
  fileName?: string;
  /** ボタンのサイズ */
  size?: 'small' | 'medium' | 'large';
  /** カスタムクラス名 */
  className?: string;
  /** アイコンを表示するか */
  showIcon?: boolean;
  /** 無効時でもボタンを表示するか */
  showWhenDisabled?: boolean;
}

export default function DownloadButton({
  downloadUrl,
  label,
  fileName,
  size = 'medium',
  className = '',
  showIcon = true,
  showWhenDisabled = true,
}: DownloadButtonProps) {
  const hasFile = Boolean(downloadUrl);

  // サイズに応じたスタイル
  const sizeStyles = {
    small: {
      button: 'px-3 py-1.5 text-sm',
      icon: 'w-4 h-4',
    },
    medium: {
      button: 'px-4 py-2 text-md',
      icon: 'w-5 h-5',
    },
    large: {
      button: 'px-6 py-3 text-xl',
      icon: 'w-6 h-6',
    },
  };

  const currentSize = sizeStyles[size];

  // 基本スタイル
  const baseClasses = `
    flex items-center justify-center
    bg-hami-control-dl-link
    text-hami-glyph-base
    border border-hami-border-default
    rounded-[2px]
    font-medium
    transition-colors duration-200
    whitespace-nowrap
    ${currentSize.button}
    ${showIcon ? 'gap-2' : ''}
  `.trim().replace(/\s+/g, ' ');

  // 有効/無効状態のスタイル
  const stateClasses = hasFile
    ? 'hover:bg-opacity-80 cursor-pointer'
    : 'opacity-50 cursor-not-allowed';

  const buttonClasses = `${baseClasses} ${stateClasses} ${className}`;

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!hasFile || !downloadUrl) return;

    // ダウンロードリンクを作成
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';

    if (fileName) {
      link.download = fileName;
    }

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // ファイルがない場合で、無効時非表示の場合は何も表示しない
  if (!hasFile && !showWhenDisabled) {
    return null;
  }

  const content = (
    <>
      <span>{label}</span>
    </>
  );

  if (hasFile) {
    return (
      <button
        type="button"
        onClick={handleClick}
        className={buttonClasses}
        aria-label={`${label}をダウンロード`}
      >
        {content}
      </button>
    );
  }

  // 無効状態
  return (
    <div
      className={buttonClasses}
      aria-label={`${label}は利用できません`}
    >
      {content}
    </div>
  );
}
