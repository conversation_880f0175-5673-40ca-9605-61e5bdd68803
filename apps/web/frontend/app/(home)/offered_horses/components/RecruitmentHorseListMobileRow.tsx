'use client';

import Link from 'next/link';
import VideoButton from './VideoButton';

import type { RecruitmentHorseItem } from '@hami/web-api-schema/recruitment_horse_service_pb';
import { Label } from './Label';
import { HorseGender } from './horse/HorseGender';
import { formatCoatColor, getAffiliationColor } from '@web/utils/horseFormatters';

interface RecruitmentHorseListMobileRowProps {
  horse: RecruitmentHorseItem;
}

const getStatusBadgeColor = (statusLabel: string) => {
  switch (statusLabel) {
    case '募集中':
      return 'bg-[#96DFB3] text-[color:var(--color-hami-text-primary)]';
    case '募集前':
      return 'bg-gray-200 text-[color:var(--color-hami-text-secondary)]';
    case '満口':
      return 'bg-red-200 text-red-800';
    case '募集終了':
      return 'bg-gray-300 text-gray-700';
    default:
      return 'bg-gray-200 text-[color:var(--color-hami-text-secondary)]';
  }
};

export function RecruitmentHorseListMobileRow({ horse }: RecruitmentHorseListMobileRowProps) {
  const sharePrice = horse.amountTotal / horse.sharesTotal / 10000;
  const totalAmountInManYen = horse.amountTotal / 10000;

  // 一覧API拡張により props から直接判定・取得
  const videoId = horse.latestYoutubeId;
  const startAt = horse.latestStartAtSeconds ?? undefined;

  const formatNumber = (n: number) => new Intl.NumberFormat('ja-JP').format(n);

  return (
    <Link href={`/offered_horses/${horse.recruitmentYear}/${horse.recruitmentNo}`}>
      <div className="self-stretch bg-white border-b border-[color:var(--color-hami-stroke-border)]">
        <div className="px-3 py-3 border-b border-[color:var(--color-hami-stroke-border)] flex flex-col gap-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <div className="rounded-full border border-[color:var(--color-hami-stroke-border)] px-2 h-6 inline-flex items-center gap-1">
                <span className="text-sm text-hami-glyph-subtle leading-snug">No.</span>
                <span className="text-sm text-hami-glyph-base leading-snug">{horse.recruitmentNo.toString().padStart(3, '0')}</span>
              </div>
              {horse.specialFlag && (
                <div className="bg-hami-bush-700 text-white text-xs px-2 py-1 rounded-full font-medium whitespace-nowrap">
                  キャンペーン募集馬
                </div>
              )}
            </div>
            <div className={`h-5 px-2 rounded-sm inline-flex items-center ${getStatusBadgeColor(horse.statusLabel)}`}>
              <span className="text-xs leading-none">{horse.statusLabel}</span>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            {/* 馬名と性別 */}
            <div className="flex items-center gap-2">
              <div className="text-[color:var(--color-hami-text-primary)] text-lg leading-relaxed font-noto-serif-jp">
                {horse.horseName || horse.recruitmentName}
              </div>
              <HorseGender gender={horse.gender} />
            </div>

            <div className="flex flex-col gap-1">
              {/* 父・母父名（1列で収まる時は1列、収まらない時は2段表示・左揃え） */}
              <div className="flex flex-wrap gap-1">
                {horse.sireName && (
                  <div className="text-sm font-normal text-hami-glyph-subtle bg-hami-bg-gray rounded px-1 h-4 flex items-center justify-start leading-none">
                    父 {horse.sireName}
                  </div>
                )}
                {horse.broodmareSireName && (
                  <div className="text-sm font-normal text-hami-glyph-subtle bg-hami-bg-gray rounded px-1 h-4 flex items-center justify-start leading-none">
                    母父 {horse.broodmareSireName}
                  </div>
                )}
              </div>

              <div className="flex items-start gap-1">
                <Label label={formatCoatColor(horse.coatColor)} />
                <Label label={horse.birthDateFormatted} />
                {horse.affiliation && horse.stableName && (
                  <Label>
                    <>
                      <span style={{ color: getAffiliationColor(horse.affiliation) }}>{horse.affiliation}</span>
                      <div className="w-1 h-1" />
                      <span>{horse.stableName}</span>
                    </>
                  </Label>
                )}
              </div>
            </div>
          </div>

          <VideoButton
            youtubeVideoId={videoId}
            startAtSeconds={startAt}
            size="small"
            showWhenDisabled={false}
            showIcon={false}
          />

          {/* 募集情報（順序変更：募集総額、一口価格、募集口数） */}
          <div className="grid grid-cols-3">
            <div className="px-2 flex flex-col items-center gap-1">
              <div className="flex items-end">
                <span className="text-base text-[color:var(--color-hami-text-primary)]">{formatNumber(totalAmountInManYen)}</span>
                <span className="text-sm text-hami-glyph-subtle ml-1">万円</span>
              </div>
              <div className="text-xs text-hami-glyph-subtle leading-none">募集総額</div>
            </div>
            <div className="px-2 border-x border-[color:var(--color-hami-stroke-separator)] flex flex-col items-center gap-1">
              <div className="flex items-end">
                <span className="text-base text-[color:var(--color-hami-text-primary)]">{sharePrice}</span>
                <span className="text-sm text-hami-glyph-subtle ml-1">万円</span>
              </div>
              <div className="text-xs text-hami-glyph-subtle leading-none">一口出資額</div>
            </div>
            <div className="px-2 flex flex-col items-center gap-1">
              <div className="flex items-end">
                <span className="text-base text-[color:var(--color-hami-text-primary)]">{formatNumber(horse.sharesTotal)}</span>
                <span className="text-sm text-hami-glyph-subtle ml-1">口</span>
              </div>
              <div className="text-xs text-hami-glyph-subtle leading-none">募集口数</div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
