'use client';

import { useState } from 'react';
import IconPlay from '@web/icons/icon_play.svg';
import VideoModal from './VideoModal';

export interface VideoButtonProps {
  /** YouTube動画ID */
  youtubeVideoId?: string | null;
  /** 動画の開始時間（秒） */
  startAtSeconds?: number | null;
  /** ボタンのサイズ */
  size?: 'small' | 'medium' | 'large';
  /** カスタムクラス名 */
  className?: string;
  /** 動画が利用できない場合でもボタンを表示するか */
  showWhenDisabled?: boolean;
  /** ボタンの幅を100%にするか */
  fullWidth?: boolean;
  /** アイコンを表示するか */
  showIcon?: boolean;
}

export default function VideoButton({
  youtubeVideoId,
  startAtSeconds,
  size = 'medium',
  className = '',
  showWhenDisabled = true,
  fullWidth = false,
  showIcon = true,
}: VideoButtonProps) {
  const [isVideoOpen, setIsVideoOpen] = useState(false);

  const hasVideo = Boolean(youtubeVideoId);

  // サイズに応じたスタイル
  const sizeStyles = {
    small: {
      button: 'h-6 px-4 text-xs',
      icon: 'w-3 h-3',
      gap: 'gap-1',
    },
    medium: {
      button: 'h-8 px-4 text-sm',
      icon: 'w-4 h-4',
      gap: 'gap-2',
    },
    large: {
      button: 'h-10 px-6 text-base',
      icon: 'w-5 h-5',
      gap: 'gap-2',
    },
  };

  const currentSize = sizeStyles[size];

  // 基本スタイル
  const baseClasses = `
    whitespace-nowrap
    bg-[color:var(--color-hami-bg-control)]
    border border-[color:var(--color-hami-stroke-border)]
    rounded
    flex items-center justify-center
    text-[color:var(--color-hami-text-primary)]
    leading-none
    transition-colors duration-150
    ${currentSize.button}
    ${showIcon ? currentSize.gap : ''}
    ${fullWidth ? 'w-full' : ''}
  `.trim().replace(/\s+/g, ' ');

  // 有効/無効状態のスタイル
  const stateClasses = hasVideo
    ? 'hover:bg-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[color:var(--color-hami-primary-dark)] cursor-pointer'
    : 'opacity-50 cursor-not-allowed';

  const buttonClasses = `${baseClasses} ${stateClasses} ${className}`;

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (hasVideo) {
      setIsVideoOpen(true);
    }
  };

  // 動画がない場合で、無効時非表示の場合は何も表示しない
  if (!hasVideo && !showWhenDisabled) {
    return null;
  }

  return (
    <>
      <button
        type="button"
        onClick={handleClick}
        disabled={!hasVideo}
        className={buttonClasses}
        aria-label={hasVideo ? '動画を再生' : '動画は利用できません'}
      >
        <IconPlay className={`${currentSize.icon} fill-[color:var(--color-hami-text-primary)]`} />
        <span>動画を再生</span>
      </button>

      {/* VideoModal */}
      {isVideoOpen && hasVideo && youtubeVideoId && (
        <VideoModal
          youtubeVideoId={youtubeVideoId}
          startAtSeconds={startAtSeconds}
          onClose={() => setIsVideoOpen(false)}
        />
      )}
    </>
  );
}
