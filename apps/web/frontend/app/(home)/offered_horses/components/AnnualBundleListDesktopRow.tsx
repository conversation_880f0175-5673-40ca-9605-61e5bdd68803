'use client';

import type { AnnualBundle } from '@hami/web-api-schema/annual_bundle_service_pb';
import { AnnualBundleRecruitmentStatus } from '@hami/web-api-schema/annual_bundle_service_pb';
import { QuantityControl } from './QuantityControl';
import Link from 'next/link';
import { GetMeResponse } from '@hami/web-api-schema/user_service_pb';
import { useInvestmentCart } from '@web/contexts/InvestmentCartContext';

interface Props {
  bundle: AnnualBundle;
  userInfo: GetMeResponse | null;
}

const fmt = (n: number) => new Intl.NumberFormat('ja-JP').format(n);

export function AnnualBundleListDesktopRow({ bundle, userInfo }: Props) {
  const perShareManYen = (bundle.perShareAmount || 0) / 10000;
  const totalManYen = ((bundle.perShareAmount || 0) * (bundle.shares || 0)) / 10000;
  const { upsertBundle, findBundleItem } = useInvestmentCart();
  const cartItem = findBundleItem(bundle.annualBundleId, bundle.fiscalYear);
  const requestedShares = cartItem?.requestedNumber ?? 0;

  const handleRequestedSharesChange = (value: number) => {
    upsertBundle({
      annualBundleId: bundle.annualBundleId,
      fiscalYear: bundle.fiscalYear,
      name: bundle.name,
      requestedNumber: value,
      shares: bundle.shares,
      perShareAmountManYen: perShareManYen,
      horses: (bundle.horses ?? []).map((horse) => ({
        recruitmentYear: bundle.fiscalYear,
        recruitmentNo: horse.recruitmentNo,
        horseName: horse.horseName,
        recruitmentName: horse.recruitmentName,
      })),
    });
  };

  const getStatusLabel = () => {
    switch (bundle.recruitmentStatus) {
      case AnnualBundleRecruitmentStatus.AB_ACTIVE:
        return '募集中';
      case AnnualBundleRecruitmentStatus.AB_UPCOMING:
        return '募集前';
      case AnnualBundleRecruitmentStatus.AB_FULL:
        return '満口';
      case AnnualBundleRecruitmentStatus.AB_CLOSED:
        return '募集終了';
      default:
        return '';
    }
  };

  const getStatusBadgeColor = (statusLabel: string) => {
    switch (statusLabel) {
      case '募集中':
        return 'bg-hami-bush-700 text-[color:var(--color-hami-glyph-reverse)]';
      case '満口':
        return 'bg-red-200 text-red-800';
      case '募集終了':
        return 'bg-gray-300 text-gray-700';
      default:
        return 'bg-gray-200 text-[color:var(--color-hami-text-secondary)]';
    }
  };

  return (
    <tr className="border-b border-[color:var(--color-hami-stroke-separator)] bg-hami-bg-gray/40">
      <td className="px-3 py-3 align-middle w-14 text-center text-sm text-[color:var(--color-hami-text-primary)]"></td>
      <td className="px-3 py-3 align-middle w-28">
        <div className={`w-20 h-5 rounded-sm inline-flex justify-center items-center ${getStatusBadgeColor(getStatusLabel())}`}>
          <div className="text-xs leading-none">{getStatusLabel()}</div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle">
        <div className="flex flex-col gap-1">
          <div className="text-base leading-normal tracking-wide font-noto-serif-jp">
            <Link href={`/offered_horses/annual_bundles/${bundle.fiscalYear}/${bundle.annualBundleId}`} className="text-hami-primary-dark hover:underline">
              {bundle.name}
            </Link>
          </div>
          <div>
            <div className="text-[10px] text-hami-glyph-subtle">募集馬19頭全頭に、一口ずつ出資の応援パッケージです。</div>
            <div className="text-[10px] text-hami-glyph-subtle">優先当選権がついているので、個別出資よりも優先抽選の対象となります。</div>
            <div className="text-[10px] text-hami-glyph-subtle">100口限定で、一次募集と二次募集にて各50口ずつ募集させていただきます。募集口数オーバーの際は抽選します。</div>
            <div className="text-[10px] text-hami-glyph-subtle">リアルダビスタ企画の2頭もパッケージに含まれます。</div>
          </div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle w-32 text-end">
        <div className="inline-flex items-end justify-center">
          <div className="text-[color:var(--color-hami-text-primary)] text-base tracking-wide">{fmt(totalManYen)}</div>
          <div className="text-hami-glyph-subtle text-sm leading-snug tracking-tight ml-1">万円</div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle w-28 text-right">
        <div className="inline-flex items-end justify-end">
          <div className="text-[color:var(--color-hami-text-primary)] text-base tracking-wide">{fmt(perShareManYen)}</div>
          <div className="text-hami-glyph-subtle text-sm leading-snug tracking-tight ml-1">万円</div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle w-28 text-right">
        <div className="inline-flex items-end justify-end">
          <div className="text-[color:var(--color-hami-text-primary)] text-base tracking-wide">{fmt(bundle.shares)}</div>
          <div className="text-hami-glyph-subtle text-sm leading-snug tracking-tight ml-1">口</div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle w-32 text-center"></td>
      {userInfo && (
        <td className="px-2 w-[98px] py-3 align-middle">
          <QuantityControl value={requestedShares} onChange={handleRequestedSharesChange} min={0} />
        </td>
      )}
    </tr>
  );
}
