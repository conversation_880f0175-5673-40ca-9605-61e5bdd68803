'use client';

import { useState } from 'react';
import { RecruitmentStatusFilter } from './RecruitmentStatusFilter';
import { RecruitmentHorsesList } from './RecruitmentHorsesList';
import type { RecruitmentHorseItem } from '@hami/web-api-schema/recruitment_horse_service_pb';
import type { AnnualBundle } from '@hami/web-api-schema/annual_bundle_service_pb';
import type { GetMeResponse } from '@hami/web-api-schema/user_service_pb';

interface Props {
  horses: RecruitmentHorseItem[];
  bundles?: AnnualBundle[] | null;
  userInfo: GetMeResponse | null;
}

export function OfferedHorsesClient({ horses, bundles = null, userInfo }: Props) {
  const [statusFilter, setStatusFilter] = useState('');
  const [viewMode, _setViewMode] = useState<'card' | 'list'>('list');

  return (
    <>
      {/* 検索・フィルター */}
      <div className="mb-6 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3">
        <RecruitmentStatusFilter onFilterChange={setStatusFilter} currentFilter={statusFilter} />
      </div>

      {/* 馬一覧 */}
      <RecruitmentHorsesList
        statusFilter={statusFilter}
        viewMode={viewMode}
        horses={horses}
        bundles={bundles ?? undefined}
        userInfo={userInfo}
      />
    </>
  );
}

