'use client';

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface VideoModalProps {
  youtubeVideoId: string;
  startAtSeconds?: number | null;
  onClose: () => void;
}

export default function VideoModal({ youtubeVideoId, startAtSeconds, onClose }: VideoModalProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const prevOverflow = document.body.style.overflow;
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = prevOverflow;
      setMounted(false);
    };
  }, []);

  if (!mounted) return null;

  const startParam = startAtSeconds ? `start=${startAtSeconds}&` : '';
  const src = `https://www.youtube.com/embed/${youtubeVideoId}?${startParam}enablejsapi=1&origin=${typeof window !== 'undefined' ? window.location.origin : ''}`;

  const stop = (e: React.SyntheticEvent) => {
    // タッチイベントでのpreventDefaultはパッシブリスナーの問題を避けるため条件付きで実行
    if (e.type !== 'touchstart') {
      e.preventDefault();
    }
    e.stopPropagation();
  };

  return createPortal(
    <div
      className="fixed inset-0 z-[1000] flex items-center justify-center bg-black/70"
      onClick={(e) => {
        // バックグラウンドクリック時のみ閉じる
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
      role="dialog"
      aria-modal="true"
    >
      <div
        className="relative w-[95vw] md:w-[90vw] max-w-4xl aspect-video bg-black rounded-lg overflow-hidden shadow-2xl"
        onClick={stop}
      >
        <button
          type="button"
          aria-label="閉じる"
          onClick={(e) => {
            stop(e);
            onClose();
          }}
          className="absolute -top-12 md:-top-10 right-0 text-white bg-black/60 hover:bg-black/80 rounded px-3 py-2 md:py-1 text-sm md:text-base z-10"
        >
          閉じる
        </button>
        <iframe
          className="w-full h-full"
          src={src}
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
          loading="lazy"
          tabIndex={0}
          aria-label="YouTube動画プレーヤー"
        />
      </div>
    </div>,
    document.body
  );
}
