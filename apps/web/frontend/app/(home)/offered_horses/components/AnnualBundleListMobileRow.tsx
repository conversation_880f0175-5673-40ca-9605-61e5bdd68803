'use client';

import type { AnnualBundle } from '@hami/web-api-schema/annual_bundle_service_pb';
import Link from 'next/link';
import { AnnualBundleRecruitmentStatus } from '@hami/web-api-schema/annual_bundle_service_pb';

interface Props {
  bundle: AnnualBundle;
}

const fmt = (n: number) => new Intl.NumberFormat('ja-JP').format(n);

export function AnnualBundleListMobileRow({ bundle }: Props) {
  const perShareManYen = (bundle.perShareAmount || 0) / 10000;
  const totalManYen = ((bundle.perShareAmount || 0) * (bundle.shares || 0)) / 10000;

  const getStatusLabel = () => {
    switch (bundle.recruitmentStatus) {
      case AnnualBundleRecruitmentStatus.AB_ACTIVE:
        return '募集中';
      case AnnualBundleRecruitmentStatus.AB_UPCOMING:
        return '募集前';
      case AnnualBundleRecruitmentStatus.AB_FULL:
        return '満口';
      case AnnualBundleRecruitmentStatus.AB_CLOSED:
        return '募集終了';
      default:
        return '';
    }
  };

  const getStatusBadgeColor = (statusLabel: string) => {
    switch (statusLabel) {
      case '募集中':
        return 'bg-[#96DFB3] text-[color:var(--color-hami-text-primary)]';
      case '募集前':
        return 'bg-gray-200 text-[color:var(--color-hami-text-secondary)]';
      case '満口':
        return 'bg-red-200 text-red-800';
      case '募集終了':
        return 'bg-gray-300 text-gray-700';
      default:
        return 'bg-gray-200 text-[color:var(--color-hami-text-secondary)]';
    }
  };

  return (
    <Link href={`/offered_horses/annual_bundles/${bundle.fiscalYear}/${bundle.annualBundleId}`} className="self-stretch bg-white border-b border-[color:var(--color-hami-stroke-border)]">
      <div className="px-3 py-3 border-b border-[color:var(--color-hami-stroke-border)] flex flex-col gap-3">
        <div className="flex items-start justify-between">
          <div className={`h-5 px-2 rounded-sm inline-flex items-center ${getStatusBadgeColor(getStatusLabel())}`}>
            <span className="text-xs leading-none">{getStatusLabel()}</span>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <div className="text-[color:var(--color-hami-text-primary)] text-lg leading-relaxed font-noto-serif-jp">{bundle.name}</div>
          <div className="text-[10px] text-hami-glyph-subtle">募集馬19頭全頭に、一口ずつ出資の応援パッケージです。</div>
          <div className="text-[10px] text-hami-glyph-subtle">優先当選権がついているので、個別出資よりも優先抽選の対象となります。</div>
          <div className="text-[10px] text-hami-glyph-subtle">100口限定で、一次募集と二次募集に各50口ずつ募集させていただきます。募集口数オーバーの際は抽選します。</div>
          <div className="text-[10px] text-hami-glyph-subtle">リアルダビスタ企画の2頭もパッケージに含まれます。</div>
        </div>

        <div className="grid grid-cols-3">
          <div className="px-2 flex flex-col items-center gap-1">
            <div className="flex items-end">
              <span className="text-base text-[color:var(--color-hami-text-primary)]">{fmt(totalManYen)}</span>
              <span className="text-sm text-hami-glyph-subtle ml-1">万円</span>
            </div>
            <div className="text-xs text-hami-glyph-subtle leading-none">募集総額</div>
          </div>
          <div className="px-2 border-x border-[color:var(--color-hami-stroke-separator)] flex flex-col items-center gap-1">
            <div className="flex items-end">
              <span className="text-base text-[color:var(--color-hami-text-primary)]">{fmt(perShareManYen)}</span>
              <span className="text-sm text-hami-glyph-subtle ml-1">万円</span>
            </div>
            <div className="text-xs text-hami-glyph-subtle leading-none">一口出資額</div>
          </div>
          <div className="px-2 flex flex-col items-center gap-1">
            <div className="flex items-end">
              <span className="text-base text-[color:var(--color-hami-text-primary)]">{fmt(bundle.shares)}</span>
              <span className="text-sm text-hami-glyph-subtle ml-1">口</span>
            </div>
            <div className="text-xs text-hami-glyph-subtle leading-none">募集口数</div>
          </div>
        </div>
      </div>
    </Link>
  );
}
