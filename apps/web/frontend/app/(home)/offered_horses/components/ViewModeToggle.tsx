'use client';

interface ViewModeToggleProps {
  mode: 'card' | 'list';
  onChange: (mode: 'card' | 'list') => void;
}

export function ViewModeToggle({ mode, onChange }: ViewModeToggleProps) {
  return (
    <div className="inline-flex items-center gap-2">
      <span className="text-lg font-bold text-[color:var(--color-hami-text-muted)]">表示</span>
      <div className="w-px h-4 bg-[color:var(--color-hami-stroke-separator)]"></div>
      <div className="flex rounded-lg overflow-hidden border border-[color:var(--color-hami-border-default)]">
        <button
          type="button"
          onClick={() => onChange('card')}
          className={`px-3 py-2 text-sm md:text-base ${
            mode === 'card'
              ? 'bg-[color:var(--color-hami-primary-light)] text-[color:var(--color-hami-primary-dark)]'
              : 'bg-white text-[color:var(--color-hami-text-primary)] hover:bg-gray-50'
          }`}
        >
          カード
        </button>
        <div className="w-px bg-[color:var(--color-hami-stroke-separator)]" />
        <button
          type="button"
          onClick={() => onChange('list')}
          className={`px-3 py-2 text-sm md:text-base ${
            mode === 'list'
              ? 'bg-[color:var(--color-hami-primary-light)] text-[color:var(--color-hami-primary-dark)]'
              : 'bg-white text-[color:var(--color-hami-text-primary)] hover:bg-gray-50'
          }`}
        >
          リスト
        </button>
      </div>
    </div>
  );
}


