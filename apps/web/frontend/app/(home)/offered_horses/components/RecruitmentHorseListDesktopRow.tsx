'use client';

import Link from 'next/link';
import { RecruitmentStatus, type RecruitmentHorseItem } from '@hami/web-api-schema/recruitment_horse_service_pb';
import { Label } from './Label';
import { HorseGender } from './horse/HorseGender';
import { QuantityControl } from './QuantityControl';
import { formatCoatColor, getAffiliationColor, formatAffiliation } from '@web/utils/horseFormatters';
import { GetMeResponse } from '@hami/web-api-schema/user_service_pb';
import VideoButton from './VideoButton';
import { useInvestmentCart } from '@web/contexts/InvestmentCartContext';

interface Props {
  horse: RecruitmentHorseItem;
  userInfo: GetMeResponse | null;
}

const getStatusBadgeColor = (statusLabel: string) => {
  switch (statusLabel) {
    case '募集中':
      return 'bg-hami-bush-700 text-[color:var(--color-hami-glyph-reverse)]';
    case '満口':
      return 'bg-red-200 text-red-800';
    case '募集終了':
      return 'bg-gray-300 text-gray-700';
    default:
      return 'bg-gray-200 text-[color:var(--color-hami-text-secondary)]';
  }
};

export function RecruitmentHorseListDesktopRow({ horse, userInfo }: Props) {
  const sharePrice = horse.amountTotal / horse.sharesTotal / 10000;
  const totalAmountInManYen = horse.amountTotal / 10000;
  const fmt = (n: number) => new Intl.NumberFormat('ja-JP').format(n);

  // 一覧API拡張により props から直接判定・取得
  const videoId = horse.latestYoutubeId;
  const startAt = horse.latestStartAtSeconds ?? undefined;
  const { upsertHorse, findHorseItem } = useInvestmentCart();
  const cartItem = findHorseItem(horse.recruitmentYear, horse.recruitmentNo);
  const requestedShares = cartItem?.requestedNumber ?? 0;

  const handleRequestedSharesChange = (value: number) => {
    upsertHorse({
      recruitmentYear: horse.recruitmentYear,
      recruitmentNo: horse.recruitmentNo,
      horseName: `${horse.recruitmentNo.toString().padStart(3, '0')}. ${horse.horseName || horse.recruitmentName}`,
      requestedNumber: value,
      rejectPartialAllocation: cartItem?.rejectPartialAllocation ?? false,
      maxSharesPerOrder: horse.maxSharesPerOrder,
      sharePriceManYen: sharePrice,
    });
  };

  return (
    <tr className="border-b border-[color:var(--color-hami-stroke-separator)]">
      <td className="px-3 py-3 align-middle w-14 text-center text-sm text-[color:var(--color-hami-text-primary)]">
        {horse.recruitmentNo.toString().padStart(3, '0')}
      </td>
      <td className="px-3 py-3 align-middle w-28">
        <div className={`w-20 h-5 rounded-sm inline-flex justify-center items-center ${getStatusBadgeColor(horse.statusLabel)}`}>
          <div className="text-xs leading-none">{horse.statusLabel}</div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle">
        <div className="flex flex-col gap-3">
          <Link
            href={`/offered_horses/${horse.recruitmentYear}/${horse.recruitmentNo}`}
            className="text-[color:var(--color-hami-text-primary)] hover:underline"
          >
            {/* 馬名と性別 */}
            <div className="flex items-center gap-2">
              <div className="text-base leading-normal tracking-wide font-noto-serif-jp whitespace-nowrap">
                {horse.horseName || horse.recruitmentName}
              </div>
              <HorseGender gender={horse.gender} />
              {horse.specialFlag && (
                <div className="whitespace-nowrap bg-hami-bush-700 text-white text-xs px-2 rounded-full font-medium">
                  キャンペーン募集馬
                </div>
              )}
            </div>
          </Link>
          <div className="flex flex-col gap-1">
            {/* 父・母父名（1列で収まる時は1列、収まらない時は2段表示・左揃え） */}
            <div className="flex flex-wrap gap-1">
              {horse.sireName && (
                <div className="text-sm font-normal text-hami-glyph-subtle bg-hami-bg-gray rounded px-1 h-4 flex items-center justify-start leading-none">
                  父 {horse.sireName}
                </div>
              )}
              {horse.broodmareSireName && (
                <div className="text-sm font-normal text-hami-glyph-subtle bg-hami-bg-gray rounded px-1 h-4 flex items-center justify-start leading-none">
                  母父 {horse.broodmareSireName}
                </div>
              )}
            </div>
            {/* その他の情報 */}
            <div className="flex flex-wrap items-center gap-1">
              <Label label={formatCoatColor(horse.coatColor)} />
              <Label label={horse.birthDateFormatted} />
              {horse.affiliation && horse.affiliation !== '未定' && horse.stableName && (
                <Label>
                  <>
                    <span style={{ color: getAffiliationColor(horse.affiliation) }}>{formatAffiliation(horse.affiliation)}</span>
                    <div className="w-1 h-1" />
                    <span>{horse.stableName}</span>
                  </>
                </Label>
              )}
            </div>
          </div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle w-32 text-right">
        <div className="inline-flex items-end justify-end">
          <div className="text-[color:var(--color-hami-text-primary)] text-base tracking-wide">{fmt(totalAmountInManYen)}</div>
          <div className="text-hami-glyph-subtle text-sm leading-snug tracking-tight ml-1">万円</div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle w-28 text-right">
        <div className="inline-flex items-end justify-end">
          <div className="text-[color:var(--color-hami-text-primary)] text-base tracking-wide">{fmt(sharePrice)}</div>
          <div className="text-hami-glyph-subtle text-sm leading-snug tracking-tight ml-1">万円</div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle w-28 text-right">
        <div className="inline-flex items-end justify-end">
          <div className="text-[color:var(--color-hami-text-primary)] text-base tracking-wide">{fmt(horse.sharesTotal)}</div>
          <div className="text-hami-glyph-subtle text-sm leading-snug tracking-tight ml-1">口</div>
        </div>
      </td>
      <td className="px-3 py-3 align-middle w-32 text-center">
        <VideoButton
          youtubeVideoId={videoId}
          startAtSeconds={startAt}
          size="medium"
          fullWidth={true}
          showWhenDisabled={false}
          showIcon={false}
        />
      </td>
      {userInfo && horse.recruitmentStatus === RecruitmentStatus.ACTIVE && (
        <td className="px-2 w-[98px] py-3 align-middle">
          <QuantityControl
            value={requestedShares}
            onChange={handleRequestedSharesChange}
            min={0}
            max={horse.maxSharesPerOrder ?? undefined}
          />
        </td>
      )}
    </tr>
  );
}
