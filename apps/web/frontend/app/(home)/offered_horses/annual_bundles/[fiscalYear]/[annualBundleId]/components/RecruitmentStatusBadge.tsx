import { AnnualBundleRecruitmentStatus } from '@hami/web-api-schema/annual_bundle_service_pb';

interface RecruitmentStatusBadgeProps {
  status?: AnnualBundleRecruitmentStatus;
  className?: string;
}

// デスクトップ用 募集ステータスバッジ（募集中 / 満口 のみ）
export default function RecruitmentStatusBadge({ status, className = '' }: RecruitmentStatusBadgeProps) {
  if (status !== AnnualBundleRecruitmentStatus.AB_ACTIVE && status !== AnnualBundleRecruitmentStatus.AB_FULL) return null;

  const isActive = status === AnnualBundleRecruitmentStatus.AB_ACTIVE;

  const baseColor = isActive ? '#266f44' : '#b61a2b';
  const borderOuter = isActive ? '#013115' : '#82010f';
  const textColor = '#f6f5f5';

  return (
    <>
      <div className={`hidden lg:inline-block ${className}`} aria-label={isActive ? '募集中' : '満口'}>
        <div
          className="rounded-[24px] border border-solid"
          style={{
            backgroundColor: baseColor,
            borderColor: borderOuter,
            boxShadow: '0px 14px 6px -12px rgba(12,18,57,0.6)'
          }}
        >
          <div
            className="m-1 rounded-[20px] border border-white px-6 py-3 flex items-center justify-center"
            style={{ backgroundColor: baseColor }}
          >
            <span className="font-semibold tracking-[0.2px]" style={{ color: textColor }}>
              {isActive ? '募集中' : '満口'}
            </span>
          </div>
        </div>
      </div>
      
      <div className={`inline-block lg:hidden ${className}`} aria-label={isActive ? '募集中' : '満口'}>
        {/* 外側ブロック（濃色背景＋外枠＋ドロップ影） */}
        <div
          className="rounded-[12px] border border-solid"
          style={{ backgroundColor: baseColor, borderColor: borderOuter, boxShadow: '0px 7px 3px -6px rgba(12,18,57,0.6)' }}
        >
          {/* 内側ブロック（2px マージン、白ボーダー、角丸10px） */}
          <div
            className="m-[2px] rounded-[10px] border border-white px-3 py-1 flex items-center justify-center"
            style={{ backgroundColor: baseColor }}
          >
            <span className="text-[11px] leading-none font-semibold" style={{ color: textColor }}>
              {isActive ? '募集中' : '満口'}
            </span>
          </div>
        </div>
      </div>
    </>
  );
}
