'use client';

import Link from 'next/link';

import type { RecruitmentHorseItem } from '@hami/web-api-schema/recruitment_horse_service_pb';
import { Label } from '../../../../components/Label';
import { HorseGender } from '../../../../components/horse/HorseGender';
import { formatCoatColor, getAffiliationColor } from '@web/utils/horseFormatters';

interface HorseListRowProps {
  horse: RecruitmentHorseItem;
}

export function HorseListRow({ horse }: HorseListRowProps) {
  return (
    <Link href={`/offered_horses/${horse.recruitmentYear}/${horse.recruitmentNo}`}>
      <div className="self-stretch bg-white border-b border-hami-stroke-border">
        <div className="px-3 py-3 border-b border-[color:var(--color-hami-stroke-border)] flex flex-col gap-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <div className="h-4 inline-flex items-center gap-1">
                <span className="text-sm text-hami-glyph-subtle leading-snug">No.</span>
                <span className="text-sm text-hami-glyph-base leading-snug">{horse.recruitmentNo.toString().padStart(3, '0')}</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            {/* 馬名と性別 */}
            <div className="flex items-center gap-2">
              <div className="text-[color:var(--color-hami-text-primary)] text-lg leading-relaxed font-noto-serif-jp">
                {horse.horseName || horse.recruitmentName}
              </div>
              <HorseGender gender={horse.gender} />
            </div>

            <div className="flex flex-col gap-1">
              {/* 父・母父名（1列で収まる時は1列、収まらない時は2段表示・左揃え） */}
              <div className="flex flex-wrap gap-1">
                {horse.sireName && (
                  <div className="text-sm font-normal text-hami-glyph-subtle bg-hami-bg-gray rounded px-1 h-4 flex items-center justify-start leading-none">
                    父 {horse.sireName}
                  </div>
                )}
                {horse.broodmareSireName && (
                  <div className="text-sm font-normal text-hami-glyph-subtle bg-hami-bg-gray rounded px-1 h-4 flex items-center justify-start leading-none">
                    母父 {horse.broodmareSireName}
                  </div>
                )}
              </div>

              <div className="flex items-start gap-1">
                <Label label={formatCoatColor(horse.coatColor)} />
                <Label label={horse.birthDateFormatted} />
                {horse.affiliation && horse.stableName && (
                  <Label>
                    <>
                      <span style={{ color: getAffiliationColor(horse.affiliation) }}>{horse.affiliation}</span>
                      <div className="w-1 h-1" />
                      <span>{horse.stableName}</span>
                    </>
                  </Label>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
