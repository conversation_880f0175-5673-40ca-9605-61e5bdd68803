
'use client';

import type { RecruitmentHorseItem } from '@hami/web-api-schema/recruitment_horse_service_pb';
import { HorseListRow } from './HorseListRow';
import RecruitmentStatusBadge from './RecruitmentStatusBadge';
import { AnnualBundleRecruitmentStatus } from '@hami/web-api-schema/annual_bundle_service_pb';

interface Props {
  status: AnnualBundleRecruitmentStatus;
  name: string;
  totalManYen: number;
  perShareManYen: number;
  shares: number;
  horses: RecruitmentHorseItem[];
}

const fmt = (n: number) => new Intl.NumberFormat('ja-JP').format(n);

export default function AnnualBundleDetailClient({ status, name, totalManYen, perShareManYen, shares, horses }: Props) {
  return (
    <div>
        {/*募集ステータスバッジ*/}
        <div className="flex items-center gap-4 mb-4">
            <RecruitmentStatusBadge status={status} />
            </div>
                <div className="flex items-center gap-4 mb-4">
                    <h1 className="text-xl lg:text-2xl text-hami-primary-dark font-noto-serif-jp">{name}</h1>
                </div>
                <div className="mb-4">
                    <img
                        src="https://bhc-prd-public-files.s3.ap-northeast-1.amazonaws.com/top/carousel/2.jpg"
                        alt="一括購入パッケージ紹介画像"
                        className="w-full h-auto rounded-sm"
                    />
                </div>
                <div className="bg-white rounded-2xl p-4">
                    <div className="space-y-1 mb-4">
                    <div className="flex justify-between items-center py-2">
                        <span className="text-sm text-hami-glyph-base">募集総額</span>
                        <div className="text-right">
                        <span className="text-[24px] font-[400] text-hami-glyph-base">
                            {totalManYen ? fmt(totalManYen) : 0}
                        </span>
                        <span className="text-xs text-hami-glyph-subtle ml-1">万円</span>
                        </div>
                    </div>

                    <div className="flex justify-between items-center py-2">
                        <span className="text-sm text-hami-glyph-base">一口出資額</span>
                        <div className="text-right">
                        <span className="text-[24px] font-[400] text-hami-glyph-base">
                            {perShareManYen
                            ? (perShareManYen).toFixed(1)
                            : '0.0'}
                        </span>
                        <span className="text-sm text-hami-glyph-subtle ml-1">万円</span>
                        </div>
                    </div>

                    <div className="flex justify-between items-center py-2">
                        <span className="text-sm text-hami-glyph-base">募集口数</span>
                        <div className="text-right">
                        <span className="text-[24px] font-[400] text-hami-glyph-base">
                            {shares ? fmt(shares) : 0}
                        </span>
                        <span className="text-sm text-hami-glyph-subtle ml-1">口</span>
                    </div>
                </div>
            </div>
        </div>
            

        <div className="mb-4 mt-6">
            <h2 className="text-lg font-bold">概要</h2>
        </div>

        <div className="">
            {horses.map((horse) => (
                <HorseListRow key={horse.horseId} horse={horse} />
            ))}
        </div>
    </div>
  );
}


