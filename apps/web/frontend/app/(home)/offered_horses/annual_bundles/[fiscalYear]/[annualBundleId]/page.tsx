'use server';

import { notFound } from 'next/navigation';
import Link from 'next/link';
import { listAnnualBundlesByFiscalYear } from '@web/api_clients/annual_bundle_client';
import type { AnnualBundle } from '@hami/web-api-schema/annual_bundle_service_pb';
import AnnualBundleDetailClient from './components/AnnualBundleDetailClient';
import { listRecruitmentHorses } from '@web/api_clients/recruitment_horse_client';
import type { RecruitmentHorseItem } from '@hami/web-api-schema/recruitment_horse_service_pb';

interface Props {
  params: Promise<{ fiscalYear: string; annualBundleId: string }>;
}

export default async function AnnualBundleDetailPage({ params }: Props) {
  const { fiscalYear: fy, annualBundleId: id } = await params;
  const fiscalYear = parseInt(fy, 10);
  const annualBundleId = parseInt(id, 10);

  if (isNaN(fiscalYear) || isNaN(annualBundleId)) {
    notFound();
  }

  // 年度で一覧取得し、該当IDのバンドルを抽出（専用詳細APIがないため）
  const res = await listAnnualBundlesByFiscalYear({ fiscalYear });
  const bundle: AnnualBundle | undefined = res?.bundles?.find((b) => b.annualBundleId === annualBundleId);
  if (!bundle) {
    notFound();
  }

  // 募集馬一覧（同年度）を取得し、バンドル内の馬のみ抽出してモバイルに供給
  let horses: RecruitmentHorseItem[] = [];
  try {
    const horsesRes = await listRecruitmentHorses({ targetYear: fiscalYear, page: 1, pageSize: 100 });
    const horseLists = horsesRes?.horses ?? [];
    const recruitmentNos = new Set((bundle.horses || []).map((h) => h.recruitmentNo));
    horses = horseLists.filter((h) => recruitmentNos.has(h.recruitmentNo));
  } catch {
    horses = [];
  }

  const perShareManYen = (bundle.perShareAmount || 0) / 10000;
  const totalManYen = ((bundle.perShareAmount || 0) * (bundle.shares || 0)) / 10000;

  return (
    <div className="min-h-screen bg-[color:var(--color-hami-bg-main)]">
      <div className="max-w-6xl mx-auto px-4 lg:px-6 py-6 lg:py-8">
        <nav className="mb-4">
          <ol className="flex items-center gap-2 text-sm text-hami-glyph-base">
            <li className="flex items-center gap-2">
              <Link href="/offered_horses" className="hover:underline">募集馬</Link>
              <span>/</span>
              <span>一括購入パッケージ詳細</span>
            </li>
          </ol>
        </nav>

        <div className="flex items-center justify-center">
          <div className="w-full max-w-[600px]">
            <AnnualBundleDetailClient
              status={bundle.recruitmentStatus}
              name={bundle.name}
              totalManYen={totalManYen}
              perShareManYen={perShareManYen}
              shares={bundle.shares || 0}
              horses={horses}
            />
          </div>
        </div>
        

        <div className="mt-6">
          <Link href="/offered_horses" className="text-hami-primary-dark hover:underline">一覧へ戻る</Link>
        </div>
      </div>
    </div>
  );
}


