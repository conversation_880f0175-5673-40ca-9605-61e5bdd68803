'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { listRecruitmentHorseVideos, getRecruitmentHorse } from '../../../../../../api_clients/recruitment_horse_client';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import type { RecruitmentHorseVideo, RecruitmentHorseDetail } from '@hami/web-api-schema/recruitment_horse_service_pb';

export default function HorseVideosPage() {
  const params = useParams();
  const [videos, setVideos] = useState<RecruitmentHorseVideo[]>([]);
  const [horse, setHorse] = useState<RecruitmentHorseDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const recruitmentYear = parseInt(params.recruitmentYear as string);
  const recruitmentNo = parseInt(params.recruitmentNo as string);

  useEffect(() => {
    if (isNaN(recruitmentYear) || isNaN(recruitmentNo)) {
      notFound();
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        const [videosRes, horseRes] = await Promise.all([
          listRecruitmentHorseVideos({ recruitmentYear, recruitmentNo }),
          getRecruitmentHorse({ recruitmentYear, recruitmentNo }),
        ]);

        if (!horseRes.horse) {
          notFound();
        }

        setVideos(videosRes.videos || []);
        setHorse(horseRes.horse);
      } catch (err) {
        console.error('動画一覧取得エラー:', err);
        setError('動画一覧の取得に失敗しました。');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [recruitmentYear, recruitmentNo]);

  const handleVideoClick = (youtubeVideoId: string, startAtSeconds?: number) => {
    const startParam = startAtSeconds ? `?start=${startAtSeconds}` : '';
    window.open(`https://www.youtube.com/watch?v=${youtubeVideoId}${startParam}`, '_blank');
  };

  const formatDate = (year: number, month: number, day: number) => {
    return `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[color:var(--color-hami-bg-main)] flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[color:var(--color-hami-primary-dark)]"></div>
        <span className="ml-2 text-[color:var(--color-hami-text-secondary)]">読み込み中...</span>
      </div>
    );
  }

  if (error || !horse) {
    return (
      <div className="min-h-screen bg-[color:var(--color-hami-bg-main)] flex items-center justify-center">
        <div className="text-center">
          <p className="text-[color:var(--color-hami-error-text)] mb-4">{error || '馬の情報が見つかりません'}</p>
          <Link href="/offered_horses" className="text-[color:var(--color-hami-primary-dark)] hover:underline">
            募集馬一覧に戻る
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* スマホ版（Figmaデザイン通り） */}
      <div className="min-h-screen bg-white md:hidden">
        <div className="max-w-md mx-auto">
          {/* パンくずナビゲーション */}
          <div className="h-[50px] flex items-center px-4 border-b border-[#e7e6e6]">
            <Link href={`/offered_horses/${recruitmentYear}/${recruitmentNo}`} className="text-[#0e311e] text-[16px] hover:underline">
              ← {horse.basicInfo?.horseName || horse.basicInfo?.recruitmentName}
            </Link>
          </div>

          {/* メインコンテンツ */}
          <div className="px-0 py-0">
            {/* H1見出し */}
            <div className="h-[58px] flex items-center px-4">
              <h1 className="font-normal text-[28px] text-[#201f1f] leading-[1.4]">動画一覧</h1>
            </div>

            {/* 動画リスト */}
            <div className="px-5 space-y-4">
              {videos.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-[#6f6b6b] text-[16px]">動画がありません</p>
                </div>
              ) : (
                videos.map((video, index) => (
                  <div
                    key={`${video.videoId}-${index}`}
                    className="flex gap-2 items-start cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => handleVideoClick(video.youtubeVideoId, video.startAtSeconds)}
                  >
                    {/* サムネイル + 再生ボタン */}
                    <div className="relative shrink-0">
                      <div className="w-[120px] h-[67px] bg-gray-300 rounded overflow-hidden">
                        <img
                          src={`https://img.youtube.com/vi/${video.youtubeVideoId}/mqdefault.jpg`}
                          alt="動画サムネイル"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      {/* 再生ボタンオーバーレイ */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-6 h-6 bg-black bg-opacity-60 rounded-full flex items-center justify-center">
                          <div className="w-0 h-0 border-l-[6px] border-l-white border-t-[4px] border-t-transparent border-b-[4px] border-b-transparent ml-0.5"></div>
                        </div>
                      </div>
                    </div>

                    {/* 日付 */}
                    <div className="flex items-start justify-start px-0 py-2 flex-grow">
                      <span className="font-normal text-[15px] text-[#201f1f] tracking-[0.75px] leading-[1.6]">
                        {formatDate(video.videoYear, video.videoMonth, video.videoDay)}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* デスクトップ版（レスポンシブ対応） */}
      <div className="min-h-screen bg-background-default hidden md:block">
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* パンくずナビゲーション */}
          <div className="mb-6">
            <Link
              href={`/offered_horses/${recruitmentYear}/${recruitmentNo}`}
              className="text-[#0e311e] text-[16px] hover:underline flex items-center gap-2"
            >
              <span>←</span>
              <span>{horse.basicInfo?.horseName || horse.basicInfo?.recruitmentName}</span>
            </Link>
          </div>

          {/* メインコンテンツ */}
          <div className="bg-white rounded-lg shadow-sm">
            {/* H1見出し */}
            <div className="px-6 py-6 border-b border-[#e7e6e6]">
              <h1 className="font-normal text-[32px] text-[#201f1f] leading-[1.4]">動画一覧</h1>
            </div>

            {/* 動画リスト */}
            <div className="p-6">
              {videos.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-[#6f6b6b] text-[16px]">動画がありません</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {videos.map((video, index) => (
                    <div
                      key={`${video.videoId}-${index}`}
                      className="cursor-pointer hover:shadow-md transition-shadow duration-200 bg-white border border-[#e7e6e6] rounded-lg overflow-hidden"
                      onClick={() => handleVideoClick(video.youtubeVideoId, video.startAtSeconds)}
                    >
                      {/* サムネイル + 再生ボタン */}
                      <div className="relative aspect-video bg-gray-300">
                        <img
                          src={`https://img.youtube.com/vi/${video.youtubeVideoId}/mqdefault.jpg`}
                          alt="動画サムネイル"
                          className="w-full h-full object-cover"
                        />
                        {/* 再生ボタンオーバーレイ */}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="w-12 h-12 bg-black bg-opacity-60 rounded-full flex items-center justify-center hover:bg-opacity-80 transition-all">
                            <div className="w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1"></div>
                          </div>
                        </div>
                      </div>

                      {/* 動画情報 */}
                      <div className="p-4">
                        <div className="text-[#201f1f] font-medium text-[16px] mb-2">{video.title || '動画'}</div>
                        <div className="text-[#6f6b6b] text-[14px]">{formatDate(video.videoYear, video.videoMonth, video.videoDay)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
