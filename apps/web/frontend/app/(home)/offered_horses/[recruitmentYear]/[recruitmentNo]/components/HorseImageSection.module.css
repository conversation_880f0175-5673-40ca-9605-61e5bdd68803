/* Desktop Swiper Pagination Styles */
.desktopPagination {
  width: auto !important;
  display: inline-flex !important;
}

.desktopPagination :global(.swiper-pagination-bullet) {
  width: 6px;
  height: 6px;
  background-color: #d1d0d0;
  opacity: 1;
  margin: 0 !important;
  border-radius: 9999px;
  transition: all 0.2s;
}

.desktopPagination :global(.swiper-pagination-bullet-active) {
  width: 18px;
  height: 6px;
  background-color: #0e311e;
  border-radius: 9999px;
}

/* Mobile Swiper Pagination Styles */
.mobilePagination {
  width: auto !important;
  display: inline-flex !important;
}

.mobilePagination :global(.swiper-pagination-bullet) {
  width: 6px;
  height: 6px;
  background-color: #d1d0d0;
  opacity: 1;
  margin: 0 !important;
  border-radius: 9999px;
  transition: all 0.2s;
}

.mobilePagination :global(.swiper-pagination-bullet-active) {
  width: 18px;
  height: 6px;
  background-color: #0e311e;
  border-radius: 9999px;
}

/* Hide default Swiper navigation buttons (we use custom ones) */
.swiperScope :global(.swiper-button-prev),
.swiperScope :global(.swiper-button-next) {
  display: none;
}

/* Mobile Swiper specific styles */
.mobileSwiper {
  width: 100% !important;
  height: 100% !important;
}

.mobileSwiper :global(.swiper-wrapper) {
  width: 100% !important;
  height: 100% !important;
}

.mobileSwiper :global(.swiper-slide) {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
}
