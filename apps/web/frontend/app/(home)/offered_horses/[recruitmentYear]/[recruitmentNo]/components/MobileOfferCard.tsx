"use client";

import { useCallback } from 'react';
import { RecruitmentStatus } from '@hami/web-api-schema/recruitment_horse_service_pb';
import type { RecruitmentHorseDetail } from '@hami/web-api-schema/recruitment_horse_service_pb';
import { GetMeResponse } from '@hami/web-api-schema/user_service_pb';
import { useInvestmentCart } from '@web/contexts/InvestmentCartContext';
import { QuantityControl } from '@web/app/(home)/offered_horses/components/QuantityControl';

interface MobileOfferCardProps {
  horse: RecruitmentHorseDetail;
  onInvestmentApplication: () => void;
  userInfo: GetMeResponse | null;
}

export default function MobileOfferCard({ horse, onInvestmentApplication, userInfo }: MobileOfferCardProps) {
  const isActive = horse.basicInfo?.recruitmentStatus === RecruitmentStatus.ACTIVE;
  const isConflict = Boolean(horse.basicInfo?.conflictOfInterest);
  const { upsertHorse, findHorseItem } = useInvestmentCart();
  const cartItem = horse.basicInfo
    ? findHorseItem(horse.basicInfo.recruitmentYear, horse.basicInfo.recruitmentNo)
    : undefined;
  const requestedNumber = cartItem?.requestedNumber ?? 0;

  const formatPrice = (price: number) => new Intl.NumberFormat('ja-JP').format(price);

  const handleRequestedNumberChange = useCallback(
    (value: number) => {
      if (!horse.basicInfo) return;
      upsertHorse({
        recruitmentYear: horse.basicInfo.recruitmentYear,
        recruitmentNo: horse.basicInfo.recruitmentNo,
        horseName: `${horse.basicInfo.recruitmentNo.toString().padStart(3, '0')}. ${horse.basicInfo.horseName || horse.basicInfo.recruitmentName}`,
        requestedNumber: value,
        rejectPartialAllocation: cartItem?.rejectPartialAllocation ?? false,
        maxSharesPerOrder: horse.basicInfo.maxSharesPerOrder ?? undefined,
        sharePriceManYen:
          horse.basicInfo.amountTotal && horse.basicInfo.sharesTotal
            ? horse.basicInfo.amountTotal / horse.basicInfo.sharesTotal / 10000
            : undefined,
      });
    },
    [horse.basicInfo, upsertHorse, cartItem?.rejectPartialAllocation],
  );

  const handleApplyClick = useCallback(() => {
    if (!horse.basicInfo) return;
    if (requestedNumber <= 0) {
      handleRequestedNumberChange(1);
    }
    onInvestmentApplication();
  }, [handleRequestedNumberChange, horse.basicInfo, onInvestmentApplication, requestedNumber]);

  return (
    <div className="lg:hidden mt-4">
      <div className="bg-white rounded-2xl p-4">
        <div className="space-y-1 mb-4">
          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-hami-glyph-base">募集総額</span>
            <div className="text-right">
              <span className="text-[24px] font-[400] text-hami-glyph-base">
                {horse.basicInfo?.amountTotal ? formatPrice(Math.round(horse.basicInfo.amountTotal / 10000)) : 0}
              </span>
              <span className="text-xs text-hami-glyph-subtle ml-1">万円</span>
            </div>
          </div>

          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-hami-glyph-base">一口出資額</span>
            <div className="text-right">
              <span className="text-[24px] font-[400] text-hami-glyph-base">
                {horse.basicInfo?.amountTotal && horse.basicInfo?.sharesTotal
                  ? (horse.basicInfo.amountTotal / horse.basicInfo.sharesTotal / 10000).toFixed(1)
                  : '0.0'}
              </span>
              <span className="text-sm text-hami-glyph-subtle ml-1">万円</span>
            </div>
          </div>

          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-hami-glyph-base">募集口数</span>
            <div className="text-right">
              <span className="text-[24px] font-[400] text-hami-glyph-base">
                {horse.basicInfo?.sharesTotal ? formatPrice(horse.basicInfo.sharesTotal) : 0}
              </span>
              <span className="text-sm text-hami-glyph-subtle ml-1">口</span>
            </div>
          </div>
        </div>

        <div className="flex flex-col justify-center items-center gap-3">
          {userInfo && isActive && (
            <>
              <div className="w-full">
                <QuantityControl
                  value={requestedNumber}
                  onChange={handleRequestedNumberChange}
                  min={0}
                  max={horse.basicInfo?.maxSharesPerOrder ?? undefined}
                />
              </div>
              <button
                onClick={handleApplyClick}
                className="w-full bg-hami-primary-dark text-white py-3 rounded font-medium hover:bg-hami-primary-dark-hover hover:cursor-pointer transition-colors"
              >
                この馬をカートに追加
              </button>
            </>
          )}
          {isConflict && (
            <p className="w-full text-[10px] text-hami-text-secondary text-center">
              本馬は利益相反取引該当馬です。ご了承の上、ご出資ください。
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

