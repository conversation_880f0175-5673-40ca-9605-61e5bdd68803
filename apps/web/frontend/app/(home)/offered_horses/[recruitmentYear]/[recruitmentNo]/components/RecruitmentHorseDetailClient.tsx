'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import type { RecruitmentHorseDetail } from '@hami/web-api-schema/recruitment_horse_service_pb';
import { Gender } from '@hami/web-api-schema/common_enums_pb';
import { formatCoatColor } from '@web/utils/horseFormatters';
import RecruitmentStatusBadgeMobile from '@web/components/featured-horses/RecruitmentStatusBadgeMobile';
import Breadcrumb from './Breadcrumb';
import HorseImageSection from './HorseImageSection';
import HorseSidebar from './HorseSidebar';
import HorseDetailsSection from './HorseDetailsSection';
import ImageModal from './ImageModal';
import MobileOfferCard from './MobileOfferCard';
import type { GetMeResponse } from '@hami/web-api-schema/user_service_pb';
import VideoButton from '../../../components/VideoButton';
import DownloadButton from '../../../components/DownloadButton';
import { useInvestmentCart } from '@web/contexts/InvestmentCartContext';

interface Props {
  horse: RecruitmentHorseDetail;
  latestYoutubeId: string | null;
  latestStartAt: number | null;
  userInfo: GetMeResponse | null;
}

export default function RecruitmentHorseDetailClient({ horse, latestYoutubeId, latestStartAt, userInfo }: Props) {
  const router = useRouter();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isImageExpanded, setIsImageExpanded] = useState(false);
  const { upsertHorse, findHorseItem } = useInvestmentCart();

  const handleInvestmentApplication = () => {
    if (!horse?.basicInfo) return;
    const cartItem = findHorseItem(horse.basicInfo.recruitmentYear, horse.basicInfo.recruitmentNo);
    if (!cartItem || cartItem.requestedNumber <= 0) {
      upsertHorse({
        recruitmentYear: horse.basicInfo.recruitmentYear,
        recruitmentNo: horse.basicInfo.recruitmentNo,
        horseName: `${horse.basicInfo.recruitmentNo.toString().padStart(3, '0')}. ${horse.basicInfo.horseName || horse.basicInfo.recruitmentName}`,
        requestedNumber: 1,
        rejectPartialAllocation: false,
        maxSharesPerOrder: horse.basicInfo.maxSharesPerOrder ?? undefined,
        sharePriceManYen:
          horse.basicInfo.amountTotal && horse.basicInfo.sharesTotal
            ? horse.basicInfo.amountTotal / horse.basicInfo.sharesTotal / 10000
            : undefined,
      });
    }
    router.push('/member/investment-application');
  };

  const handleImageSelect = (index: number) => setSelectedImageIndex(index);
  const handleExpandImage = () => setIsImageExpanded(true);
  const handleCloseModal = () => setIsImageExpanded(false);
  const handlePrevImage = () => {
    const total = horse?.basicInfo?.imageUrls?.length ?? 0;
    if (total === 0) return;
    setSelectedImageIndex((prev) => (prev === 0 ? total - 1 : prev - 1));
  };
  const handleNextImage = () => {
    const total = horse?.basicInfo?.imageUrls?.length ?? 0;
    if (total === 0) return;
    setSelectedImageIndex((prev) => (prev === total - 1 ? 0 : prev + 1));
  };

  return (
    <div className="min-h-screen bg-background-default">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* パンくずリスト */}
        <Breadcrumb horseName={horse.basicInfo?.horseName} recruitmentName={horse.basicInfo?.recruitmentName} />

        {/* SP用 ヘッダー（募集状態ラベル・馬名・基本情報） */}
        <div className="lg:hidden mt-4">
          <div className="flex flex-row gap-2 mb-3 items-center">
            <p className="text-sm text-hami-glyph-base">募集番号：{horse.basicInfo?.recruitmentNo.toString().padStart(3, '0')}</p>
            {horse.basicInfo?.specialFlag && (
                <div className="whitespace-nowrap bg-hami-bush-700 text-white text-xs px-2 py-1 rounded-full font-medium">
                  キャンペーン募集馬
                </div>
            )}
          </div>
          <div className="mb-3">
            <RecruitmentStatusBadgeMobile status={horse.basicInfo?.recruitmentStatus} />
          </div>
          <h1 className="text-[28px] font-noto-serif-jp text-hami-glyph-base mb-3">
            {horse.basicInfo?.horseName || horse.basicInfo?.recruitmentName}
          </h1>
          <div className="flex items-center gap-2">
            <div className="border border-[color:var(--color-hami-border-default)] rounded-full px-2 py-1">
              <span className="text-sm">
                {(() => {
                  switch (horse.basicInfo?.gender) {
                    case Gender.STALLION:
                      return '牡';
                    case Gender.MARE:
                      return '牝';
                    case Gender.GELDING:
                      return 'セ';
                    default:
                      return '不明';
                  }
                })()}
              </span>
            </div>
            <div className="border border-[color:var(--color-hami-border-default)] rounded-full px-2 py-1">
              <span className="text-sm text-[color:var(--color-hami-text-secondary)]">{formatCoatColor(horse.basicInfo?.coatColor)}</span>
            </div>
            <div className="border border-[color:var(--color-hami-border-default)] rounded-full px-2 py-1">
              <span className="text-sm text-[color:var(--color-hami-text-secondary)]">
                {horse.basicInfo?.birthYear}年{horse.basicInfo?.birthMonth}月{horse.basicInfo?.birthDay}日
              </span>
            </div>
          </div>
        </div>

        {/* 顔写真（SPのみ） */}
        {(() => {
          const original = horse.basicInfo?.imageUrls || [];
          const face = horse.basicInfo?.faceImageUrl || original[0] || '';
          if (!face) return null;
          return (
            <div className="lg:hidden my-4 grid grid-cols-[4fr_9fr] items-stretch w-full gap-2">
              <div className="aspect-[2/3]">
                <Image
                  src={face}
                  alt={`${horse.basicInfo?.horseName || '馬'}の顔写真`}
                  width={400}
                  height={600}
                  className="w-full aspect-[2/3] object-cover rounded-sm"
                  sizes="(max-width: 1024px) 33vw, 180px"
                  priority
                  unoptimized
                />
              </div>
              <div className="relative aspect-[3/2]">
                <HorseImageSection
                  imageUrls={horse.basicInfo?.imageUrls || []}
                  horseName={horse.basicInfo?.horseName || ''}
                  selectedImageIndex={selectedImageIndex}
                  onImageSelect={handleImageSelect}
                  onExpandImage={handleExpandImage}
                  isMobileOnly={true}
                />
              </div>
            </div>
          );
        })()}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 order-1 lg:order-1">
            {/* 画像セクション - PCのみ表示 */}
            <HorseImageSection
              imageUrls={horse.basicInfo?.imageUrls || []}
              horseName={horse.basicInfo?.horseName || ''}
              selectedImageIndex={selectedImageIndex}
              onImageSelect={handleImageSelect}
              onExpandImage={handleExpandImage}
              isMobileOnly={false}
              faceImageUrl={horse.basicInfo?.faceImageUrl}
            />

            {/* 動画ボタン */}
            <div className="mb-4">
              <VideoButton
                youtubeVideoId={latestYoutubeId}
                startAtSeconds={latestStartAt}
                size="medium"
                fullWidth={true}
                showWhenDisabled={true}
                showIcon={true}
              />
            </div>

              {/* ダウンロードボタン群 */}
            <div className="grid grid-cols-2 gap-4">
              <DownloadButton
                downloadUrl={horse.basicInfo?.pedigreeUrl}
                label="血統表"
                fileName={`${horse.basicInfo?.horseName || '募集馬'}_血統表.pdf`}
                size="medium"
                showIcon={true}
                showWhenDisabled={true}
              />
              <DownloadButton
                downloadUrl={horse.basicInfo?.blackTypeUrl}
                label="ブラックタイプ"
                fileName={`${horse.basicInfo?.horseName || '募集馬'}_ブラックタイプ.pdf`}
                size="medium"
                showIcon={true}
                showWhenDisabled={true}
              />
            </div>

            {/* SP専用 募集情報＋出資ボタン（画像のすぐ下に配置） */}
            <MobileOfferCard horse={horse} onInvestmentApplication={handleInvestmentApplication} userInfo={userInfo}/>

            {/* 詳細情報セクション（概要の直下に動画を表示） */}
            <HorseDetailsSection horse={horse} latestYoutubeId={latestYoutubeId} latestStartAt={latestStartAt} />
          </div>

          {/* サイドバー */}
          <div className="lg:col-span-1 order-2 lg:order-2">
            <HorseSidebar horse={horse} onInvestmentApplication={handleInvestmentApplication} userInfo={userInfo} />
          </div>
        </div>
      </div>

      {/* 画像拡大モーダル */}
      {isImageExpanded && horse.basicInfo?.imageUrls && (
        <ImageModal
          imageUrls={horse.basicInfo.imageUrls}
          horseName={horse.basicInfo?.horseName || ''}
          selectedImageIndex={selectedImageIndex}
          onClose={handleCloseModal}
          onImageSelect={handleImageSelect}
          onPrevImage={handlePrevImage}
          onNextImage={handleNextImage}
        />
      )}
    </div>
  );
}
