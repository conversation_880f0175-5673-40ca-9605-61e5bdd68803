import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '2025年度募集スケジュールのお知らせ | Blooming Horse Club',
  description:
    'Blooming Horse Clubの2025年度募集スケジュールをご案内いたします。新規入会希望者向けの詳細なスケジュールと注意事項をご確認ください。',
  openGraph: {
    title: '2025年度募集スケジュールのお知らせ | Blooming Horse Club',
    description:
      'Blooming Horse Clubの2025年度募集スケジュールをご案内いたします。新規入会希望者向けの詳細なスケジュールと注意事項をご確認ください。',
    type: 'website',
  },
  twitter: {
    card: 'summary',
    title: '2025年度募集スケジュールのお知らせ | Blooming Horse Club',
    description:
      'Blooming Horse Clubの2025年度募集スケジュールをご案内いたします。新規入会希望者向けの詳細なスケジュールと注意事項をご確認ください。',
  },
  alternates: {
    canonical: '/special/2025',
  },
};

export default function Join2025Layout({ children }: { children: React.ReactNode }) {
  return children;
}
