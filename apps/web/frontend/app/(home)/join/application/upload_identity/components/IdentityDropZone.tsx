'use client';

import React from 'react';

type Props = {
  inputId: string;
  identityPreview: string | null;
  isUploading: boolean;
  onRemove: () => void;
  onDragEnter: (e: React.DragEvent) => void;
  onDragLeave: (e: React.DragEvent) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  accept: string;
  variant?: 'desktop' | 'mobile';
};

export const IdentityDropZone: React.FC<Props> = ({
  inputId,
  identityPreview,
  isUploading,
  onRemove,
  onDragEnter,
  onDragLeave,
  onDragOver,
  onDrop,
  onChange,
  accept,
  variant = 'desktop',
}) => {
  const delBtnSize = variant === 'desktop' ? 'w-8 h-8' : 'w-6 h-6';
  const delIconSize = variant === 'desktop' ? 'w-4 h-4' : 'w-3 h-3';
  const iconSize = variant === 'desktop' ? 'w-12 h-12' : 'w-8 h-8';
  const pdfTextClass = variant === 'desktop' ? 'text-sm' : 'text-xs';

  return (
    <div className="relative">
      <div
        className="relative w-full aspect-[2/1] bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors"
        data-testid="identity-drop-zone"
        onClick={() => document.getElementById(inputId)?.click()}
        onDragEnter={onDragEnter}
        onDragLeave={onDragLeave}
        onDragOver={onDragOver}
        onDrop={onDrop}
      >
        {identityPreview && identityPreview !== 'PDF' ? (
          <div className="relative w-full h-full" data-testid="identity-preview">
            <img src={identityPreview} alt="本人確認書類プレビュー" className="w-full h-full object-contain rounded-lg bg-white" />
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
              className={`absolute top-2 right-2 ${delBtnSize} bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg transition-colors`}
              aria-label="削除"
              disabled={isUploading}
            >
              <svg className={delIconSize} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        ) : identityPreview === 'PDF' ? (
          <div className="absolute inset-0 flex items-center justify-center" data-testid="identity-preview">
            <div className="text-center">
              <div className="text-gray-600 mb-2">
                <svg className={`${iconSize} mx-auto`} fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <p className={`${pdfTextClass} text-gray-600`}>PDF ファイル</p>
            </div>
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
              className={`absolute top-2 right-2 ${delBtnSize} bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg transition-colors`}
              aria-label="削除"
              disabled={isUploading}
            >
              <svg className={delIconSize} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-gray-400 mb-2">
                <svg className={`${iconSize} mx-auto`} fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <p className={variant === 'desktop' ? 'text-sm text-gray-500' : 'text-xs text-gray-500'}>
                {variant === 'desktop' ? 'クリックしてファイルを選択' : 'タップして選択'}
              </p>
            </div>
          </div>
        )}
      </div>

      <input
        id={inputId}
        name="identity-document"
        type="file"
        className="sr-only"
        onChange={onChange}
        accept={accept}
        disabled={isUploading}
      />
    </div>
  );
};

export default IdentityDropZone;
