import { useState, useCallback, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  getPersonalUploadUrl,
  getCorporateUploadUrl,
  completeDocumentUpload,
  getApplicantTypeByUploadToken,
  getUploadedDocuments,
} from '@web/api_clients/membership_application_client';
import { DocumentType } from '@hami/web-api-schema/membership_application_service_pb';
// import { getPersonalDocOption } from './personal_doc_options';

// Constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
const ALLOWED_DOCUMENT_TYPES = [...ALLOWED_IMAGE_TYPES, 'application/pdf'];

// 個人の本人確認書類の種類
export type PersonalDocType = 'DL_OR_HISTORY' | 'RESIDENCE_CARD' | 'MY_NUMBER';

interface UseUploadIdentityReturn {
  // State
  identityFrontFile: File | null; // 法人で使用
  identityBackFile: File | null; // 旧個人UIで使用（互換のため暫定残置）
  // 個人: 最大4枚
  personalFiles: (File | null)[];
  personalPreviews: (string | null)[];
  isUploading: boolean;
  uploadStatus: string;
  uploadCompleted: boolean;
  uploadProgress: Record<string, number>;
  errors: Record<string, string | undefined>;
  isDragging: Record<string, boolean>;
  identityFrontPreview: string | null;
  identityBackPreview: string | null;
  uploadToken: string | null;
  applicantType: 'INDIVIDUAL' | 'CORPORATE' | null;
  corporateDocType: 'CORP_SEAL_CERT' | 'CORP_REGISTRY_CERT';
  personalDocType: PersonalDocType;
  documentGroupCompleted: boolean | null;

  // Handlers
  handleFileChange: (
    event: React.ChangeEvent<HTMLInputElement>,
    isDocument: boolean,
    setFile: React.Dispatch<React.SetStateAction<File | null>>,
    setPreview: React.Dispatch<React.SetStateAction<string | null>>,
    errorKey: string,
    pdfOnly?: boolean
  ) => void;
  handleDragEnter: (e: React.DragEvent, type: string) => void;
  handleDragLeave: (e: React.DragEvent, type: string) => void;
  handleDragOver: (e: React.DragEvent) => void;
  handleDrop: (
    e: React.DragEvent,
    type: string,
    isDocument: boolean,
    setFile: React.Dispatch<React.SetStateAction<File | null>>,
    setPreview: React.Dispatch<React.SetStateAction<string | null>>,
    pdfOnly?: boolean
  ) => void;
  handleBack: () => void;
  handleSubmit: () => Promise<void>;
  handleRemoveFile: (type: string) => void;

  // Utility functions
  setIdentityFrontFile: React.Dispatch<React.SetStateAction<File | null>>;
  setIdentityBackFile: React.Dispatch<React.SetStateAction<File | null>>;
  setIdentityFrontPreview: React.Dispatch<React.SetStateAction<string | null>>;
  setIdentityBackPreview: React.Dispatch<React.SetStateAction<string | null>>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string | undefined>>>;
  setCorporateDocType: React.Dispatch<React.SetStateAction<'CORP_SEAL_CERT' | 'CORP_REGISTRY_CERT'>>;
  setPersonalDocType: React.Dispatch<React.SetStateAction<PersonalDocType>>;
  setPersonalFiles: React.Dispatch<React.SetStateAction<(File | null)[]>>;
  setPersonalPreviews: React.Dispatch<React.SetStateAction<(string | null)[]>>;
}

export const useUploadIdentity = (): UseUploadIdentityReturn => {
  const [identityFrontFile, setIdentityFrontFile] = useState<File | null>(null);
  const [identityBackFile, setIdentityBackFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<string>('');
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [errors, setErrors] = useState<Record<string, string | undefined>>({});
  const [isDragging, setIsDragging] = useState<Record<string, boolean>>({});
  const [personalFiles, setPersonalFiles] = useState<(File | null)[]>([null, null, null, null]);
  const [personalPreviews, setPersonalPreviews] = useState<(string | null)[]>([null, null, null, null]);
  const [identityFrontPreview, setIdentityFrontPreview] = useState<string | null>(null);
  const [identityBackPreview, setIdentityBackPreview] = useState<string | null>(null);
  const [uploadCompleted, setUploadCompleted] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const uploadToken = searchParams.get('uploadToken');
  const applicantTypeParam = searchParams.get('applicantType');
  const applicantType: 'INDIVIDUAL' | 'CORPORATE' | null =
    applicantTypeParam === 'INDIVIDUAL' ? 'INDIVIDUAL' : applicantTypeParam === 'CORPORATE' ? 'CORPORATE' : null;
  const [resolvedApplicantType, setResolvedApplicantType] = useState<'INDIVIDUAL' | 'CORPORATE' | null>(applicantType);
  const effectiveApplicantType = resolvedApplicantType ?? applicantType;
  const [corporateDocType, setCorporateDocType] = useState<'CORP_SEAL_CERT' | 'CORP_REGISTRY_CERT'>('CORP_SEAL_CERT');
  const [personalDocType, setPersonalDocType] = useState<PersonalDocType>('MY_NUMBER');
  const [documentGroupCompleted, setDocumentGroupCompleted] = useState<boolean | null>(null);

  useEffect(() => {
    (async () => {
      if (uploadToken && documentGroupCompleted === null) {
        try {
          const res = await getApplicantTypeByUploadToken({ uploadToken });
          const t = res.applicantType === 2 ? 'CORPORATE' : res.applicantType === 1 ? 'INDIVIDUAL' : null;
          if (!resolvedApplicantType) {
            setResolvedApplicantType(t);
          }
          setDocumentGroupCompleted(res.isCompleted);

          // 既存の書類を取得
          const uploadedDocs = await getUploadedDocuments({ uploadToken });
          if (uploadedDocs.documents && uploadedDocs.documents.length > 0) {
            // 個人の場合、personalIndexに基づいてプレビューを設定
            if (t === 'INDIVIDUAL') {
              const newPreviews = [...personalPreviews];
              uploadedDocs.documents.forEach((doc) => {
                if (doc.personalIndex !== undefined && doc.personalIndex !== null) {
                  const idx = Number(doc.personalIndex) - 1; // personalIndex(1-4) -> 配列インデックス(0-3)
                  if (idx >= 0 && idx < newPreviews.length) {
                    newPreviews[idx] = doc.imageUrl;
                  }
                }
              });
              setPersonalPreviews(newPreviews);
            } else if (t === 'CORPORATE') {
              // 法人の場合、最初の書類をプレビューとして設定
              if (uploadedDocs.documents[0]) {
                setIdentityFrontPreview(uploadedDocs.documents[0].imageUrl);
              }
            }
          }
        } catch (_e) {
          // 失敗時は何もしない（UIは個人/法人を選ぶ操作なしのため、バックエンドで再度型チェックされる）
        }
      }
    })();
  }, [uploadToken, documentGroupCompleted, resolvedApplicantType]);

  /**
   * ファイルをJPEGに変換する
   */
  const convertToJpeg = async (file: File): Promise<File> => {
    // PDFファイルはそのまま返す
    if (file.type === 'application/pdf') {
      return file;
    }

    // 既にJPEGの場合はそのまま返す
    if (file.type === 'image/jpeg' || file.type === 'image/jpg') {
      return file;
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Canvas context not available'));
            return;
          }
          ctx.drawImage(img, 0, 0);
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Failed to convert image'));
                return;
              }
              const jpegFile = new File([blob], file.name.replace(/\.[^/.]+$/, '.jpg'), {
                type: 'image/jpeg',
              });
              resolve(jpegFile);
            },
            'image/jpeg',
            0.95
          );
        };
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = e.target?.result as string;
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  };

  /**
   * ファイルの検証
   */
  const validateFile = useCallback((file: File, isDocument: boolean, pdfOnly: boolean = false): string | null => {
    // ファイルサイズチェック
    if (file.size > MAX_FILE_SIZE) {
      return `ファイルサイズが大きすぎます。10MB以下のファイルを選択してください。（現在: ${(file.size / 1024 / 1024).toFixed(1)}MB）`;
    }

    // 履歴事項証明書などPDF限定のケース（法人向け）
    if (pdfOnly && file.type !== 'application/pdf') {
      return 'この書類はPDFファイルのみアップロード可能です';
    }

    // ファイルタイプチェック
    const allowedTypes = isDocument ? ALLOWED_DOCUMENT_TYPES : ALLOWED_IMAGE_TYPES;
    if (!allowedTypes.includes(file.type)) {
      return isDocument ? 'PNG、JPG、GIF、PDFファイルのみアップロード可能です' : 'PNG、JPG、GIFファイルのみアップロード可能です';
    }

    return null;
  }, []);

  /**
   * ファイルプレビューの生成
   */
  const generatePreview = (file: File, setPreview: React.Dispatch<React.SetStateAction<string | null>>) => {
    if (file.type === 'application/pdf') {
      setPreview('PDF');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  /**
   * ファイル選択ハンドラー
   */
  const handleFileSelect = useCallback(
    async (
      file: File,
      isDocument: boolean,
      setFile: React.Dispatch<React.SetStateAction<File | null>>,
      setPreview: React.Dispatch<React.SetStateAction<string | null>>,
      errorKey: string,
      pdfOnly: boolean = false
    ) => {
      // 既存のエラーをクリア
      setErrors((prev) => ({ ...prev, [errorKey]: undefined }));

      // ファイル検証
      const error = validateFile(file, isDocument, pdfOnly);
      if (error) {
        setErrors((prev) => ({ ...prev, [errorKey]: error }));
        return;
      }

      try {
        // 画像の場合はJPEGに変換
        const processedFile = await convertToJpeg(file);
        setFile(processedFile);
        generatePreview(processedFile, setPreview);
      } catch (_err) {
        setErrors((prev) => ({ ...prev, [errorKey]: '画像の処理中にエラーが発生しました' }));
      }
    },
    [validateFile]
  );

  const handleFileChange = useCallback(
    (
      event: React.ChangeEvent<HTMLInputElement>,
      isDocument: boolean,
      setFile: React.Dispatch<React.SetStateAction<File | null>>,
      setPreview: React.Dispatch<React.SetStateAction<string | null>>,
      errorKey: string,
      pdfOnly: boolean = false
    ) => {
      if (event.target.files && event.target.files[0]) {
        handleFileSelect(event.target.files[0], isDocument, setFile, setPreview, errorKey, pdfOnly);
      } else {
        setFile(null);
        setPreview(null);
      }
    },
    [handleFileSelect]
  );

  /**
   * ドラッグ&ドロップのハンドラー
   */
  const handleDragEnter = useCallback((e: React.DragEvent, type: string) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging((prev) => ({ ...prev, [type]: true }));
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent, type: string) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging((prev) => ({ ...prev, [type]: false }));
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (
      e: React.DragEvent,
      type: string,
      isDocument: boolean,
      setFile: React.Dispatch<React.SetStateAction<File | null>>,
      setPreview: React.Dispatch<React.SetStateAction<string | null>>,
      pdfOnly: boolean = false
    ) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging((prev) => ({ ...prev, [type]: false }));

      const files = e.dataTransfer.files;
      if (files && files.length > 0) {
        handleFileSelect(files[0], isDocument, setFile, setPreview, type, pdfOnly);
      }
    },
    [handleFileSelect]
  );

  const handleBack = useCallback(() => {
    // アップロード中の場合は確認ダイアログを表示
    if (isUploading) {
      const confirmLeave = window.confirm('アップロード中です。本当にページを離れますか？');
      if (!confirmLeave) return;
    }
    router.back();
  }, [isUploading, router]);

  /**
   * ファイル削除ハンドラー
   */
  const handleRemoveFile = useCallback((type: string) => {
    if (type === 'identityFront') {
      setIdentityFrontFile(null);
      setIdentityFrontPreview(null);
    } else if (type === 'identityBack') {
      setIdentityBackFile(null);
      setIdentityBackPreview(null);
    } else if (type.startsWith('personal')) {
      const idx = Number(type.replace('personal', '')) - 1;
      if (idx >= 0 && idx < 4) {
        setPersonalFiles((prev) => {
          const next = [...prev];
          next[idx] = null;
          return next;
        });
        setPersonalPreviews((prev) => {
          const next = [...prev];
          next[idx] = null;
          return next;
        });
      }
    }

    // エラーもクリア
    setErrors((prev) => ({ ...prev, [type]: undefined }));
  }, []);

  /**
   * ファイルをS3にアップロードする（リトライ機能付き）
   */
  const uploadFileToS3WithRetry = useCallback(
    async (
      file: File,
      documentType: DocumentType,
      progressKey: 'identityFront' | 'identityBack',
      maxRetries: number = 3
    ): Promise<boolean> => {
      let retries = 0;

      while (retries <= maxRetries) {
        try {
          if (!uploadToken) {
            throw new Error('アップロードトークンが見つかりません');
          }

          // アップロードURL取得
          const uploadUrlResponse = await getCorporateUploadUrl({
            fileType: file.type,
            uploadToken,
            documentType,
          });

          // XMLHttpRequestを使用してプログレスを追跡
          const xhr = new XMLHttpRequest();

          return await new Promise<boolean>((resolve, reject) => {
            // プログレスイベントの設定
            xhr.upload.addEventListener('progress', (event) => {
              if (event.lengthComputable) {
                const percentComplete = Math.round((event.loaded / event.total) * 100);
                setUploadProgress((prev) => ({ ...prev, [progressKey]: percentComplete }));
              }
            });

            // 完了イベント
            xhr.addEventListener('load', () => {
              if (xhr.status >= 200 && xhr.status < 300) {
                setUploadProgress((prev) => ({ ...prev, [progressKey]: 100 }));
                resolve(true);
              } else {
                reject(new Error(`アップロードに失敗しました (${xhr.status})`));
              }
            });

            // エラーイベント
            xhr.addEventListener('error', () => {
              reject(new Error('ネットワークエラーが発生しました'));
            });

            // タイムアウトイベント
            xhr.addEventListener('timeout', () => {
              reject(new Error('アップロードがタイムアウトしました'));
            });

            // リクエストの設定と送信
            xhr.open('PUT', uploadUrlResponse.url);
            xhr.setRequestHeader('Content-Type', file.type);
            xhr.timeout = 60000; // 60秒のタイムアウト
            xhr.send(file);
          });
        } catch (error) {
          if (retries === maxRetries) {
            throw error;
          }

          // リトライ前に少し待機（指数バックオフ）
          await new Promise((resolve) => setTimeout(resolve, Math.pow(2, retries) * 1000));
          retries++;
          setUploadStatus(`アップロードに失敗しました。リトライ中... (${retries}/${maxRetries})`);
        }
      }

      return false;
    },
    [uploadToken]
  );
  /**
   * 個人用: personalIndex(1〜4)でS3にアップロード（リトライ機能付き）
   */
  const uploadPersonalFileToS3WithRetry = useCallback(
    async (file: File, index: number, maxRetries: number = 3): Promise<boolean> => {
      let retries = 0;
      const progressKey = `personal${index + 1}`;

      while (retries <= maxRetries) {
        try {
          if (!uploadToken) {
            throw new Error('アップロードトークンが見つかりません');
          }

          const uploadUrlResponse = await getPersonalUploadUrl({
            fileType: file.type,
            uploadToken,
            personalIndex: index + 1,
          });

          const xhr = new XMLHttpRequest();

          return await new Promise<boolean>((resolve, reject) => {
            xhr.upload.addEventListener('progress', (event) => {
              if (event.lengthComputable) {
                const percentComplete = Math.round((event.loaded / event.total) * 100);
                setUploadProgress((prev) => ({ ...prev, [progressKey]: percentComplete }));
              }
            });

            xhr.addEventListener('load', () => {
              if (xhr.status >= 200 && xhr.status < 300) {
                setUploadProgress((prev) => ({ ...prev, [progressKey]: 100 }));
                resolve(true);
              } else {
                reject(new Error(`アップロードに失敗しました (${xhr.status})`));
              }
            });

            xhr.addEventListener('error', () => reject(new Error('ネットワークエラーが発生しました')));
            xhr.addEventListener('timeout', () => reject(new Error('アップロードがタイムアウトしました')));

            xhr.open('PUT', uploadUrlResponse.url);
            xhr.setRequestHeader('Content-Type', file.type);
            xhr.timeout = 60000;
            xhr.send(file);
          });
        } catch (error) {
          if (retries === maxRetries) throw error;
          await new Promise((resolve) => setTimeout(resolve, Math.pow(2, retries) * 1000));
          retries++;
          setUploadStatus(`アップロードに失敗しました。リトライ中... (${retries}/${maxRetries})`);
        }
      }

      return false;
    },
    [uploadToken]
  );

  const handleSubmit = useCallback(async () => {
    if (!uploadToken) {
      setUploadStatus('必要なファイルまたはトークンが不足しています');
      return;
    }

    // 必須ファイルチェック（既存アップロード済みを含めて1点以上あればOK）
    if (effectiveApplicantType === 'CORPORATE') {
      const hasExisting = Boolean(identityFrontPreview);
      if (!identityFrontFile && !hasExisting) {
        setUploadStatus('必要なファイルが不足しています');
        setErrors((prev) => ({ ...prev, identityFront: '書類を選択してください' }));
        return;
      }
    } else {
      const hasFirstGroup = Boolean(personalFiles[0] || personalFiles[1] || personalPreviews[0] || personalPreviews[1]); // 1枚目 or 2枚目
      const hasSecondGroup = Boolean(personalFiles[2] || personalFiles[3] || personalPreviews[2] || personalPreviews[3]); // 3枚目 or 4枚目
      if (!hasFirstGroup || !hasSecondGroup) {
        setUploadStatus('必要なファイルが不足しています');
        setErrors((prev) => ({
          ...prev,
          ...(!hasFirstGroup ? { personal1: '本人確認書類①（A群）のいずれか1枚は必須です' } : {}),
          ...(!hasSecondGroup ? { personal3: '本人確認書類②（A群 または B群）のいずれか1枚は必須です' } : {}),
        }));
        return;
      }
    }

    setIsUploading(true);
    setUploadStatus('アップロード準備中...');
    setUploadProgress({ identityFront: 0, identityBack: 0 });
    setUploadCompleted(false);
    setErrors({});

    try {
      if (effectiveApplicantType === 'CORPORATE') {
        // 法人: 選択された書類種別で1ファイルのみ
        const docType = corporateDocType === 'CORP_REGISTRY_CERT' ? DocumentType.CORP_REGISTRY_CERT : DocumentType.CORP_SEAL_CERT;
        const file = identityFrontFile;
        if (!file) {
          throw new Error('身分確認書類が見つかりません');
        }
        const ok = await uploadFileToS3WithRetry(file, docType, 'identityFront');
        if (!ok) throw new Error('身分確認書類のアップロードに失敗しました');
      } else {
        // 個人: 最大4枚（1〜4）
        for (let i = 0; i < 4; i++) {
          const file = personalFiles[i];
          if (!file) continue;
          setUploadStatus(`本人確認書類(${i + 1})をアップロード中...`);
          const ok = await uploadPersonalFileToS3WithRetry(file, i);
          if (!ok) throw new Error(`本人確認書類(${i + 1})のアップロードに失敗しました`);
        }
      }

      // アップロード完了を通知
      setUploadStatus('アップロード処理を完了中...');
      await completeDocumentUpload({ uploadToken });

      setUploadCompleted(true);
      setUploadStatus('アップロードが完了しました！');

      // 成功後、本人確認書類提出完了ページに遷移
      setTimeout(() => {
        router.push('/join/application/upload_identity/sent');
      }, 1500);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'アップロードに失敗しました';
      setUploadStatus(errorMessage);
      setErrors((prev) => ({ ...prev, identityFront: prev.identityFront ?? errorMessage }));
    } finally {
      setIsUploading(false);
    }
  }, [
    uploadToken,
    router,
    uploadFileToS3WithRetry,
    uploadPersonalFileToS3WithRetry,
    effectiveApplicantType,
    corporateDocType,
    identityFrontFile,
    identityBackFile,
    personalFiles,
    personalPreviews,
    identityFrontPreview,
  ]);

  return {
    // State
    identityFrontFile,
    identityBackFile,
    personalFiles,
    personalPreviews,
    isUploading,
    uploadStatus,
    uploadCompleted,
    uploadProgress,
    errors,
    isDragging,
    identityFrontPreview,
    identityBackPreview,
    uploadToken,
    applicantType: effectiveApplicantType,
    corporateDocType,
    personalDocType,
    documentGroupCompleted,

    // Handlers
    handleFileChange,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
    handleBack,
    handleSubmit,
    handleRemoveFile,

    // Utility functions
    setIdentityFrontFile,
    setIdentityBackFile,
    setIdentityFrontPreview,
    setIdentityBackPreview,
    setErrors,
    setCorporateDocType,
    setPersonalDocType,
    setPersonalFiles,
    setPersonalPreviews,
  };
};
