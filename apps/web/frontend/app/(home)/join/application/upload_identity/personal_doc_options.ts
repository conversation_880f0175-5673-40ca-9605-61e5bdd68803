import type { PersonalDocType } from './useUploadIdentity';

export type PersonalDocOption = {
  value: PersonalDocType;
  label: string;
  needsBack: boolean;
};

export const PERSONAL_DOC_OPTIONS: ReadonlyArray<PersonalDocOption> = [
  { value: 'MY_NUMBER', label: 'マイナンバーカード', needsBack: false },
  { value: 'DL_OR_HISTORY', label: '運転免許証 / 運転経歴証明書', needsBack: true },
  { value: 'RESIDENCE_CARD', label: '在留カード / 特別永住者証明書', needsBack: true },
] as const;

export const PERSONAL_DOC_OPTION_MAP: Readonly<Record<PersonalDocType, PersonalDocOption>> = Object.freeze(
  Object.fromEntries(PERSONAL_DOC_OPTIONS.map((o) => [o.value, o])) as Record<PersonalDocType, PersonalDocOption>
);

export const getPersonalDocOption = (value: PersonalDocType): PersonalDocOption => PERSONAL_DOC_OPTION_MAP[value];
