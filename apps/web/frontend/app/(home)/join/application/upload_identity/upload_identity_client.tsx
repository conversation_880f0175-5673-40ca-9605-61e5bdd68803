'use client';

import React, { Suspense, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useUploadIdentity } from './useUploadIdentity';
import Logo from './components/Logo';
import { CorporateDocTypeSelector } from './components/CorporateDocTypeSelector';
import { IdentityDropZone } from './components/IdentityDropZone';
import { SubmitButton } from './components/SubmitButton';

function UploadIdentityContent() {
  const router = useRouter();
  const {
    identityFrontFile,
    setIdentityFrontFile,
    identityFrontPreview,
    setIdentityFrontPreview,
    isUploading,
    errors,
    handleFileChange,
    handleDragEnter,
    handleDragLeave,
    handleDragOver,
    handleDrop,
    handleSubmit,
    handleRemoveFile,
    uploadToken,
    applicantType,
    corporateDocType,
    setCorporateDocType,
    personalFiles,
    personalPreviews,
    setPersonalFiles,
    setPersonalPreviews,
    documentGroupCompleted,
  } = useUploadIdentity();

  const searchParams = useSearchParams();
  const isRemand = searchParams.get('remand') === '1';

  const pdfOnly = applicantType === 'CORPORATE' && corporateDocType === 'CORP_REGISTRY_CERT';

  // personalFiles/personalPreviews の特定インデックスを単一ファイルsetterインターフェースに合わせるラッパー
  const makeSetPersonalFile =
    (idx: number): React.Dispatch<React.SetStateAction<File | null>> =>
    (value) => {
      const nextFile = typeof value === 'function' ? (value as (prev: File | null) => File | null)(personalFiles[idx]) : value;
      setPersonalFiles((prev) => {
        const next = [...prev];
        next[idx] = nextFile;
        return next;
      });
    };
  const makeSetPersonalPreview =
    (idx: number): React.Dispatch<React.SetStateAction<string | null>> =>
    (value) => {
      const nextPreview = typeof value === 'function' ? (value as (prev: string | null) => string | null)(personalPreviews[idx]) : value;
      setPersonalPreviews((prev) => {
        const next = [...prev];
        next[idx] = nextPreview;
        return next;
      });
    };

  // アップロード完了済みの場合は完了画面に遷移
  useEffect(() => {
    if (documentGroupCompleted === true && !isRemand) {
      router.push('/join/application/upload_identity/sent');
    }
  }, [documentGroupCompleted, isRemand, router]);

  // uploadTokenがない場合のエラー表示
  if (!uploadToken) {
    return (
      <main className="min-h-screen bg-hami-bg-main relative">
        {/* 上部ロゴ（全画面共通） */}
        <div className="absolute top-10 left-1/2 transform -translate-x-1/2 z-10">
          <Logo size="large" variant="horizontal" />
        </div>

        {/* エラー表示 */}
        <div className="flex items-center justify-center min-h-screen pt-32">
          <div className="max-w-md mx-auto bg-hami-bg-white p-8 rounded-xl shadow-xl text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">エラー</h1>
            <p className="text-hami-text-secondary mb-6">アップロードトークンが必要です</p>
            <button
              onClick={() => window.history.back()}
              className="px-6 py-2 bg-hami-primary-dark text-hami-primary-light rounded-md hover:bg-hami-primary-dark-hover"
            >
              前のページに戻る
            </button>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-hami-bg-main relative">
      {/* 上部ロゴ（全画面共通） */}
      <div className="absolute top-10 left-1/2 transform -translate-x-1/2 z-10">
        <Logo size="large" variant="horizontal" />
      </div>

      {/* デスクトップレイアウト */}
      <div className="hidden lg:flex h-screen">
        {/* 左側：ロゴエリア（固定） */}
        <div className="flex-1 flex items-center justify-center bg-hami-bg-white h-full">
          <Logo size="large" />
        </div>

        {/* 右側：コンテンツエリア（スクロール可能） */}
        <div className="flex-1 h-full overflow-y-auto">
          <div className="flex items-start justify-center py-20 pt-32 min-h-full">
            <div className="w-full max-w-lg px-4">
              <div className="space-y-8">
                {/* Page Title */}
                <div className="text-center space-y-4">
                  <h1 data-testid="page-title" className="text-4xl font-normal text-hami-text-primary mb-4" style={{ fontFamily: 'serif' }}>
                    本人確認書類の提出
                  </h1>
                </div>

                {applicantType !== 'CORPORATE' && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded text-sm text-blue-900">
                    <div className="space-y-2">
                      <div>提出書類：本人確認書類(A群) 2点、または本人確認書類(A群) 1点 + 補完書類(B群) 1点</div>
                      <div>
                        <p className="font-medium text-sm mb-1">本人確認書類(A群)</p>
                        <ul className="list-disc pl-4 space-y-1 text-xs">
                          <li>運転免許証（運転経歴証明書を含む）(表裏)</li>
                          <li>個人番号カード（マイナンバーカード）(表面のみ)</li>
                          <li>旅券（パスポート）</li>
                          <li>特別永住者証明書</li>
                          <li>身体障害者手帳、精神障害者保健福祉手帳、療育手帳</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium text-sm mb-1">補完書類(B群)</p>
                        <ul className="list-disc pl-4 space-y-1 text-xs">
                          <li>各種健康保険証（国民健康保険、健康保険、共済組合など）</li>
                          <li>年金手帳</li>
                          <li>住民票の写し（発行から6ヶ月以内のもの）</li>
                          <li>印鑑登録証明書（発行から6ヶ月以内のもの）</li>
                        </ul>
                      </div>
                      <div>
                        <p className="font-medium text-sm mb-1">注意事項</p>
                        <ul className="list-disc pl-4 space-y-1 text-xs">
                          <li>氏名・生年月日・現住所が鮮明に写っていることを確認してください</li>
                          <li>加工・トリミング禁止です</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                {(errors.identityFront ||
                  errors.identityBack ||
                  errors.personal1 ||
                  errors.personal2 ||
                  errors.personal3 ||
                  errors.personal4) && (
                  <div className="mb-6 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    <p>
                      {errors.identityFront ||
                        errors.identityBack ||
                        errors.personal1 ||
                        errors.personal2 ||
                        errors.personal3 ||
                        errors.personal4}
                    </p>
                  </div>
                )}

                {/* Upload Section */}
                <div className="space-y-6">
                  {/* Identity Document Upload */}
                  <div className="space-y-3" data-testid="identity-document-section">
                    <CorporateDocTypeSelector
                      applicantType={applicantType}
                      corporateDocType={corporateDocType}
                      setCorporateDocType={setCorporateDocType}
                      isUploading={isUploading}
                    />

                    {applicantType === 'CORPORATE' ? (
                      <>
                        <label className="block text-sm font-bold text-hami-text-secondary">本人確認書類</label>
                        <IdentityDropZone
                          inputId="identity-file-upload"
                          identityPreview={identityFrontPreview}
                          isUploading={isUploading}
                          onRemove={() => handleRemoveFile('identityFront')}
                          onDragEnter={(e) => handleDragEnter(e, 'identityFront')}
                          onDragLeave={(e) => handleDragLeave(e, 'identityFront')}
                          onDragOver={handleDragOver}
                          onDrop={(e) => handleDrop(e, 'identityFront', true, setIdentityFrontFile, setIdentityFrontPreview, pdfOnly)}
                          onChange={(e) =>
                            handleFileChange(e, true, setIdentityFrontFile, setIdentityFrontPreview, 'identityFront', pdfOnly)
                          }
                          accept={pdfOnly ? 'application/pdf' : 'image/*,.pdf'}
                          variant="desktop"
                        />
                      </>
                    ) : (
                      <>
                        <label className="block text-sm font-bold text-hami-text-secondary">本人確認書類①（A群）</label>
                        <div className="space-y-4">
                          {/* 1枚目 */}
                          <div key={`personal-1`}>
                            <IdentityDropZone
                              inputId={`personal-file-upload-1`}
                              identityPreview={personalPreviews[0]}
                              isUploading={isUploading}
                              onRemove={() => handleRemoveFile(`personal1`)}
                              onDragEnter={(e) => handleDragEnter(e, `personal1`)}
                              onDragLeave={(e) => handleDragLeave(e, `personal1`)}
                              onDragOver={handleDragOver}
                              onDrop={(e) => handleDrop(e, `personal1`, true, makeSetPersonalFile(0), makeSetPersonalPreview(0))}
                              onChange={(e) => handleFileChange(e, true, makeSetPersonalFile(0), makeSetPersonalPreview(0), `personal1`)}
                              accept="image/*,.pdf"
                              variant="desktop"
                            />
                          </div>
                          {/* 2枚目: 1枚目が入ったら表示（または2枚目に既にファイルがある場合も表示）*/}
                          {(personalFiles[0] || personalFiles[1] || personalPreviews[0] || personalPreviews[1]) && (
                            <div key={`personal-2`}>
                              <IdentityDropZone
                                inputId={`personal-file-upload-2`}
                                identityPreview={personalPreviews[1]}
                                isUploading={isUploading}
                                onRemove={() => handleRemoveFile(`personal2`)}
                                onDragEnter={(e) => handleDragEnter(e, `personal2`)}
                                onDragLeave={(e) => handleDragLeave(e, `personal2`)}
                                onDragOver={handleDragOver}
                                onDrop={(e) => handleDrop(e, `personal2`, true, makeSetPersonalFile(1), makeSetPersonalPreview(1))}
                                onChange={(e) => handleFileChange(e, true, makeSetPersonalFile(1), makeSetPersonalPreview(1), `personal2`)}
                                accept="image/*,.pdf"
                                variant="desktop"
                              />
                            </div>
                          )}
                        </div>
                        <label className="block text-sm font-bold text-hami-text-secondary">本人確認書類②（A群 または B群）</label>
                        <div className="space-y-4">
                          {/* 3枚目 */}
                          <div key={`personal-3`}>
                            <IdentityDropZone
                              inputId={`personal-file-upload-3`}
                              identityPreview={personalPreviews[2]}
                              isUploading={isUploading}
                              onRemove={() => handleRemoveFile(`personal3`)}
                              onDragEnter={(e) => handleDragEnter(e, `personal3`)}
                              onDragLeave={(e) => handleDragLeave(e, `personal3`)}
                              onDragOver={handleDragOver}
                              onDrop={(e) => handleDrop(e, `personal3`, true, makeSetPersonalFile(2), makeSetPersonalPreview(2))}
                              onChange={(e) => handleFileChange(e, true, makeSetPersonalFile(2), makeSetPersonalPreview(2), `personal3`)}
                              accept="image/*,.pdf"
                              variant="desktop"
                            />
                          </div>
                          {/* 4枚目: 3枚目が入ったら表示（または4枚目に既にファイルがある場合も表示）*/}
                          {(personalFiles[2] || personalFiles[3] || personalPreviews[2] || personalPreviews[3]) && (
                            <div key={`personal-4`}>
                              <IdentityDropZone
                                inputId={`personal-file-upload-4`}
                                identityPreview={personalPreviews[3]}
                                isUploading={isUploading}
                                onRemove={() => handleRemoveFile(`personal4`)}
                                onDragEnter={(e) => handleDragEnter(e, `personal4`)}
                                onDragLeave={(e) => handleDragLeave(e, `personal4`)}
                                onDragOver={handleDragOver}
                                onDrop={(e) => handleDrop(e, `personal4`, true, makeSetPersonalFile(3), makeSetPersonalPreview(3))}
                                onChange={(e) => handleFileChange(e, true, makeSetPersonalFile(3), makeSetPersonalPreview(3), `personal4`)}
                                accept="image/*,.pdf"
                                variant="desktop"
                              />
                            </div>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Accordion */}
                {/* <div className="border border-gray-200 rounded-lg">
                <button
                  type="button"
                  className="w-full px-4 py-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors flex items-center justify-between"
                  onClick={toggleAccordion}
                >
                  <span className="text-sm font-medium text-hami-text-secondary">本人確認書類として利用可能なもの</span>
                  <div className={`transform transition-transform ${isAccordionOpen ? 'rotate-180' : ''}`}>
                    <ChevronDownIcon />
                  </div>
                </button>

                {isAccordionOpen && (
                  <div className="px-4 py-3 space-y-4 text-sm text-hami-text-secondary">
                    <div>
                      <h4 className="font-medium text-hami-text-primary mb-2">運転免許証</h4>
                      <p>有効期限内のもの。裏面に変更事項がある場合は裏面もアップロードしてください。</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-hami-text-primary mb-2">パスポート</h4>
                      <p>日本国発行のもの。顔写真・氏名・生年月日が確認できるページと住所記載ページをアップロードしてください。</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-hami-text-primary mb-2">マイナンバーカード</h4>
                      <p>有効期限内のもの。表面のみアップロードしてください。</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-hami-text-primary mb-2">在留カード・特別永住者証明書</h4>
                      <p>有効期限内で顔写真付きのもの。裏面に変更事項がある場合は裏面もアップロードしてください。</p>
                    </div>
                  </div>
                )}
              </div> */}

                {/* Submit Button */}
                <div className="pt-4">
                  <div className="w-full h-px bg-hami-border-separator mb-8"></div>
                  <SubmitButton
                    onClick={handleSubmit}
                    disabled={
                      applicantType === 'CORPORATE'
                        ? !(identityFrontFile || identityFrontPreview) || isUploading
                        : !(
                            (personalFiles[0] || personalFiles[1] || personalPreviews[0] || personalPreviews[1]) &&
                            (personalFiles[2] || personalFiles[3] || personalPreviews[2] || personalPreviews[3])
                          ) || isUploading
                    }
                    isUploading={isUploading}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* モバイルレイアウト */}

      <div className="lg:hidden min-h-screen flex flex-col">
        {/* コンテンツエリア */}
        <div className="flex-1 flex items-center justify-center px-5 py-10 pt-32">
          <div className="w-full max-w-sm">
            <div className="text-center mb-10">
              <h1 data-testid="page-title" className="text-3xl font-normal text-hami-text-primary mb-4" style={{ fontFamily: 'serif' }}>
                本人確認書類の提出
              </h1>
            </div>

            {applicantType !== 'CORPORATE' && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded text-sm text-blue-900">
                <div className="space-y-2">
                  <div>提出書類：本人確認書類(A群) 2点、または本人確認書類(A群) 1点 + 補完書類(B群) 1点</div>
                  <div>
                    <p className="font-medium text-sm mb-1">本人確認書類(A群)</p>
                    <ul className="list-disc pl-4 space-y-1 text-xs">
                      <li>運転免許証（運転経歴証明書を含む）(表裏)</li>
                      <li>個人番号カード（マイナンバーカード）(表面のみ)</li>
                      <li>旅券（パスポート）</li>
                      <li>特別永住者証明書</li>
                      <li>身体障害者手帳、精神障害者保健福祉手帳、療育手帳</li>
                    </ul>
                  </div>
                  <div>
                    <p className="font-medium text-sm mb-1">補完書類(B群)</p>
                    <ul className="list-disc pl-4 space-y-1 text-xs">
                      <li>各種健康保険証（国民健康保険、健康保険、共済組合など）</li>
                      <li>年金手帳</li>
                      <li>住民票の写し（発行から6ヶ月以内のもの）</li>
                      <li>印鑑登録証明書（発行から6ヶ月以内のもの）</li>
                    </ul>
                  </div>
                  <div>
                    <p className="font-medium text-xs mb-1">注意事項</p>
                    <ul className="list-disc pl-4 space-y-1 text-xs">
                      <li>氏名・生年月日・現住所が鮮明に写っていることを確認してください</li>
                      <li>加工・トリミング禁止です</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {(errors.identityFront || errors.identityBack) && (
              <div className="mb-6 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                <p>{errors.identityFront || errors.identityBack}</p>
              </div>
            )}

            {/* モバイル用アップロードセクション */}
            <div className="space-y-6">
              {/* Identity Document Upload */}
              <div className="space-y-3" data-testid="identity-document-section">
                <CorporateDocTypeSelector
                  applicantType={applicantType}
                  corporateDocType={corporateDocType}
                  setCorporateDocType={setCorporateDocType}
                  isUploading={isUploading}
                  nameSuffix="mobile"
                />

                {applicantType === 'CORPORATE' ? (
                  <>
                    <label className="block text-sm font-bold text-hami-text-secondary">本人確認書類</label>
                    <IdentityDropZone
                      inputId="identity-file-upload-mobile"
                      identityPreview={identityFrontPreview}
                      isUploading={isUploading}
                      onRemove={() => handleRemoveFile('identityFront')}
                      onDragEnter={(e) => handleDragEnter(e, 'identityFront')}
                      onDragLeave={(e) => handleDragLeave(e, 'identityFront')}
                      onDragOver={handleDragOver}
                      onDrop={(e) => handleDrop(e, 'identityFront', true, setIdentityFrontFile, setIdentityFrontPreview, pdfOnly)}
                      onChange={(e) => handleFileChange(e, true, setIdentityFrontFile, setIdentityFrontPreview, 'identityFront', pdfOnly)}
                      accept={pdfOnly ? 'application/pdf' : 'image/*,.pdf'}
                      variant="mobile"
                    />
                  </>
                ) : (
                  <>
                    <label className="block text-sm font-bold text-hami-text-secondary">本人確認書類①（A群）</label>
                    <div className="space-y-4">
                      {/* 1枚目 */}
                      <div key={`personal-mobile-1`}>
                        <IdentityDropZone
                          inputId={`personal-file-upload-mobile-1`}
                          identityPreview={personalPreviews[0]}
                          isUploading={isUploading}
                          onRemove={() => handleRemoveFile(`personal1`)}
                          onDragEnter={(e) => handleDragEnter(e, `personal1`)}
                          onDragLeave={(e) => handleDragLeave(e, `personal1`)}
                          onDragOver={handleDragOver}
                          onDrop={(e) => handleDrop(e, `personal1`, true, makeSetPersonalFile(0), makeSetPersonalPreview(0))}
                          onChange={(e) => handleFileChange(e, true, makeSetPersonalFile(0), makeSetPersonalPreview(0), `personal1`)}
                          accept="image/*,.pdf"
                          variant="mobile"
                        />
                      </div>
                      {/* 2枚目: 1枚目が入ったら表示（または2枚目に既にファイルがある場合も表示）*/}
                      {(personalFiles[0] || personalFiles[1] || personalPreviews[0] || personalPreviews[1]) && (
                        <div key={`personal-mobile-2`}>
                          <IdentityDropZone
                            inputId={`personal-file-upload-mobile-2`}
                            identityPreview={personalPreviews[1]}
                            isUploading={isUploading}
                            onRemove={() => handleRemoveFile(`personal2`)}
                            onDragEnter={(e) => handleDragEnter(e, `personal2`)}
                            onDragLeave={(e) => handleDragLeave(e, `personal2`)}
                            onDragOver={handleDragOver}
                            onDrop={(e) => handleDrop(e, `personal2`, true, makeSetPersonalFile(1), makeSetPersonalPreview(1))}
                            onChange={(e) => handleFileChange(e, true, makeSetPersonalFile(1), makeSetPersonalPreview(1), `personal2`)}
                            accept="image/*,.pdf"
                            variant="mobile"
                          />
                        </div>
                      )}
                    </div>
                    <label className="block text-sm font-bold text-hami-text-secondary">本人確認書類②（A群 または B群）</label>
                    <div className="space-y-4">
                      {/* 3枚目 */}
                      <div key={`personal-mobile-3`}>
                        <IdentityDropZone
                          inputId={`personal-file-upload-mobile-3`}
                          identityPreview={personalPreviews[2]}
                          isUploading={isUploading}
                          onRemove={() => handleRemoveFile(`personal3`)}
                          onDragEnter={(e) => handleDragEnter(e, `personal3`)}
                          onDragLeave={(e) => handleDragLeave(e, `personal3`)}
                          onDragOver={handleDragOver}
                          onDrop={(e) => handleDrop(e, `personal3`, true, makeSetPersonalFile(2), makeSetPersonalPreview(2))}
                          onChange={(e) => handleFileChange(e, true, makeSetPersonalFile(2), makeSetPersonalPreview(2), `personal3`)}
                          accept="image/*,.pdf"
                          variant="mobile"
                        />
                      </div>
                      {/* 4枚目: 3枚目が入ったら表示（または4枚目に既にファイルがある場合も表示）*/}
                      {(personalFiles[2] || personalFiles[3] || personalPreviews[2] || personalPreviews[3]) && (
                        <div key={`personal-mobile-4`}>
                          <IdentityDropZone
                            inputId={`personal-file-upload-mobile-4`}
                            identityPreview={personalPreviews[3]}
                            isUploading={isUploading}
                            onRemove={() => handleRemoveFile(`personal4`)}
                            onDragEnter={(e) => handleDragEnter(e, `personal4`)}
                            onDragLeave={(e) => handleDragLeave(e, `personal4`)}
                            onDragOver={handleDragOver}
                            onDrop={(e) => handleDrop(e, `personal4`, true, makeSetPersonalFile(3), makeSetPersonalPreview(3))}
                            onChange={(e) => handleFileChange(e, true, makeSetPersonalFile(3), makeSetPersonalPreview(3), `personal4`)}
                            accept="image/*,.pdf"
                            variant="mobile"
                          />
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-6">
              <div className="w-full h-px bg-hami-border-separator mb-8"></div>
              <SubmitButton
                onClick={handleSubmit}
                disabled={
                  applicantType === 'CORPORATE'
                    ? !(identityFrontFile || identityFrontPreview) || isUploading
                    : !(
                        (personalFiles[0] || personalFiles[1] || personalPreviews[0] || personalPreviews[1]) &&
                        (personalFiles[2] || personalFiles[3] || personalPreviews[2] || personalPreviews[3])
                      ) || isUploading
                }
                isUploading={isUploading}
              />
            </div>
          </div>
        </div>

        {/* フッターロゴ */}
        <div className="bg-hami-bg-white px-4 py-10 flex justify-center">
          <Logo size="large" variant="horizontal" />
        </div>
      </div>
    </main>
  );
}

export default function UploadIdentityClient() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">読み込み中...</p>
          </div>
        </div>
      }
    >
      <UploadIdentityContent />
    </Suspense>
  );
}
