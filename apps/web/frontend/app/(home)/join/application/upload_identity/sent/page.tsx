import Link from 'next/link';
import { Logo } from '@web/components/logo';

export default function Page() {
  return (
    <main className="min-h-screen bg-hami-bg-main relative">
      {/* 上部ロゴ(全画面共通) */}
      <div className="absolute top-10 left-1/2 transform -translate-x-1/2 z-10">
        <Logo size="large" variant="horizontal" />
      </div>

      {/* デスクトップレイアウト */}
      <div className="hidden lg:flex min-h-screen">
        <div className="flex-1 flex items-center justify-center bg-hami-bg-white py-20 pt-32">
          <Logo size="large" />
        </div>

        <div className="flex-1 flex items-center justify-center py-20 pt-32">
          <div className="w-full max-w-sm">
            <div className="text-center mb-12">
              <h1 className="text-4xl font-normal text-hami-text-primary mb-8" style={{ fontFamily: 'serif' }}>
                本人確認書類の提出完了
              </h1>
            </div>

            <div className="space-y-8">
              <div className="text-center space-y-4">
                <p className="text-hami-glyph-subtle font-bold">ご提出ありがとうございました</p>
              </div>

              <div className="text-hami-glyph-subtle space-y-1">
                <p>本人確認書類の提出を受け付けました。</p>
                <p>ただいま審査を開始いたします。結果はメールにてお知らせします。</p>
                <p>審査完了まで、しばらくお待ちください。</p>
              </div>

              <div className="pt-4">
                <Link
                  href="/"
                  className="w-full mx-auto block bg-hami-primary-dark text-hami-primary-light py-3 px-6 rounded font-bold hover:bg-hami-primary-dark-hover focus:outline-none focus:ring-2 focus:ring-hami-primary-dark focus:ring-offset-2 transition-colors text-center"
                >
                  トップページへ
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* モバイルレイアウト */}
      <div className="lg:hidden min-h-screen flex flex-col">
        <div className="flex-1 flex items-center justify-center px-5 py-10 pt-32">
          <div className="w-full max-w-sm">
            <div className="text-center mb-10">
              <h1 className="text-3xl font-normal text-hami-text-primary" style={{ fontFamily: 'serif' }}>
                本人確認書類の提出完了
              </h1>
            </div>

            <div className="space-y-6">
              <div className="text-center space-y-4">
                <p className="text-hami-glyph-subtle font-bold">ご提出ありがとうございました</p>
              </div>

              <div className="text-hami-glyph-subtle space-y-1">
                <p>本人確認書類の提出を受け付けました。</p>
                <p>ただいま審査を開始いたします。結果はメールにてお知らせします。</p>
                <p>審査完了まで、しばらくお待ちください。</p>
              </div>

              <div className="pt-4">
                <div className="w-full h-px bg-hami-border-separator mb-8"></div>
                <div className="flex flex-col space-y-4">
                  <Link
                    href="/join"
                    className="w-full mx-auto block bg-hami-primary-dark text-hami-primary-light py-3 px-6 rounded font-bold hover:bg-hami-primary-dark-hover focus:outline-none focus:ring-2 focus:ring-hami-primary-dark focus:ring-offset-2 transition-colors text-center"
                  >
                    トップページへ
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* フッターロゴ */}
        <div className="bg-hami-bg-white px-4 py-10 flex justify-center">
          <Logo size="medium" variant="square" />
        </div>
      </div>
    </main>
  );
}
