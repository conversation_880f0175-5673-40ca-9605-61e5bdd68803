'use client';

import React from 'react';
import type { PersonalDocType } from '../useUploadIdentity';
import { PERSONAL_DOC_OPTIONS } from '../personal_doc_options';

type Props = {
  applicantType: 'INDIVIDUAL' | 'CORPORATE' | null;
  personalDocType: PersonalDocType;
  setPersonalDocType: React.Dispatch<React.SetStateAction<PersonalDocType>>;
  isUploading: boolean;
  nameSuffix?: string; // to differentiate radio name between desktop/mobile
};

const cardClass = (selected: boolean) =>
  `block rounded-lg border p-3 cursor-pointer transition-colors ${
    selected ? 'border-hami-primary-dark ring-1 ring-hami-primary-dark bg-hami-bg-white' : 'border-gray-200 hover:border-hami-primary-dark'
  }`;

export const PersonalDocTypeSelector: React.FC<Props> = ({
  applicantType,
  personalDocType,
  setPersonalDocType,
  isUploading,
  nameSuffix,
}) => {
  if (applicantType !== 'INDIVIDUAL') return null;
  const name = `personal-doc-type${nameSuffix ? `-${nameSuffix}` : ''}`;

  return (
    <div className="space-y-2">
      <span className="block text-xs text-hami-text-secondary">アップロードする書類の種類</span>
      <div className="space-y-3">
        {PERSONAL_DOC_OPTIONS.map(({ value, label }) => {
          const selected = personalDocType === value;
          return (
            <label key={value} className={cardClass(selected)}>
              <span className="flex items-center gap-3">
                <input
                  type="radio"
                  name={name}
                  className="accent-hami-primary-dark"
                  checked={selected}
                  onChange={() => setPersonalDocType(value)}
                  disabled={isUploading}
                />
                <span className="text-sm">{label}</span>
              </span>
            </label>
          );
        })}
      </div>
    </div>
  );
};

export default PersonalDocTypeSelector;
