'use client';

import React from 'react';

type Props = {
  applicantType: 'INDIVIDUAL' | 'CORPORATE' | null;
  corporateDocType: 'CORP_SEAL_CERT' | 'CORP_REGISTRY_CERT';
  setCorporateDocType: React.Dispatch<React.SetStateAction<'CORP_SEAL_CERT' | 'CORP_REGISTRY_CERT'>>;
  isUploading: boolean;
  nameSuffix?: string; // to differentiate radio name between desktop/mobile
};

export const CorporateDocTypeSelector: React.FC<Props> = ({
  applicantType,
  corporateDocType,
  setCorporateDocType,
  isUploading,
  nameSuffix,
}) => {
  if (applicantType !== 'CORPORATE') return null;
  const name = `corp-doc-type${nameSuffix ? `-${nameSuffix}` : ''}`;
  return (
    <div className="space-y-2">
      <span className="block text-xs text-hami-text-secondary">アップロードする書類の種類</span>
      <div className="space-y-3">
        <label
          className={`block rounded-lg border p-3 cursor-pointer transition-colors ${
            corporateDocType === 'CORP_SEAL_CERT'
              ? 'border-hami-primary-dark ring-1 ring-hami-primary-dark bg-hami-bg-white'
              : 'border-gray-200 hover:border-hami-primary-dark'
          }`}
        >
          <span className="flex items-center gap-3">
            <input
              type="radio"
              name={name}
              className="accent-hami-primary-dark"
              checked={corporateDocType === 'CORP_SEAL_CERT'}
              onChange={() => setCorporateDocType('CORP_SEAL_CERT')}
              disabled={isUploading}
            />
            <span className="text-sm">印鑑証明書</span>
          </span>
        </label>

        <label
          className={`block rounded-lg border p-3 cursor-pointer transition-colors ${
            corporateDocType === 'CORP_REGISTRY_CERT'
              ? 'border-hami-primary-dark ring-1 ring-hami-primary-dark bg-hami-bg-white'
              : 'border-gray-200 hover:border-hami-primary-dark'
          }`}
        >
          <span className="flex items-center gap-3">
            <input
              type="radio"
              name={name}
              className="accent-hami-primary-dark"
              checked={corporateDocType === 'CORP_REGISTRY_CERT'}
              onChange={() => setCorporateDocType('CORP_REGISTRY_CERT')}
              disabled={isUploading}
            />
            <span className="text-sm">履歴事項証明書</span>
          </span>
        </label>
      </div>
    </div>
  );
};

export default CorporateDocTypeSelector;
