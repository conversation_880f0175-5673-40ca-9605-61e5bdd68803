'use client';

import React from 'react';
import LogoSquare from '@web/icons/logo_square.svg';
import LogoHorizontal from '@web/icons/logo_horizontal.svg';

type Props = { size?: 'large' | 'small'; variant?: 'square' | 'horizontal' };

export const Logo: React.FC<Props> = ({ size = 'large', variant = 'square' }) => {
  if (variant === 'horizontal') {
    const logoWidth = size === 'large' ? 164 : 110;
    const logoHeight = size === 'large' ? 48 : 32;
    return (
      <div className="flex items-center justify-center">
        <LogoHorizontal width={logoWidth} height={logoHeight} className="object-contain" aria-label="HAMI Logo" />
      </div>
    );
  }

  const logoSize = size === 'large' ? 200 : 60;
  return (
    <div className="flex items-center justify-center">
      <LogoSquare width={logoSize} height={logoSize} className="object-contain" aria-label="HAMI Logo" />
    </div>
  );
};

export default Logo;

