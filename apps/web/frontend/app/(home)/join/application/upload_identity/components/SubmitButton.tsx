'use client';

import React from 'react';

type Props = {
  onClick: () => void;
  disabled: boolean;
  isUploading: boolean;
  className?: string;
};

export const SubmitButton: React.FC<Props> = ({ onClick, disabled, isUploading, className }) => {
  return (
    <button
      type="button"
      data-testid="submit-button"
      onClick={onClick}
      disabled={disabled}
      className={
        className ??
        'w-48 mx-auto block bg-hami-primary-dark text-hami-primary-light py-3 px-6 rounded font-bold hover:bg-hami-primary-dark-hover focus:outline-none focus:ring-2 focus:ring-hami-primary-dark focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
      }
    >
      {isUploading ? '提出中...' : '提出'}
    </button>
  );
};

export default SubmitButton;
