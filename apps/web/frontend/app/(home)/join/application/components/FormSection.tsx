import React from 'react';

interface FormSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * フォームセクションコンポーネント
 * タイトル、区切り線、コンテンツエリアを持つ再利用可能なセクション
 */
export const FormSection: React.FC<FormSectionProps> = ({ 
  title, 
  children, 
  className = '' 
}) => {
  return (
    <div className={className}>
      <h2 className="text-lg text-hami-glyph-subtle pb-4">{title}</h2>
      <div className="w-full h-px bg-hami-border-separator mb-4"></div>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
};
