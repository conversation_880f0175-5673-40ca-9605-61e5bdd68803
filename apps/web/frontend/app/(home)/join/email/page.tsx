'use client';

// A0206: 入会申込メールアドレス入力

import { useRouter } from 'next/navigation';
import { Logo } from '../../../../components/logo';
import { useState } from 'react';

export default function Page() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const submitHandler = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    const formData = new FormData(e.currentTarget);
    const email = formData.get('email');
    if (email) {
      sessionStorage.setItem('pendingEmail', email as string);
      router.push('/join/confirm');
    }
  };

  return (
    <main className="min-h-screen bg-hami-bg-main relative">
      {/* 上部ロゴ(全画面共通) */}
      <div className="absolute top-10 left-1/2 transform -translate-x-1/2 z-10">
        <Logo size="large" variant="horizontal" />
      </div>

      {/* デスクトップレイアウト */}
      <div className="hidden lg:flex min-h-screen">
        <div className="flex-1 flex items-center justify-center bg-hami-bg-white py-20 pt-32">
          <Logo size="large" />
        </div>

        <div className="flex-1 flex items-center justify-center py-20 pt-32">
          <div className="w-full max-w-sm">
            <div className="text-center mb-12">
              <h1 className="text-4xl font-normal text-hami-text-primary mb-12" style={{ fontFamily: 'serif' }}>
                新規入会
              </h1>
            </div>

            <form className="space-y-6" onSubmit={submitHandler}>
              <div className="space-y-3">
                <label htmlFor="email" className="block text-sm font-bold text-hami-text-secondary">
                  メールアドレス
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                  disabled={isLoading}
                  className="w-full px-4 py-3 bg-hami-bg-white border border-hami-border-default rounded-lg text-lg focus:outline-none focus:ring-2 focus:ring-hami-primary-dark focus:border-transparent disabled:opacity-50"
                />
              </div>

              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-48 mx-auto block bg-hami-primary-dark text-hami-primary-light py-3 px-6 rounded font-bold hover:bg-hami-primary-dark-hover focus:outline-none focus:ring-2 focus:ring-hami-primary-dark focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  次へ
                </button>
              </div>
            </form>

            <div className="space-y-2 mt-12">
              <p className="text-sm text-hami-glyph-base font-bold">認証メールが届かない場合はこちらをご確認ください。</p>
              <div className="text-hami-glyph-subtle">
                <ol className="list-decimal list-inside space-y-1">
                  <li>迷惑メールフォルダーに分類されていないかの確認</li>
                  <li>bloominghorseclub.co.jpからのメールを受信できるように設定</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* モバイルレイアウト */}
      <div className="lg:hidden min-h-screen flex flex-col">
        {/* フォームエリア */}
        <div className="flex-1 flex items-center justify-center px-5 py-10 pt-32">
          <div className="w-full max-w-sm">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-normal text-hami-text-primary" style={{ fontFamily: 'serif' }}>
                新規入会
              </h2>
            </div>
            <form onSubmit={submitHandler} className="space-y-6">
              <div className="space-y-3">
                <label htmlFor="email-mobile" className="block text-sm font-bold text-hami-text-secondary">
                  メールアドレス
                </label>
                <input
                  id="email-mobile"
                  name="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                  disabled={isLoading}
                  className="w-full px-4 py-3 bg-hami-bg-white border border-hami-border-default rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-hami-primary-dark focus:border-transparent disabled:opacity-50"
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-48 mx-auto block bg-hami-primary-dark text-hami-primary-light py-3 px-6 rounded font-bold hover:bg-hami-primary-dark-hover focus:outline-none focus:ring-2 focus:ring-hami-primary-dark focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                次へ
              </button>
            </form>

            <div className="space-y-2 mt-12">
              <p className="text-xs text-hami-glyph-base font-bold">認証メールが届かない場合はこちらをご確認ください。</p>
              <div className="text-hami-glyph-subtle text-xs">
                <ol className="list-decimal list-inside space-y-1">
                  <li>迷惑メールフォルダーに分類されていないかの確認</li>
                  <li>bloominghorseclub.co.jpからのメールを受信できるように設定</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
