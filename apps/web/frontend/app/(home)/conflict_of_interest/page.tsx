import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '利益相反取引 | Blooming Horse Club',
  description: '利益相反取引である旨の表記について',
};

export default function ConflictOfInterestPage() {
  return (
    <div className="bg-white">
      <div className="bg-gray-100 py-4 px-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-black py-4">利益相反取引</h1>
        </div>
      </div>
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="prose prose-lg max-w-none text-hami-glyph-base">
          <p className="mb-6">
            株式会社Blooming Horse Club（以下「当社」といいます。）は、競走用馬ファンドの運営にあたり、当社の親法人等（金融商品取引法第31条の4第3項及び金融商品取引法施行令第15条の16第1項に定める「親法人等」）または親法人等の主要株主、もしくはその関連会社（以下「当社のグループ会社」といいます。）から、当社ホームページまたは競走馬カタログに記載する募集馬のうち、全てまたは一部を購入することを予定しています。
          </p>

          <p className="mb-6">
            当社は、購入する競走馬について、その代金が市場価格に照らして妥当であることを確認した上で購入します。<br />
            ただし、当該取引は、お客様と当社または当社のグループ会社との間で利害が対立する取引であるため、利益相反取引に該当します。
          </p>

          <p>
            本取引に関して利益相反が存在することを十分にご理解いただき、私（または当社）の判断と責任において、競走用馬ファンドへの出資の申し込みを行うことをご確認いただきますようお願い申し上げます。
          </p>
        </div>
      </div>
    </div>
  );
}
