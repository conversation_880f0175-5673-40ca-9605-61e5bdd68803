'use server';

import { HorseVideoService,
  ListHorseVideosRequestSchema,
  GetHorseVideoRequestSchema,
  CreateHorseVideoRequestSchema,
  UpdateHorseVideoRequestSchema,
  DeleteHorseVideoRequestSchema,
} from '@hami/admin-api-schema/horse_video_service_pb';
import { getClient } from '@web-admin/utils/api_clients';
import { create } from '@bufbuild/protobuf';
import type { MessageInitShape } from '@bufbuild/protobuf';
import { withAuthErrorHandling } from '@web-admin/utils/api_error_handlers';

const client = getClient(HorseVideoService);

export const listHorseVideos = async (req: MessageInitShape<typeof ListHorseVideosRequestSchema>) => withAuthErrorHandling(() => client.listHorseVideos(create(ListHorseVideosRequestSchema, req)));

export const getHorseVideo = async (req: MessageInitShape<typeof GetHorseVideoRequestSchema>) => withAuthErrorHandling(() => client.getHorseVideo(create(GetHorseVideoRequestSchema, req)));

export const createHorseVideo = async (req: MessageInitShape<typeof CreateHorseVideoRequestSchema>) => withAuthErrorHandling(() => client.createHorseVideo(create(CreateHorseVideoRequestSchema, req)));

export const updateHorseVideo = async (req: MessageInitShape<typeof UpdateHorseVideoRequestSchema>) => withAuthErrorHandling(() => client.updateHorseVideo(create(UpdateHorseVideoRequestSchema, req)));

export const deleteHorseVideo = async (req: MessageInitShape<typeof DeleteHorseVideoRequestSchema>) => withAuthErrorHandling(() => client.deleteHorseVideo(create(DeleteHorseVideoRequestSchema, req)));

