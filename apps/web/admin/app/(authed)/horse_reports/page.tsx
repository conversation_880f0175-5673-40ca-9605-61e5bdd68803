"use client";

import Link from 'next/link';
import { listHorseReports, publishMultipleReports } from '@web-admin/api_clients/horse_report_client';
import { useEffect, useMemo, useState, useTransition } from 'react';
import { useSearchParams } from 'next/navigation';
import { type HorseReportItem } from '@hami/admin-api-schema/models/horse_report_pb';
import { PublishStatusBadge, formatReportDate } from '../horses/[id]/reports/components';
import DataTable, { type Column } from '@web-admin/components/DataTable';

export default function Page() {
  const searchParams = useSearchParams();

  const [reports, setReports] = useState<HorseReportItem[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [selectedReports, setSelectedReports] = useState<Set<number>>(new Set());
  const [actionMessage, setActionMessage] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const pageSize = 20;

  const query = useMemo(() => {
    const page = parseInt(searchParams.get('page') ?? '1');
    const startYear = searchParams.get('startYear');
    const startMonth = searchParams.get('startMonth');
    const startDay = searchParams.get('startDay');
    const endYear = searchParams.get('endYear');
    const endMonth = searchParams.get('endMonth');
    const endDay = searchParams.get('endDay');
    return {
      page,
      startYear: startYear ? parseInt(startYear) : undefined,
      startMonth: startMonth ? parseInt(startMonth) : undefined,
      startDay: startDay ? parseInt(startDay) : undefined,
      endYear: endYear ? parseInt(endYear) : undefined,
      endMonth: endMonth ? parseInt(endMonth) : undefined,
      endDay: endDay ? parseInt(endDay) : undefined,
    };
  }, [searchParams]);

  useEffect(() => {
    const load = async () => {
      try {
        setLoading(true);
        setError(null);
        const res = await listHorseReports({
          startYear: query.startYear,
          startMonth: query.startMonth,
          startDay: query.startDay,
          endYear: query.endYear,
          endMonth: query.endMonth,
          endDay: query.endDay,
          page: query.page,
          pageSize,
        });
        setReports(res.reports);
        setTotalCount(res.totalCount);
        setCurrentPage(res.currentPage);
        setTotalPages(res.totalPages);
        setHasNextPage(res.hasNextPage);
        setSelectedReports(new Set());
      } catch (e) {
        console.error(e);
        setError('データの取得に失敗しました。');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [query.page, query.startYear, query.startMonth, query.startDay, query.endYear, query.endMonth, query.endDay]);

  useEffect(() => {
    if (!actionMessage) return;
    const t = setTimeout(() => setActionMessage(null), 3000);
    return () => clearTimeout(t);
  }, [actionMessage]);

  const handleSelectOne = (reportId: number, checked: boolean) => {
    const next = new Set(selectedReports);
    if (checked) {
      next.add(reportId);
    } else {
      next.delete(reportId);
    }
    setSelectedReports(next);
  };


  const isAllSelected = useMemo(() => {
    if (reports.length === 0) return false;
    return reports.every((r) => selectedReports.has(r.horseReportId));
  }, [reports, selectedReports]);

  const toggleSelectAllDisplayed = (checked: boolean) => {
    setSelectedReports((prev) => {
      if (checked) {
        const next = new Set(prev);
        reports.forEach((r) => next.add(r.horseReportId));
        return next;
      }
      return new Set();
    });
  };

  async function refetchList() {
    try {
      setError(null);
      const res = await listHorseReports({
        startYear: query.startYear,
        startMonth: query.startMonth,
        startDay: query.startDay,
        endYear: query.endYear,
        endMonth: query.endMonth,
        endDay: query.endDay,
        page: query.page,
        pageSize,
      });
      setReports(res.reports);
      setTotalCount(res.totalCount);
      setCurrentPage(res.currentPage);
      setTotalPages(res.totalPages);
      setHasNextPage(res.hasNextPage);
    } catch (e) {
      console.error(e);
      setError('データの取得に失敗しました。');
    }
  }

  const handleBulkPublish = () => {
    if (selectedReports.size === 0) return;
    setActionMessage(null);
    startTransition(() => {
      void (async () => {
        try {
          await publishMultipleReports({ horseReportIds: Array.from(selectedReports) });
          await refetchList();
          setSelectedReports(new Set());
          setActionMessage('近況を公開しました。');
        } catch (e) {
          console.error(e);
          setError('公開に失敗しました。');
        }
      })();
    });
  };

  const columns: Column<HorseReportItem>[] = [
    {
      header: '選択',
      accessor: 'horseReportId',
      width: '60px',
      render: (_value, row) => (
        <div className="w-full flex justify-center">
          <input
            type="checkbox"
            className="w-4 h-4"
            checked={selectedReports.has(row.horseReportId)}
            onChange={(e) => handleSelectOne(row.horseReportId, e.target.checked)}
          />
        </div>
      ),
    },
    {
      header: '馬名',
      accessor: 'horseName',
      width: '160px',
      render: (_value, row) => (
        <Link
          href={`/horses/${row.horseId}`}
          className="text-[18px] text-[#201f1f] hover:text-[#0e311e] underline block"
          title={row.horseName || row.horse?.recruitmentName}
        >
          {row.horseName || row.horse?.recruitmentName}
        </Link>
      ),
    },
    {
      header: '日付',
      accessor: 'reportDay',
      width: '120px',
      render: (_value, row) => (
        <span className="text-[16px] text-[#201f1f]">
          {formatReportDate(row.reportYear, row.reportMonth, row.reportDay)}
        </span>
      ),
    },
    {
      header: '場所',
      accessor: 'location',
      width: '120px',
      render: (_value, row) => (
        <span className="text-[16px] text-[#201f1f]" title={row.location}>
          {row.location}
        </span>
      ),
    },
    {
      header: '内容',
      accessor: 'content',
      render: (_value, row) => (
        <span className="text-[16px] text-[#201f1f] break-words whitespace-pre-wrap" title={row.content}>
          {row.content}
        </span>
      ),
    },
    {
      header: 'ステータス',
      accessor: 'publishStatus',
      width: '120px',
      render: (_value, row) => (
        <div className="w-full flex justify-center">
          <PublishStatusBadge status={row.publishStatus} />
        </div>
      ),
    },
    {
      header: 'メディア',
      accessor: 'media',
      width: '100px',
      render: (_value, row) => (
        <div className="w-full flex justify-center">
          {row.media.length > 0 ? (
            <span className="text-primary">{row.media.length}件</span>
          ) : (
            <span className="text-hami-glyph-subtle">なし</span>
          )}
        </div>
      ),
    },
    {
      header: '操作',
      accessor: 'horseReportId',
      width: '120px',
      render: (_value, row) => (
        <div className="w-full flex justify-center">
          <Link
            href={`/horses/${row.horseId}/reports/${row.horseReportId}`}
            className="btn-primary text-[16px] h-8 px-4 py-2 whitespace-nowrap"
          >
            詳細
          </Link>
        </div>
      ),
    },
  ];

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-hami-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">近況レポート一覧（全頭）</h1>
      </div>
      <div className="w-full max-w-6xl mx-auto px-6 space-y-6">
        {error && <div className="text-[#a51b2f] font-semibold">{error}</div>}

        <div className="sticky top-0 z-40 h-14 bg-white/95 backdrop-blur border border-[#e7e6e6] rounded shadow p-3 items-center flex justify-between">
          {selectedReports.size === 0 ? (
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  className="w-4 h-4"
                  checked={isAllSelected}
                  onChange={(e) => toggleSelectAllDisplayed(e.target.checked)}
                />
                <span className="text-[14px] text-[#201f1f]">表示中を全選択</span>
              </div>
              {actionMessage && (
                <div className="text-[#0e311e] text-[14px] font-semibold">{actionMessage}</div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-between flex-wrap gap-3 w-full">
              <div className="flex items-center gap-3">
                <div className="text-[14px] text-hami-glyph-subtle">{selectedReports.size} 件選択中</div>
                <button
                  className="text-[12px] underline text-[#201f1f]"
                  onClick={() => {
                    setSelectedReports(new Set());
                    setActionMessage(null);
                  }}
                  disabled={isPending}
                >
                  選択をクリア
                </button>
              </div>
              <div className="flex items-center gap-3 flex-wrap">
                <button
                  className="btn-primary text-[14px] h-8 px-3"
                  onClick={handleBulkPublish}
                  disabled={isPending || selectedReports.size === 0}
                >
                  選択した近況を公開
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="text-secondary">合計: {totalCount}件</div>

        <DataTable columns={columns} data={reports} className="border-[#e7e6e6] bg-[#e7e6e6]" />

        <div className="flex justify-between items-center pt-4">
          <div className="text-[14px] text-hami-glyph-subtle">
            ページ {currentPage} / {totalPages}
          </div>
          <div className="flex gap-2">
            {currentPage > 1 && (
              <Link className="btn-secondary text-[16px] h-8 px-4" href={`/horse_reports?page=${currentPage - 1}`}>
                前へ
              </Link>
            )}
            {hasNextPage && (
              <Link className="btn-primary text-[16px] h-8 px-4" href={`/horse_reports?page=${currentPage + 1}`}>
                次へ
              </Link>
            )}
          </div>
        </div>

        {loading && <div className="text-[14px] text-hami-glyph-subtle">読み込み中…</div>}
      </div>
    </main>
  );
}



