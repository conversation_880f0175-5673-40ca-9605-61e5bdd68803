'use client';

import Link from 'next/link';
import { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { listHorseVideos } from '@web-admin/api_clients/horse_video_client';
import type { HorseVideoItem } from '@hami/admin-api-schema/models/horse_video_pb';
import DataTable, { type Column } from '@web-admin/components/DataTable';
import { PublishStatusBadge } from '../horses/[id]/reports/components/PublishStatusBadge';

function formatVideoDate(y?: number, m?: number, d?: number) {
  if (!y || !m || !d) return '-';
  const mm = String(m).padStart(2, '0');
  const dd = String(d).padStart(2, '0');
  return `${y}/${mm}/${dd}`;
}

export default function Page() {
  const searchParams = useSearchParams();
  const [videos, setVideos] = useState<HorseVideoItem[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const pageSize = 20;

  const page = useMemo(() => parseInt(searchParams.get('page') ?? '1'), [searchParams]);

  useEffect(() => {
    const load = async () => {
      try {
        setLoading(true);
        setError(null);
        const res = await listHorseVideos({ page, pageSize, sortBy: 'video_date_desc' });
        setVideos(res.videos);
        setTotalCount(res.totalCount);
        setCurrentPage(res.currentPage);
        setTotalPages(res.totalPages);
        setHasNextPage(res.hasNextPage);
      } catch (e) {
        console.error(e);
        setError('データの取得に失敗しました。');
      } finally {
        setLoading(false);
      }
    };
    void load();
  }, [page]);

  const columns: Column<HorseVideoItem>[] = [
    {
      header: '馬名',
      accessor: 'horseName',
      width: '160px',
      render: (_value, row) => (
        <Link href={`/horses/${row.horseId}`} className="text-[18px] underline text-[#201f1f] hover:text-[#0e311e]">
          {row.horseName || row.horse?.recruitmentName}
        </Link>
      ),
    },
    {
      header: '日付',
      accessor: 'videoDay',
      width: '120px',
      render: (_value, row) => <span className="text-[16px]">{formatVideoDate(row.videoYear, row.videoMonth, row.videoDay)}</span>,
    },
    { header: 'タイトル', accessor: 'title' },
    { header: 'YouTube ID', accessor: 'youtubeVideoId', width: '200px' },
    {
      header: 'ステータス',
      accessor: 'publishStatus',
      width: '120px',
      render: (_v, row) => (
        <div className="w-full flex justify-center">
          <PublishStatusBadge status={row.publishStatus} />
        </div>
      ),
    },
    {
      header: '操作',
      accessor: 'horseVideoId',
      width: '120px',
      render: (_v, row) => (
        <div className="w-full flex justify-center">
          <Link href={`/horse_videos/${row.horseVideoId}`} className="btn-primary text-[16px] h-8 px-4 py-2">
            詳細
          </Link>
        </div>
      ),
    },
  ];

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-hami-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">動画一覧（全頭）</h1>
      </div>
      <div className="w-full max-w-6xl mx-auto px-6 space-y-6">
        <div className="flex justify-between items-center">
          <div className="text-secondary">合計: {totalCount}件</div>
        </div>

        {error && <div className="text-[#a51b2f] font-semibold">{error}</div>}

        <DataTable columns={columns} data={videos} className="border-[#e7e6e6] bg-[#e7e6e6]" />

        <div className="flex justify-between items-center pt-4">
          <div className="text-[14px] text-hami-glyph-subtle">
            ページ {currentPage} / {totalPages}
          </div>
          <div className="flex gap-2">
            {currentPage > 1 && (
              <Link className="btn-secondary text-[16px] h-8 px-4" href={`/horse_videos?page=${currentPage - 1}`}>
                前へ
              </Link>
            )}
            {hasNextPage && (
              <Link className="btn-primary text-[16px] h-8 px-4" href={`/horse_videos?page=${currentPage + 1}`}>
                次へ
              </Link>
            )}
          </div>
        </div>

        {loading && <div className="text-[14px] text-hami-glyph-subtle">読み込み中…</div>}
      </div>
    </main>
  );
}
