'use client';

import { useParams, useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import DateInput from '@web-admin/components/DateInput';
import { createHorseVideo } from '@web-admin/api_clients/horse_video_client';
import { PublishStatus } from '@hami/admin-api-schema/common_enums_pb';
import { extractYouTubeVideoId } from '@web-admin/utils/youtube';

function parseYmd(value: string): { y?: number; m?: number; d?: number } {
  const m = value.match(/^(\d{4})\/(\d{2})\/(\d{2})$/);
  if (!m) return {};
  return { y: Number(m[1]), m: Number(m[2]), d: Number(m[3]) };
}

export default function Page() {
  const router = useRouter();
  const params = useParams();
  const horseId = useMemo(() => Number(params?.id), [params]);

  const [date, setDate] = useState('');
  const [title, setTitle] = useState('');
  const [youtubeId, setYoutubeId] = useState('');
  const [startAtSeconds, setStartAtSeconds] = useState('');
  const [publishStatus, setPublishStatus] = useState<PublishStatus>(PublishStatus.DRAFT);
  const [description, setDescription] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setError(null);

    const ymd = parseYmd(date);
    if (!horseId || !ymd.y || !ymd.m || !ymd.d || !title) {
      setError('必須項目が不足しています。');
      return;
    }

    const parsedId = extractYouTubeVideoId(youtubeId);
    if (!parsedId) {
      setError('YouTubeのURLまたはIDの形式が正しくありません。');
      return;
    }

    try {
      setIsSubmitting(true);
      await createHorseVideo({
        horseId,
        videoYear: ymd.y,
        videoMonth: ymd.m,
        videoDay: ymd.d,
        title,
        description: description || undefined,
        youtubeVideoId: parsedId,
        startAtSeconds: startAtSeconds ? Number(startAtSeconds) : undefined,
        publishStatus,
      });
      router.push(`/horses/${horseId}/videos`);
    } catch (e) {
      console.error(e);
      setError('作成に失敗しました。');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-hami-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">動画の新規作成</h1>
      </div>

      <div className="w-full max-w-3xl mx-auto px-6 pb-12">
        {error && <div className="text-[#a51b2f] font-semibold mb-4">{error}</div>}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-[14px] mb-2">日付（必須）</label>
            <DateInput value={date} onChange={setDate} required />
          </div>

          <div>
            <label className="block text-[14px] mb-2">タイトル（必須）</label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full h-12 bg-white border border-hami-stroke-border rounded-lg px-4 text-[18px]"
              required
            />
          </div>

          <div>
            <label className="block text-[14px] mb-2">YouTube URLまたはID（必須）</label>
            <input
              type="text"
              value={youtubeId}
              onChange={(e) => setYoutubeId(e.target.value)}
              className="w-full h-12 bg-white border border-hami-stroke-border rounded-lg px-4 text-[18px]"
              required
            />
          </div>

          <div>
            <label className="block text-[14px] mb-2">開始秒（任意）</label>
            <input
              type="number"
              value={startAtSeconds}
              onChange={(e) => setStartAtSeconds(e.target.value)}
              className="w-full h-12 bg-white border border-hami-stroke-border rounded-lg px-4 text-[18px]"
              min={0}
            />
          </div>

          <div>
            <label className="block text-[14px] mb-2">公開ステータス</label>
            <select
              value={publishStatus}
              onChange={(e) => setPublishStatus(Number(e.target.value) as PublishStatus)}
              className="w-full h-12 bg-white border border-hami-stroke-border rounded-lg px-4 text-[18px]"
            >
              <option value={PublishStatus.DRAFT}>下書き</option>
              <option value={PublishStatus.PUBLISHED}>公開</option>
              <option value={PublishStatus.ARCHIVED}>アーカイブ</option>
            </select>
          </div>

          <div>
            <label className="block text-[14px] mb-2">説明（任意）</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full bg-white border border-hami-stroke-border rounded-lg px-4 py-3 text-[16px] min-h-[120px]"
            />
          </div>

          <div className="flex gap-3">
            <button type="submit" className="btn-primary h-10 px-5" disabled={isSubmitting}>
              {isSubmitting ? '作成中…' : '作成する'}
            </button>
            <button type="button" className="btn-secondary h-10 px-5" onClick={() => router.push(`/horses/${horseId}/videos`)}>
              キャンセル
            </button>
          </div>
        </form>
      </div>
    </main>
  );
}
