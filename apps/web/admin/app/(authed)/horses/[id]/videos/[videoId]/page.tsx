'use client';

import { useEffect, useMemo, useState, useTransition } from 'react';
import { useParams, useRouter } from 'next/navigation';
import DateInput from '@web-admin/components/DateInput';
import { deleteHorseVideo, getHorseVideo, updateHorseVideo } from '@web-admin/api_clients/horse_video_client';
import type { HorseVideoItem } from '@hami/admin-api-schema/models/horse_video_pb';
import { PublishStatus } from '@hami/admin-api-schema/common_enums_pb';
import { extractYouTubeVideoId } from '@web-admin/utils/youtube';

function formatYmd(y?: number, m?: number, d?: number) {
  if (!y || !m || !d) return '';
  const mm = String(m).padStart(2, '0');
  const dd = String(d).padStart(2, '0');
  return `${y}/${mm}/${dd}`;
}

function parseYmd(value: string): { y?: number; m?: number; d?: number } {
  const m = value.match(/^(\d{4})\/(\d{2})\/(\d{2})$/);
  if (!m) return {};
  return { y: Number(m[1]), m: Number(m[2]), d: Number(m[3]) };
}

export default function Page() {
  const router = useRouter();
  const params = useParams();
  const horseId = useMemo(() => Number(params?.id), [params]);
  const videoId = useMemo(() => Number(params?.videoId), [params]);

  const [video, setVideo] = useState<HorseVideoItem | null>(null);
  const [date, setDate] = useState('');
  const [title, setTitle] = useState('');
  const [youtubeId, setYoutubeId] = useState('');
  const [startAtSeconds, setStartAtSeconds] = useState('');
  const [publishStatus, setPublishStatus] = useState<PublishStatus>(PublishStatus.DRAFT);
  const [description, setDescription] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  useEffect(() => {
    if (!videoId) return;
    const load = async () => {
      try {
        setError(null);
        const res = await getHorseVideo({ horseVideoId: videoId });
        if (!res.video) {
          setError('データが見つかりません。');
          return;
        }
        setVideo(res.video);
        setDate(formatYmd(res.video.videoYear, res.video.videoMonth, res.video.videoDay));
        setTitle(res.video.title ?? '');
        setYoutubeId(res.video.youtubeVideoId ?? '');
        setStartAtSeconds(res.video.startAtSeconds != null ? String(res.video.startAtSeconds) : '');
        setPublishStatus(res.video.publishStatus);
        setDescription(res.video.description ?? '');
      } catch (e) {
        console.error(e);
        setError('読み込みに失敗しました。');
      }
    };
    void load();
  }, [videoId]);

  function handleUpdate(e: React.FormEvent) {
    e.preventDefault();
    if (!videoId) return;
    setError(null);
    setMessage(null);

    const ymd = parseYmd(date);
    if (!ymd.y || !ymd.m || !ymd.d || !title) {
      setError('必須項目が不足しています。');
      return;
    }
    const parsedId = extractYouTubeVideoId(youtubeId);
    if (!parsedId) {
      setError('YouTubeのURLまたはIDの形式が正しくありません。');
      return;
    }

    startTransition(() => {
      void (async () => {
        try {
          await updateHorseVideo({
            horseVideoId: videoId,
            videoYear: ymd.y,
            videoMonth: ymd.m,
            videoDay: ymd.d,
            title,
            description: description || undefined,
            youtubeVideoId: parsedId,
            startAtSeconds: startAtSeconds ? Number(startAtSeconds) : undefined,
            publishStatus,
          });
          setMessage('更新しました。');
        } catch (e) {
          console.error(e);
          setError('更新に失敗しました。');
        }
      })();
    });
  }

  async function handleDelete() {
    if (!videoId) return;
    if (!confirm('この動画を削除しますか？')) return;
    try {
      await deleteHorseVideo({ horseVideoId: videoId });
      router.push(`/horses/${horseId}/videos`);
    } catch (e) {
      console.error(e);
      setError('削除に失敗しました。');
    }
  }

  if (!video) {
    return (
      <main className="bg-white min-h-screen">
        <div className="w-full max-w-3xl mx-auto px-6 py-12">読み込み中…</div>
      </main>
    );
  }

  return (
    <main className="bg-white min-h-screen">
      <div className="bg-hami-surface-elevated text-center py-6 mb-6">
        <h1 className="text-xl font-bold text-primary">動画詳細 / 編集</h1>
      </div>

      <div className="w-full max-w-3xl mx-auto px-6 pb-12 space-y-6">
        {error && <div className="text-[#a51b2f] font-semibold">{error}</div>}
        {message && <div className="text-[#0e311e] font-semibold">{message}</div>}

        <div className="text-[14px] text-hami-glyph-subtle">馬ID: {video.horseId}</div>

        <form onSubmit={handleUpdate} className="space-y-6">
          <div>
            <label className="block text-[14px] mb-2">日付（必須）</label>
            <DateInput value={date} onChange={setDate} required />
          </div>

          <div>
            <label className="block text-[14px] mb-2">タイトル（必須）</label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full h-12 bg-white border border-hami-stroke-border rounded-lg px-4 text-[18px]"
              required
            />
          </div>

          <div>
            <label className="block text-[14px] mb-2">YouTube URLまたはID（必須）</label>
            <input
              type="text"
              value={youtubeId}
              onChange={(e) => setYoutubeId(e.target.value)}
              className="w-full h-12 bg-white border border-hami-stroke-border rounded-lg px-4 text-[18px]"
              required
            />
          </div>

          <div>
            <label className="block text-[14px] mb-2">開始秒（任意）</label>
            <input
              type="number"
              value={startAtSeconds}
              onChange={(e) => setStartAtSeconds(e.target.value)}
              className="w-full h-12 bg-white border border-hami-stroke-border rounded-lg px-4 text-[18px]"
              min={0}
            />
          </div>

          <div>
            <label className="block text-[14px] mb-2">公開ステータス</label>
            <select
              value={publishStatus}
              onChange={(e) => setPublishStatus(Number(e.target.value) as PublishStatus)}
              className="w-full h-12 bg-white border border-hami-stroke-border rounded-lg px-4 text-[18px]"
            >
              <option value={PublishStatus.DRAFT}>下書き</option>
              <option value={PublishStatus.PUBLISHED}>公開</option>
              <option value={PublishStatus.ARCHIVED}>アーカイブ</option>
            </select>
          </div>

          <div>
            <label className="block text-[14px] mb-2">説明（任意）</label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full bg-white border border-hami-stroke-border rounded-lg px-4 py-3 text-[16px] min-h-[120px]"
            />
          </div>

          <div className="flex gap-3">
            <button type="submit" className="btn-primary h-10 px-5" disabled={isPending}>
              {isPending ? '更新中…' : '更新する'}
            </button>
            <button type="button" className="btn-secondary h-10 px-5" onClick={() => router.push(`/horses/${horseId}/videos`)}>
              一覧へ戻る
            </button>
            <button type="button" className="btn-danger h-10 px-5 ml-auto" onClick={handleDelete}>
              削除する
            </button>
          </div>
        </form>
      </div>
    </main>
  );
}
