'use client';

import { useMemo, useCallback, useState } from 'react';
import { useHorseFaceImageUploader } from '@web-admin/hooks/useHorseFaceImageUploader';
import type { FaceImageFormState, FaceImageAction } from './faceImageTypes';

interface HorseFaceImageManagerProps {
  horseId: number;
  faceImageState: FaceImageFormState;
  onChange: (nextState: FaceImageFormState) => void;
}

export default function HorseFaceImageManager({ horseId, faceImageState, onChange }: HorseFaceImageManagerProps) {
  const { isUploading, error, uploadFile, setError, clearError } = useHorseFaceImageUploader({ horseId });
  const [isDragging, setIsDragging] = useState(false);

  const currentImageUrl = useMemo(() => {
    if (faceImageState.newImage) {
      return faceImageState.newImage.previewUrl;
    }
    if (faceImageState.action === 'delete') {
      return undefined;
    }
    return faceImageState.originalUrl;
  }, [faceImageState]);

  const canUpload = useMemo(() => {
    if (faceImageState.newImage) {
      return true;
    }
    if (faceImageState.originalUrl) {
      return faceImageState.action === 'delete';
    }
    return true;
  }, [faceImageState]);

  const updateState = useCallback(
    (partial: Partial<FaceImageFormState>) => {
      onChange({
        originalUrl: faceImageState.originalUrl,
        action: faceImageState.action,
        newImage: faceImageState.newImage ?? null,
        ...partial,
      });
    },
    [faceImageState, onChange]
  );

  const handleDeleteOriginal = () => {
    clearError();
    if (faceImageState.newImage?.previewUrl) {
      URL.revokeObjectURL(faceImageState.newImage.previewUrl);
    }
    updateState({ action: 'delete', newImage: null });
  };

  const handleRemoveNewImage = () => {
    clearError();
    if (faceImageState.newImage?.previewUrl) {
      URL.revokeObjectURL(faceImageState.newImage.previewUrl);
    }
    if (faceImageState.originalUrl) {
      updateState({ action: 'delete', newImage: null });
    } else {
      updateState({ action: 'none', newImage: null });
    }
  };

  const handleUpload = async (file: File) => {
    if (!canUpload) {
      setError('現在の顔写真を削除してからアップロードしてください');
      return;
    }

    clearError();
    const result = await uploadFile(file);
    if (!result) {
      return;
    }

    const nextAction: FaceImageAction = faceImageState.originalUrl ? 'replace' : 'add';
    if (faceImageState.newImage?.previewUrl) {
      URL.revokeObjectURL(faceImageState.newImage.previewUrl);
    }
    updateState({ action: nextAction, newImage: result });
  };

  const handleDrop: React.DragEventHandler<HTMLDivElement> = (event) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
    if (!canUpload) {
      setError('現在の顔写真を削除してからアップロードしてください');
      return;
    }

    const file = event.dataTransfer.files?.[0];
    if (file) {
      handleUpload(file);
    }
  };

  const handleDragOver: React.DragEventHandler<HTMLDivElement> = (event) => {
    event.preventDefault();
    event.stopPropagation();
    if (!canUpload) return;
    setIsDragging(true);
  };

  const handleDragLeave: React.DragEventHandler<HTMLDivElement> = (event) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  };

  return (
    <section className="space-y-4">
      <header className="flex items-center justify-between">
        <h3 className="text-base font-semibold text-hami-glyph-base">顔写真</h3>
        {isUploading && <span className="text-xs text-hami-glyph-subtle">アップロード中...</span>}
      </header>

      <div className="space-y-3">
        <div
          className={`w-full aspect-[2/3] max-h-[300px] max-w-[200px] rounded-xl overflow-hidden flex items-center justify-center relative group ${currentImageUrl ? 'border border-hami-border-default bg-hami-surface-elevated' : 'border-2 border-dashed cursor-pointer transition-colors p-4'} ${!currentImageUrl ? (isDragging ? 'border-hami-bush-800 bg-hami-bush-50' : 'border-gray-300 bg-gray-200') : ''}`}
          onClick={() => {
            if (currentImageUrl) return;
            if (!canUpload || isUploading) return;
            clearError();
            document.getElementById('face-image-input')?.click();
          }}
          onDragOver={(e) => {
            if (currentImageUrl) return;
            handleDragOver(e);
          }}
          onDragLeave={(e) => {
            if (currentImageUrl) return;
            handleDragLeave(e);
          }}
          onDrop={(e) => {
            if (currentImageUrl) return;
            handleDrop(e);
          }}
        >
          {currentImageUrl ? (
            <>
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img src={currentImageUrl} alt="顔写真プレビュー" className="w-full h-full object-cover" />
              <button
                type="button"
                onClick={() => (faceImageState.newImage ? handleRemoveNewImage() : handleDeleteOriginal())}
                className="absolute inset-0 rounded-xl flex items-center justify-center bg-black/40 text-white text-lg font-bold opacity-0 group-hover:opacity-100 transition-opacity"
                aria-label="顔写真を削除"
              >
                削除
              </button>
            </>
          ) : (
            <p className="text-sm text-hami-glyph-subtle select-none">画像をドラッグ＆ドロップまたはクリック</p>
          )}
        </div>

        {/* 非表示のファイル入力（クリックで発火） */}
        <input
          id="face-image-input"
          type="file"
          accept="image/*"
          className="hidden"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (!file) return;
            if (!canUpload || isUploading) return;
            clearError();
            handleUpload(file);
            // 同じファイルを連続選択できるようにリセット
            e.currentTarget.value = '';
          }}
        />
      </div>

      {error && <div className="text-xs text-red-600 bg-red-50 border border-red-200 rounded px-3 py-2">{error}</div>}
    </section>
  );
}
