import { useCallback, useState } from 'react';
import { getHorseBlackTypeUploadUrl, createHorseBlackType, updateHorseBlackType } from '@web-admin/api_clients/horse_client';

const PDF_EXT_REGEX = /\.pdf$/i;

interface UploadResult {
  success: boolean;
  url?: string; // アップロード後のURL（S3のパス想定）
  fileKey?: string; // サーバ側で管理するキー
}

interface UseHorseBlackTypeUploadProps {
  horseId: number;
  onUploadComplete?: (pdfUrl: string, fileKey: string) => void;
}

export const useHorseBlackTypeUpload = ({ horseId, onUploadComplete }: UseHorseBlackTypeUploadProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [filename, setFilename] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<string>('');
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [pending, setPending] = useState<{ fileKey: string; url: string } | null>(null);

  const sanitizeFilename = useCallback((name: string): string => {
    const base = name.split('/').pop() || name;
    const sanitized = base.replace(/[^A-Za-z0-9._-]/g, '_');
    // 拡張子強制（.pdf）
    if (!/\.[^.]+$/.test(sanitized)) return sanitized + '.pdf';
    return sanitized.replace(/\.[^.]+$/, '.pdf');
  }, []);

  const validateFile = useCallback((f: File): string | null => {
    if (!PDF_EXT_REGEX.test(f.name)) {
      return 'PDFファイル（.pdf）のみアップロード可能です';
    }
    return null;
  }, []);

  const uploadSingleFileToS3Only = useCallback(async (inputFile?: File): Promise<UploadResult> => {
    // 直渡し or 内部状態のいずれか
    let targetFile = file;
    let targetFilename = filename;

    if (inputFile) {
      const err = validateFile(inputFile);
      if (err) {
        setError(err);
        return { success: false };
      }
      const safeName = sanitizeFilename(inputFile.name);
      const wrapped = new File([inputFile], safeName, { type: inputFile.type || 'application/pdf' });
      targetFile = wrapped;
      targetFilename = safeName;
      // 表示用に状態も更新
      setFile(wrapped);
      setFilename(safeName);
    }

    if (!targetFile) {
      setError('ファイルが選択されていません');
      return { success: false };
    }

    setIsUploading(true);
    setUploadStatus('アップロード準備中...');
    setUploadProgress(0);

    try {
      // 1) 署名付きURL取得
      const { uploadUrl, fileKey } = await getHorseBlackTypeUploadUrl({
        horseId,
        fileType: targetFile.type || 'application/pdf',
        filename: targetFilename || targetFile.name,
      });

      // 2) S3へPUTアップロード
      await new Promise<void>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percentComplete = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(percentComplete);
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            setUploadProgress(100);
            resolve();
          } else {
            reject(new Error(`アップロードに失敗しました (${xhr.status})`));
          }
        });

        xhr.addEventListener('error', () => reject(new Error('ネットワークエラーが発生しました')));
        xhr.addEventListener('timeout', () => reject(new Error('アップロードがタイムアウトしました')));

        xhr.open('PUT', uploadUrl);
        xhr.setRequestHeader('Content-Type', targetFile.type || 'application/pdf');
        xhr.timeout = 60000;
        xhr.send(targetFile);
      });

      const uploadedUrl = uploadUrl.split('?')[0];
      setUploadStatus('アップロードが完了しました！');
      setPending({ fileKey, url: uploadedUrl });
      onUploadComplete?.(uploadedUrl, fileKey);
      return { success: true, url: uploadedUrl, fileKey };
    } catch (e) {
      const message = e && typeof e === 'object' && 'message' in e ? (e as any).message : 'アップロードに失敗しました';
      setError(String(message));
      setUploadStatus(String(message));
      return { success: false };
    } finally {
      setIsUploading(false);
    }
  }, [file, filename, horseId, onUploadComplete, sanitizeFilename, validateFile]);

  const getPendingBlackType = useCallback(() => pending, [pending]);

  const commitBlackTypeByFileKey = useCallback(async (fileKey: string, description?: string) => {
    try {
      await createHorseBlackType({ horseId, fileKey });
      if (description) {
        await updateHorseBlackType({ horseId, description });
      }
      return true;
    } catch (e) {
      const message = e && typeof e === 'object' && 'message' in e ? (e as any).message : '保存に失敗しました';
      setError(String(message));
      setUploadStatus(String(message));
      return false;
    }
  }, [horseId]);

  return {
    file,
    filename,
    isUploading,
    uploadStatus,
    uploadProgress,
    error,
    uploadSingleFileToS3Only,
    pending,
    getPendingBlackType,
    commitBlackTypeByFileKey,
  };
};
