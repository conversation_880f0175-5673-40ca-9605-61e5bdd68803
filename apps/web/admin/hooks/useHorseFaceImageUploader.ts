'use client';

import { useState, useCallback } from 'react';
import { getHorseImageUploadUrl } from '@web-admin/api_clients/horse_client';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];

const sanitizeFilename = (filename: string): string => {
  const basename = filename.split('/').pop() || filename;
  const sanitized = basename.replace(/[^A-Za-z0-9._-]/g, '_');
  const result = sanitized.startsWith('.') ? sanitized.substring(1) : sanitized;
  return result || 'horse-face-image.jpg';
};

const validateFile = (file: File): string | null => {
  if (file.size > MAX_FILE_SIZE) {
    return `ファイルサイズが大きすぎます。10MB以下のファイルを選択してください。（現在: ${(file.size / 1024 / 1024).toFixed(1)}MB）`;
  }
  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
    return 'JPEG、PNG、GIFファイルのみアップロード可能です';
  }
  if (!file.name || file.name.length > 255) {
    return 'ファイル名が無効です。255文字以下のファイル名を使用してください。';
  }
  const filenameRegex = /^[a-zA-Z0-9._-]+\.(jpe?g|png|gif)$/i;
  if (!filenameRegex.test(file.name)) {
    return 'ファイル名は英数字、ピリオド、ハイフン、アンダースコアのみを含む有効な画像ファイル名である必要があります';
  }
  if (file.name.includes('..') || file.name.startsWith('.')) {
    return 'ファイル名に不正な文字列が含まれています';
  }
  return null;
};

const convertToJpeg = async (file: File): Promise<File> => {
  if (file.type === 'image/jpeg' || file.type === 'image/jpg') {
    return file;
  }

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Canvas context not available'));
          return;
        }
        ctx.drawImage(img, 0, 0);
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to convert image'));
              return;
            }
            const jpegFile = new File([blob], file.name.replace(/\.[^/.]+$/, '.jpg'), {
              type: 'image/jpeg',
            });
            resolve(jpegFile);
          },
          'image/jpeg',
          0.95
        );
      };
      img.onerror = () => reject(new Error('Failed to load image for conversion'));
      img.src = e.target?.result as string;
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
};

export interface UploadFaceImageResult {
  previewUrl: string;
  fileKey: string;
}

export const useHorseFaceImageUploader = ({ horseId }: { horseId: number }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadFile = useCallback(
    async (file: File): Promise<UploadFaceImageResult | null> => {
      setError(null);
      const sanitizedFile = new File([file], sanitizeFilename(file.name), { type: file.type });
      const errorMessage = validateFile(sanitizedFile);
      if (errorMessage) {
        setError(errorMessage);
        return null;
      }

      setIsUploading(true);
      try {
        const processedFile = await convertToJpeg(sanitizedFile);
        const previewUrl = URL.createObjectURL(processedFile);
        const uploadInfo = await getHorseImageUploadUrl({
          horseId,
          fileType: processedFile.type,
          filename: processedFile.name,
        });

        const response = await fetch(uploadInfo.uploadUrl, {
          method: 'PUT',
          headers: {
            'Content-Type': processedFile.type,
          },
          body: processedFile,
        });

        if (!response.ok) {
          URL.revokeObjectURL(previewUrl);
          throw new Error(`Failed to upload face image to S3: ${response.status}`);
        }

        return {
          previewUrl,
          fileKey: uploadInfo.fileKey,
        } satisfies UploadFaceImageResult;
      } catch (err) {
        console.error('Failed to upload horse face image:', err);
        setError('顔写真のアップロードに失敗しました');
        return null;
      } finally {
        setIsUploading(false);
      }
    },
    [horseId]
  );

  const clearError = useCallback(() => setError(null), []);

  return {
    isUploading,
    error,
    uploadFile,
    setError,
    clearError,
  };
};
