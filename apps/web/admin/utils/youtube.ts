/**
 * YouTube の共有URLや通常のURL、ショートURL、埋め込みURLから videoId を抽出します。
 * すでに videoId のみが渡された場合はそのまま返します。
 */
export const extractYouTubeVideoId = (input: string): string | null => {
  if (!input) return null;
  const trimmed = input.trim();

  // すでにIDのみ（11文字の一般的なID形式）と思われる場合は軽くバリデーションして返す
  // ただし厳密ではないため、URLパースに失敗した場合のフォールバックとしても利用
  const idLike = /^[a-zA-Z0-9_-]{6,}$/; // 6文字以上にして、将来のID長変更にも少し耐性

  // URLとして解釈できる場合
  try {
    const url = new URL(trimmed);
    const host = url.hostname.toLowerCase();

    // youtu.be 短縮URL: https://youtu.be/<id>
    if (host === 'youtu.be') {
      const pathId = url.pathname.split('/').filter(Boolean)[0];
      return pathId ? pathId : null;
    }

    // www.youtube.com / m.youtube.com 等: /watch?v=ID, /shorts/ID, /embed/ID, /live/ID
    if (host.endsWith('youtube.com')) {
      // watch パラメータ
      const v = url.searchParams.get('v');
      if (v) return v;

      const segments = url.pathname.split('/').filter(Boolean);
      // /shorts/ID
      if (segments[0] === 'shorts' && segments[1]) return segments[1];
      // /embed/ID
      if (segments[0] === 'embed' && segments[1]) return segments[1];
      // /live/ID
      if (segments[0] === 'live' && segments[1]) return segments[1];
    }
  } catch {
    // URL でなければそのままID候補として扱う
  }

  // 最後にIDっぽければ受け入れる
  return idLike.test(trimmed) ? trimmed : null;
};

