'use client';

import { useState, useRef, useEffect } from 'react';
import IconCalendarMonth from '@web-admin/icons/icon_calendar_month.svg';

interface DateInputProps {
  value: string; // YYYY/MM/DD format
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
}

export default function DateInput({
  value,
  onChange,
  placeholder = 'YYYY/MM/DD',
  className = '',
  disabled = false,
  required = false,
}: DateInputProps) {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const calendarRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // valueが変更されたときにinputValueを更新
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // カレンダー外クリックで閉じる
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        setIsCalendarOpen(false);
      }
    };

    if (isCalendarOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isCalendarOpen]);

  // テキスト入力の処理
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    const formattedValue = formatDateInput(rawValue);

    setInputValue(formattedValue);

    // YYYY/MM/DD形式の検証
    if (isValidDateFormat(formattedValue) || formattedValue === '') {
      onChange(formattedValue);
    }
  };

  // 日付入力のフォーマット処理
  const formatDateInput = (value: string): string => {
    // 数字以外を除去
    const numbersOnly = value.replace(/\D/g, '');

    // 最大8桁まで
    const truncated = numbersOnly.slice(0, 8);

    // 長さに応じてスラッシュを挿入
    if (truncated.length <= 4) {
      return truncated;
    } else if (truncated.length <= 6) {
      return `${truncated.slice(0, 4)}/${truncated.slice(4)}`;
    } else {
      return `${truncated.slice(0, 4)}/${truncated.slice(4, 6)}/${truncated.slice(6)}`;
    }
  };

  // キーボード入力の処理
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // 数字、バックスペース、デリート、矢印キー、タブ、エンターのみ許可
    const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Enter', 'Escape', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'];

    const isNumber = /^[0-9]$/.test(e.key);
    const isAllowedKey = allowedKeys.includes(e.key);
    const isCtrlCmd = e.ctrlKey || e.metaKey;

    if (!isNumber && !isAllowedKey && !isCtrlCmd) {
      e.preventDefault();
    }
  };

  // ペースト処理
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData('text');
    const formattedValue = formatDateInput(pastedText);
    setInputValue(formattedValue);

    if (isValidDateFormat(formattedValue) || formattedValue === '') {
      onChange(formattedValue);
    }
  };

  // 日付形式の検証
  const isValidDateFormat = (dateStr: string): boolean => {
    const regex = /^\d{4}\/\d{2}\/\d{2}$/;
    if (!regex.test(dateStr)) return false;

    const [year, month, day] = dateStr.split('/').map(Number);
    const date = new Date(year, month - 1, day);

    return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;
  };

  // カレンダーから日付選択
  const handleDateSelect = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const dateStr = `${year}/${month}/${day}`;

    setInputValue(dateStr);
    onChange(dateStr);
    setIsCalendarOpen(false);
  };

  // カレンダーボタンクリック
  const handleCalendarClick = () => {
    if (!disabled) {
      setIsCalendarOpen(!isCalendarOpen);
    }
  };

  // 現在の日付を取得（デフォルト表示用）
  const getCurrentDate = (): Date => {
    if (value && isValidDateFormat(value)) {
      const [year, month, day] = value.split('/').map(Number);
      return new Date(year, month - 1, day);
    }
    return new Date();
  };

  return (
    <div className="relative">
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          maxLength={10}
          className={`w-full h-12 bg-white border border-hami-stroke-border rounded-lg pl-4 pr-12 text-[18px] text-hami-glyph-base tracking-[1.8px] leading-[1.8] focus:outline-none focus:ring-2 focus:ring-hami-bush-800 ${
            disabled ? 'bg-hami-surface-base text-hami-glyph-subtle' : ''
          } ${className}`}
        />
        <button
          type="button"
          onClick={handleCalendarClick}
          disabled={disabled}
          className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded hover:bg-gray-100 transition-colors ${
            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
          }`}
        >
          <IconCalendarMonth className="w-5 h-5 text-hami-glyph-subtle" />
        </button>
      </div>

      {/* カレンダー */}
      {isCalendarOpen && (
        <div
          ref={calendarRef}
          className="absolute top-full left-0 mt-1 bg-white border border-hami-stroke-border rounded-lg shadow-lg z-50 p-4"
        >
          <Calendar selectedDate={getCurrentDate()} onDateSelect={handleDateSelect} />
        </div>
      )}
    </div>
  );
}

// シンプルなカレンダーコンポーネント
interface CalendarProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
}

function Calendar({ selectedDate, onDateSelect }: CalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1));

  const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // 前月の日付で埋める
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // 当月の日付
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }

    return days;
  };

  const days = getDaysInMonth(currentMonth);

  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  const isSelectedDate = (date: Date | null) => {
    if (!date) return false;
    return date.toDateString() === selectedDate.toDateString();
  };

  const isToday = (date: Date | null) => {
    if (!date) return false;
    return date.toDateString() === new Date().toDateString();
  };

  return (
    <div className="w-64">
      {/* ヘッダー */}
      <div className="flex items-center justify-between mb-4">
        <button type="button" onClick={goToPreviousMonth} className="p-1 hover:bg-gray-100 rounded">
          ←
        </button>
        <div className="font-semibold">
          {currentMonth.getFullYear()}年 {monthNames[currentMonth.getMonth()]}
        </div>
        <button type="button" onClick={goToNextMonth} className="p-1 hover:bg-gray-100 rounded">
          →
        </button>
      </div>

      {/* 曜日ヘッダー */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {['日', '月', '火', '水', '木', '金', '土'].map((day) => (
          <div key={day} className="text-center text-sm text-gray-500 py-1">
            {day}
          </div>
        ))}
      </div>

      {/* 日付グリッド */}
      <div className="grid grid-cols-7 gap-1">
        {days.map((date, index) => (
          <button
            key={index}
            type="button"
            onClick={() => date && onDateSelect(date)}
            disabled={!date}
            className={`
              h-8 text-sm rounded transition-colors
              ${!date ? 'invisible' : ''}
              ${isSelectedDate(date) ? 'bg-hami-bush-800 text-white' : ''}
              ${isToday(date) && !isSelectedDate(date) ? 'bg-hami-bush-100 text-hami-bush-800' : ''}
              ${date && !isSelectedDate(date) && !isToday(date) ? 'hover:bg-gray-100' : ''}
            `}
          >
            {date?.getDate()}
          </button>
        ))}
      </div>
    </div>
  );
}
