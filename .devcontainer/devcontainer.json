{
  "name": "Hami Development Environment",
  "dockerComposeFile": "docker-compose.yml",
  "service": "devcontainer",
  "workspaceFolder": "/workspace",
  "shutdownAction": "stopCompose",

  "features": {
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/marcozac/devcontainer-features/buf:1": {},
    "ghcr.io/devcontainers/features/docker-in-docker:2": {
      "version": "latest",
      "enableNonRootDocker": "true"
    },
    "ghcr.io/devcontainers/features/common-utils:2": {}
  },

  "privileged": true,
  "initializeCommand": "echo 'Initializing Docker in DevContainer...'",

  "customizations": {
    "vscode": {
      "extensions": [
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-json",
        "ms-playwright.playwright",
        "Prisma.prisma",
        "ms-vscode.vscode-docker",
        "bufbuild.vscode-buf",
        "ms-vscode.vscode-proto3",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",
        "ms-vscode.vscode-yaml",
        "redhat.vscode-xml",
        "ms-vscode.hexeditor"
      ],
      "settings": {
        "typescript.preferences.importModuleSpecifier": "relative",
        "typescript.suggest.autoImports": true,
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": "explicit"
        },
        "files.associations": {
          "*.proto": "proto3"
        },
        "terminal.integrated.defaultProfile.linux": "bash",
        "terminal.integrated.profiles.linux": {
          "bash": {
            "path": "/bin/bash"
          }
        }
      }
    }
  },

  "forwardPorts": [
    3000, // web-frontend
    3001, // web-admin
    3002, // core-admin
    8080, // web-api
    8081, // core-api
    5432, // PostgreSQL
    8025, // Mailpit Web UI
    1025, // Mailpit SMTP
    9000, // MinIO S3 API
    9001, // MinIO Console
    5555 // Prisma Studio
  ],

  "portsAttributes": {
    "3000": {
      "label": "Web Frontend",
      "onAutoForward": "notify"
    },
    "3001": {
      "label": "Web Admin",
      "onAutoForward": "notify"
    },
    "3002": {
      "label": "Core Admin",
      "onAutoForward": "notify"
    },
    "8080": {
      "label": "Web API",
      "onAutoForward": "notify"
    },
    "8081": {
      "label": "Core API",
      "onAutoForward": "notify"
    },
    "5432": {
      "label": "PostgreSQL",
      "onAutoForward": "silent"
    },
    "8025": {
      "label": "Mailpit Web UI",
      "onAutoForward": "openBrowser"
    },
    "1025": {
      "label": "Mailpit SMTP",
      "onAutoForward": "silent"
    },
    "9000": {
      "label": "MinIO S3 API",
      "onAutoForward": "silent"
    },
    "9001": {
      "label": "MinIO Console",
      "onAutoForward": "openBrowser"
    },
    "5555": {
      "label": "Prisma Studio",
      "onAutoForward": "openBrowser"
    }
  },

  "postCreateCommand": "bash .devcontainer/postCreateCommand.sh",

  "remoteUser": "node",

  "mounts": [
    "source=${localWorkspaceFolder}/node_modules,target=/workspace/node_modules,type=bind,consistency=cached",
    "source=hami-devcontainer-pnpm-store,target=/home/<USER>/.local/share/pnpm,type=volume"
  ]
}
