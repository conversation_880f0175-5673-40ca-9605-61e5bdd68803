#!/bin/bash

set -e

echo "🚀 Starting Hami development environment setup..."

# Change to workspace directory
cd /workspace

# Install dependencies
echo "📦 Installing dependencies with pnpm..."
pnpm install

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
until pg_isready -h db -p 5432 -U postgres; do
  echo "Waiting for database..."
  sleep 2
done

echo "✅ Database is ready!"

# Set up database schema
echo "🗄️ Setting up database schema..."
pnpm --filter @hami/prisma exec prisma db push

# Set up test database
echo "🧪 Setting up test database..."
DATABASE_URL="**************************************/hami_test" pnpm --filter @hami/prisma exec prisma db push

# Generate Prisma client
echo "🔧 Generating Prisma client..."
pnpm --filter @hami/prisma exec prisma generate

# Generate API schemas
echo "🔗 Generating API schemas..."
pnpm --filter @hami/web-api-schema generate
pnpm --filter @hami/admin-api-schema generate
pnpm --filter @hami/core-admin-api-schema generate

# Generate API clients
echo "📡 Generating API clients..."
pnpm --filter @hami/web-admin gen_api_clients
pnpm --filter @hami/web-frontend gen_api_clients

# Create MinIO bucket
echo "🪣 Setting up MinIO bucket..."
echo "⏳ Waiting for MinIO to be ready..."
until curl --silent --fail http://minio:9000/minio/health/ready; do
  echo "Waiting for MinIO..."
  sleep 2
done
echo "✅ MinIO is ready!"
curl -X PUT http://minio:9000/test-bucket \
  -H "Authorization: AWS4-HMAC-SHA256 Credential=minioadmin/20230101/us-east-1/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=dummy" \
  || echo "MinIO bucket creation failed or bucket already exists"

echo "✅ Development environment setup complete!"
echo ""
echo "🎉 You can now start developing!"
echo ""
echo "Available commands:"
echo "  make up      - Start all services"
echo "  make down    - Stop all services"
echo "  make test    - Run tests"
echo "  make routes  - Generate API schemas and clients"
echo ""
echo "Available services:"
echo "  Web Frontend:  http://localhost:3000"
echo "  Web Admin:     http://localhost:3001"
echo "  Core Admin:    http://localhost:3002"
echo "  Web API:       http://localhost:8080"
echo "  Core API:      http://localhost:8081"
echo "  Mailpit UI:    http://localhost:8025"
echo "  MinIO Console: http://localhost:9001"
echo "  Prisma Studio: http://localhost:5555"
