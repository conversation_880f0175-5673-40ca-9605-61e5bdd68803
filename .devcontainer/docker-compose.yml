version: '3.8'

services:
  devcontainer:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../:/workspace:cached
      - hami-devcontainer-pnpm-store:/home/<USER>/.local/share/pnpm
    command: sleep infinity
    networks:
      - hami-network
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/hami
      - FRONTEND_ENDPOINT=http://localhost:3000
      - SMTP_HOST=mailpit
      - SMTP_PORT=1025
      - MAILPIT_URL=http://mailpit:8025
      - JWT_SECRET=dev-secret
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin
      - AWS_REGION=ap-northeast-1
      - S3_BUCKET_NAME=test-bucket
      - S3_ENDPOINT=http://minio:9000
    depends_on:
      - db
      - mailpit
      - minio

  db:
    image: postgres:15-alpine
    restart: unless-stopped
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=hami
    volumes:
      - db_data:/var/lib/postgresql/data
      - ../packages/prisma/docker/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    networks:
      - hami-network

  mailpit:
    image: axllent/mailpit
    restart: unless-stopped
    ports:
      - '1025:1025' # SMTP Port
      - '8025:8025' # Web UI Port
    networks:
      - hami-network

  minio:
    image: minio/minio:latest
    restart: unless-stopped
    command: server /data --console-address ":9001"
    ports:
      - '9000:9000' # S3 API
      - '9001:9001' # Console UI
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio-data:/data
    networks:
      - hami-network

networks:
  hami-network:
    driver: bridge

volumes:
  db_data:
    driver: local
  minio-data:
    driver: local
  hami-devcontainer-pnpm-store:
    driver: local
