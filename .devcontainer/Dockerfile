FROM node:20-slim

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    curl \
    wget \
    unzip \
    build-essential \
    python3 \
    python3-pip \
    openssl \
    ca-certificates \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Install pnpm globally
RUN npm install -g pnpm@10.8.0

# Install turbo globally
RUN npm install -g turbo

# Create workspace directory
WORKDIR /workspace

# Set up user permissions
RUN chown -R node:node /workspace
USER node

# Set up pnpm store directory
RUN mkdir -p /home/<USER>/.local/share/pnpm

# Set environment variables
ENV PNPM_HOME="/home/<USER>/.local/share/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Default command
CMD ["sleep", "infinity"]
