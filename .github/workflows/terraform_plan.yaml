name: Terraform Plan

on:
  pull_request:
    paths:
      - 'infra/dev/**'
      - 'infra/bhc/**'
      - '.github/workflows/terraform_plan.yaml'
      - '.github/workflows/terraform_apply.yaml'

jobs:
  plan:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      id-token: write
      contents: write
    strategy:
      matrix:
        include:
          - env: dev
            dir: dev
          - env: bhc-prd
            dir: bhc
    environment:
      name: ${{ matrix.env }}
    defaults:
      run:
        working-directory: infra/${{ matrix.dir }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Auth
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: ${{ vars.AWS_ROLE_ARN }}
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.9.4
      - uses: actions/setup-go@v5
      - run: go install github.com/reproio/terraform-j2md/cmd/terraform-j2md@v0.0.9
      - name: Terraform Init
        run: terraform init
      - name: Terraform Plan
        id: plan
        run: |
          terraform plan -no-color -out plan.tfplan >> plan_detail.md
          echo "## [${{ matrix.env }}] terraform plan" > plan.md
          terraform show -json plan.tfplan | terraform-j2md >> plan.md
          echo "<details><summary>Plan raw outputs</summary><pre><code>" >> plan.md
          cat plan_detail.md >> plan.md
          echo "</code></pre></details>" >> plan.md
      - name: Post Plan to PR
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: '[${{ matrix.env }}] terraform plan result'
          path: infra/${{ matrix.dir }}/plan.md
