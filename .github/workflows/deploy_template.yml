name: deploy
run-name: '${{ inputs.service }}-deploy [${{ inputs.environment }}: ${{ github.ref_name }}]'

on:
  workflow_call:
    inputs:
      service:
        required: true
        type: string
        description: 'Service name (frontend, api, admin)'
      environment:
        required: true
        type: string
        description: 'Deployment environment (dev, stg, prd)'
      role_to_assume:
        required: true
        type: string
        description: 'AWS IAM role to assume'
      repository:
        required: true
        type: string
        description: 'ECR repository name'
      dockerfile_path:
        required: true
        type: string
        description: 'Path to the Dockerfile'
      cluster_name:
        required: true
        type: string
        description: 'ECS cluster name'
      service_name:
        required: true
        type: string
        description: 'ECS service name'
      FRONTEND_ENDPOINT:
        required: false
        type: string
        description: 'Frontend endpoint URL (only for frontend service)'

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    environment: ${{ inputs.environment }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.ref_name }}
      - name: auth
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: ${{ inputs.role_to_assume }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build and push frontend docker image
        if: ${{ inputs.service == 'frontend' }}
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{ inputs.dockerfile_path }}
          push: true
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ inputs.repository }}:latest
          build-args: |
            NEXT_PUBLIC_SENTRY_DSN=${{ vars.WEB_FRONTEND_SENTRY_DSN }}
            NEXT_PUBLIC_GA_MEASUREMENT_ID=${{ vars.WEB_FRONTEND_GA_MEASUREMENT_ID }}
            FRONTEND_ENDPOINT=${{ inputs.FRONTEND_ENDPOINT }}

      - name: Build and push ${{ inputs.service }} docker image
        if: ${{ inputs.service != 'frontend' }}
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{ inputs.dockerfile_path }}
          push: true
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ inputs.repository }}:latest

      - name: Update ECS service
        run: |
          aws ecs update-service \
            --cluster ${{ inputs.cluster_name }} \
            --service ${{ inputs.service_name }} \
            --force-new-deployment

      - name: Wait for ECS service to be stable
        run: |
          echo "Waiting for ECS service to reach stable state..."
          aws ecs wait services-stable \
            --cluster ${{ inputs.cluster_name }} \
            --services ${{ inputs.service_name }}
          echo "ECS service is now stable and healthy!"
