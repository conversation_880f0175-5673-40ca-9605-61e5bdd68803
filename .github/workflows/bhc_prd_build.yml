name: BHC PRD Deploy

on:
  release:
    types: [published]

jobs:
  check-diff:
    runs-on: ubuntu-latest
    outputs:
      prisma: ${{ steps.prisma.outputs.changed }}
      web_api: ${{ steps.web_api.outputs.changed }}
      core_api: ${{ steps.core_api.outputs.changed }}
      web_frontend: ${{ steps.web_frontend.outputs.changed }}
      web_admin: ${{ steps.web_admin.outputs.changed }}
      core_admin: ${{ steps.core_admin.outputs.changed }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.release.tag_name }}

      - name: Determine diff base
        id: base
        run: |
          CURRENT_TAG="${{ github.event.release.tag_name }}"
          BASE_COMMIT=""
          git fetch --force --tags --prune
          PREV_TAG=$(curl -sS -H "Authorization: token $GITHUB_TOKEN" -H "Accept: application/vnd.github+json" \
            "https://api.github.com/repos/${GITHUB_REPOSITORY}/releases?per_page=10" \
            | jq -r 'map(select(.draft==false and .prerelease==false)) | .[1].tag_name // empty')
          # 初回リリース: 直前のリリースが無ければ全面デプロイ対象とするためBASEを空のままにする
          if [ -z "$PREV_TAG" ]; then
            echo "base=" >> $GITHUB_OUTPUT
            exit 0
          fi
          if [ -n "$PREV_TAG" ]; then
            BASE_COMMIT=$(git rev-list -n 1 "$PREV_TAG")
          fi
          # 直前リリースがあるのにコミット取得に失敗した場合のみ親コミットへフォールバック
          if [ -z "$BASE_COMMIT" ] && git rev-parse "${{ github.sha }}^" >/dev/null 2>&1; then
            BASE_COMMIT="$(git rev-parse ${{ github.sha }}^)"
          fi
          echo "base=$BASE_COMMIT" >> $GITHUB_OUTPUT
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - id: prisma
        run: |
          BASE="${{ steps.base.outputs.base }}"
          HEAD="${{ github.sha }}"
          if [ -z "$BASE" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          if git diff --name-only "$BASE" "$HEAD" | grep '^packages/prisma/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: web_api
        run: |
          BASE="${{ steps.base.outputs.base }}"
          HEAD="${{ github.sha }}"
          if [ -z "$BASE" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          if git diff --name-only "$BASE" "$HEAD" | grep '^apps/web/api/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: core_api
        run: |
          BASE="${{ steps.base.outputs.base }}"
          HEAD="${{ github.sha }}"
          if [ -z "$BASE" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          if git diff --name-only "$BASE" "$HEAD" | grep '^apps/core/api/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: web_frontend
        run: |
          BASE="${{ steps.base.outputs.base }}"
          HEAD="${{ github.sha }}"
          if [ -z "$BASE" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          if git diff --name-only "$BASE" "$HEAD" | grep '^apps/web/frontend/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: web_admin
        run: |
          BASE="${{ steps.base.outputs.base }}"
          HEAD="${{ github.sha }}"
          if [ -z "$BASE" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          if git diff --name-only "$BASE" "$HEAD" | grep '^apps/web/admin/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: core_admin
        run: |
          BASE="${{ steps.base.outputs.base }}"
          HEAD="${{ github.sha }}"
          if [ -z "$BASE" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          if git diff --name-only "$BASE" "$HEAD" | grep '^apps/core/admin/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

  migrate:
    name: DB Migration
    needs: check-diff
    uses: ./.github/workflows/migrate.yml
    if: needs.check-diff.outputs.prisma == 'true'
    with:
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-web-migrate
      dockerfile_path: packages/prisma/docker/Dockerfile
      cluster_name: bhc-prd-cluster
      service_name: bhc-prd-web-migrate
      task_definition: bhc-web-migrate-task
      should_migrate: ${{ needs.check-diff.outputs.prisma }}
    secrets: inherit

  deploy-web-api:
    name: Deploy web/api
    needs: migrate
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.web_api == 'true' &&
      !failure() && !cancelled() &&
      (needs.migrate.result == 'success' || needs.migrate.result == 'skipped')
    with:
      service: api
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-web-api
      dockerfile_path: apps/web/api/docker/Dockerfile
      cluster_name: bhc-prd-cluster
      service_name: bhc-web-api-service
    secrets: inherit

  deploy-core-api:
    name: Deploy core/api
    needs: migrate
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.core_api == 'true' &&
      !failure() && !cancelled() &&
      (needs.migrate.result == 'success' || needs.migrate.result == 'skipped')
    with:
      service: api
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-core-api
      dockerfile_path: apps/core/api/docker/Dockerfile
      cluster_name: bhc-prd-core-cluster
      service_name: bhc-core-api-service
    secrets: inherit

  deploy-web-frontend:
    name: Deploy web/frontend
    needs: deploy-web-api
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.web_frontend == 'true' &&
      !failure() && !cancelled() &&
      (needs.deploy-web-api.result == 'success' || needs.deploy-web-api.result == 'skipped')
    with:
      service: frontend
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-web-frontend
      dockerfile_path: apps/web/frontend/Dockerfile
      cluster_name: bhc-prd-cluster
      service_name: bhc-web-frontend-service
      FRONTEND_ENDPOINT: https://bloominghorseclub.co.jp
    secrets: inherit

  deploy-web-admin:
    name: Deploy web/admin
    needs: deploy-web-api
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.web_admin == 'true' &&
      !failure() && !cancelled() &&
      (needs.deploy-web-api.result == 'success' || needs.deploy-web-api.result == 'skipped')
    with:
      service: admin
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-web-admin
      dockerfile_path: apps/web/admin/Dockerfile
      cluster_name: bhc-prd-cluster
      service_name: bhc-web-admin-service
    secrets: inherit

  deploy-core-admin:
    name: Deploy core/admin
    needs: deploy-core-api
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.core_admin == 'true' &&
      !failure() && !cancelled() &&
      (needs.deploy-core-api.result == 'success' || needs.deploy-core-api.result == 'skipped')
    with:
      service: admin
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-core-admin
      dockerfile_path: apps/core/admin/Dockerfile
      cluster_name: bhc-prd-core-cluster
      service_name: bhc-core-admin-service
    secrets: inherit
