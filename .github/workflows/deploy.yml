name: Deploy

on:
  push:
    branches:
      - main

jobs:
  check-diff:
    runs-on: ubuntu-latest
    outputs:
      prisma: ${{ steps.prisma.outputs.changed }}
      web_api: ${{ steps.web_api.outputs.changed }}
      core_api: ${{ steps.core_api.outputs.changed }}
      web_frontend: ${{ steps.web_frontend.outputs.changed }}
      web_admin: ${{ steps.web_admin.outputs.changed }}
      core_admin: ${{ steps.core_admin.outputs.changed }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - id: prisma
        run: |
          if git diff --name-only ${{ github.event.before }} ${{ github.sha }} | grep '^packages/prisma/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: web_api
        run: |
          if git diff --name-only ${{ github.event.before }} ${{ github.sha }} | grep '^apps/web/api/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: core_api
        run: |
          if git diff --name-only ${{ github.event.before }} ${{ github.sha }} | grep '^apps/core/api/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: web_frontend
        run: |
          if git diff --name-only ${{ github.event.before }} ${{ github.sha }} | grep '^apps/web/frontend/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: web_admin
        run: |
          if git diff --name-only ${{ github.event.before }} ${{ github.sha }} | grep '^apps/web/admin/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

      - id: core_admin
        run: |
          if git diff --name-only ${{ github.event.before }} ${{ github.sha }} | grep '^apps/core/admin/'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi

  migrate:
    name: DB Migration
    needs: check-diff
    uses: ./.github/workflows/migrate.yml
    if: needs.check-diff.outputs.prisma == 'true'
    with:
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-web-migrate
      dockerfile_path: packages/prisma/docker/Dockerfile
      cluster_name: hami-cluster
      service_name: hami-web-migrate
      task_definition: hami-web-migrate-task
      should_migrate: ${{ needs.check-diff.outputs.prisma }}
    secrets: inherit

  deploy-web-api:
    name: Deploy web/api
    needs: migrate
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.web_api == 'true' &&
      !failure() && !cancelled() &&
      (needs.migrate.result == 'success' || needs.migrate.result == 'skipped')
    with:
      service: api
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-web-api
      dockerfile_path: apps/web/api/docker/Dockerfile
      cluster_name: hami-cluster
      service_name: hami-web-api-service
    secrets: inherit

  deploy-core-api:
    name: Deploy core/api
    needs: migrate
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.core_api == 'true' &&
      !failure() && !cancelled() &&
      (needs.migrate.result == 'success' || needs.migrate.result == 'skipped')
    with:
      service: api
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-core-api
      dockerfile_path: apps/core/api/docker/Dockerfile
      cluster_name: hami-core-cluster
      service_name: hami-core-api-service
    secrets: inherit

  deploy-web-frontend:
    name: Deploy web/frontend
    needs: deploy-web-api
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.web_frontend == 'true' &&
      !failure() && !cancelled() &&
      (needs.deploy-web-api.result == 'success' || needs.deploy-web-api.result == 'skipped')
    with:
      service: frontend
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-web-frontend
      dockerfile_path: apps/web/frontend/Dockerfile
      cluster_name: hami-cluster
      service_name: hami-web-frontend-service
      FRONTEND_ENDPOINT: https://stg.bloominghorseclub.co.jp
    secrets: inherit

  deploy-web-admin:
    name: Deploy web/admin
    needs: deploy-web-api
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.web_admin == 'true' &&
      !failure() && !cancelled() &&
      (needs.deploy-web-api.result == 'success' || needs.deploy-web-api.result == 'skipped')
    with:
      service: admin
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-web-admin
      dockerfile_path: apps/web/admin/Dockerfile
      cluster_name: hami-cluster
      service_name: hami-web-admin-service
    secrets: inherit

  deploy-core-admin:
    name: Deploy core/admin
    needs: deploy-core-api
    uses: ./.github/workflows/deploy_template.yml
    if: needs.check-diff.outputs.core_admin == 'true' &&
      !failure() && !cancelled() &&
      (needs.deploy-core-api.result == 'success' || needs.deploy-core-api.result == 'skipped')
    with:
      service: admin
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-core-admin
      dockerfile_path: apps/core/admin/Dockerfile
      cluster_name: hami-core-cluster
      service_name: hami-core-admin-service
    secrets: inherit
