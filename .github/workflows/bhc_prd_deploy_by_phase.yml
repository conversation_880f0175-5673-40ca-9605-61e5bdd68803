name: BHC PRD Deploy by Phase

on:
  workflow_dispatch:
    inputs:
      phase:
        description: 'Which phase to run'
        required: true
        type: choice
        options:
          - migrate
          - web-api
          - core-api
          - web-frontend
          - web-admin
          - core-admin
          - all
        default: web-api

jobs:
  migrate:
    name: DB Migration
    uses: ./.github/workflows/migrate.yml
    if: ${{ inputs.phase == 'migrate' || inputs.phase == 'all' }}
    with:
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-web-migrate
      dockerfile_path: packages/prisma/docker/Dockerfile
      cluster_name: bhc-prd-cluster
      service_name: bhc-prd-web-migrate
      task_definition: bhc-web-migrate-task
      should_migrate: 'true'
    secrets: inherit

  deploy-web-api:
    name: Deploy web/api
    needs: migrate
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'web-api' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.migrate.result == 'success' || needs.migrate.result == 'skipped') }}
    with:
      service: api
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-web-api
      dockerfile_path: apps/web/api/docker/Dockerfile
      cluster_name: bhc-prd-cluster
      service_name: bhc-web-api-service
    secrets: inherit

  deploy-core-api:
    name: Deploy core/api
    needs: migrate
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'core-api' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.migrate.result == 'success' || needs.migrate.result == 'skipped') }}
    with:
      service: api
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-core-api
      dockerfile_path: apps/core/api/docker/Dockerfile
      cluster_name: bhc-prd-core-cluster
      service_name: bhc-core-api-service
    secrets: inherit

  deploy-web-frontend:
    name: Deploy web/frontend
    needs: deploy-web-api
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'web-frontend' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.deploy-web-api.result == 'success' || needs.deploy-web-api.result == 'skipped') }}
    with:
      service: frontend
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-web-frontend
      dockerfile_path: apps/web/frontend/Dockerfile
      cluster_name: bhc-prd-cluster
      service_name: bhc-web-frontend-service
      FRONTEND_ENDPOINT: https://bloominghorseclub.co.jp
    secrets: inherit

  deploy-web-admin:
    name: Deploy web/admin
    needs: deploy-web-api
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'web-admin' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.deploy-web-api.result == 'success' || needs.deploy-web-api.result == 'skipped') }}
    with:
      service: admin
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-web-admin
      dockerfile_path: apps/web/admin/Dockerfile
      cluster_name: bhc-prd-cluster
      service_name: bhc-web-admin-service
    secrets: inherit

  deploy-core-admin:
    name: Deploy core/admin
    needs: deploy-core-api
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'core-admin' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.deploy-core-api.result == 'success' || needs.deploy-core-api.result == 'skipped') }}
    with:
      service: admin
      environment: bhc-prd
      role_to_assume: arn:aws:iam::218903531167:role/bhc-prd-github-actions-role
      repository: bhc-prd-core-admin
      dockerfile_path: apps/core/admin/Dockerfile
      cluster_name: bhc-prd-core-cluster
      service_name: bhc-core-admin-service
    secrets: inherit
