name: Deploy by Phase

on:
  workflow_dispatch:
    inputs:
      phase:
        description: 'Which phase to run'
        required: true
        type: choice
        options:
          - migrate
          - web-api
          - core-api
          - web-frontend
          - web-admin
          - core-admin
          - all
        default: web-api

jobs:
  migrate:
    name: DB Migration
    uses: ./.github/workflows/migrate.yml
    if: ${{ inputs.phase == 'migrate' || inputs.phase == 'all' }}
    with:
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-web-migrate
      dockerfile_path: packages/prisma/docker/Dockerfile
      cluster_name: hami-cluster
      service_name: hami-web-migrate
      task_definition: hami-web-migrate-task
      should_migrate: 'true'
    secrets: inherit

  deploy-web-api:
    name: Deploy web/api
    needs: migrate
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'web-api' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.migrate.result == 'success' || needs.migrate.result == 'skipped') }}
    with:
      service: api
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-web-api
      dockerfile_path: apps/web/api/docker/Dockerfile
      cluster_name: hami-cluster
      service_name: hami-web-api-service
    secrets: inherit

  deploy-core-api:
    name: Deploy core/api
    needs: migrate
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'core-api' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.migrate.result == 'success' || needs.migrate.result == 'skipped') }}
    with:
      service: api
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-core-api
      dockerfile_path: apps/core/api/docker/Dockerfile
      cluster_name: hami-core-cluster
      service_name: hami-core-api-service
    secrets: inherit

  deploy-web-frontend:
    name: Deploy web/frontend
    needs: deploy-web-api
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'web-frontend' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.deploy-web-api.result == 'success' || needs.deploy-web-api.result == 'skipped') }}
    with:
      service: frontend
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-web-frontend
      dockerfile_path: apps/web/frontend/Dockerfile
      cluster_name: hami-cluster
      service_name: hami-web-frontend-service
      FRONTEND_ENDPOINT: https://stg.bloominghorseclub.co.jp
    secrets: inherit

  deploy-web-admin:
    name: Deploy web/admin
    needs: deploy-web-api
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'web-admin' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.deploy-web-api.result == 'success' || needs.deploy-web-api.result == 'skipped') }}
    with:
      service: admin
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-web-admin
      dockerfile_path: apps/web/admin/Dockerfile
      cluster_name: hami-cluster
      service_name: hami-web-admin-service
    secrets: inherit

  deploy-core-admin:
    name: Deploy core/admin
    needs: deploy-core-api
    uses: ./.github/workflows/deploy_template.yml
    if: ${{ (inputs.phase == 'core-admin' || inputs.phase == 'all') && !failure() && !cancelled() && (needs.deploy-core-api.result == 'success' || needs.deploy-core-api.result == 'skipped') }}
    with:
      service: admin
      environment: dev
      role_to_assume: arn:aws:iam::774305578476:role/github-actions-role
      repository: hami-core-admin
      dockerfile_path: apps/core/admin/Dockerfile
      cluster_name: hami-core-cluster
      service_name: hami-core-admin-service
    secrets: inherit
