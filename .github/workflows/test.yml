name: Test

on:
  pull_request:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  test:
    runs-on: ubuntu-24.04-arm-4core
    services:
      db:
        image: postgres:15
        ports:
          - 5432:5432
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: hami_test
        options: >-
          --health-cmd "pg_isready -U postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          --volume /tmp/postgres-socket:/var/run/postgresql
      mailpit:
        image: axllent/mailpit
        ports:
          - '1025:1025'
          - '8025:8025'
      minio:
        image: bitnamilegacy/minio:latest
        env:
          MINIO_ROOT_USER: minioadmin
          MINIO_ROOT_PASSWORD: minioadmin
          MINIO_DEFAULT_BUCKETS: test-bucket
        ports:
          - 9000:9000
    env:
      DATABASE_URL: postgresql://postgres:postgres@127.0.0.1:5432/hami_test
      FRONTEND_ENDPOINT: http://localhost:3000
      SMTP_HOST: 127.0.0.1
      SMTP_PORT: 1025
      MAILPIT_URL: http://127.0.0.1:8025
      JWT_SECRET: dev-secret
      AWS_ACCESS_KEY_ID: minioadmin
      AWS_SECRET_ACCESS_KEY: minioadmin
      AWS_REGION: ap-northeast-1
      S3_BUCKET_NAME: test-bucket
      S3_ENDPOINT: http://127.0.0.1:9000
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Spell Check
        run: pnpm spell-check

      - name: Frontend Lint
        run: pnpm --filter @hami/web-frontend lint

      - name: Admin Lint
        run: pnpm --filter @hami/web-admin lint

      - name: Core Admin Lint
        run: pnpm --filter @hami/core-admin lint

      - name: Migrate
        run: pnpm --filter @hami/prisma exec prisma migrate deploy

      - name: Generate Prisma Client
        run: pnpm --filter @hami/prisma exec prisma generate

      - name: Build
        run: pnpm --filter @hami/prisma build

      - name: Type Check Web API
        run: pnpm --filter @hami/web-api exec tsc -p tsconfig.json --noEmit

      - name: Type Check Core API
        run: pnpm --filter @hami/core-api exec tsc -p tsconfig.json --noEmit

      - name: Run tests
        run: pnpm --filter @hami/web-api test

      - name: Run tests
        run: pnpm --filter @hami/core-api test

  build:
    runs-on: ubuntu-24.04-arm-4core
    env:
      FRONTEND_ENDPOINT: https://hami-app.com

    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build Frontend
        run: pnpm --filter @hami/web-frontend build

      - name: Build Web Admin
        run: pnpm --filter @hami/web-admin build

      - name: Build Core Admin
        run: pnpm --filter @hami/core-admin build

  frontend-e2e:
    runs-on: ubuntu-24.04-arm-4core
    services:
      db:
        image: postgres:15
        ports:
          - 5432:5432
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: hami_test
        options: >-
          --health-cmd "pg_isready -U postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      mailpit:
        image: axllent/mailpit
        ports:
          - '1025:1025'
          - '8025:8025'
      minio:
        image: bitnamilegacy/minio:latest
        env:
          MINIO_ROOT_USER: minioadmin
          MINIO_ROOT_PASSWORD: minioadmin
          MINIO_DEFAULT_BUCKETS: test-bucket
        ports:
          - 9000:9000
    env:
      DATABASE_URL: postgresql://postgres:postgres@127.0.0.1:5432/hami_test
      FRONTEND_ENDPOINT: http://localhost:3000
      SMTP_HOST: 127.0.0.1
      SMTP_PORT: 1025
      MAILPIT_URL: http://127.0.0.1:8025
      JWT_SECRET: dev-secret
      AWS_ACCESS_KEY_ID: minioadmin
      AWS_SECRET_ACCESS_KEY: minioadmin
      AWS_REGION: ap-northeast-1
      S3_BUCKET_NAME: test-bucket
      S3_ENDPOINT: http://127.0.0.1:9000
      WEB_API_ENDPOINT: http://localhost:8080/
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Migrate Database
        run: pnpm --filter @hami/prisma exec prisma migrate deploy

      - name: Generate Prisma Client
        run: pnpm --filter @hami/prisma exec prisma generate

      - name: Build Prisma
        run: pnpm --filter @hami/prisma build

      - name: Start Web API Server
        run: pnpm --filter @hami/web-api dev &
        env:
          PORT: 8080

      - name: Wait for API Server
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:8080/ 2>/dev/null; do sleep 2; done'
          echo "API Server is ready"

      - name: Seed Database
        run: |
          echo "Creating test data..."
          pnpm --filter @hami/prisma exec prisma db seed
          echo "Test data created successfully"

      - name: Setup Playwright with Cache
        uses: ./.github/actions/setup-playwright
        with:
          working-directory: apps/web/frontend
          browsers: chromium
          install-deps: 'true'

      - name: Run Frontend E2E Tests
        run: |
          echo "Starting E2E tests with API endpoint: http://localhost:8080/"
          pnpm --filter @hami/web-frontend test:fast
        env:
          CI: true
          WEB_API_ENDPOINT: http://localhost:8080/

      - name: Upload Playwright Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: apps/web/frontend/playwright-report/
          retention-days: 30

      - name: Upload Test Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: apps/web/frontend/test-results/
          retention-days: 30
