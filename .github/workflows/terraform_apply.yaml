name: Terraform Apply

on:
  push:
    branches:
      - main
    paths:
      - 'infra/dev/**'
      - 'infra/bhc/**'
      - '.github/workflows/terraform_plan.yaml'
      - '.github/workflows/terraform_apply.yaml'

jobs:
  apply:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix:
        include:
          - env: dev
            dir: dev
          - env: bhc-prd
            dir: bhc
    environment: ${{ matrix.env }}
    defaults:
      run:
        working-directory: infra/${{ matrix.dir }}
    steps:
      - uses: actions/checkout@v4
      - name: Auth
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: ${{ vars.AWS_ROLE_ARN }}
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.9.4
      - name: Terraform Init
        run: terraform init
      - name: Terraform Apply
        run: terraform apply -auto-approve
