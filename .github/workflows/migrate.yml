name: DB Migration

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: 'Deployment environment (dev, stg, prd)'
      role_to_assume:
        required: true
        type: string
        description: 'AWS IAM role to assume'
      repository:
        required: true
        type: string
        description: 'ECR repository name'
      dockerfile_path:
        required: true
        type: string
        description: 'Path to the Dockerfile'
      cluster_name:
        required: true
        type: string
        description: 'ECS cluster name'
      service_name:
        required: true
        type: string
        description: 'ECS service name'
      task_definition:
        required: true
        type: string
        description: 'ECS task definition name'
      should_migrate:
        required: false
        type: string
        default: 'true'
        description: 'Whether to actually run migration or just skip.'

jobs:
  migrate:
    name: DB Migration
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    environment: ${{ inputs.environment }}
    steps:
      - name: Check should_migrate
        run: |
          if [ "${{ inputs.should_migrate }}" != "true" ]; then
            echo "No changes detected. Skipping migration."
            exit 0
          fi
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.ref_name }}
      - name: auth
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: ${{ inputs.role_to_assume }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build and push migrate docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{ inputs.dockerfile_path }}
          push: true
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ inputs.repository }}:latest
      - name: Run migration ECS task
        run: |
          TASK_ARN=$(aws ecs run-task \
            --cluster ${{ inputs.cluster_name }} \
            --task-definition ${{ inputs.task_definition }} \
            --launch-type FARGATE \
            --network-configuration "awsvpcConfiguration={subnets=[${{ secrets.AWS_PRIVATE_SUBNET_A_ID }},${{ secrets.AWS_PRIVATE_SUBNET_B_ID }}],securityGroups=[${{ secrets.AWS_ECS_API_SECURITY_GROUP_ID }}],assignPublicIp=DISABLED}" \
            --count 1 \
            --query "tasks[0].taskArn" \
            --output text)
          echo "Task ARN: $TASK_ARN"
          aws ecs wait tasks-stopped --cluster ${{ inputs.cluster_name }} --tasks $TASK_ARN
          EXIT_CODE=$(aws ecs describe-tasks --cluster ${{ inputs.cluster_name }} --tasks $TASK_ARN --query "tasks[0].containers[0].exitCode" --output text)
          if [ "$EXIT_CODE" == "0" ]; then
            echo "Migration succeeded"
          else
            echo "Migration failed (exit code: $EXIT_CODE)"
            exit 1
          fi
        env:
          AWS_REGION: ap-northeast-1
