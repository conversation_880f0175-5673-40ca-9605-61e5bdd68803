# Setup Playwright with <PERSON>ache Action

このComposite Actionは、Playwrightのブラウザバイナリをキャッシュして、CI実行時間を大幅に短縮します。

## 機能

- Playwrightのバージョンを自動検出
- ブラウザバイナリのキャッシュ
- キャッシュヒット時はシステム依存関係のみインストール
- 指定したブラウザのみインストール（デフォルト: chromium）

## 使用方法

```yaml
- name: Setup Playwright with <PERSON><PERSON>
  uses: ./.github/actions/setup-playwright
  with:
    working-directory: apps/web/frontend
    browsers: chromium
    install-deps: 'true'
```

## パラメータ

| パラメータ          | 説明                                 | 必須 | デフォルト |
| ------------------- | ------------------------------------ | ---- | ---------- |
| `working-directory` | Playwrightの作業ディレクトリ         | No   | `.`        |
| `browsers`          | インストールするブラウザ             | No   | `chromium` |
| `install-deps`      | システム依存関係をインストールするか | No   | `true`     |

## パフォーマンス改善

- **初回実行**: 通常通りブラウザをダウンロード（約3分）
- **2回目以降**: キャッシュから復元（約10-30秒）
- **改善効果**: 約90%の時間短縮

## キャッシュキー

キャッシュキーは以下の要素で構成されます：

- OS (`${{ runner.os }}`)
- Playwrightバージョン
- インストールするブラウザ

これにより、Playwrightのバージョンが変更された場合のみ新しいキャッシュが作成されます。

## 仕組み

1. **キャッシュチェック**: 既存のブラウザキャッシュを確認
2. **キャッシュミス**: ブラウザとシステム依存関係を完全インストール
3. **キャッシュヒット**: システム依存関係のみインストール（ブラウザはキャッシュから復元）
