name: 'Setup Playwright with Cache'
description: 'Setup Playwright with browser caching for faster CI runs'
inputs:
  working-directory:
    description: 'Working directory for Playwright'
    required: false
    default: '.'
  browsers:
    description: 'Browsers to install (default: chromium)'
    required: false
    default: 'chromium'
  install-deps:
    description: 'Install system dependencies'
    required: false
    default: 'true'

runs:
  using: 'composite'
  steps:
    - name: Get Playwright version
      id: playwright-version
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: |
        VERSION=$(pnpm exec playwright --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -1)
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "Playwright version: $VERSION"

    - name: <PERSON>ache Playwright browsers
      uses: actions/cache@v4
      id: playwright-cache
      with:
        path: ~/.cache/ms-playwright
        key: ${{ runner.os }}-playwright-${{ steps.playwright-version.outputs.version }}-${{ inputs.browsers }}
        restore-keys: |
          ${{ runner.os }}-playwright-${{ steps.playwright-version.outputs.version }}-
          ${{ runner.os }}-playwright-

    - name: Install Playwright Browsers
      if: steps.playwright-cache.outputs.cache-hit != 'true'
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: |
        echo "Cache miss - installing browsers: ${{ inputs.browsers }}"
        if [ "${{ inputs.install-deps }}" = "true" ]; then
          pnpm exec playwright install ${{ inputs.browsers }} --with-deps
        else
          pnpm exec playwright install ${{ inputs.browsers }}
        fi

    - name: Install Playwright system dependencies only
      if: steps.playwright-cache.outputs.cache-hit == 'true' && inputs.install-deps == 'true'
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: |
        echo "Cache hit - installing system dependencies only for: ${{ inputs.browsers }}"
        pnpm exec playwright install-deps ${{ inputs.browsers }}

    - name: Verify Playwright installation
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: |
        echo "Verifying Playwright installation..."
        pnpm exec playwright --version
        echo "Available browsers:"
        ls -la ~/.cache/ms-playwright/ || echo "Browser cache directory not found"
