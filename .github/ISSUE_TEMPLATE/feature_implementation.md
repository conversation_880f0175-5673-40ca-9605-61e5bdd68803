---
name: 機能実装
about: API・機能実装のためのissueテンプレート
title: '[web/api] '
labels: 'web/api,api,enhancement'
assignees: ''
---

<!--
タイトル命名規則:
- [web/api] フロントエンド向けAPI実装
- [admin/api] 管理画面向けAPI実装
- [core/api] 基幹システムAPI実装
- [database] データベース関連実装
- [infra] インフラ関連実装
-->

# 概要

<!-- 実装する機能の概要を簡潔に記述 -->

## 背景

<!-- なぜこの機能が必要なのか -->

## 実装内容

### API Schema

<!-- 必要な場合のみ記述 -->

```protobuf
// 新しいRPCやメッセージの定義
```

### 実装するファイル

- [ ] `packages/web-api-schema/proto/` - スキーマ定義
- [ ] `apps/web/api/src/routes/` - APIハンドラー
- [ ] `apps/web/api/src/repositories/` - Repository関数
- [ ] `apps/web/frontend/api_clients/` - APIクライアント
- [ ] その他:

### 主な機能

- [ ]
- [ ]
- [ ]

## 技術的考慮事項

<!-- 重要なポイントのみ -->

- **セキュリティ**:
- **認証**: 必要/不要
- **その他**:

## 完了条件

- [ ] API実装
- [ ] テスト実装
- [ ] `make routes` 実行
- [ ] 動作確認

## 関連Issue

<!-- あれば記述 -->

## 優先度

<!-- High/Medium/Low -->

## 参考実装

## <!-- 参考にできるファイルがあれば -->
