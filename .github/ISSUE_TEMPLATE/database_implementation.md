---
name: データベース実装
about: データベース・スキーマ実装のためのissueテンプレート
title: '[database] '
labels: 'database,enhancement'
assignees: ''
---

<!-- 
タイトル命名規則:
- [database] データベース関連実装の概要
例: [database] ユーザーテーブルのスキーマ実装
-->

# 概要

<!-- 実装するデータベース機能の概要を簡潔に記述 -->

## 背景

<!-- なぜこのデータベース変更が必要なのか -->

## 実装内容

### スキーマ定義

```prisma
// 新しいモデルやフィールドの定義
```

### 実装するファイル

- [ ] `packages/prisma/prisma/schema/` - スキーマ定義
- [ ] `packages/prisma/prisma/migrations/` - マイグレーションファイル
- [ ] `packages/prisma/prisma/factories/` - テストデータファクトリ
- [ ] `packages/prisma/prisma/seed.ts` - シードデータ追加

### 主な機能

- [ ] 
- [ ] 
- [ ] 

## 技術的考慮事項

### データ設計
- **データ型**: 
- **リレーション**: 
- **インデックス**: 
- **制約**: 

### パフォーマンス
- **クエリ最適化**: 
- **インデックス設計**: 

### データ整合性
- **外部キー制約**: 
- **一意制約**: 
- **バリデーション**: 

## 完了条件

- [ ] Prismaスキーマファイル作成
- [ ] マイグレーション実行
- [ ] ファクトリ関数実装
- [ ] シードデータ追加
- [ ] `make prisma-generate` 実行
- [ ] 動作確認

## 関連Issue

<!-- 関連するAPIやその他のissueがあれば記述 -->

## 優先度

<!-- High/Medium/Low -->

## 参考実装

<!-- 参考にできるスキーマファイルがあれば -->

## 備考

<!-- その他の重要な情報 -->
