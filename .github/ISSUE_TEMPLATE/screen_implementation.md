---
name: 画面実装
about: 画面実装のためのissueテンプレート
title: '[web/frontend] '
labels: 'web/frontend,frontend,enhancement'
assignees: ''
---

<!--
タイトル命名規則:
- [web/frontend] フロントエンド画面実装
- [web/admin] 管理画面実装
- [core/admin] 基幹システム管理画面実装
-->

# 概要

<!-- 実装する画面の概要を簡潔に記述 -->

## 背景

<!-- なぜこの画面が必要なのか、現在の状況 -->

## 実装内容

### ファイル

- [ ] `apps/web/frontend/app/` - メインページ
- [ ] `apps/web/frontend/app/components/` - 関連コンポーネント

### 主な機能

- [ ]
- [ ]
- [ ]

### ワイヤーフレームスクリーンショット

<!-- 画面のスクリーンショットを貼り付け -->

### Figmaリンク

<!-- Figmaのリンクがあれば記載 -->

## 技術的考慮事項

### API連携

- 使用するAPIクライアント:
- エラーハンドリング:
- ローディング状態:

### UI/UX

- レスポンシブ対応:
- アクセシビリティ対応:
- モバイル対応:

## 完了条件

- [ ] 画面が正常に表示される
- [ ] 主要機能が正常に動作する
- [ ] エラーハンドリングが適切に実装されている
- [ ] レスポンシブデザインが適切に動作する
- [ ] アクセシビリティ要件を満たしている

## 関連Issue

<!-- 関連するAPIやその他のissueがあれば記述 -->

## 優先度

<!-- High/Medium/Low -->

## 参考実装

<!-- 参考にできるファイルや画面があれば -->

## 備考

<!-- その他の重要な情報 -->
