import { AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@prisma/client';
import { defineAnnualBundleHorseFactory } from '../../src/__generated__/fabbrica';
import { horseFactory } from './horse_factory';
import { annualBundleFactory } from './annual_bundle_factory';

export const annualBundleHorseFactory = defineAnnualBundleHorseFactory({
    defaultData: () => ({
        horse: horseFactory,
        annualBundle: annualBundleFactory,
    }),
});

