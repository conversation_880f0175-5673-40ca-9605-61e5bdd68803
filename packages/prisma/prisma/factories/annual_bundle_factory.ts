import { AnnualBundlePublishStatus, AnnualBundleRecruitmentStatus } from '@prisma/client';
import { defineAnnualBundleFactory } from '../../src/__generated__/fabbrica';

export const annualBundleFactory = defineAnnualBundleFactory({
    defaultData: ({ seq }) => ({
        fiscalYear: 2020 + seq,
        name: '年度バンドル',
        shares: 300,
        publishStatus: AnnualBundlePublishStatus.PUBLIC,
        recruitmentStatus: AnnualBundleRecruitmentStatus.ACTIVE,
    }),
});

