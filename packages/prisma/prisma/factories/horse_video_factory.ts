import { PublishStatus } from '@prisma/client';
import { defineHorseVideoFactory } from '../../src/__generated__/fabbrica';
import { horseFactory } from './horse_factory';

export const horseVideoFactory = defineHorseVideoFactory({
  defaultData: ({ seq }) => {
    const now = new Date();
    const d = new Date(now.getTime() - seq * 24 * 60 * 60 * 1000); // seq日前

    return {
      horse: horseFactory,
      videoYear: d.getFullYear(),
      videoMonth: d.getMonth() + 1,
      videoDay: d.getDate(),
      publishStatus: PublishStatus.DRAFT,
      title: `調教動画 ${seq + 1}`,
      description: `坂路での調教動画（サンプル ${seq + 1}）です。フォームや反応を確認できます。`,
      youtubeVideoId: `sample_youtube_id_${seq + 1}`,
      startAtSeconds: seq % 2 === 0 ? 0 : undefined,
      thumbnailImagePath: undefined,
    };
  },
  traits: {
    published: {
      data: {
        publishStatus: PublishStatus.PUBLISHED,
      },
    },
    today: {
      data: () => {
        const today = new Date();
        return {
          videoYear: today.getFullYear(),
          videoMonth: today.getMonth() + 1,
          videoDay: today.getDate(),
        };
      },
    },
    withThumbnail: {
      data: {
        thumbnailImagePath: 's3://bucket/horse-videos/thumbs/sample.jpg',
      },
    },
    withStartAt: {
      data: {
        startAtSeconds: 30,
      },
    },
  },
});

