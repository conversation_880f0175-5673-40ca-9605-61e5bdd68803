import { logSeedProgress, logSeedComplete } from '../utils/common';
import { Horse } from '@prisma/client';
import { annualBundleFactory } from '../../factories/annual_bundle_factory';
import { annualBundleHorseFactory } from '../../factories/annual_bundle_horse_factory';

/**
 * 馬・馬プロフィールのシードデータを作成
 */
export const seedAnnualBundles = async (horses: Horse[]) => {
  logSeedProgress('Creating annual bundles...');

  // 馬の年度の一意な配列を作成
  const fiscalYears = horses.map((horse) => horse.recruitmentYear);
  const uniqueFiscalYears = [...new Set(fiscalYears)];

  // 一括購入パッケージを作成
  const annualBundles = await annualBundleFactory.createList(uniqueFiscalYears.map((fiscalYear) => ({ fiscalYear })));

  // 一括購入パッケージと馬を紐づける
  await annualBundleHorseFactory.createList(
    annualBundles.map((bundle) => ({
      annualBundle: { connect: { annualBundleId: bundle.annualBundleId } },
      horse: { connect: { horseId: horses.find((horse) => horse.recruitmentYear === bundle.fiscalYear)!.horseId } },
    }))
  );

  logSeedComplete('Annual bundles');

  // 作成した馬を返す（他のseedで使用するため）
  return annualBundles;
};

