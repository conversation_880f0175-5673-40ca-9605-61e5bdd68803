import { membershipApplicationFactory } from '../../factories/membership_application_factory';
import { prisma, logSeedProgress, logSeedComplete } from '../utils/common';
import { ApplicantType } from '@prisma/client';

/**
 * 法人申し込み（MembershipApplication: CORPORATE）のシードデータを作成
 * - Memberレコードは作成しない（法人は対象外）
 * - 実質的支配者申告（BeneficialOwnerDeclaration）を1件ずつ付与
 */
export const seedCorporateMembershipApplications = async () => {
  logSeedProgress('Creating corporate membership applications...');

  // 1. 株式会社テスト商事
  const corpApp1 = await membershipApplicationFactory.create({
    applicantType: ApplicantType.CORPORATE,
    // 個人用フィールドはnull
    firstName: null,
    lastName: null,
    firstNameKana: null,
    lastNameKana: null,
    birthYear: null,
    birthMonth: null,
    birthDay: null,
    // 法人用フィールド
    corporateName: '株式会社テスト商事',
    corporateNameKana: 'カブシキガイシャテストショウジ',
    representativeName: '山田 太郎',
    representativeNameKana: 'ヤマダ タロウ',
    representativePosition: '代表取締役',
    corporateNumber: '1234567890123',
    establishedYear: 2001,
    establishedMonth: 4,
    establishedDay: 1,
    // 共通フィールド
    postalCode: '100-0001',
    prefecture: '東京都',
    address: '千代田区千代田1-1',
    apartment: 'テストビル10F',
    phoneNumber: '03-1234-5678',
  });

  await prisma.beneficialOwnerDeclaration.create({
    data: {
      membershipApplication: { connect: { membershipApplicationId: corpApp1.membershipApplicationId } },
      beneficialOwnerName: '佐藤 花子',
      beneficialOwnerBirthYear: 1985,
      beneficialOwnerBirthMonth: 7,
      beneficialOwnerBirthDay: 15,
      beneficialOwnerPostalCode: '150-0002',
      beneficialOwnerPrefecture: '東京都',
      beneficialOwnerAddress: '渋谷区渋谷2-2-2',
      beneficialOwnerApartment: 'コーポABC 202',
      declarantName: '山田 太郎',
      declarantPosition: '代表取締役',
      isConfirmed: true,
      confirmedAt: new Date(),
    },
  });

  // 2. テスト工業株式会社
  const corpApp2 = await membershipApplicationFactory.create({
    applicantType: ApplicantType.CORPORATE,
    firstName: null,
    lastName: null,
    firstNameKana: null,
    lastNameKana: null,
    birthYear: null,
    birthMonth: null,
    birthDay: null,
    corporateName: 'テスト工業株式会社',
    corporateNameKana: 'テストコウギョウカブシキガイシャ',
    representativeName: '鈴木 次郎',
    representativeNameKana: 'スズキ ジロウ',
    representativePosition: '代表取締役社長',
    corporateNumber: '9876543210123',
    establishedYear: 1998,
    establishedMonth: 11,
    establishedDay: 20,
    postalCode: '530-0001',
    prefecture: '大阪府',
    address: '大阪市北区梅田1-1-1',
    apartment: '梅田タワー21F',
    phoneNumber: '06-1111-2222',
  });

  await prisma.beneficialOwnerDeclaration.create({
    data: {
      membershipApplication: { connect: { membershipApplicationId: corpApp2.membershipApplicationId } },
      beneficialOwnerName: '高橋 三郎',
      beneficialOwnerBirthYear: 1979,
      beneficialOwnerBirthMonth: 3,
      beneficialOwnerBirthDay: 9,
      beneficialOwnerPostalCode: '540-0008',
      beneficialOwnerPrefecture: '大阪府',
      beneficialOwnerAddress: '大阪市中央区大手前3-3-3',
      beneficialOwnerApartment: null,
      declarantName: '鈴木 次郎',
      declarantPosition: '代表取締役社長',
      isConfirmed: false,
      confirmedAt: null,
    },
  });

  // 3. 合同会社サンプル
  const corpApp3 = await membershipApplicationFactory.create({
    applicantType: ApplicantType.CORPORATE,
    firstName: null,
    lastName: null,
    firstNameKana: null,
    lastNameKana: null,
    birthYear: null,
    birthMonth: null,
    birthDay: null,
    corporateName: '合同会社サンプル',
    corporateNameKana: 'ゴウドウガイシャサンプル',
    representativeName: '田中 四郎',
    representativeNameKana: 'タナカ シロウ',
    representativePosition: '業務執行社員',
    corporateNumber: '5555555555555',
    establishedYear: 2015,
    establishedMonth: 1,
    establishedDay: 5,
    postalCode: '060-0001',
    prefecture: '北海道',
    address: '札幌市中央区北一条西1-1-1',
    apartment: null,
    phoneNumber: '************',
  });

  await prisma.beneficialOwnerDeclaration.create({
    data: {
      membershipApplication: { connect: { membershipApplicationId: corpApp3.membershipApplicationId } },
      beneficialOwnerName: '伊藤 五郎',
      beneficialOwnerBirthYear: 1990,
      beneficialOwnerBirthMonth: 12,
      beneficialOwnerBirthDay: 1,
      beneficialOwnerPostalCode: '064-0804',
      beneficialOwnerPrefecture: '北海道',
      beneficialOwnerAddress: '札幌市中央区南四条西4-4-4',
      beneficialOwnerApartment: 'サンプルマンション101',
      declarantName: '田中 四郎',
      declarantPosition: '業務執行社員',
      isConfirmed: true,
      confirmedAt: new Date(),
    },
  });

  logSeedComplete('Corporate membership applications');
};

