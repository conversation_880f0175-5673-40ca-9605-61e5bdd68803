import { membershipApplicationFactory } from '../../factories/membership_application_factory';
import {
  defineMembershipApplicationReviewLogFactory,
  defineMembershipApplicationDocumentGroupReviewLogFactory,
} from '../../../src/__generated__/fabbrica';
import { ReviewType } from '@prisma/client';
import { logSeedProgress, logSeedComplete, ADMIN_EMAIL } from '../utils/common';

/**
 * 入会申込: 一次審査は承認済み、コンプライアンス未承認のデータを作成
 */
export const seedCompliancePendingMembershipApplications = async (count = 3) => {
  logSeedProgress('Creating membership applications approved by primary, pending compliance...');

  const applicationReviewLogFactory = defineMembershipApplicationReviewLogFactory({
    defaultData: () => ({
      reviewer: ADMIN_EMAIL,
      reviewType: ReviewType.APPROVE,
      // 型要件で必須のためデフォルトは関連ファクトリを設定（呼び出し側で上書き）
      membershipApplication: membershipApplicationFactory,
    }),
  });

  const documentGroupReviewLogFactory = defineMembershipApplicationDocumentGroupReviewLogFactory({
    defaultData: () => ({
      reviewer: ADMIN_EMAIL,
      reviewType: ReviewType.APPROVE,
      // 型要件で必須のためデフォルトは関連ファクトリを設定（呼び出し側で上書き）
      membershipApplication: membershipApplicationFactory,
    }),
  });

  for (let i = 0; i < count; i++) {
    // 申込を作成（メール認証付き）
    const app = await membershipApplicationFactory.create();

    // 一次審査（申込/書類）を承認ログで付与
    await applicationReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: app.membershipApplicationId } },
    });
    await documentGroupReviewLogFactory.create({
      membershipApplication: { connect: { membershipApplicationId: app.membershipApplicationId } },
    });

    // コンプライアンス側のレビューは未作成（＝未承認として表示される）
  }

  logSeedComplete('Compliance-pending membership applications');
};
