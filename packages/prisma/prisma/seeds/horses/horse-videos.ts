import { horseVideoFactory } from '../../factories/horse_video_factory';
import { prisma, logSeedProgress, logSeedComplete } from '../utils/common';

/**
 * 馬の動画（YouTube埋め込み）シードデータを作成
 */
export const seedHorseVideos = async (horses: any) => {
  logSeedProgress('Creating horse videos...');

  const { sundaytc202512 } = horses;

  // 特定の馬（例）に対して6件生成（最新3件は公開）
  for (let i = 0; i < 6; i++) {
    await horseVideoFactory.create({
      horse: { connect: { horseId: sundaytc202512.horseId } },
      publishStatus: i < 3 ? 'PUBLISHED' : 'DRAFT',
      title: `近況動画 ${i + 1}`,
      youtubeVideoId: `sundaytc202512_vid_${i + 1}`,
    });
  }

  // リクエストに基づき、募集年=2025・募集番号=1 の馬に対して10件の公開動画を作成
  const target2025_1 = await prisma.horse.findFirst({
    where: { recruitmentYear: 2025, recruitmentNo: 1 },
    select: { horseId: true },
  });

  if (target2025_1) {
    // 最新動画（埋め込み対象）：指定のYouTube ID
    await horseVideoFactory.use('published').create({
      horse: { connect: { horseId: target2025_1.horseId } },
      title: '最新動画',
      youtubeVideoId: 'HHJccBlrLYQ',
      videoYear: 2025,
      videoMonth: 8,
      videoDay: 30,
    });

    // 追加の9件（一覧向け・公開）: 日付は少しずつ過去に設定
    const extra = [
      { y: 2025, m: 8, d: 29, id: 'recruit2025_1_vid_02' },
      { y: 2025, m: 8, d: 28, id: 'recruit2025_1_vid_03' },
      { y: 2025, m: 8, d: 27, id: 'recruit2025_1_vid_04' },
      { y: 2025, m: 8, d: 26, id: 'recruit2025_1_vid_05' },
      { y: 2025, m: 8, d: 25, id: 'recruit2025_1_vid_06' },
      { y: 2025, m: 8, d: 24, id: 'recruit2025_1_vid_07' },
      { y: 2025, m: 8, d: 23, id: 'recruit2025_1_vid_08' },
      { y: 2025, m: 8, d: 22, id: 'recruit2025_1_vid_09' },
      { y: 2025, m: 8, d: 21, id: 'recruit2025_1_vid_10' },
    ];

    for (const [i, v] of extra.entries()) {
      await horseVideoFactory.use('published').create({
        horse: { connect: { horseId: target2025_1.horseId } },
        title: `募集2025/1 動画 #${i + 2}`,
        youtubeVideoId: v.id,
        videoYear: v.y,
        videoMonth: v.m,
        videoDay: v.d,
      });
    }
  }

  // 他の馬にも各1件ずつ公開動画を追加
  const otherHorses = await prisma.horse.findMany({
    take: 3,
    where: { horseId: { not: sundaytc202512.horseId } },
    orderBy: { horseId: 'asc' },
  });

  for (const [idx, horse] of otherHorses.entries()) {
    await horseVideoFactory.use('published').create({
      horse: { connect: { horseId: horse.horseId } },
      title: `公開動画 ${idx + 1}`,
      youtubeVideoId: `horse_${horse.horseId}_vid_${idx + 1}`,
    });
  }

  logSeedComplete('Horse videos');
};
