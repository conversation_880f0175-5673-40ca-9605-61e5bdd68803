/*
  Warnings:

  - A unique constraint covering the columns `[documentGroupId,fileType]` on the table `MembershipApplicationIdentityDocument` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "public"."MembershipApplicationIdentityDocument" ADD COLUMN     "personalIndex" INTEGER,
ALTER COLUMN "fileType" DROP NOT NULL;

-- CreateIndex
CREATE INDEX "MembershipApplicationIdentityDocument_documentGroupId_perso_idx" ON "public"."MembershipApplicationIdentityDocument"("documentGroupId", "personalIndex");

-- Data cleanup: remove duplicates on (documentGroupId, fileType) keeping the latest record per pair
WITH ranked AS (
  SELECT "membershipApplicationIdentityDocumentId",
         ROW_NUMBER() OVER (
           PARTITION BY "documentGroupId", "fileType"
           ORDER BY "membershipApplicationIdentityDocumentId" DESC
         ) AS rn
  FROM "public"."MembershipApplicationIdentityDocument"
  WHERE "documentGroupId" IS NOT NULL
    AND "fileType" IS NOT NULL
)
DELETE FROM "public"."MembershipApplicationIdentityDocument" d
USING ranked
WHERE d."membershipApplicationIdentityDocumentId" = ranked."membershipApplicationIdentityDocumentId"
  AND ranked.rn > 1;

-- CreateIndex
CREATE UNIQUE INDEX "MembershipApplicationIdentityDocument_documentGroupId_fileT_key" ON "public"."MembershipApplicationIdentityDocument"("documentGroupId", "fileType");


-- Check: exactly one of (personalIndex, fileType) must be set (XOR)
ALTER TABLE "public"."MembershipApplicationIdentityDocument"
  ADD CONSTRAINT "MembershipApplicationIdentityDocument_target_xor_chk"
  CHECK (
    ("personalIndex" IS NOT NULL AND "fileType" IS NULL) OR
    ("personalIndex" IS NULL AND "fileType" IS NOT NULL)
  );

-- Check: personalIndex range 1..4 when present
ALTER TABLE "public"."MembershipApplicationIdentityDocument"
  ADD CONSTRAINT "MembershipApplicationIdentityDocument_personalIndex_range_chk"
  CHECK ("personalIndex" IS NULL OR ("personalIndex" BETWEEN 1 AND 4));

-- Unique index for (documentGroupId, personalIndex) only when personalIndex is not null
CREATE UNIQUE INDEX "MembershipApplicationIdentityDocument_documentGroupId_personalIndex_key"
  ON "public"."MembershipApplicationIdentityDocument"("documentGroupId", "personalIndex")
  WHERE "personalIndex" IS NOT NULL;
