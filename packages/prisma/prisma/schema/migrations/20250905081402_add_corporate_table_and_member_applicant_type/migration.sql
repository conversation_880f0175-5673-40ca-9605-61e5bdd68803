-- AlterTable
ALTER TABLE "public"."Member" ADD COLUMN     "applicantType" "public"."ApplicantType" NOT NULL DEFAULT 'INDIVIDUAL';

-- CreateTable
CREATE TABLE "public"."Corporate" (
    "corporateId" SERIAL NOT NULL,
    "memberId" INTEGER NOT NULL,
    "corporateName" TEXT NOT NULL,
    "corporateNameKana" TEXT NOT NULL,
    "corporateNumber" TEXT NOT NULL,
    "establishedYear" INTEGER,
    "establishedMonth" INTEGER,
    "establishedDay" INTEGER,
    "representativeName" TEXT NOT NULL,
    "representativeName<PERSON><PERSON>" TEXT NOT NULL,
    "representativePosition" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Corporate_pkey" PRIMARY KEY ("corporateId")
);

-- CreateIndex
CREATE UNIQUE INDEX "Corporate_memberId_key" ON "public"."Corporate"("memberId");

-- AddForeignKey
ALTER TABLE "public"."Corporate" ADD CONSTRAINT "Corporate_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES "public"."Member"("memberId") ON DELETE RESTRICT ON UPDATE CASCADE;
