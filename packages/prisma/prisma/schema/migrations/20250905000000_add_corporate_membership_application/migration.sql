-- CreateEnum
CREATE TYPE "ApplicantType" AS ENUM ('INDIVIDUAL', 'CORPORATE');

-- AlterTable
ALTER TABLE "MembershipApplication" ADD COLUMN     "applicantType" "ApplicantType" NOT NULL DEFAULT 'INDIVIDUAL',
ADD COLUMN     "corporateName" TEXT,
ADD COLUMN     "corporateNameKana" TEXT,
ADD COLUMN     "corporateNumber" TEXT,
ADD COLUMN     "establishedDay" INTEGER,
ADD COLUMN     "establishedMonth" INTEGER,
ADD COLUMN     "establishedYear" INTEGER,
ADD COLUMN     "representativeName" TEXT,
ADD COLUMN     "representativeNameKana" TEXT,
ADD COLUMN     "representativePosition" TEXT;

-- AlterTable: 個人用フィールドをnullableに変更
ALTER TABLE "MembershipApplication" ALTER COLUMN "firstName" DROP NOT NULL,
ALTER COLUMN "lastName" DROP NOT NULL,
ALTER COLUMN "firstNameK<PERSON>" DROP NOT NULL,
ALTER COLUMN "lastNameKana" DROP NOT NULL,
ALTER COLUMN "birthYear" DROP NOT NULL,
ALTER COLUMN "birthMonth" DROP NOT NULL,
ALTER COLUMN "birthDay" DROP NOT NULL;

-- CreateTable
CREATE TABLE "BeneficialOwnerDeclaration" (
    "beneficialOwnerDeclarationId" SERIAL NOT NULL,
    "membershipApplicationId" INTEGER NOT NULL,
    "beneficialOwnerName" TEXT NOT NULL,
    "beneficialOwnerBirthYear" INTEGER NOT NULL,
    "beneficialOwnerBirthMonth" INTEGER NOT NULL,
    "beneficialOwnerBirthDay" INTEGER NOT NULL,
    "beneficialOwnerPostalCode" TEXT NOT NULL,
    "beneficialOwnerPrefecture" TEXT NOT NULL,
    "beneficialOwnerAddress" TEXT NOT NULL,
    "beneficialOwnerApartment" TEXT,
    "declarantName" TEXT NOT NULL,
    "declarantPosition" TEXT NOT NULL,
    "isConfirmed" BOOLEAN NOT NULL DEFAULT false,
    "confirmedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BeneficialOwnerDeclaration_pkey" PRIMARY KEY ("beneficialOwnerDeclarationId")
);

-- CreateIndex
CREATE INDEX "BeneficialOwnerDeclaration_membershipApplicationId_idx" ON "BeneficialOwnerDeclaration"("membershipApplicationId");

-- AddForeignKey
ALTER TABLE "BeneficialOwnerDeclaration" ADD CONSTRAINT "BeneficialOwnerDeclaration_membershipApplicationId_fkey" FOREIGN KEY ("membershipApplicationId") REFERENCES "MembershipApplication"("membershipApplicationId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- 既存データの整合性確保: 既存の申し込みは全て個人として扱う
-- 既存データは既にapplicantType = 'INDIVIDUAL'がデフォルトで設定されているため、追加の更新は不要

-- 制約追加: 個人の場合は個人用フィールドが必須、法人の場合は法人用フィールドが必須
-- これはアプリケーションレベルで制御するため、データベース制約は追加しない
