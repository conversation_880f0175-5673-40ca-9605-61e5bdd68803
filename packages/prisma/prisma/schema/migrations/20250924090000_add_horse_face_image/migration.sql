-- HorseFaceImageテーブルを作成
CREATE TABLE "HorseFaceImage" (
    "horseFaceImageId" SERIAL NOT NULL,
    "horseId" INTEGER NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "HorseFaceImage_pkey" PRIMARY KEY ("horseFaceImageId")
);

-- HorseFaceImageテーブルのインデックスと外部キー制約
CREATE UNIQUE INDEX "HorseFaceImage_horseId_key" ON "HorseFaceImage"("horseId");
CREATE INDEX "HorseFaceImage_horseId_idx" ON "HorseFaceImage"("horseId");
ALTER TABLE "HorseFaceImage" ADD CONSTRAINT "HorseFaceImage_horseId_fkey" FOREIGN KEY ("horseId") REFERENCES "Horse"("horseId") ON DELETE RESTRICT ON UPDATE CASCADE;
