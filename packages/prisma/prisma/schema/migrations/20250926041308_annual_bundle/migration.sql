-- CreateEnum
CREATE TYPE "public"."AnnualBundlePublishStatus" AS ENUM ('PUBLIC', 'PRIVATE');

-- CreateEnum
CREATE TYPE "public"."AnnualBundleRecruitmentStatus" AS ENUM ('UPCOMING', 'ACTIVE', 'CLOSED', 'FULL');

-- DropForeignKey
ALTER TABLE "public"."InvestmentApplication" DROP CONSTRAINT "InvestmentApplication_horseId_fkey";

-- AlterTable
ALTER TABLE "public"."InvestmentApplication" ADD COLUMN     "annualBundleId" INTEGER,
ALTER COLUMN "horseId" DROP NOT NULL;

-- CreateTable
CREATE TABLE "public"."AnnualBundle" (
    "annualBundleId" SERIAL NOT NULL,
    "fiscalYear" INTEGER NOT NULL,
    "publishStatus" "public"."AnnualBundlePublishStatus" NOT NULL,
    "recruitmentStatus" "public"."AnnualBundleRecruitmentStatus" NOT NULL,
    "name" TEXT NOT NULL,
    "shares" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AnnualBundle_pkey" PRIMARY KEY ("annualBundleId")
);

-- CreateTable
CREATE TABLE "public"."AnnualBundleHorse" (
    "annualBundleHorseId" SERIAL NOT NULL,
    "annualBundleId" INTEGER NOT NULL,
    "horseId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AnnualBundleHorse_pkey" PRIMARY KEY ("annualBundleHorseId")
);

-- CreateIndex
CREATE INDEX "AnnualBundleHorse_horseId_idx" ON "public"."AnnualBundleHorse"("horseId");

-- CreateIndex
CREATE UNIQUE INDEX "AnnualBundleHorse_annualBundleId_horseId_key" ON "public"."AnnualBundleHorse"("annualBundleId", "horseId");

-- CreateIndex
CREATE INDEX "InvestmentApplication_annualBundleId_idx" ON "public"."InvestmentApplication"("annualBundleId");

-- AddForeignKey
ALTER TABLE "public"."AnnualBundleHorse" ADD CONSTRAINT "AnnualBundleHorse_annualBundleId_fkey" FOREIGN KEY ("annualBundleId") REFERENCES "public"."AnnualBundle"("annualBundleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AnnualBundleHorse" ADD CONSTRAINT "AnnualBundleHorse_horseId_fkey" FOREIGN KEY ("horseId") REFERENCES "public"."Horse"("horseId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."InvestmentApplication" ADD CONSTRAINT "InvestmentApplication_horseId_fkey" FOREIGN KEY ("horseId") REFERENCES "public"."Horse"("horseId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."InvestmentApplication" ADD CONSTRAINT "InvestmentApplication_annualBundleId_fkey" FOREIGN KEY ("annualBundleId") REFERENCES "public"."AnnualBundle"("annualBundleId") ON DELETE SET NULL ON UPDATE CASCADE;
