-- CreateTable
CREATE TABLE "public"."HorseVideo" (
    "horseVideoId" SERIAL NOT NULL,
    "horseId" INTEGER NOT NULL,
    "videoYear" INTEGER NOT NULL,
    "videoMonth" INTEGER NOT NULL,
    "videoDay" INTEGER NOT NULL,
    "publishStatus" "public"."PublishStatus" NOT NULL DEFAULT 'DRAFT',
    "title" TEXT NOT NULL,
    "description" TEXT,
    "youtubeVideoId" TEXT NOT NULL,
    "startAtSeconds" INTEGER,
    "thumbnailImagePath" TEXT,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "HorseVideo_pkey" PRIMARY KEY ("horseVideoId")
);

-- CreateIndex
CREATE INDEX "HorseVideo_horseId_idx" ON "public"."HorseVideo"("horseId");

-- CreateIndex
CREATE INDEX "HorseVideo_videoYear_videoMonth_videoDay_idx" ON "public"."HorseVideo"("videoYear", "videoMonth", "videoDay");

-- CreateIndex
CREATE UNIQUE INDEX "HorseVideo_horseId_youtubeVideoId_key" ON "public"."HorseVideo"("horseId", "youtubeVideoId");

-- AddForeignKey
ALTER TABLE "public"."HorseVideo" ADD CONSTRAINT "HorseVideo_horseId_fkey" FOREIGN KEY ("horseId") REFERENCES "public"."Horse"("horseId") ON DELETE RESTRICT ON UPDATE CASCADE;
