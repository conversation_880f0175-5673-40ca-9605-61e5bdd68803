-- CreateTable
CREATE TABLE "public"."HorseBlackType" (
    "horseBlackTypeId" SERIAL NOT NULL,
    "horseId" INTEGER NOT NULL,
    "pdfUrl" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "HorseBlackType_pkey" PRIMARY KEY ("horseBlackTypeId")
);

-- CreateIndex
CREATE UNIQUE INDEX "HorseBlackType_horseId_key" ON "public"."HorseBlackType"("horseId");

-- CreateIndex
CREATE INDEX "HorseBlackType_horseId_idx" ON "public"."HorseBlackType"("horseId");

-- AddForeignKey
ALTER TABLE "public"."HorseBlackType" ADD CONSTRAINT "HorseBlackType_horseId_fkey" FOREIGN KEY ("horseId") REFERENCES "public"."Horse"("horseId") ON DELETE RESTRICT ON UPDATE CASCADE;
