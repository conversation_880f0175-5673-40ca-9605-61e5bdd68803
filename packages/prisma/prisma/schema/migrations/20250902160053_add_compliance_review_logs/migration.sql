-- CreateTable
CREATE TABLE "public"."MembershipApplicationComplianceReviewLog" (
    "membershipApplicationComplianceReviewLogId" SERIAL NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "membershipApplicationId" INTEGER NOT NULL,
    "reviewer" TEXT NOT NULL,
    "reviewType" "public"."ReviewType" NOT NULL,
    "remandReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MembershipApplicationComplianceReviewLog_pkey" PRIMARY KEY ("membershipApplicationComplianceReviewLogId")
);

-- CreateTable
CREATE TABLE "public"."MembershipApplicationComplianceDocumentGroupReviewLog" (
    "membershipApplicationComplianceDocumentGroupReviewLogId" SERIAL NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "membershipApplicationId" INTEGER NOT NULL,
    "reviewer" TEXT NOT NULL,
    "reviewType" "public"."ReviewType" NOT NULL,
    "remandReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MembershipApplicationComplianceDocumentGroupReviewLog_pkey" PRIMARY KEY ("membershipApplicationComplianceDocumentGroupReviewLogId")
);

-- CreateIndex
CREATE INDEX "MembershipApplicationComplianceReviewLog_membershipApplicat_idx" ON "public"."MembershipApplicationComplianceReviewLog"("membershipApplicationId", "timestamp");

-- CreateIndex
CREATE INDEX "MembershipApplicationComplianceDocumentGroupReviewLog_membe_idx" ON "public"."MembershipApplicationComplianceDocumentGroupReviewLog"("membershipApplicationId", "timestamp");

-- AddForeignKey
ALTER TABLE "public"."MembershipApplicationComplianceReviewLog" ADD CONSTRAINT "MembershipApplicationComplianceReviewLog_membershipApplica_fkey" FOREIGN KEY ("membershipApplicationId") REFERENCES "public"."MembershipApplication"("membershipApplicationId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."MembershipApplicationComplianceDocumentGroupReviewLog" ADD CONSTRAINT "MembershipApplicationComplianceDocumentGroupReviewLog_memb_fkey" FOREIGN KEY ("membershipApplicationId") REFERENCES "public"."MembershipApplication"("membershipApplicationId") ON DELETE RESTRICT ON UPDATE CASCADE;
