// 年度ごとのまとめ買い商品（応援パッケージ）

enum AnnualBundlePublishStatus {
    PUBLIC // 公開
    PRIVATE // 非公開
}

enum AnnualBundleRecruitmentStatus {
    UPCOMING // 募集前
    ACTIVE // 募集中
    CLOSED // 募集終了
    FULL // 満口
}

model AnnualBundle {
    annualBundleId Int @id @default(autoincrement())

    // 年度ごとに1商品のみ公開可能
    fiscalYear Int

    publishStatus     AnnualBundlePublishStatus
    recruitmentStatus AnnualBundleRecruitmentStatus

    name   String
    shares Int // 口数

    // リレーション
    horses                 AnnualBundleHorse[]
    investmentApplications InvestmentApplication[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}

// バンドルと募集馬のリレーション
model AnnualBundleHorse {
    annualBundleHorseId Int @id @default(autoincrement())
    annualBundleId      Int
    horseId             Int

    annualBundle AnnualBundle @relation(fields: [annualBundleId], references: [annualBundleId])
    horse        Horse        @relation(fields: [horseId], references: [horseId])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@unique([annualBundleId, horseId])
    @@index([horseId])
}
