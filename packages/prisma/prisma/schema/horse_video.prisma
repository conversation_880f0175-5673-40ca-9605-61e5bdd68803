// 馬の動画一覧（YouTube埋め込み前提）

model HorseVideo {
  horseVideoId Int   @id @default(autoincrement())
  horseId      Int
  horse        Horse @relation(fields: [horseId], references: [horseId])

  // 動画日付（並び・検索用）
  videoYear  Int
  videoMonth Int
  videoDay   Int

  // 公開設定
  publishStatus PublishStatus @default(DRAFT)

  // 表示用情報
  title       String
  description String?

  // YouTube 埋め込み情報
  youtubeVideoId String // 例: "dQw4w9WgXcQ"
  startAtSeconds Int? // 再生開始位置（秒）

  // サムネイル（内製S3保管を考慮して Path）
  thumbnailImagePath String?

  // 論理削除
  deletedAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([horseId, youtubeVideoId])
  @@index([horseId])
  @@index([videoYear, videoMonth, videoDay])
}
