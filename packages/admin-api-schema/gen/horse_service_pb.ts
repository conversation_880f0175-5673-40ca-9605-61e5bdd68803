// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file horse_service.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { CoatColor, HorseClass, HorseGender, PublishStatus, RecruitmentAppealLabel, RecruitmentStatus } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { HorseDetail, HorseListItem } from "./models/horse_pb";
import { file_models_horse } from "./models/horse_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_service.proto.
 */
export const file_horse_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums, file_models_horse]);

/**
 * 馬一覧取得リクエスト
 *
 * @generated from message hami.admin.v1.ListHorsesRequest
 */
export type ListHorsesRequest = Message<"hami.admin.v1.ListHorsesRequest"> & {
  /**
   * @generated from field: int32 page = 1;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 2;
   */
  limit: number;

  /**
   * 馬名または母馬名での検索
   *
   * @generated from field: optional string search = 3;
   */
  search?: string;

  /**
   * 募集年での絞り込み
   *
   * @generated from field: optional int32 recruitment_year = 4;
   */
  recruitmentYear?: number;

  /**
   * 募集状態での絞り込み
   *
   * @generated from field: optional hami.admin.v1.RecruitmentStatus recruitment_status = 5;
   */
  recruitmentStatus?: RecruitmentStatus;
};

/**
 * Describes the message hami.admin.v1.ListHorsesRequest.
 * Use `create(ListHorsesRequestSchema)` to create a new message.
 */
export const ListHorsesRequestSchema: GenMessage<ListHorsesRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 0);

/**
 * 馬一覧取得レスポンス
 *
 * @generated from message hami.admin.v1.ListHorsesResponse
 */
export type ListHorsesResponse = Message<"hami.admin.v1.ListHorsesResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.HorseListItem horses = 1;
   */
  horses: HorseListItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 4;
   */
  limit: number;

  /**
   * @generated from field: int32 total_pages = 5;
   */
  totalPages: number;
};

/**
 * Describes the message hami.admin.v1.ListHorsesResponse.
 * Use `create(ListHorsesResponseSchema)` to create a new message.
 */
export const ListHorsesResponseSchema: GenMessage<ListHorsesResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 1);

/**
 * 馬詳細取得リクエスト
 *
 * @generated from message hami.admin.v1.GetHorseRequest
 */
export type GetHorseRequest = Message<"hami.admin.v1.GetHorseRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message hami.admin.v1.GetHorseRequest.
 * Use `create(GetHorseRequestSchema)` to create a new message.
 */
export const GetHorseRequestSchema: GenMessage<GetHorseRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 2);

/**
 * 馬詳細取得レスポンス
 *
 * @generated from message hami.admin.v1.GetHorseResponse
 */
export type GetHorseResponse = Message<"hami.admin.v1.GetHorseResponse"> & {
  /**
   * @generated from field: hami.admin.v1.HorseDetail horse = 1;
   */
  horse?: HorseDetail;
};

/**
 * Describes the message hami.admin.v1.GetHorseResponse.
 * Use `create(GetHorseResponseSchema)` to create a new message.
 */
export const GetHorseResponseSchema: GenMessage<GetHorseResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 3);

/**
 * 馬更新リクエスト
 *
 * @generated from message hami.admin.v1.UpdateHorseRequest
 */
export type UpdateHorseRequest = Message<"hami.admin.v1.UpdateHorseRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: optional string horse_name_en = 2;
   */
  horseNameEn?: string;

  /**
   * @generated from field: optional string name_origin = 3;
   */
  nameOrigin?: string;

  /**
   * @generated from field: optional string affiliation = 4;
   */
  affiliation?: string;

  /**
   * @generated from field: optional string stable_name = 5;
   */
  stableName?: string;

  /**
   * @generated from field: optional hami.admin.v1.HorseGender gender = 6;
   */
  gender?: HorseGender;

  /**
   * @generated from field: optional hami.admin.v1.CoatColor coat_color = 7;
   */
  coatColor?: CoatColor;

  /**
   * @generated from field: optional string birth_place = 8;
   */
  birthPlace?: string;

  /**
   * @generated from field: optional string breeder_name = 9;
   */
  breederName?: string;

  /**
   * @generated from field: optional string training_farm = 10;
   */
  trainingFarm?: string;

  /**
   * @generated from field: optional string sire_name = 11;
   */
  sireName?: string;

  /**
   * @generated from field: optional string dam_name = 12;
   */
  damName?: string;

  /**
   * @generated from field: optional string broodmare_sire_name = 13;
   */
  broodmareSireName?: string;

  /**
   * @generated from field: optional string cross_detail = 27;
   */
  crossDetail?: string;

  /**
   * @generated from field: optional string recruitment_comment = 14;
   */
  recruitmentComment?: string;

  /**
   * @generated from field: optional string note = 15;
   */
  note?: string;

  /**
   * @generated from field: optional int32 max_shares_per_order = 16;
   */
  maxSharesPerOrder?: number;

  /**
   * @generated from field: optional bool special_flag = 17;
   */
  specialFlag?: boolean;

  /**
   * @generated from field: optional hami.admin.v1.PublishStatus publish_status = 18;
   */
  publishStatus?: PublishStatus;

  /**
   * @generated from field: optional hami.admin.v1.RecruitmentStatus recruitment_status = 19;
   */
  recruitmentStatus?: RecruitmentStatus;

  /**
   * @generated from field: optional bool retirement_settled = 20;
   */
  retirementSettled?: boolean;

  /**
   * @generated from field: optional string medical_history = 21;
   */
  medicalHistory?: string;

  /**
   * @generated from field: optional hami.admin.v1.HorseClass horse_class = 22;
   */
  horseClass?: HorseClass;

  /**
   * @generated from field: optional bool featured = 23;
   */
  featured?: boolean;

  /**
   * @generated from field: optional hami.admin.v1.RecruitmentAppealLabel recruitment_appeal_label = 24;
   */
  recruitmentAppealLabel?: RecruitmentAppealLabel;

  /**
   * 一括出資の対象外フラグ
   *
   * @generated from field: optional bool excluded_from_bulk_investment = 25;
   */
  excludedFromBulkInvestment?: boolean;

  /**
   * @generated from field: optional string black_type_file_key = 26;
   */
  blackTypeFileKey?: string;
};

/**
 * Describes the message hami.admin.v1.UpdateHorseRequest.
 * Use `create(UpdateHorseRequestSchema)` to create a new message.
 */
export const UpdateHorseRequestSchema: GenMessage<UpdateHorseRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 4);

/**
 * 馬更新レスポンス
 *
 * @generated from message hami.admin.v1.UpdateHorseResponse
 */
export type UpdateHorseResponse = Message<"hami.admin.v1.UpdateHorseResponse"> & {
};

/**
 * Describes the message hami.admin.v1.UpdateHorseResponse.
 * Use `create(UpdateHorseResponseSchema)` to create a new message.
 */
export const UpdateHorseResponseSchema: GenMessage<UpdateHorseResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 5);

/**
 * 馬画像アップロード用署名付きURL取得リクエスト
 *
 * @generated from message hami.admin.v1.GetHorseImageUploadUrlRequest
 */
export type GetHorseImageUploadUrlRequest = Message<"hami.admin.v1.GetHorseImageUploadUrlRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string file_type = 2;
   */
  fileType: string;

  /**
   * @generated from field: string filename = 3;
   */
  filename: string;
};

/**
 * Describes the message hami.admin.v1.GetHorseImageUploadUrlRequest.
 * Use `create(GetHorseImageUploadUrlRequestSchema)` to create a new message.
 */
export const GetHorseImageUploadUrlRequestSchema: GenMessage<GetHorseImageUploadUrlRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 6);

/**
 * 馬画像ダウンロード用署名付きURL取得リクエスト
 *
 * @generated from message hami.admin.v1.GetHorseImageDownloadUrlRequest
 */
export type GetHorseImageDownloadUrlRequest = Message<"hami.admin.v1.GetHorseImageDownloadUrlRequest"> & {
  /**
   * @generated from field: string file_key = 1;
   */
  fileKey: string;
};

/**
 * Describes the message hami.admin.v1.GetHorseImageDownloadUrlRequest.
 * Use `create(GetHorseImageDownloadUrlRequestSchema)` to create a new message.
 */
export const GetHorseImageDownloadUrlRequestSchema: GenMessage<GetHorseImageDownloadUrlRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 7);

/**
 * 馬画像ダウンロード用署名付きURL取得レスポンス
 *
 * @generated from message hami.admin.v1.GetHorseImageDownloadUrlResponse
 */
export type GetHorseImageDownloadUrlResponse = Message<"hami.admin.v1.GetHorseImageDownloadUrlResponse"> & {
  /**
   * @generated from field: string download_url = 1;
   */
  downloadUrl: string;

  /**
   * @generated from field: int32 expires_in = 2;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.admin.v1.GetHorseImageDownloadUrlResponse.
 * Use `create(GetHorseImageDownloadUrlResponseSchema)` to create a new message.
 */
export const GetHorseImageDownloadUrlResponseSchema: GenMessage<GetHorseImageDownloadUrlResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 8);

/**
 * @generated from message hami.admin.v1.GetHorseImageUploadUrlResponse
 */
export type GetHorseImageUploadUrlResponse = Message<"hami.admin.v1.GetHorseImageUploadUrlResponse"> & {
  /**
   * @generated from field: string upload_url = 1;
   */
  uploadUrl: string;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: int32 expires_in = 3;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.admin.v1.GetHorseImageUploadUrlResponse.
 * Use `create(GetHorseImageUploadUrlResponseSchema)` to create a new message.
 */
export const GetHorseImageUploadUrlResponseSchema: GenMessage<GetHorseImageUploadUrlResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 9);

/**
 * 馬画像情報保存リクエスト
 *
 * @generated from message hami.admin.v1.CreateHorseImageRequest
 */
export type CreateHorseImageRequest = Message<"hami.admin.v1.CreateHorseImageRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;
};

/**
 * Describes the message hami.admin.v1.CreateHorseImageRequest.
 * Use `create(CreateHorseImageRequestSchema)` to create a new message.
 */
export const CreateHorseImageRequestSchema: GenMessage<CreateHorseImageRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 10);

/**
 * 馬画像情報保存レスポンス
 *
 * @generated from message hami.admin.v1.CreateHorseImageResponse
 */
export type CreateHorseImageResponse = Message<"hami.admin.v1.CreateHorseImageResponse"> & {
  /**
   * @generated from field: int32 horse_image_id = 1;
   */
  horseImageId: number;

  /**
   * @generated from field: string image_url = 2;
   */
  imageUrl: string;
};

/**
 * Describes the message hami.admin.v1.CreateHorseImageResponse.
 * Use `create(CreateHorseImageResponseSchema)` to create a new message.
 */
export const CreateHorseImageResponseSchema: GenMessage<CreateHorseImageResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 11);

/**
 * 馬画像一覧取得リクエスト
 *
 * @generated from message hami.admin.v1.ListHorseImagesRequest
 */
export type ListHorseImagesRequest = Message<"hami.admin.v1.ListHorseImagesRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: optional int32 page = 2;
   */
  page?: number;

  /**
   * @generated from field: optional int32 limit = 3;
   */
  limit?: number;
};

/**
 * Describes the message hami.admin.v1.ListHorseImagesRequest.
 * Use `create(ListHorseImagesRequestSchema)` to create a new message.
 */
export const ListHorseImagesRequestSchema: GenMessage<ListHorseImagesRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 12);

/**
 * 馬画像一覧取得レスポンス
 *
 * @generated from message hami.admin.v1.ListHorseImagesResponse
 */
export type ListHorseImagesResponse = Message<"hami.admin.v1.ListHorseImagesResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.HorseImage images = 1;
   */
  images: HorseImage[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;
};

/**
 * Describes the message hami.admin.v1.ListHorseImagesResponse.
 * Use `create(ListHorseImagesResponseSchema)` to create a new message.
 */
export const ListHorseImagesResponseSchema: GenMessage<ListHorseImagesResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 13);

/**
 * 馬画像情報
 *
 * @generated from message hami.admin.v1.HorseImage
 */
export type HorseImage = Message<"hami.admin.v1.HorseImage"> & {
  /**
   * @generated from field: int32 horse_image_id = 1;
   */
  horseImageId: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * @generated from field: string image_url = 3;
   */
  imageUrl: string;

  /**
   * @generated from field: optional string description = 4;
   */
  description?: string;

  /**
   * @generated from field: int32 display_order = 5;
   */
  displayOrder: number;

  /**
   * @generated from field: string created_at = 6;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 7;
   */
  updatedAt: string;
};

/**
 * Describes the message hami.admin.v1.HorseImage.
 * Use `create(HorseImageSchema)` to create a new message.
 */
export const HorseImageSchema: GenMessage<HorseImage> = /*@__PURE__*/
  messageDesc(file_horse_service, 14);

/**
 * 馬顔画像情報
 *
 * @generated from message hami.admin.v1.HorseFaceImage
 */
export type HorseFaceImage = Message<"hami.admin.v1.HorseFaceImage"> & {
  /**
   * @generated from field: int32 horse_face_image_id = 1;
   */
  horseFaceImageId: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * @generated from field: string image_url = 3;
   */
  imageUrl: string;

  /**
   * @generated from field: optional string description = 4;
   */
  description?: string;

  /**
   * @generated from field: string created_at = 5;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 6;
   */
  updatedAt: string;
};

/**
 * Describes the message hami.admin.v1.HorseFaceImage.
 * Use `create(HorseFaceImageSchema)` to create a new message.
 */
export const HorseFaceImageSchema: GenMessage<HorseFaceImage> = /*@__PURE__*/
  messageDesc(file_horse_service, 15);

/**
 * 馬画像更新リクエスト
 *
 * @generated from message hami.admin.v1.UpdateHorseImageRequest
 */
export type UpdateHorseImageRequest = Message<"hami.admin.v1.UpdateHorseImageRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 image_id = 2;
   */
  imageId: number;

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;

  /**
   * @generated from field: optional int32 display_order = 4;
   */
  displayOrder?: number;
};

/**
 * Describes the message hami.admin.v1.UpdateHorseImageRequest.
 * Use `create(UpdateHorseImageRequestSchema)` to create a new message.
 */
export const UpdateHorseImageRequestSchema: GenMessage<UpdateHorseImageRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 16);

/**
 * 馬画像更新レスポンス
 *
 * @generated from message hami.admin.v1.UpdateHorseImageResponse
 */
export type UpdateHorseImageResponse = Message<"hami.admin.v1.UpdateHorseImageResponse"> & {
};

/**
 * Describes the message hami.admin.v1.UpdateHorseImageResponse.
 * Use `create(UpdateHorseImageResponseSchema)` to create a new message.
 */
export const UpdateHorseImageResponseSchema: GenMessage<UpdateHorseImageResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 17);

/**
 * 馬画像削除リクエスト
 *
 * @generated from message hami.admin.v1.DeleteHorseImageRequest
 */
export type DeleteHorseImageRequest = Message<"hami.admin.v1.DeleteHorseImageRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 image_id = 2;
   */
  imageId: number;
};

/**
 * Describes the message hami.admin.v1.DeleteHorseImageRequest.
 * Use `create(DeleteHorseImageRequestSchema)` to create a new message.
 */
export const DeleteHorseImageRequestSchema: GenMessage<DeleteHorseImageRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 18);

/**
 * 馬画像削除レスポンス
 *
 * @generated from message hami.admin.v1.DeleteHorseImageResponse
 */
export type DeleteHorseImageResponse = Message<"hami.admin.v1.DeleteHorseImageResponse"> & {
};

/**
 * Describes the message hami.admin.v1.DeleteHorseImageResponse.
 * Use `create(DeleteHorseImageResponseSchema)` to create a new message.
 */
export const DeleteHorseImageResponseSchema: GenMessage<DeleteHorseImageResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 19);

/**
 * 馬画像並び替えリクエスト
 *
 * @generated from message hami.admin.v1.ReorderHorseImagesRequest
 */
export type ReorderHorseImagesRequest = Message<"hami.admin.v1.ReorderHorseImagesRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: repeated hami.admin.v1.ImageOrder image_orders = 2;
   */
  imageOrders: ImageOrder[];
};

/**
 * Describes the message hami.admin.v1.ReorderHorseImagesRequest.
 * Use `create(ReorderHorseImagesRequestSchema)` to create a new message.
 */
export const ReorderHorseImagesRequestSchema: GenMessage<ReorderHorseImagesRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 20);

/**
 * 馬画像並び替えレスポンス
 *
 * @generated from message hami.admin.v1.ReorderHorseImagesResponse
 */
export type ReorderHorseImagesResponse = Message<"hami.admin.v1.ReorderHorseImagesResponse"> & {
};

/**
 * Describes the message hami.admin.v1.ReorderHorseImagesResponse.
 * Use `create(ReorderHorseImagesResponseSchema)` to create a new message.
 */
export const ReorderHorseImagesResponseSchema: GenMessage<ReorderHorseImagesResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 21);

/**
 * 画像順序情報
 *
 * @generated from message hami.admin.v1.ImageOrder
 */
export type ImageOrder = Message<"hami.admin.v1.ImageOrder"> & {
  /**
   * @generated from field: int32 image_id = 1;
   */
  imageId: number;

  /**
   * @generated from field: int32 display_order = 2;
   */
  displayOrder: number;
};

/**
 * Describes the message hami.admin.v1.ImageOrder.
 * Use `create(ImageOrderSchema)` to create a new message.
 */
export const ImageOrderSchema: GenMessage<ImageOrder> = /*@__PURE__*/
  messageDesc(file_horse_service, 22);

/**
 * 馬顔画像取得リクエスト
 *
 * @generated from message hami.admin.v1.GetHorseFaceImageRequest
 */
export type GetHorseFaceImageRequest = Message<"hami.admin.v1.GetHorseFaceImageRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message hami.admin.v1.GetHorseFaceImageRequest.
 * Use `create(GetHorseFaceImageRequestSchema)` to create a new message.
 */
export const GetHorseFaceImageRequestSchema: GenMessage<GetHorseFaceImageRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 23);

/**
 * 馬顔画像取得レスポンス
 *
 * @generated from message hami.admin.v1.GetHorseFaceImageResponse
 */
export type GetHorseFaceImageResponse = Message<"hami.admin.v1.GetHorseFaceImageResponse"> & {
  /**
   * @generated from field: optional hami.admin.v1.HorseFaceImage image = 1;
   */
  image?: HorseFaceImage;
};

/**
 * Describes the message hami.admin.v1.GetHorseFaceImageResponse.
 * Use `create(GetHorseFaceImageResponseSchema)` to create a new message.
 */
export const GetHorseFaceImageResponseSchema: GenMessage<GetHorseFaceImageResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 24);

/**
 * 馬顔画像保存リクエスト
 *
 * @generated from message hami.admin.v1.UpsertHorseFaceImageRequest
 */
export type UpsertHorseFaceImageRequest = Message<"hami.admin.v1.UpsertHorseFaceImageRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;
};

/**
 * Describes the message hami.admin.v1.UpsertHorseFaceImageRequest.
 * Use `create(UpsertHorseFaceImageRequestSchema)` to create a new message.
 */
export const UpsertHorseFaceImageRequestSchema: GenMessage<UpsertHorseFaceImageRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 25);

/**
 * 馬顔画像保存レスポンス
 *
 * @generated from message hami.admin.v1.UpsertHorseFaceImageResponse
 */
export type UpsertHorseFaceImageResponse = Message<"hami.admin.v1.UpsertHorseFaceImageResponse"> & {
  /**
   * @generated from field: hami.admin.v1.HorseFaceImage image = 1;
   */
  image?: HorseFaceImage;
};

/**
 * Describes the message hami.admin.v1.UpsertHorseFaceImageResponse.
 * Use `create(UpsertHorseFaceImageResponseSchema)` to create a new message.
 */
export const UpsertHorseFaceImageResponseSchema: GenMessage<UpsertHorseFaceImageResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 26);

/**
 * 馬顔画像削除リクエスト
 *
 * @generated from message hami.admin.v1.DeleteHorseFaceImageRequest
 */
export type DeleteHorseFaceImageRequest = Message<"hami.admin.v1.DeleteHorseFaceImageRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message hami.admin.v1.DeleteHorseFaceImageRequest.
 * Use `create(DeleteHorseFaceImageRequestSchema)` to create a new message.
 */
export const DeleteHorseFaceImageRequestSchema: GenMessage<DeleteHorseFaceImageRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 27);

/**
 * 馬顔画像削除レスポンス
 *
 * @generated from message hami.admin.v1.DeleteHorseFaceImageResponse
 */
export type DeleteHorseFaceImageResponse = Message<"hami.admin.v1.DeleteHorseFaceImageResponse"> & {
};

/**
 * Describes the message hami.admin.v1.DeleteHorseFaceImageResponse.
 * Use `create(DeleteHorseFaceImageResponseSchema)` to create a new message.
 */
export const DeleteHorseFaceImageResponseSchema: GenMessage<DeleteHorseFaceImageResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 28);

/**
 * 馬公開ステータス一括更新リクエスト
 *
 * @generated from message hami.admin.v1.BatchUpdateHorsePublishStatusRequest
 */
export type BatchUpdateHorsePublishStatusRequest = Message<"hami.admin.v1.BatchUpdateHorsePublishStatusRequest"> & {
  /**
   * 変更対象の馬ID一覧
   *
   * @generated from field: repeated int32 horse_ids = 1;
   */
  horseIds: number[];

  /**
   * 設定する公開ステータス
   *
   * @generated from field: hami.admin.v1.PublishStatus publish_status = 2;
   */
  publishStatus: PublishStatus;
};

/**
 * Describes the message hami.admin.v1.BatchUpdateHorsePublishStatusRequest.
 * Use `create(BatchUpdateHorsePublishStatusRequestSchema)` to create a new message.
 */
export const BatchUpdateHorsePublishStatusRequestSchema: GenMessage<BatchUpdateHorsePublishStatusRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 29);

/**
 * 馬公開ステータス一括更新レスポンス
 *
 * @generated from message hami.admin.v1.BatchUpdateHorsePublishStatusResponse
 */
export type BatchUpdateHorsePublishStatusResponse = Message<"hami.admin.v1.BatchUpdateHorsePublishStatusResponse"> & {
};

/**
 * Describes the message hami.admin.v1.BatchUpdateHorsePublishStatusResponse.
 * Use `create(BatchUpdateHorsePublishStatusResponseSchema)` to create a new message.
 */
export const BatchUpdateHorsePublishStatusResponseSchema: GenMessage<BatchUpdateHorsePublishStatusResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 30);

/**
 * 馬募集ステータス一括更新リクエスト
 *
 * @generated from message hami.admin.v1.BatchUpdateHorseRecruitmentStatusRequest
 */
export type BatchUpdateHorseRecruitmentStatusRequest = Message<"hami.admin.v1.BatchUpdateHorseRecruitmentStatusRequest"> & {
  /**
   * 変更対象の馬ID一覧
   *
   * @generated from field: repeated int32 horse_ids = 1;
   */
  horseIds: number[];

  /**
   * 設定する募集ステータス
   *
   * @generated from field: hami.admin.v1.RecruitmentStatus recruitment_status = 2;
   */
  recruitmentStatus: RecruitmentStatus;
};

/**
 * Describes the message hami.admin.v1.BatchUpdateHorseRecruitmentStatusRequest.
 * Use `create(BatchUpdateHorseRecruitmentStatusRequestSchema)` to create a new message.
 */
export const BatchUpdateHorseRecruitmentStatusRequestSchema: GenMessage<BatchUpdateHorseRecruitmentStatusRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 31);

/**
 * 馬募集ステータス一括更新レスポンス
 *
 * @generated from message hami.admin.v1.BatchUpdateHorseRecruitmentStatusResponse
 */
export type BatchUpdateHorseRecruitmentStatusResponse = Message<"hami.admin.v1.BatchUpdateHorseRecruitmentStatusResponse"> & {
};

/**
 * Describes the message hami.admin.v1.BatchUpdateHorseRecruitmentStatusResponse.
 * Use `create(BatchUpdateHorseRecruitmentStatusResponseSchema)` to create a new message.
 */
export const BatchUpdateHorseRecruitmentStatusResponseSchema: GenMessage<BatchUpdateHorseRecruitmentStatusResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 32);

/**
 * @generated from message hami.admin.v1.GetHorseBlackTypeRequest
 */
export type GetHorseBlackTypeRequest = Message<"hami.admin.v1.GetHorseBlackTypeRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message hami.admin.v1.GetHorseBlackTypeRequest.
 * Use `create(GetHorseBlackTypeRequestSchema)` to create a new message.
 */
export const GetHorseBlackTypeRequestSchema: GenMessage<GetHorseBlackTypeRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 33);

/**
 * @generated from message hami.admin.v1.HorseBlackType
 */
export type HorseBlackType = Message<"hami.admin.v1.HorseBlackType"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string black_type_url = 2;
   */
  blackTypeUrl: string;

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;

  /**
   * @generated from field: string created_at = 4;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 5;
   */
  updatedAt: string;
};

/**
 * Describes the message hami.admin.v1.HorseBlackType.
 * Use `create(HorseBlackTypeSchema)` to create a new message.
 */
export const HorseBlackTypeSchema: GenMessage<HorseBlackType> = /*@__PURE__*/
  messageDesc(file_horse_service, 34);

/**
 * @generated from message hami.admin.v1.GetHorseBlackTypeResponse
 */
export type GetHorseBlackTypeResponse = Message<"hami.admin.v1.GetHorseBlackTypeResponse"> & {
  /**
   * @generated from field: hami.admin.v1.HorseBlackType black_type = 1;
   */
  blackType?: HorseBlackType;
};

/**
 * Describes the message hami.admin.v1.GetHorseBlackTypeResponse.
 * Use `create(GetHorseBlackTypeResponseSchema)` to create a new message.
 */
export const GetHorseBlackTypeResponseSchema: GenMessage<GetHorseBlackTypeResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 35);

/**
 * ブラックタイプのアップロード用署名付きURL取得リクエスト
 *
 * @generated from message hami.admin.v1.GetHorseBlackTypeUploadUrlRequest
 */
export type GetHorseBlackTypeUploadUrlRequest = Message<"hami.admin.v1.GetHorseBlackTypeUploadUrlRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string file_type = 2;
   */
  fileType: string;

  /**
   * @generated from field: string filename = 3;
   */
  filename: string;
};

/**
 * Describes the message hami.admin.v1.GetHorseBlackTypeUploadUrlRequest.
 * Use `create(GetHorseBlackTypeUploadUrlRequestSchema)` to create a new message.
 */
export const GetHorseBlackTypeUploadUrlRequestSchema: GenMessage<GetHorseBlackTypeUploadUrlRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 36);

/**
 * ブラックタイプのアップロード用署名付きURL取得レスポンス
 *
 * @generated from message hami.admin.v1.GetHorseBlackTypeUploadUrlResponse
 */
export type GetHorseBlackTypeUploadUrlResponse = Message<"hami.admin.v1.GetHorseBlackTypeUploadUrlResponse"> & {
  /**
   * @generated from field: string upload_url = 1;
   */
  uploadUrl: string;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: int32 expires_in = 3;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.admin.v1.GetHorseBlackTypeUploadUrlResponse.
 * Use `create(GetHorseBlackTypeUploadUrlResponseSchema)` to create a new message.
 */
export const GetHorseBlackTypeUploadUrlResponseSchema: GenMessage<GetHorseBlackTypeUploadUrlResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 37);

/**
 * ブラックタイプ作成リクエスト
 *
 * @generated from message hami.admin.v1.CreateHorseBlackTypeRequest
 */
export type CreateHorseBlackTypeRequest = Message<"hami.admin.v1.CreateHorseBlackTypeRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;
};

/**
 * Describes the message hami.admin.v1.CreateHorseBlackTypeRequest.
 * Use `create(CreateHorseBlackTypeRequestSchema)` to create a new message.
 */
export const CreateHorseBlackTypeRequestSchema: GenMessage<CreateHorseBlackTypeRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 38);

/**
 * ブラックタイプ作成レスポンス
 *
 * @generated from message hami.admin.v1.CreateHorseBlackTypeResponse
 */
export type CreateHorseBlackTypeResponse = Message<"hami.admin.v1.CreateHorseBlackTypeResponse"> & {
};

/**
 * Describes the message hami.admin.v1.CreateHorseBlackTypeResponse.
 * Use `create(CreateHorseBlackTypeResponseSchema)` to create a new message.
 */
export const CreateHorseBlackTypeResponseSchema: GenMessage<CreateHorseBlackTypeResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 39);

/**
 * ブラックタイプ更新リクエスト
 *
 * @generated from message hami.admin.v1.UpdateHorseBlackTypeRequest
 */
export type UpdateHorseBlackTypeRequest = Message<"hami.admin.v1.UpdateHorseBlackTypeRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: optional string file_key = 2;
   */
  fileKey?: string;

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;
};

/**
 * Describes the message hami.admin.v1.UpdateHorseBlackTypeRequest.
 * Use `create(UpdateHorseBlackTypeRequestSchema)` to create a new message.
 */
export const UpdateHorseBlackTypeRequestSchema: GenMessage<UpdateHorseBlackTypeRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 40);

/**
 * ブラックタイプ更新レスポンス
 *
 * @generated from message hami.admin.v1.UpdateHorseBlackTypeResponse
 */
export type UpdateHorseBlackTypeResponse = Message<"hami.admin.v1.UpdateHorseBlackTypeResponse"> & {
};

/**
 * Describes the message hami.admin.v1.UpdateHorseBlackTypeResponse.
 * Use `create(UpdateHorseBlackTypeResponseSchema)` to create a new message.
 */
export const UpdateHorseBlackTypeResponseSchema: GenMessage<UpdateHorseBlackTypeResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 41);

/**
 * 血統表のアップロード用署名付きURL取得リクエスト
 *
 * @generated from message hami.admin.v1.GetHorsePedigreeUploadUrlRequest
 */
export type GetHorsePedigreeUploadUrlRequest = Message<"hami.admin.v1.GetHorsePedigreeUploadUrlRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string file_type = 2;
   */
  fileType: string;

  /**
   * @generated from field: string filename = 3;
   */
  filename: string;
};

/**
 * Describes the message hami.admin.v1.GetHorsePedigreeUploadUrlRequest.
 * Use `create(GetHorsePedigreeUploadUrlRequestSchema)` to create a new message.
 */
export const GetHorsePedigreeUploadUrlRequestSchema: GenMessage<GetHorsePedigreeUploadUrlRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 42);

/**
 * 血統表のアップロード用署名付きURL取得レスポンス
 *
 * @generated from message hami.admin.v1.GetHorsePedigreeUploadUrlResponse
 */
export type GetHorsePedigreeUploadUrlResponse = Message<"hami.admin.v1.GetHorsePedigreeUploadUrlResponse"> & {
  /**
   * @generated from field: string upload_url = 1;
   */
  uploadUrl: string;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: int32 expires_in = 3;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.admin.v1.GetHorsePedigreeUploadUrlResponse.
 * Use `create(GetHorsePedigreeUploadUrlResponseSchema)` to create a new message.
 */
export const GetHorsePedigreeUploadUrlResponseSchema: GenMessage<GetHorsePedigreeUploadUrlResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 43);

/**
 * 血統表作成リクエスト
 *
 * @generated from message hami.admin.v1.CreateHorsePedigreeRequest
 */
export type CreateHorsePedigreeRequest = Message<"hami.admin.v1.CreateHorsePedigreeRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;
};

/**
 * Describes the message hami.admin.v1.CreateHorsePedigreeRequest.
 * Use `create(CreateHorsePedigreeRequestSchema)` to create a new message.
 */
export const CreateHorsePedigreeRequestSchema: GenMessage<CreateHorsePedigreeRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 44);

/**
 * 血統表作成レスポンス
 *
 * @generated from message hami.admin.v1.CreateHorsePedigreeResponse
 */
export type CreateHorsePedigreeResponse = Message<"hami.admin.v1.CreateHorsePedigreeResponse"> & {
};

/**
 * Describes the message hami.admin.v1.CreateHorsePedigreeResponse.
 * Use `create(CreateHorsePedigreeResponseSchema)` to create a new message.
 */
export const CreateHorsePedigreeResponseSchema: GenMessage<CreateHorsePedigreeResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 45);

/**
 * 血統表更新リクエスト
 *
 * @generated from message hami.admin.v1.UpdateHorsePedigreeRequest
 */
export type UpdateHorsePedigreeRequest = Message<"hami.admin.v1.UpdateHorsePedigreeRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: optional string file_key = 2;
   */
  fileKey?: string;
};

/**
 * Describes the message hami.admin.v1.UpdateHorsePedigreeRequest.
 * Use `create(UpdateHorsePedigreeRequestSchema)` to create a new message.
 */
export const UpdateHorsePedigreeRequestSchema: GenMessage<UpdateHorsePedigreeRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 46);

/**
 * 血統表更新レスポンス
 *
 * @generated from message hami.admin.v1.UpdateHorsePedigreeResponse
 */
export type UpdateHorsePedigreeResponse = Message<"hami.admin.v1.UpdateHorsePedigreeResponse"> & {
};

/**
 * Describes the message hami.admin.v1.UpdateHorsePedigreeResponse.
 * Use `create(UpdateHorsePedigreeResponseSchema)` to create a new message.
 */
export const UpdateHorsePedigreeResponseSchema: GenMessage<UpdateHorsePedigreeResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 47);

/**
 * 馬管理サービス
 *
 * @generated from service hami.admin.v1.HorseService
 */
export const HorseService: GenService<{
  /**
   * 馬の一覧を取得
   *
   * @generated from rpc hami.admin.v1.HorseService.ListHorses
   */
  listHorses: {
    methodKind: "unary";
    input: typeof ListHorsesRequestSchema;
    output: typeof ListHorsesResponseSchema;
  },
  /**
   * 馬の詳細を取得
   *
   * @generated from rpc hami.admin.v1.HorseService.GetHorse
   */
  getHorse: {
    methodKind: "unary";
    input: typeof GetHorseRequestSchema;
    output: typeof GetHorseResponseSchema;
  },
  /**
   * 馬を更新
   *
   * @generated from rpc hami.admin.v1.HorseService.UpdateHorse
   */
  updateHorse: {
    methodKind: "unary";
    input: typeof UpdateHorseRequestSchema;
    output: typeof UpdateHorseResponseSchema;
  },
  /**
   * 馬画像アップロード用署名付きURL取得
   *
   * @generated from rpc hami.admin.v1.HorseService.GetHorseImageUploadUrl
   */
  getHorseImageUploadUrl: {
    methodKind: "unary";
    input: typeof GetHorseImageUploadUrlRequestSchema;
    output: typeof GetHorseImageUploadUrlResponseSchema;
  },
  /**
   * 馬画像ダウンロード用署名付きURL取得
   *
   * @generated from rpc hami.admin.v1.HorseService.GetHorseImageDownloadUrl
   */
  getHorseImageDownloadUrl: {
    methodKind: "unary";
    input: typeof GetHorseImageDownloadUrlRequestSchema;
    output: typeof GetHorseImageDownloadUrlResponseSchema;
  },
  /**
   * 馬画像情報保存
   *
   * @generated from rpc hami.admin.v1.HorseService.CreateHorseImage
   */
  createHorseImage: {
    methodKind: "unary";
    input: typeof CreateHorseImageRequestSchema;
    output: typeof CreateHorseImageResponseSchema;
  },
  /**
   * 馬画像一覧取得
   *
   * @generated from rpc hami.admin.v1.HorseService.ListHorseImages
   */
  listHorseImages: {
    methodKind: "unary";
    input: typeof ListHorseImagesRequestSchema;
    output: typeof ListHorseImagesResponseSchema;
  },
  /**
   * 馬画像更新
   *
   * @generated from rpc hami.admin.v1.HorseService.UpdateHorseImage
   */
  updateHorseImage: {
    methodKind: "unary";
    input: typeof UpdateHorseImageRequestSchema;
    output: typeof UpdateHorseImageResponseSchema;
  },
  /**
   * 馬画像削除
   *
   * @generated from rpc hami.admin.v1.HorseService.DeleteHorseImage
   */
  deleteHorseImage: {
    methodKind: "unary";
    input: typeof DeleteHorseImageRequestSchema;
    output: typeof DeleteHorseImageResponseSchema;
  },
  /**
   * 馬画像並び替え
   *
   * @generated from rpc hami.admin.v1.HorseService.ReorderHorseImages
   */
  reorderHorseImages: {
    methodKind: "unary";
    input: typeof ReorderHorseImagesRequestSchema;
    output: typeof ReorderHorseImagesResponseSchema;
  },
  /**
   * 馬顔画像取得
   *
   * @generated from rpc hami.admin.v1.HorseService.GetHorseFaceImage
   */
  getHorseFaceImage: {
    methodKind: "unary";
    input: typeof GetHorseFaceImageRequestSchema;
    output: typeof GetHorseFaceImageResponseSchema;
  },
  /**
   * 馬顔画像保存（作成・更新）
   *
   * @generated from rpc hami.admin.v1.HorseService.UpsertHorseFaceImage
   */
  upsertHorseFaceImage: {
    methodKind: "unary";
    input: typeof UpsertHorseFaceImageRequestSchema;
    output: typeof UpsertHorseFaceImageResponseSchema;
  },
  /**
   * 馬顔画像削除
   *
   * @generated from rpc hami.admin.v1.HorseService.DeleteHorseFaceImage
   */
  deleteHorseFaceImage: {
    methodKind: "unary";
    input: typeof DeleteHorseFaceImageRequestSchema;
    output: typeof DeleteHorseFaceImageResponseSchema;
  },
  /**
   * 複数の馬の公開ステータスを一括更新
   *
   * @generated from rpc hami.admin.v1.HorseService.BatchUpdateHorsePublishStatus
   */
  batchUpdateHorsePublishStatus: {
    methodKind: "unary";
    input: typeof BatchUpdateHorsePublishStatusRequestSchema;
    output: typeof BatchUpdateHorsePublishStatusResponseSchema;
  },
  /**
   * 複数の馬の募集ステータスを一括更新
   *
   * @generated from rpc hami.admin.v1.HorseService.BatchUpdateHorseRecruitmentStatus
   */
  batchUpdateHorseRecruitmentStatus: {
    methodKind: "unary";
    input: typeof BatchUpdateHorseRecruitmentStatusRequestSchema;
    output: typeof BatchUpdateHorseRecruitmentStatusResponseSchema;
  },
  /**
   * ブラックタイプを取得
   *
   * @generated from rpc hami.admin.v1.HorseService.GetHorseBlackType
   */
  getHorseBlackType: {
    methodKind: "unary";
    input: typeof GetHorseBlackTypeRequestSchema;
    output: typeof GetHorseBlackTypeResponseSchema;
  },
  /**
   * ブラックタイプのアップロード用署名付きURL取得リクエスト
   *
   * @generated from rpc hami.admin.v1.HorseService.GetHorseBlackTypeUploadUrl
   */
  getHorseBlackTypeUploadUrl: {
    methodKind: "unary";
    input: typeof GetHorseBlackTypeUploadUrlRequestSchema;
    output: typeof GetHorseBlackTypeUploadUrlResponseSchema;
  },
  /**
   * ブラックタイプを保存
   *
   * @generated from rpc hami.admin.v1.HorseService.CreateHorseBlackType
   */
  createHorseBlackType: {
    methodKind: "unary";
    input: typeof CreateHorseBlackTypeRequestSchema;
    output: typeof CreateHorseBlackTypeResponseSchema;
  },
  /**
   * ブラックタイプを更新
   *
   * @generated from rpc hami.admin.v1.HorseService.UpdateHorseBlackType
   */
  updateHorseBlackType: {
    methodKind: "unary";
    input: typeof UpdateHorseBlackTypeRequestSchema;
    output: typeof UpdateHorseBlackTypeResponseSchema;
  },
  /**
   * 血統表のアップロード用署名付きURL取得
   *
   * @generated from rpc hami.admin.v1.HorseService.GetHorsePedigreeUploadUrl
   */
  getHorsePedigreeUploadUrl: {
    methodKind: "unary";
    input: typeof GetHorsePedigreeUploadUrlRequestSchema;
    output: typeof GetHorsePedigreeUploadUrlResponseSchema;
  },
  /**
   * 血統表を保存
   *
   * @generated from rpc hami.admin.v1.HorseService.CreateHorsePedigree
   */
  createHorsePedigree: {
    methodKind: "unary";
    input: typeof CreateHorsePedigreeRequestSchema;
    output: typeof CreateHorsePedigreeResponseSchema;
  },
  /**
   * 血統表を更新
   *
   * @generated from rpc hami.admin.v1.HorseService.UpdateHorsePedigree
   */
  updateHorsePedigree: {
    methodKind: "unary";
    input: typeof UpdateHorsePedigreeRequestSchema;
    output: typeof UpdateHorsePedigreeResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_service, 0);

