// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file models/horse_report.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { MediaType, PublishStatus } from "../common_enums_pb";
import { file_common_enums } from "../common_enums_pb";
import type { HorseListItem } from "./horse_pb";
import { file_models_horse } from "./horse_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file models/horse_report.proto.
 */
export const file_models_horse_report: GenFile = /*@__PURE__*/
  fileDesc("Chltb2RlbHMvaG9yc2VfcmVwb3J0LnByb3RvEg1oYW1pLmFkbWluLnYxIskCCg9Ib3JzZVJlcG9ydEl0ZW0SFwoPaG9yc2VfcmVwb3J0X2lkGAEgASgFEhAKCGhvcnNlX2lkGAIgASgFEhIKCmhvcnNlX25hbWUYAyABKAkSEwoLcmVwb3J0X3llYXIYBCABKAUSFAoMcmVwb3J0X21vbnRoGAUgASgFEhIKCnJlcG9ydF9kYXkYBiABKAUSNAoOcHVibGlzaF9zdGF0dXMYByABKA4yHC5oYW1pLmFkbWluLnYxLlB1Ymxpc2hTdGF0dXMSEAoIbG9jYXRpb24YCCABKAkSDwoHY29udGVudBgJIAEoCRIyCgVtZWRpYRgKIAMoCzIjLmhhbWkuYWRtaW4udjEuSG9yc2VSZXBvcnRNZWRpYUl0ZW0SKwoFaG9yc2UYCyABKAsyHC5oYW1pLmFkbWluLnYxLkhvcnNlTGlzdEl0ZW0iqQEKFEhvcnNlUmVwb3J0TWVkaWFJdGVtEh0KFWhvcnNlX3JlcG9ydF9tZWRpYV9pZBgBIAEoBRIWCg5tZWRpYV9maWxlX3VybBgCIAEoCRIVCg10aHVtYm5haWxfdXJsGAMgASgJEiwKCm1lZGlhX3R5cGUYBCABKA4yGC5oYW1pLmFkbWluLnYxLk1lZGlhVHlwZRIVCg1kaXNwbGF5X29yZGVyGAUgASgFYgZwcm90bzM", [file_common_enums, file_models_horse]);

/**
 * 近況レポートアイテム
 *
 * @generated from message hami.admin.v1.HorseReportItem
 */
export type HorseReportItem = Message<"hami.admin.v1.HorseReportItem"> & {
  /**
   * @generated from field: int32 horse_report_id = 1;
   */
  horseReportId: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * @generated from field: string horse_name = 3;
   */
  horseName: string;

  /**
   * @generated from field: int32 report_year = 4;
   */
  reportYear: number;

  /**
   * @generated from field: int32 report_month = 5;
   */
  reportMonth: number;

  /**
   * @generated from field: int32 report_day = 6;
   */
  reportDay: number;

  /**
   * @generated from field: hami.admin.v1.PublishStatus publish_status = 7;
   */
  publishStatus: PublishStatus;

  /**
   * @generated from field: string location = 8;
   */
  location: string;

  /**
   * @generated from field: string content = 9;
   */
  content: string;

  /**
   * @generated from field: repeated hami.admin.v1.HorseReportMediaItem media = 10;
   */
  media: HorseReportMediaItem[];

  /**
   * @generated from field: hami.admin.v1.HorseListItem horse = 11;
   */
  horse?: HorseListItem;
};

/**
 * Describes the message hami.admin.v1.HorseReportItem.
 * Use `create(HorseReportItemSchema)` to create a new message.
 */
export const HorseReportItemSchema: GenMessage<HorseReportItem> = /*@__PURE__*/
  messageDesc(file_models_horse_report, 0);

/**
 * 近況レポートメディア
 *
 * @generated from message hami.admin.v1.HorseReportMediaItem
 */
export type HorseReportMediaItem = Message<"hami.admin.v1.HorseReportMediaItem"> & {
  /**
   * @generated from field: int32 horse_report_media_id = 1;
   */
  horseReportMediaId: number;

  /**
   * @generated from field: string media_file_url = 2;
   */
  mediaFileUrl: string;

  /**
   * @generated from field: string thumbnail_url = 3;
   */
  thumbnailUrl: string;

  /**
   * @generated from field: hami.admin.v1.MediaType media_type = 4;
   */
  mediaType: MediaType;

  /**
   * @generated from field: int32 display_order = 5;
   */
  displayOrder: number;
};

/**
 * Describes the message hami.admin.v1.HorseReportMediaItem.
 * Use `create(HorseReportMediaItemSchema)` to create a new message.
 */
export const HorseReportMediaItemSchema: GenMessage<HorseReportMediaItem> = /*@__PURE__*/
  messageDesc(file_models_horse_report, 1);

