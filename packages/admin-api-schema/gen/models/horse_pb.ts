// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file models/horse.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { CoatColor, HorseClass, HorseGender, PublishStatus, RecruitmentAppealLabel, RecruitmentStatus } from "../common_enums_pb";
import { file_common_enums } from "../common_enums_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file models/horse.proto.
 */
export const file_models_horse: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums]);

/**
 * 馬の詳細情報
 *
 * @generated from message hami.admin.v1.HorseDetail
 */
export type HorseDetail = Message<"hami.admin.v1.HorseDetail"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 recruitment_year = 2;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 3;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string recruitment_name = 4;
   */
  recruitmentName: string;

  /**
   * @generated from field: string horse_name = 5;
   */
  horseName: string;

  /**
   * @generated from field: int32 birth_year = 6;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 7;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 8;
   */
  birthDay: number;

  /**
   * @generated from field: int32 shares_total = 9;
   */
  sharesTotal: number;

  /**
   * @generated from field: int32 amount_total = 10;
   */
  amountTotal: number;

  /**
   * 追加項目
   *
   * 馬名（英名）
   *
   * @generated from field: optional string horse_name_en = 11;
   */
  horseNameEn?: string;

  /**
   * 馬名の由来
   *
   * @generated from field: optional string name_origin = 12;
   */
  nameOrigin?: string;

  /**
   * 所属
   *
   * @generated from field: optional string affiliation = 13;
   */
  affiliation?: string;

  /**
   * 厩舎名
   *
   * @generated from field: optional string stable_name = 14;
   */
  stableName?: string;

  /**
   * 性別
   *
   * @generated from field: optional hami.admin.v1.HorseGender gender = 15;
   */
  gender?: HorseGender;

  /**
   * 毛色
   *
   * @generated from field: optional hami.admin.v1.CoatColor coat_color = 16;
   */
  coatColor?: CoatColor;

  /**
   * 父馬名
   *
   * @generated from field: optional string sire_name = 17;
   */
  sireName?: string;

  /**
   * 母馬名
   *
   * @generated from field: optional string dam_name = 18;
   */
  damName?: string;

  /**
   * 母父馬名
   *
   * @generated from field: optional string broodmare_sire_name = 19;
   */
  broodmareSireName?: string;

  /**
   * クロス詳細
   *
   * @generated from field: optional string cross_detail = 49;
   */
  crossDetail?: string;

  /**
   * 生産地
   *
   * @generated from field: optional string birth_place = 20;
   */
  birthPlace?: string;

  /**
   * 生産者名
   *
   * @generated from field: optional string breeder_name = 21;
   */
  breederName?: string;

  /**
   * 育成牧場
   *
   * @generated from field: optional string training_farm = 22;
   */
  trainingFarm?: string;

  /**
   * 備考
   *
   * @generated from field: optional string note = 23;
   */
  note?: string;

  /**
   * 募集コメント
   *
   * @generated from field: optional string recruitment_comment = 24;
   */
  recruitmentComment?: string;

  /**
   * @generated from field: int32 max_shares_per_order = 25;
   */
  maxSharesPerOrder: number;

  /**
   * @generated from field: bool special_flag = 26;
   */
  specialFlag: boolean;

  /**
   * @generated from field: hami.admin.v1.PublishStatus publish_status = 27;
   */
  publishStatus: PublishStatus;

  /**
   * @generated from field: hami.admin.v1.RecruitmentStatus recruitment_status = 28;
   */
  recruitmentStatus: RecruitmentStatus;

  /**
   * @generated from field: optional hami.admin.v1.RecruitmentAppealLabel recruitment_appeal_label = 34;
   */
  recruitmentAppealLabel?: RecruitmentAppealLabel;

  /**
   * 引退済みフラグ
   *
   * @generated from field: bool retirement_settled = 29;
   */
  retirementSettled: boolean;

  /**
   * 病歴手術歴
   *
   * @generated from field: optional string medical_history = 30;
   */
  medicalHistory?: string;

  /**
   * クラス
   *
   * @generated from field: optional hami.admin.v1.HorseClass horse_class = 31;
   */
  horseClass?: HorseClass;

  /**
   * @generated from field: bool featured = 32;
   */
  featured: boolean;

  /**
   * 画像URL配列
   *
   * @generated from field: repeated string image_urls = 33;
   */
  imageUrls: string[];

  /**
   * 一括出資の対象外フラグ
   *
   * @generated from field: bool excluded_from_bulk_investment = 45;
   */
  excludedFromBulkInvestment: boolean;

  /**
   * @generated from field: optional string black_type_url = 46;
   */
  blackTypeUrl?: string;

  /**
   * 顔写真URL
   *
   * @generated from field: optional string face_image_url = 47;
   */
  faceImageUrl?: string;

  /**
   * 血統表URL
   *
   * @generated from field: optional string pedigree_url = 48;
   */
  pedigreeUrl?: string;
};

/**
 * Describes the message hami.admin.v1.HorseDetail.
 * Use `create(HorseDetailSchema)` to create a new message.
 */
export const HorseDetailSchema: GenMessage<HorseDetail> = /*@__PURE__*/
  messageDesc(file_models_horse, 0);

/**
 * 馬の一覧項目
 *
 * @generated from message hami.admin.v1.HorseListItem
 */
export type HorseListItem = Message<"hami.admin.v1.HorseListItem"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 recruitment_year = 2;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 3;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string recruitment_name = 4;
   */
  recruitmentName: string;

  /**
   * @generated from field: string horse_name = 5;
   */
  horseName: string;

  /**
   * @generated from field: int32 birth_year = 6;
   */
  birthYear: number;

  /**
   * @generated from field: optional hami.admin.v1.HorseGender gender = 7;
   */
  gender?: HorseGender;

  /**
   * @generated from field: optional hami.admin.v1.PublishStatus publish_status = 8;
   */
  publishStatus?: PublishStatus;

  /**
   * @generated from field: optional hami.admin.v1.RecruitmentStatus recruitment_status = 9;
   */
  recruitmentStatus?: RecruitmentStatus;
};

/**
 * Describes the message hami.admin.v1.HorseListItem.
 * Use `create(HorseListItemSchema)` to create a new message.
 */
export const HorseListItemSchema: GenMessage<HorseListItem> = /*@__PURE__*/
  messageDesc(file_models_horse, 1);

