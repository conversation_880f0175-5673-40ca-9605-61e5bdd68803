// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file models/horse_video.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { PublishStatus } from "../common_enums_pb";
import { file_common_enums } from "../common_enums_pb";
import type { HorseListItem } from "./horse_pb";
import { file_models_horse } from "./horse_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file models/horse_video.proto.
 */
export const file_models_horse_video: GenFile = /*@__PURE__*/
  fileDesc("Chhtb2RlbHMvaG9yc2VfdmlkZW8ucHJvdG8SDWhhbWkuYWRtaW4udjEisAMKDkhvcnNlVmlkZW9JdGVtEhYKDmhvcnNlX3ZpZGVvX2lkGAEgASgFEhAKCGhvcnNlX2lkGAIgASgFEhIKCmhvcnNlX25hbWUYAyABKAkSEgoKdmlkZW9feWVhchgEIAEoBRITCgt2aWRlb19tb250aBgFIAEoBRIRCgl2aWRlb19kYXkYBiABKAUSNAoOcHVibGlzaF9zdGF0dXMYByABKA4yHC5oYW1pLmFkbWluLnYxLlB1Ymxpc2hTdGF0dXMSDQoFdGl0bGUYCCABKAkSGAoLZGVzY3JpcHRpb24YCSABKAlIAIgBARIYChB5b3V0dWJlX3ZpZGVvX2lkGAogASgJEh0KEHN0YXJ0X2F0X3NlY29uZHMYCyABKAVIAYgBARIhChR0aHVtYm5haWxfaW1hZ2VfcGF0aBgMIAEoCUgCiAEBEisKBWhvcnNlGBQgASgLMhwuaGFtaS5hZG1pbi52MS5Ib3JzZUxpc3RJdGVtQg4KDF9kZXNjcmlwdGlvbkITChFfc3RhcnRfYXRfc2Vjb25kc0IXChVfdGh1bWJuYWlsX2ltYWdlX3BhdGhiBnByb3RvMw", [file_common_enums, file_models_horse]);

/**
 * 馬動画アイテム（管理画面用）
 *
 * @generated from message hami.admin.v1.HorseVideoItem
 */
export type HorseVideoItem = Message<"hami.admin.v1.HorseVideoItem"> & {
  /**
   * @generated from field: int32 horse_video_id = 1;
   */
  horseVideoId: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * 表示補助
   *
   * @generated from field: string horse_name = 3;
   */
  horseName: string;

  /**
   * 動画日付
   *
   * @generated from field: int32 video_year = 4;
   */
  videoYear: number;

  /**
   * @generated from field: int32 video_month = 5;
   */
  videoMonth: number;

  /**
   * @generated from field: int32 video_day = 6;
   */
  videoDay: number;

  /**
   * 公開設定
   *
   * @generated from field: hami.admin.v1.PublishStatus publish_status = 7;
   */
  publishStatus: PublishStatus;

  /**
   * 表示用情報
   *
   * @generated from field: string title = 8;
   */
  title: string;

  /**
   * @generated from field: optional string description = 9;
   */
  description?: string;

  /**
   * YouTube 埋め込み情報
   *
   * @generated from field: string youtube_video_id = 10;
   */
  youtubeVideoId: string;

  /**
   * @generated from field: optional int32 start_at_seconds = 11;
   */
  startAtSeconds?: number;

  /**
   * サムネイル（S3パス想定）
   *
   * @generated from field: optional string thumbnail_image_path = 12;
   */
  thumbnailImagePath?: string;

  /**
   * 参照用（一覧で馬情報を付与したい場合）
   *
   * @generated from field: hami.admin.v1.HorseListItem horse = 20;
   */
  horse?: HorseListItem;
};

/**
 * Describes the message hami.admin.v1.HorseVideoItem.
 * Use `create(HorseVideoItemSchema)` to create a new message.
 */
export const HorseVideoItemSchema: GenMessage<HorseVideoItem> = /*@__PURE__*/
  messageDesc(file_models_horse_video, 0);

