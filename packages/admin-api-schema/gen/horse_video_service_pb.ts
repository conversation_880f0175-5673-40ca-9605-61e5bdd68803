// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file horse_video_service.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { PublishStatus } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { HorseVideoItem } from "./models/horse_video_pb";
import { file_models_horse_video } from "./models/horse_video_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_video_service.proto.
 */
export const file_horse_video_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums, file_models_horse_video]);

/**
 * 一覧取得
 *
 * @generated from message hami.admin.v1.ListHorseVideosRequest
 */
export type ListHorseVideosRequest = Message<"hami.admin.v1.ListHorseVideosRequest"> & {
  /**
   * 絞り込み条件（horse_id または recruitment_year+recruitment_no
   * のいずれかで特定可）
   *
   * @generated from field: optional int32 horse_id = 1;
   */
  horseId?: number;

  /**
   * @generated from field: optional int32 recruitment_year = 2;
   */
  recruitmentYear?: number;

  /**
   * @generated from field: optional int32 recruitment_no = 3;
   */
  recruitmentNo?: number;

  /**
   * 公開ステータス
   *
   * @generated from field: optional hami.admin.v1.PublishStatus publish_status = 4;
   */
  publishStatus?: PublishStatus;

  /**
   * ページング
   *
   * 1 起点
   *
   * @generated from field: optional int32 page = 10;
   */
  page?: number;

  /**
   * 既定: 20 を想定（実装側で適用）
   *
   * @generated from field: optional int32 page_size = 11;
   */
  pageSize?: number;

  /**
   * 並び順
   * "video_date_desc"（既定：日付の降順）などを実装側で解釈
   *
   * @generated from field: optional string sort_by = 12;
   */
  sortBy?: string;
};

/**
 * Describes the message hami.admin.v1.ListHorseVideosRequest.
 * Use `create(ListHorseVideosRequestSchema)` to create a new message.
 */
export const ListHorseVideosRequestSchema: GenMessage<ListHorseVideosRequest> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 0);

/**
 * @generated from message hami.admin.v1.ListHorseVideosResponse
 */
export type ListHorseVideosResponse = Message<"hami.admin.v1.ListHorseVideosResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.HorseVideoItem videos = 1;
   */
  videos: HorseVideoItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 current_page = 3;
   */
  currentPage: number;

  /**
   * @generated from field: int32 total_pages = 4;
   */
  totalPages: number;

  /**
   * @generated from field: bool has_next_page = 5;
   */
  hasNextPage: boolean;

  /**
   * @generated from field: bool has_previous_page = 6;
   */
  hasPreviousPage: boolean;
};

/**
 * Describes the message hami.admin.v1.ListHorseVideosResponse.
 * Use `create(ListHorseVideosResponseSchema)` to create a new message.
 */
export const ListHorseVideosResponseSchema: GenMessage<ListHorseVideosResponse> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 1);

/**
 * 詳細取得
 *
 * @generated from message hami.admin.v1.GetHorseVideoRequest
 */
export type GetHorseVideoRequest = Message<"hami.admin.v1.GetHorseVideoRequest"> & {
  /**
   * @generated from field: int32 horse_video_id = 1;
   */
  horseVideoId: number;
};

/**
 * Describes the message hami.admin.v1.GetHorseVideoRequest.
 * Use `create(GetHorseVideoRequestSchema)` to create a new message.
 */
export const GetHorseVideoRequestSchema: GenMessage<GetHorseVideoRequest> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 2);

/**
 * @generated from message hami.admin.v1.GetHorseVideoResponse
 */
export type GetHorseVideoResponse = Message<"hami.admin.v1.GetHorseVideoResponse"> & {
  /**
   * @generated from field: hami.admin.v1.HorseVideoItem video = 1;
   */
  video?: HorseVideoItem;
};

/**
 * Describes the message hami.admin.v1.GetHorseVideoResponse.
 * Use `create(GetHorseVideoResponseSchema)` to create a new message.
 */
export const GetHorseVideoResponseSchema: GenMessage<GetHorseVideoResponse> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 3);

/**
 * 作成
 *
 * @generated from message hami.admin.v1.CreateHorseVideoRequest
 */
export type CreateHorseVideoRequest = Message<"hami.admin.v1.CreateHorseVideoRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 video_year = 2;
   */
  videoYear: number;

  /**
   * @generated from field: int32 video_month = 3;
   */
  videoMonth: number;

  /**
   * @generated from field: int32 video_day = 4;
   */
  videoDay: number;

  /**
   * @generated from field: string title = 5;
   */
  title: string;

  /**
   * @generated from field: optional string description = 6;
   */
  description?: string;

  /**
   * @generated from field: string youtube_video_id = 7;
   */
  youtubeVideoId: string;

  /**
   * @generated from field: optional int32 start_at_seconds = 8;
   */
  startAtSeconds?: number;

  /**
   * default: DRAFT
   *
   * @generated from field: optional hami.admin.v1.PublishStatus publish_status = 9;
   */
  publishStatus?: PublishStatus;

  /**
   * @generated from field: optional string thumbnail_image_path = 10;
   */
  thumbnailImagePath?: string;
};

/**
 * Describes the message hami.admin.v1.CreateHorseVideoRequest.
 * Use `create(CreateHorseVideoRequestSchema)` to create a new message.
 */
export const CreateHorseVideoRequestSchema: GenMessage<CreateHorseVideoRequest> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 4);

/**
 * @generated from message hami.admin.v1.CreateHorseVideoResponse
 */
export type CreateHorseVideoResponse = Message<"hami.admin.v1.CreateHorseVideoResponse"> & {
  /**
   * @generated from field: hami.admin.v1.HorseVideoItem video = 1;
   */
  video?: HorseVideoItem;
};

/**
 * Describes the message hami.admin.v1.CreateHorseVideoResponse.
 * Use `create(CreateHorseVideoResponseSchema)` to create a new message.
 */
export const CreateHorseVideoResponseSchema: GenMessage<CreateHorseVideoResponse> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 5);

/**
 * 更新
 *
 * @generated from message hami.admin.v1.UpdateHorseVideoRequest
 */
export type UpdateHorseVideoRequest = Message<"hami.admin.v1.UpdateHorseVideoRequest"> & {
  /**
   * @generated from field: int32 horse_video_id = 1;
   */
  horseVideoId: number;

  /**
   * @generated from field: optional int32 video_year = 2;
   */
  videoYear?: number;

  /**
   * @generated from field: optional int32 video_month = 3;
   */
  videoMonth?: number;

  /**
   * @generated from field: optional int32 video_day = 4;
   */
  videoDay?: number;

  /**
   * @generated from field: optional string title = 5;
   */
  title?: string;

  /**
   * @generated from field: optional string description = 6;
   */
  description?: string;

  /**
   * @generated from field: optional string youtube_video_id = 7;
   */
  youtubeVideoId?: string;

  /**
   * @generated from field: optional int32 start_at_seconds = 8;
   */
  startAtSeconds?: number;

  /**
   * @generated from field: optional hami.admin.v1.PublishStatus publish_status = 9;
   */
  publishStatus?: PublishStatus;

  /**
   * @generated from field: optional string thumbnail_image_path = 10;
   */
  thumbnailImagePath?: string;
};

/**
 * Describes the message hami.admin.v1.UpdateHorseVideoRequest.
 * Use `create(UpdateHorseVideoRequestSchema)` to create a new message.
 */
export const UpdateHorseVideoRequestSchema: GenMessage<UpdateHorseVideoRequest> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 6);

/**
 * @generated from message hami.admin.v1.UpdateHorseVideoResponse
 */
export type UpdateHorseVideoResponse = Message<"hami.admin.v1.UpdateHorseVideoResponse"> & {
  /**
   * @generated from field: hami.admin.v1.HorseVideoItem video = 1;
   */
  video?: HorseVideoItem;
};

/**
 * Describes the message hami.admin.v1.UpdateHorseVideoResponse.
 * Use `create(UpdateHorseVideoResponseSchema)` to create a new message.
 */
export const UpdateHorseVideoResponseSchema: GenMessage<UpdateHorseVideoResponse> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 7);

/**
 * 削除（論理削除）
 *
 * @generated from message hami.admin.v1.DeleteHorseVideoRequest
 */
export type DeleteHorseVideoRequest = Message<"hami.admin.v1.DeleteHorseVideoRequest"> & {
  /**
   * @generated from field: int32 horse_video_id = 1;
   */
  horseVideoId: number;
};

/**
 * Describes the message hami.admin.v1.DeleteHorseVideoRequest.
 * Use `create(DeleteHorseVideoRequestSchema)` to create a new message.
 */
export const DeleteHorseVideoRequestSchema: GenMessage<DeleteHorseVideoRequest> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 8);

/**
 * @generated from message hami.admin.v1.DeleteHorseVideoResponse
 */
export type DeleteHorseVideoResponse = Message<"hami.admin.v1.DeleteHorseVideoResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.admin.v1.DeleteHorseVideoResponse.
 * Use `create(DeleteHorseVideoResponseSchema)` to create a new message.
 */
export const DeleteHorseVideoResponseSchema: GenMessage<DeleteHorseVideoResponse> = /*@__PURE__*/
  messageDesc(file_horse_video_service, 9);

/**
 * 入稿動画（馬動画）の管理サービス
 *
 * @generated from service hami.admin.v1.HorseVideoService
 */
export const HorseVideoService: GenService<{
  /**
   * 動画一覧取得（馬ごと／全体・ステータス・ページング対応）
   *
   * @generated from rpc hami.admin.v1.HorseVideoService.ListHorseVideos
   */
  listHorseVideos: {
    methodKind: "unary";
    input: typeof ListHorseVideosRequestSchema;
    output: typeof ListHorseVideosResponseSchema;
  },
  /**
   * 動画詳細取得
   *
   * @generated from rpc hami.admin.v1.HorseVideoService.GetHorseVideo
   */
  getHorseVideo: {
    methodKind: "unary";
    input: typeof GetHorseVideoRequestSchema;
    output: typeof GetHorseVideoResponseSchema;
  },
  /**
   * 動画作成
   *
   * @generated from rpc hami.admin.v1.HorseVideoService.CreateHorseVideo
   */
  createHorseVideo: {
    methodKind: "unary";
    input: typeof CreateHorseVideoRequestSchema;
    output: typeof CreateHorseVideoResponseSchema;
  },
  /**
   * 動画更新
   *
   * @generated from rpc hami.admin.v1.HorseVideoService.UpdateHorseVideo
   */
  updateHorseVideo: {
    methodKind: "unary";
    input: typeof UpdateHorseVideoRequestSchema;
    output: typeof UpdateHorseVideoResponseSchema;
  },
  /**
   * 動画削除（論理削除）
   *
   * @generated from rpc hami.admin.v1.HorseVideoService.DeleteHorseVideo
   */
  deleteHorseVideo: {
    methodKind: "unary";
    input: typeof DeleteHorseVideoRequestSchema;
    output: typeof DeleteHorseVideoResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_video_service, 0);

