// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file horse_report_service.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { MediaType, PublishStatus } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { HorseReportItem } from "./models/horse_report_pb";
import { file_models_horse_report } from "./models/horse_report_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_report_service.proto.
 */
export const file_horse_report_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums, file_models_horse_report]);

/**
 * 馬ごとの近況一覧取得リクエスト
 *
 * @generated from message hami.admin.v1.ListHorseReportsRequest
 */
export type ListHorseReportsRequest = Message<"hami.admin.v1.ListHorseReportsRequest"> & {
  /**
   * @generated from field: optional int32 horse_id = 1;
   */
  horseId?: number;

  /**
   * @generated from field: optional int32 start_year = 2;
   */
  startYear?: number;

  /**
   * @generated from field: optional int32 start_month = 3;
   */
  startMonth?: number;

  /**
   * @generated from field: optional int32 start_day = 4;
   */
  startDay?: number;

  /**
   * @generated from field: optional int32 end_year = 5;
   */
  endYear?: number;

  /**
   * @generated from field: optional int32 end_month = 6;
   */
  endMonth?: number;

  /**
   * @generated from field: optional int32 end_day = 7;
   */
  endDay?: number;

  /**
   * @generated from field: int32 page = 8;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 9;
   */
  pageSize: number;
};

/**
 * Describes the message hami.admin.v1.ListHorseReportsRequest.
 * Use `create(ListHorseReportsRequestSchema)` to create a new message.
 */
export const ListHorseReportsRequestSchema: GenMessage<ListHorseReportsRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 0);

/**
 * 馬ごとの近況一覧取得レスポンス
 *
 * @generated from message hami.admin.v1.ListHorseReportsResponse
 */
export type ListHorseReportsResponse = Message<"hami.admin.v1.ListHorseReportsResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.HorseReportItem reports = 1;
   */
  reports: HorseReportItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 current_page = 3;
   */
  currentPage: number;

  /**
   * @generated from field: int32 total_pages = 4;
   */
  totalPages: number;

  /**
   * @generated from field: bool has_next_page = 5;
   */
  hasNextPage: boolean;

  /**
   * @generated from field: bool has_previous_page = 6;
   */
  hasPreviousPage: boolean;
};

/**
 * Describes the message hami.admin.v1.ListHorseReportsResponse.
 * Use `create(ListHorseReportsResponseSchema)` to create a new message.
 */
export const ListHorseReportsResponseSchema: GenMessage<ListHorseReportsResponse> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 1);

/**
 * 近況詳細取得リクエスト
 *
 * @generated from message hami.admin.v1.GetHorseReportRequest
 */
export type GetHorseReportRequest = Message<"hami.admin.v1.GetHorseReportRequest"> & {
  /**
   * @generated from field: int32 horse_report_id = 1;
   */
  horseReportId: number;
};

/**
 * Describes the message hami.admin.v1.GetHorseReportRequest.
 * Use `create(GetHorseReportRequestSchema)` to create a new message.
 */
export const GetHorseReportRequestSchema: GenMessage<GetHorseReportRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 2);

/**
 * 近況詳細取得レスポンス
 *
 * @generated from message hami.admin.v1.GetHorseReportResponse
 */
export type GetHorseReportResponse = Message<"hami.admin.v1.GetHorseReportResponse"> & {
  /**
   * @generated from field: hami.admin.v1.HorseReportItem report = 1;
   */
  report?: HorseReportItem;
};

/**
 * Describes the message hami.admin.v1.GetHorseReportResponse.
 * Use `create(GetHorseReportResponseSchema)` to create a new message.
 */
export const GetHorseReportResponseSchema: GenMessage<GetHorseReportResponse> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 3);

/**
 * 近況作成リクエスト
 *
 * @generated from message hami.admin.v1.CreateHorseReportRequest
 */
export type CreateHorseReportRequest = Message<"hami.admin.v1.CreateHorseReportRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 report_year = 2;
   */
  reportYear: number;

  /**
   * @generated from field: int32 report_month = 3;
   */
  reportMonth: number;

  /**
   * @generated from field: int32 report_day = 4;
   */
  reportDay: number;

  /**
   * @generated from field: string location = 5;
   */
  location: string;

  /**
   * @generated from field: string content = 6;
   */
  content: string;

  /**
   * @generated from field: repeated hami.admin.v1.CreateHorseReportMediaRequest media = 7;
   */
  media: CreateHorseReportMediaRequest[];
};

/**
 * Describes the message hami.admin.v1.CreateHorseReportRequest.
 * Use `create(CreateHorseReportRequestSchema)` to create a new message.
 */
export const CreateHorseReportRequestSchema: GenMessage<CreateHorseReportRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 4);

/**
 * 近況作成メディアリクエスト
 *
 * @generated from message hami.admin.v1.CreateHorseReportMediaRequest
 */
export type CreateHorseReportMediaRequest = Message<"hami.admin.v1.CreateHorseReportMediaRequest"> & {
  /**
   * @generated from field: string media_file_url = 1;
   */
  mediaFileUrl: string;

  /**
   * @generated from field: string thumbnail_url = 2;
   */
  thumbnailUrl: string;

  /**
   * @generated from field: hami.admin.v1.MediaType media_type = 3;
   */
  mediaType: MediaType;

  /**
   * @generated from field: int32 display_order = 4;
   */
  displayOrder: number;
};

/**
 * Describes the message hami.admin.v1.CreateHorseReportMediaRequest.
 * Use `create(CreateHorseReportMediaRequestSchema)` to create a new message.
 */
export const CreateHorseReportMediaRequestSchema: GenMessage<CreateHorseReportMediaRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 5);

/**
 * 近況作成レスポンス
 *
 * @generated from message hami.admin.v1.CreateHorseReportResponse
 */
export type CreateHorseReportResponse = Message<"hami.admin.v1.CreateHorseReportResponse"> & {
};

/**
 * Describes the message hami.admin.v1.CreateHorseReportResponse.
 * Use `create(CreateHorseReportResponseSchema)` to create a new message.
 */
export const CreateHorseReportResponseSchema: GenMessage<CreateHorseReportResponse> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 6);

/**
 * 近況更新リクエスト
 *
 * @generated from message hami.admin.v1.UpdateHorseReportRequest
 */
export type UpdateHorseReportRequest = Message<"hami.admin.v1.UpdateHorseReportRequest"> & {
  /**
   * @generated from field: int32 horse_report_id = 1;
   */
  horseReportId: number;

  /**
   * @generated from field: optional int32 report_year = 2;
   */
  reportYear?: number;

  /**
   * @generated from field: optional int32 report_month = 3;
   */
  reportMonth?: number;

  /**
   * @generated from field: optional int32 report_day = 4;
   */
  reportDay?: number;

  /**
   * @generated from field: optional string location = 5;
   */
  location?: string;

  /**
   * @generated from field: optional string content = 6;
   */
  content?: string;

  /**
   * @generated from field: optional hami.admin.v1.PublishStatus publish_status = 7;
   */
  publishStatus?: PublishStatus;

  /**
   * @generated from field: repeated hami.admin.v1.UpdateHorseReportMediaRequest media = 8;
   */
  media: UpdateHorseReportMediaRequest[];
};

/**
 * Describes the message hami.admin.v1.UpdateHorseReportRequest.
 * Use `create(UpdateHorseReportRequestSchema)` to create a new message.
 */
export const UpdateHorseReportRequestSchema: GenMessage<UpdateHorseReportRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 7);

/**
 * 近況更新メディアリクエスト
 *
 * @generated from message hami.admin.v1.UpdateHorseReportMediaRequest
 */
export type UpdateHorseReportMediaRequest = Message<"hami.admin.v1.UpdateHorseReportMediaRequest"> & {
  /**
   * nullの場合は新規作成
   *
   * @generated from field: optional int32 horse_report_media_id = 1;
   */
  horseReportMediaId?: number;

  /**
   * @generated from field: string media_file_url = 2;
   */
  mediaFileUrl: string;

  /**
   * @generated from field: string thumbnail_url = 3;
   */
  thumbnailUrl: string;

  /**
   * @generated from field: hami.admin.v1.MediaType media_type = 4;
   */
  mediaType: MediaType;

  /**
   * @generated from field: int32 display_order = 5;
   */
  displayOrder: number;
};

/**
 * Describes the message hami.admin.v1.UpdateHorseReportMediaRequest.
 * Use `create(UpdateHorseReportMediaRequestSchema)` to create a new message.
 */
export const UpdateHorseReportMediaRequestSchema: GenMessage<UpdateHorseReportMediaRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 8);

/**
 * 近況更新レスポンス
 *
 * @generated from message hami.admin.v1.UpdateHorseReportResponse
 */
export type UpdateHorseReportResponse = Message<"hami.admin.v1.UpdateHorseReportResponse"> & {
};

/**
 * Describes the message hami.admin.v1.UpdateHorseReportResponse.
 * Use `create(UpdateHorseReportResponseSchema)` to create a new message.
 */
export const UpdateHorseReportResponseSchema: GenMessage<UpdateHorseReportResponse> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 9);

/**
 * 近況削除リクエスト
 *
 * @generated from message hami.admin.v1.DeleteHorseReportRequest
 */
export type DeleteHorseReportRequest = Message<"hami.admin.v1.DeleteHorseReportRequest"> & {
  /**
   * @generated from field: int32 horse_report_id = 1;
   */
  horseReportId: number;
};

/**
 * Describes the message hami.admin.v1.DeleteHorseReportRequest.
 * Use `create(DeleteHorseReportRequestSchema)` to create a new message.
 */
export const DeleteHorseReportRequestSchema: GenMessage<DeleteHorseReportRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 10);

/**
 * 近況削除レスポンス
 *
 * @generated from message hami.admin.v1.DeleteHorseReportResponse
 */
export type DeleteHorseReportResponse = Message<"hami.admin.v1.DeleteHorseReportResponse"> & {
};

/**
 * Describes the message hami.admin.v1.DeleteHorseReportResponse.
 * Use `create(DeleteHorseReportResponseSchema)` to create a new message.
 */
export const DeleteHorseReportResponseSchema: GenMessage<DeleteHorseReportResponse> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 11);

/**
 * 本日分未公開近況一覧取得リクエスト
 *
 * @generated from message hami.admin.v1.ListTodayUnpublishedReportsRequest
 */
export type ListTodayUnpublishedReportsRequest = Message<"hami.admin.v1.ListTodayUnpublishedReportsRequest"> & {
};

/**
 * Describes the message hami.admin.v1.ListTodayUnpublishedReportsRequest.
 * Use `create(ListTodayUnpublishedReportsRequestSchema)` to create a new message.
 */
export const ListTodayUnpublishedReportsRequestSchema: GenMessage<ListTodayUnpublishedReportsRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 12);

/**
 * 本日分未公開近況一覧取得レスポンス
 *
 * @generated from message hami.admin.v1.ListTodayUnpublishedReportsResponse
 */
export type ListTodayUnpublishedReportsResponse = Message<"hami.admin.v1.ListTodayUnpublishedReportsResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.HorseReportItem reports = 1;
   */
  reports: HorseReportItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;
};

/**
 * Describes the message hami.admin.v1.ListTodayUnpublishedReportsResponse.
 * Use `create(ListTodayUnpublishedReportsResponseSchema)` to create a new message.
 */
export const ListTodayUnpublishedReportsResponseSchema: GenMessage<ListTodayUnpublishedReportsResponse> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 13);

/**
 * 複数近況一括公開リクエスト
 *
 * @generated from message hami.admin.v1.PublishMultipleReportsRequest
 */
export type PublishMultipleReportsRequest = Message<"hami.admin.v1.PublishMultipleReportsRequest"> & {
  /**
   * @generated from field: repeated int32 horse_report_ids = 1;
   */
  horseReportIds: number[];
};

/**
 * Describes the message hami.admin.v1.PublishMultipleReportsRequest.
 * Use `create(PublishMultipleReportsRequestSchema)` to create a new message.
 */
export const PublishMultipleReportsRequestSchema: GenMessage<PublishMultipleReportsRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 14);

/**
 * 複数近況一括公開レスポンス
 *
 * @generated from message hami.admin.v1.PublishMultipleReportsResponse
 */
export type PublishMultipleReportsResponse = Message<"hami.admin.v1.PublishMultipleReportsResponse"> & {
  /**
   * @generated from field: int32 published_count = 1;
   */
  publishedCount: number;
};

/**
 * Describes the message hami.admin.v1.PublishMultipleReportsResponse.
 * Use `create(PublishMultipleReportsResponseSchema)` to create a new message.
 */
export const PublishMultipleReportsResponseSchema: GenMessage<PublishMultipleReportsResponse> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 15);

/**
 * 近況メディアアップロードURL取得リクエスト
 *
 * @generated from message hami.admin.v1.GetHorseReportMediaUploadUrlRequest
 */
export type GetHorseReportMediaUploadUrlRequest = Message<"hami.admin.v1.GetHorseReportMediaUploadUrlRequest"> & {
  /**
   * @generated from field: int32 horse_report_id = 1;
   */
  horseReportId: number;

  /**
   * @generated from field: string file_type = 2;
   */
  fileType: string;

  /**
   * @generated from field: string filename = 3;
   */
  filename: string;
};

/**
 * Describes the message hami.admin.v1.GetHorseReportMediaUploadUrlRequest.
 * Use `create(GetHorseReportMediaUploadUrlRequestSchema)` to create a new message.
 */
export const GetHorseReportMediaUploadUrlRequestSchema: GenMessage<GetHorseReportMediaUploadUrlRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 16);

/**
 * 近況メディアアップロードURL取得レスポンス
 *
 * @generated from message hami.admin.v1.GetHorseReportMediaUploadUrlResponse
 */
export type GetHorseReportMediaUploadUrlResponse = Message<"hami.admin.v1.GetHorseReportMediaUploadUrlResponse"> & {
  /**
   * @generated from field: string upload_url = 1;
   */
  uploadUrl: string;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: int32 expires_in = 3;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.admin.v1.GetHorseReportMediaUploadUrlResponse.
 * Use `create(GetHorseReportMediaUploadUrlResponseSchema)` to create a new message.
 */
export const GetHorseReportMediaUploadUrlResponseSchema: GenMessage<GetHorseReportMediaUploadUrlResponse> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 17);

/**
 * 一時的なメディアアップロードURL取得リクエスト
 *
 * @generated from message hami.admin.v1.GetTempMediaUploadUrlRequest
 */
export type GetTempMediaUploadUrlRequest = Message<"hami.admin.v1.GetTempMediaUploadUrlRequest"> & {
  /**
   * @generated from field: string file_type = 1;
   */
  fileType: string;

  /**
   * @generated from field: string filename = 2;
   */
  filename: string;
};

/**
 * Describes the message hami.admin.v1.GetTempMediaUploadUrlRequest.
 * Use `create(GetTempMediaUploadUrlRequestSchema)` to create a new message.
 */
export const GetTempMediaUploadUrlRequestSchema: GenMessage<GetTempMediaUploadUrlRequest> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 18);

/**
 * 一時的なメディアアップロードURL取得レスポンス
 *
 * @generated from message hami.admin.v1.GetTempMediaUploadUrlResponse
 */
export type GetTempMediaUploadUrlResponse = Message<"hami.admin.v1.GetTempMediaUploadUrlResponse"> & {
  /**
   * @generated from field: string upload_url = 1;
   */
  uploadUrl: string;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: int32 expires_in = 3;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.admin.v1.GetTempMediaUploadUrlResponse.
 * Use `create(GetTempMediaUploadUrlResponseSchema)` to create a new message.
 */
export const GetTempMediaUploadUrlResponseSchema: GenMessage<GetTempMediaUploadUrlResponse> = /*@__PURE__*/
  messageDesc(file_horse_report_service, 19);

/**
 * 馬近況レポートサービス
 *
 * @generated from service hami.admin.v1.HorseReportService
 */
export const HorseReportService: GenService<{
  /**
   * 馬ごとの近況一覧取得
   *
   * @generated from rpc hami.admin.v1.HorseReportService.ListHorseReports
   */
  listHorseReports: {
    methodKind: "unary";
    input: typeof ListHorseReportsRequestSchema;
    output: typeof ListHorseReportsResponseSchema;
  },
  /**
   * 近況詳細取得
   *
   * @generated from rpc hami.admin.v1.HorseReportService.GetHorseReport
   */
  getHorseReport: {
    methodKind: "unary";
    input: typeof GetHorseReportRequestSchema;
    output: typeof GetHorseReportResponseSchema;
  },
  /**
   * 近況作成
   *
   * @generated from rpc hami.admin.v1.HorseReportService.CreateHorseReport
   */
  createHorseReport: {
    methodKind: "unary";
    input: typeof CreateHorseReportRequestSchema;
    output: typeof CreateHorseReportResponseSchema;
  },
  /**
   * 近況更新
   *
   * @generated from rpc hami.admin.v1.HorseReportService.UpdateHorseReport
   */
  updateHorseReport: {
    methodKind: "unary";
    input: typeof UpdateHorseReportRequestSchema;
    output: typeof UpdateHorseReportResponseSchema;
  },
  /**
   * 近況削除
   *
   * @generated from rpc hami.admin.v1.HorseReportService.DeleteHorseReport
   */
  deleteHorseReport: {
    methodKind: "unary";
    input: typeof DeleteHorseReportRequestSchema;
    output: typeof DeleteHorseReportResponseSchema;
  },
  /**
   * 本日分未公開近況一覧取得
   *
   * @generated from rpc hami.admin.v1.HorseReportService.ListTodayUnpublishedReports
   */
  listTodayUnpublishedReports: {
    methodKind: "unary";
    input: typeof ListTodayUnpublishedReportsRequestSchema;
    output: typeof ListTodayUnpublishedReportsResponseSchema;
  },
  /**
   * 複数近況の一括公開
   *
   * @generated from rpc hami.admin.v1.HorseReportService.PublishMultipleReports
   */
  publishMultipleReports: {
    methodKind: "unary";
    input: typeof PublishMultipleReportsRequestSchema;
    output: typeof PublishMultipleReportsResponseSchema;
  },
  /**
   * 近況メディアアップロードURL取得
   *
   * @generated from rpc hami.admin.v1.HorseReportService.GetHorseReportMediaUploadUrl
   */
  getHorseReportMediaUploadUrl: {
    methodKind: "unary";
    input: typeof GetHorseReportMediaUploadUrlRequestSchema;
    output: typeof GetHorseReportMediaUploadUrlResponseSchema;
  },
  /**
   * 一時的なメディアアップロードURL取得
   *
   * @generated from rpc hami.admin.v1.HorseReportService.GetTempMediaUploadUrl
   */
  getTempMediaUploadUrl: {
    methodKind: "unary";
    input: typeof GetTempMediaUploadUrlRequestSchema;
    output: typeof GetTempMediaUploadUrlResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_report_service, 0);

