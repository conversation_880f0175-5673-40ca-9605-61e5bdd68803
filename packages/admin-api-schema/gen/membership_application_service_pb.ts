// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file membership_application_service.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file membership_application_service.proto.
 */
export const file_membership_application_service: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message hami.admin.v1.IdentityDocument
 */
export type IdentityDocument = Message<"hami.admin.v1.IdentityDocument"> & {
  /**
   * @generated from field: int32 identity_document_id = 1;
   */
  identityDocumentId: number;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: hami.admin.v1.DocumentType document_type = 3;
   */
  documentType: DocumentType;

  /**
   * @generated from field: int64 uploaded_at = 4;
   */
  uploadedAt: bigint;

  /**
   * @generated from field: int64 created_at = 5;
   */
  createdAt: bigint;
};

/**
 * Describes the message hami.admin.v1.IdentityDocument.
 * Use `create(IdentityDocumentSchema)` to create a new message.
 */
export const IdentityDocumentSchema: GenMessage<IdentityDocument> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 0);

/**
 * @generated from message hami.admin.v1.DocumentGroup
 */
export type DocumentGroup = Message<"hami.admin.v1.DocumentGroup"> & {
  /**
   * @generated from field: int32 document_group_id = 1;
   */
  documentGroupId: number;

  /**
   * @generated from field: string upload_token = 2;
   */
  uploadToken: string;

  /**
   * @generated from field: string group_key = 3;
   */
  groupKey: string;

  /**
   * @generated from field: bool is_completed = 4;
   */
  isCompleted: boolean;

  /**
   * @generated from field: repeated hami.admin.v1.IdentityDocument documents = 5;
   */
  documents: IdentityDocument[];

  /**
   * @generated from field: int64 created_at = 6;
   */
  createdAt: bigint;
};

/**
 * Describes the message hami.admin.v1.DocumentGroup.
 * Use `create(DocumentGroupSchema)` to create a new message.
 */
export const DocumentGroupSchema: GenMessage<DocumentGroup> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 1);

/**
 * @generated from message hami.admin.v1.MembershipApplication
 */
export type MembershipApplication = Message<"hami.admin.v1.MembershipApplication"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: string status = 3 [deprecated = true];
   * @deprecated
   */
  status: string;

  /**
   * @generated from field: int64 applied_at = 4;
   */
  appliedAt: bigint;

  /**
   * @generated from field: int64 updated_at = 5;
   */
  updatedAt: bigint;

  /**
   * @generated from field: string first_name = 6;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 7;
   */
  lastName: string;

  /**
   * @generated from field: string first_name_kana = 8;
   */
  firstNameKana: string;

  /**
   * @generated from field: string last_name_kana = 9;
   */
  lastNameKana: string;

  /**
   * @generated from field: string postal_code = 10;
   */
  postalCode: string;

  /**
   * @generated from field: string prefecture = 11;
   */
  prefecture: string;

  /**
   * @generated from field: string address = 12;
   */
  address: string;

  /**
   * @generated from field: string apartment = 13;
   */
  apartment: string;

  /**
   * @generated from field: string phone_number = 14;
   */
  phoneNumber: string;

  /**
   * @generated from field: int32 birth_year = 15;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 16;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 17;
   */
  birthDay: number;

  /**
   * @generated from field: string job = 18;
   */
  job: string;

  /**
   * @generated from field: string note = 19;
   */
  note: string;

  /**
   * @generated from field: string annual_income = 20;
   */
  annualIncome: string;

  /**
   * @generated from field: string financial_assets = 21;
   */
  financialAssets: string;

  /**
   * @generated from field: string deposit_amount = 22;
   */
  depositAmount: string;

  /**
   * @generated from field: string income_source = 23;
   */
  incomeSource: string;

  /**
   * @generated from field: string residence_type = 24;
   */
  residenceType: string;

  /**
   * @generated from field: bool has_spouse = 25;
   */
  hasSpouse: boolean;

  /**
   * @generated from field: int32 household_members = 26;
   */
  householdMembers: number;

  /**
   * @generated from field: string asset_operation_experience = 27;
   */
  assetOperationExperience: string;

  /**
   * @generated from field: string club_join_experience = 28;
   */
  clubJoinExperience: string;

  /**
   * @generated from field: string jra_license_number = 29;
   */
  jraLicenseNumber: string;

  /**
   * @generated from field: string nar_license_number = 30;
   */
  narLicenseNumber: string;

  /**
   * @generated from field: hami.admin.v1.ReviewType application_review_status = 31;
   */
  applicationReviewStatus: ReviewType;

  /**
   * @generated from field: hami.admin.v1.ReviewType document_group_review_status = 32;
   */
  documentGroupReviewStatus: ReviewType;

  /**
   * @generated from field: repeated hami.admin.v1.IdentityDocument identity_documents = 33;
   */
  identityDocuments: IdentityDocument[];

  /**
   * @generated from field: repeated hami.admin.v1.DocumentGroup document_groups = 34;
   */
  documentGroups: DocumentGroup[];
};

/**
 * Describes the message hami.admin.v1.MembershipApplication.
 * Use `create(MembershipApplicationSchema)` to create a new message.
 */
export const MembershipApplicationSchema: GenMessage<MembershipApplication> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 2);

/**
 * @generated from message hami.admin.v1.ListMembershipApplicationsRequest
 */
export type ListMembershipApplicationsRequest = Message<"hami.admin.v1.ListMembershipApplicationsRequest"> & {
  /**
   * ページ番号（1から開始）
   * 指定されない場合は1ページ目を返す
   *
   * @generated from field: optional int32 page = 1;
   */
  page?: number;

  /**
   * ページサイズ（1ページあたりの件数）
   * 指定されない場合はデフォルト値を使用
   *
   * @generated from field: optional int32 page_size = 2;
   */
  pageSize?: number;
};

/**
 * Describes the message hami.admin.v1.ListMembershipApplicationsRequest.
 * Use `create(ListMembershipApplicationsRequestSchema)` to create a new message.
 */
export const ListMembershipApplicationsRequestSchema: GenMessage<ListMembershipApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 3);

/**
 * @generated from message hami.admin.v1.ListMembershipApplicationsResponse
 */
export type ListMembershipApplicationsResponse = Message<"hami.admin.v1.ListMembershipApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.MembershipApplication membership_applications = 1;
   */
  membershipApplications: MembershipApplication[];

  /**
   * 次のページがあるかどうか
   *
   * @generated from field: bool has_next_page = 2;
   */
  hasNextPage: boolean;
};

/**
 * Describes the message hami.admin.v1.ListMembershipApplicationsResponse.
 * Use `create(ListMembershipApplicationsResponseSchema)` to create a new message.
 */
export const ListMembershipApplicationsResponseSchema: GenMessage<ListMembershipApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 4);

/**
 * @generated from message hami.admin.v1.GetMembershipApplicationRequest
 */
export type GetMembershipApplicationRequest = Message<"hami.admin.v1.GetMembershipApplicationRequest"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;
};

/**
 * Describes the message hami.admin.v1.GetMembershipApplicationRequest.
 * Use `create(GetMembershipApplicationRequestSchema)` to create a new message.
 */
export const GetMembershipApplicationRequestSchema: GenMessage<GetMembershipApplicationRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 5);

/**
 * @generated from message hami.admin.v1.GetMembershipApplicationResponse
 */
export type GetMembershipApplicationResponse = Message<"hami.admin.v1.GetMembershipApplicationResponse"> & {
  /**
   * @generated from field: hami.admin.v1.MembershipApplication membership_application = 1;
   */
  membershipApplication?: MembershipApplication;
};

/**
 * Describes the message hami.admin.v1.GetMembershipApplicationResponse.
 * Use `create(GetMembershipApplicationResponseSchema)` to create a new message.
 */
export const GetMembershipApplicationResponseSchema: GenMessage<GetMembershipApplicationResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 6);

/**
 * @generated from message hami.admin.v1.ReviewMembershipApplicationRequest
 */
export type ReviewMembershipApplicationRequest = Message<"hami.admin.v1.ReviewMembershipApplicationRequest"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: hami.admin.v1.ReviewType review_type = 2;
   */
  reviewType: ReviewType;

  /**
   * @generated from field: string remand_reason = 3;
   */
  remandReason: string;
};

/**
 * Describes the message hami.admin.v1.ReviewMembershipApplicationRequest.
 * Use `create(ReviewMembershipApplicationRequestSchema)` to create a new message.
 */
export const ReviewMembershipApplicationRequestSchema: GenMessage<ReviewMembershipApplicationRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 7);

/**
 * @generated from message hami.admin.v1.ReviewMembershipApplicationResponse
 */
export type ReviewMembershipApplicationResponse = Message<"hami.admin.v1.ReviewMembershipApplicationResponse"> & {
};

/**
 * Describes the message hami.admin.v1.ReviewMembershipApplicationResponse.
 * Use `create(ReviewMembershipApplicationResponseSchema)` to create a new message.
 */
export const ReviewMembershipApplicationResponseSchema: GenMessage<ReviewMembershipApplicationResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 8);

/**
 * @generated from message hami.admin.v1.ReviewMembershipApplicationDocumentGroupRequest
 */
export type ReviewMembershipApplicationDocumentGroupRequest = Message<"hami.admin.v1.ReviewMembershipApplicationDocumentGroupRequest"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: hami.admin.v1.ReviewType review_type = 2;
   */
  reviewType: ReviewType;

  /**
   * @generated from field: string remand_reason = 3;
   */
  remandReason: string;
};

/**
 * Describes the message hami.admin.v1.ReviewMembershipApplicationDocumentGroupRequest.
 * Use `create(ReviewMembershipApplicationDocumentGroupRequestSchema)` to create a new message.
 */
export const ReviewMembershipApplicationDocumentGroupRequestSchema: GenMessage<ReviewMembershipApplicationDocumentGroupRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 9);

/**
 * @generated from message hami.admin.v1.ReviewMembershipApplicationDocumentGroupResponse
 */
export type ReviewMembershipApplicationDocumentGroupResponse = Message<"hami.admin.v1.ReviewMembershipApplicationDocumentGroupResponse"> & {
};

/**
 * Describes the message hami.admin.v1.ReviewMembershipApplicationDocumentGroupResponse.
 * Use `create(ReviewMembershipApplicationDocumentGroupResponseSchema)` to create a new message.
 */
export const ReviewMembershipApplicationDocumentGroupResponseSchema: GenMessage<ReviewMembershipApplicationDocumentGroupResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 10);

/**
 * @generated from enum hami.admin.v1.ReviewType
 */
export enum ReviewType {
  /**
   * @generated from enum value: REVIEW_TYPE_UNKNOWN = 0;
   */
  REVIEW_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: APPROVE = 1;
   */
  APPROVE = 1,

  /**
   * @generated from enum value: REJECT = 2;
   */
  REJECT = 2,

  /**
   * @generated from enum value: REMAND = 3;
   */
  REMAND = 3,

  /**
   * @generated from enum value: NOT_REVIEWED = 4;
   */
  NOT_REVIEWED = 4,
}

/**
 * Describes the enum hami.admin.v1.ReviewType.
 */
export const ReviewTypeSchema: GenEnum<ReviewType> = /*@__PURE__*/
  enumDesc(file_membership_application_service, 0);

/**
 * @generated from enum hami.admin.v1.DocumentType
 */
export enum DocumentType {
  /**
   * @generated from enum value: DOCUMENT_TYPE_UNKNOWN = 0;
   */
  DOCUMENT_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: IDENTITY_FRONT = 1;
   */
  IDENTITY_FRONT = 1,

  /**
   * @generated from enum value: IDENTITY_BACK = 2;
   */
  IDENTITY_BACK = 2,

  /**
   * 法人KYC書類
   *
   * 印鑑証明書
   *
   * @generated from enum value: CORP_SEAL_CERT = 3;
   */
  CORP_SEAL_CERT = 3,

  /**
   * 履歴事項（全部/現在）証明書
   *
   * @generated from enum value: CORP_REGISTRY_CERT = 4;
   */
  CORP_REGISTRY_CERT = 4,
}

/**
 * Describes the enum hami.admin.v1.DocumentType.
 */
export const DocumentTypeSchema: GenEnum<DocumentType> = /*@__PURE__*/
  enumDesc(file_membership_application_service, 1);

/**
 * @generated from service hami.admin.v1.MembershipApplicationService
 */
export const MembershipApplicationService: GenService<{
  /**
   * @generated from rpc hami.admin.v1.MembershipApplicationService.ListMembershipApplications
   */
  listMembershipApplications: {
    methodKind: "unary";
    input: typeof ListMembershipApplicationsRequestSchema;
    output: typeof ListMembershipApplicationsResponseSchema;
  },
  /**
   * @generated from rpc hami.admin.v1.MembershipApplicationService.GetMembershipApplication
   */
  getMembershipApplication: {
    methodKind: "unary";
    input: typeof GetMembershipApplicationRequestSchema;
    output: typeof GetMembershipApplicationResponseSchema;
  },
  /**
   * @generated from rpc hami.admin.v1.MembershipApplicationService.ReviewMembershipApplication
   */
  reviewMembershipApplication: {
    methodKind: "unary";
    input: typeof ReviewMembershipApplicationRequestSchema;
    output: typeof ReviewMembershipApplicationResponseSchema;
  },
  /**
   * @generated from rpc hami.admin.v1.MembershipApplicationService.ReviewMembershipApplicationDocumentGroup
   */
  reviewMembershipApplicationDocumentGroup: {
    methodKind: "unary";
    input: typeof ReviewMembershipApplicationDocumentGroupRequestSchema;
    output: typeof ReviewMembershipApplicationDocumentGroupResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_membership_application_service, 0);

