// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file common_enums.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file common_enums.proto.
 */
export const file_common_enums: GenFile = /*@__PURE__*/
  fileDesc("ChJjb21tb25fZW51bXMucHJvdG8SDWhhbWkuYWRtaW4udjEqUAoLSG9yc2VHZW5kZXISHAoYSE9SU0VfR0VOREVSX1VOU1BFQ0lGSUVEEAASDAoIU1RBTExJT04QARIICgRNQVJFEAISCwoHR0VMRElORxADKooBCglDb2F0Q29sb3ISGgoWQ09BVF9DT0xPUl9VTlNQRUNJRklFRBAAEgcKA0JBWRABEgwKCERBUktfQkFZEAISCQoFQlJPV04QAxIJCgVCTEFDSxAEEggKBEdSQVkQBRIMCghDSEVTVE5VVBAGEhEKDURBUktfQ0hFU1ROVVQQBxIJCgVXSElURRAIKo8BCgpIb3JzZUNsYXNzEhsKF0hPUlNFX0NMQVNTX1VOU1BFQ0lGSUVEEAASCAoET1BFThABEg4KClRIUkVFX1dJTlMQAhIMCghUV09fV0lOUxADEgsKB09ORV9XSU4QBBIKCgZNQUlERU4QBRIKCgZOT1ZJQ0UQBhIJCgVPVEhFUhAHEgwKCE5PX0NMQVNTEAgqVwoNUHVibGlzaFN0YXR1cxIeChpQVUJMSVNIX1NUQVRVU19VTlNQRUNJRklFRBAAEgkKBURSQUZUEAESDQoJUFVCTElTSEVEEAISDAoIQVJDSElWRUQQAypnChFSZWNydWl0bWVudFN0YXR1cxIiCh5SRUNSVUlUTUVOVF9TVEFUVVNfVU5TUEVDSUZJRUQQABIMCghVUENPTUlORxABEgoKBkFDVElWRRACEggKBEZVTEwQAxIKCgZDTE9TRUQQBCqWAQoWUmVjcnVpdG1lbnRBcHBlYWxMYWJlbBIoCiRSRUNSVUlUTUVOVF9BUFBFQUxfTEFCRUxfVU5TUEVDSUZJRUQQABIMCghVTkRFUl81MBABEgwKCFVOREVSXzQwEAISDAoIVU5ERVJfMzAQAxIMCghVTkRFUl8yMBAEEgwKCFVOREVSXzEwEAUSDAoIRkVXX0xFRlQQBio9CglNZWRpYVR5cGUSGgoWTUVESUFfVFlQRV9VTlNQRUNJRklFRBAAEgkKBUlNQUdFEAESCQoFVklERU8QAiqTAgoiSG9yc2VOYW1lQXBwbGljYXRpb25DYW1wYWlnblN0YXR1cxI2CjJIT1JTRV9OQU1FX0FQUExJQ0FUSU9OX0NBTVBBSUdOX1NUQVRVU19VTlNQRUNJRklFRBAAEiAKHEhPUlNFX05BTUVfQVBQTElDQVRJT05fRFJBRlQQARIkCiBIT1JTRV9OQU1FX0FQUExJQ0FUSU9OX1BVQkxJU0hFRBACEiEKHUhPUlNFX05BTUVfQVBQTElDQVRJT05fQ0xPU0VEEAMSJAogSE9SU0VfTkFNRV9BUFBMSUNBVElPTl9DT01QTEVURUQQBBIkCiBIT1JTRV9OQU1FX0FQUExJQ0FUSU9OX0NBTkNFTExFRBAFYgZwcm90bzM");

/**
 * 馬の性別
 *
 * @generated from enum hami.admin.v1.HorseGender
 */
export enum HorseGender {
  /**
   * @generated from enum value: HORSE_GENDER_UNSPECIFIED = 0;
   */
  HORSE_GENDER_UNSPECIFIED = 0,

  /**
   * 牡
   *
   * @generated from enum value: STALLION = 1;
   */
  STALLION = 1,

  /**
   * 牝
   *
   * @generated from enum value: MARE = 2;
   */
  MARE = 2,

  /**
   * セン
   *
   * @generated from enum value: GELDING = 3;
   */
  GELDING = 3,
}

/**
 * Describes the enum hami.admin.v1.HorseGender.
 */
export const HorseGenderSchema: GenEnum<HorseGender> = /*@__PURE__*/
  enumDesc(file_common_enums, 0);

/**
 * 毛色
 *
 * @generated from enum hami.admin.v1.CoatColor
 */
export enum CoatColor {
  /**
   * @generated from enum value: COAT_COLOR_UNSPECIFIED = 0;
   */
  COAT_COLOR_UNSPECIFIED = 0,

  /**
   * 鹿毛
   *
   * @generated from enum value: BAY = 1;
   */
  BAY = 1,

  /**
   * 黒鹿毛
   *
   * @generated from enum value: DARK_BAY = 2;
   */
  DARK_BAY = 2,

  /**
   * 青鹿毛
   *
   * @generated from enum value: BROWN = 3;
   */
  BROWN = 3,

  /**
   * 青毛
   *
   * @generated from enum value: BLACK = 4;
   */
  BLACK = 4,

  /**
   * 芦毛
   *
   * @generated from enum value: GRAY = 5;
   */
  GRAY = 5,

  /**
   * 栗毛
   *
   * @generated from enum value: CHESTNUT = 6;
   */
  CHESTNUT = 6,

  /**
   * 栃栗毛
   *
   * @generated from enum value: DARK_CHESTNUT = 7;
   */
  DARK_CHESTNUT = 7,

  /**
   * 白毛
   *
   * @generated from enum value: WHITE = 8;
   */
  WHITE = 8,
}

/**
 * Describes the enum hami.admin.v1.CoatColor.
 */
export const CoatColorSchema: GenEnum<CoatColor> = /*@__PURE__*/
  enumDesc(file_common_enums, 1);

/**
 * 馬のクラス
 *
 * @generated from enum hami.admin.v1.HorseClass
 */
export enum HorseClass {
  /**
   * @generated from enum value: HORSE_CLASS_UNSPECIFIED = 0;
   */
  HORSE_CLASS_UNSPECIFIED = 0,

  /**
   * オープン
   *
   * @generated from enum value: OPEN = 1;
   */
  OPEN = 1,

  /**
   * 3勝クラス
   *
   * @generated from enum value: THREE_WINS = 2;
   */
  THREE_WINS = 2,

  /**
   * 2勝クラス
   *
   * @generated from enum value: TWO_WINS = 3;
   */
  TWO_WINS = 3,

  /**
   * 1勝クラス
   *
   * @generated from enum value: ONE_WIN = 4;
   */
  ONE_WIN = 4,

  /**
   * 新馬
   *
   * @generated from enum value: MAIDEN = 5;
   */
  MAIDEN = 5,

  /**
   * 未勝利
   *
   * @generated from enum value: NOVICE = 6;
   */
  NOVICE = 6,

  /**
   * その他
   *
   * @generated from enum value: OTHER = 7;
   */
  OTHER = 7,

  /**
   * クラス未定
   *
   * @generated from enum value: NO_CLASS = 8;
   */
  NO_CLASS = 8,
}

/**
 * Describes the enum hami.admin.v1.HorseClass.
 */
export const HorseClassSchema: GenEnum<HorseClass> = /*@__PURE__*/
  enumDesc(file_common_enums, 2);

/**
 * 公開ステータス
 *
 * @generated from enum hami.admin.v1.PublishStatus
 */
export enum PublishStatus {
  /**
   * @generated from enum value: PUBLISH_STATUS_UNSPECIFIED = 0;
   */
  PUBLISH_STATUS_UNSPECIFIED = 0,

  /**
   * 下書き
   *
   * @generated from enum value: DRAFT = 1;
   */
  DRAFT = 1,

  /**
   * 公開
   *
   * @generated from enum value: PUBLISHED = 2;
   */
  PUBLISHED = 2,

  /**
   * 削除
   *
   * @generated from enum value: ARCHIVED = 3;
   */
  ARCHIVED = 3,
}

/**
 * Describes the enum hami.admin.v1.PublishStatus.
 */
export const PublishStatusSchema: GenEnum<PublishStatus> = /*@__PURE__*/
  enumDesc(file_common_enums, 3);

/**
 * 募集ステータス
 *
 * @generated from enum hami.admin.v1.RecruitmentStatus
 */
export enum RecruitmentStatus {
  /**
   * @generated from enum value: RECRUITMENT_STATUS_UNSPECIFIED = 0;
   */
  RECRUITMENT_STATUS_UNSPECIFIED = 0,

  /**
   * 募集前
   *
   * @generated from enum value: UPCOMING = 1;
   */
  UPCOMING = 1,

  /**
   * 募集中
   *
   * @generated from enum value: ACTIVE = 2;
   */
  ACTIVE = 2,

  /**
   * 満口
   *
   * @generated from enum value: FULL = 3;
   */
  FULL = 3,

  /**
   * 募集終了
   *
   * @generated from enum value: CLOSED = 4;
   */
  CLOSED = 4,
}

/**
 * Describes the enum hami.admin.v1.RecruitmentStatus.
 */
export const RecruitmentStatusSchema: GenEnum<RecruitmentStatus> = /*@__PURE__*/
  enumDesc(file_common_enums, 4);

/**
 * 募集訴求用ラベル
 *
 * @generated from enum hami.admin.v1.RecruitmentAppealLabel
 */
export enum RecruitmentAppealLabel {
  /**
   * @generated from enum value: RECRUITMENT_APPEAL_LABEL_UNSPECIFIED = 0;
   */
  RECRUITMENT_APPEAL_LABEL_UNSPECIFIED = 0,

  /**
   * 50%以下
   *
   * @generated from enum value: UNDER_50 = 1;
   */
  UNDER_50 = 1,

  /**
   * 40%以下
   *
   * @generated from enum value: UNDER_40 = 2;
   */
  UNDER_40 = 2,

  /**
   * 30%以下
   *
   * @generated from enum value: UNDER_30 = 3;
   */
  UNDER_30 = 3,

  /**
   * 20%以下
   *
   * @generated from enum value: UNDER_20 = 4;
   */
  UNDER_20 = 4,

  /**
   * 10%以下
   *
   * @generated from enum value: UNDER_10 = 5;
   */
  UNDER_10 = 5,

  /**
   * 残りわずか
   *
   * @generated from enum value: FEW_LEFT = 6;
   */
  FEW_LEFT = 6,
}

/**
 * Describes the enum hami.admin.v1.RecruitmentAppealLabel.
 */
export const RecruitmentAppealLabelSchema: GenEnum<RecruitmentAppealLabel> = /*@__PURE__*/
  enumDesc(file_common_enums, 5);

/**
 * メディアタイプ
 *
 * @generated from enum hami.admin.v1.MediaType
 */
export enum MediaType {
  /**
   * @generated from enum value: MEDIA_TYPE_UNSPECIFIED = 0;
   */
  MEDIA_TYPE_UNSPECIFIED = 0,

  /**
   * @generated from enum value: IMAGE = 1;
   */
  IMAGE = 1,

  /**
   * @generated from enum value: VIDEO = 2;
   */
  VIDEO = 2,
}

/**
 * Describes the enum hami.admin.v1.MediaType.
 */
export const MediaTypeSchema: GenEnum<MediaType> = /*@__PURE__*/
  enumDesc(file_common_enums, 6);

/**
 * 馬名応募キャンペーンステータス
 *
 * @generated from enum hami.admin.v1.HorseNameApplicationCampaignStatus
 */
export enum HorseNameApplicationCampaignStatus {
  /**
   * @generated from enum value: HORSE_NAME_APPLICATION_CAMPAIGN_STATUS_UNSPECIFIED = 0;
   */
  HORSE_NAME_APPLICATION_CAMPAIGN_STATUS_UNSPECIFIED = 0,

  /**
   * 下書き
   *
   * @generated from enum value: HORSE_NAME_APPLICATION_DRAFT = 1;
   */
  HORSE_NAME_APPLICATION_DRAFT = 1,

  /**
   * 公開中（応募受付中）
   *
   * @generated from enum value: HORSE_NAME_APPLICATION_PUBLISHED = 2;
   */
  HORSE_NAME_APPLICATION_PUBLISHED = 2,

  /**
   * 応募終了
   *
   * @generated from enum value: HORSE_NAME_APPLICATION_CLOSED = 3;
   */
  HORSE_NAME_APPLICATION_CLOSED = 3,

  /**
   * 審査完了
   *
   * @generated from enum value: HORSE_NAME_APPLICATION_COMPLETED = 4;
   */
  HORSE_NAME_APPLICATION_COMPLETED = 4,

  /**
   * キャンセル
   *
   * @generated from enum value: HORSE_NAME_APPLICATION_CANCELLED = 5;
   */
  HORSE_NAME_APPLICATION_CANCELLED = 5,
}

/**
 * Describes the enum hami.admin.v1.HorseNameApplicationCampaignStatus.
 */
export const HorseNameApplicationCampaignStatusSchema: GenEnum<HorseNameApplicationCampaignStatus> = /*@__PURE__*/
  enumDesc(file_common_enums, 7);

