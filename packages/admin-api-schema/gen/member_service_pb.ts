// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file member_service.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { HorseGender } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file member_service.proto.
 */
export const file_member_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums]);

/**
 * @generated from message hami.admin.v1.Member
 */
export type Member = Message<"hami.admin.v1.Member"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;

  /**
   * @generated from field: int32 member_number = 2;
   */
  memberNumber: number;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: string first_name = 4;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 5;
   */
  lastName: string;

  /**
   * @generated from field: string first_name_kana = 6;
   */
  firstNameKana: string;

  /**
   * @generated from field: string last_name_kana = 7;
   */
  lastNameKana: string;

  /**
   * @generated from field: string postal_code = 8;
   */
  postalCode: string;

  /**
   * @generated from field: string prefecture = 9;
   */
  prefecture: string;

  /**
   * @generated from field: string address = 10;
   */
  address: string;

  /**
   * @generated from field: string apartment = 11;
   */
  apartment: string;

  /**
   * @generated from field: string phone_number = 12;
   */
  phoneNumber: string;

  /**
   * @generated from field: optional int32 birth_year = 13;
   */
  birthYear?: number;

  /**
   * @generated from field: optional int32 birth_month = 14;
   */
  birthMonth?: number;

  /**
   * @generated from field: optional int32 birth_day = 15;
   */
  birthDay?: number;

  /**
   * @generated from field: int64 approved_at = 27;
   */
  approvedAt: bigint;

  /**
   * @generated from field: string approved_by = 28;
   */
  approvedBy: string;

  /**
   * @generated from field: int32 membership_application_id = 29;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: optional int32 retirementAt = 30;
   */
  retirementAt?: number;

  /**
   * @generated from field: int64 created_at = 31;
   */
  createdAt: bigint;

  /**
   * @generated from field: int64 updated_at = 32;
   */
  updatedAt: bigint;
};

/**
 * Describes the message hami.admin.v1.Member.
 * Use `create(MemberSchema)` to create a new message.
 */
export const MemberSchema: GenMessage<Member> = /*@__PURE__*/
  messageDesc(file_member_service, 0);

/**
 * @generated from message hami.admin.v1.ListMembersRequest
 */
export type ListMembersRequest = Message<"hami.admin.v1.ListMembersRequest"> & {
  /**
   * 検索用: 名
   *
   * @generated from field: optional string firstName = 1;
   */
  firstName?: string;

  /**
   * 検索用: 姓
   *
   * @generated from field: optional string lastName = 2;
   */
  lastName?: string;

  /**
   * 検索用: 会員番号
   *
   * @generated from field: optional int32 memberNumber = 3;
   */
  memberNumber?: number;

  /**
   * 退会者も含めるか
   *
   * @generated from field: optional bool includeRetired = 4;
   */
  includeRetired?: boolean;
};

/**
 * Describes the message hami.admin.v1.ListMembersRequest.
 * Use `create(ListMembersRequestSchema)` to create a new message.
 */
export const ListMembersRequestSchema: GenMessage<ListMembersRequest> = /*@__PURE__*/
  messageDesc(file_member_service, 1);

/**
 * @generated from message hami.admin.v1.ListMembersResponse
 */
export type ListMembersResponse = Message<"hami.admin.v1.ListMembersResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.Member members = 1;
   */
  members: Member[];
};

/**
 * Describes the message hami.admin.v1.ListMembersResponse.
 * Use `create(ListMembersResponseSchema)` to create a new message.
 */
export const ListMembersResponseSchema: GenMessage<ListMembersResponse> = /*@__PURE__*/
  messageDesc(file_member_service, 2);

/**
 * @generated from message hami.admin.v1.GetMemberRequest
 */
export type GetMemberRequest = Message<"hami.admin.v1.GetMemberRequest"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;
};

/**
 * Describes the message hami.admin.v1.GetMemberRequest.
 * Use `create(GetMemberRequestSchema)` to create a new message.
 */
export const GetMemberRequestSchema: GenMessage<GetMemberRequest> = /*@__PURE__*/
  messageDesc(file_member_service, 3);

/**
 * @generated from message hami.admin.v1.GetMemberResponse
 */
export type GetMemberResponse = Message<"hami.admin.v1.GetMemberResponse"> & {
  /**
   * @generated from field: hami.admin.v1.Member member = 1;
   */
  member?: Member;
};

/**
 * Describes the message hami.admin.v1.GetMemberResponse.
 * Use `create(GetMemberResponseSchema)` to create a new message.
 */
export const GetMemberResponseSchema: GenMessage<GetMemberResponse> = /*@__PURE__*/
  messageDesc(file_member_service, 4);

/**
 * 会員の出資馬一覧取得リクエスト
 *
 * @generated from message hami.admin.v1.ListMemberInvestmentHorsesRequest
 */
export type ListMemberInvestmentHorsesRequest = Message<"hami.admin.v1.ListMemberInvestmentHorsesRequest"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;
};

/**
 * Describes the message hami.admin.v1.ListMemberInvestmentHorsesRequest.
 * Use `create(ListMemberInvestmentHorsesRequestSchema)` to create a new message.
 */
export const ListMemberInvestmentHorsesRequestSchema: GenMessage<ListMemberInvestmentHorsesRequest> = /*@__PURE__*/
  messageDesc(file_member_service, 5);

/**
 * 会員の出資馬一覧取得レスポンス
 *
 * @generated from message hami.admin.v1.ListMemberInvestmentHorsesResponse
 */
export type ListMemberInvestmentHorsesResponse = Message<"hami.admin.v1.ListMemberInvestmentHorsesResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.MemberInvestmentHorse horses = 1;
   */
  horses: MemberInvestmentHorse[];
};

/**
 * Describes the message hami.admin.v1.ListMemberInvestmentHorsesResponse.
 * Use `create(ListMemberInvestmentHorsesResponseSchema)` to create a new message.
 */
export const ListMemberInvestmentHorsesResponseSchema: GenMessage<ListMemberInvestmentHorsesResponse> = /*@__PURE__*/
  messageDesc(file_member_service, 6);

/**
 * 会員の出資馬情報
 *
 * @generated from message hami.admin.v1.MemberInvestmentHorse
 */
export type MemberInvestmentHorse = Message<"hami.admin.v1.MemberInvestmentHorse"> & {
  /**
   * 馬ID
   *
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * 生年
   *
   * @generated from field: int32 birth_year = 2;
   */
  birthYear: number;

  /**
   * 性別
   *
   * @generated from field: optional hami.admin.v1.HorseGender gender = 3;
   */
  gender?: HorseGender;

  /**
   * 馬名（募集馬名）
   *
   * @generated from field: string horse_name = 4;
   */
  horseName: string;

  /**
   * 出資口数
   *
   * @generated from field: int32 investment_shares = 5;
   */
  investmentShares: number;

  /**
   * 出資金額
   *
   * @generated from field: int32 investment_amount = 6;
   */
  investmentAmount: number;
};

/**
 * Describes the message hami.admin.v1.MemberInvestmentHorse.
 * Use `create(MemberInvestmentHorseSchema)` to create a new message.
 */
export const MemberInvestmentHorseSchema: GenMessage<MemberInvestmentHorse> = /*@__PURE__*/
  messageDesc(file_member_service, 7);

/**
 * @generated from service hami.admin.v1.MemberService
 */
export const MemberService: GenService<{
  /**
   * @generated from rpc hami.admin.v1.MemberService.ListMembers
   */
  listMembers: {
    methodKind: "unary";
    input: typeof ListMembersRequestSchema;
    output: typeof ListMembersResponseSchema;
  },
  /**
   * @generated from rpc hami.admin.v1.MemberService.GetMember
   */
  getMember: {
    methodKind: "unary";
    input: typeof GetMemberRequestSchema;
    output: typeof GetMemberResponseSchema;
  },
  /**
   * 会員の出資馬一覧を取得
   *
   * @generated from rpc hami.admin.v1.MemberService.ListMemberInvestmentHorses
   */
  listMemberInvestmentHorses: {
    methodKind: "unary";
    input: typeof ListMemberInvestmentHorsesRequestSchema;
    output: typeof ListMemberInvestmentHorsesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_member_service, 0);

