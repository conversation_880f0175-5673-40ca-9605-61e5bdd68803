// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file admin_announcement_service.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file admin_announcement_service.proto.
 */
export const file_admin_announcement_service: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * お知らせ情報
 *
 * @generated from message hami.admin.v1.Announcement
 */
export type Announcement = Message<"hami.admin.v1.Announcement"> & {
  /**
   * @generated from field: int32 announcement_id = 1;
   */
  announcementId: number;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string body = 3;
   */
  body: string;

  /**
   * @generated from field: hami.admin.v1.AnnouncementCategory category = 4;
   */
  category: AnnouncementCategory;

  /**
   * @generated from field: hami.admin.v1.AnnouncementStatus status = 5;
   */
  status: AnnouncementStatus;

  /**
   * @generated from field: bool is_headline = 6;
   */
  isHeadline: boolean;

  /**
   * ISO 8601形式
   *
   * @generated from field: optional string published_at = 7;
   */
  publishedAt?: string;

  /**
   * ISO 8601形式
   *
   * @generated from field: string created_at = 8;
   */
  createdAt: string;

  /**
   * ISO 8601形式
   *
   * @generated from field: string updated_at = 9;
   */
  updatedAt: string;

  /**
   * @generated from field: repeated hami.admin.v1.AnnouncementAttachment attachments = 10;
   */
  attachments: AnnouncementAttachment[];
};

/**
 * Describes the message hami.admin.v1.Announcement.
 * Use `create(AnnouncementSchema)` to create a new message.
 */
export const AnnouncementSchema: GenMessage<Announcement> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 0);

/**
 * 添付ファイル情報
 *
 * @generated from message hami.admin.v1.AnnouncementAttachment
 */
export type AnnouncementAttachment = Message<"hami.admin.v1.AnnouncementAttachment"> & {
  /**
   * @generated from field: int32 announcement_attachment_id = 1;
   */
  announcementAttachmentId: number;

  /**
   * @generated from field: string file_name = 2;
   */
  fileName: string;

  /**
   * @generated from field: string mime_type = 3;
   */
  mimeType: string;

  /**
   * @generated from field: int32 file_size = 4;
   */
  fileSize: number;

  /**
   * @generated from field: hami.admin.v1.AttachmentType attachment_type = 5;
   */
  attachmentType: AttachmentType;

  /**
   * @generated from field: int32 display_order = 6;
   */
  displayOrder: number;

  /**
   * ダウンロード用URL
   *
   * @generated from field: string download_url = 7;
   */
  downloadUrl: string;
};

/**
 * Describes the message hami.admin.v1.AnnouncementAttachment.
 * Use `create(AnnouncementAttachmentSchema)` to create a new message.
 */
export const AnnouncementAttachmentSchema: GenMessage<AnnouncementAttachment> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 1);

/**
 * 一覧取得リクエスト
 *
 * @generated from message hami.admin.v1.ListAnnouncementsRequest
 */
export type ListAnnouncementsRequest = Message<"hami.admin.v1.ListAnnouncementsRequest"> & {
  /**
   * @generated from field: optional hami.admin.v1.AnnouncementCategory category = 1;
   */
  category?: AnnouncementCategory;

  /**
   * @generated from field: optional hami.admin.v1.AnnouncementStatus status = 2;
   */
  status?: AnnouncementStatus;

  /**
   * @generated from field: optional bool is_headline = 3;
   */
  isHeadline?: boolean;

  /**
   * タイトル・本文検索
   *
   * @generated from field: optional string search_query = 4;
   */
  searchQuery?: string;

  /**
   * @generated from field: int32 page = 5;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 6;
   */
  pageSize: number;

  /**
   * "created_at", "updated_at", "published_at"
   *
   * @generated from field: string sort_by = 7;
   */
  sortBy: string;

  /**
   * "asc", "desc"
   *
   * @generated from field: string sort_order = 8;
   */
  sortOrder: string;
};

/**
 * Describes the message hami.admin.v1.ListAnnouncementsRequest.
 * Use `create(ListAnnouncementsRequestSchema)` to create a new message.
 */
export const ListAnnouncementsRequestSchema: GenMessage<ListAnnouncementsRequest> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 2);

/**
 * @generated from message hami.admin.v1.ListAnnouncementsResponse
 */
export type ListAnnouncementsResponse = Message<"hami.admin.v1.ListAnnouncementsResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.Announcement announcements = 1;
   */
  announcements: Announcement[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 total_pages = 3;
   */
  totalPages: number;
};

/**
 * Describes the message hami.admin.v1.ListAnnouncementsResponse.
 * Use `create(ListAnnouncementsResponseSchema)` to create a new message.
 */
export const ListAnnouncementsResponseSchema: GenMessage<ListAnnouncementsResponse> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 3);

/**
 * 詳細取得リクエスト
 *
 * @generated from message hami.admin.v1.GetAnnouncementRequest
 */
export type GetAnnouncementRequest = Message<"hami.admin.v1.GetAnnouncementRequest"> & {
  /**
   * @generated from field: int32 announcement_id = 1;
   */
  announcementId: number;
};

/**
 * Describes the message hami.admin.v1.GetAnnouncementRequest.
 * Use `create(GetAnnouncementRequestSchema)` to create a new message.
 */
export const GetAnnouncementRequestSchema: GenMessage<GetAnnouncementRequest> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 4);

/**
 * @generated from message hami.admin.v1.GetAnnouncementResponse
 */
export type GetAnnouncementResponse = Message<"hami.admin.v1.GetAnnouncementResponse"> & {
  /**
   * @generated from field: hami.admin.v1.Announcement announcement = 1;
   */
  announcement?: Announcement;
};

/**
 * Describes the message hami.admin.v1.GetAnnouncementResponse.
 * Use `create(GetAnnouncementResponseSchema)` to create a new message.
 */
export const GetAnnouncementResponseSchema: GenMessage<GetAnnouncementResponse> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 5);

/**
 * 作成リクエスト
 *
 * @generated from message hami.admin.v1.CreateAnnouncementRequest
 */
export type CreateAnnouncementRequest = Message<"hami.admin.v1.CreateAnnouncementRequest"> & {
  /**
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: string body = 2;
   */
  body: string;

  /**
   * @generated from field: hami.admin.v1.AnnouncementCategory category = 3;
   */
  category: AnnouncementCategory;

  /**
   * @generated from field: hami.admin.v1.AnnouncementStatus status = 4;
   */
  status: AnnouncementStatus;

  /**
   * @generated from field: bool is_headline = 5;
   */
  isHeadline: boolean;

  /**
   * ISO 8601形式
   *
   * @generated from field: optional string published_at = 6;
   */
  publishedAt?: string;
};

/**
 * Describes the message hami.admin.v1.CreateAnnouncementRequest.
 * Use `create(CreateAnnouncementRequestSchema)` to create a new message.
 */
export const CreateAnnouncementRequestSchema: GenMessage<CreateAnnouncementRequest> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 6);

/**
 * @generated from message hami.admin.v1.CreateAnnouncementResponse
 */
export type CreateAnnouncementResponse = Message<"hami.admin.v1.CreateAnnouncementResponse"> & {
  /**
   * @generated from field: hami.admin.v1.Announcement announcement = 1;
   */
  announcement?: Announcement;
};

/**
 * Describes the message hami.admin.v1.CreateAnnouncementResponse.
 * Use `create(CreateAnnouncementResponseSchema)` to create a new message.
 */
export const CreateAnnouncementResponseSchema: GenMessage<CreateAnnouncementResponse> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 7);

/**
 * 更新リクエスト
 *
 * @generated from message hami.admin.v1.UpdateAnnouncementRequest
 */
export type UpdateAnnouncementRequest = Message<"hami.admin.v1.UpdateAnnouncementRequest"> & {
  /**
   * @generated from field: int32 announcement_id = 1;
   */
  announcementId: number;

  /**
   * @generated from field: optional string title = 2;
   */
  title?: string;

  /**
   * @generated from field: optional string body = 3;
   */
  body?: string;

  /**
   * @generated from field: optional hami.admin.v1.AnnouncementCategory category = 4;
   */
  category?: AnnouncementCategory;

  /**
   * @generated from field: optional hami.admin.v1.AnnouncementStatus status = 5;
   */
  status?: AnnouncementStatus;

  /**
   * @generated from field: optional bool is_headline = 6;
   */
  isHeadline?: boolean;

  /**
   * ISO 8601形式
   *
   * @generated from field: optional string published_at = 7;
   */
  publishedAt?: string;
};

/**
 * Describes the message hami.admin.v1.UpdateAnnouncementRequest.
 * Use `create(UpdateAnnouncementRequestSchema)` to create a new message.
 */
export const UpdateAnnouncementRequestSchema: GenMessage<UpdateAnnouncementRequest> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 8);

/**
 * @generated from message hami.admin.v1.UpdateAnnouncementResponse
 */
export type UpdateAnnouncementResponse = Message<"hami.admin.v1.UpdateAnnouncementResponse"> & {
  /**
   * @generated from field: hami.admin.v1.Announcement announcement = 1;
   */
  announcement?: Announcement;
};

/**
 * Describes the message hami.admin.v1.UpdateAnnouncementResponse.
 * Use `create(UpdateAnnouncementResponseSchema)` to create a new message.
 */
export const UpdateAnnouncementResponseSchema: GenMessage<UpdateAnnouncementResponse> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 9);

/**
 * 削除リクエスト
 *
 * @generated from message hami.admin.v1.DeleteAnnouncementRequest
 */
export type DeleteAnnouncementRequest = Message<"hami.admin.v1.DeleteAnnouncementRequest"> & {
  /**
   * @generated from field: int32 announcement_id = 1;
   */
  announcementId: number;
};

/**
 * Describes the message hami.admin.v1.DeleteAnnouncementRequest.
 * Use `create(DeleteAnnouncementRequestSchema)` to create a new message.
 */
export const DeleteAnnouncementRequestSchema: GenMessage<DeleteAnnouncementRequest> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 10);

/**
 * @generated from message hami.admin.v1.DeleteAnnouncementResponse
 */
export type DeleteAnnouncementResponse = Message<"hami.admin.v1.DeleteAnnouncementResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.admin.v1.DeleteAnnouncementResponse.
 * Use `create(DeleteAnnouncementResponseSchema)` to create a new message.
 */
export const DeleteAnnouncementResponseSchema: GenMessage<DeleteAnnouncementResponse> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 11);

/**
 * 添付ファイルアップロードURL取得リクエスト
 *
 * @generated from message hami.admin.v1.GetAttachmentUploadUrlRequest
 */
export type GetAttachmentUploadUrlRequest = Message<"hami.admin.v1.GetAttachmentUploadUrlRequest"> & {
  /**
   * @generated from field: int32 announcement_id = 1;
   */
  announcementId: number;

  /**
   * @generated from field: string file_name = 2;
   */
  fileName: string;

  /**
   * @generated from field: string mime_type = 3;
   */
  mimeType: string;
};

/**
 * Describes the message hami.admin.v1.GetAttachmentUploadUrlRequest.
 * Use `create(GetAttachmentUploadUrlRequestSchema)` to create a new message.
 */
export const GetAttachmentUploadUrlRequestSchema: GenMessage<GetAttachmentUploadUrlRequest> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 12);

/**
 * @generated from message hami.admin.v1.GetAttachmentUploadUrlResponse
 */
export type GetAttachmentUploadUrlResponse = Message<"hami.admin.v1.GetAttachmentUploadUrlResponse"> & {
  /**
   * @generated from field: string upload_url = 1;
   */
  uploadUrl: string;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: int32 expires_in = 3;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.admin.v1.GetAttachmentUploadUrlResponse.
 * Use `create(GetAttachmentUploadUrlResponseSchema)` to create a new message.
 */
export const GetAttachmentUploadUrlResponseSchema: GenMessage<GetAttachmentUploadUrlResponse> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 13);

/**
 * 添付ファイル作成リクエスト（アップロード完了後）
 *
 * @generated from message hami.admin.v1.CreateAttachmentRequest
 */
export type CreateAttachmentRequest = Message<"hami.admin.v1.CreateAttachmentRequest"> & {
  /**
   * @generated from field: int32 announcement_id = 1;
   */
  announcementId: number;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * @generated from field: string file_name = 3;
   */
  fileName: string;

  /**
   * @generated from field: string mime_type = 4;
   */
  mimeType: string;

  /**
   * @generated from field: hami.admin.v1.AttachmentType attachment_type = 5;
   */
  attachmentType: AttachmentType;
};

/**
 * Describes the message hami.admin.v1.CreateAttachmentRequest.
 * Use `create(CreateAttachmentRequestSchema)` to create a new message.
 */
export const CreateAttachmentRequestSchema: GenMessage<CreateAttachmentRequest> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 14);

/**
 * @generated from message hami.admin.v1.CreateAttachmentResponse
 */
export type CreateAttachmentResponse = Message<"hami.admin.v1.CreateAttachmentResponse"> & {
  /**
   * @generated from field: hami.admin.v1.AnnouncementAttachment attachment = 1;
   */
  attachment?: AnnouncementAttachment;
};

/**
 * Describes the message hami.admin.v1.CreateAttachmentResponse.
 * Use `create(CreateAttachmentResponseSchema)` to create a new message.
 */
export const CreateAttachmentResponseSchema: GenMessage<CreateAttachmentResponse> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 15);

/**
 * ファイル削除リクエスト
 *
 * @generated from message hami.admin.v1.DeleteAttachmentRequest
 */
export type DeleteAttachmentRequest = Message<"hami.admin.v1.DeleteAttachmentRequest"> & {
  /**
   * @generated from field: int32 announcement_attachment_id = 1;
   */
  announcementAttachmentId: number;
};

/**
 * Describes the message hami.admin.v1.DeleteAttachmentRequest.
 * Use `create(DeleteAttachmentRequestSchema)` to create a new message.
 */
export const DeleteAttachmentRequestSchema: GenMessage<DeleteAttachmentRequest> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 16);

/**
 * @generated from message hami.admin.v1.DeleteAttachmentResponse
 */
export type DeleteAttachmentResponse = Message<"hami.admin.v1.DeleteAttachmentResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.admin.v1.DeleteAttachmentResponse.
 * Use `create(DeleteAttachmentResponseSchema)` to create a new message.
 */
export const DeleteAttachmentResponseSchema: GenMessage<DeleteAttachmentResponse> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 17);

/**
 * ファイル順序変更リクエスト
 *
 * @generated from message hami.admin.v1.ReorderAttachmentsRequest
 */
export type ReorderAttachmentsRequest = Message<"hami.admin.v1.ReorderAttachmentsRequest"> & {
  /**
   * @generated from field: int32 announcement_id = 1;
   */
  announcementId: number;

  /**
   * @generated from field: repeated hami.admin.v1.AttachmentOrder attachment_orders = 2;
   */
  attachmentOrders: AttachmentOrder[];
};

/**
 * Describes the message hami.admin.v1.ReorderAttachmentsRequest.
 * Use `create(ReorderAttachmentsRequestSchema)` to create a new message.
 */
export const ReorderAttachmentsRequestSchema: GenMessage<ReorderAttachmentsRequest> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 18);

/**
 * @generated from message hami.admin.v1.AttachmentOrder
 */
export type AttachmentOrder = Message<"hami.admin.v1.AttachmentOrder"> & {
  /**
   * @generated from field: int32 announcement_attachment_id = 1;
   */
  announcementAttachmentId: number;

  /**
   * @generated from field: int32 display_order = 2;
   */
  displayOrder: number;
};

/**
 * Describes the message hami.admin.v1.AttachmentOrder.
 * Use `create(AttachmentOrderSchema)` to create a new message.
 */
export const AttachmentOrderSchema: GenMessage<AttachmentOrder> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 19);

/**
 * @generated from message hami.admin.v1.ReorderAttachmentsResponse
 */
export type ReorderAttachmentsResponse = Message<"hami.admin.v1.ReorderAttachmentsResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.AnnouncementAttachment attachments = 1;
   */
  attachments: AnnouncementAttachment[];
};

/**
 * Describes the message hami.admin.v1.ReorderAttachmentsResponse.
 * Use `create(ReorderAttachmentsResponseSchema)` to create a new message.
 */
export const ReorderAttachmentsResponseSchema: GenMessage<ReorderAttachmentsResponse> = /*@__PURE__*/
  messageDesc(file_admin_announcement_service, 20);

/**
 * お知らせカテゴリ
 *
 * @generated from enum hami.admin.v1.AnnouncementCategory
 */
export enum AnnouncementCategory {
  /**
   * @generated from enum value: ANNOUNCEMENT_CATEGORY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 全体向け（会員・非会員）
   *
   * @generated from enum value: ANNOUNCEMENT_CATEGORY_PUBLIC = 1;
   */
  PUBLIC = 1,

  /**
   * 会員のみ
   *
   * @generated from enum value: ANNOUNCEMENT_CATEGORY_MEMBER_ONLY = 2;
   */
  MEMBER_ONLY = 2,
}

/**
 * Describes the enum hami.admin.v1.AnnouncementCategory.
 */
export const AnnouncementCategorySchema: GenEnum<AnnouncementCategory> = /*@__PURE__*/
  enumDesc(file_admin_announcement_service, 0);

/**
 * お知らせ公開状態
 *
 * @generated from enum hami.admin.v1.AnnouncementStatus
 */
export enum AnnouncementStatus {
  /**
   * @generated from enum value: ANNOUNCEMENT_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 下書き
   *
   * @generated from enum value: ANNOUNCEMENT_STATUS_DRAFT = 1;
   */
  DRAFT = 1,

  /**
   * 公開中
   *
   * @generated from enum value: ANNOUNCEMENT_STATUS_PUBLISHED = 2;
   */
  PUBLISHED = 2,

  /**
   * 公開停止
   *
   * @generated from enum value: ANNOUNCEMENT_STATUS_SUSPENDED = 3;
   */
  SUSPENDED = 3,

  /**
   * 予約公開
   *
   * @generated from enum value: ANNOUNCEMENT_STATUS_SCHEDULED = 4;
   */
  SCHEDULED = 4,
}

/**
 * Describes the enum hami.admin.v1.AnnouncementStatus.
 */
export const AnnouncementStatusSchema: GenEnum<AnnouncementStatus> = /*@__PURE__*/
  enumDesc(file_admin_announcement_service, 1);

/**
 * 添付ファイル種別
 *
 * @generated from enum hami.admin.v1.AttachmentType
 */
export enum AttachmentType {
  /**
   * @generated from enum value: ATTACHMENT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 画像ファイル（JPEG, PNG, GIF等）
   *
   * @generated from enum value: ATTACHMENT_TYPE_IMAGE = 1;
   */
  IMAGE = 1,

  /**
   * 文書ファイル（PDF, Word, Excel等）
   *
   * @generated from enum value: ATTACHMENT_TYPE_DOCUMENT = 2;
   */
  DOCUMENT = 2,

  /**
   * その他のファイル
   *
   * @generated from enum value: ATTACHMENT_TYPE_OTHER = 3;
   */
  OTHER = 3,
}

/**
 * Describes the enum hami.admin.v1.AttachmentType.
 */
export const AttachmentTypeSchema: GenEnum<AttachmentType> = /*@__PURE__*/
  enumDesc(file_admin_announcement_service, 2);

/**
 * お知らせ管理サービス
 *
 * @generated from service hami.admin.v1.AdminAnnouncementService
 */
export const AdminAnnouncementService: GenService<{
  /**
   * お知らせ一覧取得
   *
   * @generated from rpc hami.admin.v1.AdminAnnouncementService.ListAnnouncements
   */
  listAnnouncements: {
    methodKind: "unary";
    input: typeof ListAnnouncementsRequestSchema;
    output: typeof ListAnnouncementsResponseSchema;
  },
  /**
   * お知らせ詳細取得
   *
   * @generated from rpc hami.admin.v1.AdminAnnouncementService.GetAnnouncement
   */
  getAnnouncement: {
    methodKind: "unary";
    input: typeof GetAnnouncementRequestSchema;
    output: typeof GetAnnouncementResponseSchema;
  },
  /**
   * お知らせ作成
   *
   * @generated from rpc hami.admin.v1.AdminAnnouncementService.CreateAnnouncement
   */
  createAnnouncement: {
    methodKind: "unary";
    input: typeof CreateAnnouncementRequestSchema;
    output: typeof CreateAnnouncementResponseSchema;
  },
  /**
   * お知らせ更新
   *
   * @generated from rpc hami.admin.v1.AdminAnnouncementService.UpdateAnnouncement
   */
  updateAnnouncement: {
    methodKind: "unary";
    input: typeof UpdateAnnouncementRequestSchema;
    output: typeof UpdateAnnouncementResponseSchema;
  },
  /**
   * お知らせ削除
   *
   * @generated from rpc hami.admin.v1.AdminAnnouncementService.DeleteAnnouncement
   */
  deleteAnnouncement: {
    methodKind: "unary";
    input: typeof DeleteAnnouncementRequestSchema;
    output: typeof DeleteAnnouncementResponseSchema;
  },
  /**
   * 添付ファイルアップロードURL取得
   *
   * @generated from rpc hami.admin.v1.AdminAnnouncementService.GetAttachmentUploadUrl
   */
  getAttachmentUploadUrl: {
    methodKind: "unary";
    input: typeof GetAttachmentUploadUrlRequestSchema;
    output: typeof GetAttachmentUploadUrlResponseSchema;
  },
  /**
   * 添付ファイル作成（アップロード完了後）
   *
   * @generated from rpc hami.admin.v1.AdminAnnouncementService.CreateAttachment
   */
  createAttachment: {
    methodKind: "unary";
    input: typeof CreateAttachmentRequestSchema;
    output: typeof CreateAttachmentResponseSchema;
  },
  /**
   * 添付ファイル削除
   *
   * @generated from rpc hami.admin.v1.AdminAnnouncementService.DeleteAttachment
   */
  deleteAttachment: {
    methodKind: "unary";
    input: typeof DeleteAttachmentRequestSchema;
    output: typeof DeleteAttachmentResponseSchema;
  },
  /**
   * 添付ファイル順序変更
   *
   * @generated from rpc hami.admin.v1.AdminAnnouncementService.ReorderAttachments
   */
  reorderAttachments: {
    methodKind: "unary";
    input: typeof ReorderAttachmentsRequestSchema;
    output: typeof ReorderAttachmentsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_admin_announcement_service, 0);

