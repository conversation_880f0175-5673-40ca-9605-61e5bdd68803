// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file horse_name_application_service.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { HorseNameApplicationCampaignStatus } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_name_application_service.proto.
 */
export const file_horse_name_application_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp, file_common_enums]);

/**
 * キャンペーン一覧取得リクエスト
 *
 * @generated from message hami.admin.v1.ListHorseNameApplicationCampaignsRequest
 */
export type ListHorseNameApplicationCampaignsRequest = Message<"hami.admin.v1.ListHorseNameApplicationCampaignsRequest"> & {
  /**
   * ページ番号（1から開始）
   *
   * @generated from field: optional int32 page = 1;
   */
  page?: number;

  /**
   * ページサイズ
   *
   * @generated from field: optional int32 limit = 2;
   */
  limit?: number;

  /**
   * ステータスフィルタ
   *
   * @generated from field: optional hami.admin.v1.HorseNameApplicationCampaignStatus status = 3;
   */
  status?: HorseNameApplicationCampaignStatus;

  /**
   * 馬IDフィルタ
   *
   * @generated from field: optional int64 horse_id = 4;
   */
  horseId?: bigint;

  /**
   * 馬名検索
   *
   * @generated from field: optional string horse_name = 5;
   */
  horseName?: string;
};

/**
 * Describes the message hami.admin.v1.ListHorseNameApplicationCampaignsRequest.
 * Use `create(ListHorseNameApplicationCampaignsRequestSchema)` to create a new message.
 */
export const ListHorseNameApplicationCampaignsRequestSchema: GenMessage<ListHorseNameApplicationCampaignsRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 0);

/**
 * キャンペーン一覧取得レスポンス
 *
 * @generated from message hami.admin.v1.ListHorseNameApplicationCampaignsResponse
 */
export type ListHorseNameApplicationCampaignsResponse = Message<"hami.admin.v1.ListHorseNameApplicationCampaignsResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.HorseNameApplicationCampaignListItem campaigns = 1;
   */
  campaigns: HorseNameApplicationCampaignListItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 4;
   */
  limit: number;

  /**
   * @generated from field: int32 total_pages = 5;
   */
  totalPages: number;
};

/**
 * Describes the message hami.admin.v1.ListHorseNameApplicationCampaignsResponse.
 * Use `create(ListHorseNameApplicationCampaignsResponseSchema)` to create a new message.
 */
export const ListHorseNameApplicationCampaignsResponseSchema: GenMessage<ListHorseNameApplicationCampaignsResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 1);

/**
 * キャンペーン一覧項目
 *
 * @generated from message hami.admin.v1.HorseNameApplicationCampaignListItem
 */
export type HorseNameApplicationCampaignListItem = Message<"hami.admin.v1.HorseNameApplicationCampaignListItem"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * 馬ID
   *
   * @generated from field: int64 horse_id = 2;
   */
  horseId: bigint;

  /**
   * 馬名
   *
   * @generated from field: string horse_name = 3;
   */
  horseName: string;

  /**
   * 募集年
   *
   * @generated from field: int32 recruitment_year = 4;
   */
  recruitmentYear: number;

  /**
   * 募集番号
   *
   * @generated from field: int32 recruitment_no = 5;
   */
  recruitmentNo: number;

  /**
   * 応募開始日時
   *
   * @generated from field: google.protobuf.Timestamp application_start_at = 6;
   */
  applicationStartAt?: Timestamp;

  /**
   * 応募終了日時
   *
   * @generated from field: google.protobuf.Timestamp application_end_at = 7;
   */
  applicationEndAt?: Timestamp;

  /**
   * キャンペーンステータス
   *
   * @generated from field: hami.admin.v1.HorseNameApplicationCampaignStatus status = 8;
   */
  status: HorseNameApplicationCampaignStatus;

  /**
   * 有効応募数
   *
   * @generated from field: int32 application_count = 9;
   */
  applicationCount: number;

  /**
   * 作成日時
   *
   * @generated from field: google.protobuf.Timestamp created_at = 10;
   */
  createdAt?: Timestamp;

  /**
   * 更新日時
   *
   * @generated from field: google.protobuf.Timestamp updated_at = 11;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hami.admin.v1.HorseNameApplicationCampaignListItem.
 * Use `create(HorseNameApplicationCampaignListItemSchema)` to create a new message.
 */
export const HorseNameApplicationCampaignListItemSchema: GenMessage<HorseNameApplicationCampaignListItem> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 2);

/**
 * キャンペーン作成リクエスト
 *
 * @generated from message hami.admin.v1.CreateHorseNameApplicationCampaignRequest
 */
export type CreateHorseNameApplicationCampaignRequest = Message<"hami.admin.v1.CreateHorseNameApplicationCampaignRequest"> & {
  /**
   * 馬ID
   *
   * @generated from field: int64 horse_id = 1;
   */
  horseId: bigint;

  /**
   * 応募開始日時
   *
   * @generated from field: google.protobuf.Timestamp application_start_at = 2;
   */
  applicationStartAt?: Timestamp;

  /**
   * 応募終了日時
   *
   * @generated from field: google.protobuf.Timestamp application_end_at = 3;
   */
  applicationEndAt?: Timestamp;

  /**
   * 備考
   *
   * @generated from field: optional string note = 4;
   */
  note?: string;
};

/**
 * Describes the message hami.admin.v1.CreateHorseNameApplicationCampaignRequest.
 * Use `create(CreateHorseNameApplicationCampaignRequestSchema)` to create a new message.
 */
export const CreateHorseNameApplicationCampaignRequestSchema: GenMessage<CreateHorseNameApplicationCampaignRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 3);

/**
 * キャンペーン作成レスポンス
 *
 * @generated from message hami.admin.v1.CreateHorseNameApplicationCampaignResponse
 */
export type CreateHorseNameApplicationCampaignResponse = Message<"hami.admin.v1.CreateHorseNameApplicationCampaignResponse"> & {
  /**
   * 作成されたキャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;
};

/**
 * Describes the message hami.admin.v1.CreateHorseNameApplicationCampaignResponse.
 * Use `create(CreateHorseNameApplicationCampaignResponseSchema)` to create a new message.
 */
export const CreateHorseNameApplicationCampaignResponseSchema: GenMessage<CreateHorseNameApplicationCampaignResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 4);

/**
 * キャンペーン更新リクエスト
 *
 * @generated from message hami.admin.v1.UpdateHorseNameApplicationCampaignRequest
 */
export type UpdateHorseNameApplicationCampaignRequest = Message<"hami.admin.v1.UpdateHorseNameApplicationCampaignRequest"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * 応募開始日時
   *
   * @generated from field: optional google.protobuf.Timestamp application_start_at = 2;
   */
  applicationStartAt?: Timestamp;

  /**
   * 応募終了日時
   *
   * @generated from field: optional google.protobuf.Timestamp application_end_at = 3;
   */
  applicationEndAt?: Timestamp;

  /**
   * ステータス
   *
   * @generated from field: optional hami.admin.v1.HorseNameApplicationCampaignStatus status = 4;
   */
  status?: HorseNameApplicationCampaignStatus;

  /**
   * 備考
   *
   * @generated from field: optional string note = 5;
   */
  note?: string;
};

/**
 * Describes the message hami.admin.v1.UpdateHorseNameApplicationCampaignRequest.
 * Use `create(UpdateHorseNameApplicationCampaignRequestSchema)` to create a new message.
 */
export const UpdateHorseNameApplicationCampaignRequestSchema: GenMessage<UpdateHorseNameApplicationCampaignRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 5);

/**
 * キャンペーン更新レスポンス
 *
 * @generated from message hami.admin.v1.UpdateHorseNameApplicationCampaignResponse
 */
export type UpdateHorseNameApplicationCampaignResponse = Message<"hami.admin.v1.UpdateHorseNameApplicationCampaignResponse"> & {
};

/**
 * Describes the message hami.admin.v1.UpdateHorseNameApplicationCampaignResponse.
 * Use `create(UpdateHorseNameApplicationCampaignResponseSchema)` to create a new message.
 */
export const UpdateHorseNameApplicationCampaignResponseSchema: GenMessage<UpdateHorseNameApplicationCampaignResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 6);

/**
 * キャンペーン詳細取得リクエスト
 *
 * @generated from message hami.admin.v1.GetHorseNameApplicationCampaignDetailRequest
 */
export type GetHorseNameApplicationCampaignDetailRequest = Message<"hami.admin.v1.GetHorseNameApplicationCampaignDetailRequest"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;
};

/**
 * Describes the message hami.admin.v1.GetHorseNameApplicationCampaignDetailRequest.
 * Use `create(GetHorseNameApplicationCampaignDetailRequestSchema)` to create a new message.
 */
export const GetHorseNameApplicationCampaignDetailRequestSchema: GenMessage<GetHorseNameApplicationCampaignDetailRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 7);

/**
 * キャンペーン詳細取得レスポンス
 *
 * @generated from message hami.admin.v1.GetHorseNameApplicationCampaignDetailResponse
 */
export type GetHorseNameApplicationCampaignDetailResponse = Message<"hami.admin.v1.GetHorseNameApplicationCampaignDetailResponse"> & {
  /**
   * @generated from field: hami.admin.v1.HorseNameApplicationCampaignDetail campaign = 1;
   */
  campaign?: HorseNameApplicationCampaignDetail;
};

/**
 * Describes the message hami.admin.v1.GetHorseNameApplicationCampaignDetailResponse.
 * Use `create(GetHorseNameApplicationCampaignDetailResponseSchema)` to create a new message.
 */
export const GetHorseNameApplicationCampaignDetailResponseSchema: GenMessage<GetHorseNameApplicationCampaignDetailResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 8);

/**
 * キャンペーン詳細
 *
 * @generated from message hami.admin.v1.HorseNameApplicationCampaignDetail
 */
export type HorseNameApplicationCampaignDetail = Message<"hami.admin.v1.HorseNameApplicationCampaignDetail"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * 馬ID
   *
   * @generated from field: int64 horse_id = 2;
   */
  horseId: bigint;

  /**
   * 馬名
   *
   * @generated from field: string horse_name = 3;
   */
  horseName: string;

  /**
   * 募集年
   *
   * @generated from field: int32 recruitment_year = 4;
   */
  recruitmentYear: number;

  /**
   * 募集番号
   *
   * @generated from field: int32 recruitment_no = 5;
   */
  recruitmentNo: number;

  /**
   * 応募開始日時
   *
   * @generated from field: google.protobuf.Timestamp application_start_at = 6;
   */
  applicationStartAt?: Timestamp;

  /**
   * 応募終了日時
   *
   * @generated from field: google.protobuf.Timestamp application_end_at = 7;
   */
  applicationEndAt?: Timestamp;

  /**
   * キャンペーンステータス
   *
   * @generated from field: hami.admin.v1.HorseNameApplicationCampaignStatus status = 8;
   */
  status: HorseNameApplicationCampaignStatus;

  /**
   * 備考
   *
   * @generated from field: optional string note = 9;
   */
  note?: string;

  /**
   * 有効応募数
   *
   * @generated from field: int32 application_count = 10;
   */
  applicationCount: number;

  /**
   * 作成日時
   *
   * @generated from field: google.protobuf.Timestamp created_at = 11;
   */
  createdAt?: Timestamp;

  /**
   * 更新日時
   *
   * @generated from field: google.protobuf.Timestamp updated_at = 12;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hami.admin.v1.HorseNameApplicationCampaignDetail.
 * Use `create(HorseNameApplicationCampaignDetailSchema)` to create a new message.
 */
export const HorseNameApplicationCampaignDetailSchema: GenMessage<HorseNameApplicationCampaignDetail> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 9);

/**
 * 応募一覧取得リクエスト
 *
 * @generated from message hami.admin.v1.ListHorseNameApplicationsRequest
 */
export type ListHorseNameApplicationsRequest = Message<"hami.admin.v1.ListHorseNameApplicationsRequest"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * ページ番号（1から開始）
   *
   * @generated from field: optional int32 page = 2;
   */
  page?: number;

  /**
   * ページサイズ
   *
   * @generated from field: optional int32 limit = 3;
   */
  limit?: number;

  /**
   * 会員名・馬名検索
   *
   * @generated from field: optional string search = 4;
   */
  search?: string;
};

/**
 * Describes the message hami.admin.v1.ListHorseNameApplicationsRequest.
 * Use `create(ListHorseNameApplicationsRequestSchema)` to create a new message.
 */
export const ListHorseNameApplicationsRequestSchema: GenMessage<ListHorseNameApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 10);

/**
 * 応募一覧取得レスポンス
 *
 * @generated from message hami.admin.v1.ListHorseNameApplicationsResponse
 */
export type ListHorseNameApplicationsResponse = Message<"hami.admin.v1.ListHorseNameApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.admin.v1.HorseNameApplicationItem applications = 1;
   */
  applications: HorseNameApplicationItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 4;
   */
  limit: number;

  /**
   * @generated from field: int32 total_pages = 5;
   */
  totalPages: number;
};

/**
 * Describes the message hami.admin.v1.ListHorseNameApplicationsResponse.
 * Use `create(ListHorseNameApplicationsResponseSchema)` to create a new message.
 */
export const ListHorseNameApplicationsResponseSchema: GenMessage<ListHorseNameApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 11);

/**
 * 応募項目
 *
 * @generated from message hami.admin.v1.HorseNameApplicationItem
 */
export type HorseNameApplicationItem = Message<"hami.admin.v1.HorseNameApplicationItem"> & {
  /**
   * 応募ID
   *
   * @generated from field: int64 horse_name_application_id = 1;
   */
  horseNameApplicationId: bigint;

  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 2;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * 会員ID
   *
   * @generated from field: int64 member_id = 3;
   */
  memberId: bigint;

  /**
   * 会員名
   *
   * @generated from field: string member_name = 4;
   */
  memberName: string;

  /**
   * 会員メールアドレス
   *
   * @generated from field: string member_email = 5;
   */
  memberEmail: string;

  /**
   * 提案馬名
   *
   * @generated from field: string proposed_horse_name = 6;
   */
  proposedHorseName: string;

  /**
   * 名前の由来
   *
   * @generated from field: optional string name_origin = 7;
   */
  nameOrigin?: string;

  /**
   * 備考
   *
   * @generated from field: optional string note = 8;
   */
  note?: string;

  /**
   * 応募日時
   *
   * @generated from field: google.protobuf.Timestamp submitted_at = 9;
   */
  submittedAt?: Timestamp;

  /**
   * 最新応募かどうか
   *
   * @generated from field: bool is_latest = 10;
   */
  isLatest: boolean;
};

/**
 * Describes the message hami.admin.v1.HorseNameApplicationItem.
 * Use `create(HorseNameApplicationItemSchema)` to create a new message.
 */
export const HorseNameApplicationItemSchema: GenMessage<HorseNameApplicationItem> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 12);

/**
 * 馬名選定リクエスト
 *
 * @generated from message hami.admin.v1.SelectHorseNameRequest
 */
export type SelectHorseNameRequest = Message<"hami.admin.v1.SelectHorseNameRequest"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * 選定する応募ID
   *
   * @generated from field: int64 horse_name_application_id = 2;
   */
  horseNameApplicationId: bigint;
};

/**
 * Describes the message hami.admin.v1.SelectHorseNameRequest.
 * Use `create(SelectHorseNameRequestSchema)` to create a new message.
 */
export const SelectHorseNameRequestSchema: GenMessage<SelectHorseNameRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 13);

/**
 * 馬名選定レスポンス
 *
 * @generated from message hami.admin.v1.SelectHorseNameResponse
 */
export type SelectHorseNameResponse = Message<"hami.admin.v1.SelectHorseNameResponse"> & {
  /**
   * 選定された馬名
   *
   * @generated from field: string selected_horse_name = 1;
   */
  selectedHorseName: string;
};

/**
 * Describes the message hami.admin.v1.SelectHorseNameResponse.
 * Use `create(SelectHorseNameResponseSchema)` to create a new message.
 */
export const SelectHorseNameResponseSchema: GenMessage<SelectHorseNameResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 14);

/**
 * 馬名応募管理サービス
 *
 * @generated from service hami.admin.v1.HorseNameApplicationService
 */
export const HorseNameApplicationService: GenService<{
  /**
   * キャンペーン一覧取得
   *
   * @generated from rpc hami.admin.v1.HorseNameApplicationService.ListHorseNameApplicationCampaigns
   */
  listHorseNameApplicationCampaigns: {
    methodKind: "unary";
    input: typeof ListHorseNameApplicationCampaignsRequestSchema;
    output: typeof ListHorseNameApplicationCampaignsResponseSchema;
  },
  /**
   * キャンペーン作成
   *
   * @generated from rpc hami.admin.v1.HorseNameApplicationService.CreateHorseNameApplicationCampaign
   */
  createHorseNameApplicationCampaign: {
    methodKind: "unary";
    input: typeof CreateHorseNameApplicationCampaignRequestSchema;
    output: typeof CreateHorseNameApplicationCampaignResponseSchema;
  },
  /**
   * キャンペーン更新
   *
   * @generated from rpc hami.admin.v1.HorseNameApplicationService.UpdateHorseNameApplicationCampaign
   */
  updateHorseNameApplicationCampaign: {
    methodKind: "unary";
    input: typeof UpdateHorseNameApplicationCampaignRequestSchema;
    output: typeof UpdateHorseNameApplicationCampaignResponseSchema;
  },
  /**
   * キャンペーン詳細取得
   *
   * @generated from rpc hami.admin.v1.HorseNameApplicationService.GetHorseNameApplicationCampaignDetail
   */
  getHorseNameApplicationCampaignDetail: {
    methodKind: "unary";
    input: typeof GetHorseNameApplicationCampaignDetailRequestSchema;
    output: typeof GetHorseNameApplicationCampaignDetailResponseSchema;
  },
  /**
   * 応募一覧取得（有効応募のみ）
   *
   * @generated from rpc hami.admin.v1.HorseNameApplicationService.ListHorseNameApplications
   */
  listHorseNameApplications: {
    methodKind: "unary";
    input: typeof ListHorseNameApplicationsRequestSchema;
    output: typeof ListHorseNameApplicationsResponseSchema;
  },
  /**
   * 馬名選定
   *
   * @generated from rpc hami.admin.v1.HorseNameApplicationService.SelectHorseName
   */
  selectHorseName: {
    methodKind: "unary";
    input: typeof SelectHorseNameRequestSchema;
    output: typeof SelectHorseNameResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_name_application_service, 0);

