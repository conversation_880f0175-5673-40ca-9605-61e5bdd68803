// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file admin_user_service.proto (package hami.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file admin_user_service.proto.
 */
export const file_admin_user_service: GenFile = /*@__PURE__*/
  fileDesc("ChhhZG1pbl91c2VyX3NlcnZpY2UucHJvdG8SDWhhbWkuYWRtaW4udjEiLwoMTG9naW5SZXF1ZXN0Eg0KBWVtYWlsGAEgASgJEhAKCHBhc3N3b3JkGAIgASgJIiYKDUxvZ2luUmVzcG9uc2USFQoNc2Vzc2lvbl90b2tlbhgBIAEoCSIlCgxHZXRNZVJlcXVlc3QSFQoNc2Vzc2lvbl90b2tlbhgBIAEoCSJ2Cg1HZXRNZVJlc3BvbnNlEhUKDWFkbWluX3VzZXJfaWQYASABKAkSDQoFZW1haWwYAiABKAkSDAoEbmFtZRgDIAEoCRIxCg1sYXN0X2xvZ2luX2F0GAQgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcCImCg1Mb2dvdXRSZXF1ZXN0EhUKDXNlc3Npb25fdG9rZW4YASABKAkiIQoOTG9nb3V0UmVzcG9uc2USDwoHbWVzc2FnZRgBIAEoCTLhAQoQQWRtaW5Vc2VyU2VydmljZRJCCgVMb2dpbhIbLmhhbWkuYWRtaW4udjEuTG9naW5SZXF1ZXN0GhwuaGFtaS5hZG1pbi52MS5Mb2dpblJlc3BvbnNlEkIKBUdldE1lEhsuaGFtaS5hZG1pbi52MS5HZXRNZVJlcXVlc3QaHC5oYW1pLmFkbWluLnYxLkdldE1lUmVzcG9uc2USRQoGTG9nb3V0EhwuaGFtaS5hZG1pbi52MS5Mb2dvdXRSZXF1ZXN0Gh0uaGFtaS5hZG1pbi52MS5Mb2dvdXRSZXNwb25zZWIGcHJvdG8z", [file_google_protobuf_timestamp]);

/**
 * @generated from message hami.admin.v1.LoginRequest
 */
export type LoginRequest = Message<"hami.admin.v1.LoginRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;
};

/**
 * Describes the message hami.admin.v1.LoginRequest.
 * Use `create(LoginRequestSchema)` to create a new message.
 */
export const LoginRequestSchema: GenMessage<LoginRequest> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 0);

/**
 * @generated from message hami.admin.v1.LoginResponse
 */
export type LoginResponse = Message<"hami.admin.v1.LoginResponse"> & {
  /**
   * @generated from field: string session_token = 1;
   */
  sessionToken: string;
};

/**
 * Describes the message hami.admin.v1.LoginResponse.
 * Use `create(LoginResponseSchema)` to create a new message.
 */
export const LoginResponseSchema: GenMessage<LoginResponse> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 1);

/**
 * @generated from message hami.admin.v1.GetMeRequest
 */
export type GetMeRequest = Message<"hami.admin.v1.GetMeRequest"> & {
  /**
   * @generated from field: string session_token = 1;
   */
  sessionToken: string;
};

/**
 * Describes the message hami.admin.v1.GetMeRequest.
 * Use `create(GetMeRequestSchema)` to create a new message.
 */
export const GetMeRequestSchema: GenMessage<GetMeRequest> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 2);

/**
 * @generated from message hami.admin.v1.GetMeResponse
 */
export type GetMeResponse = Message<"hami.admin.v1.GetMeResponse"> & {
  /**
   * @generated from field: string admin_user_id = 1;
   */
  adminUserId: string;

  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: google.protobuf.Timestamp last_login_at = 4;
   */
  lastLoginAt?: Timestamp;
};

/**
 * Describes the message hami.admin.v1.GetMeResponse.
 * Use `create(GetMeResponseSchema)` to create a new message.
 */
export const GetMeResponseSchema: GenMessage<GetMeResponse> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 3);

/**
 * @generated from message hami.admin.v1.LogoutRequest
 */
export type LogoutRequest = Message<"hami.admin.v1.LogoutRequest"> & {
  /**
   * @generated from field: string session_token = 1;
   */
  sessionToken: string;
};

/**
 * Describes the message hami.admin.v1.LogoutRequest.
 * Use `create(LogoutRequestSchema)` to create a new message.
 */
export const LogoutRequestSchema: GenMessage<LogoutRequest> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 4);

/**
 * @generated from message hami.admin.v1.LogoutResponse
 */
export type LogoutResponse = Message<"hami.admin.v1.LogoutResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.admin.v1.LogoutResponse.
 * Use `create(LogoutResponseSchema)` to create a new message.
 */
export const LogoutResponseSchema: GenMessage<LogoutResponse> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 5);

/**
 * @generated from service hami.admin.v1.AdminUserService
 */
export const AdminUserService: GenService<{
  /**
   * @generated from rpc hami.admin.v1.AdminUserService.Login
   */
  login: {
    methodKind: "unary";
    input: typeof LoginRequestSchema;
    output: typeof LoginResponseSchema;
  },
  /**
   * @generated from rpc hami.admin.v1.AdminUserService.GetMe
   */
  getMe: {
    methodKind: "unary";
    input: typeof GetMeRequestSchema;
    output: typeof GetMeResponseSchema;
  },
  /**
   * @generated from rpc hami.admin.v1.AdminUserService.Logout
   */
  logout: {
    methodKind: "unary";
    input: typeof LogoutRequestSchema;
    output: typeof LogoutResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_admin_user_service, 0);

