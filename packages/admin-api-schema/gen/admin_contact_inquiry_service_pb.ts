// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file admin_contact_inquiry_service.proto (package admin_contact_inquiry_service, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file admin_contact_inquiry_service.proto.
 */
export const file_admin_contact_inquiry_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * お問い合わせ情報
 *
 * @generated from message admin_contact_inquiry_service.ContactInquiry
 */
export type ContactInquiry = Message<"admin_contact_inquiry_service.ContactInquiry"> & {
  /**
   * 外部公開用ID（public_idを使用）
   *
   * @generated from field: string contact_inquiry_id = 1;
   */
  contactInquiryId: string;

  /**
   * 問い合わせ者名
   *
   * @generated from field: string inquirer_name = 2;
   */
  inquirerName: string;

  /**
   * 問い合わせ者メールアドレス
   *
   * @generated from field: string inquirer_email = 3;
   */
  inquirerEmail: string;

  /**
   * 問い合わせ者電話番号
   *
   * @generated from field: optional string inquirer_phone = 4;
   */
  inquirerPhone?: string;

  /**
   * 問い合わせカテゴリ
   *
   * @generated from field: admin_contact_inquiry_service.InquiryCategory category = 5;
   */
  category: InquiryCategory;

  /**
   * 件名
   *
   * @generated from field: string subject = 6;
   */
  subject: string;

  /**
   * 問い合わせ内容
   *
   * @generated from field: string message = 7;
   */
  message: string;

  /**
   * 対応状況
   *
   * @generated from field: admin_contact_inquiry_service.InquiryStatus status = 8;
   */
  status: InquiryStatus;

  /**
   * 優先度
   *
   * @generated from field: admin_contact_inquiry_service.InquiryPriority priority = 9;
   */
  priority: InquiryPriority;

  /**
   * 対応メッセージ
   *
   * @generated from field: optional string response_message = 10;
   */
  responseMessage?: string;

  /**
   * 対応完了日時
   *
   * @generated from field: optional google.protobuf.Timestamp responded_at = 11;
   */
  respondedAt?: Timestamp;

  /**
   * 対応者名
   *
   * @generated from field: optional string responded_by = 12;
   */
  respondedBy?: string;

  /**
   * 問い合わせ送信日時
   *
   * @generated from field: google.protobuf.Timestamp submitted_at = 13;
   */
  submittedAt?: Timestamp;

  /**
   * 作成日時
   *
   * @generated from field: google.protobuf.Timestamp created_at = 14;
   */
  createdAt?: Timestamp;

  /**
   * 更新日時
   *
   * @generated from field: google.protobuf.Timestamp updated_at = 15;
   */
  updatedAt?: Timestamp;

  /**
   * 添付ファイル一覧
   *
   * @generated from field: repeated admin_contact_inquiry_service.ContactInquiryAttachment attachments = 16;
   */
  attachments: ContactInquiryAttachment[];
};

/**
 * Describes the message admin_contact_inquiry_service.ContactInquiry.
 * Use `create(ContactInquirySchema)` to create a new message.
 */
export const ContactInquirySchema: GenMessage<ContactInquiry> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 0);

/**
 * お問い合わせ添付ファイル情報
 *
 * @generated from message admin_contact_inquiry_service.ContactInquiryAttachment
 */
export type ContactInquiryAttachment = Message<"admin_contact_inquiry_service.ContactInquiryAttachment"> & {
  /**
   * 添付ファイルID
   *
   * @generated from field: int32 contact_inquiry_attachment_id = 1;
   */
  contactInquiryAttachmentId: number;

  /**
   * ファイル名
   *
   * @generated from field: string file_name = 2;
   */
  fileName: string;

  /**
   * MIMEタイプ
   *
   * @generated from field: string mime_type = 3;
   */
  mimeType: string;

  /**
   * ファイルサイズ（bytes）
   *
   * @generated from field: int64 file_size = 4;
   */
  fileSize: bigint;

  /**
   * 表示順序
   *
   * @generated from field: int32 display_order = 5;
   */
  displayOrder: number;

  /**
   * 作成日時
   *
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;

  /**
   * 更新日時
   *
   * @generated from field: google.protobuf.Timestamp updated_at = 7;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message admin_contact_inquiry_service.ContactInquiryAttachment.
 * Use `create(ContactInquiryAttachmentSchema)` to create a new message.
 */
export const ContactInquiryAttachmentSchema: GenMessage<ContactInquiryAttachment> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 1);

/**
 * お問い合わせ一覧取得リクエスト
 *
 * @generated from message admin_contact_inquiry_service.ListContactInquiriesRequest
 */
export type ListContactInquiriesRequest = Message<"admin_contact_inquiry_service.ListContactInquiriesRequest"> & {
  /**
   * カテゴリフィルター
   *
   * @generated from field: optional int32 category = 1;
   */
  category?: number;

  /**
   * ステータスフィルター
   *
   * @generated from field: optional int32 status = 2;
   */
  status?: number;

  /**
   * 優先度フィルター
   *
   * @generated from field: optional int32 priority = 3;
   */
  priority?: number;

  /**
   * 検索クエリ
   *
   * @generated from field: optional string search_query = 4;
   */
  searchQuery?: string;

  /**
   * ページ番号（1から開始）
   *
   * @generated from field: int32 page = 5;
   */
  page: number;

  /**
   * ページサイズ
   *
   * @generated from field: int32 page_size = 6;
   */
  pageSize: number;

  /**
   * ソートフィールド
   *
   * @generated from field: string sort_by = 7;
   */
  sortBy: string;

  /**
   * ソート順序（asc/desc）
   *
   * @generated from field: string sort_order = 8;
   */
  sortOrder: string;
};

/**
 * Describes the message admin_contact_inquiry_service.ListContactInquiriesRequest.
 * Use `create(ListContactInquiriesRequestSchema)` to create a new message.
 */
export const ListContactInquiriesRequestSchema: GenMessage<ListContactInquiriesRequest> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 2);

/**
 * お問い合わせ一覧取得レスポンス
 *
 * @generated from message admin_contact_inquiry_service.ListContactInquiriesResponse
 */
export type ListContactInquiriesResponse = Message<"admin_contact_inquiry_service.ListContactInquiriesResponse"> & {
  /**
   * お問い合わせ一覧
   *
   * @generated from field: repeated admin_contact_inquiry_service.ContactInquiry contact_inquiries = 1;
   */
  contactInquiries: ContactInquiry[];

  /**
   * 総件数
   *
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * 総ページ数
   *
   * @generated from field: int32 total_pages = 3;
   */
  totalPages: number;
};

/**
 * Describes the message admin_contact_inquiry_service.ListContactInquiriesResponse.
 * Use `create(ListContactInquiriesResponseSchema)` to create a new message.
 */
export const ListContactInquiriesResponseSchema: GenMessage<ListContactInquiriesResponse> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 3);

/**
 * お問い合わせ詳細取得リクエスト
 *
 * @generated from message admin_contact_inquiry_service.GetContactInquiryRequest
 */
export type GetContactInquiryRequest = Message<"admin_contact_inquiry_service.GetContactInquiryRequest"> & {
  /**
   * お問い合わせID（public_id）
   *
   * @generated from field: string contact_inquiry_id = 1;
   */
  contactInquiryId: string;
};

/**
 * Describes the message admin_contact_inquiry_service.GetContactInquiryRequest.
 * Use `create(GetContactInquiryRequestSchema)` to create a new message.
 */
export const GetContactInquiryRequestSchema: GenMessage<GetContactInquiryRequest> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 4);

/**
 * お問い合わせ詳細取得レスポンス
 *
 * @generated from message admin_contact_inquiry_service.GetContactInquiryResponse
 */
export type GetContactInquiryResponse = Message<"admin_contact_inquiry_service.GetContactInquiryResponse"> & {
  /**
   * お問い合わせ詳細
   *
   * @generated from field: admin_contact_inquiry_service.ContactInquiry contact_inquiry = 1;
   */
  contactInquiry?: ContactInquiry;
};

/**
 * Describes the message admin_contact_inquiry_service.GetContactInquiryResponse.
 * Use `create(GetContactInquiryResponseSchema)` to create a new message.
 */
export const GetContactInquiryResponseSchema: GenMessage<GetContactInquiryResponse> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 5);

/**
 * お問い合わせ状態更新リクエスト
 *
 * @generated from message admin_contact_inquiry_service.UpdateContactInquiryStatusRequest
 */
export type UpdateContactInquiryStatusRequest = Message<"admin_contact_inquiry_service.UpdateContactInquiryStatusRequest"> & {
  /**
   * お問い合わせID（public_id）
   *
   * @generated from field: string contact_inquiry_id = 1;
   */
  contactInquiryId: string;

  /**
   * 新しいステータス
   *
   * @generated from field: int32 status = 2;
   */
  status: number;

  /**
   * 新しい優先度
   *
   * @generated from field: int32 priority = 3;
   */
  priority: number;

  /**
   * 対応メッセージ
   *
   * @generated from field: optional string response_message = 4;
   */
  responseMessage?: string;

  /**
   * 対応者名
   *
   * @generated from field: optional string responded_by = 5;
   */
  respondedBy?: string;
};

/**
 * Describes the message admin_contact_inquiry_service.UpdateContactInquiryStatusRequest.
 * Use `create(UpdateContactInquiryStatusRequestSchema)` to create a new message.
 */
export const UpdateContactInquiryStatusRequestSchema: GenMessage<UpdateContactInquiryStatusRequest> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 6);

/**
 * お問い合わせ状態更新レスポンス
 *
 * @generated from message admin_contact_inquiry_service.UpdateContactInquiryStatusResponse
 */
export type UpdateContactInquiryStatusResponse = Message<"admin_contact_inquiry_service.UpdateContactInquiryStatusResponse"> & {
  /**
   * 更新後のお問い合わせ情報
   *
   * @generated from field: admin_contact_inquiry_service.ContactInquiry contact_inquiry = 1;
   */
  contactInquiry?: ContactInquiry;
};

/**
 * Describes the message admin_contact_inquiry_service.UpdateContactInquiryStatusResponse.
 * Use `create(UpdateContactInquiryStatusResponseSchema)` to create a new message.
 */
export const UpdateContactInquiryStatusResponseSchema: GenMessage<UpdateContactInquiryStatusResponse> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 7);

/**
 * 添付ファイルダウンロードURL取得リクエスト
 *
 * @generated from message admin_contact_inquiry_service.GetAttachmentDownloadUrlRequest
 */
export type GetAttachmentDownloadUrlRequest = Message<"admin_contact_inquiry_service.GetAttachmentDownloadUrlRequest"> & {
  /**
   * @generated from field: string contact_inquiry_id = 1;
   */
  contactInquiryId: string;

  /**
   * @generated from field: int32 contact_inquiry_attachment_id = 2;
   */
  contactInquiryAttachmentId: number;
};

/**
 * Describes the message admin_contact_inquiry_service.GetAttachmentDownloadUrlRequest.
 * Use `create(GetAttachmentDownloadUrlRequestSchema)` to create a new message.
 */
export const GetAttachmentDownloadUrlRequestSchema: GenMessage<GetAttachmentDownloadUrlRequest> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 8);

/**
 * 添付ファイルダウンロードURL取得レスポンス
 *
 * @generated from message admin_contact_inquiry_service.GetAttachmentDownloadUrlResponse
 */
export type GetAttachmentDownloadUrlResponse = Message<"admin_contact_inquiry_service.GetAttachmentDownloadUrlResponse"> & {
  /**
   * @generated from field: string download_url = 1;
   */
  downloadUrl: string;

  /**
   * @generated from field: google.protobuf.Timestamp expires_at = 2;
   */
  expiresAt?: Timestamp;
};

/**
 * Describes the message admin_contact_inquiry_service.GetAttachmentDownloadUrlResponse.
 * Use `create(GetAttachmentDownloadUrlResponseSchema)` to create a new message.
 */
export const GetAttachmentDownloadUrlResponseSchema: GenMessage<GetAttachmentDownloadUrlResponse> = /*@__PURE__*/
  messageDesc(file_admin_contact_inquiry_service, 9);

/**
 * お問い合わせカテゴリ
 *
 * @generated from enum admin_contact_inquiry_service.InquiryCategory
 */
export enum InquiryCategory {
  /**
   * @generated from enum value: INQUIRY_CATEGORY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 一般的な問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_GENERAL = 1;
   */
  GENERAL = 1,

  /**
   * 出資に関する問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_INVESTMENT = 2;
   */
  INVESTMENT = 2,

  /**
   * アカウント・ログインに関する問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_ACCOUNT = 3;
   */
  ACCOUNT = 3,

  /**
   * 技術的な問題
   *
   * @generated from enum value: INQUIRY_CATEGORY_TECHNICAL = 4;
   */
  TECHNICAL = 4,

  /**
   * 請求・支払いに関する問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_BILLING = 5;
   */
  BILLING = 5,

  /**
   * 馬の情報に関する問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_HORSE_INFO = 6;
   */
  HORSE_INFO = 6,

  /**
   * 苦情・要望
   *
   * @generated from enum value: INQUIRY_CATEGORY_COMPLAINT = 7;
   */
  COMPLAINT = 7,

  /**
   * その他
   *
   * @generated from enum value: INQUIRY_CATEGORY_OTHER = 8;
   */
  OTHER = 8,
}

/**
 * Describes the enum admin_contact_inquiry_service.InquiryCategory.
 */
export const InquiryCategorySchema: GenEnum<InquiryCategory> = /*@__PURE__*/
  enumDesc(file_admin_contact_inquiry_service, 0);

/**
 * お問い合わせ状態
 *
 * @generated from enum admin_contact_inquiry_service.InquiryStatus
 */
export enum InquiryStatus {
  /**
   * @generated from enum value: INQUIRY_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 対応待ち
   *
   * @generated from enum value: INQUIRY_STATUS_PENDING = 1;
   */
  PENDING = 1,

  /**
   * 対応中
   *
   * @generated from enum value: INQUIRY_STATUS_IN_PROGRESS = 2;
   */
  IN_PROGRESS = 2,

  /**
   * 解決済み
   *
   * @generated from enum value: INQUIRY_STATUS_RESOLVED = 3;
   */
  RESOLVED = 3,

  /**
   * 終了
   *
   * @generated from enum value: INQUIRY_STATUS_CLOSED = 4;
   */
  CLOSED = 4,

  /**
   * エスカレーション
   *
   * @generated from enum value: INQUIRY_STATUS_ESCALATED = 5;
   */
  ESCALATED = 5,
}

/**
 * Describes the enum admin_contact_inquiry_service.InquiryStatus.
 */
export const InquiryStatusSchema: GenEnum<InquiryStatus> = /*@__PURE__*/
  enumDesc(file_admin_contact_inquiry_service, 1);

/**
 * お問い合わせ優先度
 *
 * @generated from enum admin_contact_inquiry_service.InquiryPriority
 */
export enum InquiryPriority {
  /**
   * @generated from enum value: INQUIRY_PRIORITY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 低
   *
   * @generated from enum value: INQUIRY_PRIORITY_LOW = 1;
   */
  LOW = 1,

  /**
   * 通常
   *
   * @generated from enum value: INQUIRY_PRIORITY_NORMAL = 2;
   */
  NORMAL = 2,

  /**
   * 高
   *
   * @generated from enum value: INQUIRY_PRIORITY_HIGH = 3;
   */
  HIGH = 3,

  /**
   * 緊急
   *
   * @generated from enum value: INQUIRY_PRIORITY_URGENT = 4;
   */
  URGENT = 4,
}

/**
 * Describes the enum admin_contact_inquiry_service.InquiryPriority.
 */
export const InquiryPrioritySchema: GenEnum<InquiryPriority> = /*@__PURE__*/
  enumDesc(file_admin_contact_inquiry_service, 2);

/**
 * お問い合わせ管理サービス
 *
 * @generated from service admin_contact_inquiry_service.AdminContactInquiryService
 */
export const AdminContactInquiryService: GenService<{
  /**
   * お問い合わせ一覧取得
   *
   * @generated from rpc admin_contact_inquiry_service.AdminContactInquiryService.ListContactInquiries
   */
  listContactInquiries: {
    methodKind: "unary";
    input: typeof ListContactInquiriesRequestSchema;
    output: typeof ListContactInquiriesResponseSchema;
  },
  /**
   * お問い合わせ詳細取得
   *
   * @generated from rpc admin_contact_inquiry_service.AdminContactInquiryService.GetContactInquiry
   */
  getContactInquiry: {
    methodKind: "unary";
    input: typeof GetContactInquiryRequestSchema;
    output: typeof GetContactInquiryResponseSchema;
  },
  /**
   * お問い合わせ状態更新
   *
   * @generated from rpc admin_contact_inquiry_service.AdminContactInquiryService.UpdateContactInquiryStatus
   */
  updateContactInquiryStatus: {
    methodKind: "unary";
    input: typeof UpdateContactInquiryStatusRequestSchema;
    output: typeof UpdateContactInquiryStatusResponseSchema;
  },
  /**
   * 添付ファイルダウンロードURL生成
   *
   * @generated from rpc admin_contact_inquiry_service.AdminContactInquiryService.GetAttachmentDownloadUrl
   */
  getAttachmentDownloadUrl: {
    methodKind: "unary";
    input: typeof GetAttachmentDownloadUrlRequestSchema;
    output: typeof GetAttachmentDownloadUrlResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_admin_contact_inquiry_service, 0);

