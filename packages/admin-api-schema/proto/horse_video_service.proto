syntax = "proto3";
package hami.admin.v1;

import "common_enums.proto";
import "models/horse_video.proto";

// 入稿動画（馬動画）の管理サービス
service HorseVideoService {
  // 動画一覧取得（馬ごと／全体・ステータス・ページング対応）
  rpc ListHorseVideos(ListHorseVideosRequest) returns (ListHorseVideosResponse);

  // 動画詳細取得
  rpc GetHorseVideo(GetHorseVideoRequest) returns (GetHorseVideoResponse);

  // 動画作成
  rpc CreateHorseVideo(CreateHorseVideoRequest)
      returns (CreateHorseVideoResponse);

  // 動画更新
  rpc UpdateHorseVideo(UpdateHorseVideoRequest)
      returns (UpdateHorseVideoResponse);

  // 動画削除（論理削除）
  rpc DeleteHorseVideo(DeleteHorseVideoRequest)
      returns (DeleteHorseVideoResponse);
}

// 一覧取得
message ListHorseVideosRequest {
  // 絞り込み条件（horse_id または recruitment_year+recruitment_no
  // のいずれかで特定可）
  optional int32 horse_id = 1;
  optional int32 recruitment_year = 2;
  optional int32 recruitment_no = 3;

  // 公開ステータス
  optional PublishStatus publish_status = 4;

  // ページング
  optional int32 page = 10;      // 1 起点
  optional int32 page_size = 11; // 既定: 20 を想定（実装側で適用）

  // 並び順
  // "video_date_desc"（既定：日付の降順）などを実装側で解釈
  optional string sort_by = 12;
}

message ListHorseVideosResponse {
  repeated HorseVideoItem videos = 1;
  int32 total_count = 2;
  int32 current_page = 3;
  int32 total_pages = 4;
  bool has_next_page = 5;
  bool has_previous_page = 6;
}

// 詳細取得
message GetHorseVideoRequest { int32 horse_video_id = 1; }
message GetHorseVideoResponse { HorseVideoItem video = 1; }

// 作成
message CreateHorseVideoRequest {
  int32 horse_id = 1;
  int32 video_year = 2;
  int32 video_month = 3;
  int32 video_day = 4;
  string title = 5;
  optional string description = 6;
  string youtube_video_id = 7;
  optional int32 start_at_seconds = 8;
  optional PublishStatus publish_status = 9; // default: DRAFT
  optional string thumbnail_image_path = 10;
}
message CreateHorseVideoResponse { HorseVideoItem video = 1; }

// 更新
message UpdateHorseVideoRequest {
  int32 horse_video_id = 1;
  optional int32 video_year = 2;
  optional int32 video_month = 3;
  optional int32 video_day = 4;
  optional string title = 5;
  optional string description = 6;
  optional string youtube_video_id = 7;
  optional int32 start_at_seconds = 8;
  optional PublishStatus publish_status = 9;
  optional string thumbnail_image_path = 10;
}
message UpdateHorseVideoResponse { HorseVideoItem video = 1; }

// 削除（論理削除）
message DeleteHorseVideoRequest { int32 horse_video_id = 1; }
message DeleteHorseVideoResponse { string message = 1; }
