syntax = "proto3";
package hami.admin.v1;

import "common_enums.proto";
import "models/horse.proto";

// 近況レポートアイテム
message HorseReportItem {
  int32 horse_report_id = 1;
  int32 horse_id = 2;
  string horse_name = 3;
  int32 report_year = 4;
  int32 report_month = 5;
  int32 report_day = 6;
  PublishStatus publish_status = 7;
  string location = 8;
  string content = 9;
  repeated HorseReportMediaItem media = 10;
  HorseListItem horse = 11;
}

// 近況レポートメディア
message HorseReportMediaItem {
  int32 horse_report_media_id = 1;
  string media_file_url = 2;
  string thumbnail_url = 3;
  MediaType media_type = 4;
  int32 display_order = 5;
}
