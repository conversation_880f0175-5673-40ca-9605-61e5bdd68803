syntax = "proto3";
package hami.admin.v1;

import "common_enums.proto";
import "models/horse.proto";

// 馬動画アイテム（管理画面用）
message HorseVideoItem {
  int32 horse_video_id = 1;
  int32 horse_id = 2;

  // 表示補助
  string horse_name = 3;

  // 動画日付
  int32 video_year = 4;
  int32 video_month = 5;
  int32 video_day = 6;

  // 公開設定
  PublishStatus publish_status = 7;

  // 表示用情報
  string title = 8;
  optional string description = 9;

  // YouTube 埋め込み情報
  string youtube_video_id = 10;
  optional int32 start_at_seconds = 11;

  // サムネイル（S3パス想定）
  optional string thumbnail_image_path = 12;

  // 参照用（一覧で馬情報を付与したい場合）
  HorseListItem horse = 20;
}

