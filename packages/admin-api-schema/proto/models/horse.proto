syntax = "proto3";
package hami.admin.v1;

import "common_enums.proto";

// 馬の詳細情報
message HorseDetail {
    int32 horse_id = 1;
    int32 recruitment_year = 2;
    int32 recruitment_no = 3;
    string recruitment_name = 4;
    string horse_name = 5;
    int32 birth_year = 6;
    int32 birth_month = 7;
    int32 birth_day = 8;
    int32 shares_total = 9;
    int32 amount_total = 10;

    // 追加項目
    optional string horse_name_en = 11;       // 馬名（英名）
    optional string name_origin = 12;         // 馬名の由来
    optional string affiliation = 13;         // 所属
    optional string stable_name = 14;         // 厩舎名
    optional HorseGender gender = 15;         // 性別
    optional CoatColor coat_color = 16;       // 毛色
    optional string sire_name = 17;           // 父馬名
    optional string dam_name = 18;            // 母馬名
    optional string broodmare_sire_name = 19; // 母父馬名
    optional string cross_detail = 49;        // クロス詳細
    optional string birth_place = 20;         // 生産地
    optional string breeder_name = 21;        // 生産者名
    optional string training_farm = 22;       // 育成牧場
    optional string note = 23;                // 備考
    optional string recruitment_comment = 24; // 募集コメント
    int32 max_shares_per_order = 25;
    bool special_flag = 26;
    PublishStatus publish_status = 27;
    RecruitmentStatus recruitment_status = 28;
    optional RecruitmentAppealLabel recruitment_appeal_label = 34;
    bool retirement_settled = 29;         // 引退済みフラグ
    optional string medical_history = 30; // 病歴手術歴
    optional HorseClass horse_class = 31; // クラス
    bool featured = 32;
    repeated string image_urls = 33;      // 画像URL配列
    // 一括出資の対象外フラグ
    bool excluded_from_bulk_investment = 45;
    optional string black_type_url = 46;
    optional string face_image_url = 47;  // 顔写真URL
    optional string pedigree_url = 48;  // 血統表URL
}

// 馬の一覧項目
message HorseListItem {
    int32 horse_id = 1;
    int32 recruitment_year = 2;
    int32 recruitment_no = 3;
    string recruitment_name = 4;
    string horse_name = 5;
    int32 birth_year = 6;
    optional HorseGender gender = 7;
    optional PublishStatus publish_status = 8;
    optional RecruitmentStatus recruitment_status = 9;
}
