syntax = "proto3";
package hami.core.admin.v1;

import "google/protobuf/timestamp.proto";

// 年度バンドルの公開ステータス
enum AnnualBundlePublishStatus {
  ANNUAL_BUNDLE_PUBLISH_STATUS_UNSPECIFIED = 0;
  AB_PUBLIC = 1;   // 公開
  AB_PRIVATE = 2;  // 非公開
}

// 年度バンドルの募集ステータス
enum AnnualBundleRecruitmentStatus {
  ANNUAL_BUNDLE_RECRUITMENT_STATUS_UNSPECIFIED = 0;
  AB_UPCOMING = 1; // 募集前
  AB_ACTIVE = 2;   // 募集中
  AB_CLOSED = 3;   // 募集終了
  AB_FULL = 4;     // 満口
}

service AnnualBundleService {
  rpc ListAnnualBundles(ListAnnualBundlesRequest) returns (ListAnnualBundlesResponse);
  rpc GetAnnualBundle(GetAnnualBundleRequest) returns (GetAnnualBundleResponse);
  rpc CreateAnnualBundle(CreateAnnualBundleRequest) returns (CreateAnnualBundleResponse);
  rpc UpdateAnnualBundle(UpdateAnnualBundleRequest) returns (UpdateAnnualBundleResponse);
  // 同年度の馬から候補を取得（選択状態付き）
  rpc ListAnnualBundleHorsesCandidates(ListAnnualBundleHorsesCandidatesRequest) returns (ListAnnualBundleHorsesCandidatesResponse);
}

message ListAnnualBundlesRequest {
  int32 page = 1;
  int32 limit = 2;
  optional int32 fiscal_year = 3; // 年度フィルタ
}

message ListAnnualBundlesResponse {
  repeated AnnualBundleListItem bundles = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 limit = 4;
  int32 total_pages = 5;
}

message AnnualBundleListItem {
  int32 annual_bundle_id = 1;
  int32 fiscal_year = 2;
  string name = 3;
  int32 shares = 4;
  AnnualBundlePublishStatus publish_status = 5;
  AnnualBundleRecruitmentStatus recruitment_status = 6;
}

message GetAnnualBundleRequest { int32 annual_bundle_id = 1; }

message GetAnnualBundleResponse { AnnualBundleDetail bundle = 1; }

message AnnualBundleDetail {
  int32 annual_bundle_id = 1;
  int32 fiscal_year = 2;
  string name = 3;
  int32 shares = 4;
  AnnualBundlePublishStatus publish_status = 5;
  AnnualBundleRecruitmentStatus recruitment_status = 6;
  repeated AnnualBundleHorseRelation horses = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
}

message AnnualBundleHorseRelation {
  int32 horse_id = 1;
  string horse_name = 2;
  string recruitment_name = 3;
  int32 recruitment_no = 4;
  int32 shares_total = 5; // 募集口数
}

message CreateAnnualBundleRequest {
  int32 fiscal_year = 1;
  string name = 2;
  int32 shares = 3;
  AnnualBundlePublishStatus publish_status = 4;
  AnnualBundleRecruitmentStatus recruitment_status = 5;
  repeated int32 horse_ids = 6; // 初期紐付け
}

message CreateAnnualBundleResponse { int32 annual_bundle_id = 1; }

message UpdateAnnualBundleRequest {
  int32 annual_bundle_id = 1;
  optional string name = 2;
  optional int32 shares = 3;
  optional AnnualBundlePublishStatus publish_status = 4;
  optional AnnualBundleRecruitmentStatus recruitment_status = 5;
  repeated int32 horse_ids = 6; // 全置換（送られたIDセットに同期）
}

message UpdateAnnualBundleResponse {}

message ListAnnualBundleHorsesCandidatesRequest {
  int32 annual_bundle_id = 1;
  int32 page = 2;
  int32 limit = 3;
  optional string search = 4; // 馬名/募集名 検索
}

message ListAnnualBundleHorsesCandidatesResponse {
  repeated AnnualBundleHorseCandidate horses = 1;
  int32 total_count = 2;
  int32 page = 3;
  int32 limit = 4;
  int32 total_pages = 5;
}

message AnnualBundleHorseCandidate {
  int32 horse_id = 1;
  string recruitment_name = 2;
  string horse_name = 3;
  int32 recruitment_year = 4;
  bool selected = 5;
  int32 recruitment_no = 6; // 募集馬ID
  int32 shares_total = 7;   // 募集口数
}


