// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file hello.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hello.proto.
 */
export const file_hello: GenFile = /*@__PURE__*/
  fileDesc("CgtoZWxsby5wcm90bxISaGFtaS5jb3JlLmFkbWluLnYxIhwKDEhlbGxvUmVxdWVzdBIMCgRuYW1lGAEgASgJIiAKDUhlbGxvUmVzcG9uc2USDwoHbWVzc2FnZRgBIAEoCTJhCgxIZWxsb1NlcnZpY2USUQoIU2F5SGVsbG8SIC5oYW1pLmNvcmUuYWRtaW4udjEuSGVsbG9SZXF1ZXN0GiEuaGFtaS5jb3JlLmFkbWluLnYxLkhlbGxvUmVzcG9uc2UiAGIGcHJvdG8z");

/**
 * @generated from message hami.core.admin.v1.HelloRequest
 */
export type HelloRequest = Message<"hami.core.admin.v1.HelloRequest"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;
};

/**
 * Describes the message hami.core.admin.v1.HelloRequest.
 * Use `create(HelloRequestSchema)` to create a new message.
 */
export const HelloRequestSchema: GenMessage<HelloRequest> = /*@__PURE__*/
  messageDesc(file_hello, 0);

/**
 * @generated from message hami.core.admin.v1.HelloResponse
 */
export type HelloResponse = Message<"hami.core.admin.v1.HelloResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.core.admin.v1.HelloResponse.
 * Use `create(HelloResponseSchema)` to create a new message.
 */
export const HelloResponseSchema: GenMessage<HelloResponse> = /*@__PURE__*/
  messageDesc(file_hello, 1);

/**
 * @generated from service hami.core.admin.v1.HelloService
 */
export const HelloService: GenService<{
  /**
   * @generated from rpc hami.core.admin.v1.HelloService.SayHello
   */
  sayHello: {
    methodKind: "unary";
    input: typeof HelloRequestSchema;
    output: typeof HelloResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hello, 0);

