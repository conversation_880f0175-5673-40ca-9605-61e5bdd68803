// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file member_information_change_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file member_information_change_service.proto.
 */
export const file_member_information_change_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * 変更申請情報（管理者用）
 *
 * @generated from message hami.core.admin.v1.MemberInformationChangeApplication
 */
export type MemberInformationChangeApplication = Message<"hami.core.admin.v1.MemberInformationChangeApplication"> & {
  /**
   * @generated from field: int32 member_information_change_application_id = 1;
   */
  memberInformationChangeApplicationId: number;

  /**
   * @generated from field: int32 member_id = 2;
   */
  memberId: number;

  /**
   * @generated from field: int32 member_number = 3;
   */
  memberNumber: number;

  /**
   * @generated from field: string member_email = 4;
   */
  memberEmail: string;

  /**
   * @generated from field: string member_name = 5;
   */
  memberName: string;

  /**
   * 現在の登録情報
   *
   * @generated from field: string current_postal_code = 6;
   */
  currentPostalCode: string;

  /**
   * @generated from field: string current_prefecture = 7;
   */
  currentPrefecture: string;

  /**
   * @generated from field: string current_address = 8;
   */
  currentAddress: string;

  /**
   * @generated from field: optional string current_apartment = 9;
   */
  currentApartment?: string;

  /**
   * @generated from field: string current_phone_number = 10;
   */
  currentPhoneNumber: string;

  /**
   * 変更希望情報
   *
   * @generated from field: optional string new_postal_code = 12;
   */
  newPostalCode?: string;

  /**
   * @generated from field: optional string new_prefecture = 13;
   */
  newPrefecture?: string;

  /**
   * @generated from field: optional string new_address = 14;
   */
  newAddress?: string;

  /**
   * @generated from field: optional string new_apartment = 15;
   */
  newApartment?: string;

  /**
   * @generated from field: optional string new_phone_number = 16;
   */
  newPhoneNumber?: string;

  /**
   * @generated from field: google.protobuf.Timestamp requested_change_date = 18;
   */
  requestedChangeDate?: Timestamp;

  /**
   * @generated from field: optional string reason = 19;
   */
  reason?: string;

  /**
   * @generated from field: hami.core.admin.v1.MemberInformationChangeStatus status = 20;
   */
  status: MemberInformationChangeStatus;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 21;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 22;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hami.core.admin.v1.MemberInformationChangeApplication.
 * Use `create(MemberInformationChangeApplicationSchema)` to create a new message.
 */
export const MemberInformationChangeApplicationSchema: GenMessage<MemberInformationChangeApplication> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 0);

/**
 * 申請一覧取得リクエスト（管理者用）
 *
 * @generated from message hami.core.admin.v1.GetMemberInformationChangeApplicationsRequest
 */
export type GetMemberInformationChangeApplicationsRequest = Message<"hami.core.admin.v1.GetMemberInformationChangeApplicationsRequest"> & {
  /**
   * @generated from field: optional int32 page = 1;
   */
  page?: number;

  /**
   * @generated from field: optional int32 limit = 2;
   */
  limit?: number;

  /**
   * @generated from field: optional hami.core.admin.v1.MemberInformationChangeStatus status = 3;
   */
  status?: MemberInformationChangeStatus;

  /**
   * @generated from field: optional hami.core.admin.v1.MemberInformationChangeSortField sort_by = 4;
   */
  sortBy?: MemberInformationChangeSortField;

  /**
   * "asc", "desc"
   *
   * @generated from field: optional string sort_order = 5;
   */
  sortOrder?: string;
};

/**
 * Describes the message hami.core.admin.v1.GetMemberInformationChangeApplicationsRequest.
 * Use `create(GetMemberInformationChangeApplicationsRequestSchema)` to create a new message.
 */
export const GetMemberInformationChangeApplicationsRequestSchema: GenMessage<GetMemberInformationChangeApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 1);

/**
 * 申請一覧取得レスポンス（管理者用）
 *
 * @generated from message hami.core.admin.v1.GetMemberInformationChangeApplicationsResponse
 */
export type GetMemberInformationChangeApplicationsResponse = Message<"hami.core.admin.v1.GetMemberInformationChangeApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.MemberInformationChangeApplication applications = 1;
   */
  applications: MemberInformationChangeApplication[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 4;
   */
  limit: number;
};

/**
 * Describes the message hami.core.admin.v1.GetMemberInformationChangeApplicationsResponse.
 * Use `create(GetMemberInformationChangeApplicationsResponseSchema)` to create a new message.
 */
export const GetMemberInformationChangeApplicationsResponseSchema: GenMessage<GetMemberInformationChangeApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 2);

/**
 * 申請詳細取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetMemberInformationChangeApplicationRequest
 */
export type GetMemberInformationChangeApplicationRequest = Message<"hami.core.admin.v1.GetMemberInformationChangeApplicationRequest"> & {
  /**
   * @generated from field: int32 member_information_change_application_id = 1;
   */
  memberInformationChangeApplicationId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetMemberInformationChangeApplicationRequest.
 * Use `create(GetMemberInformationChangeApplicationRequestSchema)` to create a new message.
 */
export const GetMemberInformationChangeApplicationRequestSchema: GenMessage<GetMemberInformationChangeApplicationRequest> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 3);

/**
 * 申請詳細取得レスポンス
 *
 * @generated from message hami.core.admin.v1.GetMemberInformationChangeApplicationResponse
 */
export type GetMemberInformationChangeApplicationResponse = Message<"hami.core.admin.v1.GetMemberInformationChangeApplicationResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.MemberInformationChangeApplication application = 1;
   */
  application?: MemberInformationChangeApplication;

  /**
   * @generated from field: repeated hami.core.admin.v1.MemberInformationChangeReviewLog review_logs = 2;
   */
  reviewLogs: MemberInformationChangeReviewLog[];
};

/**
 * Describes the message hami.core.admin.v1.GetMemberInformationChangeApplicationResponse.
 * Use `create(GetMemberInformationChangeApplicationResponseSchema)` to create a new message.
 */
export const GetMemberInformationChangeApplicationResponseSchema: GenMessage<GetMemberInformationChangeApplicationResponse> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 4);

/**
 * 申請審査リクエスト
 *
 * @generated from message hami.core.admin.v1.ReviewMemberInformationChangeApplicationRequest
 */
export type ReviewMemberInformationChangeApplicationRequest = Message<"hami.core.admin.v1.ReviewMemberInformationChangeApplicationRequest"> & {
  /**
   * @generated from field: int32 member_information_change_application_id = 1;
   */
  memberInformationChangeApplicationId: number;

  /**
   * @generated from field: hami.core.admin.v1.MemberInformationChangeReviewType review_type = 2;
   */
  reviewType: MemberInformationChangeReviewType;

  /**
   * @generated from field: optional string comment = 3;
   */
  comment?: string;
};

/**
 * Describes the message hami.core.admin.v1.ReviewMemberInformationChangeApplicationRequest.
 * Use `create(ReviewMemberInformationChangeApplicationRequestSchema)` to create a new message.
 */
export const ReviewMemberInformationChangeApplicationRequestSchema: GenMessage<ReviewMemberInformationChangeApplicationRequest> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 5);

/**
 * 申請審査レスポンス
 *
 * @generated from message hami.core.admin.v1.ReviewMemberInformationChangeApplicationResponse
 */
export type ReviewMemberInformationChangeApplicationResponse = Message<"hami.core.admin.v1.ReviewMemberInformationChangeApplicationResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.MemberInformationChangeApplication application = 1;
   */
  application?: MemberInformationChangeApplication;
};

/**
 * Describes the message hami.core.admin.v1.ReviewMemberInformationChangeApplicationResponse.
 * Use `create(ReviewMemberInformationChangeApplicationResponseSchema)` to create a new message.
 */
export const ReviewMemberInformationChangeApplicationResponseSchema: GenMessage<ReviewMemberInformationChangeApplicationResponse> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 6);

/**
 * 審査ログ
 *
 * @generated from message hami.core.admin.v1.MemberInformationChangeReviewLog
 */
export type MemberInformationChangeReviewLog = Message<"hami.core.admin.v1.MemberInformationChangeReviewLog"> & {
  /**
   * @generated from field: int32 member_information_change_review_log_id = 1;
   */
  memberInformationChangeReviewLogId: number;

  /**
   * @generated from field: int32 member_information_change_application_id = 2;
   */
  memberInformationChangeApplicationId: number;

  /**
   * @generated from field: google.protobuf.Timestamp timestamp = 3;
   */
  timestamp?: Timestamp;

  /**
   * @generated from field: string reviewer = 4;
   */
  reviewer: string;

  /**
   * @generated from field: hami.core.admin.v1.MemberInformationChangeReviewType review_type = 5;
   */
  reviewType: MemberInformationChangeReviewType;

  /**
   * @generated from field: optional string comment = 6;
   */
  comment?: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 7;
   */
  createdAt?: Timestamp;
};

/**
 * Describes the message hami.core.admin.v1.MemberInformationChangeReviewLog.
 * Use `create(MemberInformationChangeReviewLogSchema)` to create a new message.
 */
export const MemberInformationChangeReviewLogSchema: GenMessage<MemberInformationChangeReviewLog> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 7);

/**
 * 変更申請ステータス
 *
 * @generated from enum hami.core.admin.v1.MemberInformationChangeStatus
 */
export enum MemberInformationChangeStatus {
  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_PENDING = 1;
   */
  PENDING = 1,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_APPROVED = 2;
   */
  APPROVED = 2,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_REJECTED = 3;
   */
  REJECTED = 3,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_CANCELLED = 4;
   */
  CANCELLED = 4,
}

/**
 * Describes the enum hami.core.admin.v1.MemberInformationChangeStatus.
 */
export const MemberInformationChangeStatusSchema: GenEnum<MemberInformationChangeStatus> = /*@__PURE__*/
  enumDesc(file_member_information_change_service, 0);

/**
 * 審査タイプ（登録情報変更用）
 *
 * @generated from enum hami.core.admin.v1.MemberInformationChangeReviewType
 */
export enum MemberInformationChangeReviewType {
  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_REVIEW_TYPE_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_REVIEW_TYPE_APPROVE = 1;
   */
  APPROVE = 1,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_REVIEW_TYPE_REJECT = 2;
   */
  REJECT = 2,
}

/**
 * Describes the enum hami.core.admin.v1.MemberInformationChangeReviewType.
 */
export const MemberInformationChangeReviewTypeSchema: GenEnum<MemberInformationChangeReviewType> = /*@__PURE__*/
  enumDesc(file_member_information_change_service, 1);

/**
 * ソートフィールド（登録情報変更申請用）
 *
 * @generated from enum hami.core.admin.v1.MemberInformationChangeSortField
 */
export enum MemberInformationChangeSortField {
  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_SORT_FIELD_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_SORT_FIELD_CREATED_AT = 1;
   */
  CREATED_AT = 1,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_SORT_FIELD_UPDATED_AT = 2;
   */
  UPDATED_AT = 2,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_SORT_FIELD_REQUESTED_CHANGE_DATE = 3;
   */
  REQUESTED_CHANGE_DATE = 3,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_SORT_FIELD_MEMBER_INFORMATION_CHANGE_APPLICATION_ID = 4;
   */
  MEMBER_INFORMATION_CHANGE_APPLICATION_ID = 4,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_SORT_FIELD_STATUS = 5;
   */
  STATUS = 5,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_SORT_FIELD_MEMBER_NUMBER = 6;
   */
  MEMBER_NUMBER = 6,
}

/**
 * Describes the enum hami.core.admin.v1.MemberInformationChangeSortField.
 */
export const MemberInformationChangeSortFieldSchema: GenEnum<MemberInformationChangeSortField> = /*@__PURE__*/
  enumDesc(file_member_information_change_service, 2);

/**
 * 管理者向け登録情報変更申請サービス
 *
 * @generated from service hami.core.admin.v1.MemberInformationChangeService
 */
export const MemberInformationChangeService: GenService<{
  /**
   * 申請一覧取得（管理者用）
   *
   * @generated from rpc hami.core.admin.v1.MemberInformationChangeService.GetMemberInformationChangeApplications
   */
  getMemberInformationChangeApplications: {
    methodKind: "unary";
    input: typeof GetMemberInformationChangeApplicationsRequestSchema;
    output: typeof GetMemberInformationChangeApplicationsResponseSchema;
  },
  /**
   * 申請詳細取得
   *
   * @generated from rpc hami.core.admin.v1.MemberInformationChangeService.GetMemberInformationChangeApplication
   */
  getMemberInformationChangeApplication: {
    methodKind: "unary";
    input: typeof GetMemberInformationChangeApplicationRequestSchema;
    output: typeof GetMemberInformationChangeApplicationResponseSchema;
  },
  /**
   * 申請審査（承認・拒否）
   *
   * @generated from rpc hami.core.admin.v1.MemberInformationChangeService.ReviewMemberInformationChangeApplication
   */
  reviewMemberInformationChangeApplication: {
    methodKind: "unary";
    input: typeof ReviewMemberInformationChangeApplicationRequestSchema;
    output: typeof ReviewMemberInformationChangeApplicationResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_member_information_change_service, 0);

