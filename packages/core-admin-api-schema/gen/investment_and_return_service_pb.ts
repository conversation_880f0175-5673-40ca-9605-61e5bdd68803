// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file investment_and_return_service.proto (package investment_and_return_service, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file investment_and_return_service.proto.
 */
export const file_investment_and_return_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * 出資・分配金作成リクエスト
 *
 * @generated from message investment_and_return_service.CreateInvestmentAndReturnRequest
 */
export type CreateInvestmentAndReturnRequest = Message<"investment_and_return_service.CreateInvestmentAndReturnRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 target_year = 2;
   */
  targetYear: number;

  /**
   * @generated from field: int32 target_month = 3;
   */
  targetMonth: number;

  /**
   * @generated from field: bool yearly_return_target_flag = 4;
   */
  yearlyReturnTargetFlag: boolean;

  /**
   * @generated from field: bool retirement_flag = 5;
   */
  retirementFlag: boolean;
};

/**
 * Describes the message investment_and_return_service.CreateInvestmentAndReturnRequest.
 * Use `create(CreateInvestmentAndReturnRequestSchema)` to create a new message.
 */
export const CreateInvestmentAndReturnRequestSchema: GenMessage<CreateInvestmentAndReturnRequest> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 0);

/**
 * 出資・分配金作成レスポンス
 *
 * @generated from message investment_and_return_service.CreateInvestmentAndReturnResponse
 */
export type CreateInvestmentAndReturnResponse = Message<"investment_and_return_service.CreateInvestmentAndReturnResponse"> & {
};

/**
 * Describes the message investment_and_return_service.CreateInvestmentAndReturnResponse.
 * Use `create(CreateInvestmentAndReturnResponseSchema)` to create a new message.
 */
export const CreateInvestmentAndReturnResponseSchema: GenMessage<CreateInvestmentAndReturnResponse> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 1);

/**
 * 請求・支払いデータ作成リクエスト
 *
 * @generated from message investment_and_return_service.CreateMemberClaimAndPayRequest
 */
export type CreateMemberClaimAndPayRequest = Message<"investment_and_return_service.CreateMemberClaimAndPayRequest"> & {
  /**
   * ISO8601形式
   *
   * @generated from field: string created_date = 1;
   */
  createdDate: string;

  /**
   * 月会費
   *
   * @generated from field: int32 membership_dues = 2;
   */
  membershipDues: number;

  /**
   * 税率（パーセント）
   *
   * @generated from field: int32 tax_rate = 3;
   */
  taxRate: number;
};

/**
 * Describes the message investment_and_return_service.CreateMemberClaimAndPayRequest.
 * Use `create(CreateMemberClaimAndPayRequestSchema)` to create a new message.
 */
export const CreateMemberClaimAndPayRequestSchema: GenMessage<CreateMemberClaimAndPayRequest> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 2);

/**
 * 請求・支払いデータ作成レスポンス
 *
 * @generated from message investment_and_return_service.CreateMemberClaimAndPayResponse
 */
export type CreateMemberClaimAndPayResponse = Message<"investment_and_return_service.CreateMemberClaimAndPayResponse"> & {
};

/**
 * Describes the message investment_and_return_service.CreateMemberClaimAndPayResponse.
 * Use `create(CreateMemberClaimAndPayResponseSchema)` to create a new message.
 */
export const CreateMemberClaimAndPayResponseSchema: GenMessage<CreateMemberClaimAndPayResponse> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 3);

/**
 * リクエスト: 馬ID
 *
 * @generated from message investment_and_return_service.GetCreatedInvestmentAndReturnSharesSumByHorseIdRequest
 */
export type GetCreatedInvestmentAndReturnSharesSumByHorseIdRequest = Message<"investment_and_return_service.GetCreatedInvestmentAndReturnSharesSumByHorseIdRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message investment_and_return_service.GetCreatedInvestmentAndReturnSharesSumByHorseIdRequest.
 * Use `create(GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema)` to create a new message.
 */
export const GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema: GenMessage<GetCreatedInvestmentAndReturnSharesSumByHorseIdRequest> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 4);

/**
 * レスポンス: createdDateごとのsharesSum合計リスト
 *
 * @generated from message investment_and_return_service.GetCreatedInvestmentAndReturnSharesSumByHorseIdResponse
 */
export type GetCreatedInvestmentAndReturnSharesSumByHorseIdResponse = Message<"investment_and_return_service.GetCreatedInvestmentAndReturnSharesSumByHorseIdResponse"> & {
  /**
   * @generated from field: repeated investment_and_return_service.CreatedInvestmentAndReturnSharesSumByDate list = 1;
   */
  list: CreatedInvestmentAndReturnSharesSumByDate[];
};

/**
 * Describes the message investment_and_return_service.GetCreatedInvestmentAndReturnSharesSumByHorseIdResponse.
 * Use `create(GetCreatedInvestmentAndReturnSharesSumByHorseIdResponseSchema)` to create a new message.
 */
export const GetCreatedInvestmentAndReturnSharesSumByHorseIdResponseSchema: GenMessage<GetCreatedInvestmentAndReturnSharesSumByHorseIdResponse> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 5);

/**
 * createdDateごとのsharesSum合計
 *
 * @generated from message investment_and_return_service.CreatedInvestmentAndReturnSharesSumByDate
 */
export type CreatedInvestmentAndReturnSharesSumByDate = Message<"investment_and_return_service.CreatedInvestmentAndReturnSharesSumByDate"> & {
  /**
   * ISO8601形式
   *
   * @generated from field: string created_date = 1;
   */
  createdDate: string;

  /**
   * @generated from field: int32 shares_sum = 2;
   */
  sharesSum: number;
};

/**
 * Describes the message investment_and_return_service.CreatedInvestmentAndReturnSharesSumByDate.
 * Use `create(CreatedInvestmentAndReturnSharesSumByDateSchema)` to create a new message.
 */
export const CreatedInvestmentAndReturnSharesSumByDateSchema: GenMessage<CreatedInvestmentAndReturnSharesSumByDate> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 6);

/**
 * リクエスト: 馬IDと作成日
 *
 * @generated from message investment_and_return_service.GetInvestmentAndReturnListByHorseIdAndCreatedDateRequest
 */
export type GetInvestmentAndReturnListByHorseIdAndCreatedDateRequest = Message<"investment_and_return_service.GetInvestmentAndReturnListByHorseIdAndCreatedDateRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 created_date_year = 2;
   */
  createdDateYear: number;

  /**
   * @generated from field: int32 created_date_month = 3;
   */
  createdDateMonth: number;

  /**
   * @generated from field: int32 created_date_day = 4;
   */
  createdDateDay: number;
};

/**
 * Describes the message investment_and_return_service.GetInvestmentAndReturnListByHorseIdAndCreatedDateRequest.
 * Use `create(GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema)` to create a new message.
 */
export const GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema: GenMessage<GetInvestmentAndReturnListByHorseIdAndCreatedDateRequest> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 7);

/**
 * レスポンス: InvestmentAndReturnの全リスト
 *
 * @generated from message investment_and_return_service.GetInvestmentAndReturnListByHorseIdAndCreatedDateResponse
 */
export type GetInvestmentAndReturnListByHorseIdAndCreatedDateResponse = Message<"investment_and_return_service.GetInvestmentAndReturnListByHorseIdAndCreatedDateResponse"> & {
  /**
   * @generated from field: repeated investment_and_return_service.InvestmentAndReturnListItem list = 1;
   */
  list: InvestmentAndReturnListItem[];
};

/**
 * Describes the message investment_and_return_service.GetInvestmentAndReturnListByHorseIdAndCreatedDateResponse.
 * Use `create(GetInvestmentAndReturnListByHorseIdAndCreatedDateResponseSchema)` to create a new message.
 */
export const GetInvestmentAndReturnListByHorseIdAndCreatedDateResponseSchema: GenMessage<GetInvestmentAndReturnListByHorseIdAndCreatedDateResponse> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 8);

/**
 * InvestmentAndReturnリストアイテム
 *
 * @generated from message investment_and_return_service.InvestmentAndReturnListItem
 */
export type InvestmentAndReturnListItem = Message<"investment_and_return_service.InvestmentAndReturnListItem"> & {
  /**
   * @generated from field: int32 investment_and_return_id = 1;
   */
  investmentAndReturnId: number;

  /**
   * @generated from field: int32 created_date_year = 2;
   */
  createdDateYear: number;

  /**
   * @generated from field: int32 created_date_month = 3;
   */
  createdDateMonth: number;

  /**
   * @generated from field: int32 created_date_day = 4;
   */
  createdDateDay: number;

  /**
   * @generated from field: int32 progressed_month = 5;
   */
  progressedMonth: number;

  /**
   * @generated from field: bool yearly_return_target_flag = 6;
   */
  yearlyReturnTargetFlag: boolean;

  /**
   * @generated from field: int32 billing_amount = 7;
   */
  billingAmount: number;

  /**
   * @generated from field: int32 payment_amount = 8;
   */
  paymentAmount: number;

  /**
   * @generated from field: int32 member_id = 9;
   */
  memberId: number;

  /**
   * @generated from field: string member_name = 10;
   */
  memberName: string;
};

/**
 * Describes the message investment_and_return_service.InvestmentAndReturnListItem.
 * Use `create(InvestmentAndReturnListItemSchema)` to create a new message.
 */
export const InvestmentAndReturnListItemSchema: GenMessage<InvestmentAndReturnListItem> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 9);

/**
 * リクエスト: 馬ID、作成日、memberId
 *
 * @generated from message investment_and_return_service.GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequest
 */
export type GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequest = Message<"investment_and_return_service.GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 created_date_year = 2;
   */
  createdDateYear: number;

  /**
   * @generated from field: int32 created_date_month = 3;
   */
  createdDateMonth: number;

  /**
   * @generated from field: int32 created_date_day = 4;
   */
  createdDateDay: number;

  /**
   * @generated from field: int32 member_id = 5;
   */
  memberId: number;
};

/**
 * Describes the message investment_and_return_service.GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequest.
 * Use `create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema)` to create a new message.
 */
export const GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema: GenMessage<GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequest> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 10);

/**
 * レスポンス: InvestmentAndReturn詳細情報（関連データ含む）
 *
 * @generated from message investment_and_return_service.GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponse
 */
export type GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponse = Message<"investment_and_return_service.GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponse"> & {
  /**
   * @generated from field: investment_and_return_service.InvestmentAndReturn investment_and_return = 1;
   */
  investmentAndReturn?: InvestmentAndReturn;
};

/**
 * Describes the message investment_and_return_service.GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponse.
 * Use `create(GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponseSchema)` to create a new message.
 */
export const GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponseSchema: GenMessage<GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponse> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 11);

/**
 * InvestmentAndReturn詳細情報
 *
 * @generated from message investment_and_return_service.InvestmentAndReturn
 */
export type InvestmentAndReturn = Message<"investment_and_return_service.InvestmentAndReturn"> & {
  /**
   * @generated from field: int32 investment_and_return_id = 1;
   */
  investmentAndReturnId: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * @generated from field: int32 member_id = 3;
   */
  memberId: number;

  /**
   * @generated from field: int32 created_date_year = 4;
   */
  createdDateYear: number;

  /**
   * @generated from field: int32 created_date_month = 5;
   */
  createdDateMonth: number;

  /**
   * @generated from field: int32 created_date_day = 6;
   */
  createdDateDay: number;

  /**
   * @generated from field: int32 progressed_month = 7;
   */
  progressedMonth: number;

  /**
   * @generated from field: bool yearly_return_target_flag = 8;
   */
  yearlyReturnTargetFlag: boolean;

  /**
   * @generated from field: int32 billing_amount = 9;
   */
  billingAmount: number;

  /**
   * @generated from field: int32 payment_amount = 10;
   */
  paymentAmount: number;

  /**
   * @generated from field: int32 shares_number = 11;
   */
  sharesNumber: number;

  /**
   * @generated from field: optional investment_and_return_service.InvestmentAndReturnInvestment investment = 12;
   */
  investment?: InvestmentAndReturnInvestment;

  /**
   * @generated from field: repeated investment_and_return_service.InvestmentAndReturnReturn returns = 13;
   */
  returns: InvestmentAndReturnReturn[];
};

/**
 * Describes the message investment_and_return_service.InvestmentAndReturn.
 * Use `create(InvestmentAndReturnSchema)` to create a new message.
 */
export const InvestmentAndReturnSchema: GenMessage<InvestmentAndReturn> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 12);

/**
 * InvestmentAndReturnInvestment詳細情報
 *
 * @generated from message investment_and_return_service.InvestmentAndReturnInvestment
 */
export type InvestmentAndReturnInvestment = Message<"investment_and_return_service.InvestmentAndReturnInvestment"> & {
  /**
   * @generated from field: int32 investment_and_return_investment_id = 1;
   */
  investmentAndReturnInvestmentId: number;

  /**
   * @generated from field: int32 investment_and_return_id = 2;
   */
  investmentAndReturnId: number;

  /**
   * @generated from field: int32 racehorse_investment_equivalent = 3;
   */
  racehorseInvestmentEquivalent: number;

  /**
   * @generated from field: int32 discount_allocation = 4;
   */
  discountAllocation: number;

  /**
   * @generated from field: int32 racehorse_investment = 5;
   */
  racehorseInvestment: number;

  /**
   * @generated from field: int32 running_cost = 6;
   */
  runningCost: number;

  /**
   * @generated from field: int32 subsidy = 7;
   */
  subsidy: number;

  /**
   * @generated from field: int32 retroactive_running_cost = 8;
   */
  retroactiveRunningCost: number;

  /**
   * @generated from field: int32 running_cost_investment = 9;
   */
  runningCostInvestment: number;

  /**
   * @generated from field: int32 insurance_investment = 10;
   */
  insuranceInvestment: number;

  /**
   * @generated from field: int32 other_investment = 11;
   */
  otherInvestment: number;

  /**
   * @generated from field: int32 current_month_investment_total = 12;
   */
  currentMonthInvestmentTotal: number;
};

/**
 * Describes the message investment_and_return_service.InvestmentAndReturnInvestment.
 * Use `create(InvestmentAndReturnInvestmentSchema)` to create a new message.
 */
export const InvestmentAndReturnInvestmentSchema: GenMessage<InvestmentAndReturnInvestment> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 13);

/**
 * InvestmentAndReturnReturn詳細情報
 *
 * @generated from message investment_and_return_service.InvestmentAndReturnReturn
 */
export type InvestmentAndReturnReturn = Message<"investment_and_return_service.InvestmentAndReturnReturn"> & {
  /**
   * @generated from field: int32 investment_and_return_return_id = 1;
   */
  investmentAndReturnReturnId: number;

  /**
   * @generated from field: int32 investment_and_return_id = 2;
   */
  investmentAndReturnId: number;

  /**
   * @generated from field: string return_category = 3;
   */
  returnCategory: string;

  /**
   * @generated from field: optional int32 investment_refund_paid_up_to_last_month = 4;
   */
  investmentRefundPaidUpToLastMonth?: number;

  /**
   * @generated from field: int32 refundable_investment_amount = 5;
   */
  refundableInvestmentAmount: number;

  /**
   * @generated from field: int32 distribution_target_amount = 6;
   */
  distributionTargetAmount: number;

  /**
   * @generated from field: int32 distribution_target_amount_refundable = 7;
   */
  distributionTargetAmountRefundable: number;

  /**
   * @generated from field: int32 distribution_target_amount_profit = 8;
   */
  distributionTargetAmountProfit: number;

  /**
   * @generated from field: int32 distribution_target_amount_withholding_tax = 9;
   */
  distributionTargetAmountWithholdingTax: number;

  /**
   * @generated from field: int32 distribution_amount = 10;
   */
  distributionAmount: number;

  /**
   * @generated from field: int32 refundable_investment_amount_carried_forward = 11;
   */
  refundableInvestmentAmountCarriedForward: number;
};

/**
 * Describes the message investment_and_return_service.InvestmentAndReturnReturn.
 * Use `create(InvestmentAndReturnReturnSchema)` to create a new message.
 */
export const InvestmentAndReturnReturnSchema: GenMessage<InvestmentAndReturnReturn> = /*@__PURE__*/
  messageDesc(file_investment_and_return_service, 14);

/**
 * 出資・分配金サービス
 *
 * @generated from service investment_and_return_service.InvestmentAndReturnService
 */
export const InvestmentAndReturnService: GenService<{
  /**
   * 出資・分配金作成
   *
   * @generated from rpc investment_and_return_service.InvestmentAndReturnService.CreateInvestmentAndReturn
   */
  createInvestmentAndReturn: {
    methodKind: "unary";
    input: typeof CreateInvestmentAndReturnRequestSchema;
    output: typeof CreateInvestmentAndReturnResponseSchema;
  },
  /**
   * 指定したcreatedDateについて請求・支払いデータを作成
   *
   * @generated from rpc investment_and_return_service.InvestmentAndReturnService.CreateMemberClaimAndPay
   */
  createMemberClaimAndPay: {
    methodKind: "unary";
    input: typeof CreateMemberClaimAndPayRequestSchema;
    output: typeof CreateMemberClaimAndPayResponseSchema;
  },
  /**
   * 指定した馬IDのcreatedDateごとのsharesSum合計リストを取得
   *
   * @generated from rpc investment_and_return_service.InvestmentAndReturnService.GetCreatedInvestmentAndReturnSharesSumByHorseId
   */
  getCreatedInvestmentAndReturnSharesSumByHorseId: {
    methodKind: "unary";
    input: typeof GetCreatedInvestmentAndReturnSharesSumByHorseIdRequestSchema;
    output: typeof GetCreatedInvestmentAndReturnSharesSumByHorseIdResponseSchema;
  },
  /**
   * 指定した馬IDと作成日のInvestmentAndReturnの全リストを取得
   *
   * @generated from rpc investment_and_return_service.InvestmentAndReturnService.GetInvestmentAndReturnListByHorseIdAndCreatedDate
   */
  getInvestmentAndReturnListByHorseIdAndCreatedDate: {
    methodKind: "unary";
    input: typeof GetInvestmentAndReturnListByHorseIdAndCreatedDateRequestSchema;
    output: typeof GetInvestmentAndReturnListByHorseIdAndCreatedDateResponseSchema;
  },
  /**
   * 指定した馬・作成日・memberIdでInvestmentAndReturnを特定して返却
   *
   * @generated from rpc investment_and_return_service.InvestmentAndReturnService.GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId
   */
  getInvestmentAndReturnByHorseIdAndCreatedDateAndMemberId: {
    methodKind: "unary";
    input: typeof GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdRequestSchema;
    output: typeof GetInvestmentAndReturnByHorseIdAndCreatedDateAndMemberIdResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_investment_and_return_service, 0);

