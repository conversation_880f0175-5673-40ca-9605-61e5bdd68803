// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file biller_service.proto (package core.admin.biller, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file biller_service.proto.
 */
export const file_biller_service: GenFile = /*@__PURE__*/
  fileDesc("ChRiaWxsZXJfc2VydmljZS5wcm90bxIRY29yZS5hZG1pbi5iaWxsZXIiywEKBkJpbGxlchIKCgJpZBgBIAEoBRIMCgRuYW1lGAIgASgJEg8KB2FkZHJlc3MYAyABKAkSEQoJYmFua19uYW1lGAQgASgJEhMKC2JhbmtfYnJhbmNoGAUgASgJEhQKDGJhbmtfYWNjb3VudBgGIAEoCRI9ChFiYW5rX2FjY291bnRfdHlwZRgHIAEoDjIiLmNvcmUuYWRtaW4uYmlsbGVyLkJhbmtBY2NvdW50VHlwZRIZChFiYW5rX2FjY291bnRfbmFtZRgIIAEoCSI7ChJMaXN0QmlsbGVyc1JlcXVlc3QSEQoJcGFnZV9zaXplGAEgASgFEhIKCnBhZ2VfdG9rZW4YAiABKAkiWgoTTGlzdEJpbGxlcnNSZXNwb25zZRIqCgdiaWxsZXJzGAEgAygLMhkuY29yZS5hZG1pbi5iaWxsZXIuQmlsbGVyEhcKD25leHRfcGFnZV90b2tlbhgCIAEoCSIeChBHZXRCaWxsZXJSZXF1ZXN0EgoKAmlkGAEgASgFIj4KEUdldEJpbGxlclJlc3BvbnNlEikKBmJpbGxlchgBIAEoCzIZLmNvcmUuYWRtaW4uYmlsbGVyLkJpbGxlciLMAQoTQ3JlYXRlQmlsbGVyUmVxdWVzdBIMCgRuYW1lGAEgASgJEg8KB2FkZHJlc3MYAiABKAkSEQoJYmFua19uYW1lGAMgASgJEhMKC2JhbmtfYnJhbmNoGAQgASgJEhQKDGJhbmtfYWNjb3VudBgFIAEoCRI9ChFiYW5rX2FjY291bnRfdHlwZRgGIAEoDjIiLmNvcmUuYWRtaW4uYmlsbGVyLkJhbmtBY2NvdW50VHlwZRIZChFiYW5rX2FjY291bnRfbmFtZRgHIAEoCSIWChRDcmVhdGVCaWxsZXJSZXNwb25zZSLYAQoTVXBkYXRlQmlsbGVyUmVxdWVzdBIKCgJpZBgBIAEoBRIMCgRuYW1lGAIgASgJEg8KB2FkZHJlc3MYAyABKAkSEQoJYmFua19uYW1lGAQgASgJEhMKC2JhbmtfYnJhbmNoGAUgASgJEhQKDGJhbmtfYWNjb3VudBgGIAEoCRI9ChFiYW5rX2FjY291bnRfdHlwZRgHIAEoDjIiLmNvcmUuYWRtaW4uYmlsbGVyLkJhbmtBY2NvdW50VHlwZRIZChFiYW5rX2FjY291bnRfbmFtZRgIIAEoCSIWChRVcGRhdGVCaWxsZXJSZXNwb25zZSpzCg9CYW5rQWNjb3VudFR5cGUSIQodQkFOS19BQ0NPVU5UX1RZUEVfVU5TUEVDSUZJRUQQABIdChlCQU5LX0FDQ09VTlRfVFlQRV9TQVZJTkdTEAESHgoaQkFOS19BQ0NPVU5UX1RZUEVfQ0hFQ0tJTkcQAjKPAwoNQmlsbGVyU2VydmljZRJeCgtMaXN0QmlsbGVycxIlLmNvcmUuYWRtaW4uYmlsbGVyLkxpc3RCaWxsZXJzUmVxdWVzdBomLmNvcmUuYWRtaW4uYmlsbGVyLkxpc3RCaWxsZXJzUmVzcG9uc2UiABJYCglHZXRCaWxsZXISIy5jb3JlLmFkbWluLmJpbGxlci5HZXRCaWxsZXJSZXF1ZXN0GiQuY29yZS5hZG1pbi5iaWxsZXIuR2V0QmlsbGVyUmVzcG9uc2UiABJhCgxDcmVhdGVCaWxsZXISJi5jb3JlLmFkbWluLmJpbGxlci5DcmVhdGVCaWxsZXJSZXF1ZXN0GicuY29yZS5hZG1pbi5iaWxsZXIuQ3JlYXRlQmlsbGVyUmVzcG9uc2UiABJhCgxVcGRhdGVCaWxsZXISJi5jb3JlLmFkbWluLmJpbGxlci5VcGRhdGVCaWxsZXJSZXF1ZXN0GicuY29yZS5hZG1pbi5iaWxsZXIuVXBkYXRlQmlsbGVyUmVzcG9uc2UiAGIGcHJvdG8z");

/**
 * 請求元情報
 *
 * @generated from message core.admin.biller.Biller
 */
export type Biller = Message<"core.admin.biller.Biller"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string address = 3;
   */
  address: string;

  /**
   * @generated from field: string bank_name = 4;
   */
  bankName: string;

  /**
   * @generated from field: string bank_branch = 5;
   */
  bankBranch: string;

  /**
   * @generated from field: string bank_account = 6;
   */
  bankAccount: string;

  /**
   * @generated from field: core.admin.biller.BankAccountType bank_account_type = 7;
   */
  bankAccountType: BankAccountType;

  /**
   * @generated from field: string bank_account_name = 8;
   */
  bankAccountName: string;
};

/**
 * Describes the message core.admin.biller.Biller.
 * Use `create(BillerSchema)` to create a new message.
 */
export const BillerSchema: GenMessage<Biller> = /*@__PURE__*/
  messageDesc(file_biller_service, 0);

/**
 * 請求元一覧取得リクエスト
 *
 * @generated from message core.admin.biller.ListBillersRequest
 */
export type ListBillersRequest = Message<"core.admin.biller.ListBillersRequest"> & {
  /**
   * @generated from field: int32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;
};

/**
 * Describes the message core.admin.biller.ListBillersRequest.
 * Use `create(ListBillersRequestSchema)` to create a new message.
 */
export const ListBillersRequestSchema: GenMessage<ListBillersRequest> = /*@__PURE__*/
  messageDesc(file_biller_service, 1);

/**
 * 請求元一覧取得レスポンス
 *
 * @generated from message core.admin.biller.ListBillersResponse
 */
export type ListBillersResponse = Message<"core.admin.biller.ListBillersResponse"> & {
  /**
   * @generated from field: repeated core.admin.biller.Biller billers = 1;
   */
  billers: Biller[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message core.admin.biller.ListBillersResponse.
 * Use `create(ListBillersResponseSchema)` to create a new message.
 */
export const ListBillersResponseSchema: GenMessage<ListBillersResponse> = /*@__PURE__*/
  messageDesc(file_biller_service, 2);

/**
 * 請求元詳細取得リクエスト
 *
 * @generated from message core.admin.biller.GetBillerRequest
 */
export type GetBillerRequest = Message<"core.admin.biller.GetBillerRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message core.admin.biller.GetBillerRequest.
 * Use `create(GetBillerRequestSchema)` to create a new message.
 */
export const GetBillerRequestSchema: GenMessage<GetBillerRequest> = /*@__PURE__*/
  messageDesc(file_biller_service, 3);

/**
 * 請求元詳細取得レスポンス
 *
 * @generated from message core.admin.biller.GetBillerResponse
 */
export type GetBillerResponse = Message<"core.admin.biller.GetBillerResponse"> & {
  /**
   * @generated from field: core.admin.biller.Biller biller = 1;
   */
  biller?: Biller;
};

/**
 * Describes the message core.admin.biller.GetBillerResponse.
 * Use `create(GetBillerResponseSchema)` to create a new message.
 */
export const GetBillerResponseSchema: GenMessage<GetBillerResponse> = /*@__PURE__*/
  messageDesc(file_biller_service, 4);

/**
 * 請求元作成リクエスト
 *
 * @generated from message core.admin.biller.CreateBillerRequest
 */
export type CreateBillerRequest = Message<"core.admin.biller.CreateBillerRequest"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string address = 2;
   */
  address: string;

  /**
   * @generated from field: string bank_name = 3;
   */
  bankName: string;

  /**
   * @generated from field: string bank_branch = 4;
   */
  bankBranch: string;

  /**
   * @generated from field: string bank_account = 5;
   */
  bankAccount: string;

  /**
   * @generated from field: core.admin.biller.BankAccountType bank_account_type = 6;
   */
  bankAccountType: BankAccountType;

  /**
   * @generated from field: string bank_account_name = 7;
   */
  bankAccountName: string;
};

/**
 * Describes the message core.admin.biller.CreateBillerRequest.
 * Use `create(CreateBillerRequestSchema)` to create a new message.
 */
export const CreateBillerRequestSchema: GenMessage<CreateBillerRequest> = /*@__PURE__*/
  messageDesc(file_biller_service, 5);

/**
 * 請求元作成レスポンス
 *
 * @generated from message core.admin.biller.CreateBillerResponse
 */
export type CreateBillerResponse = Message<"core.admin.biller.CreateBillerResponse"> & {
};

/**
 * Describes the message core.admin.biller.CreateBillerResponse.
 * Use `create(CreateBillerResponseSchema)` to create a new message.
 */
export const CreateBillerResponseSchema: GenMessage<CreateBillerResponse> = /*@__PURE__*/
  messageDesc(file_biller_service, 6);

/**
 * 請求元更新リクエスト
 *
 * @generated from message core.admin.biller.UpdateBillerRequest
 */
export type UpdateBillerRequest = Message<"core.admin.biller.UpdateBillerRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string address = 3;
   */
  address: string;

  /**
   * @generated from field: string bank_name = 4;
   */
  bankName: string;

  /**
   * @generated from field: string bank_branch = 5;
   */
  bankBranch: string;

  /**
   * @generated from field: string bank_account = 6;
   */
  bankAccount: string;

  /**
   * @generated from field: core.admin.biller.BankAccountType bank_account_type = 7;
   */
  bankAccountType: BankAccountType;

  /**
   * @generated from field: string bank_account_name = 8;
   */
  bankAccountName: string;
};

/**
 * Describes the message core.admin.biller.UpdateBillerRequest.
 * Use `create(UpdateBillerRequestSchema)` to create a new message.
 */
export const UpdateBillerRequestSchema: GenMessage<UpdateBillerRequest> = /*@__PURE__*/
  messageDesc(file_biller_service, 7);

/**
 * 請求元更新レスポンス
 *
 * @generated from message core.admin.biller.UpdateBillerResponse
 */
export type UpdateBillerResponse = Message<"core.admin.biller.UpdateBillerResponse"> & {
};

/**
 * Describes the message core.admin.biller.UpdateBillerResponse.
 * Use `create(UpdateBillerResponseSchema)` to create a new message.
 */
export const UpdateBillerResponseSchema: GenMessage<UpdateBillerResponse> = /*@__PURE__*/
  messageDesc(file_biller_service, 8);

/**
 * 口座種別
 *
 * @generated from enum core.admin.biller.BankAccountType
 */
export enum BankAccountType {
  /**
   * @generated from enum value: BANK_ACCOUNT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 普通預金
   *
   * @generated from enum value: BANK_ACCOUNT_TYPE_SAVINGS = 1;
   */
  SAVINGS = 1,

  /**
   * 当座預金
   *
   * @generated from enum value: BANK_ACCOUNT_TYPE_CHECKING = 2;
   */
  CHECKING = 2,
}

/**
 * Describes the enum core.admin.biller.BankAccountType.
 */
export const BankAccountTypeSchema: GenEnum<BankAccountType> = /*@__PURE__*/
  enumDesc(file_biller_service, 0);

/**
 * 請求元サービス
 *
 * @generated from service core.admin.biller.BillerService
 */
export const BillerService: GenService<{
  /**
   * 請求元一覧を取得
   *
   * @generated from rpc core.admin.biller.BillerService.ListBillers
   */
  listBillers: {
    methodKind: "unary";
    input: typeof ListBillersRequestSchema;
    output: typeof ListBillersResponseSchema;
  },
  /**
   * 請求元詳細を取得
   *
   * @generated from rpc core.admin.biller.BillerService.GetBiller
   */
  getBiller: {
    methodKind: "unary";
    input: typeof GetBillerRequestSchema;
    output: typeof GetBillerResponseSchema;
  },
  /**
   * 請求元を作成
   *
   * @generated from rpc core.admin.biller.BillerService.CreateBiller
   */
  createBiller: {
    methodKind: "unary";
    input: typeof CreateBillerRequestSchema;
    output: typeof CreateBillerResponseSchema;
  },
  /**
   * 請求元を更新
   *
   * @generated from rpc core.admin.biller.BillerService.UpdateBiller
   */
  updateBiller: {
    methodKind: "unary";
    input: typeof UpdateBillerRequestSchema;
    output: typeof UpdateBillerResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_biller_service, 0);

