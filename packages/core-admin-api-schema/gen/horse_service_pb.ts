// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file horse_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { HorseGender } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_service.proto.
 */
export const file_horse_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums, file_google_protobuf_timestamp]);

/**
 * 馬の一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListHorsesRequest
 */
export type ListHorsesRequest = Message<"hami.core.admin.v1.ListHorsesRequest"> & {
  /**
   * @generated from field: int32 page = 1;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 2;
   */
  limit: number;

  /**
   * 馬名または母馬名での検索
   *
   * @generated from field: optional string search = 3;
   */
  search?: string;

  /**
   * 生年での絞り込み
   *
   * @generated from field: optional int32 birth_year = 4;
   */
  birthYear?: number;
};

/**
 * Describes the message hami.core.admin.v1.ListHorsesRequest.
 * Use `create(ListHorsesRequestSchema)` to create a new message.
 */
export const ListHorsesRequestSchema: GenMessage<ListHorsesRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 0);

/**
 * 馬の一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListHorsesResponse
 */
export type ListHorsesResponse = Message<"hami.core.admin.v1.ListHorsesResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.HorseListItem horses = 1;
   */
  horses: HorseListItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 4;
   */
  limit: number;

  /**
   * @generated from field: int32 total_pages = 5;
   */
  totalPages: number;
};

/**
 * Describes the message hami.core.admin.v1.ListHorsesResponse.
 * Use `create(ListHorsesResponseSchema)` to create a new message.
 */
export const ListHorsesResponseSchema: GenMessage<ListHorsesResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 1);

/**
 * 馬の一覧項目
 *
 * @generated from message hami.core.admin.v1.HorseListItem
 */
export type HorseListItem = Message<"hami.core.admin.v1.HorseListItem"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 recruitment_year = 2;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 3;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string recruitment_name = 4;
   */
  recruitmentName: string;

  /**
   * @generated from field: string horse_name = 5;
   */
  horseName: string;

  /**
   * @generated from field: int32 birth_year = 6;
   */
  birthYear: number;

  /**
   * @generated from field: optional hami.core.admin.v1.HorseGender gender = 7;
   */
  gender?: HorseGender;

  /**
   * @generated from field: optional hami.core.admin.v1.PublishStatus publish_status = 8;
   */
  publishStatus?: PublishStatus;

  /**
   * @generated from field: optional hami.core.admin.v1.RecruitmentStatus recruitment_status = 9;
   */
  recruitmentStatus?: RecruitmentStatus;

  /**
   * @generated from field: bool conflict_of_interest = 10;
   */
  conflictOfInterest: boolean;
};

/**
 * Describes the message hami.core.admin.v1.HorseListItem.
 * Use `create(HorseListItemSchema)` to create a new message.
 */
export const HorseListItemSchema: GenMessage<HorseListItem> = /*@__PURE__*/
  messageDesc(file_horse_service, 2);

/**
 * 馬の詳細取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetHorseRequest
 */
export type GetHorseRequest = Message<"hami.core.admin.v1.GetHorseRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetHorseRequest.
 * Use `create(GetHorseRequestSchema)` to create a new message.
 */
export const GetHorseRequestSchema: GenMessage<GetHorseRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 3);

/**
 * 馬の詳細取得レスポンス
 *
 * @generated from message hami.core.admin.v1.GetHorseResponse
 */
export type GetHorseResponse = Message<"hami.core.admin.v1.GetHorseResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.HorseDetail horse = 1;
   */
  horse?: HorseDetail;
};

/**
 * Describes the message hami.core.admin.v1.GetHorseResponse.
 * Use `create(GetHorseResponseSchema)` to create a new message.
 */
export const GetHorseResponseSchema: GenMessage<GetHorseResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 4);

/**
 * 馬の詳細情報
 *
 * @generated from message hami.core.admin.v1.HorseDetail
 */
export type HorseDetail = Message<"hami.core.admin.v1.HorseDetail"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 recruitment_year = 2;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 3;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string recruitment_name = 4;
   */
  recruitmentName: string;

  /**
   * @generated from field: string horse_name = 5;
   */
  horseName: string;

  /**
   * @generated from field: int32 birth_year = 6;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 7;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 8;
   */
  birthDay: number;

  /**
   * @generated from field: int32 shares_total = 9;
   */
  sharesTotal: number;

  /**
   * @generated from field: int32 amount_total = 10;
   */
  amountTotal: number;

  /**
   * @generated from field: string note = 11;
   */
  note: string;

  /**
   * @generated from field: int32 fund_start_year = 12;
   */
  fundStartYear: number;

  /**
   * @generated from field: int32 fund_start_month = 13;
   */
  fundStartMonth: number;

  /**
   * @generated from field: int32 fund_start_day = 14;
   */
  fundStartDay: number;

  /**
   * @generated from field: bool conflict_of_interest = 15;
   */
  conflictOfInterest: boolean;

  /**
   * @generated from field: optional hami.core.admin.v1.HorseGender gender = 16;
   */
  gender?: HorseGender;

  /**
   * @generated from field: optional hami.core.admin.v1.PublishStatus publish_status = 17;
   */
  publishStatus?: PublishStatus;

  /**
   * @generated from field: optional hami.core.admin.v1.RecruitmentStatus recruitment_status = 18;
   */
  recruitmentStatus?: RecruitmentStatus;
};

/**
 * Describes the message hami.core.admin.v1.HorseDetail.
 * Use `create(HorseDetailSchema)` to create a new message.
 */
export const HorseDetailSchema: GenMessage<HorseDetail> = /*@__PURE__*/
  messageDesc(file_horse_service, 5);

/**
 * 馬の登録リクエスト
 *
 * @generated from message hami.core.admin.v1.CreateHorseRequest
 */
export type CreateHorseRequest = Message<"hami.core.admin.v1.CreateHorseRequest"> & {
  /**
   * @generated from field: int32 recruitment_year = 1;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 2;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string recruitment_name = 3;
   */
  recruitmentName: string;

  /**
   * @generated from field: string horse_name = 4;
   */
  horseName: string;

  /**
   * @generated from field: int32 birth_year = 5;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 6;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 7;
   */
  birthDay: number;

  /**
   * @generated from field: int32 shares_total = 8;
   */
  sharesTotal: number;

  /**
   * @generated from field: int32 amount_total = 9;
   */
  amountTotal: number;

  /**
   * @generated from field: string note = 10;
   */
  note: string;

  /**
   * @generated from field: int32 fund_start_year = 11;
   */
  fundStartYear: number;

  /**
   * @generated from field: int32 fund_start_month = 12;
   */
  fundStartMonth: number;

  /**
   * @generated from field: int32 fund_start_day = 13;
   */
  fundStartDay: number;

  /**
   * @generated from field: bool conflict_of_interest = 14;
   */
  conflictOfInterest: boolean;
};

/**
 * Describes the message hami.core.admin.v1.CreateHorseRequest.
 * Use `create(CreateHorseRequestSchema)` to create a new message.
 */
export const CreateHorseRequestSchema: GenMessage<CreateHorseRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 6);

/**
 * 馬の登録レスポンス
 *
 * @generated from message hami.core.admin.v1.CreateHorseResponse
 */
export type CreateHorseResponse = Message<"hami.core.admin.v1.CreateHorseResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.CreateHorseResponse.
 * Use `create(CreateHorseResponseSchema)` to create a new message.
 */
export const CreateHorseResponseSchema: GenMessage<CreateHorseResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 7);

/**
 * 馬の更新リクエスト
 *
 * @generated from message hami.core.admin.v1.UpdateHorseRequest
 */
export type UpdateHorseRequest = Message<"hami.core.admin.v1.UpdateHorseRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string recruitment_name = 2;
   */
  recruitmentName: string;

  /**
   * @generated from field: string horse_name = 3;
   */
  horseName: string;

  /**
   * @generated from field: int32 birth_year = 4;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 5;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 6;
   */
  birthDay: number;

  /**
   * @generated from field: int32 shares_total = 7;
   */
  sharesTotal: number;

  /**
   * @generated from field: int32 amount_total = 8;
   */
  amountTotal: number;

  /**
   * @generated from field: string note = 9;
   */
  note: string;

  /**
   * @generated from field: int32 fund_start_year = 10;
   */
  fundStartYear: number;

  /**
   * @generated from field: int32 fund_start_month = 11;
   */
  fundStartMonth: number;

  /**
   * @generated from field: int32 fund_start_day = 12;
   */
  fundStartDay: number;

  /**
   * @generated from field: bool conflict_of_interest = 13;
   */
  conflictOfInterest: boolean;
};

/**
 * Describes the message hami.core.admin.v1.UpdateHorseRequest.
 * Use `create(UpdateHorseRequestSchema)` to create a new message.
 */
export const UpdateHorseRequestSchema: GenMessage<UpdateHorseRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 8);

/**
 * 馬の更新レスポンス
 *
 * @generated from message hami.core.admin.v1.UpdateHorseResponse
 */
export type UpdateHorseResponse = Message<"hami.core.admin.v1.UpdateHorseResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.UpdateHorseResponse.
 * Use `create(UpdateHorseResponseSchema)` to create a new message.
 */
export const UpdateHorseResponseSchema: GenMessage<UpdateHorseResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 9);

/**
 * 馬の削除リクエスト
 *
 * @generated from message hami.core.admin.v1.DeleteHorseRequest
 */
export type DeleteHorseRequest = Message<"hami.core.admin.v1.DeleteHorseRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message hami.core.admin.v1.DeleteHorseRequest.
 * Use `create(DeleteHorseRequestSchema)` to create a new message.
 */
export const DeleteHorseRequestSchema: GenMessage<DeleteHorseRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 10);

/**
 * 馬の削除レスポンス
 *
 * @generated from message hami.core.admin.v1.DeleteHorseResponse
 */
export type DeleteHorseResponse = Message<"hami.core.admin.v1.DeleteHorseResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.DeleteHorseResponse.
 * Use `create(DeleteHorseResponseSchema)` to create a new message.
 */
export const DeleteHorseResponseSchema: GenMessage<DeleteHorseResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 11);

/**
 * 募集年度・募集番号重複チェックリクエスト
 *
 * @generated from message hami.core.admin.v1.CheckHorseExistsRequest
 */
export type CheckHorseExistsRequest = Message<"hami.core.admin.v1.CheckHorseExistsRequest"> & {
  /**
   * @generated from field: int32 recruitment_year = 1;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 2;
   */
  recruitmentNo: number;
};

/**
 * Describes the message hami.core.admin.v1.CheckHorseExistsRequest.
 * Use `create(CheckHorseExistsRequestSchema)` to create a new message.
 */
export const CheckHorseExistsRequestSchema: GenMessage<CheckHorseExistsRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 12);

/**
 * 募集年度・募集番号重複チェックレスポンス
 *
 * @generated from message hami.core.admin.v1.CheckHorseExistsResponse
 */
export type CheckHorseExistsResponse = Message<"hami.core.admin.v1.CheckHorseExistsResponse"> & {
  /**
   * 該当する募集年度・募集番号の馬が存在するかどうか
   *
   * @generated from field: bool exists = 1;
   */
  exists: boolean;
};

/**
 * Describes the message hami.core.admin.v1.CheckHorseExistsResponse.
 * Use `create(CheckHorseExistsResponseSchema)` to create a new message.
 */
export const CheckHorseExistsResponseSchema: GenMessage<CheckHorseExistsResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 13);

/**
 * 馬に出資している会員一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListHorseInvestorsRequest
 */
export type ListHorseInvestorsRequest = Message<"hami.core.admin.v1.ListHorseInvestorsRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message hami.core.admin.v1.ListHorseInvestorsRequest.
 * Use `create(ListHorseInvestorsRequestSchema)` to create a new message.
 */
export const ListHorseInvestorsRequestSchema: GenMessage<ListHorseInvestorsRequest> = /*@__PURE__*/
  messageDesc(file_horse_service, 14);

/**
 * 馬に出資している会員一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListHorseInvestorsResponse
 */
export type ListHorseInvestorsResponse = Message<"hami.core.admin.v1.ListHorseInvestorsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.HorseInvestor investors = 1;
   */
  investors: HorseInvestor[];
};

/**
 * Describes the message hami.core.admin.v1.ListHorseInvestorsResponse.
 * Use `create(ListHorseInvestorsResponseSchema)` to create a new message.
 */
export const ListHorseInvestorsResponseSchema: GenMessage<ListHorseInvestorsResponse> = /*@__PURE__*/
  messageDesc(file_horse_service, 15);

/**
 * 馬に出資している会員情報
 *
 * @generated from message hami.core.admin.v1.HorseInvestor
 */
export type HorseInvestor = Message<"hami.core.admin.v1.HorseInvestor"> & {
  /**
   * 会員ID
   *
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;

  /**
   * 会員番号
   *
   * @generated from field: int32 member_number = 2;
   */
  memberNumber: number;

  /**
   * 会員氏名
   *
   * @generated from field: string member_name = 3;
   */
  memberName: string;

  /**
   * 出資口数
   *
   * @generated from field: int32 shares_number = 4;
   */
  sharesNumber: number;

  /**
   * 出資金額
   *
   * @generated from field: int32 investment_amount = 5;
   */
  investmentAmount: number;

  /**
   * @generated from field: google.protobuf.Timestamp retirement_date = 6;
   */
  retirementDate?: Timestamp;
};

/**
 * Describes the message hami.core.admin.v1.HorseInvestor.
 * Use `create(HorseInvestorSchema)` to create a new message.
 */
export const HorseInvestorSchema: GenMessage<HorseInvestor> = /*@__PURE__*/
  messageDesc(file_horse_service, 16);

/**
 * 公開ステータス
 *
 * @generated from enum hami.core.admin.v1.PublishStatus
 */
export enum PublishStatus {
  /**
   * @generated from enum value: PUBLISH_STATUS_UNSPECIFIED = 0;
   */
  PUBLISH_STATUS_UNSPECIFIED = 0,

  /**
   * 下書き
   *
   * @generated from enum value: DRAFT = 1;
   */
  DRAFT = 1,

  /**
   * 公開
   *
   * @generated from enum value: PUBLISHED = 2;
   */
  PUBLISHED = 2,

  /**
   * 削除
   *
   * @generated from enum value: ARCHIVED = 3;
   */
  ARCHIVED = 3,
}

/**
 * Describes the enum hami.core.admin.v1.PublishStatus.
 */
export const PublishStatusSchema: GenEnum<PublishStatus> = /*@__PURE__*/
  enumDesc(file_horse_service, 0);

/**
 * 募集ステータス
 *
 * @generated from enum hami.core.admin.v1.RecruitmentStatus
 */
export enum RecruitmentStatus {
  /**
   * @generated from enum value: RECRUITMENT_STATUS_UNSPECIFIED = 0;
   */
  RECRUITMENT_STATUS_UNSPECIFIED = 0,

  /**
   * 募集前
   *
   * @generated from enum value: UPCOMING = 1;
   */
  UPCOMING = 1,

  /**
   * 募集中
   *
   * @generated from enum value: ACTIVE = 2;
   */
  ACTIVE = 2,

  /**
   * 満口
   *
   * @generated from enum value: FULL = 3;
   */
  FULL = 3,

  /**
   * 募集終了
   *
   * @generated from enum value: CLOSED = 4;
   */
  CLOSED = 4,
}

/**
 * Describes the enum hami.core.admin.v1.RecruitmentStatus.
 */
export const RecruitmentStatusSchema: GenEnum<RecruitmentStatus> = /*@__PURE__*/
  enumDesc(file_horse_service, 1);

/**
 * 馬管理サービス
 *
 * @generated from service hami.core.admin.v1.HorseService
 */
export const HorseService: GenService<{
  /**
   * 馬の一覧を取得
   *
   * @generated from rpc hami.core.admin.v1.HorseService.ListHorses
   */
  listHorses: {
    methodKind: "unary";
    input: typeof ListHorsesRequestSchema;
    output: typeof ListHorsesResponseSchema;
  },
  /**
   * 馬の詳細を取得
   *
   * @generated from rpc hami.core.admin.v1.HorseService.GetHorse
   */
  getHorse: {
    methodKind: "unary";
    input: typeof GetHorseRequestSchema;
    output: typeof GetHorseResponseSchema;
  },
  /**
   * 馬を登録
   *
   * @generated from rpc hami.core.admin.v1.HorseService.CreateHorse
   */
  createHorse: {
    methodKind: "unary";
    input: typeof CreateHorseRequestSchema;
    output: typeof CreateHorseResponseSchema;
  },
  /**
   * 馬を更新
   *
   * @generated from rpc hami.core.admin.v1.HorseService.UpdateHorse
   */
  updateHorse: {
    methodKind: "unary";
    input: typeof UpdateHorseRequestSchema;
    output: typeof UpdateHorseResponseSchema;
  },
  /**
   * 馬を削除
   *
   * @generated from rpc hami.core.admin.v1.HorseService.DeleteHorse
   */
  deleteHorse: {
    methodKind: "unary";
    input: typeof DeleteHorseRequestSchema;
    output: typeof DeleteHorseResponseSchema;
  },
  /**
   * 募集年度と募集番号の組み合わせで馬が存在するかチェック
   *
   * @generated from rpc hami.core.admin.v1.HorseService.CheckHorseExists
   */
  checkHorseExists: {
    methodKind: "unary";
    input: typeof CheckHorseExistsRequestSchema;
    output: typeof CheckHorseExistsResponseSchema;
  },
  /**
   * 馬に出資している会員一覧を取得
   *
   * @generated from rpc hami.core.admin.v1.HorseService.ListHorseInvestors
   */
  listHorseInvestors: {
    methodKind: "unary";
    input: typeof ListHorseInvestorsRequestSchema;
    output: typeof ListHorseInvestorsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_service, 0);

