// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file membership_application_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file membership_application_service.proto.
 */
export const file_membership_application_service: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message hami.core.admin.v1.IdentityDocument
 */
export type IdentityDocument = Message<"hami.core.admin.v1.IdentityDocument"> & {
  /**
   * @generated from field: int32 identity_document_id = 1;
   */
  identityDocumentId: number;

  /**
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * 個人では未使用（DOCUMENT_TYPE_UNKNOWN）
   *
   * @generated from field: hami.core.admin.v1.DocumentType document_type = 3;
   */
  documentType: DocumentType;

  /**
   * @generated from field: int64 uploaded_at = 4;
   */
  uploadedAt: bigint;

  /**
   * @generated from field: int64 created_at = 5;
   */
  createdAt: bigint;

  /**
   * 個人: 1〜4、法人では0または未設定
   *
   * @generated from field: int32 personal_index = 6;
   */
  personalIndex: number;
};

/**
 * Describes the message hami.core.admin.v1.IdentityDocument.
 * Use `create(IdentityDocumentSchema)` to create a new message.
 */
export const IdentityDocumentSchema: GenMessage<IdentityDocument> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 0);

/**
 * @generated from message hami.core.admin.v1.DocumentGroup
 */
export type DocumentGroup = Message<"hami.core.admin.v1.DocumentGroup"> & {
  /**
   * @generated from field: int32 document_group_id = 1;
   */
  documentGroupId: number;

  /**
   * @generated from field: string upload_token = 2;
   */
  uploadToken: string;

  /**
   * @generated from field: string group_key = 3;
   */
  groupKey: string;

  /**
   * @generated from field: bool is_completed = 4;
   */
  isCompleted: boolean;

  /**
   * @generated from field: repeated hami.core.admin.v1.IdentityDocument documents = 5;
   */
  documents: IdentityDocument[];

  /**
   * @generated from field: int64 created_at = 6;
   */
  createdAt: bigint;
};

/**
 * Describes the message hami.core.admin.v1.DocumentGroup.
 * Use `create(DocumentGroupSchema)` to create a new message.
 */
export const DocumentGroupSchema: GenMessage<DocumentGroup> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 1);

/**
 * @generated from message hami.core.admin.v1.BeneficialOwnerDeclaration
 */
export type BeneficialOwnerDeclaration = Message<"hami.core.admin.v1.BeneficialOwnerDeclaration"> & {
  /**
   * @generated from field: int32 beneficial_owner_declaration_id = 1;
   */
  beneficialOwnerDeclarationId: number;

  /**
   * @generated from field: string beneficial_owner_name = 2;
   */
  beneficialOwnerName: string;

  /**
   * @generated from field: int32 beneficial_owner_birth_year = 3;
   */
  beneficialOwnerBirthYear: number;

  /**
   * @generated from field: int32 beneficial_owner_birth_month = 4;
   */
  beneficialOwnerBirthMonth: number;

  /**
   * @generated from field: int32 beneficial_owner_birth_day = 5;
   */
  beneficialOwnerBirthDay: number;

  /**
   * @generated from field: string beneficial_owner_postal_code = 6;
   */
  beneficialOwnerPostalCode: string;

  /**
   * @generated from field: string beneficial_owner_prefecture = 7;
   */
  beneficialOwnerPrefecture: string;

  /**
   * @generated from field: string beneficial_owner_address = 8;
   */
  beneficialOwnerAddress: string;

  /**
   * @generated from field: string beneficial_owner_apartment = 9;
   */
  beneficialOwnerApartment: string;

  /**
   * @generated from field: string declarant_name = 10;
   */
  declarantName: string;

  /**
   * @generated from field: string declarant_position = 11;
   */
  declarantPosition: string;

  /**
   * @generated from field: bool is_confirmed = 12;
   */
  isConfirmed: boolean;

  /**
   * @generated from field: int64 confirmed_at = 13;
   */
  confirmedAt: bigint;

  /**
   * @generated from field: int64 created_at = 14;
   */
  createdAt: bigint;
};

/**
 * Describes the message hami.core.admin.v1.BeneficialOwnerDeclaration.
 * Use `create(BeneficialOwnerDeclarationSchema)` to create a new message.
 */
export const BeneficialOwnerDeclarationSchema: GenMessage<BeneficialOwnerDeclaration> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 2);

/**
 * @generated from message hami.core.admin.v1.MembershipApplicationReviewLog
 */
export type MembershipApplicationReviewLog = Message<"hami.core.admin.v1.MembershipApplicationReviewLog"> & {
  /**
   * @generated from field: int32 membership_application_review_log_id = 1;
   */
  membershipApplicationReviewLogId: number;

  /**
   * @generated from field: int32 membership_application_id = 2;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: int64 timestamp = 3;
   */
  timestamp: bigint;

  /**
   * @generated from field: string reviewer = 4;
   */
  reviewer: string;

  /**
   * @generated from field: hami.core.admin.v1.ReviewType review_type = 5;
   */
  reviewType: ReviewType;

  /**
   * @generated from field: optional string remand_reason = 6;
   */
  remandReason?: string;

  /**
   * @generated from field: int64 created_at = 7;
   */
  createdAt: bigint;
};

/**
 * Describes the message hami.core.admin.v1.MembershipApplicationReviewLog.
 * Use `create(MembershipApplicationReviewLogSchema)` to create a new message.
 */
export const MembershipApplicationReviewLogSchema: GenMessage<MembershipApplicationReviewLog> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 3);

/**
 * @generated from message hami.core.admin.v1.MembershipApplicationDocumentGroupReviewLog
 */
export type MembershipApplicationDocumentGroupReviewLog = Message<"hami.core.admin.v1.MembershipApplicationDocumentGroupReviewLog"> & {
  /**
   * @generated from field: int32 membership_application_document_group_review_log_id = 1;
   */
  membershipApplicationDocumentGroupReviewLogId: number;

  /**
   * @generated from field: int32 membership_application_id = 2;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: int64 timestamp = 3;
   */
  timestamp: bigint;

  /**
   * @generated from field: string reviewer = 4;
   */
  reviewer: string;

  /**
   * @generated from field: hami.core.admin.v1.ReviewType review_type = 5;
   */
  reviewType: ReviewType;

  /**
   * @generated from field: optional string remand_reason = 6;
   */
  remandReason?: string;

  /**
   * @generated from field: int64 created_at = 7;
   */
  createdAt: bigint;
};

/**
 * Describes the message hami.core.admin.v1.MembershipApplicationDocumentGroupReviewLog.
 * Use `create(MembershipApplicationDocumentGroupReviewLogSchema)` to create a new message.
 */
export const MembershipApplicationDocumentGroupReviewLogSchema: GenMessage<MembershipApplicationDocumentGroupReviewLog> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 4);

/**
 * @generated from message hami.core.admin.v1.MembershipApplication
 */
export type MembershipApplication = Message<"hami.core.admin.v1.MembershipApplication"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: string status = 3 [deprecated = true];
   * @deprecated
   */
  status: string;

  /**
   * @generated from field: int64 applied_at = 4;
   */
  appliedAt: bigint;

  /**
   * @generated from field: int64 updated_at = 5;
   */
  updatedAt: bigint;

  /**
   * 申込者タイプ
   *
   * @generated from field: hami.core.admin.v1.ApplicantType applicant_type = 6;
   */
  applicantType: ApplicantType;

  /**
   * 個人用フィールド（個人の場合のみ）
   *
   * @generated from field: string first_name = 7;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 8;
   */
  lastName: string;

  /**
   * @generated from field: string first_name_kana = 9;
   */
  firstNameKana: string;

  /**
   * @generated from field: string last_name_kana = 10;
   */
  lastNameKana: string;

  /**
   * @generated from field: int32 birth_year = 11;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 12;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 13;
   */
  birthDay: number;

  /**
   * 個人用：金融情報（個人の場合のみ）
   *
   * @generated from field: string annual_income = 38;
   */
  annualIncome: string;

  /**
   * @generated from field: string deposit_amount = 39;
   */
  depositAmount: string;

  /**
   * @generated from field: string financial_assets = 40;
   */
  financialAssets: string;

  /**
   * 個人用：勤務先情報（個人の場合のみ、任意）
   *
   * @generated from field: string company_name = 41;
   */
  companyName: string;

  /**
   * @generated from field: string company_address = 42;
   */
  companyAddress: string;

  /**
   * @generated from field: string company_phone_number = 43;
   */
  companyPhoneNumber: string;

  /**
   * 個人用：職業（個人の場合のみ）
   *
   * @generated from field: string occupation = 44;
   */
  occupation: string;

  /**
   * 個人情報保護方針への同意
   *
   * @generated from field: bool privacy_policy_agreement = 45;
   */
  privacyPolicyAgreement: boolean;

  /**
   * 法人用フィールド（法人の場合のみ）
   *
   * @generated from field: string corporate_name = 14;
   */
  corporateName: string;

  /**
   * @generated from field: string corporate_name_kana = 15;
   */
  corporateNameKana: string;

  /**
   * @generated from field: string representative_name = 16;
   */
  representativeName: string;

  /**
   * @generated from field: string representative_name_kana = 17;
   */
  representativeNameKana: string;

  /**
   * @generated from field: string representative_position = 18;
   */
  representativePosition: string;

  /**
   * @generated from field: string corporate_number = 19;
   */
  corporateNumber: string;

  /**
   * @generated from field: int32 established_year = 20;
   */
  establishedYear: number;

  /**
   * @generated from field: int32 established_month = 21;
   */
  establishedMonth: number;

  /**
   * @generated from field: int32 established_day = 22;
   */
  establishedDay: number;

  /**
   * 共通フィールド
   *
   * @generated from field: string postal_code = 23;
   */
  postalCode: string;

  /**
   * @generated from field: string prefecture = 24;
   */
  prefecture: string;

  /**
   * @generated from field: string address = 25;
   */
  address: string;

  /**
   * @generated from field: string apartment = 26;
   */
  apartment: string;

  /**
   * @generated from field: string phone_number = 27;
   */
  phoneNumber: string;

  /**
   * 実質的支配者申告（法人の場合のみ）
   *
   * @generated from field: repeated hami.core.admin.v1.BeneficialOwnerDeclaration beneficial_owner_declarations = 28;
   */
  beneficialOwnerDeclarations: BeneficialOwnerDeclaration[];

  /**
   * 審査ステータス
   *
   * @generated from field: hami.core.admin.v1.ReviewType application_review_status = 29;
   */
  applicationReviewStatus: ReviewType;

  /**
   * @generated from field: hami.core.admin.v1.ReviewType document_group_review_status = 30;
   */
  documentGroupReviewStatus: ReviewType;

  /**
   * 関連データ
   *
   * @generated from field: repeated hami.core.admin.v1.IdentityDocument identity_documents = 33;
   */
  identityDocuments: IdentityDocument[];

  /**
   * @generated from field: repeated hami.core.admin.v1.DocumentGroup document_groups = 34;
   */
  documentGroups: DocumentGroup[];

  /**
   * New compliance status fields
   *
   * @generated from field: hami.core.admin.v1.ReviewType compliance_application_review_status = 35;
   */
  complianceApplicationReviewStatus: ReviewType;

  /**
   * @generated from field: hami.core.admin.v1.ReviewType compliance_document_group_review_status = 36;
   */
  complianceDocumentGroupReviewStatus: ReviewType;

  /**
   * 一次審査（申込/書類）両方が承認となった日時（ミリ秒）
   *
   * @generated from field: int64 primary_approved_at = 37;
   */
  primaryApprovedAt: bigint;

  /**
   * 差し戻し後の再提出かどうか
   *
   * @generated from field: bool is_resubmitted_after_remand = 46;
   */
  isResubmittedAfterRemand: boolean;

  /**
   * 発行済み書類が印刷済みかどうか
   *
   * @generated from field: bool is_printed = 47;
   */
  isPrinted: boolean;
};

/**
 * Describes the message hami.core.admin.v1.MembershipApplication.
 * Use `create(MembershipApplicationSchema)` to create a new message.
 */
export const MembershipApplicationSchema: GenMessage<MembershipApplication> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 5);

/**
 * @generated from message hami.core.admin.v1.ListMembershipApplicationsRequest
 */
export type ListMembershipApplicationsRequest = Message<"hami.core.admin.v1.ListMembershipApplicationsRequest"> & {
  /**
   * ページ番号（1から開始）
   * 指定されない場合は1ページ目を返す
   *
   * @generated from field: optional int32 page = 1;
   */
  page?: number;

  /**
   * ページサイズ（1ページあたりの件数）
   * 指定されない場合はデフォルト値を使用
   *
   * @generated from field: optional int32 page_size = 2;
   */
  pageSize?: number;

  /**
   * フィルター対象ステータス（省略可）
   *
   * @generated from field: optional hami.core.admin.v1.MembershipApplicationListStatus status_filter = 3;
   */
  statusFilter?: MembershipApplicationListStatus;
};

/**
 * Describes the message hami.core.admin.v1.ListMembershipApplicationsRequest.
 * Use `create(ListMembershipApplicationsRequestSchema)` to create a new message.
 */
export const ListMembershipApplicationsRequestSchema: GenMessage<ListMembershipApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 6);

/**
 * @generated from message hami.core.admin.v1.ListMembershipApplicationsResponse
 */
export type ListMembershipApplicationsResponse = Message<"hami.core.admin.v1.ListMembershipApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.MembershipApplication membership_applications = 1;
   */
  membershipApplications: MembershipApplication[];

  /**
   * 次のページがあるかどうか
   *
   * @generated from field: bool has_next_page = 2;
   */
  hasNextPage: boolean;
};

/**
 * Describes the message hami.core.admin.v1.ListMembershipApplicationsResponse.
 * Use `create(ListMembershipApplicationsResponseSchema)` to create a new message.
 */
export const ListMembershipApplicationsResponseSchema: GenMessage<ListMembershipApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 7);

/**
 * @generated from message hami.core.admin.v1.GetMembershipApplicationRequest
 */
export type GetMembershipApplicationRequest = Message<"hami.core.admin.v1.GetMembershipApplicationRequest"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetMembershipApplicationRequest.
 * Use `create(GetMembershipApplicationRequestSchema)` to create a new message.
 */
export const GetMembershipApplicationRequestSchema: GenMessage<GetMembershipApplicationRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 8);

/**
 * @generated from message hami.core.admin.v1.GetMembershipApplicationResponse
 */
export type GetMembershipApplicationResponse = Message<"hami.core.admin.v1.GetMembershipApplicationResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.MembershipApplication membership_application = 1;
   */
  membershipApplication?: MembershipApplication;

  /**
   * @generated from field: repeated hami.core.admin.v1.MembershipApplicationReviewLog application_review_logs = 2;
   */
  applicationReviewLogs: MembershipApplicationReviewLog[];

  /**
   * @generated from field: repeated hami.core.admin.v1.MembershipApplicationDocumentGroupReviewLog document_group_review_logs = 3;
   */
  documentGroupReviewLogs: MembershipApplicationDocumentGroupReviewLog[];

  /**
   * Compliance review logs
   *
   * @generated from field: repeated hami.core.admin.v1.MembershipApplicationReviewLog compliance_application_review_logs = 4;
   */
  complianceApplicationReviewLogs: MembershipApplicationReviewLog[];

  /**
   * @generated from field: repeated hami.core.admin.v1.MembershipApplicationDocumentGroupReviewLog compliance_document_group_review_logs = 5;
   */
  complianceDocumentGroupReviewLogs: MembershipApplicationDocumentGroupReviewLog[];
};

/**
 * Describes the message hami.core.admin.v1.GetMembershipApplicationResponse.
 * Use `create(GetMembershipApplicationResponseSchema)` to create a new message.
 */
export const GetMembershipApplicationResponseSchema: GenMessage<GetMembershipApplicationResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 9);

/**
 * @generated from message hami.core.admin.v1.ReviewMembershipApplicationRequest
 */
export type ReviewMembershipApplicationRequest = Message<"hami.core.admin.v1.ReviewMembershipApplicationRequest"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: hami.core.admin.v1.ReviewType review_type = 2;
   */
  reviewType: ReviewType;

  /**
   * @generated from field: string remand_reason = 3;
   */
  remandReason: string;
};

/**
 * Describes the message hami.core.admin.v1.ReviewMembershipApplicationRequest.
 * Use `create(ReviewMembershipApplicationRequestSchema)` to create a new message.
 */
export const ReviewMembershipApplicationRequestSchema: GenMessage<ReviewMembershipApplicationRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 10);

/**
 * @generated from message hami.core.admin.v1.ReviewMembershipApplicationResponse
 */
export type ReviewMembershipApplicationResponse = Message<"hami.core.admin.v1.ReviewMembershipApplicationResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.ReviewMembershipApplicationResponse.
 * Use `create(ReviewMembershipApplicationResponseSchema)` to create a new message.
 */
export const ReviewMembershipApplicationResponseSchema: GenMessage<ReviewMembershipApplicationResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 11);

/**
 * @generated from message hami.core.admin.v1.ReviewMembershipApplicationDocumentGroupRequest
 */
export type ReviewMembershipApplicationDocumentGroupRequest = Message<"hami.core.admin.v1.ReviewMembershipApplicationDocumentGroupRequest"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: hami.core.admin.v1.ReviewType review_type = 2;
   */
  reviewType: ReviewType;

  /**
   * @generated from field: string remand_reason = 3;
   */
  remandReason: string;
};

/**
 * Describes the message hami.core.admin.v1.ReviewMembershipApplicationDocumentGroupRequest.
 * Use `create(ReviewMembershipApplicationDocumentGroupRequestSchema)` to create a new message.
 */
export const ReviewMembershipApplicationDocumentGroupRequestSchema: GenMessage<ReviewMembershipApplicationDocumentGroupRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 12);

/**
 * @generated from message hami.core.admin.v1.ReviewComplianceMembershipApplicationRequest
 */
export type ReviewComplianceMembershipApplicationRequest = Message<"hami.core.admin.v1.ReviewComplianceMembershipApplicationRequest"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: hami.core.admin.v1.ReviewType review_type = 2;
   */
  reviewType: ReviewType;

  /**
   * @generated from field: string remand_reason = 3;
   */
  remandReason: string;
};

/**
 * Describes the message hami.core.admin.v1.ReviewComplianceMembershipApplicationRequest.
 * Use `create(ReviewComplianceMembershipApplicationRequestSchema)` to create a new message.
 */
export const ReviewComplianceMembershipApplicationRequestSchema: GenMessage<ReviewComplianceMembershipApplicationRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 13);

/**
 * @generated from message hami.core.admin.v1.ReviewComplianceMembershipApplicationResponse
 */
export type ReviewComplianceMembershipApplicationResponse = Message<"hami.core.admin.v1.ReviewComplianceMembershipApplicationResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.ReviewComplianceMembershipApplicationResponse.
 * Use `create(ReviewComplianceMembershipApplicationResponseSchema)` to create a new message.
 */
export const ReviewComplianceMembershipApplicationResponseSchema: GenMessage<ReviewComplianceMembershipApplicationResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 14);

/**
 * @generated from message hami.core.admin.v1.ReviewComplianceMembershipApplicationDocumentGroupRequest
 */
export type ReviewComplianceMembershipApplicationDocumentGroupRequest = Message<"hami.core.admin.v1.ReviewComplianceMembershipApplicationDocumentGroupRequest"> & {
  /**
   * @generated from field: int32 membership_application_id = 1;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: hami.core.admin.v1.ReviewType review_type = 2;
   */
  reviewType: ReviewType;

  /**
   * @generated from field: string remand_reason = 3;
   */
  remandReason: string;
};

/**
 * Describes the message hami.core.admin.v1.ReviewComplianceMembershipApplicationDocumentGroupRequest.
 * Use `create(ReviewComplianceMembershipApplicationDocumentGroupRequestSchema)` to create a new message.
 */
export const ReviewComplianceMembershipApplicationDocumentGroupRequestSchema: GenMessage<ReviewComplianceMembershipApplicationDocumentGroupRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 15);

/**
 * @generated from message hami.core.admin.v1.ReviewComplianceMembershipApplicationDocumentGroupResponse
 */
export type ReviewComplianceMembershipApplicationDocumentGroupResponse = Message<"hami.core.admin.v1.ReviewComplianceMembershipApplicationDocumentGroupResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.ReviewComplianceMembershipApplicationDocumentGroupResponse.
 * Use `create(ReviewComplianceMembershipApplicationDocumentGroupResponseSchema)` to create a new message.
 */
export const ReviewComplianceMembershipApplicationDocumentGroupResponseSchema: GenMessage<ReviewComplianceMembershipApplicationDocumentGroupResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 16);

/**
 * @generated from message hami.core.admin.v1.ReviewMembershipApplicationDocumentGroupResponse
 */
export type ReviewMembershipApplicationDocumentGroupResponse = Message<"hami.core.admin.v1.ReviewMembershipApplicationDocumentGroupResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.ReviewMembershipApplicationDocumentGroupResponse.
 * Use `create(ReviewMembershipApplicationDocumentGroupResponseSchema)` to create a new message.
 */
export const ReviewMembershipApplicationDocumentGroupResponseSchema: GenMessage<ReviewMembershipApplicationDocumentGroupResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 17);

/**
 * @generated from message hami.core.admin.v1.GetIdentityDocumentImageUrlRequest
 */
export type GetIdentityDocumentImageUrlRequest = Message<"hami.core.admin.v1.GetIdentityDocumentImageUrlRequest"> & {
  /**
   * @generated from field: int32 identity_document_id = 1;
   */
  identityDocumentId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetIdentityDocumentImageUrlRequest.
 * Use `create(GetIdentityDocumentImageUrlRequestSchema)` to create a new message.
 */
export const GetIdentityDocumentImageUrlRequestSchema: GenMessage<GetIdentityDocumentImageUrlRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 18);

/**
 * @generated from message hami.core.admin.v1.GetIdentityDocumentImageUrlResponse
 */
export type GetIdentityDocumentImageUrlResponse = Message<"hami.core.admin.v1.GetIdentityDocumentImageUrlResponse"> & {
  /**
   * @generated from field: string image_url = 1;
   */
  imageUrl: string;

  /**
   * @generated from field: int64 expires_at = 2;
   */
  expiresAt: bigint;
};

/**
 * Describes the message hami.core.admin.v1.GetIdentityDocumentImageUrlResponse.
 * Use `create(GetIdentityDocumentImageUrlResponseSchema)` to create a new message.
 */
export const GetIdentityDocumentImageUrlResponseSchema: GenMessage<GetIdentityDocumentImageUrlResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 19);

/**
 * 承認済みの入会申込をCSVにエクスポートする
 *
 * @generated from message hami.core.admin.v1.ExportApprovedMembershipApplicationsCsvRequest
 */
export type ExportApprovedMembershipApplicationsCsvRequest = Message<"hami.core.admin.v1.ExportApprovedMembershipApplicationsCsvRequest"> & {
};

/**
 * Describes the message hami.core.admin.v1.ExportApprovedMembershipApplicationsCsvRequest.
 * Use `create(ExportApprovedMembershipApplicationsCsvRequestSchema)` to create a new message.
 */
export const ExportApprovedMembershipApplicationsCsvRequestSchema: GenMessage<ExportApprovedMembershipApplicationsCsvRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 20);

/**
 * @generated from message hami.core.admin.v1.ExportApprovedMembershipApplicationsCsvResponse
 */
export type ExportApprovedMembershipApplicationsCsvResponse = Message<"hami.core.admin.v1.ExportApprovedMembershipApplicationsCsvResponse"> & {
  /**
   * S3上のCSVファイルキー
   *
   * @generated from field: string csv_file_key = 1;
   */
  csvFileKey: string;

  /**
   * ダウンロード用署名付きURL
   *
   * @generated from field: string download_url = 2;
   */
  downloadUrl: string;

  /**
   * CSVに含めた申込件数
   *
   * @generated from field: int32 total_applications = 3;
   */
  totalApplications: number;
};

/**
 * Describes the message hami.core.admin.v1.ExportApprovedMembershipApplicationsCsvResponse.
 * Use `create(ExportApprovedMembershipApplicationsCsvResponseSchema)` to create a new message.
 */
export const ExportApprovedMembershipApplicationsCsvResponseSchema: GenMessage<ExportApprovedMembershipApplicationsCsvResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 21);

/**
 * @generated from enum hami.core.admin.v1.ReviewType
 */
export enum ReviewType {
  /**
   * @generated from enum value: REVIEW_TYPE_UNKNOWN = 0;
   */
  REVIEW_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: APPROVE = 1;
   */
  APPROVE = 1,

  /**
   * @generated from enum value: REJECT = 2;
   */
  REJECT = 2,

  /**
   * @generated from enum value: REMAND = 3;
   */
  REMAND = 3,

  /**
   * @generated from enum value: NOT_REVIEWED = 4;
   */
  NOT_REVIEWED = 4,
}

/**
 * Describes the enum hami.core.admin.v1.ReviewType.
 */
export const ReviewTypeSchema: GenEnum<ReviewType> = /*@__PURE__*/
  enumDesc(file_membership_application_service, 0);

/**
 * @generated from enum hami.core.admin.v1.DocumentType
 */
export enum DocumentType {
  /**
   * @generated from enum value: DOCUMENT_TYPE_UNKNOWN = 0;
   */
  DOCUMENT_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: IDENTITY_FRONT = 1;
   */
  IDENTITY_FRONT = 1,

  /**
   * @generated from enum value: IDENTITY_BACK = 2;
   */
  IDENTITY_BACK = 2,

  /**
   * 法人KYC書類
   *
   * 印鑑証明書
   *
   * @generated from enum value: CORP_SEAL_CERT = 3;
   */
  CORP_SEAL_CERT = 3,

  /**
   * 履歴事項（全部/現在）証明書
   *
   * @generated from enum value: CORP_REGISTRY_CERT = 4;
   */
  CORP_REGISTRY_CERT = 4,
}

/**
 * Describes the enum hami.core.admin.v1.DocumentType.
 */
export const DocumentTypeSchema: GenEnum<DocumentType> = /*@__PURE__*/
  enumDesc(file_membership_application_service, 1);

/**
 * @generated from enum hami.core.admin.v1.ApplicantType
 */
export enum ApplicantType {
  /**
   * @generated from enum value: APPLICANT_TYPE_UNKNOWN = 0;
   */
  APPLICANT_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: INDIVIDUAL = 1;
   */
  INDIVIDUAL = 1,

  /**
   * @generated from enum value: CORPORATE = 2;
   */
  CORPORATE = 2,
}

/**
 * Describes the enum hami.core.admin.v1.ApplicantType.
 */
export const ApplicantTypeSchema: GenEnum<ApplicantType> = /*@__PURE__*/
  enumDesc(file_membership_application_service, 2);

/**
 * @generated from enum hami.core.admin.v1.MembershipApplicationListStatus
 */
export enum MembershipApplicationListStatus {
  /**
   * @generated from enum value: MEMBERSHIP_APPLICATION_LIST_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 要対応（未審査）
   *
   * @generated from enum value: MEMBERSHIP_APPLICATION_LIST_STATUS_ACTION_REQUIRED = 1;
   */
  ACTION_REQUIRED = 1,

  /**
   * 承認済み
   *
   * @generated from enum value: MEMBERSHIP_APPLICATION_LIST_STATUS_APPROVED = 2;
   */
  APPROVED = 2,

  /**
   * 送付済み（印刷済み）
   *
   * @generated from enum value: MEMBERSHIP_APPLICATION_LIST_STATUS_SENT = 3;
   */
  SENT = 3,

  /**
   * コンプライアンス審査待ち
   *
   * @generated from enum value: MEMBERSHIP_APPLICATION_LIST_STATUS_COMPLIANCE_PENDING = 4;
   */
  COMPLIANCE_PENDING = 4,
}

/**
 * Describes the enum hami.core.admin.v1.MembershipApplicationListStatus.
 */
export const MembershipApplicationListStatusSchema: GenEnum<MembershipApplicationListStatus> = /*@__PURE__*/
  enumDesc(file_membership_application_service, 3);

/**
 * @generated from service hami.core.admin.v1.MembershipApplicationService
 */
export const MembershipApplicationService: GenService<{
  /**
   * @generated from rpc hami.core.admin.v1.MembershipApplicationService.ListMembershipApplications
   */
  listMembershipApplications: {
    methodKind: "unary";
    input: typeof ListMembershipApplicationsRequestSchema;
    output: typeof ListMembershipApplicationsResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.MembershipApplicationService.GetMembershipApplication
   */
  getMembershipApplication: {
    methodKind: "unary";
    input: typeof GetMembershipApplicationRequestSchema;
    output: typeof GetMembershipApplicationResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.MembershipApplicationService.ReviewMembershipApplication
   */
  reviewMembershipApplication: {
    methodKind: "unary";
    input: typeof ReviewMembershipApplicationRequestSchema;
    output: typeof ReviewMembershipApplicationResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.MembershipApplicationService.ReviewMembershipApplicationDocumentGroup
   */
  reviewMembershipApplicationDocumentGroup: {
    methodKind: "unary";
    input: typeof ReviewMembershipApplicationDocumentGroupRequestSchema;
    output: typeof ReviewMembershipApplicationDocumentGroupResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.MembershipApplicationService.ReviewComplianceMembershipApplication
   */
  reviewComplianceMembershipApplication: {
    methodKind: "unary";
    input: typeof ReviewComplianceMembershipApplicationRequestSchema;
    output: typeof ReviewComplianceMembershipApplicationResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.MembershipApplicationService.ReviewComplianceMembershipApplicationDocumentGroup
   */
  reviewComplianceMembershipApplicationDocumentGroup: {
    methodKind: "unary";
    input: typeof ReviewComplianceMembershipApplicationDocumentGroupRequestSchema;
    output: typeof ReviewComplianceMembershipApplicationDocumentGroupResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.MembershipApplicationService.GetIdentityDocumentImageUrl
   */
  getIdentityDocumentImageUrl: {
    methodKind: "unary";
    input: typeof GetIdentityDocumentImageUrlRequestSchema;
    output: typeof GetIdentityDocumentImageUrlResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.MembershipApplicationService.ExportApprovedMembershipApplicationsCsv
   */
  exportApprovedMembershipApplicationsCsv: {
    methodKind: "unary";
    input: typeof ExportApprovedMembershipApplicationsCsvRequestSchema;
    output: typeof ExportApprovedMembershipApplicationsCsvResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_membership_application_service, 0);

