// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file horse_billing_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_billing_service.proto.
 */
export const file_horse_billing_service: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * 馬の支出一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListHorseBillingsRequest
 */
export type ListHorseBillingsRequest = Message<"hami.core.admin.v1.ListHorseBillingsRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * YYYYMM形式
   *
   * @generated from field: optional int32 billing_year_month = 2;
   */
  billingYearMonth?: number;

  /**
   * @generated from field: optional hami.core.admin.v1.HorseBillingItemType item_type = 3;
   */
  itemType?: HorseBillingItemType;

  /**
   * @generated from field: optional int32 biller_id = 4;
   */
  billerId?: number;

  /**
   * @generated from field: optional bool closing = 5;
   */
  closing?: boolean;

  /**
   * ページ番号（1から開始）
   *
   * @generated from field: optional int32 page = 6;
   */
  page?: number;

  /**
   * ページサイズ
   *
   * @generated from field: optional int32 page_size = 7;
   */
  pageSize?: number;
};

/**
 * Describes the message hami.core.admin.v1.ListHorseBillingsRequest.
 * Use `create(ListHorseBillingsRequestSchema)` to create a new message.
 */
export const ListHorseBillingsRequestSchema: GenMessage<ListHorseBillingsRequest> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 0);

/**
 * 全馬支出一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListAllHorseBillingsRequest
 */
export type ListAllHorseBillingsRequest = Message<"hami.core.admin.v1.ListAllHorseBillingsRequest"> & {
  /**
   * @generated from field: int32 start_year = 1;
   */
  startYear: number;

  /**
   * @generated from field: int32 start_month = 2;
   */
  startMonth: number;

  /**
   * @generated from field: int32 start_day = 3;
   */
  startDay: number;

  /**
   * @generated from field: int32 end_year = 4;
   */
  endYear: number;

  /**
   * @generated from field: int32 end_month = 5;
   */
  endMonth: number;

  /**
   * @generated from field: int32 end_day = 6;
   */
  endDay: number;
};

/**
 * Describes the message hami.core.admin.v1.ListAllHorseBillingsRequest.
 * Use `create(ListAllHorseBillingsRequestSchema)` to create a new message.
 */
export const ListAllHorseBillingsRequestSchema: GenMessage<ListAllHorseBillingsRequest> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 1);

/**
 * 全馬支出一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListAllHorseBillingsResponse
 */
export type ListAllHorseBillingsResponse = Message<"hami.core.admin.v1.ListAllHorseBillingsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.HorseBillingItem billings = 1;
   */
  billings: HorseBillingItem[];
};

/**
 * Describes the message hami.core.admin.v1.ListAllHorseBillingsResponse.
 * Use `create(ListAllHorseBillingsResponseSchema)` to create a new message.
 */
export const ListAllHorseBillingsResponseSchema: GenMessage<ListAllHorseBillingsResponse> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 2);

/**
 * 馬の支出アイテム
 *
 * @generated from message hami.core.admin.v1.HorseBillingItem
 */
export type HorseBillingItem = Message<"hami.core.admin.v1.HorseBillingItem"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * @generated from field: optional string horse_name = 3;
   */
  horseName?: string;

  /**
   * YYYYMM形式
   *
   * @generated from field: int32 billing_year_month = 4;
   */
  billingYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 5;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 6;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 7;
   */
  occurredDay: number;

  /**
   * @generated from field: int32 biller_id = 8;
   */
  billerId: number;

  /**
   * @generated from field: hami.core.admin.v1.HorseBillingItemType item_type = 9;
   */
  itemType: HorseBillingItemType;

  /**
   * @generated from field: string item_type_other = 10;
   */
  itemTypeOther: string;

  /**
   * @generated from field: int32 billing_amount = 11;
   */
  billingAmount: number;

  /**
   * @generated from field: int32 tax_amount = 12;
   */
  taxAmount: number;

  /**
   * @generated from field: int32 subsidy_amount = 13;
   */
  subsidyAmount: number;

  /**
   * @generated from field: int32 total_amount = 14;
   */
  totalAmount: number;

  /**
   * @generated from field: string note = 15;
   */
  note: string;

  /**
   * @generated from field: optional string biller_name = 16;
   */
  billerName?: string;

  /**
   * @generated from field: bool closing = 17;
   */
  closing: boolean;
};

/**
 * Describes the message hami.core.admin.v1.HorseBillingItem.
 * Use `create(HorseBillingItemSchema)` to create a new message.
 */
export const HorseBillingItemSchema: GenMessage<HorseBillingItem> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 3);

/**
 * 馬の支出一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListHorseBillingsResponse
 */
export type ListHorseBillingsResponse = Message<"hami.core.admin.v1.ListHorseBillingsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.HorseBillingItem billings = 1;
   */
  billings: HorseBillingItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 4;
   */
  limit: number;

  /**
   * @generated from field: int32 total_pages = 5;
   */
  totalPages: number;
};

/**
 * Describes the message hami.core.admin.v1.ListHorseBillingsResponse.
 * Use `create(ListHorseBillingsResponseSchema)` to create a new message.
 */
export const ListHorseBillingsResponseSchema: GenMessage<ListHorseBillingsResponse> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 4);

/**
 * 馬の支出詳細取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetHorseBillingDetailRequest
 */
export type GetHorseBillingDetailRequest = Message<"hami.core.admin.v1.GetHorseBillingDetailRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message hami.core.admin.v1.GetHorseBillingDetailRequest.
 * Use `create(GetHorseBillingDetailRequestSchema)` to create a new message.
 */
export const GetHorseBillingDetailRequestSchema: GenMessage<GetHorseBillingDetailRequest> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 5);

/**
 * 馬の支出詳細取得レスポンス
 *
 * @generated from message hami.core.admin.v1.GetHorseBillingDetailResponse
 */
export type GetHorseBillingDetailResponse = Message<"hami.core.admin.v1.GetHorseBillingDetailResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.HorseBillingItem billing = 1;
   */
  billing?: HorseBillingItem;
};

/**
 * Describes the message hami.core.admin.v1.GetHorseBillingDetailResponse.
 * Use `create(GetHorseBillingDetailResponseSchema)` to create a new message.
 */
export const GetHorseBillingDetailResponseSchema: GenMessage<GetHorseBillingDetailResponse> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 6);

/**
 * 馬の支出作成リクエスト
 *
 * @generated from message hami.core.admin.v1.CreateHorseBillingRequest
 */
export type CreateHorseBillingRequest = Message<"hami.core.admin.v1.CreateHorseBillingRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * YYYYMM形式
   *
   * @generated from field: int32 billing_year_month = 2;
   */
  billingYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 3;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 4;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 5;
   */
  occurredDay: number;

  /**
   * @generated from field: int32 biller_id = 6;
   */
  billerId: number;

  /**
   * @generated from field: hami.core.admin.v1.HorseBillingItemType item_type = 7;
   */
  itemType: HorseBillingItemType;

  /**
   * @generated from field: string item_type_other = 8;
   */
  itemTypeOther: string;

  /**
   * @generated from field: int32 billing_amount = 9;
   */
  billingAmount: number;

  /**
   * @generated from field: int32 tax_amount = 10;
   */
  taxAmount: number;

  /**
   * @generated from field: int32 subsidy_amount = 11;
   */
  subsidyAmount: number;

  /**
   * @generated from field: int32 total_amount = 12;
   */
  totalAmount: number;

  /**
   * @generated from field: string note = 13;
   */
  note: string;
};

/**
 * Describes the message hami.core.admin.v1.CreateHorseBillingRequest.
 * Use `create(CreateHorseBillingRequestSchema)` to create a new message.
 */
export const CreateHorseBillingRequestSchema: GenMessage<CreateHorseBillingRequest> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 7);

/**
 * 馬の支出作成レスポンス
 *
 * @generated from message hami.core.admin.v1.CreateHorseBillingResponse
 */
export type CreateHorseBillingResponse = Message<"hami.core.admin.v1.CreateHorseBillingResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.CreateHorseBillingResponse.
 * Use `create(CreateHorseBillingResponseSchema)` to create a new message.
 */
export const CreateHorseBillingResponseSchema: GenMessage<CreateHorseBillingResponse> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 8);

/**
 * 馬の支出更新リクエスト
 *
 * @generated from message hami.core.admin.v1.UpdateHorseBillingRequest
 */
export type UpdateHorseBillingRequest = Message<"hami.core.admin.v1.UpdateHorseBillingRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * YYYYMM形式
   *
   * @generated from field: int32 billing_year_month = 2;
   */
  billingYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 3;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 4;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 5;
   */
  occurredDay: number;

  /**
   * @generated from field: int32 biller_id = 6;
   */
  billerId: number;

  /**
   * @generated from field: hami.core.admin.v1.HorseBillingItemType item_type = 7;
   */
  itemType: HorseBillingItemType;

  /**
   * @generated from field: string item_type_other = 8;
   */
  itemTypeOther: string;

  /**
   * @generated from field: int32 billing_amount = 9;
   */
  billingAmount: number;

  /**
   * @generated from field: int32 tax_amount = 10;
   */
  taxAmount: number;

  /**
   * @generated from field: int32 subsidy_amount = 11;
   */
  subsidyAmount: number;

  /**
   * @generated from field: int32 total_amount = 12;
   */
  totalAmount: number;

  /**
   * @generated from field: string note = 13;
   */
  note: string;
};

/**
 * Describes the message hami.core.admin.v1.UpdateHorseBillingRequest.
 * Use `create(UpdateHorseBillingRequestSchema)` to create a new message.
 */
export const UpdateHorseBillingRequestSchema: GenMessage<UpdateHorseBillingRequest> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 9);

/**
 * 馬の支出更新レスポンス
 *
 * @generated from message hami.core.admin.v1.UpdateHorseBillingResponse
 */
export type UpdateHorseBillingResponse = Message<"hami.core.admin.v1.UpdateHorseBillingResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.UpdateHorseBillingResponse.
 * Use `create(UpdateHorseBillingResponseSchema)` to create a new message.
 */
export const UpdateHorseBillingResponseSchema: GenMessage<UpdateHorseBillingResponse> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 10);

/**
 * 馬の支出削除リクエスト
 *
 * @generated from message hami.core.admin.v1.DeleteHorseBillingRequest
 */
export type DeleteHorseBillingRequest = Message<"hami.core.admin.v1.DeleteHorseBillingRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message hami.core.admin.v1.DeleteHorseBillingRequest.
 * Use `create(DeleteHorseBillingRequestSchema)` to create a new message.
 */
export const DeleteHorseBillingRequestSchema: GenMessage<DeleteHorseBillingRequest> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 11);

/**
 * 馬の支出削除レスポンス
 *
 * @generated from message hami.core.admin.v1.DeleteHorseBillingResponse
 */
export type DeleteHorseBillingResponse = Message<"hami.core.admin.v1.DeleteHorseBillingResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.DeleteHorseBillingResponse.
 * Use `create(DeleteHorseBillingResponseSchema)` to create a new message.
 */
export const DeleteHorseBillingResponseSchema: GenMessage<DeleteHorseBillingResponse> = /*@__PURE__*/
  messageDesc(file_horse_billing_service, 12);

/**
 * 支出項目タイプ
 *
 * @generated from enum hami.core.admin.v1.HorseBillingItemType
 */
export enum HorseBillingItemType {
  /**
   * @generated from enum value: HORSE_BILLING_ITEM_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 預託料
   *
   * @generated from enum value: HORSE_BILLING_ITEM_TYPE_ENTRUST = 1;
   */
  ENTRUST = 1,

  /**
   * 保険料
   *
   * @generated from enum value: HORSE_BILLING_ITEM_TYPE_INSURANCE = 2;
   */
  INSURANCE = 2,

  /**
   * その他
   *
   * @generated from enum value: HORSE_BILLING_ITEM_TYPE_OTHER = 3;
   */
  OTHER = 3,
}

/**
 * Describes the enum hami.core.admin.v1.HorseBillingItemType.
 */
export const HorseBillingItemTypeSchema: GenEnum<HorseBillingItemType> = /*@__PURE__*/
  enumDesc(file_horse_billing_service, 0);

/**
 * 馬の支出管理サービス
 *
 * @generated from service hami.core.admin.v1.HorseBillingService
 */
export const HorseBillingService: GenService<{
  /**
   * 馬の支出一覧取得
   *
   * @generated from rpc hami.core.admin.v1.HorseBillingService.ListHorseBillings
   */
  listHorseBillings: {
    methodKind: "unary";
    input: typeof ListHorseBillingsRequestSchema;
    output: typeof ListHorseBillingsResponseSchema;
  },
  /**
   * 全馬の支出一覧取得（日付範囲指定）
   *
   * @generated from rpc hami.core.admin.v1.HorseBillingService.ListAllHorseBillings
   */
  listAllHorseBillings: {
    methodKind: "unary";
    input: typeof ListAllHorseBillingsRequestSchema;
    output: typeof ListAllHorseBillingsResponseSchema;
  },
  /**
   * 馬の支出詳細取得
   *
   * @generated from rpc hami.core.admin.v1.HorseBillingService.GetHorseBillingDetail
   */
  getHorseBillingDetail: {
    methodKind: "unary";
    input: typeof GetHorseBillingDetailRequestSchema;
    output: typeof GetHorseBillingDetailResponseSchema;
  },
  /**
   * 馬の支出作成
   *
   * @generated from rpc hami.core.admin.v1.HorseBillingService.CreateHorseBilling
   */
  createHorseBilling: {
    methodKind: "unary";
    input: typeof CreateHorseBillingRequestSchema;
    output: typeof CreateHorseBillingResponseSchema;
  },
  /**
   * 馬の支出更新
   *
   * @generated from rpc hami.core.admin.v1.HorseBillingService.UpdateHorseBilling
   */
  updateHorseBilling: {
    methodKind: "unary";
    input: typeof UpdateHorseBillingRequestSchema;
    output: typeof UpdateHorseBillingResponseSchema;
  },
  /**
   * 馬の支出削除
   *
   * @generated from rpc hami.core.admin.v1.HorseBillingService.DeleteHorseBilling
   */
  deleteHorseBilling: {
    methodKind: "unary";
    input: typeof DeleteHorseBillingRequestSchema;
    output: typeof DeleteHorseBillingResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_billing_service, 0);

