// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file annual_bundle_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file annual_bundle_service.proto.
 */
export const file_annual_bundle_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * @generated from message hami.core.admin.v1.ListAnnualBundlesRequest
 */
export type ListAnnualBundlesRequest = Message<"hami.core.admin.v1.ListAnnualBundlesRequest"> & {
  /**
   * @generated from field: int32 page = 1;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 2;
   */
  limit: number;

  /**
   * 年度フィルタ
   *
   * @generated from field: optional int32 fiscal_year = 3;
   */
  fiscalYear?: number;
};

/**
 * Describes the message hami.core.admin.v1.ListAnnualBundlesRequest.
 * Use `create(ListAnnualBundlesRequestSchema)` to create a new message.
 */
export const ListAnnualBundlesRequestSchema: GenMessage<ListAnnualBundlesRequest> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 0);

/**
 * @generated from message hami.core.admin.v1.ListAnnualBundlesResponse
 */
export type ListAnnualBundlesResponse = Message<"hami.core.admin.v1.ListAnnualBundlesResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.AnnualBundleListItem bundles = 1;
   */
  bundles: AnnualBundleListItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 4;
   */
  limit: number;

  /**
   * @generated from field: int32 total_pages = 5;
   */
  totalPages: number;
};

/**
 * Describes the message hami.core.admin.v1.ListAnnualBundlesResponse.
 * Use `create(ListAnnualBundlesResponseSchema)` to create a new message.
 */
export const ListAnnualBundlesResponseSchema: GenMessage<ListAnnualBundlesResponse> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 1);

/**
 * @generated from message hami.core.admin.v1.AnnualBundleListItem
 */
export type AnnualBundleListItem = Message<"hami.core.admin.v1.AnnualBundleListItem"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;

  /**
   * @generated from field: int32 fiscal_year = 2;
   */
  fiscalYear: number;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: int32 shares = 4;
   */
  shares: number;

  /**
   * @generated from field: hami.core.admin.v1.AnnualBundlePublishStatus publish_status = 5;
   */
  publishStatus: AnnualBundlePublishStatus;

  /**
   * @generated from field: hami.core.admin.v1.AnnualBundleRecruitmentStatus recruitment_status = 6;
   */
  recruitmentStatus: AnnualBundleRecruitmentStatus;
};

/**
 * Describes the message hami.core.admin.v1.AnnualBundleListItem.
 * Use `create(AnnualBundleListItemSchema)` to create a new message.
 */
export const AnnualBundleListItemSchema: GenMessage<AnnualBundleListItem> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 2);

/**
 * @generated from message hami.core.admin.v1.GetAnnualBundleRequest
 */
export type GetAnnualBundleRequest = Message<"hami.core.admin.v1.GetAnnualBundleRequest"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetAnnualBundleRequest.
 * Use `create(GetAnnualBundleRequestSchema)` to create a new message.
 */
export const GetAnnualBundleRequestSchema: GenMessage<GetAnnualBundleRequest> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 3);

/**
 * @generated from message hami.core.admin.v1.GetAnnualBundleResponse
 */
export type GetAnnualBundleResponse = Message<"hami.core.admin.v1.GetAnnualBundleResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.AnnualBundleDetail bundle = 1;
   */
  bundle?: AnnualBundleDetail;
};

/**
 * Describes the message hami.core.admin.v1.GetAnnualBundleResponse.
 * Use `create(GetAnnualBundleResponseSchema)` to create a new message.
 */
export const GetAnnualBundleResponseSchema: GenMessage<GetAnnualBundleResponse> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 4);

/**
 * @generated from message hami.core.admin.v1.AnnualBundleDetail
 */
export type AnnualBundleDetail = Message<"hami.core.admin.v1.AnnualBundleDetail"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;

  /**
   * @generated from field: int32 fiscal_year = 2;
   */
  fiscalYear: number;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: int32 shares = 4;
   */
  shares: number;

  /**
   * @generated from field: hami.core.admin.v1.AnnualBundlePublishStatus publish_status = 5;
   */
  publishStatus: AnnualBundlePublishStatus;

  /**
   * @generated from field: hami.core.admin.v1.AnnualBundleRecruitmentStatus recruitment_status = 6;
   */
  recruitmentStatus: AnnualBundleRecruitmentStatus;

  /**
   * @generated from field: repeated hami.core.admin.v1.AnnualBundleHorseRelation horses = 7;
   */
  horses: AnnualBundleHorseRelation[];

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 8;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 9;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hami.core.admin.v1.AnnualBundleDetail.
 * Use `create(AnnualBundleDetailSchema)` to create a new message.
 */
export const AnnualBundleDetailSchema: GenMessage<AnnualBundleDetail> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 5);

/**
 * @generated from message hami.core.admin.v1.AnnualBundleHorseRelation
 */
export type AnnualBundleHorseRelation = Message<"hami.core.admin.v1.AnnualBundleHorseRelation"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string horse_name = 2;
   */
  horseName: string;

  /**
   * @generated from field: string recruitment_name = 3;
   */
  recruitmentName: string;

  /**
   * @generated from field: int32 recruitment_no = 4;
   */
  recruitmentNo: number;

  /**
   * 募集口数
   *
   * @generated from field: int32 shares_total = 5;
   */
  sharesTotal: number;
};

/**
 * Describes the message hami.core.admin.v1.AnnualBundleHorseRelation.
 * Use `create(AnnualBundleHorseRelationSchema)` to create a new message.
 */
export const AnnualBundleHorseRelationSchema: GenMessage<AnnualBundleHorseRelation> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 6);

/**
 * @generated from message hami.core.admin.v1.CreateAnnualBundleRequest
 */
export type CreateAnnualBundleRequest = Message<"hami.core.admin.v1.CreateAnnualBundleRequest"> & {
  /**
   * @generated from field: int32 fiscal_year = 1;
   */
  fiscalYear: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: int32 shares = 3;
   */
  shares: number;

  /**
   * @generated from field: hami.core.admin.v1.AnnualBundlePublishStatus publish_status = 4;
   */
  publishStatus: AnnualBundlePublishStatus;

  /**
   * @generated from field: hami.core.admin.v1.AnnualBundleRecruitmentStatus recruitment_status = 5;
   */
  recruitmentStatus: AnnualBundleRecruitmentStatus;

  /**
   * 初期紐付け
   *
   * @generated from field: repeated int32 horse_ids = 6;
   */
  horseIds: number[];
};

/**
 * Describes the message hami.core.admin.v1.CreateAnnualBundleRequest.
 * Use `create(CreateAnnualBundleRequestSchema)` to create a new message.
 */
export const CreateAnnualBundleRequestSchema: GenMessage<CreateAnnualBundleRequest> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 7);

/**
 * @generated from message hami.core.admin.v1.CreateAnnualBundleResponse
 */
export type CreateAnnualBundleResponse = Message<"hami.core.admin.v1.CreateAnnualBundleResponse"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;
};

/**
 * Describes the message hami.core.admin.v1.CreateAnnualBundleResponse.
 * Use `create(CreateAnnualBundleResponseSchema)` to create a new message.
 */
export const CreateAnnualBundleResponseSchema: GenMessage<CreateAnnualBundleResponse> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 8);

/**
 * @generated from message hami.core.admin.v1.UpdateAnnualBundleRequest
 */
export type UpdateAnnualBundleRequest = Message<"hami.core.admin.v1.UpdateAnnualBundleRequest"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;

  /**
   * @generated from field: optional string name = 2;
   */
  name?: string;

  /**
   * @generated from field: optional int32 shares = 3;
   */
  shares?: number;

  /**
   * @generated from field: optional hami.core.admin.v1.AnnualBundlePublishStatus publish_status = 4;
   */
  publishStatus?: AnnualBundlePublishStatus;

  /**
   * @generated from field: optional hami.core.admin.v1.AnnualBundleRecruitmentStatus recruitment_status = 5;
   */
  recruitmentStatus?: AnnualBundleRecruitmentStatus;

  /**
   * 全置換（送られたIDセットに同期）
   *
   * @generated from field: repeated int32 horse_ids = 6;
   */
  horseIds: number[];
};

/**
 * Describes the message hami.core.admin.v1.UpdateAnnualBundleRequest.
 * Use `create(UpdateAnnualBundleRequestSchema)` to create a new message.
 */
export const UpdateAnnualBundleRequestSchema: GenMessage<UpdateAnnualBundleRequest> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 9);

/**
 * @generated from message hami.core.admin.v1.UpdateAnnualBundleResponse
 */
export type UpdateAnnualBundleResponse = Message<"hami.core.admin.v1.UpdateAnnualBundleResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.UpdateAnnualBundleResponse.
 * Use `create(UpdateAnnualBundleResponseSchema)` to create a new message.
 */
export const UpdateAnnualBundleResponseSchema: GenMessage<UpdateAnnualBundleResponse> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 10);

/**
 * @generated from message hami.core.admin.v1.ListAnnualBundleHorsesCandidatesRequest
 */
export type ListAnnualBundleHorsesCandidatesRequest = Message<"hami.core.admin.v1.ListAnnualBundleHorsesCandidatesRequest"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;

  /**
   * @generated from field: int32 page = 2;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 3;
   */
  limit: number;

  /**
   * 馬名/募集名 検索
   *
   * @generated from field: optional string search = 4;
   */
  search?: string;
};

/**
 * Describes the message hami.core.admin.v1.ListAnnualBundleHorsesCandidatesRequest.
 * Use `create(ListAnnualBundleHorsesCandidatesRequestSchema)` to create a new message.
 */
export const ListAnnualBundleHorsesCandidatesRequestSchema: GenMessage<ListAnnualBundleHorsesCandidatesRequest> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 11);

/**
 * @generated from message hami.core.admin.v1.ListAnnualBundleHorsesCandidatesResponse
 */
export type ListAnnualBundleHorsesCandidatesResponse = Message<"hami.core.admin.v1.ListAnnualBundleHorsesCandidatesResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.AnnualBundleHorseCandidate horses = 1;
   */
  horses: AnnualBundleHorseCandidate[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 limit = 4;
   */
  limit: number;

  /**
   * @generated from field: int32 total_pages = 5;
   */
  totalPages: number;
};

/**
 * Describes the message hami.core.admin.v1.ListAnnualBundleHorsesCandidatesResponse.
 * Use `create(ListAnnualBundleHorsesCandidatesResponseSchema)` to create a new message.
 */
export const ListAnnualBundleHorsesCandidatesResponseSchema: GenMessage<ListAnnualBundleHorsesCandidatesResponse> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 12);

/**
 * @generated from message hami.core.admin.v1.AnnualBundleHorseCandidate
 */
export type AnnualBundleHorseCandidate = Message<"hami.core.admin.v1.AnnualBundleHorseCandidate"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string recruitment_name = 2;
   */
  recruitmentName: string;

  /**
   * @generated from field: string horse_name = 3;
   */
  horseName: string;

  /**
   * @generated from field: int32 recruitment_year = 4;
   */
  recruitmentYear: number;

  /**
   * @generated from field: bool selected = 5;
   */
  selected: boolean;

  /**
   * 募集馬ID
   *
   * @generated from field: int32 recruitment_no = 6;
   */
  recruitmentNo: number;

  /**
   * 募集口数
   *
   * @generated from field: int32 shares_total = 7;
   */
  sharesTotal: number;
};

/**
 * Describes the message hami.core.admin.v1.AnnualBundleHorseCandidate.
 * Use `create(AnnualBundleHorseCandidateSchema)` to create a new message.
 */
export const AnnualBundleHorseCandidateSchema: GenMessage<AnnualBundleHorseCandidate> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 13);

/**
 * 年度バンドルの公開ステータス
 *
 * @generated from enum hami.core.admin.v1.AnnualBundlePublishStatus
 */
export enum AnnualBundlePublishStatus {
  /**
   * @generated from enum value: ANNUAL_BUNDLE_PUBLISH_STATUS_UNSPECIFIED = 0;
   */
  ANNUAL_BUNDLE_PUBLISH_STATUS_UNSPECIFIED = 0,

  /**
   * 公開
   *
   * @generated from enum value: AB_PUBLIC = 1;
   */
  AB_PUBLIC = 1,

  /**
   * 非公開
   *
   * @generated from enum value: AB_PRIVATE = 2;
   */
  AB_PRIVATE = 2,
}

/**
 * Describes the enum hami.core.admin.v1.AnnualBundlePublishStatus.
 */
export const AnnualBundlePublishStatusSchema: GenEnum<AnnualBundlePublishStatus> = /*@__PURE__*/
  enumDesc(file_annual_bundle_service, 0);

/**
 * 年度バンドルの募集ステータス
 *
 * @generated from enum hami.core.admin.v1.AnnualBundleRecruitmentStatus
 */
export enum AnnualBundleRecruitmentStatus {
  /**
   * @generated from enum value: ANNUAL_BUNDLE_RECRUITMENT_STATUS_UNSPECIFIED = 0;
   */
  ANNUAL_BUNDLE_RECRUITMENT_STATUS_UNSPECIFIED = 0,

  /**
   * 募集前
   *
   * @generated from enum value: AB_UPCOMING = 1;
   */
  AB_UPCOMING = 1,

  /**
   * 募集中
   *
   * @generated from enum value: AB_ACTIVE = 2;
   */
  AB_ACTIVE = 2,

  /**
   * 募集終了
   *
   * @generated from enum value: AB_CLOSED = 3;
   */
  AB_CLOSED = 3,

  /**
   * 満口
   *
   * @generated from enum value: AB_FULL = 4;
   */
  AB_FULL = 4,
}

/**
 * Describes the enum hami.core.admin.v1.AnnualBundleRecruitmentStatus.
 */
export const AnnualBundleRecruitmentStatusSchema: GenEnum<AnnualBundleRecruitmentStatus> = /*@__PURE__*/
  enumDesc(file_annual_bundle_service, 1);

/**
 * @generated from service hami.core.admin.v1.AnnualBundleService
 */
export const AnnualBundleService: GenService<{
  /**
   * @generated from rpc hami.core.admin.v1.AnnualBundleService.ListAnnualBundles
   */
  listAnnualBundles: {
    methodKind: "unary";
    input: typeof ListAnnualBundlesRequestSchema;
    output: typeof ListAnnualBundlesResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.AnnualBundleService.GetAnnualBundle
   */
  getAnnualBundle: {
    methodKind: "unary";
    input: typeof GetAnnualBundleRequestSchema;
    output: typeof GetAnnualBundleResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.AnnualBundleService.CreateAnnualBundle
   */
  createAnnualBundle: {
    methodKind: "unary";
    input: typeof CreateAnnualBundleRequestSchema;
    output: typeof CreateAnnualBundleResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.AnnualBundleService.UpdateAnnualBundle
   */
  updateAnnualBundle: {
    methodKind: "unary";
    input: typeof UpdateAnnualBundleRequestSchema;
    output: typeof UpdateAnnualBundleResponseSchema;
  },
  /**
   * 同年度の馬から候補を取得（選択状態付き）
   *
   * @generated from rpc hami.core.admin.v1.AnnualBundleService.ListAnnualBundleHorsesCandidates
   */
  listAnnualBundleHorsesCandidates: {
    methodKind: "unary";
    input: typeof ListAnnualBundleHorsesCandidatesRequestSchema;
    output: typeof ListAnnualBundleHorsesCandidatesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_annual_bundle_service, 0);

