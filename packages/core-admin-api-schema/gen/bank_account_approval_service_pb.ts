// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file bank_account_approval_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file bank_account_approval_service.proto.
 */
export const file_bank_account_approval_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * 承認待ち口座登録アイテム（一覧表示用）
 *
 * @generated from message hami.core.admin.v1.BankAccountRegistrationItem
 */
export type BankAccountRegistrationItem = Message<"hami.core.admin.v1.BankAccountRegistrationItem"> & {
  /**
   * @generated from field: int32 bank_account_registration_id = 1;
   */
  bankAccountRegistrationId: number;

  /**
   * @generated from field: int32 member_id = 2;
   */
  memberId: number;

  /**
   * 姓名結合
   *
   * @generated from field: string member_name = 3;
   */
  memberName: string;

  /**
   * カナ姓名結合
   *
   * @generated from field: string member_name_kana = 4;
   */
  memberNameKana: string;

  /**
   * 口座名義カナ
   *
   * @generated from field: string result_account_name = 5;
   */
  resultAccountName: string;

  /**
   * @generated from field: string result_bank_code = 6;
   */
  resultBankCode: string;

  /**
   * @generated from field: string result_branch_code = 7;
   */
  resultBranchCode: string;

  /**
   * マスク済み
   *
   * @generated from field: string result_account_number = 8;
   */
  resultAccountNumber: string;

  /**
   * @generated from field: google.protobuf.Timestamp completed_at = 9;
   */
  completedAt?: Timestamp;

  /**
   * 承認状況
   *
   * @generated from field: hami.core.admin.v1.BankAccountApprovalStatus approval_status = 10;
   */
  approvalStatus: BankAccountApprovalStatus;

  /**
   * 承認・却下日時
   *
   * @generated from field: google.protobuf.Timestamp approved_at = 11;
   */
  approvedAt?: Timestamp;

  /**
   * 承認者名
   *
   * @generated from field: string admin_user_name = 12;
   */
  adminUserName: string;
};

/**
 * Describes the message hami.core.admin.v1.BankAccountRegistrationItem.
 * Use `create(BankAccountRegistrationItemSchema)` to create a new message.
 */
export const BankAccountRegistrationItemSchema: GenMessage<BankAccountRegistrationItem> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 0);

/**
 * 口座登録承認詳細情報（承認履歴含む）
 *
 * @generated from message hami.core.admin.v1.BankAccountApprovalDetail
 */
export type BankAccountApprovalDetail = Message<"hami.core.admin.v1.BankAccountApprovalDetail"> & {
  /**
   * @generated from field: int32 bank_account_registration_id = 1;
   */
  bankAccountRegistrationId: number;

  /**
   * @generated from field: int32 member_id = 2;
   */
  memberId: number;

  /**
   * @generated from field: string member_name = 3;
   */
  memberName: string;

  /**
   * @generated from field: string member_name_kana = 4;
   */
  memberNameKana: string;

  /**
   * @generated from field: string member_email = 5;
   */
  memberEmail: string;

  /**
   * @generated from field: int32 member_number = 6;
   */
  memberNumber: number;

  /**
   * 口座情報
   *
   * @generated from field: string result_bank_code = 7;
   */
  resultBankCode: string;

  /**
   * @generated from field: string result_bank_name = 8;
   */
  resultBankName: string;

  /**
   * @generated from field: string result_branch_code = 9;
   */
  resultBranchCode: string;

  /**
   * @generated from field: string result_branch_name = 10;
   */
  resultBranchName: string;

  /**
   * マスク済み
   *
   * @generated from field: string result_account_number = 11;
   */
  resultAccountNumber: string;

  /**
   * 口座名義カナ
   *
   * @generated from field: string result_account_name = 12;
   */
  resultAccountName: string;

  /**
   * 申請情報
   *
   * @generated from field: google.protobuf.Timestamp completed_at = 13;
   */
  completedAt?: Timestamp;

  /**
   * @generated from field: hami.core.admin.v1.BankAccountApprovalStatus approval_status = 14;
   */
  approvalStatus: BankAccountApprovalStatus;

  /**
   * 承認履歴
   *
   * @generated from field: repeated hami.core.admin.v1.BankAccountApprovalHistory approval_histories = 15;
   */
  approvalHistories: BankAccountApprovalHistory[];
};

/**
 * Describes the message hami.core.admin.v1.BankAccountApprovalDetail.
 * Use `create(BankAccountApprovalDetailSchema)` to create a new message.
 */
export const BankAccountApprovalDetailSchema: GenMessage<BankAccountApprovalDetail> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 1);

/**
 * 口座登録承認履歴
 *
 * @generated from message hami.core.admin.v1.BankAccountApprovalHistory
 */
export type BankAccountApprovalHistory = Message<"hami.core.admin.v1.BankAccountApprovalHistory"> & {
  /**
   * @generated from field: int32 bank_account_approval_id = 1;
   */
  bankAccountApprovalId: number;

  /**
   * @generated from field: int32 bank_account_registration_id = 2;
   */
  bankAccountRegistrationId: number;

  /**
   * @generated from field: hami.core.admin.v1.BankAccountApprovalStatus approval_status = 3;
   */
  approvalStatus: BankAccountApprovalStatus;

  /**
   * 承認コメントまたは却下理由
   *
   * @generated from field: string comment = 4;
   */
  comment: string;

  /**
   * 承認者名
   *
   * @generated from field: string admin_user_name = 5;
   */
  adminUserName: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;
};

/**
 * Describes the message hami.core.admin.v1.BankAccountApprovalHistory.
 * Use `create(BankAccountApprovalHistorySchema)` to create a new message.
 */
export const BankAccountApprovalHistorySchema: GenMessage<BankAccountApprovalHistory> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 2);

/**
 * 承認待ち口座一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListPendingBankAccountRegistrationsRequest
 */
export type ListPendingBankAccountRegistrationsRequest = Message<"hami.core.admin.v1.ListPendingBankAccountRegistrationsRequest"> & {
  /**
   * @generated from field: int32 page = 1;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;
};

/**
 * Describes the message hami.core.admin.v1.ListPendingBankAccountRegistrationsRequest.
 * Use `create(ListPendingBankAccountRegistrationsRequestSchema)` to create a new message.
 */
export const ListPendingBankAccountRegistrationsRequestSchema: GenMessage<ListPendingBankAccountRegistrationsRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 3);

/**
 * 承認待ち口座一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListPendingBankAccountRegistrationsResponse
 */
export type ListPendingBankAccountRegistrationsResponse = Message<"hami.core.admin.v1.ListPendingBankAccountRegistrationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.BankAccountRegistrationItem items = 1;
   */
  items: BankAccountRegistrationItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 4;
   */
  pageSize: number;
};

/**
 * Describes the message hami.core.admin.v1.ListPendingBankAccountRegistrationsResponse.
 * Use `create(ListPendingBankAccountRegistrationsResponseSchema)` to create a new message.
 */
export const ListPendingBankAccountRegistrationsResponseSchema: GenMessage<ListPendingBankAccountRegistrationsResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 4);

/**
 * 全口座登録一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListAllBankAccountRegistrationsRequest
 */
export type ListAllBankAccountRegistrationsRequest = Message<"hami.core.admin.v1.ListAllBankAccountRegistrationsRequest"> & {
  /**
   * @generated from field: int32 page = 1;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * フィルタ用（省略時は全件）
   *
   * @generated from field: optional hami.core.admin.v1.BankAccountApprovalStatus status_filter = 3;
   */
  statusFilter?: BankAccountApprovalStatus;
};

/**
 * Describes the message hami.core.admin.v1.ListAllBankAccountRegistrationsRequest.
 * Use `create(ListAllBankAccountRegistrationsRequestSchema)` to create a new message.
 */
export const ListAllBankAccountRegistrationsRequestSchema: GenMessage<ListAllBankAccountRegistrationsRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 5);

/**
 * 全口座登録一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListAllBankAccountRegistrationsResponse
 */
export type ListAllBankAccountRegistrationsResponse = Message<"hami.core.admin.v1.ListAllBankAccountRegistrationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.BankAccountRegistrationItem items = 1;
   */
  items: BankAccountRegistrationItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 4;
   */
  pageSize: number;
};

/**
 * Describes the message hami.core.admin.v1.ListAllBankAccountRegistrationsResponse.
 * Use `create(ListAllBankAccountRegistrationsResponseSchema)` to create a new message.
 */
export const ListAllBankAccountRegistrationsResponseSchema: GenMessage<ListAllBankAccountRegistrationsResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 6);

/**
 * 口座登録詳細取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetBankAccountRegistrationDetailRequest
 */
export type GetBankAccountRegistrationDetailRequest = Message<"hami.core.admin.v1.GetBankAccountRegistrationDetailRequest"> & {
  /**
   * @generated from field: int32 bank_account_registration_id = 1;
   */
  bankAccountRegistrationId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetBankAccountRegistrationDetailRequest.
 * Use `create(GetBankAccountRegistrationDetailRequestSchema)` to create a new message.
 */
export const GetBankAccountRegistrationDetailRequestSchema: GenMessage<GetBankAccountRegistrationDetailRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 7);

/**
 * 口座登録詳細取得レスポンス
 *
 * @generated from message hami.core.admin.v1.GetBankAccountRegistrationDetailResponse
 */
export type GetBankAccountRegistrationDetailResponse = Message<"hami.core.admin.v1.GetBankAccountRegistrationDetailResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.BankAccountApprovalDetail detail = 1;
   */
  detail?: BankAccountApprovalDetail;
};

/**
 * Describes the message hami.core.admin.v1.GetBankAccountRegistrationDetailResponse.
 * Use `create(GetBankAccountRegistrationDetailResponseSchema)` to create a new message.
 */
export const GetBankAccountRegistrationDetailResponseSchema: GenMessage<GetBankAccountRegistrationDetailResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 8);

/**
 * 口座登録承認リクエスト
 *
 * @generated from message hami.core.admin.v1.ApproveBankAccountRegistrationRequest
 */
export type ApproveBankAccountRegistrationRequest = Message<"hami.core.admin.v1.ApproveBankAccountRegistrationRequest"> & {
  /**
   * @generated from field: int32 bank_account_registration_id = 1;
   */
  bankAccountRegistrationId: number;

  /**
   * 承認コメント
   *
   * @generated from field: string comment = 2;
   */
  comment: string;
};

/**
 * Describes the message hami.core.admin.v1.ApproveBankAccountRegistrationRequest.
 * Use `create(ApproveBankAccountRegistrationRequestSchema)` to create a new message.
 */
export const ApproveBankAccountRegistrationRequestSchema: GenMessage<ApproveBankAccountRegistrationRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 9);

/**
 * 口座登録承認レスポンス
 *
 * @generated from message hami.core.admin.v1.ApproveBankAccountRegistrationResponse
 */
export type ApproveBankAccountRegistrationResponse = Message<"hami.core.admin.v1.ApproveBankAccountRegistrationResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.ApproveBankAccountRegistrationResponse.
 * Use `create(ApproveBankAccountRegistrationResponseSchema)` to create a new message.
 */
export const ApproveBankAccountRegistrationResponseSchema: GenMessage<ApproveBankAccountRegistrationResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 10);

/**
 * 口座登録却下リクエスト
 *
 * @generated from message hami.core.admin.v1.RejectBankAccountRegistrationRequest
 */
export type RejectBankAccountRegistrationRequest = Message<"hami.core.admin.v1.RejectBankAccountRegistrationRequest"> & {
  /**
   * @generated from field: int32 bank_account_registration_id = 1;
   */
  bankAccountRegistrationId: number;

  /**
   * 却下理由（必須）
   *
   * @generated from field: string rejection_reason = 2;
   */
  rejectionReason: string;
};

/**
 * Describes the message hami.core.admin.v1.RejectBankAccountRegistrationRequest.
 * Use `create(RejectBankAccountRegistrationRequestSchema)` to create a new message.
 */
export const RejectBankAccountRegistrationRequestSchema: GenMessage<RejectBankAccountRegistrationRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 11);

/**
 * 口座登録却下レスポンス
 *
 * @generated from message hami.core.admin.v1.RejectBankAccountRegistrationResponse
 */
export type RejectBankAccountRegistrationResponse = Message<"hami.core.admin.v1.RejectBankAccountRegistrationResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.RejectBankAccountRegistrationResponse.
 * Use `create(RejectBankAccountRegistrationResponseSchema)` to create a new message.
 */
export const RejectBankAccountRegistrationResponseSchema: GenMessage<RejectBankAccountRegistrationResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_approval_service, 12);

/**
 * 口座登録承認状態
 *
 * @generated from enum hami.core.admin.v1.BankAccountApprovalStatus
 */
export enum BankAccountApprovalStatus {
  /**
   * @generated from enum value: BANK_ACCOUNT_APPROVAL_STATUS_UNKNOWN = 0;
   */
  BANK_ACCOUNT_APPROVAL_STATUS_UNKNOWN = 0,

  /**
   * 承認待ち
   *
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,

  /**
   * 承認済み
   *
   * @generated from enum value: APPROVED = 2;
   */
  APPROVED = 2,

  /**
   * 却下済み
   *
   * @generated from enum value: REJECTED = 3;
   */
  REJECTED = 3,
}

/**
 * Describes the enum hami.core.admin.v1.BankAccountApprovalStatus.
 */
export const BankAccountApprovalStatusSchema: GenEnum<BankAccountApprovalStatus> = /*@__PURE__*/
  enumDesc(file_bank_account_approval_service, 0);

/**
 * 口座登録承認管理サービス
 *
 * @generated from service hami.core.admin.v1.BankAccountApprovalService
 */
export const BankAccountApprovalService: GenService<{
  /**
   * 承認待ち口座一覧取得
   *
   * @generated from rpc hami.core.admin.v1.BankAccountApprovalService.ListPendingBankAccountRegistrations
   */
  listPendingBankAccountRegistrations: {
    methodKind: "unary";
    input: typeof ListPendingBankAccountRegistrationsRequestSchema;
    output: typeof ListPendingBankAccountRegistrationsResponseSchema;
  },
  /**
   * 全口座登録一覧取得（承認済み・却下済み含む）
   *
   * @generated from rpc hami.core.admin.v1.BankAccountApprovalService.ListAllBankAccountRegistrations
   */
  listAllBankAccountRegistrations: {
    methodKind: "unary";
    input: typeof ListAllBankAccountRegistrationsRequestSchema;
    output: typeof ListAllBankAccountRegistrationsResponseSchema;
  },
  /**
   * 口座登録詳細取得（承認履歴含む）
   *
   * @generated from rpc hami.core.admin.v1.BankAccountApprovalService.GetBankAccountRegistrationDetail
   */
  getBankAccountRegistrationDetail: {
    methodKind: "unary";
    input: typeof GetBankAccountRegistrationDetailRequestSchema;
    output: typeof GetBankAccountRegistrationDetailResponseSchema;
  },
  /**
   * 口座登録承認
   *
   * @generated from rpc hami.core.admin.v1.BankAccountApprovalService.ApproveBankAccountRegistration
   */
  approveBankAccountRegistration: {
    methodKind: "unary";
    input: typeof ApproveBankAccountRegistrationRequestSchema;
    output: typeof ApproveBankAccountRegistrationResponseSchema;
  },
  /**
   * 口座登録却下
   *
   * @generated from rpc hami.core.admin.v1.BankAccountApprovalService.RejectBankAccountRegistration
   */
  rejectBankAccountRegistration: {
    methodKind: "unary";
    input: typeof RejectBankAccountRegistrationRequestSchema;
    output: typeof RejectBankAccountRegistrationResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_bank_account_approval_service, 0);

