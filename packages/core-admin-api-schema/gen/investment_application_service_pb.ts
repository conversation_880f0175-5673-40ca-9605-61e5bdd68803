// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file investment_application_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file investment_application_service.proto.
 */
export const file_investment_application_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * 出資申込馬一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListInvestmentApplicationHorsesRequest
 */
export type ListInvestmentApplicationHorsesRequest = Message<"hami.core.admin.v1.ListInvestmentApplicationHorsesRequest"> & {
};

/**
 * Describes the message hami.core.admin.v1.ListInvestmentApplicationHorsesRequest.
 * Use `create(ListInvestmentApplicationHorsesRequestSchema)` to create a new message.
 */
export const ListInvestmentApplicationHorsesRequestSchema: GenMessage<ListInvestmentApplicationHorsesRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 0);

/**
 * 出資申込馬アイテム
 *
 * @generated from message hami.core.admin.v1.InvestmentApplicationHorseItem
 */
export type InvestmentApplicationHorseItem = Message<"hami.core.admin.v1.InvestmentApplicationHorseItem"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 recruitment_year = 2;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 3;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string horse_name = 4;
   */
  horseName: string;

  /**
   * @generated from field: string recruitment_name = 5;
   */
  recruitmentName: string;

  /**
   * 合計申込口数
   *
   * @generated from field: int32 total_application_shares = 6;
   */
  totalApplicationShares: number;

  /**
   * 募集口数
   *
   * @generated from field: int32 recruitment_shares = 7;
   */
  recruitmentShares: number;

  /**
   * 契約締結済口数
   *
   * @generated from field: int32 contracted_shares = 8;
   */
  contractedShares: number;
};

/**
 * Describes the message hami.core.admin.v1.InvestmentApplicationHorseItem.
 * Use `create(InvestmentApplicationHorseItemSchema)` to create a new message.
 */
export const InvestmentApplicationHorseItemSchema: GenMessage<InvestmentApplicationHorseItem> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 1);

/**
 * 出資申込馬一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListInvestmentApplicationHorsesResponse
 */
export type ListInvestmentApplicationHorsesResponse = Message<"hami.core.admin.v1.ListInvestmentApplicationHorsesResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.InvestmentApplicationHorseItem horses = 1;
   */
  horses: InvestmentApplicationHorseItem[];
};

/**
 * Describes the message hami.core.admin.v1.ListInvestmentApplicationHorsesResponse.
 * Use `create(ListInvestmentApplicationHorsesResponseSchema)` to create a new message.
 */
export const ListInvestmentApplicationHorsesResponseSchema: GenMessage<ListInvestmentApplicationHorsesResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 2);

/**
 * 出資申込馬詳細取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetInvestmentApplicationHorseDetailRequest
 */
export type GetInvestmentApplicationHorseDetailRequest = Message<"hami.core.admin.v1.GetInvestmentApplicationHorseDetailRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetInvestmentApplicationHorseDetailRequest.
 * Use `create(GetInvestmentApplicationHorseDetailRequestSchema)` to create a new message.
 */
export const GetInvestmentApplicationHorseDetailRequestSchema: GenMessage<GetInvestmentApplicationHorseDetailRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 3);

/**
 * 出資申込馬詳細取得レスポンス
 *
 * @generated from message hami.core.admin.v1.GetInvestmentApplicationHorseDetailResponse
 */
export type GetInvestmentApplicationHorseDetailResponse = Message<"hami.core.admin.v1.GetInvestmentApplicationHorseDetailResponse"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 birth_year = 2;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 3;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 4;
   */
  birthDay: number;

  /**
   * @generated from field: int32 recruitment_year = 5;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 6;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string horse_name = 7;
   */
  horseName: string;

  /**
   * @generated from field: string recruitment_name = 8;
   */
  recruitmentName: string;

  /**
   * 合計申込口数
   *
   * @generated from field: int32 total_application_shares = 9;
   */
  totalApplicationShares: number;

  /**
   * 募集口数
   *
   * @generated from field: int32 recruitment_shares = 10;
   */
  recruitmentShares: number;

  /**
   * 契約締結済口数
   *
   * @generated from field: int32 contracted_shares = 11;
   */
  contractedShares: number;

  /**
   * 募集総額
   *
   * @generated from field: int32 recruitment_total_amount = 12;
   */
  recruitmentTotalAmount: number;
};

/**
 * Describes the message hami.core.admin.v1.GetInvestmentApplicationHorseDetailResponse.
 * Use `create(GetInvestmentApplicationHorseDetailResponseSchema)` to create a new message.
 */
export const GetInvestmentApplicationHorseDetailResponseSchema: GenMessage<GetInvestmentApplicationHorseDetailResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 4);

/**
 * 馬ごと出資申込一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListInvestmentApplicationsByHorseRequest
 */
export type ListInvestmentApplicationsByHorseRequest = Message<"hami.core.admin.v1.ListInvestmentApplicationsByHorseRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: optional bool is_whole = 2;
   */
  isWhole?: boolean;

  /**
   * 並べ替え項目
   *
   * @generated from field: optional hami.core.admin.v1.InvestmentApplicationSortField sort_field = 3;
   */
  sortField?: InvestmentApplicationSortField;

  /**
   * 並べ替え順
   *
   * @generated from field: optional hami.core.admin.v1.SortOrder sort_order = 4;
   */
  sortOrder?: SortOrder;
};

/**
 * Describes the message hami.core.admin.v1.ListInvestmentApplicationsByHorseRequest.
 * Use `create(ListInvestmentApplicationsByHorseRequestSchema)` to create a new message.
 */
export const ListInvestmentApplicationsByHorseRequestSchema: GenMessage<ListInvestmentApplicationsByHorseRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 5);

/**
 * 出資申込アイテム
 *
 * @generated from message hami.core.admin.v1.InvestmentApplicationItem
 */
export type InvestmentApplicationItem = Message<"hami.core.admin.v1.InvestmentApplicationItem"> & {
  /**
   * @generated from field: int32 investment_application_id = 1;
   */
  investmentApplicationId: number;

  /**
   * 申込日時
   *
   * @generated from field: google.protobuf.Timestamp application_date = 2;
   */
  applicationDate?: Timestamp;

  /**
   * 会員名
   *
   * @generated from field: string member_name = 3;
   */
  memberName: string;

  /**
   * 会員ID
   *
   * @generated from field: int32 member_id = 4;
   */
  memberId: number;

  /**
   * 会員番号
   *
   * @generated from field: int32 member_number = 5;
   */
  memberNumber: number;

  /**
   * 申込口数
   *
   * @generated from field: int32 requested_shares = 6;
   */
  requestedShares: number;

  /**
   * 希望口数に満たない場合に出資を希望しないフラグ
   *
   * @generated from field: bool reject_partial_allocation = 7;
   */
  rejectPartialAllocation: boolean;

  /**
   * 出資受入口数
   *
   * @generated from field: optional int32 allocated_shares = 8;
   */
  allocatedShares?: number;

  /**
   * 全口出資フラグ
   *
   * @generated from field: bool is_whole = 9;
   */
  isWhole: boolean;

  /**
   * 会員名（カナ）
   *
   * @generated from field: string member_name_kana = 10;
   */
  memberNameKana: string;
};

/**
 * Describes the message hami.core.admin.v1.InvestmentApplicationItem.
 * Use `create(InvestmentApplicationItemSchema)` to create a new message.
 */
export const InvestmentApplicationItemSchema: GenMessage<InvestmentApplicationItem> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 6);

/**
 * 馬ごと出資申込一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListInvestmentApplicationsByHorseResponse
 */
export type ListInvestmentApplicationsByHorseResponse = Message<"hami.core.admin.v1.ListInvestmentApplicationsByHorseResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.InvestmentApplicationItem applications = 1;
   */
  applications: InvestmentApplicationItem[];
};

/**
 * Describes the message hami.core.admin.v1.ListInvestmentApplicationsByHorseResponse.
 * Use `create(ListInvestmentApplicationsByHorseResponseSchema)` to create a new message.
 */
export const ListInvestmentApplicationsByHorseResponseSchema: GenMessage<ListInvestmentApplicationsByHorseResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 7);

/**
 * 出資受入実施リクエスト
 *
 * @generated from message hami.core.admin.v1.AcceptInvestmentApplicationsRequest
 */
export type AcceptInvestmentApplicationsRequest = Message<"hami.core.admin.v1.AcceptInvestmentApplicationsRequest"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.AcceptInvestmentApplicationItem acceptances = 1;
   */
  acceptances: AcceptInvestmentApplicationItem[];
};

/**
 * Describes the message hami.core.admin.v1.AcceptInvestmentApplicationsRequest.
 * Use `create(AcceptInvestmentApplicationsRequestSchema)` to create a new message.
 */
export const AcceptInvestmentApplicationsRequestSchema: GenMessage<AcceptInvestmentApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 8);

/**
 * 出資受入実施アイテム
 *
 * @generated from message hami.core.admin.v1.AcceptInvestmentApplicationItem
 */
export type AcceptInvestmentApplicationItem = Message<"hami.core.admin.v1.AcceptInvestmentApplicationItem"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 investment_application_id = 2;
   */
  investmentApplicationId: number;

  /**
   * 出資受入口数
   *
   * @generated from field: int32 allocated_shares = 3;
   */
  allocatedShares: number;
};

/**
 * Describes the message hami.core.admin.v1.AcceptInvestmentApplicationItem.
 * Use `create(AcceptInvestmentApplicationItemSchema)` to create a new message.
 */
export const AcceptInvestmentApplicationItemSchema: GenMessage<AcceptInvestmentApplicationItem> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 9);

/**
 * 出資受入実施レスポンス
 *
 * @generated from message hami.core.admin.v1.AcceptInvestmentApplicationsResponse
 */
export type AcceptInvestmentApplicationsResponse = Message<"hami.core.admin.v1.AcceptInvestmentApplicationsResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.AcceptInvestmentApplicationsResponse.
 * Use `create(AcceptInvestmentApplicationsResponseSchema)` to create a new message.
 */
export const AcceptInvestmentApplicationsResponseSchema: GenMessage<AcceptInvestmentApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 10);

/**
 * 出資申込契約締結対象一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListContractTargetApplicationsRequest
 */
export type ListContractTargetApplicationsRequest = Message<"hami.core.admin.v1.ListContractTargetApplicationsRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message hami.core.admin.v1.ListContractTargetApplicationsRequest.
 * Use `create(ListContractTargetApplicationsRequestSchema)` to create a new message.
 */
export const ListContractTargetApplicationsRequestSchema: GenMessage<ListContractTargetApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 11);

/**
 * 契約締結対象申込アイテム
 *
 * @generated from message hami.core.admin.v1.ContractTargetApplicationItem
 */
export type ContractTargetApplicationItem = Message<"hami.core.admin.v1.ContractTargetApplicationItem"> & {
  /**
   * @generated from field: int32 investment_application_id = 1;
   */
  investmentApplicationId: number;

  /**
   * 会員名
   *
   * @generated from field: string member_name = 2;
   */
  memberName: string;

  /**
   * 申込口数
   *
   * @generated from field: int32 requested_shares = 3;
   */
  requestedShares: number;

  /**
   * 出資受入口数
   *
   * @generated from field: int32 allocated_number = 4;
   */
  allocatedNumber: number;
};

/**
 * Describes the message hami.core.admin.v1.ContractTargetApplicationItem.
 * Use `create(ContractTargetApplicationItemSchema)` to create a new message.
 */
export const ContractTargetApplicationItemSchema: GenMessage<ContractTargetApplicationItem> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 12);

/**
 * 出資申込契約締結対象一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListContractTargetApplicationsResponse
 */
export type ListContractTargetApplicationsResponse = Message<"hami.core.admin.v1.ListContractTargetApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.ContractTargetApplicationItem applications = 1;
   */
  applications: ContractTargetApplicationItem[];
};

/**
 * Describes the message hami.core.admin.v1.ListContractTargetApplicationsResponse.
 * Use `create(ListContractTargetApplicationsResponseSchema)` to create a new message.
 */
export const ListContractTargetApplicationsResponseSchema: GenMessage<ListContractTargetApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 13);

/**
 * 出資申込契約締結実施リクエスト
 *
 * @generated from message hami.core.admin.v1.CompleteInvestmentContractsRequest
 */
export type CompleteInvestmentContractsRequest = Message<"hami.core.admin.v1.CompleteInvestmentContractsRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: repeated int32 investment_application_ids = 2;
   */
  investmentApplicationIds: number[];
};

/**
 * Describes the message hami.core.admin.v1.CompleteInvestmentContractsRequest.
 * Use `create(CompleteInvestmentContractsRequestSchema)` to create a new message.
 */
export const CompleteInvestmentContractsRequestSchema: GenMessage<CompleteInvestmentContractsRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 14);

/**
 * 出資申込契約締結実施レスポンス
 *
 * @generated from message hami.core.admin.v1.CompleteInvestmentContractsResponse
 */
export type CompleteInvestmentContractsResponse = Message<"hami.core.admin.v1.CompleteInvestmentContractsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.ContractItem contracts = 1;
   */
  contracts: ContractItem[];
};

/**
 * Describes the message hami.core.admin.v1.CompleteInvestmentContractsResponse.
 * Use `create(CompleteInvestmentContractsResponseSchema)` to create a new message.
 */
export const CompleteInvestmentContractsResponseSchema: GenMessage<CompleteInvestmentContractsResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 15);

/**
 * 契約アイテム
 *
 * @generated from message hami.core.admin.v1.ContractItem
 */
export type ContractItem = Message<"hami.core.admin.v1.ContractItem"> & {
  /**
   * @generated from field: int32 investment_application_id = 1;
   */
  investmentApplicationId: number;

  /**
   * 会員番号
   *
   * @generated from field: int32 member_number = 2;
   */
  memberNumber: number;

  /**
   * 会員名
   *
   * @generated from field: string member_name = 3;
   */
  memberName: string;

  /**
   * 契約口数
   *
   * @generated from field: int32 contracted_shares = 4;
   */
  contractedShares: number;
};

/**
 * Describes the message hami.core.admin.v1.ContractItem.
 * Use `create(ContractItemSchema)` to create a new message.
 */
export const ContractItemSchema: GenMessage<ContractItem> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 16);

/**
 * 会員サマリ
 *
 * @generated from message hami.core.admin.v1.MemberSummary
 */
export type MemberSummary = Message<"hami.core.admin.v1.MemberSummary"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;

  /**
   * @generated from field: int32 member_number = 2;
   */
  memberNumber: number;

  /**
   * @generated from field: string member_name = 3;
   */
  memberName: string;

  /**
   * @generated from field: string member_name_kana = 4;
   */
  memberNameKana: string;
};

/**
 * Describes the message hami.core.admin.v1.MemberSummary.
 * Use `create(MemberSummarySchema)` to create a new message.
 */
export const MemberSummarySchema: GenMessage<MemberSummary> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 17);

/**
 * 馬申込詳細
 *
 * @generated from message hami.core.admin.v1.HorseApplicationDetail
 */
export type HorseApplicationDetail = Message<"hami.core.admin.v1.HorseApplicationDetail"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 recruitment_year = 2;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 3;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string horse_name = 4;
   */
  horseName: string;

  /**
   * @generated from field: string recruitment_name = 5;
   */
  recruitmentName: string;
};

/**
 * Describes the message hami.core.admin.v1.HorseApplicationDetail.
 * Use `create(HorseApplicationDetailSchema)` to create a new message.
 */
export const HorseApplicationDetailSchema: GenMessage<HorseApplicationDetail> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 18);

/**
 * 年度バンドル申込詳細
 *
 * @generated from message hami.core.admin.v1.AnnualBundleApplicationDetail
 */
export type AnnualBundleApplicationDetail = Message<"hami.core.admin.v1.AnnualBundleApplicationDetail"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;

  /**
   * @generated from field: string bundle_name = 2;
   */
  bundleName: string;

  /**
   * @generated from field: int32 fiscal_year = 3;
   */
  fiscalYear: number;

  /**
   * @generated from field: int32 shares = 4;
   */
  shares: number;
};

/**
 * Describes the message hami.core.admin.v1.AnnualBundleApplicationDetail.
 * Use `create(AnnualBundleApplicationDetailSchema)` to create a new message.
 */
export const AnnualBundleApplicationDetailSchema: GenMessage<AnnualBundleApplicationDetail> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 19);

/**
 * 申込一覧用サマリ
 *
 * @generated from message hami.core.admin.v1.InvestmentApplicationSummary
 */
export type InvestmentApplicationSummary = Message<"hami.core.admin.v1.InvestmentApplicationSummary"> & {
  /**
   * @generated from field: int32 investment_application_id = 1;
   */
  investmentApplicationId: number;

  /**
   * @generated from field: hami.core.admin.v1.InvestmentApplicationType type = 2;
   */
  type: InvestmentApplicationType;

  /**
   * @generated from field: google.protobuf.Timestamp application_date = 3;
   */
  applicationDate?: Timestamp;

  /**
   * @generated from field: hami.core.admin.v1.MemberSummary member = 4;
   */
  member?: MemberSummary;

  /**
   * @generated from field: int32 requested_shares = 5;
   */
  requestedShares: number;

  /**
   * @generated from field: optional int32 allocated_shares = 6;
   */
  allocatedShares?: number;

  /**
   * @generated from field: bool reject_partial_allocation = 7;
   */
  rejectPartialAllocation: boolean;

  /**
   * @generated from field: bool is_whole = 8;
   */
  isWhole: boolean;

  /**
   * @generated from field: bool installment_payment = 9;
   */
  installmentPayment: boolean;

  /**
   * @generated from field: hami.core.admin.v1.InvestmentApplicationStatus status = 10;
   */
  status: InvestmentApplicationStatus;

  /**
   * @generated from field: hami.core.admin.v1.HorseApplicationDetail horse_detail = 11;
   */
  horseDetail?: HorseApplicationDetail;

  /**
   * @generated from field: hami.core.admin.v1.AnnualBundleApplicationDetail annual_bundle_detail = 12;
   */
  annualBundleDetail?: AnnualBundleApplicationDetail;
};

/**
 * Describes the message hami.core.admin.v1.InvestmentApplicationSummary.
 * Use `create(InvestmentApplicationSummarySchema)` to create a new message.
 */
export const InvestmentApplicationSummarySchema: GenMessage<InvestmentApplicationSummary> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 20);

/**
 * 出資申込一覧取得リクエスト（馬・年度バンドル混在）
 *
 * @generated from message hami.core.admin.v1.ListInvestmentApplicationsRequest
 */
export type ListInvestmentApplicationsRequest = Message<"hami.core.admin.v1.ListInvestmentApplicationsRequest"> & {
  /**
   * @generated from field: optional hami.core.admin.v1.InvestmentApplicationType type_filter = 1;
   */
  typeFilter?: InvestmentApplicationType;

  /**
   * @generated from field: repeated int32 horse_ids = 2;
   */
  horseIds: number[];

  /**
   * @generated from field: repeated int32 annual_bundle_ids = 3;
   */
  annualBundleIds: number[];

  /**
   * @generated from field: optional hami.core.admin.v1.InvestmentApplicationSortField sort_field = 4;
   */
  sortField?: InvestmentApplicationSortField;

  /**
   * @generated from field: optional hami.core.admin.v1.SortOrder sort_order = 5;
   */
  sortOrder?: SortOrder;

  /**
   * @generated from field: optional bool is_whole = 6;
   */
  isWhole?: boolean;
};

/**
 * Describes the message hami.core.admin.v1.ListInvestmentApplicationsRequest.
 * Use `create(ListInvestmentApplicationsRequestSchema)` to create a new message.
 */
export const ListInvestmentApplicationsRequestSchema: GenMessage<ListInvestmentApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 21);

/**
 * 出資申込一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListInvestmentApplicationsResponse
 */
export type ListInvestmentApplicationsResponse = Message<"hami.core.admin.v1.ListInvestmentApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.InvestmentApplicationSummary applications = 1;
   */
  applications: InvestmentApplicationSummary[];
};

/**
 * Describes the message hami.core.admin.v1.ListInvestmentApplicationsResponse.
 * Use `create(ListInvestmentApplicationsResponseSchema)` to create a new message.
 */
export const ListInvestmentApplicationsResponseSchema: GenMessage<ListInvestmentApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 22);

/**
 * 年度バンドル出資申込一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListAnnualBundleInvestmentApplicationsRequest
 */
export type ListAnnualBundleInvestmentApplicationsRequest = Message<"hami.core.admin.v1.ListAnnualBundleInvestmentApplicationsRequest"> & {
  /**
   * @generated from field: repeated int32 annual_bundle_ids = 1;
   */
  annualBundleIds: number[];

  /**
   * @generated from field: optional bool is_whole = 2;
   */
  isWhole?: boolean;

  /**
   * @generated from field: optional hami.core.admin.v1.InvestmentApplicationSortField sort_field = 3;
   */
  sortField?: InvestmentApplicationSortField;

  /**
   * @generated from field: optional hami.core.admin.v1.SortOrder sort_order = 4;
   */
  sortOrder?: SortOrder;
};

/**
 * Describes the message hami.core.admin.v1.ListAnnualBundleInvestmentApplicationsRequest.
 * Use `create(ListAnnualBundleInvestmentApplicationsRequestSchema)` to create a new message.
 */
export const ListAnnualBundleInvestmentApplicationsRequestSchema: GenMessage<ListAnnualBundleInvestmentApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 23);

/**
 * 年度バンドル出資申込一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListAnnualBundleInvestmentApplicationsResponse
 */
export type ListAnnualBundleInvestmentApplicationsResponse = Message<"hami.core.admin.v1.ListAnnualBundleInvestmentApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.InvestmentApplicationSummary applications = 1;
   */
  applications: InvestmentApplicationSummary[];
};

/**
 * Describes the message hami.core.admin.v1.ListAnnualBundleInvestmentApplicationsResponse.
 * Use `create(ListAnnualBundleInvestmentApplicationsResponseSchema)` to create a new message.
 */
export const ListAnnualBundleInvestmentApplicationsResponseSchema: GenMessage<ListAnnualBundleInvestmentApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 24);

/**
 * 年度バンドル出資受入実施リクエスト
 *
 * @generated from message hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationsRequest
 */
export type AcceptAnnualBundleInvestmentApplicationsRequest = Message<"hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationsRequest"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationCommand commands = 1;
   */
  commands: AcceptAnnualBundleInvestmentApplicationCommand[];
};

/**
 * Describes the message hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationsRequest.
 * Use `create(AcceptAnnualBundleInvestmentApplicationsRequestSchema)` to create a new message.
 */
export const AcceptAnnualBundleInvestmentApplicationsRequestSchema: GenMessage<AcceptAnnualBundleInvestmentApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 25);

/**
 * 年度バンドル出資受入実施コマンド
 *
 * @generated from message hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationCommand
 */
export type AcceptAnnualBundleInvestmentApplicationCommand = Message<"hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationCommand"> & {
  /**
   * @generated from field: int32 investment_application_id = 1;
   */
  investmentApplicationId: number;

  /**
   * @generated from field: int32 annual_bundle_id = 2;
   */
  annualBundleId: number;

  /**
   * 出資受入口数
   *
   * @generated from field: int32 allocated_shares = 3;
   */
  allocatedShares: number;
};

/**
 * Describes the message hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationCommand.
 * Use `create(AcceptAnnualBundleInvestmentApplicationCommandSchema)` to create a new message.
 */
export const AcceptAnnualBundleInvestmentApplicationCommandSchema: GenMessage<AcceptAnnualBundleInvestmentApplicationCommand> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 26);

/**
 * 年度バンドル出資受入実施レスポンス
 *
 * @generated from message hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationsResponse
 */
export type AcceptAnnualBundleInvestmentApplicationsResponse = Message<"hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationResult results = 1;
   */
  results: AcceptAnnualBundleInvestmentApplicationResult[];
};

/**
 * Describes the message hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationsResponse.
 * Use `create(AcceptAnnualBundleInvestmentApplicationsResponseSchema)` to create a new message.
 */
export const AcceptAnnualBundleInvestmentApplicationsResponseSchema: GenMessage<AcceptAnnualBundleInvestmentApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 27);

/**
 * 年度バンドル出資受入実施結果
 *
 * @generated from message hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationResult
 */
export type AcceptAnnualBundleInvestmentApplicationResult = Message<"hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationResult"> & {
  /**
   * @generated from field: int32 investment_application_id = 1;
   */
  investmentApplicationId: number;

  /**
   * @generated from field: bool success = 2;
   */
  success: boolean;

  /**
   * @generated from field: optional string error_message = 3;
   */
  errorMessage?: string;

  /**
   * @generated from field: optional int32 allocated_shares = 4;
   */
  allocatedShares?: number;

  /**
   * @generated from field: optional hami.core.admin.v1.InvestmentApplicationStatus status = 5;
   */
  status?: InvestmentApplicationStatus;
};

/**
 * Describes the message hami.core.admin.v1.AcceptAnnualBundleInvestmentApplicationResult.
 * Use `create(AcceptAnnualBundleInvestmentApplicationResultSchema)` to create a new message.
 */
export const AcceptAnnualBundleInvestmentApplicationResultSchema: GenMessage<AcceptAnnualBundleInvestmentApplicationResult> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 28);

/**
 * 年度バンドル出資申込契約締結実施リクエスト
 *
 * @generated from message hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationsRequest
 */
export type CompleteAnnualBundleInvestmentApplicationsRequest = Message<"hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationsRequest"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationCommand commands = 1;
   */
  commands: CompleteAnnualBundleInvestmentApplicationCommand[];
};

/**
 * Describes the message hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationsRequest.
 * Use `create(CompleteAnnualBundleInvestmentApplicationsRequestSchema)` to create a new message.
 */
export const CompleteAnnualBundleInvestmentApplicationsRequestSchema: GenMessage<CompleteAnnualBundleInvestmentApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 29);

/**
 * 年度バンドル契約締結コマンド
 *
 * @generated from message hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationCommand
 */
export type CompleteAnnualBundleInvestmentApplicationCommand = Message<"hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationCommand"> & {
  /**
   * @generated from field: int32 investment_application_id = 1;
   */
  investmentApplicationId: number;

  /**
   * @generated from field: int32 annual_bundle_id = 2;
   */
  annualBundleId: number;
};

/**
 * Describes the message hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationCommand.
 * Use `create(CompleteAnnualBundleInvestmentApplicationCommandSchema)` to create a new message.
 */
export const CompleteAnnualBundleInvestmentApplicationCommandSchema: GenMessage<CompleteAnnualBundleInvestmentApplicationCommand> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 30);

/**
 * 年度バンドル出資申込契約締結実施レスポンス
 *
 * @generated from message hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationsResponse
 */
export type CompleteAnnualBundleInvestmentApplicationsResponse = Message<"hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationResult results = 1;
   */
  results: CompleteAnnualBundleInvestmentApplicationResult[];
};

/**
 * Describes the message hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationsResponse.
 * Use `create(CompleteAnnualBundleInvestmentApplicationsResponseSchema)` to create a new message.
 */
export const CompleteAnnualBundleInvestmentApplicationsResponseSchema: GenMessage<CompleteAnnualBundleInvestmentApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 31);

/**
 * 年度バンドル出資申込契約締結結果
 *
 * @generated from message hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationResult
 */
export type CompleteAnnualBundleInvestmentApplicationResult = Message<"hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationResult"> & {
  /**
   * @generated from field: int32 investment_application_id = 1;
   */
  investmentApplicationId: number;

  /**
   * @generated from field: bool success = 2;
   */
  success: boolean;

  /**
   * @generated from field: optional string error_message = 3;
   */
  errorMessage?: string;

  /**
   * @generated from field: optional int32 contracted_shares = 4;
   */
  contractedShares?: number;

  /**
   * @generated from field: optional string member_name = 5;
   */
  memberName?: string;

  /**
   * @generated from field: optional int32 member_number = 6;
   */
  memberNumber?: number;

  /**
   * @generated from field: optional hami.core.admin.v1.InvestmentApplicationStatus status = 7;
   */
  status?: InvestmentApplicationStatus;
};

/**
 * Describes the message hami.core.admin.v1.CompleteAnnualBundleInvestmentApplicationResult.
 * Use `create(CompleteAnnualBundleInvestmentApplicationResultSchema)` to create a new message.
 */
export const CompleteAnnualBundleInvestmentApplicationResultSchema: GenMessage<CompleteAnnualBundleInvestmentApplicationResult> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 32);

/**
 * 年度バンドル出資申込詳細取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetInvestmentApplicationAnnualBundleDetailRequest
 */
export type GetInvestmentApplicationAnnualBundleDetailRequest = Message<"hami.core.admin.v1.GetInvestmentApplicationAnnualBundleDetailRequest"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetInvestmentApplicationAnnualBundleDetailRequest.
 * Use `create(GetInvestmentApplicationAnnualBundleDetailRequestSchema)` to create a new message.
 */
export const GetInvestmentApplicationAnnualBundleDetailRequestSchema: GenMessage<GetInvestmentApplicationAnnualBundleDetailRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 33);

/**
 * 年度バンドル出資申込詳細取得レスポンス
 *
 * @generated from message hami.core.admin.v1.GetInvestmentApplicationAnnualBundleDetailResponse
 */
export type GetInvestmentApplicationAnnualBundleDetailResponse = Message<"hami.core.admin.v1.GetInvestmentApplicationAnnualBundleDetailResponse"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;

  /**
   * @generated from field: int32 fiscal_year = 2;
   */
  fiscalYear: number;

  /**
   * @generated from field: string bundle_name = 3;
   */
  bundleName: string;

  /**
   * 合計申込口数
   *
   * @generated from field: int32 contracted_shares = 4;
   */
  contractedShares: number;

  /**
   * 募集口数
   *
   * @generated from field: int32 recruitment_shares = 5;
   */
  recruitmentShares: number;

  /**
   * 募集総額
   *
   * @generated from field: int32 recruitment_total_amount = 6;
   */
  recruitmentTotalAmount: number;
};

/**
 * Describes the message hami.core.admin.v1.GetInvestmentApplicationAnnualBundleDetailResponse.
 * Use `create(GetInvestmentApplicationAnnualBundleDetailResponseSchema)` to create a new message.
 */
export const GetInvestmentApplicationAnnualBundleDetailResponseSchema: GenMessage<GetInvestmentApplicationAnnualBundleDetailResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 34);

/**
 * 並べ替え項目
 *
 * @generated from enum hami.core.admin.v1.InvestmentApplicationSortField
 */
export enum InvestmentApplicationSortField {
  /**
   * @generated from enum value: SORT_FIELD_UNSPECIFIED = 0;
   */
  SORT_FIELD_UNSPECIFIED = 0,

  /**
   * 申込日時
   *
   * @generated from enum value: SORT_FIELD_APPLIED_AT = 1;
   */
  SORT_FIELD_APPLIED_AT = 1,

  /**
   * 申込者名（カナ）
   *
   * @generated from enum value: SORT_FIELD_MEMBER_NAME_KANA = 2;
   */
  SORT_FIELD_MEMBER_NAME_KANA = 2,
}

/**
 * Describes the enum hami.core.admin.v1.InvestmentApplicationSortField.
 */
export const InvestmentApplicationSortFieldSchema: GenEnum<InvestmentApplicationSortField> = /*@__PURE__*/
  enumDesc(file_investment_application_service, 0);

/**
 * 申込種別
 *
 * @generated from enum hami.core.admin.v1.InvestmentApplicationType
 */
export enum InvestmentApplicationType {
  /**
   * @generated from enum value: INVESTMENT_APPLICATION_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 募集馬
   *
   * @generated from enum value: INVESTMENT_APPLICATION_TYPE_HORSE = 1;
   */
  HORSE = 1,

  /**
   * 年度バンドル
   *
   * @generated from enum value: INVESTMENT_APPLICATION_TYPE_ANNUAL_BUNDLE = 2;
   */
  ANNUAL_BUNDLE = 2,
}

/**
 * Describes the enum hami.core.admin.v1.InvestmentApplicationType.
 */
export const InvestmentApplicationTypeSchema: GenEnum<InvestmentApplicationType> = /*@__PURE__*/
  enumDesc(file_investment_application_service, 1);

/**
 * 申込ステータス
 *
 * @generated from enum hami.core.admin.v1.InvestmentApplicationStatus
 */
export enum InvestmentApplicationStatus {
  /**
   * @generated from enum value: INVESTMENT_APPLICATION_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 申込済（受入前）
   *
   * @generated from enum value: INVESTMENT_APPLICATION_STATUS_APPLIED = 1;
   */
  APPLIED = 1,

  /**
   * 受入済（契約前）
   *
   * @generated from enum value: INVESTMENT_APPLICATION_STATUS_ACCEPTED = 2;
   */
  ACCEPTED = 2,

  /**
   * 契約締結済
   *
   * @generated from enum value: INVESTMENT_APPLICATION_STATUS_CONTRACT_COMPLETED = 3;
   */
  CONTRACT_COMPLETED = 3,

  /**
   * 受入不可
   *
   * @generated from enum value: INVESTMENT_APPLICATION_STATUS_REJECTED = 4;
   */
  REJECTED = 4,
}

/**
 * Describes the enum hami.core.admin.v1.InvestmentApplicationStatus.
 */
export const InvestmentApplicationStatusSchema: GenEnum<InvestmentApplicationStatus> = /*@__PURE__*/
  enumDesc(file_investment_application_service, 2);

/**
 * 並べ替え順
 *
 * @generated from enum hami.core.admin.v1.SortOrder
 */
export enum SortOrder {
  /**
   * @generated from enum value: SORT_ORDER_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 昇順
   *
   * @generated from enum value: SORT_ORDER_ASC = 1;
   */
  ASC = 1,

  /**
   * 降順
   *
   * @generated from enum value: SORT_ORDER_DESC = 2;
   */
  DESC = 2,
}

/**
 * Describes the enum hami.core.admin.v1.SortOrder.
 */
export const SortOrderSchema: GenEnum<SortOrder> = /*@__PURE__*/
  enumDesc(file_investment_application_service, 3);

/**
 * 出資申込サービス
 *
 * @generated from service hami.core.admin.v1.InvestmentApplicationService
 */
export const InvestmentApplicationService: GenService<{
  /**
   * 出資申込馬一覧取得
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.ListInvestmentApplicationHorses
   */
  listInvestmentApplicationHorses: {
    methodKind: "unary";
    input: typeof ListInvestmentApplicationHorsesRequestSchema;
    output: typeof ListInvestmentApplicationHorsesResponseSchema;
  },
  /**
   * 出資申込馬詳細取得
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.GetInvestmentApplicationHorseDetail
   */
  getInvestmentApplicationHorseDetail: {
    methodKind: "unary";
    input: typeof GetInvestmentApplicationHorseDetailRequestSchema;
    output: typeof GetInvestmentApplicationHorseDetailResponseSchema;
  },
  /**
   * 馬ごと出資申込一覧取得
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.ListInvestmentApplicationsByHorse
   */
  listInvestmentApplicationsByHorse: {
    methodKind: "unary";
    input: typeof ListInvestmentApplicationsByHorseRequestSchema;
    output: typeof ListInvestmentApplicationsByHorseResponseSchema;
  },
  /**
   * 出資受入実施
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.AcceptInvestmentApplications
   */
  acceptInvestmentApplications: {
    methodKind: "unary";
    input: typeof AcceptInvestmentApplicationsRequestSchema;
    output: typeof AcceptInvestmentApplicationsResponseSchema;
  },
  /**
   * 出資申込契約締結対象一覧取得
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.ListContractTargetApplications
   */
  listContractTargetApplications: {
    methodKind: "unary";
    input: typeof ListContractTargetApplicationsRequestSchema;
    output: typeof ListContractTargetApplicationsResponseSchema;
  },
  /**
   * 出資申込契約締結実施
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.CompleteInvestmentContracts
   */
  completeInvestmentContracts: {
    methodKind: "unary";
    input: typeof CompleteInvestmentContractsRequestSchema;
    output: typeof CompleteInvestmentContractsResponseSchema;
  },
  /**
   * 出資申込一覧取得（馬・年度バンドル混在）
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.ListInvestmentApplications
   */
  listInvestmentApplications: {
    methodKind: "unary";
    input: typeof ListInvestmentApplicationsRequestSchema;
    output: typeof ListInvestmentApplicationsResponseSchema;
  },
  /**
   * 年度バンドル出資申込一覧取得
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.ListAnnualBundleInvestmentApplications
   */
  listAnnualBundleInvestmentApplications: {
    methodKind: "unary";
    input: typeof ListAnnualBundleInvestmentApplicationsRequestSchema;
    output: typeof ListAnnualBundleInvestmentApplicationsResponseSchema;
  },
  /**
   * 年度バンドル出資申込詳細取得
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.GetInvestmentApplicationAnnualBundleDetail
   */
  getInvestmentApplicationAnnualBundleDetail: {
    methodKind: "unary";
    input: typeof GetInvestmentApplicationAnnualBundleDetailRequestSchema;
    output: typeof GetInvestmentApplicationAnnualBundleDetailResponseSchema;
  },
  /**
   * 年度バンドル出資受入実施
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.AcceptAnnualBundleInvestmentApplications
   */
  acceptAnnualBundleInvestmentApplications: {
    methodKind: "unary";
    input: typeof AcceptAnnualBundleInvestmentApplicationsRequestSchema;
    output: typeof AcceptAnnualBundleInvestmentApplicationsResponseSchema;
  },
  /**
   * 年度バンドル出資申込契約締結実施
   *
   * @generated from rpc hami.core.admin.v1.InvestmentApplicationService.CompleteAnnualBundleInvestmentApplications
   */
  completeAnnualBundleInvestmentApplications: {
    methodKind: "unary";
    input: typeof CompleteAnnualBundleInvestmentApplicationsRequestSchema;
    output: typeof CompleteAnnualBundleInvestmentApplicationsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_investment_application_service, 0);

