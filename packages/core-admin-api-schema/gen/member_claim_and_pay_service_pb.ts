// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file member_claim_and_pay_service.proto (package core.admin.member_claim_and_pay, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file member_claim_and_pay_service.proto.
 */
export const file_member_claim_and_pay_service: GenFile = /*@__PURE__*/
  fileDesc("CiJtZW1iZXJfY2xhaW1fYW5kX3BheV9zZXJ2aWNlLnByb3RvEh9jb3JlLmFkbWluLm1lbWJlcl9jbGFpbV9hbmRfcGF5IvsBChVNZW1iZXJDbGFpbUFuZFBheUl0ZW0SHwoXbWVtYmVyX2NsYWltX2FuZF9wYXlfaWQYASABKAUSEQoJbWVtYmVyX2lkGAIgASgFEhUKDW1lbWJlcl9udW1iZXIYAyABKAkSEQoJbGFzdF9uYW1lGAQgASgJEhIKCmZpcnN0X25hbWUYBSABKAkSFgoObGFzdF9uYW1lX2thbmEYBiABKAkSFwoPZmlyc3RfbmFtZV9rYW5hGAcgASgJEhUKDW9jY3VycmVkX2RhdGUYCCABKAkSFAoMY2xhaW1fYW1vdW50GAkgASgFEhIKCnBheV9hbW91bnQYCiABKAUiSQodTGlzdE1lbWJlckNsYWltQW5kUGF5c1JlcXVlc3QSDAoEeWVhchgBIAEoBRINCgVtb250aBgCIAEoBRILCgNkYXkYAyABKAUidwoeTGlzdE1lbWJlckNsYWltQW5kUGF5c1Jlc3BvbnNlElUKFW1lbWJlcl9jbGFpbV9hbmRfcGF5cxgBIAMoCzI2LmNvcmUuYWRtaW4ubWVtYmVyX2NsYWltX2FuZF9wYXkuTWVtYmVyQ2xhaW1BbmRQYXlJdGVtInsKJUdlbmVyYXRlSW52ZXN0bWVudEFuZFJldHVyblBkZlJlcXVlc3QSGgoSb2NjdXJyZWRfZGF0ZV95ZWFyGAEgASgFEhsKE29jY3VycmVkX2RhdGVfbW9udGgYAiABKAUSGQoRb2NjdXJyZWRfZGF0ZV9kYXkYAyABKAUiKAomR2VuZXJhdGVJbnZlc3RtZW50QW5kUmV0dXJuUGRmUmVzcG9uc2Uy7gIKGE1lbWJlckNsYWltQW5kUGF5U2VydmljZRKbAQoWTGlzdE1lbWJlckNsYWltQW5kUGF5cxI+LmNvcmUuYWRtaW4ubWVtYmVyX2NsYWltX2FuZF9wYXkuTGlzdE1lbWJlckNsYWltQW5kUGF5c1JlcXVlc3QaPy5jb3JlLmFkbWluLm1lbWJlcl9jbGFpbV9hbmRfcGF5Lkxpc3RNZW1iZXJDbGFpbUFuZFBheXNSZXNwb25zZSIAErMBCh5HZW5lcmF0ZUludmVzdG1lbnRBbmRSZXR1cm5QZGYSRi5jb3JlLmFkbWluLm1lbWJlcl9jbGFpbV9hbmRfcGF5LkdlbmVyYXRlSW52ZXN0bWVudEFuZFJldHVyblBkZlJlcXVlc3QaRy5jb3JlLmFkbWluLm1lbWJlcl9jbGFpbV9hbmRfcGF5LkdlbmVyYXRlSW52ZXN0bWVudEFuZFJldHVyblBkZlJlc3BvbnNlIgBiBnByb3RvMw");

/**
 * 会員請求・支払い情報
 *
 * @generated from message core.admin.member_claim_and_pay.MemberClaimAndPayItem
 */
export type MemberClaimAndPayItem = Message<"core.admin.member_claim_and_pay.MemberClaimAndPayItem"> & {
  /**
   * @generated from field: int32 member_claim_and_pay_id = 1;
   */
  memberClaimAndPayId: number;

  /**
   * @generated from field: int32 member_id = 2;
   */
  memberId: number;

  /**
   * @generated from field: string member_number = 3;
   */
  memberNumber: string;

  /**
   * @generated from field: string last_name = 4;
   */
  lastName: string;

  /**
   * @generated from field: string first_name = 5;
   */
  firstName: string;

  /**
   * @generated from field: string last_name_kana = 6;
   */
  lastNameKana: string;

  /**
   * @generated from field: string first_name_kana = 7;
   */
  firstNameKana: string;

  /**
   * YYYY-MM-DD形式
   *
   * @generated from field: string occurred_date = 8;
   */
  occurredDate: string;

  /**
   * @generated from field: int32 claim_amount = 9;
   */
  claimAmount: number;

  /**
   * @generated from field: int32 pay_amount = 10;
   */
  payAmount: number;
};

/**
 * Describes the message core.admin.member_claim_and_pay.MemberClaimAndPayItem.
 * Use `create(MemberClaimAndPayItemSchema)` to create a new message.
 */
export const MemberClaimAndPayItemSchema: GenMessage<MemberClaimAndPayItem> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 0);

/**
 * 会員請求・支払い一覧取得リクエスト（日付指定）
 *
 * @generated from message core.admin.member_claim_and_pay.ListMemberClaimAndPaysRequest
 */
export type ListMemberClaimAndPaysRequest = Message<"core.admin.member_claim_and_pay.ListMemberClaimAndPaysRequest"> & {
  /**
   * @generated from field: int32 year = 1;
   */
  year: number;

  /**
   * @generated from field: int32 month = 2;
   */
  month: number;

  /**
   * @generated from field: int32 day = 3;
   */
  day: number;
};

/**
 * Describes the message core.admin.member_claim_and_pay.ListMemberClaimAndPaysRequest.
 * Use `create(ListMemberClaimAndPaysRequestSchema)` to create a new message.
 */
export const ListMemberClaimAndPaysRequestSchema: GenMessage<ListMemberClaimAndPaysRequest> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 1);

/**
 * 会員請求・支払い一覧取得レスポンス
 *
 * @generated from message core.admin.member_claim_and_pay.ListMemberClaimAndPaysResponse
 */
export type ListMemberClaimAndPaysResponse = Message<"core.admin.member_claim_and_pay.ListMemberClaimAndPaysResponse"> & {
  /**
   * @generated from field: repeated core.admin.member_claim_and_pay.MemberClaimAndPayItem member_claim_and_pays = 1;
   */
  memberClaimAndPays: MemberClaimAndPayItem[];
};

/**
 * Describes the message core.admin.member_claim_and_pay.ListMemberClaimAndPaysResponse.
 * Use `create(ListMemberClaimAndPaysResponseSchema)` to create a new message.
 */
export const ListMemberClaimAndPaysResponseSchema: GenMessage<ListMemberClaimAndPaysResponse> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 2);

/**
 * 投資・分配PDF生成リクエスト
 *
 * @generated from message core.admin.member_claim_and_pay.GenerateInvestmentAndReturnPdfRequest
 */
export type GenerateInvestmentAndReturnPdfRequest = Message<"core.admin.member_claim_and_pay.GenerateInvestmentAndReturnPdfRequest"> & {
  /**
   * @generated from field: int32 occurred_date_year = 1;
   */
  occurredDateYear: number;

  /**
   * @generated from field: int32 occurred_date_month = 2;
   */
  occurredDateMonth: number;

  /**
   * @generated from field: int32 occurred_date_day = 3;
   */
  occurredDateDay: number;
};

/**
 * Describes the message core.admin.member_claim_and_pay.GenerateInvestmentAndReturnPdfRequest.
 * Use `create(GenerateInvestmentAndReturnPdfRequestSchema)` to create a new message.
 */
export const GenerateInvestmentAndReturnPdfRequestSchema: GenMessage<GenerateInvestmentAndReturnPdfRequest> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 3);

/**
 * 投資・分配PDF生成レスポンス
 *
 * @generated from message core.admin.member_claim_and_pay.GenerateInvestmentAndReturnPdfResponse
 */
export type GenerateInvestmentAndReturnPdfResponse = Message<"core.admin.member_claim_and_pay.GenerateInvestmentAndReturnPdfResponse"> & {
};

/**
 * Describes the message core.admin.member_claim_and_pay.GenerateInvestmentAndReturnPdfResponse.
 * Use `create(GenerateInvestmentAndReturnPdfResponseSchema)` to create a new message.
 */
export const GenerateInvestmentAndReturnPdfResponseSchema: GenMessage<GenerateInvestmentAndReturnPdfResponse> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 4);

/**
 * 会員請求・支払いサービス
 *
 * @generated from service core.admin.member_claim_and_pay.MemberClaimAndPayService
 */
export const MemberClaimAndPayService: GenService<{
  /**
   * 会員請求・支払い一覧を取得（日付指定）
   *
   * @generated from rpc core.admin.member_claim_and_pay.MemberClaimAndPayService.ListMemberClaimAndPays
   */
  listMemberClaimAndPays: {
    methodKind: "unary";
    input: typeof ListMemberClaimAndPaysRequestSchema;
    output: typeof ListMemberClaimAndPaysResponseSchema;
  },
  /**
   * 投資・分配PDFを生成
   *
   * @generated from rpc core.admin.member_claim_and_pay.MemberClaimAndPayService.GenerateInvestmentAndReturnPdf
   */
  generateInvestmentAndReturnPdf: {
    methodKind: "unary";
    input: typeof GenerateInvestmentAndReturnPdfRequestSchema;
    output: typeof GenerateInvestmentAndReturnPdfResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_member_claim_and_pay_service, 0);

