// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file member_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { HorseGender } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file member_service.proto.
 */
export const file_member_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums]);

/**
 * @generated from message hami.core.admin.v1.Member
 */
export type Member = Message<"hami.core.admin.v1.Member"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;

  /**
   * @generated from field: int32 member_number = 2;
   */
  memberNumber: number;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: string first_name = 4;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 5;
   */
  lastName: string;

  /**
   * @generated from field: string first_name_kana = 6;
   */
  firstNameKana: string;

  /**
   * @generated from field: string last_name_kana = 7;
   */
  lastNameKana: string;

  /**
   * @generated from field: string postal_code = 8;
   */
  postalCode: string;

  /**
   * @generated from field: string prefecture = 9;
   */
  prefecture: string;

  /**
   * @generated from field: string address = 10;
   */
  address: string;

  /**
   * @generated from field: string apartment = 11;
   */
  apartment: string;

  /**
   * @generated from field: string phone_number = 12;
   */
  phoneNumber: string;

  /**
   * @generated from field: optional int32 birth_year = 13;
   */
  birthYear?: number;

  /**
   * @generated from field: optional int32 birth_month = 14;
   */
  birthMonth?: number;

  /**
   * @generated from field: optional int32 birth_day = 15;
   */
  birthDay?: number;

  /**
   * @generated from field: int64 approved_at = 27;
   */
  approvedAt: bigint;

  /**
   * @generated from field: string approved_by = 28;
   */
  approvedBy: string;

  /**
   * @generated from field: int32 membership_application_id = 29;
   */
  membershipApplicationId: number;

  /**
   * @generated from field: optional int32 retirementAt = 30;
   */
  retirementAt?: number;

  /**
   * @generated from field: int64 created_at = 31;
   */
  createdAt: bigint;

  /**
   * @generated from field: int64 updated_at = 32;
   */
  updatedAt: bigint;
};

/**
 * Describes the message hami.core.admin.v1.Member.
 * Use `create(MemberSchema)` to create a new message.
 */
export const MemberSchema: GenMessage<Member> = /*@__PURE__*/
  messageDesc(file_member_service, 0);

/**
 * @generated from message hami.core.admin.v1.ListMembersRequest
 */
export type ListMembersRequest = Message<"hami.core.admin.v1.ListMembersRequest"> & {
  /**
   * 検索用: 名
   *
   * @generated from field: optional string firstName = 1;
   */
  firstName?: string;

  /**
   * 検索用: 姓
   *
   * @generated from field: optional string lastName = 2;
   */
  lastName?: string;

  /**
   * 検索用: 会員番号
   *
   * @generated from field: optional int32 memberNumber = 3;
   */
  memberNumber?: number;

  /**
   * 退会者も含めるか
   *
   * @generated from field: optional bool includeRetired = 4;
   */
  includeRetired?: boolean;
};

/**
 * Describes the message hami.core.admin.v1.ListMembersRequest.
 * Use `create(ListMembersRequestSchema)` to create a new message.
 */
export const ListMembersRequestSchema: GenMessage<ListMembersRequest> = /*@__PURE__*/
  messageDesc(file_member_service, 1);

/**
 * @generated from message hami.core.admin.v1.ListMembersResponse
 */
export type ListMembersResponse = Message<"hami.core.admin.v1.ListMembersResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.Member members = 1;
   */
  members: Member[];
};

/**
 * Describes the message hami.core.admin.v1.ListMembersResponse.
 * Use `create(ListMembersResponseSchema)` to create a new message.
 */
export const ListMembersResponseSchema: GenMessage<ListMembersResponse> = /*@__PURE__*/
  messageDesc(file_member_service, 2);

/**
 * @generated from message hami.core.admin.v1.GetMemberRequest
 */
export type GetMemberRequest = Message<"hami.core.admin.v1.GetMemberRequest"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetMemberRequest.
 * Use `create(GetMemberRequestSchema)` to create a new message.
 */
export const GetMemberRequestSchema: GenMessage<GetMemberRequest> = /*@__PURE__*/
  messageDesc(file_member_service, 3);

/**
 * @generated from message hami.core.admin.v1.GetMemberResponse
 */
export type GetMemberResponse = Message<"hami.core.admin.v1.GetMemberResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.Member member = 1;
   */
  member?: Member;
};

/**
 * Describes the message hami.core.admin.v1.GetMemberResponse.
 * Use `create(GetMemberResponseSchema)` to create a new message.
 */
export const GetMemberResponseSchema: GenMessage<GetMemberResponse> = /*@__PURE__*/
  messageDesc(file_member_service, 4);

/**
 * @generated from message hami.core.admin.v1.UpdateMemberRequest
 */
export type UpdateMemberRequest = Message<"hami.core.admin.v1.UpdateMemberRequest"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;

  /**
   * @generated from field: optional string postal_code = 2;
   */
  postalCode?: string;

  /**
   * @generated from field: optional string prefecture = 3;
   */
  prefecture?: string;

  /**
   * @generated from field: optional string address = 4;
   */
  address?: string;

  /**
   * @generated from field: optional string apartment = 5;
   */
  apartment?: string;

  /**
   * @generated from field: optional string phone_number = 6;
   */
  phoneNumber?: string;
};

/**
 * Describes the message hami.core.admin.v1.UpdateMemberRequest.
 * Use `create(UpdateMemberRequestSchema)` to create a new message.
 */
export const UpdateMemberRequestSchema: GenMessage<UpdateMemberRequest> = /*@__PURE__*/
  messageDesc(file_member_service, 5);

/**
 * @generated from message hami.core.admin.v1.UpdateMemberResponse
 */
export type UpdateMemberResponse = Message<"hami.core.admin.v1.UpdateMemberResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.Member member = 1;
   */
  member?: Member;
};

/**
 * Describes the message hami.core.admin.v1.UpdateMemberResponse.
 * Use `create(UpdateMemberResponseSchema)` to create a new message.
 */
export const UpdateMemberResponseSchema: GenMessage<UpdateMemberResponse> = /*@__PURE__*/
  messageDesc(file_member_service, 6);

/**
 * 退会予定をキャンセルするリクエスト
 *
 * @generated from message hami.core.admin.v1.CancelRetirementRequest
 */
export type CancelRetirementRequest = Message<"hami.core.admin.v1.CancelRetirementRequest"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;
};

/**
 * Describes the message hami.core.admin.v1.CancelRetirementRequest.
 * Use `create(CancelRetirementRequestSchema)` to create a new message.
 */
export const CancelRetirementRequestSchema: GenMessage<CancelRetirementRequest> = /*@__PURE__*/
  messageDesc(file_member_service, 7);

/**
 * 退会予定をキャンセルするレスポンス
 *
 * @generated from message hami.core.admin.v1.CancelRetirementResponse
 */
export type CancelRetirementResponse = Message<"hami.core.admin.v1.CancelRetirementResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.CancelRetirementResponse.
 * Use `create(CancelRetirementResponseSchema)` to create a new message.
 */
export const CancelRetirementResponseSchema: GenMessage<CancelRetirementResponse> = /*@__PURE__*/
  messageDesc(file_member_service, 8);

/**
 * 会員の出資馬一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListMemberInvestmentHorsesRequest
 */
export type ListMemberInvestmentHorsesRequest = Message<"hami.core.admin.v1.ListMemberInvestmentHorsesRequest"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;
};

/**
 * Describes the message hami.core.admin.v1.ListMemberInvestmentHorsesRequest.
 * Use `create(ListMemberInvestmentHorsesRequestSchema)` to create a new message.
 */
export const ListMemberInvestmentHorsesRequestSchema: GenMessage<ListMemberInvestmentHorsesRequest> = /*@__PURE__*/
  messageDesc(file_member_service, 9);

/**
 * 会員の出資馬一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListMemberInvestmentHorsesResponse
 */
export type ListMemberInvestmentHorsesResponse = Message<"hami.core.admin.v1.ListMemberInvestmentHorsesResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.MemberInvestmentHorse horses = 1;
   */
  horses: MemberInvestmentHorse[];
};

/**
 * Describes the message hami.core.admin.v1.ListMemberInvestmentHorsesResponse.
 * Use `create(ListMemberInvestmentHorsesResponseSchema)` to create a new message.
 */
export const ListMemberInvestmentHorsesResponseSchema: GenMessage<ListMemberInvestmentHorsesResponse> = /*@__PURE__*/
  messageDesc(file_member_service, 10);

/**
 * 会員の出資馬情報
 *
 * @generated from message hami.core.admin.v1.MemberInvestmentHorse
 */
export type MemberInvestmentHorse = Message<"hami.core.admin.v1.MemberInvestmentHorse"> & {
  /**
   * 馬ID
   *
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * 生年
   *
   * @generated from field: int32 birth_year = 2;
   */
  birthYear: number;

  /**
   * 性別
   *
   * @generated from field: optional hami.core.admin.v1.HorseGender gender = 3;
   */
  gender?: HorseGender;

  /**
   * 馬名（募集馬名）
   *
   * @generated from field: string horse_name = 4;
   */
  horseName: string;

  /**
   * 出資口数
   *
   * @generated from field: int32 investment_shares = 5;
   */
  investmentShares: number;

  /**
   * 出資金額
   *
   * @generated from field: int32 investment_amount = 6;
   */
  investmentAmount: number;
};

/**
 * Describes the message hami.core.admin.v1.MemberInvestmentHorse.
 * Use `create(MemberInvestmentHorseSchema)` to create a new message.
 */
export const MemberInvestmentHorseSchema: GenMessage<MemberInvestmentHorse> = /*@__PURE__*/
  messageDesc(file_member_service, 11);

/**
 * @generated from service hami.core.admin.v1.MemberService
 */
export const MemberService: GenService<{
  /**
   * @generated from rpc hami.core.admin.v1.MemberService.ListMembers
   */
  listMembers: {
    methodKind: "unary";
    input: typeof ListMembersRequestSchema;
    output: typeof ListMembersResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.MemberService.GetMember
   */
  getMember: {
    methodKind: "unary";
    input: typeof GetMemberRequestSchema;
    output: typeof GetMemberResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.MemberService.UpdateMember
   */
  updateMember: {
    methodKind: "unary";
    input: typeof UpdateMemberRequestSchema;
    output: typeof UpdateMemberResponseSchema;
  },
  /**
   * 退会予定をキャンセル
   *
   * @generated from rpc hami.core.admin.v1.MemberService.CancelRetirement
   */
  cancelRetirement: {
    methodKind: "unary";
    input: typeof CancelRetirementRequestSchema;
    output: typeof CancelRetirementResponseSchema;
  },
  /**
   * 会員の出資馬一覧を取得
   *
   * @generated from rpc hami.core.admin.v1.MemberService.ListMemberInvestmentHorses
   */
  listMemberInvestmentHorses: {
    methodKind: "unary";
    input: typeof ListMemberInvestmentHorsesRequestSchema;
    output: typeof ListMemberInvestmentHorsesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_member_service, 0);

