// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file admin_user_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file admin_user_service.proto.
 */
export const file_admin_user_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * @generated from message hami.core.admin.v1.LoginRequest
 */
export type LoginRequest = Message<"hami.core.admin.v1.LoginRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;
};

/**
 * Describes the message hami.core.admin.v1.LoginRequest.
 * Use `create(LoginRequestSchema)` to create a new message.
 */
export const LoginRequestSchema: GenMessage<LoginRequest> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 0);

/**
 * @generated from message hami.core.admin.v1.LoginResponse
 */
export type LoginResponse = Message<"hami.core.admin.v1.LoginResponse"> & {
  /**
   * @generated from field: string session_token = 1;
   */
  sessionToken: string;
};

/**
 * Describes the message hami.core.admin.v1.LoginResponse.
 * Use `create(LoginResponseSchema)` to create a new message.
 */
export const LoginResponseSchema: GenMessage<LoginResponse> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 1);

/**
 * @generated from message hami.core.admin.v1.GetMeRequest
 */
export type GetMeRequest = Message<"hami.core.admin.v1.GetMeRequest"> & {
  /**
   * @generated from field: string session_token = 1;
   */
  sessionToken: string;
};

/**
 * Describes the message hami.core.admin.v1.GetMeRequest.
 * Use `create(GetMeRequestSchema)` to create a new message.
 */
export const GetMeRequestSchema: GenMessage<GetMeRequest> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 2);

/**
 * @generated from message hami.core.admin.v1.GetMeResponse
 */
export type GetMeResponse = Message<"hami.core.admin.v1.GetMeResponse"> & {
  /**
   * @generated from field: string admin_user_id = 1;
   */
  adminUserId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: google.protobuf.Timestamp last_login_at = 4;
   */
  lastLoginAt?: Timestamp;
};

/**
 * Describes the message hami.core.admin.v1.GetMeResponse.
 * Use `create(GetMeResponseSchema)` to create a new message.
 */
export const GetMeResponseSchema: GenMessage<GetMeResponse> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 3);

/**
 * @generated from message hami.core.admin.v1.AdminUser
 */
export type AdminUser = Message<"hami.core.admin.v1.AdminUser"> & {
  /**
   * @generated from field: string admin_user_id = 1;
   */
  adminUserId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 4;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 5;
   */
  updatedAt?: Timestamp;

  /**
   * @generated from field: optional google.protobuf.Timestamp last_login_at = 6;
   */
  lastLoginAt?: Timestamp;
};

/**
 * Describes the message hami.core.admin.v1.AdminUser.
 * Use `create(AdminUserSchema)` to create a new message.
 */
export const AdminUserSchema: GenMessage<AdminUser> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 4);

/**
 * @generated from message hami.core.admin.v1.ListAdminUsersRequest
 */
export type ListAdminUsersRequest = Message<"hami.core.admin.v1.ListAdminUsersRequest"> & {
};

/**
 * Describes the message hami.core.admin.v1.ListAdminUsersRequest.
 * Use `create(ListAdminUsersRequestSchema)` to create a new message.
 */
export const ListAdminUsersRequestSchema: GenMessage<ListAdminUsersRequest> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 5);

/**
 * @generated from message hami.core.admin.v1.ListAdminUsersResponse
 */
export type ListAdminUsersResponse = Message<"hami.core.admin.v1.ListAdminUsersResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.AdminUser admin_users = 1;
   */
  adminUsers: AdminUser[];
};

/**
 * Describes the message hami.core.admin.v1.ListAdminUsersResponse.
 * Use `create(ListAdminUsersResponseSchema)` to create a new message.
 */
export const ListAdminUsersResponseSchema: GenMessage<ListAdminUsersResponse> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 6);

/**
 * @generated from message hami.core.admin.v1.CreateAdminUserRequest
 */
export type CreateAdminUserRequest = Message<"hami.core.admin.v1.CreateAdminUserRequest"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string email = 2;
   */
  email: string;
};

/**
 * Describes the message hami.core.admin.v1.CreateAdminUserRequest.
 * Use `create(CreateAdminUserRequestSchema)` to create a new message.
 */
export const CreateAdminUserRequestSchema: GenMessage<CreateAdminUserRequest> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 7);

/**
 * @generated from message hami.core.admin.v1.CreateAdminUserResponse
 */
export type CreateAdminUserResponse = Message<"hami.core.admin.v1.CreateAdminUserResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.AdminUser admin_user = 1;
   */
  adminUser?: AdminUser;
};

/**
 * Describes the message hami.core.admin.v1.CreateAdminUserResponse.
 * Use `create(CreateAdminUserResponseSchema)` to create a new message.
 */
export const CreateAdminUserResponseSchema: GenMessage<CreateAdminUserResponse> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 8);

/**
 * @generated from message hami.core.admin.v1.UpdateAdminUserRequest
 */
export type UpdateAdminUserRequest = Message<"hami.core.admin.v1.UpdateAdminUserRequest"> & {
  /**
   * @generated from field: string admin_user_id = 1;
   */
  adminUserId: string;

  /**
   * @generated from field: optional string name = 2;
   */
  name?: string;

  /**
   * @generated from field: optional string email = 3;
   */
  email?: string;

  /**
   * @generated from field: optional string password = 4;
   */
  password?: string;
};

/**
 * Describes the message hami.core.admin.v1.UpdateAdminUserRequest.
 * Use `create(UpdateAdminUserRequestSchema)` to create a new message.
 */
export const UpdateAdminUserRequestSchema: GenMessage<UpdateAdminUserRequest> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 9);

/**
 * @generated from message hami.core.admin.v1.UpdateAdminUserResponse
 */
export type UpdateAdminUserResponse = Message<"hami.core.admin.v1.UpdateAdminUserResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.AdminUser admin_user = 1;
   */
  adminUser?: AdminUser;
};

/**
 * Describes the message hami.core.admin.v1.UpdateAdminUserResponse.
 * Use `create(UpdateAdminUserResponseSchema)` to create a new message.
 */
export const UpdateAdminUserResponseSchema: GenMessage<UpdateAdminUserResponse> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 10);

/**
 * @generated from message hami.core.admin.v1.ChangePasswordRequest
 */
export type ChangePasswordRequest = Message<"hami.core.admin.v1.ChangePasswordRequest"> & {
  /**
   * @generated from field: string session_token = 1;
   */
  sessionToken: string;

  /**
   * @generated from field: string current_password = 2;
   */
  currentPassword: string;

  /**
   * @generated from field: string new_password = 3;
   */
  newPassword: string;
};

/**
 * Describes the message hami.core.admin.v1.ChangePasswordRequest.
 * Use `create(ChangePasswordRequestSchema)` to create a new message.
 */
export const ChangePasswordRequestSchema: GenMessage<ChangePasswordRequest> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 11);

/**
 * @generated from message hami.core.admin.v1.ChangePasswordResponse
 */
export type ChangePasswordResponse = Message<"hami.core.admin.v1.ChangePasswordResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.core.admin.v1.ChangePasswordResponse.
 * Use `create(ChangePasswordResponseSchema)` to create a new message.
 */
export const ChangePasswordResponseSchema: GenMessage<ChangePasswordResponse> = /*@__PURE__*/
  messageDesc(file_admin_user_service, 12);

/**
 * @generated from service hami.core.admin.v1.AdminUserService
 */
export const AdminUserService: GenService<{
  /**
   * @generated from rpc hami.core.admin.v1.AdminUserService.Login
   */
  login: {
    methodKind: "unary";
    input: typeof LoginRequestSchema;
    output: typeof LoginResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.AdminUserService.GetMe
   */
  getMe: {
    methodKind: "unary";
    input: typeof GetMeRequestSchema;
    output: typeof GetMeResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.AdminUserService.ListAdminUsers
   */
  listAdminUsers: {
    methodKind: "unary";
    input: typeof ListAdminUsersRequestSchema;
    output: typeof ListAdminUsersResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.AdminUserService.CreateAdminUser
   */
  createAdminUser: {
    methodKind: "unary";
    input: typeof CreateAdminUserRequestSchema;
    output: typeof CreateAdminUserResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.AdminUserService.UpdateAdminUser
   */
  updateAdminUser: {
    methodKind: "unary";
    input: typeof UpdateAdminUserRequestSchema;
    output: typeof UpdateAdminUserResponseSchema;
  },
  /**
   * @generated from rpc hami.core.admin.v1.AdminUserService.ChangePassword
   */
  changePassword: {
    methodKind: "unary";
    input: typeof ChangePasswordRequestSchema;
    output: typeof ChangePasswordResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_admin_user_service, 0);

