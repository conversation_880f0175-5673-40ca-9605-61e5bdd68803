// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file admin_message_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message as Message$1 } from "@bufbuild/protobuf";

/**
 * Describes the file admin_message_service.proto.
 */
export const file_admin_message_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * メッセージ情報
 *
 * @generated from message hami.core.admin.v1.Message
 */
export type Message = Message$1<"hami.core.admin.v1.Message"> & {
  /**
   * 外部公開用ID（public_idを使用）
   *
   * @generated from field: string message_id = 1;
   */
  messageId: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string body = 3;
   */
  body: string;

  /**
   * @generated from field: hami.core.admin.v1.MessageType message_type = 4;
   */
  messageType: MessageType;

  /**
   * @generated from field: hami.core.admin.v1.MessageStatus status = 5;
   */
  status: MessageStatus;

  /**
   * @generated from field: int32 sender_id = 6;
   */
  senderId: number;

  /**
   * @generated from field: optional google.protobuf.Timestamp sent_at = 7;
   */
  sentAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 8;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 9;
   */
  updatedAt?: Timestamp;

  /**
   * @generated from field: repeated hami.core.admin.v1.MessageAttachment attachments = 10;
   */
  attachments: MessageAttachment[];
};

/**
 * Describes the message hami.core.admin.v1.Message.
 * Use `create(MessageSchema)` to create a new message.
 */
export const MessageSchema: GenMessage<Message> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 0);

/**
 * 添付ファイル情報
 *
 * @generated from message hami.core.admin.v1.MessageAttachment
 */
export type MessageAttachment = Message$1<"hami.core.admin.v1.MessageAttachment"> & {
  /**
   * @generated from field: int32 message_attachment_id = 1;
   */
  messageAttachmentId: number;

  /**
   * @generated from field: string file_name = 2;
   */
  fileName: string;

  /**
   * @generated from field: string mime_type = 3;
   */
  mimeType: string;

  /**
   * @generated from field: int32 file_size = 4;
   */
  fileSize: number;

  /**
   * @generated from field: hami.core.admin.v1.AttachmentType attachment_type = 5;
   */
  attachmentType: AttachmentType;

  /**
   * @generated from field: int32 display_order = 6;
   */
  displayOrder: number;

  /**
   * ダウンロード用URL
   *
   * @generated from field: string download_url = 7;
   */
  downloadUrl: string;
};

/**
 * Describes the message hami.core.admin.v1.MessageAttachment.
 * Use `create(MessageAttachmentSchema)` to create a new message.
 */
export const MessageAttachmentSchema: GenMessage<MessageAttachment> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 1);

/**
 * 一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListMessagesRequest
 */
export type ListMessagesRequest = Message$1<"hami.core.admin.v1.ListMessagesRequest"> & {
  /**
   * @generated from field: optional hami.core.admin.v1.MessageType message_type = 1;
   */
  messageType?: MessageType;

  /**
   * @generated from field: optional hami.core.admin.v1.MessageStatus status = 2;
   */
  status?: MessageStatus;

  /**
   * @generated from field: optional int32 sender_id = 3;
   */
  senderId?: number;

  /**
   * タイトル・本文検索
   *
   * @generated from field: optional string search_query = 4;
   */
  searchQuery?: string;

  /**
   * @generated from field: int32 page = 5;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 6;
   */
  pageSize: number;

  /**
   * "created_at", "updated_at", "sent_at"
   *
   * @generated from field: string sort_by = 7;
   */
  sortBy: string;

  /**
   * "asc", "desc"
   *
   * @generated from field: string sort_order = 8;
   */
  sortOrder: string;
};

/**
 * Describes the message hami.core.admin.v1.ListMessagesRequest.
 * Use `create(ListMessagesRequestSchema)` to create a new message.
 */
export const ListMessagesRequestSchema: GenMessage<ListMessagesRequest> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 2);

/**
 * @generated from message hami.core.admin.v1.ListMessagesResponse
 */
export type ListMessagesResponse = Message$1<"hami.core.admin.v1.ListMessagesResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.Message messages = 1;
   */
  messages: Message[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 total_pages = 3;
   */
  totalPages: number;
};

/**
 * Describes the message hami.core.admin.v1.ListMessagesResponse.
 * Use `create(ListMessagesResponseSchema)` to create a new message.
 */
export const ListMessagesResponseSchema: GenMessage<ListMessagesResponse> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 3);

/**
 * メッセージ詳細取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetMessageRequest
 */
export type GetMessageRequest = Message$1<"hami.core.admin.v1.GetMessageRequest"> & {
  /**
   * @generated from field: string message_id = 1;
   */
  messageId: string;
};

/**
 * Describes the message hami.core.admin.v1.GetMessageRequest.
 * Use `create(GetMessageRequestSchema)` to create a new message.
 */
export const GetMessageRequestSchema: GenMessage<GetMessageRequest> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 4);

/**
 * @generated from message hami.core.admin.v1.GetMessageResponse
 */
export type GetMessageResponse = Message$1<"hami.core.admin.v1.GetMessageResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.Message message = 1;
   */
  message?: Message;
};

/**
 * Describes the message hami.core.admin.v1.GetMessageResponse.
 * Use `create(GetMessageResponseSchema)` to create a new message.
 */
export const GetMessageResponseSchema: GenMessage<GetMessageResponse> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 5);

/**
 * 配信状況取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetDeliveryStatusRequest
 */
export type GetDeliveryStatusRequest = Message$1<"hami.core.admin.v1.GetDeliveryStatusRequest"> & {
  /**
   * @generated from field: string message_id = 1;
   */
  messageId: string;

  /**
   * @generated from field: int32 page = 2;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 3;
   */
  pageSize: number;

  /**
   * @generated from field: optional hami.core.admin.v1.RecipientStatus status_filter = 4;
   */
  statusFilter?: RecipientStatus;
};

/**
 * Describes the message hami.core.admin.v1.GetDeliveryStatusRequest.
 * Use `create(GetDeliveryStatusRequestSchema)` to create a new message.
 */
export const GetDeliveryStatusRequestSchema: GenMessage<GetDeliveryStatusRequest> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 6);

/**
 * @generated from message hami.core.admin.v1.GetDeliveryStatusResponse
 */
export type GetDeliveryStatusResponse = Message$1<"hami.core.admin.v1.GetDeliveryStatusResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.MessageRecipientStatus recipients = 1;
   */
  recipients: MessageRecipientStatus[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 total_pages = 3;
   */
  totalPages: number;
};

/**
 * Describes the message hami.core.admin.v1.GetDeliveryStatusResponse.
 * Use `create(GetDeliveryStatusResponseSchema)` to create a new message.
 */
export const GetDeliveryStatusResponseSchema: GenMessage<GetDeliveryStatusResponse> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 7);

/**
 * @generated from message hami.core.admin.v1.MessageRecipientStatus
 */
export type MessageRecipientStatus = Message$1<"hami.core.admin.v1.MessageRecipientStatus"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;

  /**
   * @generated from field: string member_name = 2;
   */
  memberName: string;

  /**
   * @generated from field: string member_number = 3;
   */
  memberNumber: string;

  /**
   * @generated from field: hami.core.admin.v1.RecipientStatus status = 4;
   */
  status: RecipientStatus;

  /**
   * @generated from field: optional google.protobuf.Timestamp delivered_at = 5;
   */
  deliveredAt?: Timestamp;

  /**
   * @generated from field: optional google.protobuf.Timestamp read_at = 6;
   */
  readAt?: Timestamp;
};

/**
 * Describes the message hami.core.admin.v1.MessageRecipientStatus.
 * Use `create(MessageRecipientStatusSchema)` to create a new message.
 */
export const MessageRecipientStatusSchema: GenMessage<MessageRecipientStatus> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 8);

/**
 * 会員別メッセージ一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListMemberMessagesRequest
 */
export type ListMemberMessagesRequest = Message$1<"hami.core.admin.v1.ListMemberMessagesRequest"> & {
  /**
   * 対象会員ID
   *
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;

  /**
   * メッセージ種別フィルタ
   *
   * @generated from field: optional hami.core.admin.v1.MessageType message_type = 2;
   */
  messageType?: MessageType;

  /**
   * 受信状況フィルタ
   *
   * @generated from field: optional hami.core.admin.v1.RecipientStatus status = 3;
   */
  status?: RecipientStatus;

  /**
   * @generated from field: int32 page = 4;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 5;
   */
  pageSize: number;

  /**
   * "created_at", "sent_at", "delivered_at", "read_at"
   *
   * @generated from field: string sort_by = 6;
   */
  sortBy: string;

  /**
   * "asc", "desc"
   *
   * @generated from field: string sort_order = 7;
   */
  sortOrder: string;
};

/**
 * Describes the message hami.core.admin.v1.ListMemberMessagesRequest.
 * Use `create(ListMemberMessagesRequestSchema)` to create a new message.
 */
export const ListMemberMessagesRequestSchema: GenMessage<ListMemberMessagesRequest> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 9);

/**
 * @generated from message hami.core.admin.v1.ListMemberMessagesResponse
 */
export type ListMemberMessagesResponse = Message$1<"hami.core.admin.v1.ListMemberMessagesResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.MemberMessage messages = 1;
   */
  messages: MemberMessage[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 total_pages = 3;
   */
  totalPages: number;
};

/**
 * Describes the message hami.core.admin.v1.ListMemberMessagesResponse.
 * Use `create(ListMemberMessagesResponseSchema)` to create a new message.
 */
export const ListMemberMessagesResponseSchema: GenMessage<ListMemberMessagesResponse> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 10);

/**
 * 会員が受信したメッセージ情報（受信状況を含む）
 *
 * @generated from message hami.core.admin.v1.MemberMessage
 */
export type MemberMessage = Message$1<"hami.core.admin.v1.MemberMessage"> & {
  /**
   * 外部公開用ID（public_idを使用）
   *
   * @generated from field: string message_id = 1;
   */
  messageId: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string body = 3;
   */
  body: string;

  /**
   * @generated from field: hami.core.admin.v1.MessageType message_type = 4;
   */
  messageType: MessageType;

  /**
   * @generated from field: hami.core.admin.v1.MessageStatus status = 5;
   */
  status: MessageStatus;

  /**
   * この会員の受信状況
   *
   * @generated from field: hami.core.admin.v1.RecipientStatus recipient_status = 6;
   */
  recipientStatus: RecipientStatus;

  /**
   * @generated from field: int32 sender_id = 7;
   */
  senderId: number;

  /**
   * @generated from field: optional google.protobuf.Timestamp sent_at = 8;
   */
  sentAt?: Timestamp;

  /**
   * @generated from field: optional google.protobuf.Timestamp delivered_at = 9;
   */
  deliveredAt?: Timestamp;

  /**
   * @generated from field: optional google.protobuf.Timestamp read_at = 10;
   */
  readAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 11;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 12;
   */
  updatedAt?: Timestamp;

  /**
   * @generated from field: repeated hami.core.admin.v1.MessageAttachment attachments = 13;
   */
  attachments: MessageAttachment[];
};

/**
 * Describes the message hami.core.admin.v1.MemberMessage.
 * Use `create(MemberMessageSchema)` to create a new message.
 */
export const MemberMessageSchema: GenMessage<MemberMessage> = /*@__PURE__*/
  messageDesc(file_admin_message_service, 11);

/**
 * メッセージ種別
 *
 * @generated from enum hami.core.admin.v1.MessageType
 */
export enum MessageType {
  /**
   * @generated from enum value: MESSAGE_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 個別メッセージ
   *
   * @generated from enum value: MESSAGE_TYPE_INDIVIDUAL = 1;
   */
  INDIVIDUAL = 1,

  /**
   * 一斉送信
   *
   * @generated from enum value: MESSAGE_TYPE_BROADCAST = 2;
   */
  BROADCAST = 2,

  /**
   * 通知メッセージ
   *
   * @generated from enum value: MESSAGE_TYPE_NOTIFICATION = 3;
   */
  NOTIFICATION = 3,

  /**
   * リマインダー
   *
   * @generated from enum value: MESSAGE_TYPE_REMINDER = 4;
   */
  REMINDER = 4,
}

/**
 * Describes the enum hami.core.admin.v1.MessageType.
 */
export const MessageTypeSchema: GenEnum<MessageType> = /*@__PURE__*/
  enumDesc(file_admin_message_service, 0);

/**
 * メッセージ送信状態
 *
 * @generated from enum hami.core.admin.v1.MessageStatus
 */
export enum MessageStatus {
  /**
   * @generated from enum value: MESSAGE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 下書き
   *
   * @generated from enum value: MESSAGE_STATUS_DRAFT = 1;
   */
  DRAFT = 1,

  /**
   * 送信中
   *
   * @generated from enum value: MESSAGE_STATUS_SENDING = 2;
   */
  SENDING = 2,

  /**
   * 送信完了
   *
   * @generated from enum value: MESSAGE_STATUS_SENT = 3;
   */
  SENT = 3,

  /**
   * 送信失敗
   *
   * @generated from enum value: MESSAGE_STATUS_FAILED = 4;
   */
  FAILED = 4,
}

/**
 * Describes the enum hami.core.admin.v1.MessageStatus.
 */
export const MessageStatusSchema: GenEnum<MessageStatus> = /*@__PURE__*/
  enumDesc(file_admin_message_service, 1);

/**
 * 受信者ステータス
 *
 * @generated from enum hami.core.admin.v1.RecipientStatus
 */
export enum RecipientStatus {
  /**
   * @generated from enum value: RECIPIENT_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 送信待ち
   *
   * @generated from enum value: RECIPIENT_STATUS_PENDING = 1;
   */
  PENDING = 1,

  /**
   * 配信済み
   *
   * @generated from enum value: RECIPIENT_STATUS_DELIVERED = 2;
   */
  DELIVERED = 2,

  /**
   * 既読
   *
   * @generated from enum value: RECIPIENT_STATUS_READ = 3;
   */
  READ = 3,

  /**
   * 配信失敗
   *
   * @generated from enum value: RECIPIENT_STATUS_FAILED = 4;
   */
  FAILED = 4,
}

/**
 * Describes the enum hami.core.admin.v1.RecipientStatus.
 */
export const RecipientStatusSchema: GenEnum<RecipientStatus> = /*@__PURE__*/
  enumDesc(file_admin_message_service, 2);

/**
 * 添付ファイル種別
 *
 * @generated from enum hami.core.admin.v1.AttachmentType
 */
export enum AttachmentType {
  /**
   * @generated from enum value: ATTACHMENT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 画像ファイル（JPEG, PNG, GIF等）
   *
   * @generated from enum value: ATTACHMENT_TYPE_IMAGE = 1;
   */
  IMAGE = 1,

  /**
   * 文書ファイル（PDF, Word, Excel等）
   *
   * @generated from enum value: ATTACHMENT_TYPE_DOCUMENT = 2;
   */
  DOCUMENT = 2,

  /**
   * その他のファイル
   *
   * @generated from enum value: ATTACHMENT_TYPE_OTHER = 3;
   */
  OTHER = 3,
}

/**
 * Describes the enum hami.core.admin.v1.AttachmentType.
 */
export const AttachmentTypeSchema: GenEnum<AttachmentType> = /*@__PURE__*/
  enumDesc(file_admin_message_service, 3);

/**
 * メッセージ管理サービス（読み取り専用）
 *
 * @generated from service hami.core.admin.v1.AdminMessageService
 */
export const AdminMessageService: GenService<{
  /**
   * メッセージ一覧取得
   *
   * @generated from rpc hami.core.admin.v1.AdminMessageService.ListMessages
   */
  listMessages: {
    methodKind: "unary";
    input: typeof ListMessagesRequestSchema;
    output: typeof ListMessagesResponseSchema;
  },
  /**
   * メッセージ詳細取得
   *
   * @generated from rpc hami.core.admin.v1.AdminMessageService.GetMessage
   */
  getMessage: {
    methodKind: "unary";
    input: typeof GetMessageRequestSchema;
    output: typeof GetMessageResponseSchema;
  },
  /**
   * 配信状況取得
   *
   * @generated from rpc hami.core.admin.v1.AdminMessageService.GetDeliveryStatus
   */
  getDeliveryStatus: {
    methodKind: "unary";
    input: typeof GetDeliveryStatusRequestSchema;
    output: typeof GetDeliveryStatusResponseSchema;
  },
  /**
   * 会員別メッセージ一覧取得
   *
   * @generated from rpc hami.core.admin.v1.AdminMessageService.ListMemberMessages
   */
  listMemberMessages: {
    methodKind: "unary";
    input: typeof ListMemberMessagesRequestSchema;
    output: typeof ListMemberMessagesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_admin_message_service, 0);

