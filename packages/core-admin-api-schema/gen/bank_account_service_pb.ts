// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file bank_account_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file bank_account_service.proto.
 */
export const file_bank_account_service: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * 口座登録申請サマリー（一覧表示用）
 *
 * @generated from message hami.core.admin.v1.BankAccountRegistrationSummary
 */
export type BankAccountRegistrationSummary = Message<"hami.core.admin.v1.BankAccountRegistrationSummary"> & {
  /**
   * @generated from field: int32 bank_account_registration_id = 1;
   */
  bankAccountRegistrationId: number;

  /**
   * @generated from field: int32 member_id = 2;
   */
  memberId: number;

  /**
   * @generated from field: string member_name = 3;
   */
  memberName: string;

  /**
   * @generated from field: int32 member_number = 4;
   */
  memberNumber: number;

  /**
   * @generated from field: hami.core.admin.v1.BankAccountRegistrationStatus registration_status = 5;
   */
  registrationStatus: BankAccountRegistrationStatus;

  /**
   * @generated from field: string bank_code = 6;
   */
  bankCode: string;

  /**
   * @generated from field: optional string bank_name = 7;
   */
  bankName?: string;

  /**
   * マスク済み
   *
   * @generated from field: optional string result_account_number = 8;
   */
  resultAccountNumber?: string;

  /**
   * @generated from field: int64 created_at = 9;
   */
  createdAt: bigint;

  /**
   * @generated from field: int64 updated_at = 10;
   */
  updatedAt: bigint;

  /**
   * @generated from field: optional int64 completed_at = 11;
   */
  completedAt?: bigint;

  /**
   * @generated from field: optional string error_message = 12;
   */
  errorMessage?: string;

  /**
   * @generated from field: bool is_active = 13;
   */
  isActive: boolean;
};

/**
 * Describes the message hami.core.admin.v1.BankAccountRegistrationSummary.
 * Use `create(BankAccountRegistrationSummarySchema)` to create a new message.
 */
export const BankAccountRegistrationSummarySchema: GenMessage<BankAccountRegistrationSummary> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 0);

/**
 * 口座登録申請詳細
 *
 * @generated from message hami.core.admin.v1.BankAccountRegistrationDetail
 */
export type BankAccountRegistrationDetail = Message<"hami.core.admin.v1.BankAccountRegistrationDetail"> & {
  /**
   * @generated from field: int32 bank_account_registration_id = 1;
   */
  bankAccountRegistrationId: number;

  /**
   * @generated from field: hami.core.admin.v1.BankAccountRegistrationStatus registration_status = 2;
   */
  registrationStatus: BankAccountRegistrationStatus;

  /**
   * 申請時情報
   *
   * @generated from field: string bank_code = 3;
   */
  bankCode: string;

  /**
   * @generated from field: optional string bank_name = 4;
   */
  bankName?: string;

  /**
   * @generated from field: optional string branch_code = 5;
   */
  branchCode?: string;

  /**
   * @generated from field: optional int32 account_type = 6;
   */
  accountType?: number;

  /**
   * @generated from field: optional string account_number = 7;
   */
  accountNumber?: string;

  /**
   * @generated from field: optional string account_name = 8;
   */
  accountName?: string;

  /**
   * @generated from field: optional string account_name_kanji = 9;
   */
  accountNameKanji?: string;

  /**
   * 結果情報
   *
   * @generated from field: optional string result_bank_code = 10;
   */
  resultBankCode?: string;

  /**
   * @generated from field: optional string result_branch_code = 11;
   */
  resultBranchCode?: string;

  /**
   * @generated from field: optional int32 result_account_type = 12;
   */
  resultAccountType?: number;

  /**
   * マスク済み
   *
   * @generated from field: optional string result_account_number = 13;
   */
  resultAccountNumber?: string;

  /**
   * @generated from field: optional string result_account_name = 14;
   */
  resultAccountName?: string;

  /**
   * GMO連携情報
   *
   * @generated from field: optional string gmo_transaction_id = 15;
   */
  gmoTransactionId?: string;

  /**
   * @generated from field: optional string gmo_token = 16;
   */
  gmoToken?: string;

  /**
   * エラー情報
   *
   * @generated from field: optional string error_code = 17;
   */
  errorCode?: string;

  /**
   * @generated from field: optional string error_detail = 18;
   */
  errorDetail?: string;

  /**
   * @generated from field: optional string error_message = 19;
   */
  errorMessage?: string;

  /**
   * 日時情報
   *
   * @generated from field: int64 created_at = 20;
   */
  createdAt: bigint;

  /**
   * @generated from field: int64 updated_at = 21;
   */
  updatedAt: bigint;

  /**
   * @generated from field: optional int64 completed_at = 22;
   */
  completedAt?: bigint;

  /**
   * フラグ
   *
   * @generated from field: bool is_active = 23;
   */
  isActive: boolean;

  /**
   * 状態変更ログ
   *
   * @generated from field: repeated hami.core.admin.v1.BankAccountRegistrationLog status_logs = 24;
   */
  statusLogs: BankAccountRegistrationLog[];
};

/**
 * Describes the message hami.core.admin.v1.BankAccountRegistrationDetail.
 * Use `create(BankAccountRegistrationDetailSchema)` to create a new message.
 */
export const BankAccountRegistrationDetailSchema: GenMessage<BankAccountRegistrationDetail> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 1);

/**
 * 口座登録状態変更ログ
 *
 * @generated from message hami.core.admin.v1.BankAccountRegistrationLog
 */
export type BankAccountRegistrationLog = Message<"hami.core.admin.v1.BankAccountRegistrationLog"> & {
  /**
   * @generated from field: int32 bank_account_registration_log_id = 1;
   */
  bankAccountRegistrationLogId: number;

  /**
   * @generated from field: int32 bank_account_registration_id = 2;
   */
  bankAccountRegistrationId: number;

  /**
   * @generated from field: optional hami.core.admin.v1.BankAccountRegistrationStatus from_status = 3;
   */
  fromStatus?: BankAccountRegistrationStatus;

  /**
   * @generated from field: hami.core.admin.v1.BankAccountRegistrationStatus to_status = 4;
   */
  toStatus: BankAccountRegistrationStatus;

  /**
   * @generated from field: optional string gmo_response = 5;
   */
  gmoResponse?: string;

  /**
   * @generated from field: optional string triggered_by = 6;
   */
  triggeredBy?: string;

  /**
   * @generated from field: optional string ip_address = 7;
   */
  ipAddress?: string;

  /**
   * @generated from field: optional string user_agent = 8;
   */
  userAgent?: string;

  /**
   * @generated from field: int64 created_at = 9;
   */
  createdAt: bigint;
};

/**
 * Describes the message hami.core.admin.v1.BankAccountRegistrationLog.
 * Use `create(BankAccountRegistrationLogSchema)` to create a new message.
 */
export const BankAccountRegistrationLogSchema: GenMessage<BankAccountRegistrationLog> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 2);

/**
 * 会員情報（口座登録詳細用）
 *
 * @generated from message hami.core.admin.v1.MemberInfo
 */
export type MemberInfo = Message<"hami.core.admin.v1.MemberInfo"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;

  /**
   * @generated from field: string member_name = 2;
   */
  memberName: string;

  /**
   * @generated from field: int32 member_number = 3;
   */
  memberNumber: number;

  /**
   * @generated from field: string email = 4;
   */
  email: string;
};

/**
 * Describes the message hami.core.admin.v1.MemberInfo.
 * Use `create(MemberInfoSchema)` to create a new message.
 */
export const MemberInfoSchema: GenMessage<MemberInfo> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 3);

/**
 * 口座登録一覧取得リクエスト
 *
 * @generated from message hami.core.admin.v1.ListBankAccountRegistrationsRequest
 */
export type ListBankAccountRegistrationsRequest = Message<"hami.core.admin.v1.ListBankAccountRegistrationsRequest"> & {
  /**
   * @generated from field: optional int32 page = 1;
   */
  page?: number;

  /**
   * @generated from field: optional int32 limit = 2;
   */
  limit?: number;

  /**
   * @generated from field: repeated hami.core.admin.v1.BankAccountRegistrationStatus status_filter = 3;
   */
  statusFilter: BankAccountRegistrationStatus[];

  /**
   * 会員名・会員IDでの検索
   *
   * @generated from field: optional string search_query = 4;
   */
  searchQuery?: string;

  /**
   * "created_at", "updated_at", "member_name"
   *
   * @generated from field: optional string sort_by = 5;
   */
  sortBy?: string;

  /**
   * "asc", "desc"
   *
   * @generated from field: optional string sort_order = 6;
   */
  sortOrder?: string;

  /**
   * @generated from field: optional int64 date_from = 7;
   */
  dateFrom?: bigint;

  /**
   * @generated from field: optional int64 date_to = 8;
   */
  dateTo?: bigint;
};

/**
 * Describes the message hami.core.admin.v1.ListBankAccountRegistrationsRequest.
 * Use `create(ListBankAccountRegistrationsRequestSchema)` to create a new message.
 */
export const ListBankAccountRegistrationsRequestSchema: GenMessage<ListBankAccountRegistrationsRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 4);

/**
 * 口座登録一覧取得レスポンス
 *
 * @generated from message hami.core.admin.v1.ListBankAccountRegistrationsResponse
 */
export type ListBankAccountRegistrationsResponse = Message<"hami.core.admin.v1.ListBankAccountRegistrationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.BankAccountRegistrationSummary registrations = 1;
   */
  registrations: BankAccountRegistrationSummary[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 current_page = 3;
   */
  currentPage: number;

  /**
   * @generated from field: int32 total_pages = 4;
   */
  totalPages: number;
};

/**
 * Describes the message hami.core.admin.v1.ListBankAccountRegistrationsResponse.
 * Use `create(ListBankAccountRegistrationsResponseSchema)` to create a new message.
 */
export const ListBankAccountRegistrationsResponseSchema: GenMessage<ListBankAccountRegistrationsResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 5);

/**
 * 口座登録詳細取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetBankAccountDetailRequest
 */
export type GetBankAccountDetailRequest = Message<"hami.core.admin.v1.GetBankAccountDetailRequest"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetBankAccountDetailRequest.
 * Use `create(GetBankAccountDetailRequestSchema)` to create a new message.
 */
export const GetBankAccountDetailRequestSchema: GenMessage<GetBankAccountDetailRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 6);

/**
 * 口座登録詳細取得レスポンス
 *
 * @generated from message hami.core.admin.v1.GetBankAccountDetailResponse
 */
export type GetBankAccountDetailResponse = Message<"hami.core.admin.v1.GetBankAccountDetailResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.MemberInfo member = 1;
   */
  member?: MemberInfo;

  /**
   * @generated from field: repeated hami.core.admin.v1.BankAccountRegistrationDetail registrations = 2;
   */
  registrations: BankAccountRegistrationDetail[];

  /**
   * @generated from field: optional hami.core.admin.v1.BankAccountRegistrationDetail active_registration = 3;
   */
  activeRegistration?: BankAccountRegistrationDetail;
};

/**
 * Describes the message hami.core.admin.v1.GetBankAccountDetailResponse.
 * Use `create(GetBankAccountDetailResponseSchema)` to create a new message.
 */
export const GetBankAccountDetailResponseSchema: GenMessage<GetBankAccountDetailResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 7);

/**
 * 口座登録状態
 *
 * @generated from enum hami.core.admin.v1.BankAccountRegistrationStatus
 */
export enum BankAccountRegistrationStatus {
  /**
   * @generated from enum value: BANK_ACCOUNT_REGISTRATION_STATUS_UNKNOWN = 0;
   */
  BANK_ACCOUNT_REGISTRATION_STATUS_UNKNOWN = 0,

  /**
   * 登録済み（申請完了）
   *
   * @generated from enum value: ENTRY = 1;
   */
  ENTRY = 1,

  /**
   * 金融機関画面遷移
   *
   * @generated from enum value: START = 2;
   */
  START = 2,

  /**
   * 結果確認
   *
   * @generated from enum value: TERM = 3;
   */
  TERM = 3,

  /**
   * 申込成功
   *
   * @generated from enum value: SUCCESS = 4;
   */
  SUCCESS = 4,

  /**
   * 申込失敗（金融機関からのNG）
   *
   * @generated from enum value: FAIL = 5;
   */
  FAIL = 5,

  /**
   * 申込失敗（結果返却前の失敗）
   *
   * @generated from enum value: UNPROCESSED = 6;
   */
  UNPROCESSED = 6,
}

/**
 * Describes the enum hami.core.admin.v1.BankAccountRegistrationStatus.
 */
export const BankAccountRegistrationStatusSchema: GenEnum<BankAccountRegistrationStatus> = /*@__PURE__*/
  enumDesc(file_bank_account_service, 0);

/**
 * 口座登録管理サービス
 *
 * @generated from service hami.core.admin.v1.BankAccountService
 */
export const BankAccountService: GenService<{
  /**
   * 口座登録一覧取得
   *
   * @generated from rpc hami.core.admin.v1.BankAccountService.ListBankAccountRegistrations
   */
  listBankAccountRegistrations: {
    methodKind: "unary";
    input: typeof ListBankAccountRegistrationsRequestSchema;
    output: typeof ListBankAccountRegistrationsResponseSchema;
  },
  /**
   * 口座登録詳細取得
   *
   * @generated from rpc hami.core.admin.v1.BankAccountService.GetBankAccountDetail
   */
  getBankAccountDetail: {
    methodKind: "unary";
    input: typeof GetBankAccountDetailRequestSchema;
    output: typeof GetBankAccountDetailResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_bank_account_service, 0);

