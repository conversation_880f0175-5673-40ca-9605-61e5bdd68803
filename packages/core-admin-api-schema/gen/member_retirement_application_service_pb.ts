// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file member_retirement_application_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file member_retirement_application_service.proto.
 */
export const file_member_retirement_application_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_empty]);

/**
 * 退会申請一覧リクエスト
 *
 * @generated from message hami.core.admin.v1.ListMemberRetirementApplicationsRequest
 */
export type ListMemberRetirementApplicationsRequest = Message<"hami.core.admin.v1.ListMemberRetirementApplicationsRequest"> & {
  /**
   * 受理済を含めるかフラグ
   *
   * @generated from field: bool include_approved = 1;
   */
  includeApproved: boolean;

  /**
   * 拒否済を含めるかフラグ
   *
   * @generated from field: bool include_rejected = 2;
   */
  includeRejected: boolean;
};

/**
 * Describes the message hami.core.admin.v1.ListMemberRetirementApplicationsRequest.
 * Use `create(ListMemberRetirementApplicationsRequestSchema)` to create a new message.
 */
export const ListMemberRetirementApplicationsRequestSchema: GenMessage<ListMemberRetirementApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_member_retirement_application_service, 0);

/**
 * 退会申請一覧レスポンス
 *
 * @generated from message hami.core.admin.v1.ListMemberRetirementApplicationsResponse
 */
export type ListMemberRetirementApplicationsResponse = Message<"hami.core.admin.v1.ListMemberRetirementApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.core.admin.v1.MemberRetirementApplication applications = 1;
   */
  applications: MemberRetirementApplication[];
};

/**
 * Describes the message hami.core.admin.v1.ListMemberRetirementApplicationsResponse.
 * Use `create(ListMemberRetirementApplicationsResponseSchema)` to create a new message.
 */
export const ListMemberRetirementApplicationsResponseSchema: GenMessage<ListMemberRetirementApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_member_retirement_application_service, 1);

/**
 * 退会申請詳細リクエスト
 *
 * @generated from message hami.core.admin.v1.GetMemberRetirementApplicationRequest
 */
export type GetMemberRetirementApplicationRequest = Message<"hami.core.admin.v1.GetMemberRetirementApplicationRequest"> & {
  /**
   * @generated from field: int32 member_retirement_application_id = 1;
   */
  memberRetirementApplicationId: number;
};

/**
 * Describes the message hami.core.admin.v1.GetMemberRetirementApplicationRequest.
 * Use `create(GetMemberRetirementApplicationRequestSchema)` to create a new message.
 */
export const GetMemberRetirementApplicationRequestSchema: GenMessage<GetMemberRetirementApplicationRequest> = /*@__PURE__*/
  messageDesc(file_member_retirement_application_service, 2);

/**
 * 退会申請詳細レスポンス
 *
 * @generated from message hami.core.admin.v1.GetMemberRetirementApplicationResponse
 */
export type GetMemberRetirementApplicationResponse = Message<"hami.core.admin.v1.GetMemberRetirementApplicationResponse"> & {
  /**
   * @generated from field: hami.core.admin.v1.MemberRetirementApplication application = 1;
   */
  application?: MemberRetirementApplication;
};

/**
 * Describes the message hami.core.admin.v1.GetMemberRetirementApplicationResponse.
 * Use `create(GetMemberRetirementApplicationResponseSchema)` to create a new message.
 */
export const GetMemberRetirementApplicationResponseSchema: GenMessage<GetMemberRetirementApplicationResponse> = /*@__PURE__*/
  messageDesc(file_member_retirement_application_service, 3);

/**
 * 退会申請受理リクエスト
 *
 * @generated from message hami.core.admin.v1.ApproveMemberRetirementApplicationRequest
 */
export type ApproveMemberRetirementApplicationRequest = Message<"hami.core.admin.v1.ApproveMemberRetirementApplicationRequest"> & {
  /**
   * @generated from field: int32 member_retirement_application_id = 1;
   */
  memberRetirementApplicationId: number;
};

/**
 * Describes the message hami.core.admin.v1.ApproveMemberRetirementApplicationRequest.
 * Use `create(ApproveMemberRetirementApplicationRequestSchema)` to create a new message.
 */
export const ApproveMemberRetirementApplicationRequestSchema: GenMessage<ApproveMemberRetirementApplicationRequest> = /*@__PURE__*/
  messageDesc(file_member_retirement_application_service, 4);

/**
 * 退会申請受理レスポンス
 *
 * @generated from message hami.core.admin.v1.ApproveMemberRetirementApplicationResponse
 */
export type ApproveMemberRetirementApplicationResponse = Message<"hami.core.admin.v1.ApproveMemberRetirementApplicationResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.ApproveMemberRetirementApplicationResponse.
 * Use `create(ApproveMemberRetirementApplicationResponseSchema)` to create a new message.
 */
export const ApproveMemberRetirementApplicationResponseSchema: GenMessage<ApproveMemberRetirementApplicationResponse> = /*@__PURE__*/
  messageDesc(file_member_retirement_application_service, 5);

/**
 * 退会申請拒否リクエスト
 *
 * @generated from message hami.core.admin.v1.RejectMemberRetirementApplicationRequest
 */
export type RejectMemberRetirementApplicationRequest = Message<"hami.core.admin.v1.RejectMemberRetirementApplicationRequest"> & {
  /**
   * @generated from field: int32 member_retirement_application_id = 1;
   */
  memberRetirementApplicationId: number;
};

/**
 * Describes the message hami.core.admin.v1.RejectMemberRetirementApplicationRequest.
 * Use `create(RejectMemberRetirementApplicationRequestSchema)` to create a new message.
 */
export const RejectMemberRetirementApplicationRequestSchema: GenMessage<RejectMemberRetirementApplicationRequest> = /*@__PURE__*/
  messageDesc(file_member_retirement_application_service, 6);

/**
 * 退会申請拒否レスポンス
 *
 * @generated from message hami.core.admin.v1.RejectMemberRetirementApplicationResponse
 */
export type RejectMemberRetirementApplicationResponse = Message<"hami.core.admin.v1.RejectMemberRetirementApplicationResponse"> & {
};

/**
 * Describes the message hami.core.admin.v1.RejectMemberRetirementApplicationResponse.
 * Use `create(RejectMemberRetirementApplicationResponseSchema)` to create a new message.
 */
export const RejectMemberRetirementApplicationResponseSchema: GenMessage<RejectMemberRetirementApplicationResponse> = /*@__PURE__*/
  messageDesc(file_member_retirement_application_service, 7);

/**
 * 退会申請情報
 *
 * @generated from message hami.core.admin.v1.MemberRetirementApplication
 */
export type MemberRetirementApplication = Message<"hami.core.admin.v1.MemberRetirementApplication"> & {
  /**
   * 退会申請ID
   *
   * @generated from field: int32 member_retirement_application_id = 1;
   */
  memberRetirementApplicationId: number;

  /**
   * 会員ID
   *
   * @generated from field: int32 member_id = 2;
   */
  memberId: number;

  /**
   * 会員番号
   *
   * @generated from field: int32 member_number = 3;
   */
  memberNumber: number;

  /**
   * 会員氏名
   *
   * @generated from field: string member_name = 4;
   */
  memberName: string;

  /**
   * 申請日時
   *
   * @generated from field: string application_date = 5;
   */
  applicationDate: string;

  /**
   * 受理日時
   *
   * @generated from field: string approved_at = 6;
   */
  approvedAt: string;

  /**
   * 拒否日時
   *
   * @generated from field: string rejected_at = 7;
   */
  rejectedAt: string;
};

/**
 * Describes the message hami.core.admin.v1.MemberRetirementApplication.
 * Use `create(MemberRetirementApplicationSchema)` to create a new message.
 */
export const MemberRetirementApplicationSchema: GenMessage<MemberRetirementApplication> = /*@__PURE__*/
  messageDesc(file_member_retirement_application_service, 8);

/**
 * @generated from service hami.core.admin.v1.MemberRetirementApplicationService
 */
export const MemberRetirementApplicationService: GenService<{
  /**
   * 退会申請一覧取得
   *
   * @generated from rpc hami.core.admin.v1.MemberRetirementApplicationService.ListMemberRetirementApplications
   */
  listMemberRetirementApplications: {
    methodKind: "unary";
    input: typeof ListMemberRetirementApplicationsRequestSchema;
    output: typeof ListMemberRetirementApplicationsResponseSchema;
  },
  /**
   * 退会申請詳細取得
   *
   * @generated from rpc hami.core.admin.v1.MemberRetirementApplicationService.GetMemberRetirementApplication
   */
  getMemberRetirementApplication: {
    methodKind: "unary";
    input: typeof GetMemberRetirementApplicationRequestSchema;
    output: typeof GetMemberRetirementApplicationResponseSchema;
  },
  /**
   * 退会申請受理
   *
   * @generated from rpc hami.core.admin.v1.MemberRetirementApplicationService.ApproveMemberRetirementApplication
   */
  approveMemberRetirementApplication: {
    methodKind: "unary";
    input: typeof ApproveMemberRetirementApplicationRequestSchema;
    output: typeof ApproveMemberRetirementApplicationResponseSchema;
  },
  /**
   * 退会申請拒否
   *
   * @generated from rpc hami.core.admin.v1.MemberRetirementApplicationService.RejectMemberRetirementApplication
   */
  rejectMemberRetirementApplication: {
    methodKind: "unary";
    input: typeof RejectMemberRetirementApplicationRequestSchema;
    output: typeof RejectMemberRetirementApplicationResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_member_retirement_application_service, 0);

