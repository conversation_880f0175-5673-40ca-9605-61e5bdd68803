// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file common_enums.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file common_enums.proto.
 */
export const file_common_enums: GenFile = /*@__PURE__*/
  fileDesc("ChJjb21tb25fZW51bXMucHJvdG8SEmhhbWkuY29yZS5hZG1pbi52MSpQCgtIb3JzZUdlbmRlchIcChhIT1JTRV9HRU5ERVJfVU5TUEVDSUZJRUQQABIMCghTVEFMTElPThABEggKBE1BUkUQAhILCgdHRUxESU5HEANiBnByb3RvMw");

/**
 * 性別
 *
 * @generated from enum hami.core.admin.v1.HorseGender
 */
export enum HorseGender {
  /**
   * @generated from enum value: HORSE_GENDER_UNSPECIFIED = 0;
   */
  HORSE_GENDER_UNSPECIFIED = 0,

  /**
   * 牡馬
   *
   * @generated from enum value: STALLION = 1;
   */
  STALLION = 1,

  /**
   * 牝馬
   *
   * @generated from enum value: MARE = 2;
   */
  MARE = 2,

  /**
   * せん馬
   *
   * @generated from enum value: GELDING = 3;
   */
  GELDING = 3,
}

/**
 * Describes the enum hami.core.admin.v1.HorseGender.
 */
export const HorseGenderSchema: GenEnum<HorseGender> = /*@__PURE__*/
  enumDesc(file_common_enums, 0);

