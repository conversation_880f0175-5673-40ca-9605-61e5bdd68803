// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file horse_income_service.proto (package core.admin.horse_income, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_income_service.proto.
 */
export const file_horse_income_service: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * 賞金収入手当情報
 *
 * @generated from message core.admin.horse_income.HorseIncomePrizeAllowance
 */
export type HorseIncomePrizeAllowance = Message<"core.admin.horse_income.HorseIncomePrizeAllowance"> & {
  /**
   * @generated from field: int32 horse_income_prize_allowance_id = 1;
   */
  horseIncomePrizeAllowanceId: number;

  /**
   * @generated from field: int32 horse_income_prize_id = 2;
   */
  horseIncomePrizeId: number;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: int32 amount = 4;
   */
  amount: number;
};

/**
 * Describes the message core.admin.horse_income.HorseIncomePrizeAllowance.
 * Use `create(HorseIncomePrizeAllowanceSchema)` to create a new message.
 */
export const HorseIncomePrizeAllowanceSchema: GenMessage<HorseIncomePrizeAllowance> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 0);

/**
 * 賞金収入手当リクエスト
 *
 * @generated from message core.admin.horse_income.HorseIncomePrizeAllowanceRequest
 */
export type HorseIncomePrizeAllowanceRequest = Message<"core.admin.horse_income.HorseIncomePrizeAllowanceRequest"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: int32 amount = 2;
   */
  amount: number;
};

/**
 * Describes the message core.admin.horse_income.HorseIncomePrizeAllowanceRequest.
 * Use `create(HorseIncomePrizeAllowanceRequestSchema)` to create a new message.
 */
export const HorseIncomePrizeAllowanceRequestSchema: GenMessage<HorseIncomePrizeAllowanceRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 1);

/**
 * 賞金収入手当名情報
 *
 * @generated from message core.admin.horse_income.HorseIncomePrizeAllowanceName
 */
export type HorseIncomePrizeAllowanceName = Message<"core.admin.horse_income.HorseIncomePrizeAllowanceName"> & {
  /**
   * @generated from field: int32 horse_income_prize_allowance_name_id = 1;
   */
  horseIncomePrizeAllowanceNameId: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;
};

/**
 * Describes the message core.admin.horse_income.HorseIncomePrizeAllowanceName.
 * Use `create(HorseIncomePrizeAllowanceNameSchema)` to create a new message.
 */
export const HorseIncomePrizeAllowanceNameSchema: GenMessage<HorseIncomePrizeAllowanceName> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 2);

/**
 * 賞金収入手当名一覧取得リクエスト
 *
 * @generated from message core.admin.horse_income.ListHorseIncomePrizeAllowanceNamesRequest
 */
export type ListHorseIncomePrizeAllowanceNamesRequest = Message<"core.admin.horse_income.ListHorseIncomePrizeAllowanceNamesRequest"> & {
};

/**
 * Describes the message core.admin.horse_income.ListHorseIncomePrizeAllowanceNamesRequest.
 * Use `create(ListHorseIncomePrizeAllowanceNamesRequestSchema)` to create a new message.
 */
export const ListHorseIncomePrizeAllowanceNamesRequestSchema: GenMessage<ListHorseIncomePrizeAllowanceNamesRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 3);

/**
 * 賞金収入手当名一覧取得レスポンス
 *
 * @generated from message core.admin.horse_income.ListHorseIncomePrizeAllowanceNamesResponse
 */
export type ListHorseIncomePrizeAllowanceNamesResponse = Message<"core.admin.horse_income.ListHorseIncomePrizeAllowanceNamesResponse"> & {
  /**
   * @generated from field: repeated core.admin.horse_income.HorseIncomePrizeAllowanceName horse_income_prize_allowance_names = 1;
   */
  horseIncomePrizeAllowanceNames: HorseIncomePrizeAllowanceName[];
};

/**
 * Describes the message core.admin.horse_income.ListHorseIncomePrizeAllowanceNamesResponse.
 * Use `create(ListHorseIncomePrizeAllowanceNamesResponseSchema)` to create a new message.
 */
export const ListHorseIncomePrizeAllowanceNamesResponseSchema: GenMessage<ListHorseIncomePrizeAllowanceNamesResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 4);

/**
 * 馬の収入情報（一覧用）
 *
 * @generated from message core.admin.horse_income.HorseIncome
 */
export type HorseIncome = Message<"core.admin.horse_income.HorseIncome"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: bool closing = 2;
   */
  closing: boolean;

  /**
   * @generated from field: int32 income_year_month = 3;
   */
  incomeYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 4;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 5;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 6;
   */
  occurredDay: number;

  /**
   * @generated from field: core.admin.horse_income.IncomeType income_type = 7;
   */
  incomeType: IncomeType;

  /**
   * @generated from field: int32 horse_id = 8;
   */
  horseId: number;

  /**
   * @generated from field: string horse_name = 9;
   */
  horseName: string;

  /**
   * @generated from field: string name = 10;
   */
  name: string;

  /**
   * @generated from field: string name2 = 11;
   */
  name2: string;

  /**
   * @generated from field: int32 amount = 12;
   */
  amount: number;
};

/**
 * Describes the message core.admin.horse_income.HorseIncome.
 * Use `create(HorseIncomeSchema)` to create a new message.
 */
export const HorseIncomeSchema: GenMessage<HorseIncome> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 5);

/**
 * 馬の賞金収入情報
 *
 * @generated from message core.admin.horse_income.HorseIncomePrize
 */
export type HorseIncomePrize = Message<"core.admin.horse_income.HorseIncomePrize"> & {
  /**
   * @generated from field: int32 horse_income_prize_id = 1;
   */
  horseIncomePrizeId: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * @generated from field: int32 income_year_month = 3;
   */
  incomeYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 4;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 5;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 6;
   */
  occurredDay: number;

  /**
   * @generated from field: string race_place = 7;
   */
  racePlace: string;

  /**
   * @generated from field: string race_name = 8;
   */
  raceName: string;

  /**
   * @generated from field: string race_result = 9;
   */
  raceResult: string;

  /**
   * @generated from field: core.admin.horse_income.HorseIncomePrizeOrganizer organizer = 10;
   */
  organizer: HorseIncomePrizeOrganizer;

  /**
   * @generated from field: int32 main_prize_amount = 11;
   */
  mainPrizeAmount: number;

  /**
   * @generated from field: int32 appearance_fee = 12;
   */
  appearanceFee: number;

  /**
   * @generated from field: int32 withholding_tax = 13;
   */
  withholdingTax: number;

  /**
   * @generated from field: int32 commission_amount = 14;
   */
  commissionAmount: number;

  /**
   * @generated from field: string club_fee_rate = 15;
   */
  clubFeeRate: string;

  /**
   * @generated from field: string tax_rate = 16;
   */
  taxRate: string;

  /**
   * @generated from field: int32 total_prize_amount = 17;
   */
  totalPrizeAmount: number;

  /**
   * @generated from field: int32 club_fee_amount = 18;
   */
  clubFeeAmount: number;

  /**
   * @generated from field: int32 tax_amount = 19;
   */
  taxAmount: number;

  /**
   * @generated from field: int32 income_amount = 20;
   */
  incomeAmount: number;

  /**
   * @generated from field: string note = 21;
   */
  note: string;

  /**
   * @generated from field: bool closing = 22;
   */
  closing: boolean;

  /**
   * @generated from field: repeated core.admin.horse_income.HorseIncomePrizeAllowance allowances = 23;
   */
  allowances: HorseIncomePrizeAllowance[];
};

/**
 * Describes the message core.admin.horse_income.HorseIncomePrize.
 * Use `create(HorseIncomePrizeSchema)` to create a new message.
 */
export const HorseIncomePrizeSchema: GenMessage<HorseIncomePrize> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 6);

/**
 * 馬のその他収入情報
 *
 * @generated from message core.admin.horse_income.HorseIncomeOther
 */
export type HorseIncomeOther = Message<"core.admin.horse_income.HorseIncomeOther"> & {
  /**
   * @generated from field: int32 horse_income_other_id = 1;
   */
  horseIncomeOtherId: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * @generated from field: int32 income_year_month = 3;
   */
  incomeYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 4;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 5;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 6;
   */
  occurredDay: number;

  /**
   * @generated from field: core.admin.horse_income.HorseIncomeOtherName name = 7;
   */
  name: HorseIncomeOtherName;

  /**
   * @generated from field: string name_other = 8;
   */
  nameOther: string;

  /**
   * @generated from field: int32 amount = 9;
   */
  amount: number;

  /**
   * @generated from field: int32 sales_commission = 10;
   */
  salesCommission: number;

  /**
   * @generated from field: string other_fee_name = 11;
   */
  otherFeeName: string;

  /**
   * @generated from field: int32 other_fee_amount = 12;
   */
  otherFeeAmount: number;

  /**
   * @generated from field: string tax_rate = 13;
   */
  taxRate: string;

  /**
   * @generated from field: int32 tax_amount = 14;
   */
  taxAmount: number;

  /**
   * @generated from field: int32 income_amount = 15;
   */
  incomeAmount: number;

  /**
   * @generated from field: string note = 16;
   */
  note: string;

  /**
   * @generated from field: bool closing = 17;
   */
  closing: boolean;
};

/**
 * Describes the message core.admin.horse_income.HorseIncomeOther.
 * Use `create(HorseIncomeOtherSchema)` to create a new message.
 */
export const HorseIncomeOtherSchema: GenMessage<HorseIncomeOther> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 7);

/**
 * 収入一覧取得リクエスト
 *
 * @generated from message core.admin.horse_income.ListHorseIncomesRequest
 */
export type ListHorseIncomesRequest = Message<"core.admin.horse_income.ListHorseIncomesRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 limit = 2;
   */
  limit: number;

  /**
   * @generated from field: int32 offset = 3;
   */
  offset: number;
};

/**
 * Describes the message core.admin.horse_income.ListHorseIncomesRequest.
 * Use `create(ListHorseIncomesRequestSchema)` to create a new message.
 */
export const ListHorseIncomesRequestSchema: GenMessage<ListHorseIncomesRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 8);

/**
 * 収入一覧取得レスポンス
 *
 * @generated from message core.admin.horse_income.ListHorseIncomesResponse
 */
export type ListHorseIncomesResponse = Message<"core.admin.horse_income.ListHorseIncomesResponse"> & {
  /**
   * @generated from field: repeated core.admin.horse_income.HorseIncome horse_incomes = 1;
   */
  horseIncomes: HorseIncome[];

  /**
   * 総件数
   *
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * 総ページ数
   *
   * @generated from field: int32 total_pages = 3;
   */
  totalPages: number;
};

/**
 * Describes the message core.admin.horse_income.ListHorseIncomesResponse.
 * Use `create(ListHorseIncomesResponseSchema)` to create a new message.
 */
export const ListHorseIncomesResponseSchema: GenMessage<ListHorseIncomesResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 9);

/**
 * 全馬収入一覧取得リクエスト
 *
 * @generated from message core.admin.horse_income.ListAllHorseIncomesRequest
 */
export type ListAllHorseIncomesRequest = Message<"core.admin.horse_income.ListAllHorseIncomesRequest"> & {
  /**
   * @generated from field: int32 start_year = 1;
   */
  startYear: number;

  /**
   * @generated from field: int32 start_month = 2;
   */
  startMonth: number;

  /**
   * @generated from field: int32 start_day = 3;
   */
  startDay: number;

  /**
   * @generated from field: int32 end_year = 4;
   */
  endYear: number;

  /**
   * @generated from field: int32 end_month = 5;
   */
  endMonth: number;

  /**
   * @generated from field: int32 end_day = 6;
   */
  endDay: number;
};

/**
 * Describes the message core.admin.horse_income.ListAllHorseIncomesRequest.
 * Use `create(ListAllHorseIncomesRequestSchema)` to create a new message.
 */
export const ListAllHorseIncomesRequestSchema: GenMessage<ListAllHorseIncomesRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 10);

/**
 * 全馬収入一覧取得レスポンス
 *
 * @generated from message core.admin.horse_income.ListAllHorseIncomesResponse
 */
export type ListAllHorseIncomesResponse = Message<"core.admin.horse_income.ListAllHorseIncomesResponse"> & {
  /**
   * @generated from field: repeated core.admin.horse_income.HorseIncome horse_incomes = 1;
   */
  horseIncomes: HorseIncome[];
};

/**
 * Describes the message core.admin.horse_income.ListAllHorseIncomesResponse.
 * Use `create(ListAllHorseIncomesResponseSchema)` to create a new message.
 */
export const ListAllHorseIncomesResponseSchema: GenMessage<ListAllHorseIncomesResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 11);

/**
 * 賞金収入詳細取得リクエスト
 *
 * @generated from message core.admin.horse_income.GetHorseIncomePrizeRequest
 */
export type GetHorseIncomePrizeRequest = Message<"core.admin.horse_income.GetHorseIncomePrizeRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message core.admin.horse_income.GetHorseIncomePrizeRequest.
 * Use `create(GetHorseIncomePrizeRequestSchema)` to create a new message.
 */
export const GetHorseIncomePrizeRequestSchema: GenMessage<GetHorseIncomePrizeRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 12);

/**
 * 賞金収入詳細取得レスポンス
 *
 * @generated from message core.admin.horse_income.GetHorseIncomePrizeResponse
 */
export type GetHorseIncomePrizeResponse = Message<"core.admin.horse_income.GetHorseIncomePrizeResponse"> & {
  /**
   * @generated from field: core.admin.horse_income.HorseIncomePrize horse_income_prize = 1;
   */
  horseIncomePrize?: HorseIncomePrize;
};

/**
 * Describes the message core.admin.horse_income.GetHorseIncomePrizeResponse.
 * Use `create(GetHorseIncomePrizeResponseSchema)` to create a new message.
 */
export const GetHorseIncomePrizeResponseSchema: GenMessage<GetHorseIncomePrizeResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 13);

/**
 * 賞金収入作成リクエスト
 *
 * @generated from message core.admin.horse_income.CreateHorseIncomePrizeRequest
 */
export type CreateHorseIncomePrizeRequest = Message<"core.admin.horse_income.CreateHorseIncomePrizeRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 income_year_month = 2;
   */
  incomeYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 3;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 4;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 5;
   */
  occurredDay: number;

  /**
   * @generated from field: string race_place = 6;
   */
  racePlace: string;

  /**
   * @generated from field: string race_name = 7;
   */
  raceName: string;

  /**
   * @generated from field: string race_result = 8;
   */
  raceResult: string;

  /**
   * @generated from field: core.admin.horse_income.HorseIncomePrizeOrganizer organizer = 9;
   */
  organizer: HorseIncomePrizeOrganizer;

  /**
   * @generated from field: int32 main_prize_amount = 10;
   */
  mainPrizeAmount: number;

  /**
   * @generated from field: int32 appearance_fee = 11;
   */
  appearanceFee: number;

  /**
   * @generated from field: int32 withholding_tax = 12;
   */
  withholdingTax: number;

  /**
   * @generated from field: int32 commission_amount = 13;
   */
  commissionAmount: number;

  /**
   * @generated from field: string club_fee_rate = 14;
   */
  clubFeeRate: string;

  /**
   * @generated from field: string tax_rate = 15;
   */
  taxRate: string;

  /**
   * @generated from field: int32 total_prize_amount = 16;
   */
  totalPrizeAmount: number;

  /**
   * @generated from field: int32 club_fee_amount = 17;
   */
  clubFeeAmount: number;

  /**
   * @generated from field: int32 tax_amount = 18;
   */
  taxAmount: number;

  /**
   * @generated from field: int32 income_amount = 19;
   */
  incomeAmount: number;

  /**
   * @generated from field: string note = 20;
   */
  note: string;

  /**
   * @generated from field: repeated core.admin.horse_income.HorseIncomePrizeAllowanceRequest allowances = 21;
   */
  allowances: HorseIncomePrizeAllowanceRequest[];
};

/**
 * Describes the message core.admin.horse_income.CreateHorseIncomePrizeRequest.
 * Use `create(CreateHorseIncomePrizeRequestSchema)` to create a new message.
 */
export const CreateHorseIncomePrizeRequestSchema: GenMessage<CreateHorseIncomePrizeRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 14);

/**
 * 賞金収入作成レスポンス
 *
 * @generated from message core.admin.horse_income.CreateHorseIncomePrizeResponse
 */
export type CreateHorseIncomePrizeResponse = Message<"core.admin.horse_income.CreateHorseIncomePrizeResponse"> & {
};

/**
 * Describes the message core.admin.horse_income.CreateHorseIncomePrizeResponse.
 * Use `create(CreateHorseIncomePrizeResponseSchema)` to create a new message.
 */
export const CreateHorseIncomePrizeResponseSchema: GenMessage<CreateHorseIncomePrizeResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 15);

/**
 * 賞金収入更新リクエスト
 *
 * @generated from message core.admin.horse_income.UpdateHorseIncomePrizeRequest
 */
export type UpdateHorseIncomePrizeRequest = Message<"core.admin.horse_income.UpdateHorseIncomePrizeRequest"> & {
  /**
   * @generated from field: int32 horse_income_prize_id = 1;
   */
  horseIncomePrizeId: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * @generated from field: int32 income_year_month = 3;
   */
  incomeYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 4;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 5;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 6;
   */
  occurredDay: number;

  /**
   * @generated from field: string race_place = 7;
   */
  racePlace: string;

  /**
   * @generated from field: string race_name = 8;
   */
  raceName: string;

  /**
   * @generated from field: string race_result = 9;
   */
  raceResult: string;

  /**
   * @generated from field: core.admin.horse_income.HorseIncomePrizeOrganizer organizer = 10;
   */
  organizer: HorseIncomePrizeOrganizer;

  /**
   * @generated from field: int32 main_prize_amount = 11;
   */
  mainPrizeAmount: number;

  /**
   * @generated from field: int32 appearance_fee = 12;
   */
  appearanceFee: number;

  /**
   * @generated from field: int32 withholding_tax = 13;
   */
  withholdingTax: number;

  /**
   * @generated from field: int32 commission_amount = 14;
   */
  commissionAmount: number;

  /**
   * @generated from field: string club_fee_rate = 15;
   */
  clubFeeRate: string;

  /**
   * @generated from field: string tax_rate = 16;
   */
  taxRate: string;

  /**
   * @generated from field: int32 total_prize_amount = 17;
   */
  totalPrizeAmount: number;

  /**
   * @generated from field: int32 club_fee_amount = 18;
   */
  clubFeeAmount: number;

  /**
   * @generated from field: int32 tax_amount = 19;
   */
  taxAmount: number;

  /**
   * @generated from field: int32 income_amount = 20;
   */
  incomeAmount: number;

  /**
   * @generated from field: string note = 21;
   */
  note: string;

  /**
   * @generated from field: repeated core.admin.horse_income.HorseIncomePrizeAllowanceRequest allowances = 22;
   */
  allowances: HorseIncomePrizeAllowanceRequest[];
};

/**
 * Describes the message core.admin.horse_income.UpdateHorseIncomePrizeRequest.
 * Use `create(UpdateHorseIncomePrizeRequestSchema)` to create a new message.
 */
export const UpdateHorseIncomePrizeRequestSchema: GenMessage<UpdateHorseIncomePrizeRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 16);

/**
 * 賞金収入更新レスポンス
 *
 * @generated from message core.admin.horse_income.UpdateHorseIncomePrizeResponse
 */
export type UpdateHorseIncomePrizeResponse = Message<"core.admin.horse_income.UpdateHorseIncomePrizeResponse"> & {
};

/**
 * Describes the message core.admin.horse_income.UpdateHorseIncomePrizeResponse.
 * Use `create(UpdateHorseIncomePrizeResponseSchema)` to create a new message.
 */
export const UpdateHorseIncomePrizeResponseSchema: GenMessage<UpdateHorseIncomePrizeResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 17);

/**
 * 賞金収入削除リクエスト
 *
 * @generated from message core.admin.horse_income.DeleteHorseIncomePrizeRequest
 */
export type DeleteHorseIncomePrizeRequest = Message<"core.admin.horse_income.DeleteHorseIncomePrizeRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message core.admin.horse_income.DeleteHorseIncomePrizeRequest.
 * Use `create(DeleteHorseIncomePrizeRequestSchema)` to create a new message.
 */
export const DeleteHorseIncomePrizeRequestSchema: GenMessage<DeleteHorseIncomePrizeRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 18);

/**
 * 賞金収入削除レスポンス
 *
 * @generated from message core.admin.horse_income.DeleteHorseIncomePrizeResponse
 */
export type DeleteHorseIncomePrizeResponse = Message<"core.admin.horse_income.DeleteHorseIncomePrizeResponse"> & {
};

/**
 * Describes the message core.admin.horse_income.DeleteHorseIncomePrizeResponse.
 * Use `create(DeleteHorseIncomePrizeResponseSchema)` to create a new message.
 */
export const DeleteHorseIncomePrizeResponseSchema: GenMessage<DeleteHorseIncomePrizeResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 19);

/**
 * その他収入詳細取得リクエスト
 *
 * @generated from message core.admin.horse_income.GetHorseIncomeOtherRequest
 */
export type GetHorseIncomeOtherRequest = Message<"core.admin.horse_income.GetHorseIncomeOtherRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message core.admin.horse_income.GetHorseIncomeOtherRequest.
 * Use `create(GetHorseIncomeOtherRequestSchema)` to create a new message.
 */
export const GetHorseIncomeOtherRequestSchema: GenMessage<GetHorseIncomeOtherRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 20);

/**
 * その他収入詳細取得レスポンス
 *
 * @generated from message core.admin.horse_income.GetHorseIncomeOtherResponse
 */
export type GetHorseIncomeOtherResponse = Message<"core.admin.horse_income.GetHorseIncomeOtherResponse"> & {
  /**
   * @generated from field: core.admin.horse_income.HorseIncomeOther horse_income_other = 1;
   */
  horseIncomeOther?: HorseIncomeOther;
};

/**
 * Describes the message core.admin.horse_income.GetHorseIncomeOtherResponse.
 * Use `create(GetHorseIncomeOtherResponseSchema)` to create a new message.
 */
export const GetHorseIncomeOtherResponseSchema: GenMessage<GetHorseIncomeOtherResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 21);

/**
 * その他収入作成リクエスト
 *
 * @generated from message core.admin.horse_income.CreateHorseIncomeOtherRequest
 */
export type CreateHorseIncomeOtherRequest = Message<"core.admin.horse_income.CreateHorseIncomeOtherRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 income_year_month = 2;
   */
  incomeYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 3;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 4;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 5;
   */
  occurredDay: number;

  /**
   * @generated from field: core.admin.horse_income.HorseIncomeOtherName name = 6;
   */
  name: HorseIncomeOtherName;

  /**
   * @generated from field: string name_other = 7;
   */
  nameOther: string;

  /**
   * @generated from field: int32 amount = 8;
   */
  amount: number;

  /**
   * @generated from field: int32 sales_commission = 9;
   */
  salesCommission: number;

  /**
   * @generated from field: string other_fee_name = 10;
   */
  otherFeeName: string;

  /**
   * @generated from field: int32 other_fee_amount = 11;
   */
  otherFeeAmount: number;

  /**
   * @generated from field: string tax_rate = 12;
   */
  taxRate: string;

  /**
   * @generated from field: int32 tax_amount = 13;
   */
  taxAmount: number;

  /**
   * @generated from field: int32 income_amount = 14;
   */
  incomeAmount: number;

  /**
   * @generated from field: string note = 15;
   */
  note: string;
};

/**
 * Describes the message core.admin.horse_income.CreateHorseIncomeOtherRequest.
 * Use `create(CreateHorseIncomeOtherRequestSchema)` to create a new message.
 */
export const CreateHorseIncomeOtherRequestSchema: GenMessage<CreateHorseIncomeOtherRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 22);

/**
 * その他収入作成レスポンス
 *
 * @generated from message core.admin.horse_income.CreateHorseIncomeOtherResponse
 */
export type CreateHorseIncomeOtherResponse = Message<"core.admin.horse_income.CreateHorseIncomeOtherResponse"> & {
};

/**
 * Describes the message core.admin.horse_income.CreateHorseIncomeOtherResponse.
 * Use `create(CreateHorseIncomeOtherResponseSchema)` to create a new message.
 */
export const CreateHorseIncomeOtherResponseSchema: GenMessage<CreateHorseIncomeOtherResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 23);

/**
 * その他収入更新リクエスト
 *
 * @generated from message core.admin.horse_income.UpdateHorseIncomeOtherRequest
 */
export type UpdateHorseIncomeOtherRequest = Message<"core.admin.horse_income.UpdateHorseIncomeOtherRequest"> & {
  /**
   * @generated from field: int32 horse_income_other_id = 1;
   */
  horseIncomeOtherId: number;

  /**
   * @generated from field: int32 horse_id = 2;
   */
  horseId: number;

  /**
   * @generated from field: int32 income_year_month = 3;
   */
  incomeYearMonth: number;

  /**
   * @generated from field: int32 occurred_year = 4;
   */
  occurredYear: number;

  /**
   * @generated from field: int32 occurred_month = 5;
   */
  occurredMonth: number;

  /**
   * @generated from field: int32 occurred_day = 6;
   */
  occurredDay: number;

  /**
   * @generated from field: core.admin.horse_income.HorseIncomeOtherName name = 7;
   */
  name: HorseIncomeOtherName;

  /**
   * @generated from field: string name_other = 8;
   */
  nameOther: string;

  /**
   * @generated from field: int32 amount = 9;
   */
  amount: number;

  /**
   * @generated from field: int32 sales_commission = 10;
   */
  salesCommission: number;

  /**
   * @generated from field: string other_fee_name = 11;
   */
  otherFeeName: string;

  /**
   * @generated from field: int32 other_fee_amount = 12;
   */
  otherFeeAmount: number;

  /**
   * @generated from field: string tax_rate = 13;
   */
  taxRate: string;

  /**
   * @generated from field: int32 tax_amount = 14;
   */
  taxAmount: number;

  /**
   * @generated from field: int32 income_amount = 15;
   */
  incomeAmount: number;

  /**
   * @generated from field: string note = 16;
   */
  note: string;
};

/**
 * Describes the message core.admin.horse_income.UpdateHorseIncomeOtherRequest.
 * Use `create(UpdateHorseIncomeOtherRequestSchema)` to create a new message.
 */
export const UpdateHorseIncomeOtherRequestSchema: GenMessage<UpdateHorseIncomeOtherRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 24);

/**
 * その他収入更新レスポンス
 *
 * @generated from message core.admin.horse_income.UpdateHorseIncomeOtherResponse
 */
export type UpdateHorseIncomeOtherResponse = Message<"core.admin.horse_income.UpdateHorseIncomeOtherResponse"> & {
};

/**
 * Describes the message core.admin.horse_income.UpdateHorseIncomeOtherResponse.
 * Use `create(UpdateHorseIncomeOtherResponseSchema)` to create a new message.
 */
export const UpdateHorseIncomeOtherResponseSchema: GenMessage<UpdateHorseIncomeOtherResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 25);

/**
 * その他収入削除リクエスト
 *
 * @generated from message core.admin.horse_income.DeleteHorseIncomeOtherRequest
 */
export type DeleteHorseIncomeOtherRequest = Message<"core.admin.horse_income.DeleteHorseIncomeOtherRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message core.admin.horse_income.DeleteHorseIncomeOtherRequest.
 * Use `create(DeleteHorseIncomeOtherRequestSchema)` to create a new message.
 */
export const DeleteHorseIncomeOtherRequestSchema: GenMessage<DeleteHorseIncomeOtherRequest> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 26);

/**
 * その他収入削除レスポンス
 *
 * @generated from message core.admin.horse_income.DeleteHorseIncomeOtherResponse
 */
export type DeleteHorseIncomeOtherResponse = Message<"core.admin.horse_income.DeleteHorseIncomeOtherResponse"> & {
};

/**
 * Describes the message core.admin.horse_income.DeleteHorseIncomeOtherResponse.
 * Use `create(DeleteHorseIncomeOtherResponseSchema)` to create a new message.
 */
export const DeleteHorseIncomeOtherResponseSchema: GenMessage<DeleteHorseIncomeOtherResponse> = /*@__PURE__*/
  messageDesc(file_horse_income_service, 27);

/**
 * 収入種別
 *
 * @generated from enum core.admin.horse_income.IncomeType
 */
export enum IncomeType {
  /**
   * @generated from enum value: INCOME_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 賞金収入
   *
   * @generated from enum value: INCOME_TYPE_PRIZE = 1;
   */
  PRIZE = 1,

  /**
   * その他収入
   *
   * @generated from enum value: INCOME_TYPE_OTHER = 2;
   */
  OTHER = 2,
}

/**
 * Describes the enum core.admin.horse_income.IncomeType.
 */
export const IncomeTypeSchema: GenEnum<IncomeType> = /*@__PURE__*/
  enumDesc(file_horse_income_service, 0);

/**
 * 主催者
 *
 * @generated from enum core.admin.horse_income.HorseIncomePrizeOrganizer
 */
export enum HorseIncomePrizeOrganizer {
  /**
   * @generated from enum value: HORSE_INCOME_PRIZE_ORGANIZER_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: HORSE_INCOME_PRIZE_ORGANIZER_JRA = 1;
   */
  JRA = 1,

  /**
   * @generated from enum value: HORSE_INCOME_PRIZE_ORGANIZER_OTHER = 2;
   */
  OTHER = 2,
}

/**
 * Describes the enum core.admin.horse_income.HorseIncomePrizeOrganizer.
 */
export const HorseIncomePrizeOrganizerSchema: GenEnum<HorseIncomePrizeOrganizer> = /*@__PURE__*/
  enumDesc(file_horse_income_service, 1);

/**
 * その他収入名目
 *
 * @generated from enum core.admin.horse_income.HorseIncomeOtherName
 */
export enum HorseIncomeOtherName {
  /**
   * @generated from enum value: HORSE_INCOME_OTHER_NAME_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: HORSE_INCOME_OTHER_NAME_SALES_DIVIDEND = 1;
   */
  SALES_DIVIDEND = 1,

  /**
   * @generated from enum value: HORSE_INCOME_OTHER_NAME_INSURANCE = 2;
   */
  INSURANCE = 2,

  /**
   * @generated from enum value: HORSE_INCOME_OTHER_NAME_SYMPATHY = 3;
   */
  SYMPATHY = 3,

  /**
   * @generated from enum value: HORSE_INCOME_OTHER_NAME_GRANT = 4;
   */
  GRANT = 4,

  /**
   * @generated from enum value: HORSE_INCOME_OTHER_NAME_OTHER = 5;
   */
  OTHER = 5,
}

/**
 * Describes the enum core.admin.horse_income.HorseIncomeOtherName.
 */
export const HorseIncomeOtherNameSchema: GenEnum<HorseIncomeOtherName> = /*@__PURE__*/
  enumDesc(file_horse_income_service, 2);

/**
 * 馬の収入サービス
 *
 * @generated from service core.admin.horse_income.HorseIncomeService
 */
export const HorseIncomeService: GenService<{
  /**
   * 馬の収入一覧を取得（賞金収入とその他収入をマージ）
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.ListHorseIncomes
   */
  listHorseIncomes: {
    methodKind: "unary";
    input: typeof ListHorseIncomesRequestSchema;
    output: typeof ListHorseIncomesResponseSchema;
  },
  /**
   * 全馬の収入一覧を取得（賞金収入とその他収入をマージ）
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.ListAllHorseIncomes
   */
  listAllHorseIncomes: {
    methodKind: "unary";
    input: typeof ListAllHorseIncomesRequestSchema;
    output: typeof ListAllHorseIncomesResponseSchema;
  },
  /**
   * 馬の賞金収入詳細を取得
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.GetHorseIncomePrize
   */
  getHorseIncomePrize: {
    methodKind: "unary";
    input: typeof GetHorseIncomePrizeRequestSchema;
    output: typeof GetHorseIncomePrizeResponseSchema;
  },
  /**
   * 馬の賞金収入を作成
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.CreateHorseIncomePrize
   */
  createHorseIncomePrize: {
    methodKind: "unary";
    input: typeof CreateHorseIncomePrizeRequestSchema;
    output: typeof CreateHorseIncomePrizeResponseSchema;
  },
  /**
   * 馬の賞金収入を更新
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.UpdateHorseIncomePrize
   */
  updateHorseIncomePrize: {
    methodKind: "unary";
    input: typeof UpdateHorseIncomePrizeRequestSchema;
    output: typeof UpdateHorseIncomePrizeResponseSchema;
  },
  /**
   * 馬の賞金収入を削除
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.DeleteHorseIncomePrize
   */
  deleteHorseIncomePrize: {
    methodKind: "unary";
    input: typeof DeleteHorseIncomePrizeRequestSchema;
    output: typeof DeleteHorseIncomePrizeResponseSchema;
  },
  /**
   * 馬のその他収入詳細を取得
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.GetHorseIncomeOther
   */
  getHorseIncomeOther: {
    methodKind: "unary";
    input: typeof GetHorseIncomeOtherRequestSchema;
    output: typeof GetHorseIncomeOtherResponseSchema;
  },
  /**
   * 馬のその他収入を作成
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.CreateHorseIncomeOther
   */
  createHorseIncomeOther: {
    methodKind: "unary";
    input: typeof CreateHorseIncomeOtherRequestSchema;
    output: typeof CreateHorseIncomeOtherResponseSchema;
  },
  /**
   * 馬のその他収入を更新
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.UpdateHorseIncomeOther
   */
  updateHorseIncomeOther: {
    methodKind: "unary";
    input: typeof UpdateHorseIncomeOtherRequestSchema;
    output: typeof UpdateHorseIncomeOtherResponseSchema;
  },
  /**
   * 馬のその他収入を削除
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.DeleteHorseIncomeOther
   */
  deleteHorseIncomeOther: {
    methodKind: "unary";
    input: typeof DeleteHorseIncomeOtherRequestSchema;
    output: typeof DeleteHorseIncomeOtherResponseSchema;
  },
  /**
   * 賞金収入手当名一覧を取得
   *
   * @generated from rpc core.admin.horse_income.HorseIncomeService.ListHorseIncomePrizeAllowanceNames
   */
  listHorseIncomePrizeAllowanceNames: {
    methodKind: "unary";
    input: typeof ListHorseIncomePrizeAllowanceNamesRequestSchema;
    output: typeof ListHorseIncomePrizeAllowanceNamesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_income_service, 0);

