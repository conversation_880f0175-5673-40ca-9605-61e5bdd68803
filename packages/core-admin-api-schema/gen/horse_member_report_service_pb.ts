// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file horse_member_report_service.proto (package hami.core.admin.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_member_report_service.proto.
 */
export const file_horse_member_report_service: GenFile = /*@__PURE__*/
  fileDesc("CiFob3JzZV9tZW1iZXJfcmVwb3J0X3NlcnZpY2UucHJvdG8SEmhhbWkuY29yZS5hZG1pbi52MSIjCiFHZW5lcmF0ZUhvcnNlTWVtYmVyTGlzdFBkZlJlcXVlc3QikwEKIkdlbmVyYXRlSG9yc2VNZW1iZXJMaXN0UGRmUmVzcG9uc2USFAoMcGRmX2ZpbGVfa2V5GAEgASgJEhQKDGRvd25sb2FkX3VybBgCIAEoCRIUCgx0b3RhbF9ob3JzZXMYAyABKAUSFQoNdG90YWxfbWVtYmVycxgEIAEoBRIUCgxnZW5lcmF0ZWRfYXQYBSABKAMiPwonR2V0SG9yc2VNZW1iZXJMaXN0UGRmRG93bmxvYWRVcmxSZXF1ZXN0EhQKDHBkZl9maWxlX2tleRgBIAEoCSJUCihHZXRIb3JzZU1lbWJlckxpc3RQZGZEb3dubG9hZFVybFJlc3BvbnNlEhQKDGRvd25sb2FkX3VybBgBIAEoCRISCgpleHBpcmVzX2F0GAIgASgDMsgCChhIb3JzZU1lbWJlclJlcG9ydFNlcnZpY2USiwEKGkdlbmVyYXRlSG9yc2VNZW1iZXJMaXN0UGRmEjUuaGFtaS5jb3JlLmFkbWluLnYxLkdlbmVyYXRlSG9yc2VNZW1iZXJMaXN0UGRmUmVxdWVzdBo2LmhhbWkuY29yZS5hZG1pbi52MS5HZW5lcmF0ZUhvcnNlTWVtYmVyTGlzdFBkZlJlc3BvbnNlEp0BCiBHZXRIb3JzZU1lbWJlckxpc3RQZGZEb3dubG9hZFVybBI7LmhhbWkuY29yZS5hZG1pbi52MS5HZXRIb3JzZU1lbWJlckxpc3RQZGZEb3dubG9hZFVybFJlcXVlc3QaPC5oYW1pLmNvcmUuYWRtaW4udjEuR2V0SG9yc2VNZW1iZXJMaXN0UGRmRG93bmxvYWRVcmxSZXNwb25zZWIGcHJvdG8z");

/**
 * クラブ所有馬別会員一覧PDF生成リクエスト
 *
 * 全頭一括のみのため、パラメーターなし
 *
 * @generated from message hami.core.admin.v1.GenerateHorseMemberListPdfRequest
 */
export type GenerateHorseMemberListPdfRequest = Message<"hami.core.admin.v1.GenerateHorseMemberListPdfRequest"> & {
};

/**
 * Describes the message hami.core.admin.v1.GenerateHorseMemberListPdfRequest.
 * Use `create(GenerateHorseMemberListPdfRequestSchema)` to create a new message.
 */
export const GenerateHorseMemberListPdfRequestSchema: GenMessage<GenerateHorseMemberListPdfRequest> = /*@__PURE__*/
  messageDesc(file_horse_member_report_service, 0);

/**
 * クラブ所有馬別会員一覧PDF生成レスポンス
 *
 * @generated from message hami.core.admin.v1.GenerateHorseMemberListPdfResponse
 */
export type GenerateHorseMemberListPdfResponse = Message<"hami.core.admin.v1.GenerateHorseMemberListPdfResponse"> & {
  /**
   * 生成されたPDFのファイルキー
   *
   * @generated from field: string pdf_file_key = 1;
   */
  pdfFileKey: string;

  /**
   * ダウンロードURL（署名付き、1時間有効）
   *
   * @generated from field: string download_url = 2;
   */
  downloadUrl: string;

  /**
   * 対象馬数
   *
   * @generated from field: int32 total_horses = 3;
   */
  totalHorses: number;

  /**
   * 対象会員数
   *
   * @generated from field: int32 total_members = 4;
   */
  totalMembers: number;

  /**
   * 生成日時（Unix timestamp）
   *
   * @generated from field: int64 generated_at = 5;
   */
  generatedAt: bigint;
};

/**
 * Describes the message hami.core.admin.v1.GenerateHorseMemberListPdfResponse.
 * Use `create(GenerateHorseMemberListPdfResponseSchema)` to create a new message.
 */
export const GenerateHorseMemberListPdfResponseSchema: GenMessage<GenerateHorseMemberListPdfResponse> = /*@__PURE__*/
  messageDesc(file_horse_member_report_service, 1);

/**
 * 生成済みPDFダウンロードURL取得リクエスト
 *
 * @generated from message hami.core.admin.v1.GetHorseMemberListPdfDownloadUrlRequest
 */
export type GetHorseMemberListPdfDownloadUrlRequest = Message<"hami.core.admin.v1.GetHorseMemberListPdfDownloadUrlRequest"> & {
  /**
   * PDFファイルキー
   *
   * @generated from field: string pdf_file_key = 1;
   */
  pdfFileKey: string;
};

/**
 * Describes the message hami.core.admin.v1.GetHorseMemberListPdfDownloadUrlRequest.
 * Use `create(GetHorseMemberListPdfDownloadUrlRequestSchema)` to create a new message.
 */
export const GetHorseMemberListPdfDownloadUrlRequestSchema: GenMessage<GetHorseMemberListPdfDownloadUrlRequest> = /*@__PURE__*/
  messageDesc(file_horse_member_report_service, 2);

/**
 * 生成済みPDFダウンロードURL取得レスポンス
 *
 * @generated from message hami.core.admin.v1.GetHorseMemberListPdfDownloadUrlResponse
 */
export type GetHorseMemberListPdfDownloadUrlResponse = Message<"hami.core.admin.v1.GetHorseMemberListPdfDownloadUrlResponse"> & {
  /**
   * ダウンロードURL（署名付き、1時間有効）
   *
   * @generated from field: string download_url = 1;
   */
  downloadUrl: string;

  /**
   * URL有効期限（Unix timestamp）
   *
   * @generated from field: int64 expires_at = 2;
   */
  expiresAt: bigint;
};

/**
 * Describes the message hami.core.admin.v1.GetHorseMemberListPdfDownloadUrlResponse.
 * Use `create(GetHorseMemberListPdfDownloadUrlResponseSchema)` to create a new message.
 */
export const GetHorseMemberListPdfDownloadUrlResponseSchema: GenMessage<GetHorseMemberListPdfDownloadUrlResponse> = /*@__PURE__*/
  messageDesc(file_horse_member_report_service, 3);

/**
 * クラブ所有馬別会員一覧帳票サービス
 *
 * @generated from service hami.core.admin.v1.HorseMemberReportService
 */
export const HorseMemberReportService: GenService<{
  /**
   * クラブ所有馬別会員一覧PDF生成
   *
   * @generated from rpc hami.core.admin.v1.HorseMemberReportService.GenerateHorseMemberListPdf
   */
  generateHorseMemberListPdf: {
    methodKind: "unary";
    input: typeof GenerateHorseMemberListPdfRequestSchema;
    output: typeof GenerateHorseMemberListPdfResponseSchema;
  },
  /**
   * 生成済みPDFダウンロードURL取得
   *
   * @generated from rpc hami.core.admin.v1.HorseMemberReportService.GetHorseMemberListPdfDownloadUrl
   */
  getHorseMemberListPdfDownloadUrl: {
    methodKind: "unary";
    input: typeof GetHorseMemberListPdfDownloadUrlRequestSchema;
    output: typeof GetHorseMemberListPdfDownloadUrlResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_member_report_service, 0);

