syntax = "proto3";
package hami.frontend.v1;

// 会員向け請求・支払いサービス
service MemberClaimAndPayService {
  // 請求・支払い一覧取得
  rpc ListMemberClaimAndPays(ListMemberClaimAndPaysRequest)
      returns (ListMemberClaimAndPaysResponse);
  
  // 明細書PDFダウンロードURL取得
  rpc RequestStatementPdfDownloadUrl(RequestStatementPdfDownloadUrlRequest)
      returns (RequestStatementPdfDownloadUrlResponse);
}

// 会員向け請求・支払い情報
message MemberClaimAndPay {
  int32 member_claim_and_pay_id = 1; // 請求と支払いID
  int32 member_id = 2;               // 会員ID
  int32 occurred_year = 3;           // 発生日（年）
  int32 occurred_month = 4;          // 発生日（月）
  int32 occurred_day = 5;            // 発生日（日）
  int32 claim_amount = 6;            // 請求金額
  int32 pay_amount = 7;              // 支払い金額
  optional string statement_file_key = 8; // 明細書ファイルキー
  optional string statement_issued_at = 9; // 明細書発行日時（ISO 8601形式）
}

// 請求・支払い一覧取得リクエスト
message ListMemberClaimAndPaysRequest {
  // リクエストパラメータなし（認証された会員の全データを取得）
}

// 請求・支払い一覧取得レスポンス
message ListMemberClaimAndPaysResponse {
  repeated MemberClaimAndPay member_claim_and_pays = 1; // 請求・支払い一覧
}

// 明細書PDFダウンロードURL取得リクエスト
message RequestStatementPdfDownloadUrlRequest {
  string statement_file_key = 1; // 明細書ファイルキー
}

// 明細書PDFダウンロードURL取得レスポンス
message RequestStatementPdfDownloadUrlResponse {
  string download_url = 1; // ダウンロードURL
}
