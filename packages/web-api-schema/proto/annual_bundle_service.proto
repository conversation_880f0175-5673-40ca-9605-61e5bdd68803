syntax = "proto3";
package hami.frontend.v1;

// 年度バンドル（フロントエンド向け）
service AnnualBundleService {
  // 年度を指定して年度バンドルを一覧取得（公開中のみ、0件可）
  rpc ListAnnualBundlesByFiscalYear(ListAnnualBundlesByFiscalYearRequest)
      returns (ListAnnualBundlesByFiscalYearResponse);
}

// 年度バンドルの公開ステータス
enum AnnualBundlePublishStatus {
  ANNUAL_BUNDLE_PUBLISH_STATUS_UNSPECIFIED = 0;
  AB_PUBLIC = 1;   // 公開
  AB_PRIVATE = 2;  // 非公開
}

// 年度バンドルの募集ステータス
enum AnnualBundleRecruitmentStatus {
  ANNUAL_BUNDLE_RECRUITMENT_STATUS_UNSPECIFIED = 0;
  AB_UPCOMING = 1; // 募集前
  AB_ACTIVE = 2;   // 募集中
  AB_CLOSED = 3;   // 募集終了
  AB_FULL = 4;     // 満口
}

message ListAnnualBundlesByFiscalYearRequest { int32 fiscal_year = 1; }

message ListAnnualBundlesByFiscalYearResponse { repeated AnnualBundle bundles = 1; }

message AnnualBundle {
  int32 annual_bundle_id = 1;
  int32 fiscal_year = 2;
  string name = 3;
  int32 shares = 4;
  AnnualBundlePublishStatus publish_status = 5;
  AnnualBundleRecruitmentStatus recruitment_status = 6;
  repeated AnnualBundleHorseRelation horses = 7; // 表示用の最小限の紐付け情報
  // バンドル一口あたりの金額（= 各馬 amount_total / shares_total の合計）
  int32 per_share_amount = 8;
}

message AnnualBundleHorseRelation {
  int32 horse_id = 1;
  string horse_name = 2;
  string recruitment_name = 3;
  int32 recruitment_no = 4;
}


