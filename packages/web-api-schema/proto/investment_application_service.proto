syntax = "proto3";
package hami.frontend.v1;

// 出資申込アイテム
message InvestmentApplicationItem {
  optional int32 recruitment_year = 1;
  optional int32 recruitment_no = 2;
  int32 requested_number = 3;             // 希望口数
  bool reject_partial_allocation = 4;     // 希望口数に満たない場合に出資を希望しないフラグ
  bool installment_payment = 5;           // 分割払いフラグ
  optional int32 annual_bundle_id = 6;    // 年度バンドルID
}

// 出資申込作成リクエスト
message CreateInvestmentApplicationRequest {
  repeated InvestmentApplicationItem items = 1;  // 出資申込アイテムのリスト
  bool is_whole = 2;
}

// 出資申込作成レスポンス
message CreateInvestmentApplicationResponse {
}

// 出資申込サービス
service InvestmentApplicationService {
  // 出資申込作成
  rpc CreateInvestmentApplication(CreateInvestmentApplicationRequest)
      returns (CreateInvestmentApplicationResponse);
} 