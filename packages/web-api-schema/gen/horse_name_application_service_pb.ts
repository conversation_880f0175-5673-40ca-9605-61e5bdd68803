// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file horse_name_application_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_name_application_service.proto.
 */
export const file_horse_name_application_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * 応募可能キャンペーン一覧取得リクエスト
 *
 * 空のリクエスト - ログインユーザーが出資契約している馬のキャンペーンのみ取得
 *
 * @generated from message hami.frontend.v1.ListHorseNameApplicationCampaignsRequest
 */
export type ListHorseNameApplicationCampaignsRequest = Message<"hami.frontend.v1.ListHorseNameApplicationCampaignsRequest"> & {
};

/**
 * Describes the message hami.frontend.v1.ListHorseNameApplicationCampaignsRequest.
 * Use `create(ListHorseNameApplicationCampaignsRequestSchema)` to create a new message.
 */
export const ListHorseNameApplicationCampaignsRequestSchema: GenMessage<ListHorseNameApplicationCampaignsRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 0);

/**
 * キャンペーンアイテム
 *
 * @generated from message hami.frontend.v1.HorseNameApplicationCampaignItem
 */
export type HorseNameApplicationCampaignItem = Message<"hami.frontend.v1.HorseNameApplicationCampaignItem"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * 馬ID
   *
   * @generated from field: int64 horse_id = 2;
   */
  horseId: bigint;

  /**
   * 馬名
   *
   * @generated from field: string horse_name = 3;
   */
  horseName: string;

  /**
   * 募集年
   *
   * @generated from field: int32 recruitment_year = 4;
   */
  recruitmentYear: number;

  /**
   * 募集番号
   *
   * @generated from field: int32 recruitment_no = 5;
   */
  recruitmentNo: number;

  /**
   * 応募開始日時
   *
   * @generated from field: google.protobuf.Timestamp application_start_at = 6;
   */
  applicationStartAt?: Timestamp;

  /**
   * 応募終了日時
   *
   * @generated from field: google.protobuf.Timestamp application_end_at = 7;
   */
  applicationEndAt?: Timestamp;

  /**
   * キャンペーンステータス
   *
   * @generated from field: hami.frontend.v1.HorseNameApplicationCampaignStatus status = 8;
   */
  status: HorseNameApplicationCampaignStatus;

  /**
   * 備考
   *
   * @generated from field: optional string note = 9;
   */
  note?: string;

  /**
   * 自分の応募があるかどうか
   *
   * @generated from field: bool has_my_application = 10;
   */
  hasMyApplication: boolean;
};

/**
 * Describes the message hami.frontend.v1.HorseNameApplicationCampaignItem.
 * Use `create(HorseNameApplicationCampaignItemSchema)` to create a new message.
 */
export const HorseNameApplicationCampaignItemSchema: GenMessage<HorseNameApplicationCampaignItem> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 1);

/**
 * 応募可能キャンペーン一覧取得レスポンス
 *
 * @generated from message hami.frontend.v1.ListHorseNameApplicationCampaignsResponse
 */
export type ListHorseNameApplicationCampaignsResponse = Message<"hami.frontend.v1.ListHorseNameApplicationCampaignsResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.HorseNameApplicationCampaignItem campaigns = 1;
   */
  campaigns: HorseNameApplicationCampaignItem[];
};

/**
 * Describes the message hami.frontend.v1.ListHorseNameApplicationCampaignsResponse.
 * Use `create(ListHorseNameApplicationCampaignsResponseSchema)` to create a new message.
 */
export const ListHorseNameApplicationCampaignsResponseSchema: GenMessage<ListHorseNameApplicationCampaignsResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 2);

/**
 * キャンペーン詳細取得リクエスト
 *
 * @generated from message hami.frontend.v1.GetHorseNameApplicationCampaignDetailRequest
 */
export type GetHorseNameApplicationCampaignDetailRequest = Message<"hami.frontend.v1.GetHorseNameApplicationCampaignDetailRequest"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;
};

/**
 * Describes the message hami.frontend.v1.GetHorseNameApplicationCampaignDetailRequest.
 * Use `create(GetHorseNameApplicationCampaignDetailRequestSchema)` to create a new message.
 */
export const GetHorseNameApplicationCampaignDetailRequestSchema: GenMessage<GetHorseNameApplicationCampaignDetailRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 3);

/**
 * キャンペーン詳細取得レスポンス
 *
 * @generated from message hami.frontend.v1.GetHorseNameApplicationCampaignDetailResponse
 */
export type GetHorseNameApplicationCampaignDetailResponse = Message<"hami.frontend.v1.GetHorseNameApplicationCampaignDetailResponse"> & {
  /**
   * @generated from field: hami.frontend.v1.HorseNameApplicationCampaignDetail campaign = 1;
   */
  campaign?: HorseNameApplicationCampaignDetail;
};

/**
 * Describes the message hami.frontend.v1.GetHorseNameApplicationCampaignDetailResponse.
 * Use `create(GetHorseNameApplicationCampaignDetailResponseSchema)` to create a new message.
 */
export const GetHorseNameApplicationCampaignDetailResponseSchema: GenMessage<GetHorseNameApplicationCampaignDetailResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 4);

/**
 * キャンペーン詳細
 *
 * @generated from message hami.frontend.v1.HorseNameApplicationCampaignDetail
 */
export type HorseNameApplicationCampaignDetail = Message<"hami.frontend.v1.HorseNameApplicationCampaignDetail"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * 馬ID
   *
   * @generated from field: int64 horse_id = 2;
   */
  horseId: bigint;

  /**
   * 馬名
   *
   * @generated from field: string horse_name = 3;
   */
  horseName: string;

  /**
   * 募集年
   *
   * @generated from field: int32 recruitment_year = 4;
   */
  recruitmentYear: number;

  /**
   * 募集番号
   *
   * @generated from field: int32 recruitment_no = 5;
   */
  recruitmentNo: number;

  /**
   * 応募開始日時
   *
   * @generated from field: google.protobuf.Timestamp application_start_at = 6;
   */
  applicationStartAt?: Timestamp;

  /**
   * 応募終了日時
   *
   * @generated from field: google.protobuf.Timestamp application_end_at = 7;
   */
  applicationEndAt?: Timestamp;

  /**
   * キャンペーンステータス
   *
   * @generated from field: hami.frontend.v1.HorseNameApplicationCampaignStatus status = 8;
   */
  status: HorseNameApplicationCampaignStatus;

  /**
   * 備考
   *
   * @generated from field: optional string note = 9;
   */
  note?: string;

  /**
   * 自分の最新応募
   *
   * @generated from field: optional hami.frontend.v1.HorseNameApplicationItem my_application = 10;
   */
  myApplication?: HorseNameApplicationItem;
};

/**
 * Describes the message hami.frontend.v1.HorseNameApplicationCampaignDetail.
 * Use `create(HorseNameApplicationCampaignDetailSchema)` to create a new message.
 */
export const HorseNameApplicationCampaignDetailSchema: GenMessage<HorseNameApplicationCampaignDetail> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 5);

/**
 * 馬名応募作成リクエスト
 *
 * @generated from message hami.frontend.v1.CreateHorseNameApplicationRequest
 */
export type CreateHorseNameApplicationRequest = Message<"hami.frontend.v1.CreateHorseNameApplicationRequest"> & {
  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * 提案馬名
   *
   * @generated from field: string proposed_horse_name = 2;
   */
  proposedHorseName: string;

  /**
   * 名前の由来
   *
   * @generated from field: optional string name_origin = 3;
   */
  nameOrigin?: string;

  /**
   * 備考
   *
   * @generated from field: optional string note = 4;
   */
  note?: string;
};

/**
 * Describes the message hami.frontend.v1.CreateHorseNameApplicationRequest.
 * Use `create(CreateHorseNameApplicationRequestSchema)` to create a new message.
 */
export const CreateHorseNameApplicationRequestSchema: GenMessage<CreateHorseNameApplicationRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 6);

/**
 * 馬名応募作成レスポンス
 *
 * @generated from message hami.frontend.v1.CreateHorseNameApplicationResponse
 */
export type CreateHorseNameApplicationResponse = Message<"hami.frontend.v1.CreateHorseNameApplicationResponse"> & {
  /**
   * 応募ID
   *
   * @generated from field: int64 horse_name_application_id = 1;
   */
  horseNameApplicationId: bigint;

  /**
   * 処理結果メッセージ
   *
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.CreateHorseNameApplicationResponse.
 * Use `create(CreateHorseNameApplicationResponseSchema)` to create a new message.
 */
export const CreateHorseNameApplicationResponseSchema: GenMessage<CreateHorseNameApplicationResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 7);

/**
 * 自分の応募履歴取得リクエスト
 *
 * @generated from message hami.frontend.v1.GetMyHorseNameApplicationsRequest
 */
export type GetMyHorseNameApplicationsRequest = Message<"hami.frontend.v1.GetMyHorseNameApplicationsRequest"> & {
  /**
   * キャンペーンIDでフィルタ（省略時は全て）
   *
   * @generated from field: optional int64 horse_name_application_campaign_id = 1;
   */
  horseNameApplicationCampaignId?: bigint;
};

/**
 * Describes the message hami.frontend.v1.GetMyHorseNameApplicationsRequest.
 * Use `create(GetMyHorseNameApplicationsRequestSchema)` to create a new message.
 */
export const GetMyHorseNameApplicationsRequestSchema: GenMessage<GetMyHorseNameApplicationsRequest> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 8);

/**
 * 応募アイテム
 *
 * @generated from message hami.frontend.v1.HorseNameApplicationItem
 */
export type HorseNameApplicationItem = Message<"hami.frontend.v1.HorseNameApplicationItem"> & {
  /**
   * 応募ID
   *
   * @generated from field: int64 horse_name_application_id = 1;
   */
  horseNameApplicationId: bigint;

  /**
   * キャンペーンID
   *
   * @generated from field: int64 horse_name_application_campaign_id = 2;
   */
  horseNameApplicationCampaignId: bigint;

  /**
   * 馬名
   *
   * @generated from field: string horse_name = 3;
   */
  horseName: string;

  /**
   * 募集年
   *
   * @generated from field: int32 recruitment_year = 4;
   */
  recruitmentYear: number;

  /**
   * 募集番号
   *
   * @generated from field: int32 recruitment_no = 5;
   */
  recruitmentNo: number;

  /**
   * 提案馬名
   *
   * @generated from field: string proposed_horse_name = 6;
   */
  proposedHorseName: string;

  /**
   * 名前の由来
   *
   * @generated from field: optional string name_origin = 7;
   */
  nameOrigin?: string;

  /**
   * 備考
   *
   * @generated from field: optional string note = 8;
   */
  note?: string;

  /**
   * 応募日時
   *
   * @generated from field: google.protobuf.Timestamp submitted_at = 9;
   */
  submittedAt?: Timestamp;

  /**
   * 最新応募かどうか
   *
   * @generated from field: bool is_latest = 10;
   */
  isLatest: boolean;
};

/**
 * Describes the message hami.frontend.v1.HorseNameApplicationItem.
 * Use `create(HorseNameApplicationItemSchema)` to create a new message.
 */
export const HorseNameApplicationItemSchema: GenMessage<HorseNameApplicationItem> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 9);

/**
 * 自分の応募履歴取得レスポンス
 *
 * @generated from message hami.frontend.v1.GetMyHorseNameApplicationsResponse
 */
export type GetMyHorseNameApplicationsResponse = Message<"hami.frontend.v1.GetMyHorseNameApplicationsResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.HorseNameApplicationItem applications = 1;
   */
  applications: HorseNameApplicationItem[];
};

/**
 * Describes the message hami.frontend.v1.GetMyHorseNameApplicationsResponse.
 * Use `create(GetMyHorseNameApplicationsResponseSchema)` to create a new message.
 */
export const GetMyHorseNameApplicationsResponseSchema: GenMessage<GetMyHorseNameApplicationsResponse> = /*@__PURE__*/
  messageDesc(file_horse_name_application_service, 10);

/**
 * 馬名応募キャンペーンステータス
 *
 * @generated from enum hami.frontend.v1.HorseNameApplicationCampaignStatus
 */
export enum HorseNameApplicationCampaignStatus {
  /**
   * @generated from enum value: HORSE_NAME_APPLICATION_CAMPAIGN_STATUS_UNSPECIFIED = 0;
   */
  HORSE_NAME_APPLICATION_CAMPAIGN_STATUS_UNSPECIFIED = 0,

  /**
   * 下書き
   *
   * @generated from enum value: DRAFT = 1;
   */
  DRAFT = 1,

  /**
   * 公開中（応募受付中）
   *
   * @generated from enum value: PUBLISHED = 2;
   */
  PUBLISHED = 2,

  /**
   * 応募終了
   *
   * @generated from enum value: CLOSED = 3;
   */
  CLOSED = 3,

  /**
   * 審査完了
   *
   * @generated from enum value: COMPLETED = 4;
   */
  COMPLETED = 4,

  /**
   * キャンセル
   *
   * @generated from enum value: CANCELLED = 5;
   */
  CANCELLED = 5,
}

/**
 * Describes the enum hami.frontend.v1.HorseNameApplicationCampaignStatus.
 */
export const HorseNameApplicationCampaignStatusSchema: GenEnum<HorseNameApplicationCampaignStatus> = /*@__PURE__*/
  enumDesc(file_horse_name_application_service, 0);

/**
 * 馬名応募サービス
 *
 * @generated from service hami.frontend.v1.HorseNameApplicationService
 */
export const HorseNameApplicationService: GenService<{
  /**
   * 応募可能キャンペーン一覧取得
   *
   * @generated from rpc hami.frontend.v1.HorseNameApplicationService.ListHorseNameApplicationCampaigns
   */
  listHorseNameApplicationCampaigns: {
    methodKind: "unary";
    input: typeof ListHorseNameApplicationCampaignsRequestSchema;
    output: typeof ListHorseNameApplicationCampaignsResponseSchema;
  },
  /**
   * キャンペーン詳細取得
   *
   * @generated from rpc hami.frontend.v1.HorseNameApplicationService.GetHorseNameApplicationCampaignDetail
   */
  getHorseNameApplicationCampaignDetail: {
    methodKind: "unary";
    input: typeof GetHorseNameApplicationCampaignDetailRequestSchema;
    output: typeof GetHorseNameApplicationCampaignDetailResponseSchema;
  },
  /**
   * 馬名応募作成
   *
   * @generated from rpc hami.frontend.v1.HorseNameApplicationService.CreateHorseNameApplication
   */
  createHorseNameApplication: {
    methodKind: "unary";
    input: typeof CreateHorseNameApplicationRequestSchema;
    output: typeof CreateHorseNameApplicationResponseSchema;
  },
  /**
   * 自分の応募履歴取得
   *
   * @generated from rpc hami.frontend.v1.HorseNameApplicationService.GetMyHorseNameApplications
   */
  getMyHorseNameApplications: {
    methodKind: "unary";
    input: typeof GetMyHorseNameApplicationsRequestSchema;
    output: typeof GetMyHorseNameApplicationsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_name_application_service, 0);

