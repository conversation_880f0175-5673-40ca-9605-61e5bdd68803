// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file bank_account_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file bank_account_service.proto.
 */
export const file_bank_account_service: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * 口座登録依頼リクエスト
 *
 * @generated from message hami.frontend.v1.RegisterBankAccountRequest
 */
export type RegisterBankAccountRequest = Message<"hami.frontend.v1.RegisterBankAccountRequest"> & {
  /**
   * 金融機関コード（4桁）
   *
   * @generated from field: string bank_code = 1;
   */
  bankCode: string;

  /**
   * 支店コード（3桁）
   *
   * @generated from field: string branch_code = 2;
   */
  branchCode: string;

  /**
   * 預金区分（1:普通, 2:当座）
   *
   * @generated from field: int32 account_type = 3;
   */
  accountType: number;

  /**
   * 口座番号
   *
   * @generated from field: string account_number = 4;
   */
  accountNumber: string;

  /**
   * 口座名義カナ
   *
   * @generated from field: string account_name = 5;
   */
  accountName: string;

  /**
   * 口座名義漢字
   *
   * @generated from field: string account_name_kanji = 6;
   */
  accountNameKanji: string;

  /**
   * 金融機関処理完了後のリダイレクトURL
   *
   * @generated from field: string return_url = 7;
   */
  returnUrl: string;
};

/**
 * Describes the message hami.frontend.v1.RegisterBankAccountRequest.
 * Use `create(RegisterBankAccountRequestSchema)` to create a new message.
 */
export const RegisterBankAccountRequestSchema: GenMessage<RegisterBankAccountRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 0);

/**
 * 口座登録依頼レスポンス
 *
 * @generated from message hami.frontend.v1.RegisterBankAccountResponse
 */
export type RegisterBankAccountResponse = Message<"hami.frontend.v1.RegisterBankAccountResponse"> & {
  /**
   * 処理結果メッセージ
   *
   * @generated from field: string message = 1;
   */
  message: string;

  /**
   * GMOトランザクションID
   *
   * @generated from field: string gmo_transaction_id = 2;
   */
  gmoTransactionId: string;

  /**
   * GMOチェックトークン
   *
   * @generated from field: string gmo_token = 3;
   */
  gmoToken: string;

  /**
   * GMO金融機関遷移URL
   *
   * @generated from field: string gmo_start_url = 4;
   */
  gmoStartUrl: string;

  /**
   * 口座登録申請ID
   *
   * @generated from field: int32 bank_account_registration_id = 5;
   */
  bankAccountRegistrationId: number;
};

/**
 * Describes the message hami.frontend.v1.RegisterBankAccountResponse.
 * Use `create(RegisterBankAccountResponseSchema)` to create a new message.
 */
export const RegisterBankAccountResponseSchema: GenMessage<RegisterBankAccountResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 1);

/**
 * 登録状況確認リクエスト
 *
 * @generated from message hami.frontend.v1.GetRegistrationStatusRequest
 */
export type GetRegistrationStatusRequest = Message<"hami.frontend.v1.GetRegistrationStatusRequest"> & {
  /**
   * GMOトランザクションID
   *
   * @generated from field: string gmo_transaction_id = 1;
   */
  gmoTransactionId: string;
};

/**
 * Describes the message hami.frontend.v1.GetRegistrationStatusRequest.
 * Use `create(GetRegistrationStatusRequestSchema)` to create a new message.
 */
export const GetRegistrationStatusRequestSchema: GenMessage<GetRegistrationStatusRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 2);

/**
 * 登録状況確認レスポンス
 *
 * @generated from message hami.frontend.v1.GetRegistrationStatusResponse
 */
export type GetRegistrationStatusResponse = Message<"hami.frontend.v1.GetRegistrationStatusResponse"> & {
  /**
   * 口座登録申請ID
   *
   * @generated from field: int32 bank_account_registration_id = 1;
   */
  bankAccountRegistrationId: number;

  /**
   * 登録状態
   *
   * @generated from field: hami.frontend.v1.BankAccountRegistrationStatus registration_status = 2;
   */
  registrationStatus: BankAccountRegistrationStatus;

  /**
   * 結果：金融機関コード
   *
   * @generated from field: string result_bank_code = 3;
   */
  resultBankCode: string;

  /**
   * 結果：支店コード
   *
   * @generated from field: string result_branch_code = 4;
   */
  resultBranchCode: string;

  /**
   * 結果：預金区分
   *
   * @generated from field: int32 result_account_type = 5;
   */
  resultAccountType: number;

  /**
   * 結果：口座番号（マスク済み）
   *
   * @generated from field: string result_account_number = 6;
   */
  resultAccountNumber: string;

  /**
   * 結果：口座名義カナ
   *
   * @generated from field: string result_account_name = 7;
   */
  resultAccountName: string;

  /**
   * GMOエラーコード
   *
   * @generated from field: string error_code = 8;
   */
  errorCode: string;

  /**
   * GMOエラー詳細
   *
   * @generated from field: string error_detail = 9;
   */
  errorDetail: string;

  /**
   * エラーメッセージ
   *
   * @generated from field: string error_message = 10;
   */
  errorMessage: string;

  /**
   * 登録完了日時（ISO8601形式）
   *
   * @generated from field: string completed_at = 11;
   */
  completedAt: string;

  /**
   * 申請作成日時（ISO8601形式）
   *
   * @generated from field: string created_at = 12;
   */
  createdAt: string;

  /**
   * 最終更新日時（ISO8601形式）
   *
   * @generated from field: string updated_at = 13;
   */
  updatedAt: string;
};

/**
 * Describes the message hami.frontend.v1.GetRegistrationStatusResponse.
 * Use `create(GetRegistrationStatusResponseSchema)` to create a new message.
 */
export const GetRegistrationStatusResponseSchema: GenMessage<GetRegistrationStatusResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 3);

/**
 * 口座情報取得リクエスト
 *
 * 認証済み会員の情報を使用するため、リクエストパラメータなし
 *
 * @generated from message hami.frontend.v1.GetBankAccountInfoRequest
 */
export type GetBankAccountInfoRequest = Message<"hami.frontend.v1.GetBankAccountInfoRequest"> & {
};

/**
 * Describes the message hami.frontend.v1.GetBankAccountInfoRequest.
 * Use `create(GetBankAccountInfoRequestSchema)` to create a new message.
 */
export const GetBankAccountInfoRequestSchema: GenMessage<GetBankAccountInfoRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 4);

/**
 * 口座情報取得レスポンス
 *
 * @generated from message hami.frontend.v1.GetBankAccountInfoResponse
 */
export type GetBankAccountInfoResponse = Message<"hami.frontend.v1.GetBankAccountInfoResponse"> & {
  /**
   * 有効な口座登録があるか
   *
   * @generated from field: bool has_active_account = 1;
   */
  hasActiveAccount: boolean;

  /**
   * 口座登録申請ID
   *
   * @generated from field: int32 bank_account_registration_id = 2;
   */
  bankAccountRegistrationId: number;

  /**
   * 登録状態
   *
   * @generated from field: hami.frontend.v1.BankAccountRegistrationStatus registration_status = 3;
   */
  registrationStatus: BankAccountRegistrationStatus;

  /**
   * 結果：金融機関コード
   *
   * @generated from field: string result_bank_code = 4;
   */
  resultBankCode: string;

  /**
   * 結果：支店コード
   *
   * @generated from field: string result_branch_code = 5;
   */
  resultBranchCode: string;

  /**
   * 結果：預金区分
   *
   * @generated from field: int32 result_account_type = 6;
   */
  resultAccountType: number;

  /**
   * 結果：口座番号（マスク済み）
   *
   * @generated from field: string result_account_number = 7;
   */
  resultAccountNumber: string;

  /**
   * 結果：口座名義カナ
   *
   * @generated from field: string result_account_name = 8;
   */
  resultAccountName: string;

  /**
   * 登録完了日時（ISO8601形式）
   *
   * @generated from field: string completed_at = 9;
   */
  completedAt: string;
};

/**
 * Describes the message hami.frontend.v1.GetBankAccountInfoResponse.
 * Use `create(GetBankAccountInfoResponseSchema)` to create a new message.
 */
export const GetBankAccountInfoResponseSchema: GenMessage<GetBankAccountInfoResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 5);

/**
 * GMOコールバック処理リクエスト（Next.jsからの転送用）
 *
 * @generated from message hami.frontend.v1.HandleGmoCallbackRequest
 */
export type HandleGmoCallbackRequest = Message<"hami.frontend.v1.HandleGmoCallbackRequest"> & {
  /**
   * GMOトランザクションID
   *
   * @generated from field: string transaction_id = 1;
   */
  transactionId: string;

  /**
   * GMOサイトID
   *
   * @generated from field: string site_id = 2;
   */
  siteId: string;

  /**
   * GMO会員ID
   *
   * @generated from field: string member_id = 3;
   */
  memberId: string;

  /**
   * 処理結果（SUCCESS/PEND/FAIL）
   *
   * @generated from field: string result = 4;
   */
  result: string;

  /**
   * 金融機関コード
   *
   * @generated from field: string bank_code = 5;
   */
  bankCode: string;

  /**
   * 支店コード
   *
   * @generated from field: string branch_code = 6;
   */
  branchCode: string;

  /**
   * 預金区分
   *
   * @generated from field: string account_type = 7;
   */
  accountType: string;

  /**
   * 口座番号
   *
   * @generated from field: string account_number = 8;
   */
  accountNumber: string;

  /**
   * 口座名義カナ
   *
   * @generated from field: string account_name = 9;
   */
  accountName: string;
};

/**
 * Describes the message hami.frontend.v1.HandleGmoCallbackRequest.
 * Use `create(HandleGmoCallbackRequestSchema)` to create a new message.
 */
export const HandleGmoCallbackRequestSchema: GenMessage<HandleGmoCallbackRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 6);

/**
 * GMOコールバック処理レスポンス
 *
 * @generated from message hami.frontend.v1.HandleGmoCallbackResponse
 */
export type HandleGmoCallbackResponse = Message<"hami.frontend.v1.HandleGmoCallbackResponse"> & {
  /**
   * 処理成功フラグ
   *
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 処理結果メッセージ
   *
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.HandleGmoCallbackResponse.
 * Use `create(HandleGmoCallbackResponseSchema)` to create a new message.
 */
export const HandleGmoCallbackResponseSchema: GenMessage<HandleGmoCallbackResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 7);

/**
 * 登録履歴取得リクエスト
 *
 * 認証済み会員の情報を使用するため、リクエストパラメータなし
 *
 * @generated from message hami.frontend.v1.ListBankAccountRegistrationsRequest
 */
export type ListBankAccountRegistrationsRequest = Message<"hami.frontend.v1.ListBankAccountRegistrationsRequest"> & {
};

/**
 * Describes the message hami.frontend.v1.ListBankAccountRegistrationsRequest.
 * Use `create(ListBankAccountRegistrationsRequestSchema)` to create a new message.
 */
export const ListBankAccountRegistrationsRequestSchema: GenMessage<ListBankAccountRegistrationsRequest> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 8);

/**
 * 登録履歴項目
 *
 * @generated from message hami.frontend.v1.BankAccountRegistrationItem
 */
export type BankAccountRegistrationItem = Message<"hami.frontend.v1.BankAccountRegistrationItem"> & {
  /**
   * 口座登録申請ID
   *
   * @generated from field: int32 bank_account_registration_id = 1;
   */
  bankAccountRegistrationId: number;

  /**
   * 登録状態
   *
   * @generated from field: hami.frontend.v1.BankAccountRegistrationStatus registration_status = 2;
   */
  registrationStatus: BankAccountRegistrationStatus;

  /**
   * 金融機関コード
   *
   * @generated from field: string bank_code = 3;
   */
  bankCode: string;

  /**
   * 支店コード
   *
   * @generated from field: string branch_code = 4;
   */
  branchCode: string;

  /**
   * 預金区分
   *
   * @generated from field: int32 account_type = 5;
   */
  accountType: number;

  /**
   * 口座番号
   *
   * @generated from field: string account_number = 6;
   */
  accountNumber: string;

  /**
   * 口座名義カナ
   *
   * @generated from field: string account_name = 7;
   */
  accountName: string;

  /**
   * 口座名義漢字
   *
   * @generated from field: string account_name_kanji = 8;
   */
  accountNameKanji: string;

  /**
   * 結果：金融機関コード
   *
   * @generated from field: string result_bank_code = 9;
   */
  resultBankCode: string;

  /**
   * 結果：支店コード
   *
   * @generated from field: string result_branch_code = 10;
   */
  resultBranchCode: string;

  /**
   * 結果：預金区分
   *
   * @generated from field: int32 result_account_type = 11;
   */
  resultAccountType: number;

  /**
   * 結果：口座番号（マスク済み）
   *
   * @generated from field: string result_account_number = 12;
   */
  resultAccountNumber: string;

  /**
   * 結果：口座名義カナ
   *
   * @generated from field: string result_account_name = 13;
   */
  resultAccountName: string;

  /**
   * GMOエラーコード
   *
   * @generated from field: string error_code = 14;
   */
  errorCode: string;

  /**
   * GMOエラー詳細
   *
   * @generated from field: string error_detail = 15;
   */
  errorDetail: string;

  /**
   * エラーメッセージ
   *
   * @generated from field: string error_message = 16;
   */
  errorMessage: string;

  /**
   * GMOトランザクションID
   *
   * @generated from field: string gmo_transaction_id = 17;
   */
  gmoTransactionId: string;

  /**
   * アクティブフラグ
   *
   * @generated from field: bool is_active = 18;
   */
  isActive: boolean;

  /**
   * 申請作成日時（ISO8601形式）
   *
   * @generated from field: string created_at = 19;
   */
  createdAt: string;

  /**
   * 最終更新日時（ISO8601形式）
   *
   * @generated from field: string updated_at = 20;
   */
  updatedAt: string;

  /**
   * 登録完了日時（ISO8601形式）
   *
   * @generated from field: string completed_at = 21;
   */
  completedAt: string;
};

/**
 * Describes the message hami.frontend.v1.BankAccountRegistrationItem.
 * Use `create(BankAccountRegistrationItemSchema)` to create a new message.
 */
export const BankAccountRegistrationItemSchema: GenMessage<BankAccountRegistrationItem> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 9);

/**
 * 登録履歴取得レスポンス
 *
 * @generated from message hami.frontend.v1.ListBankAccountRegistrationsResponse
 */
export type ListBankAccountRegistrationsResponse = Message<"hami.frontend.v1.ListBankAccountRegistrationsResponse"> & {
  /**
   * 登録履歴一覧
   *
   * @generated from field: repeated hami.frontend.v1.BankAccountRegistrationItem registrations = 1;
   */
  registrations: BankAccountRegistrationItem[];
};

/**
 * Describes the message hami.frontend.v1.ListBankAccountRegistrationsResponse.
 * Use `create(ListBankAccountRegistrationsResponseSchema)` to create a new message.
 */
export const ListBankAccountRegistrationsResponseSchema: GenMessage<ListBankAccountRegistrationsResponse> = /*@__PURE__*/
  messageDesc(file_bank_account_service, 10);

/**
 * 口座登録状態
 *
 * @generated from enum hami.frontend.v1.BankAccountRegistrationStatus
 */
export enum BankAccountRegistrationStatus {
  /**
   * @generated from enum value: BANK_ACCOUNT_REGISTRATION_STATUS_UNKNOWN = 0;
   */
  BANK_ACCOUNT_REGISTRATION_STATUS_UNKNOWN = 0,

  /**
   * 登録済み（申請完了）
   *
   * @generated from enum value: ENTRY = 1;
   */
  ENTRY = 1,

  /**
   * 金融機関画面遷移
   *
   * @generated from enum value: START = 2;
   */
  START = 2,

  /**
   * 結果確認
   *
   * @generated from enum value: TERM = 3;
   */
  TERM = 3,

  /**
   * 申込成功
   *
   * @generated from enum value: SUCCESS = 4;
   */
  SUCCESS = 4,

  /**
   * 申込失敗（金融機関からのNG）
   *
   * @generated from enum value: FAIL = 5;
   */
  FAIL = 5,

  /**
   * 申込失敗（結果返却前の失敗）
   *
   * @generated from enum value: UNPROCESSED = 6;
   */
  UNPROCESSED = 6,
}

/**
 * Describes the enum hami.frontend.v1.BankAccountRegistrationStatus.
 */
export const BankAccountRegistrationStatusSchema: GenEnum<BankAccountRegistrationStatus> = /*@__PURE__*/
  enumDesc(file_bank_account_service, 0);

/**
 * 口座登録サービス
 *
 * @generated from service hami.frontend.v1.BankAccountService
 */
export const BankAccountService: GenService<{
  /**
   * 口座登録依頼
   *
   * @generated from rpc hami.frontend.v1.BankAccountService.RegisterBankAccount
   */
  registerBankAccount: {
    methodKind: "unary";
    input: typeof RegisterBankAccountRequestSchema;
    output: typeof RegisterBankAccountResponseSchema;
  },
  /**
   * 登録状況確認
   *
   * @generated from rpc hami.frontend.v1.BankAccountService.GetRegistrationStatus
   */
  getRegistrationStatus: {
    methodKind: "unary";
    input: typeof GetRegistrationStatusRequestSchema;
    output: typeof GetRegistrationStatusResponseSchema;
  },
  /**
   * 口座情報取得
   *
   * @generated from rpc hami.frontend.v1.BankAccountService.GetBankAccountInfo
   */
  getBankAccountInfo: {
    methodKind: "unary";
    input: typeof GetBankAccountInfoRequestSchema;
    output: typeof GetBankAccountInfoResponseSchema;
  },
  /**
   * 登録履歴取得
   *
   * @generated from rpc hami.frontend.v1.BankAccountService.ListBankAccountRegistrations
   */
  listBankAccountRegistrations: {
    methodKind: "unary";
    input: typeof ListBankAccountRegistrationsRequestSchema;
    output: typeof ListBankAccountRegistrationsResponseSchema;
  },
  /**
   * GMOコールバック処理（Next.jsからの転送用）
   *
   * @generated from rpc hami.frontend.v1.BankAccountService.HandleGmoCallback
   */
  handleGmoCallback: {
    methodKind: "unary";
    input: typeof HandleGmoCallbackRequestSchema;
    output: typeof HandleGmoCallbackResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_bank_account_service, 0);

