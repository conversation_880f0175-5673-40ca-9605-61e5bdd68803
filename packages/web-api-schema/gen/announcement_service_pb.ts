// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file announcement_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { AttachmentType } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file announcement_service.proto.
 */
export const file_announcement_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums, file_google_protobuf_timestamp]);

/**
 * ユーザー向けお知らせ情報（管理情報を除外）
 *
 * @generated from message hami.frontend.v1.PublicAnnouncement
 */
export type PublicAnnouncement = Message<"hami.frontend.v1.PublicAnnouncement"> & {
  /**
   * @generated from field: int32 announcement_id = 1;
   */
  announcementId: number;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string body = 3;
   */
  body: string;

  /**
   * @generated from field: bool is_headline = 4;
   */
  isHeadline: boolean;

  /**
   * @generated from field: google.protobuf.Timestamp published_at = 5;
   */
  publishedAt?: Timestamp;

  /**
   * @generated from field: repeated hami.frontend.v1.PublicAnnouncementAttachment attachments = 6;
   */
  attachments: PublicAnnouncementAttachment[];
};

/**
 * Describes the message hami.frontend.v1.PublicAnnouncement.
 * Use `create(PublicAnnouncementSchema)` to create a new message.
 */
export const PublicAnnouncementSchema: GenMessage<PublicAnnouncement> = /*@__PURE__*/
  messageDesc(file_announcement_service, 0);

/**
 * ユーザー向け添付ファイル情報
 *
 * @generated from message hami.frontend.v1.PublicAnnouncementAttachment
 */
export type PublicAnnouncementAttachment = Message<"hami.frontend.v1.PublicAnnouncementAttachment"> & {
  /**
   * @generated from field: int32 announcement_attachment_id = 1;
   */
  announcementAttachmentId: number;

  /**
   * @generated from field: string file_name = 2;
   */
  fileName: string;

  /**
   * @generated from field: string mime_type = 3;
   */
  mimeType: string;

  /**
   * @generated from field: int64 file_size = 4;
   */
  fileSize: bigint;

  /**
   * @generated from field: hami.frontend.v1.AttachmentType attachment_type = 5;
   */
  attachmentType: AttachmentType;

  /**
   * @generated from field: int32 display_order = 6;
   */
  displayOrder: number;
};

/**
 * Describes the message hami.frontend.v1.PublicAnnouncementAttachment.
 * Use `create(PublicAnnouncementAttachmentSchema)` to create a new message.
 */
export const PublicAnnouncementAttachmentSchema: GenMessage<PublicAnnouncementAttachment> = /*@__PURE__*/
  messageDesc(file_announcement_service, 1);

/**
 * ヘッドラインお知らせ取得リクエスト
 *
 * @generated from message hami.frontend.v1.GetHeadlineAnnouncementsRequest
 */
export type GetHeadlineAnnouncementsRequest = Message<"hami.frontend.v1.GetHeadlineAnnouncementsRequest"> & {
  /**
   * 最大取得件数（デフォルト: 5）
   *
   * @generated from field: int32 limit = 1;
   */
  limit: number;
};

/**
 * Describes the message hami.frontend.v1.GetHeadlineAnnouncementsRequest.
 * Use `create(GetHeadlineAnnouncementsRequestSchema)` to create a new message.
 */
export const GetHeadlineAnnouncementsRequestSchema: GenMessage<GetHeadlineAnnouncementsRequest> = /*@__PURE__*/
  messageDesc(file_announcement_service, 2);

/**
 * @generated from message hami.frontend.v1.GetHeadlineAnnouncementsResponse
 */
export type GetHeadlineAnnouncementsResponse = Message<"hami.frontend.v1.GetHeadlineAnnouncementsResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.PublicAnnouncement announcements = 1;
   */
  announcements: PublicAnnouncement[];
};

/**
 * Describes the message hami.frontend.v1.GetHeadlineAnnouncementsResponse.
 * Use `create(GetHeadlineAnnouncementsResponseSchema)` to create a new message.
 */
export const GetHeadlineAnnouncementsResponseSchema: GenMessage<GetHeadlineAnnouncementsResponse> = /*@__PURE__*/
  messageDesc(file_announcement_service, 3);

/**
 * お知らせ一覧取得リクエスト
 *
 * @generated from message hami.frontend.v1.ListAnnouncementsRequest
 */
export type ListAnnouncementsRequest = Message<"hami.frontend.v1.ListAnnouncementsRequest"> & {
  /**
   * タイトル・本文検索
   *
   * @generated from field: optional string search_query = 1;
   */
  searchQuery?: string;

  /**
   * @generated from field: int32 page = 2;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 3;
   */
  pageSize: number;

  /**
   * "published_at"
   *
   * @generated from field: string sort_by = 4;
   */
  sortBy: string;

  /**
   * "desc", "asc"
   *
   * @generated from field: string sort_order = 5;
   */
  sortOrder: string;
};

/**
 * Describes the message hami.frontend.v1.ListAnnouncementsRequest.
 * Use `create(ListAnnouncementsRequestSchema)` to create a new message.
 */
export const ListAnnouncementsRequestSchema: GenMessage<ListAnnouncementsRequest> = /*@__PURE__*/
  messageDesc(file_announcement_service, 4);

/**
 * @generated from message hami.frontend.v1.ListAnnouncementsResponse
 */
export type ListAnnouncementsResponse = Message<"hami.frontend.v1.ListAnnouncementsResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.PublicAnnouncement announcements = 1;
   */
  announcements: PublicAnnouncement[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 total_pages = 3;
   */
  totalPages: number;

  /**
   * @generated from field: bool has_next_page = 4;
   */
  hasNextPage: boolean;

  /**
   * @generated from field: bool has_prev_page = 5;
   */
  hasPrevPage: boolean;
};

/**
 * Describes the message hami.frontend.v1.ListAnnouncementsResponse.
 * Use `create(ListAnnouncementsResponseSchema)` to create a new message.
 */
export const ListAnnouncementsResponseSchema: GenMessage<ListAnnouncementsResponse> = /*@__PURE__*/
  messageDesc(file_announcement_service, 5);

/**
 * お知らせ詳細取得リクエスト
 *
 * @generated from message hami.frontend.v1.GetAnnouncementRequest
 */
export type GetAnnouncementRequest = Message<"hami.frontend.v1.GetAnnouncementRequest"> & {
  /**
   * @generated from field: int32 announcement_id = 1;
   */
  announcementId: number;
};

/**
 * Describes the message hami.frontend.v1.GetAnnouncementRequest.
 * Use `create(GetAnnouncementRequestSchema)` to create a new message.
 */
export const GetAnnouncementRequestSchema: GenMessage<GetAnnouncementRequest> = /*@__PURE__*/
  messageDesc(file_announcement_service, 6);

/**
 * @generated from message hami.frontend.v1.GetAnnouncementResponse
 */
export type GetAnnouncementResponse = Message<"hami.frontend.v1.GetAnnouncementResponse"> & {
  /**
   * @generated from field: hami.frontend.v1.PublicAnnouncement announcement = 1;
   */
  announcement?: PublicAnnouncement;
};

/**
 * Describes the message hami.frontend.v1.GetAnnouncementResponse.
 * Use `create(GetAnnouncementResponseSchema)` to create a new message.
 */
export const GetAnnouncementResponseSchema: GenMessage<GetAnnouncementResponse> = /*@__PURE__*/
  messageDesc(file_announcement_service, 7);

/**
 * 添付ファイルダウンロードURL取得リクエスト
 *
 * @generated from message hami.frontend.v1.GetAnnouncementDownloadUrlRequest
 */
export type GetAnnouncementDownloadUrlRequest = Message<"hami.frontend.v1.GetAnnouncementDownloadUrlRequest"> & {
  /**
   * @generated from field: int32 announcement_attachment_id = 1;
   */
  announcementAttachmentId: number;
};

/**
 * Describes the message hami.frontend.v1.GetAnnouncementDownloadUrlRequest.
 * Use `create(GetAnnouncementDownloadUrlRequestSchema)` to create a new message.
 */
export const GetAnnouncementDownloadUrlRequestSchema: GenMessage<GetAnnouncementDownloadUrlRequest> = /*@__PURE__*/
  messageDesc(file_announcement_service, 8);

/**
 * @generated from message hami.frontend.v1.GetAnnouncementDownloadUrlResponse
 */
export type GetAnnouncementDownloadUrlResponse = Message<"hami.frontend.v1.GetAnnouncementDownloadUrlResponse"> & {
  /**
   * 署名付きURL
   *
   * @generated from field: string download_url = 1;
   */
  downloadUrl: string;

  /**
   * 有効期限（秒）
   *
   * @generated from field: int32 expires_in = 2;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.frontend.v1.GetAnnouncementDownloadUrlResponse.
 * Use `create(GetAnnouncementDownloadUrlResponseSchema)` to create a new message.
 */
export const GetAnnouncementDownloadUrlResponseSchema: GenMessage<GetAnnouncementDownloadUrlResponse> = /*@__PURE__*/
  messageDesc(file_announcement_service, 9);

/**
 * ユーザー向けお知らせサービス
 *
 * @generated from service hami.frontend.v1.AnnouncementService
 */
export const AnnouncementService: GenService<{
  /**
   * ヘッドラインお知らせ一覧取得
   *
   * @generated from rpc hami.frontend.v1.AnnouncementService.GetHeadlineAnnouncements
   */
  getHeadlineAnnouncements: {
    methodKind: "unary";
    input: typeof GetHeadlineAnnouncementsRequestSchema;
    output: typeof GetHeadlineAnnouncementsResponseSchema;
  },
  /**
   * お知らせ一覧取得
   *
   * @generated from rpc hami.frontend.v1.AnnouncementService.ListAnnouncements
   */
  listAnnouncements: {
    methodKind: "unary";
    input: typeof ListAnnouncementsRequestSchema;
    output: typeof ListAnnouncementsResponseSchema;
  },
  /**
   * お知らせ詳細取得
   *
   * @generated from rpc hami.frontend.v1.AnnouncementService.GetAnnouncement
   */
  getAnnouncement: {
    methodKind: "unary";
    input: typeof GetAnnouncementRequestSchema;
    output: typeof GetAnnouncementResponseSchema;
  },
  /**
   * 添付ファイルダウンロードURL取得
   *
   * @generated from rpc hami.frontend.v1.AnnouncementService.GetAnnouncementDownloadUrl
   */
  getAnnouncementDownloadUrl: {
    methodKind: "unary";
    input: typeof GetAnnouncementDownloadUrlRequestSchema;
    output: typeof GetAnnouncementDownloadUrlResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_announcement_service, 0);

