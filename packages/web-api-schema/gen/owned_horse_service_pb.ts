// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file owned_horse_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { CoatColor, Gender, HorseClass } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file owned_horse_service.proto.
 */
export const file_owned_horse_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums]);

/**
 * 所属馬詳細取得リクエスト
 *
 * @generated from message hami.frontend.v1.GetOwnedHorseRequest
 */
export type GetOwnedHorseRequest = Message<"hami.frontend.v1.GetOwnedHorseRequest"> & {
  /**
   * @generated from field: int32 recruitment_year = 1;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 2;
   */
  recruitmentNo: number;
};

/**
 * Describes the message hami.frontend.v1.GetOwnedHorseRequest.
 * Use `create(GetOwnedHorseRequestSchema)` to create a new message.
 */
export const GetOwnedHorseRequestSchema: GenMessage<GetOwnedHorseRequest> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 0);

/**
 * 所属馬詳細取得レスポンス
 *
 * @generated from message hami.frontend.v1.GetOwnedHorseResponse
 */
export type GetOwnedHorseResponse = Message<"hami.frontend.v1.GetOwnedHorseResponse"> & {
  /**
   * @generated from field: hami.frontend.v1.OwnedHorseDetail horse = 1;
   */
  horse?: OwnedHorseDetail;
};

/**
 * Describes the message hami.frontend.v1.GetOwnedHorseResponse.
 * Use `create(GetOwnedHorseResponseSchema)` to create a new message.
 */
export const GetOwnedHorseResponseSchema: GenMessage<GetOwnedHorseResponse> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 1);

/**
 * 所属馬一覧取得リクエスト
 *
 * @generated from message hami.frontend.v1.ListOwnedHorsesRequest
 */
export type ListOwnedHorsesRequest = Message<"hami.frontend.v1.ListOwnedHorsesRequest"> & {
  /**
   * 馬名での検索
   *
   * @generated from field: optional string search = 1;
   */
  search?: string;

  /**
   * 生年での絞り込み
   *
   * @generated from field: optional int32 birth_year = 2;
   */
  birthYear?: number;
};

/**
 * Describes the message hami.frontend.v1.ListOwnedHorsesRequest.
 * Use `create(ListOwnedHorsesRequestSchema)` to create a new message.
 */
export const ListOwnedHorsesRequestSchema: GenMessage<ListOwnedHorsesRequest> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 2);

/**
 * 所属馬一覧取得レスポンス
 *
 * @generated from message hami.frontend.v1.ListOwnedHorsesResponse
 */
export type ListOwnedHorsesResponse = Message<"hami.frontend.v1.ListOwnedHorsesResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.OwnedHorseListItem horses = 1;
   */
  horses: OwnedHorseListItem[];
};

/**
 * Describes the message hami.frontend.v1.ListOwnedHorsesResponse.
 * Use `create(ListOwnedHorsesResponseSchema)` to create a new message.
 */
export const ListOwnedHorsesResponseSchema: GenMessage<ListOwnedHorsesResponse> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 3);

/**
 * 所属馬一覧項目
 *
 * @generated from message hami.frontend.v1.OwnedHorseListItem
 */
export type OwnedHorseListItem = Message<"hami.frontend.v1.OwnedHorseListItem"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 recruitment_year = 2;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 3;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string recruitment_name = 4;
   */
  recruitmentName: string;

  /**
   * @generated from field: string horse_name = 5;
   */
  horseName: string;

  /**
   * @generated from field: optional hami.frontend.v1.Gender gender = 6;
   */
  gender?: Gender;

  /**
   * @generated from field: int32 age = 7;
   */
  age: number;

  /**
   * 所属（美浦・栗東等）
   *
   * @generated from field: optional string affiliation = 8;
   */
  affiliation?: string;

  /**
   * 厩舎名
   *
   * @generated from field: optional string stable_name = 9;
   */
  stableName?: string;

  /**
   * 父馬名
   *
   * @generated from field: optional string sire_name = 10;
   */
  sireName?: string;

  /**
   * 母馬名
   *
   * @generated from field: optional string dam_name = 11;
   */
  damName?: string;
};

/**
 * Describes the message hami.frontend.v1.OwnedHorseListItem.
 * Use `create(OwnedHorseListItemSchema)` to create a new message.
 */
export const OwnedHorseListItemSchema: GenMessage<OwnedHorseListItem> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 4);

/**
 * 所属馬詳細情報
 *
 * @generated from message hami.frontend.v1.OwnedHorseDetail
 */
export type OwnedHorseDetail = Message<"hami.frontend.v1.OwnedHorseDetail"> & {
  /**
   * 基本情報
   *
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 recruitment_year = 2;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 3;
   */
  recruitmentNo: number;

  /**
   * @generated from field: string recruitment_name = 4;
   */
  recruitmentName: string;

  /**
   * @generated from field: string horse_name = 5;
   */
  horseName: string;

  /**
   * @generated from field: optional string horse_name_en = 6;
   */
  horseNameEn?: string;

  /**
   * 馬名の由来
   *
   * @generated from field: optional string name_origin = 7;
   */
  nameOrigin?: string;

  /**
   * 生年月日
   *
   * @generated from field: int32 birth_year = 8;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 9;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 10;
   */
  birthDay: number;

  /**
   * 馬の特徴
   *
   * @generated from field: optional hami.frontend.v1.Gender gender = 11;
   */
  gender?: Gender;

  /**
   * @generated from field: optional hami.frontend.v1.CoatColor coat_color = 12;
   */
  coatColor?: CoatColor;

  /**
   * @generated from field: optional hami.frontend.v1.HorseClass horse_class = 13;
   */
  horseClass?: HorseClass;

  /**
   * 血統情報
   *
   * 父
   *
   * @generated from field: optional string sire_name = 14;
   */
  sireName?: string;

  /**
   * 母
   *
   * @generated from field: optional string dam_name = 15;
   */
  damName?: string;

  /**
   * 母の父
   *
   * @generated from field: optional string broodmare_sire_name = 16;
   */
  broodmareSireName?: string;

  /**
   * 生産・育成情報
   *
   * 生産地
   *
   * @generated from field: optional string birth_place = 17;
   */
  birthPlace?: string;

  /**
   * 生産者
   *
   * @generated from field: optional string breeder_name = 18;
   */
  breederName?: string;

  /**
   * 育成牧場
   *
   * @generated from field: optional string training_farm = 19;
   */
  trainingFarm?: string;

  /**
   * 厩舎情報
   *
   * 厩舎名
   *
   * @generated from field: optional string stable_name = 20;
   */
  stableName?: string;

  /**
   * 所属（美浦・栗東等）
   *
   * @generated from field: optional string affiliation = 21;
   */
  affiliation?: string;

  /**
   * 募集情報
   *
   * 募集口数
   *
   * @generated from field: int32 shares_total = 22;
   */
  sharesTotal: number;

  /**
   * 募集総額
   *
   * @generated from field: int32 amount_total = 23;
   */
  amountTotal: number;

  /**
   * 画像
   *
   * @generated from field: repeated string image_urls = 24;
   */
  imageUrls: string[];
};

/**
 * Describes the message hami.frontend.v1.OwnedHorseDetail.
 * Use `create(OwnedHorseDetailSchema)` to create a new message.
 */
export const OwnedHorseDetailSchema: GenMessage<OwnedHorseDetail> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 5);

/**
 * 近況情報
 *
 * @generated from message hami.frontend.v1.HorseReport
 */
export type HorseReport = Message<"hami.frontend.v1.HorseReport"> & {
  /**
   * @generated from field: int32 report_id = 1;
   */
  reportId: number;

  /**
   * @generated from field: int32 report_year = 2;
   */
  reportYear: number;

  /**
   * @generated from field: int32 report_month = 3;
   */
  reportMonth: number;

  /**
   * @generated from field: int32 report_day = 4;
   */
  reportDay: number;

  /**
   * 場所（美浦・木村哲也厩舎等）
   *
   * @generated from field: string location = 5;
   */
  location: string;

  /**
   * 近況内容
   *
   * @generated from field: string content = 6;
   */
  content: string;

  /**
   * 近況画像
   *
   * @generated from field: repeated string image_urls = 7;
   */
  imageUrls: string[];
};

/**
 * Describes the message hami.frontend.v1.HorseReport.
 * Use `create(HorseReportSchema)` to create a new message.
 */
export const HorseReportSchema: GenMessage<HorseReport> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 6);

/**
 * 近況一覧取得リクエスト
 *
 * @generated from message hami.frontend.v1.ListHorseReportsRequest
 */
export type ListHorseReportsRequest = Message<"hami.frontend.v1.ListHorseReportsRequest"> & {
  /**
   * @generated from field: int32 recruitment_year = 1;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 2;
   */
  recruitmentNo: number;

  /**
   * 取得件数制限（デフォルト：全件）
   *
   * @generated from field: optional int32 limit = 3;
   */
  limit?: number;
};

/**
 * Describes the message hami.frontend.v1.ListHorseReportsRequest.
 * Use `create(ListHorseReportsRequestSchema)` to create a new message.
 */
export const ListHorseReportsRequestSchema: GenMessage<ListHorseReportsRequest> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 7);

/**
 * 近況一覧取得レスポンス
 *
 * @generated from message hami.frontend.v1.ListHorseReportsResponse
 */
export type ListHorseReportsResponse = Message<"hami.frontend.v1.ListHorseReportsResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.HorseReport reports = 1;
   */
  reports: HorseReport[];
};

/**
 * Describes the message hami.frontend.v1.ListHorseReportsResponse.
 * Use `create(ListHorseReportsResponseSchema)` to create a new message.
 */
export const ListHorseReportsResponseSchema: GenMessage<ListHorseReportsResponse> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 8);

/**
 * 動画情報
 *
 * @generated from message hami.frontend.v1.HorseVideo
 */
export type HorseVideo = Message<"hami.frontend.v1.HorseVideo"> & {
  /**
   * @generated from field: int32 video_id = 1;
   */
  videoId: number;

  /**
   * @generated from field: int32 video_year = 2;
   */
  videoYear: number;

  /**
   * @generated from field: int32 video_month = 3;
   */
  videoMonth: number;

  /**
   * @generated from field: int32 video_day = 4;
   */
  videoDay: number;

  /**
   * @generated from field: string title = 5;
   */
  title: string;

  /**
   * @generated from field: optional string description = 6;
   */
  description?: string;

  /**
   * @generated from field: string youtube_video_id = 7;
   */
  youtubeVideoId: string;

  /**
   * @generated from field: optional int32 start_at_seconds = 8;
   */
  startAtSeconds?: number;

  /**
   * @generated from field: optional string thumbnail_url = 9;
   */
  thumbnailUrl?: string;
};

/**
 * Describes the message hami.frontend.v1.HorseVideo.
 * Use `create(HorseVideoSchema)` to create a new message.
 */
export const HorseVideoSchema: GenMessage<HorseVideo> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 9);

/**
 * 動画一覧取得リクエスト
 *
 * @generated from message hami.frontend.v1.ListHorseVideosRequest
 */
export type ListHorseVideosRequest = Message<"hami.frontend.v1.ListHorseVideosRequest"> & {
  /**
   * @generated from field: int32 recruitment_year = 1;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 2;
   */
  recruitmentNo: number;

  /**
   * 取得件数制限（デフォルト：全件）
   *
   * @generated from field: optional int32 limit = 3;
   */
  limit?: number;
};

/**
 * Describes the message hami.frontend.v1.ListHorseVideosRequest.
 * Use `create(ListHorseVideosRequestSchema)` to create a new message.
 */
export const ListHorseVideosRequestSchema: GenMessage<ListHorseVideosRequest> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 10);

/**
 * 動画一覧取得レスポンス
 *
 * @generated from message hami.frontend.v1.ListHorseVideosResponse
 */
export type ListHorseVideosResponse = Message<"hami.frontend.v1.ListHorseVideosResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.HorseVideo videos = 1;
   */
  videos: HorseVideo[];
};

/**
 * Describes the message hami.frontend.v1.ListHorseVideosResponse.
 * Use `create(ListHorseVideosResponseSchema)` to create a new message.
 */
export const ListHorseVideosResponseSchema: GenMessage<ListHorseVideosResponse> = /*@__PURE__*/
  messageDesc(file_owned_horse_service, 11);

/**
 * 所属馬サービス（フロントエンド向け）
 *
 * @generated from service hami.frontend.v1.OwnedHorseService
 */
export const OwnedHorseService: GenService<{
  /**
   * 所属馬詳細取得
   *
   * @generated from rpc hami.frontend.v1.OwnedHorseService.GetOwnedHorse
   */
  getOwnedHorse: {
    methodKind: "unary";
    input: typeof GetOwnedHorseRequestSchema;
    output: typeof GetOwnedHorseResponseSchema;
  },
  /**
   * 所属馬一覧取得
   *
   * @generated from rpc hami.frontend.v1.OwnedHorseService.ListOwnedHorses
   */
  listOwnedHorses: {
    methodKind: "unary";
    input: typeof ListOwnedHorsesRequestSchema;
    output: typeof ListOwnedHorsesResponseSchema;
  },
  /**
   * 近況一覧取得
   *
   * @generated from rpc hami.frontend.v1.OwnedHorseService.ListHorseReports
   */
  listHorseReports: {
    methodKind: "unary";
    input: typeof ListHorseReportsRequestSchema;
    output: typeof ListHorseReportsResponseSchema;
  },
  /**
   * 動画一覧取得（YouTube埋め込み）
   *
   * @generated from rpc hami.frontend.v1.OwnedHorseService.ListHorseVideos
   */
  listHorseVideos: {
    methodKind: "unary";
    input: typeof ListHorseVideosRequestSchema;
    output: typeof ListHorseVideosResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_owned_horse_service, 0);

