// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file membership_application_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file membership_application_service.proto.
 */
export const file_membership_application_service: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message hami.frontend.v1.SendVerificationMailRequest
 */
export type SendVerificationMailRequest = Message<"hami.frontend.v1.SendVerificationMailRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;
};

/**
 * Describes the message hami.frontend.v1.SendVerificationMailRequest.
 * Use `create(SendVerificationMailRequestSchema)` to create a new message.
 */
export const SendVerificationMailRequestSchema: GenMessage<SendVerificationMailRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 0);

/**
 * @generated from message hami.frontend.v1.SendVerificationMailResponse
 */
export type SendVerificationMailResponse = Message<"hami.frontend.v1.SendVerificationMailResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.SendVerificationMailResponse.
 * Use `create(SendVerificationMailResponseSchema)` to create a new message.
 */
export const SendVerificationMailResponseSchema: GenMessage<SendVerificationMailResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 1);

/**
 * @generated from message hami.frontend.v1.VerifyMailTokenRequest
 */
export type VerifyMailTokenRequest = Message<"hami.frontend.v1.VerifyMailTokenRequest"> & {
  /**
   * @generated from field: string token = 1;
   */
  token: string;
};

/**
 * Describes the message hami.frontend.v1.VerifyMailTokenRequest.
 * Use `create(VerifyMailTokenRequestSchema)` to create a new message.
 */
export const VerifyMailTokenRequestSchema: GenMessage<VerifyMailTokenRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 2);

/**
 * @generated from message hami.frontend.v1.VerifyMailTokenResponse
 */
export type VerifyMailTokenResponse = Message<"hami.frontend.v1.VerifyMailTokenResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.VerifyMailTokenResponse.
 * Use `create(VerifyMailTokenResponseSchema)` to create a new message.
 */
export const VerifyMailTokenResponseSchema: GenMessage<VerifyMailTokenResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 3);

/**
 * @generated from message hami.frontend.v1.BeneficialOwnerDeclaration
 */
export type BeneficialOwnerDeclaration = Message<"hami.frontend.v1.BeneficialOwnerDeclaration"> & {
  /**
   * @generated from field: string beneficial_owner_name = 1;
   */
  beneficialOwnerName: string;

  /**
   * @generated from field: int32 beneficial_owner_birth_year = 2;
   */
  beneficialOwnerBirthYear: number;

  /**
   * @generated from field: int32 beneficial_owner_birth_month = 3;
   */
  beneficialOwnerBirthMonth: number;

  /**
   * @generated from field: int32 beneficial_owner_birth_day = 4;
   */
  beneficialOwnerBirthDay: number;

  /**
   * @generated from field: string beneficial_owner_postal_code = 5;
   */
  beneficialOwnerPostalCode: string;

  /**
   * @generated from field: string beneficial_owner_prefecture = 6;
   */
  beneficialOwnerPrefecture: string;

  /**
   * @generated from field: string beneficial_owner_address = 7;
   */
  beneficialOwnerAddress: string;

  /**
   * @generated from field: string beneficial_owner_apartment = 8;
   */
  beneficialOwnerApartment: string;

  /**
   * @generated from field: string declarant_name = 9;
   */
  declarantName: string;

  /**
   * @generated from field: string declarant_position = 10;
   */
  declarantPosition: string;

  /**
   * @generated from field: bool is_confirmed = 11;
   */
  isConfirmed: boolean;
};

/**
 * Describes the message hami.frontend.v1.BeneficialOwnerDeclaration.
 * Use `create(BeneficialOwnerDeclarationSchema)` to create a new message.
 */
export const BeneficialOwnerDeclarationSchema: GenMessage<BeneficialOwnerDeclaration> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 4);

/**
 * @generated from message hami.frontend.v1.ApplyForMembershipRequest
 */
export type ApplyForMembershipRequest = Message<"hami.frontend.v1.ApplyForMembershipRequest"> & {
  /**
   * Email is represented by a verified token
   *
   * @generated from field: string token = 1;
   */
  token: string;

  /**
   * 申込者タイプ
   *
   * @generated from field: hami.frontend.v1.ApplicantType applicant_type = 2;
   */
  applicantType: ApplicantType;

  /**
   * 個人用フィールド（個人の場合のみ必須）
   *
   * @generated from field: string first_name = 3;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 4;
   */
  lastName: string;

  /**
   * @generated from field: string first_name_kana = 5;
   */
  firstNameKana: string;

  /**
   * @generated from field: string last_name_kana = 6;
   */
  lastNameKana: string;

  /**
   * @generated from field: int32 birth_year = 7;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 8;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 9;
   */
  birthDay: number;

  /**
   * 個人用：金融情報（個人の場合のみ）
   *
   * @generated from field: string annual_income = 26;
   */
  annualIncome: string;

  /**
   * @generated from field: string deposit_amount = 27;
   */
  depositAmount: string;

  /**
   * @generated from field: string financial_assets = 28;
   */
  financialAssets: string;

  /**
   * 個人用：職業（個人の場合のみ）
   *
   * @generated from field: string occupation = 32;
   */
  occupation: string;

  /**
   * 個人用：勤務先情報（個人の場合のみ、任意）
   *
   * @generated from field: string company_name = 29;
   */
  companyName: string;

  /**
   * @generated from field: string company_address = 30;
   */
  companyAddress: string;

  /**
   * @generated from field: string company_phone_number = 31;
   */
  companyPhoneNumber: string;

  /**
   * 法人用フィールド（法人の場合のみ必須）
   *
   * @generated from field: string corporate_name = 10;
   */
  corporateName: string;

  /**
   * @generated from field: string corporate_name_kana = 11;
   */
  corporateNameKana: string;

  /**
   * @generated from field: string representative_name = 12;
   */
  representativeName: string;

  /**
   * @generated from field: string representative_name_kana = 13;
   */
  representativeNameKana: string;

  /**
   * @generated from field: string representative_position = 14;
   */
  representativePosition: string;

  /**
   * @generated from field: string corporate_number = 15;
   */
  corporateNumber: string;

  /**
   * @generated from field: int32 established_year = 16;
   */
  establishedYear: number;

  /**
   * @generated from field: int32 established_month = 17;
   */
  establishedMonth: number;

  /**
   * @generated from field: int32 established_day = 18;
   */
  establishedDay: number;

  /**
   * 共通フィールド
   *
   * @generated from field: string postal_code = 19;
   */
  postalCode: string;

  /**
   * @generated from field: string prefecture = 20;
   */
  prefecture: string;

  /**
   * @generated from field: string address = 21;
   */
  address: string;

  /**
   * @generated from field: string apartment = 22;
   */
  apartment: string;

  /**
   * @generated from field: string phone_number = 23;
   */
  phoneNumber: string;

  /**
   * 反社会的勢力に関する確認
   *
   * @generated from field: bool anti_social_force_agreement = 24;
   */
  antiSocialForceAgreement: boolean;

  /**
   * 個人情報保護方針への同意
   *
   * @generated from field: bool privacy_policy_agreement = 33;
   */
  privacyPolicyAgreement: boolean;

  /**
   * 実質的支配者申告（法人の場合のみ）
   *
   * @generated from field: repeated hami.frontend.v1.BeneficialOwnerDeclaration beneficial_owner_declarations = 25;
   */
  beneficialOwnerDeclarations: BeneficialOwnerDeclaration[];
};

/**
 * Describes the message hami.frontend.v1.ApplyForMembershipRequest.
 * Use `create(ApplyForMembershipRequestSchema)` to create a new message.
 */
export const ApplyForMembershipRequestSchema: GenMessage<ApplyForMembershipRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 5);

/**
 * @generated from message hami.frontend.v1.ApplyForMembershipResponse
 */
export type ApplyForMembershipResponse = Message<"hami.frontend.v1.ApplyForMembershipResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;

  /**
   * @generated from field: string upload_token = 2;
   */
  uploadToken: string;
};

/**
 * Describes the message hami.frontend.v1.ApplyForMembershipResponse.
 * Use `create(ApplyForMembershipResponseSchema)` to create a new message.
 */
export const ApplyForMembershipResponseSchema: GenMessage<ApplyForMembershipResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 6);

/**
 * @generated from message hami.frontend.v1.GetPersonalUploadUrlRequest
 */
export type GetPersonalUploadUrlRequest = Message<"hami.frontend.v1.GetPersonalUploadUrlRequest"> & {
  /**
   * @generated from field: string file_type = 1;
   */
  fileType: string;

  /**
   * @generated from field: string upload_token = 2;
   */
  uploadToken: string;

  /**
   * 1〜4
   *
   * @generated from field: int32 personal_index = 3;
   */
  personalIndex: number;
};

/**
 * Describes the message hami.frontend.v1.GetPersonalUploadUrlRequest.
 * Use `create(GetPersonalUploadUrlRequestSchema)` to create a new message.
 */
export const GetPersonalUploadUrlRequestSchema: GenMessage<GetPersonalUploadUrlRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 7);

/**
 * @generated from message hami.frontend.v1.GetCorporateUploadUrlRequest
 */
export type GetCorporateUploadUrlRequest = Message<"hami.frontend.v1.GetCorporateUploadUrlRequest"> & {
  /**
   * @generated from field: string file_type = 1;
   */
  fileType: string;

  /**
   * @generated from field: string upload_token = 2;
   */
  uploadToken: string;

  /**
   * CORP_* を使用
   *
   * @generated from field: hami.frontend.v1.DocumentType document_type = 3;
   */
  documentType: DocumentType;
};

/**
 * Describes the message hami.frontend.v1.GetCorporateUploadUrlRequest.
 * Use `create(GetCorporateUploadUrlRequestSchema)` to create a new message.
 */
export const GetCorporateUploadUrlRequestSchema: GenMessage<GetCorporateUploadUrlRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 8);

/**
 * @generated from message hami.frontend.v1.GetUploadUrlResponse
 */
export type GetUploadUrlResponse = Message<"hami.frontend.v1.GetUploadUrlResponse"> & {
  /**
   * @generated from field: string url = 1;
   */
  url: string;
};

/**
 * Describes the message hami.frontend.v1.GetUploadUrlResponse.
 * Use `create(GetUploadUrlResponseSchema)` to create a new message.
 */
export const GetUploadUrlResponseSchema: GenMessage<GetUploadUrlResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 9);

/**
 * @generated from message hami.frontend.v1.CompleteDocumentUploadRequest
 */
export type CompleteDocumentUploadRequest = Message<"hami.frontend.v1.CompleteDocumentUploadRequest"> & {
  /**
   * @generated from field: string upload_token = 1;
   */
  uploadToken: string;
};

/**
 * Describes the message hami.frontend.v1.CompleteDocumentUploadRequest.
 * Use `create(CompleteDocumentUploadRequestSchema)` to create a new message.
 */
export const CompleteDocumentUploadRequestSchema: GenMessage<CompleteDocumentUploadRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 10);

/**
 * @generated from message hami.frontend.v1.CompleteDocumentUploadResponse
 */
export type CompleteDocumentUploadResponse = Message<"hami.frontend.v1.CompleteDocumentUploadResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.CompleteDocumentUploadResponse.
 * Use `create(CompleteDocumentUploadResponseSchema)` to create a new message.
 */
export const CompleteDocumentUploadResponseSchema: GenMessage<CompleteDocumentUploadResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 11);

/**
 * @generated from message hami.frontend.v1.GetMembershipApplicationStatusRequest
 */
export type GetMembershipApplicationStatusRequest = Message<"hami.frontend.v1.GetMembershipApplicationStatusRequest"> & {
  /**
   * @generated from field: string token = 1;
   */
  token: string;
};

/**
 * Describes the message hami.frontend.v1.GetMembershipApplicationStatusRequest.
 * Use `create(GetMembershipApplicationStatusRequestSchema)` to create a new message.
 */
export const GetMembershipApplicationStatusRequestSchema: GenMessage<GetMembershipApplicationStatusRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 12);

/**
 * @generated from message hami.frontend.v1.GetApplicantTypeByUploadTokenRequest
 */
export type GetApplicantTypeByUploadTokenRequest = Message<"hami.frontend.v1.GetApplicantTypeByUploadTokenRequest"> & {
  /**
   * @generated from field: string upload_token = 1;
   */
  uploadToken: string;
};

/**
 * Describes the message hami.frontend.v1.GetApplicantTypeByUploadTokenRequest.
 * Use `create(GetApplicantTypeByUploadTokenRequestSchema)` to create a new message.
 */
export const GetApplicantTypeByUploadTokenRequestSchema: GenMessage<GetApplicantTypeByUploadTokenRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 13);

/**
 * @generated from message hami.frontend.v1.GetApplicantTypeByUploadTokenResponse
 */
export type GetApplicantTypeByUploadTokenResponse = Message<"hami.frontend.v1.GetApplicantTypeByUploadTokenResponse"> & {
  /**
   * @generated from field: hami.frontend.v1.ApplicantType applicant_type = 1;
   */
  applicantType: ApplicantType;

  /**
   * @generated from field: bool is_completed = 2;
   */
  isCompleted: boolean;
};

/**
 * Describes the message hami.frontend.v1.GetApplicantTypeByUploadTokenResponse.
 * Use `create(GetApplicantTypeByUploadTokenResponseSchema)` to create a new message.
 */
export const GetApplicantTypeByUploadTokenResponseSchema: GenMessage<GetApplicantTypeByUploadTokenResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 14);

/**
 * @generated from message hami.frontend.v1.DocumentGroup
 */
export type DocumentGroup = Message<"hami.frontend.v1.DocumentGroup"> & {
  /**
   * @generated from field: string upload_token = 1;
   */
  uploadToken: string;

  /**
   * @generated from field: string group_key = 2;
   */
  groupKey: string;

  /**
   * @generated from field: bool is_completed = 3;
   */
  isCompleted: boolean;
};

/**
 * Describes the message hami.frontend.v1.DocumentGroup.
 * Use `create(DocumentGroupSchema)` to create a new message.
 */
export const DocumentGroupSchema: GenMessage<DocumentGroup> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 15);

/**
 * @generated from message hami.frontend.v1.GetMembershipApplicationStatusResponse
 */
export type GetMembershipApplicationStatusResponse = Message<"hami.frontend.v1.GetMembershipApplicationStatusResponse"> & {
  /**
   * @generated from field: bool applied = 1;
   */
  applied: boolean;

  /**
   * @generated from field: string applied_at = 2;
   */
  appliedAt: string;

  /**
   * @generated from field: hami.frontend.v1.ReviewType application_review_status = 3;
   */
  applicationReviewStatus: ReviewType;

  /**
   * @generated from field: hami.frontend.v1.ReviewType document_group_review_status = 4;
   */
  documentGroupReviewStatus: ReviewType;

  /**
   * @generated from field: string remand_reason = 5;
   */
  remandReason: string;

  /**
   * @generated from field: hami.frontend.v1.DocumentGroup current_document_group = 6;
   */
  currentDocumentGroup?: DocumentGroup;

  /**
   * @generated from field: hami.frontend.v1.ApplicantType applicant_type = 7;
   */
  applicantType: ApplicantType;

  /**
   * 追加: コンプライアンス審査ステータス
   *
   * @generated from field: hami.frontend.v1.ReviewType compliance_application_review_status = 8;
   */
  complianceApplicationReviewStatus: ReviewType;

  /**
   * @generated from field: hami.frontend.v1.ReviewType compliance_document_group_review_status = 9;
   */
  complianceDocumentGroupReviewStatus: ReviewType;
};

/**
 * Describes the message hami.frontend.v1.GetMembershipApplicationStatusResponse.
 * Use `create(GetMembershipApplicationStatusResponseSchema)` to create a new message.
 */
export const GetMembershipApplicationStatusResponseSchema: GenMessage<GetMembershipApplicationStatusResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 16);

/**
 * @generated from message hami.frontend.v1.UploadedDocument
 */
export type UploadedDocument = Message<"hami.frontend.v1.UploadedDocument"> & {
  /**
   * @generated from field: int32 identity_document_id = 1;
   */
  identityDocumentId: number;

  /**
   * @generated from field: string image_url = 2;
   */
  imageUrl: string;

  /**
   * @generated from field: int64 expires_at = 3;
   */
  expiresAt: bigint;

  /**
   * @generated from field: optional int32 personal_index = 4;
   */
  personalIndex?: number;
};

/**
 * Describes the message hami.frontend.v1.UploadedDocument.
 * Use `create(UploadedDocumentSchema)` to create a new message.
 */
export const UploadedDocumentSchema: GenMessage<UploadedDocument> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 17);

/**
 * @generated from message hami.frontend.v1.GetUploadedDocumentsRequest
 */
export type GetUploadedDocumentsRequest = Message<"hami.frontend.v1.GetUploadedDocumentsRequest"> & {
  /**
   * @generated from field: string upload_token = 1;
   */
  uploadToken: string;
};

/**
 * Describes the message hami.frontend.v1.GetUploadedDocumentsRequest.
 * Use `create(GetUploadedDocumentsRequestSchema)` to create a new message.
 */
export const GetUploadedDocumentsRequestSchema: GenMessage<GetUploadedDocumentsRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 18);

/**
 * @generated from message hami.frontend.v1.GetUploadedDocumentsResponse
 */
export type GetUploadedDocumentsResponse = Message<"hami.frontend.v1.GetUploadedDocumentsResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.UploadedDocument documents = 1;
   */
  documents: UploadedDocument[];
};

/**
 * Describes the message hami.frontend.v1.GetUploadedDocumentsResponse.
 * Use `create(GetUploadedDocumentsResponseSchema)` to create a new message.
 */
export const GetUploadedDocumentsResponseSchema: GenMessage<GetUploadedDocumentsResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 19);

/**
 * @generated from message hami.frontend.v1.GetMembershipApplicationByUploadTokenRequest
 */
export type GetMembershipApplicationByUploadTokenRequest = Message<"hami.frontend.v1.GetMembershipApplicationByUploadTokenRequest"> & {
  /**
   * @generated from field: string upload_token = 1;
   */
  uploadToken: string;
};

/**
 * Describes the message hami.frontend.v1.GetMembershipApplicationByUploadTokenRequest.
 * Use `create(GetMembershipApplicationByUploadTokenRequestSchema)` to create a new message.
 */
export const GetMembershipApplicationByUploadTokenRequestSchema: GenMessage<GetMembershipApplicationByUploadTokenRequest> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 20);

/**
 * @generated from message hami.frontend.v1.GetMembershipApplicationByUploadTokenResponse
 */
export type GetMembershipApplicationByUploadTokenResponse = Message<"hami.frontend.v1.GetMembershipApplicationByUploadTokenResponse"> & {
  /**
   * @generated from field: hami.frontend.v1.ApplicantType applicant_type = 1;
   */
  applicantType: ApplicantType;

  /**
   * 個人用フィールド
   *
   * @generated from field: string last_name = 2;
   */
  lastName: string;

  /**
   * @generated from field: string first_name = 3;
   */
  firstName: string;

  /**
   * @generated from field: string last_name_kana = 4;
   */
  lastNameKana: string;

  /**
   * @generated from field: string first_name_kana = 5;
   */
  firstNameKana: string;

  /**
   * @generated from field: int32 birth_year = 6;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 7;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 8;
   */
  birthDay: number;

  /**
   * @generated from field: string occupation = 9;
   */
  occupation: string;

  /**
   * 個人用：金融情報
   *
   * @generated from field: string annual_income = 25;
   */
  annualIncome: string;

  /**
   * @generated from field: string deposit_amount = 26;
   */
  depositAmount: string;

  /**
   * @generated from field: string financial_assets = 27;
   */
  financialAssets: string;

  /**
   * 個人用：勤務先情報
   *
   * @generated from field: string company_name = 28;
   */
  companyName: string;

  /**
   * @generated from field: string company_address = 29;
   */
  companyAddress: string;

  /**
   * @generated from field: string company_phone_number = 30;
   */
  companyPhoneNumber: string;

  /**
   * 法人用フィールド
   *
   * @generated from field: string corporate_name = 10;
   */
  corporateName: string;

  /**
   * @generated from field: string corporate_name_kana = 11;
   */
  corporateNameKana: string;

  /**
   * @generated from field: string representative_name = 12;
   */
  representativeName: string;

  /**
   * @generated from field: string representative_name_kana = 13;
   */
  representativeNameKana: string;

  /**
   * @generated from field: string representative_position = 14;
   */
  representativePosition: string;

  /**
   * @generated from field: string corporate_number = 15;
   */
  corporateNumber: string;

  /**
   * @generated from field: int32 established_year = 16;
   */
  establishedYear: number;

  /**
   * @generated from field: int32 established_month = 17;
   */
  establishedMonth: number;

  /**
   * @generated from field: int32 established_day = 18;
   */
  establishedDay: number;

  /**
   * 共通フィールド
   *
   * @generated from field: string postal_code = 19;
   */
  postalCode: string;

  /**
   * @generated from field: string prefecture = 20;
   */
  prefecture: string;

  /**
   * @generated from field: string address = 21;
   */
  address: string;

  /**
   * @generated from field: string apartment = 22;
   */
  apartment: string;

  /**
   * @generated from field: string phone_number = 23;
   */
  phoneNumber: string;

  /**
   * 実質的支配者申告（法人の場合のみ）
   *
   * @generated from field: repeated hami.frontend.v1.BeneficialOwnerDeclaration beneficial_owner_declarations = 24;
   */
  beneficialOwnerDeclarations: BeneficialOwnerDeclaration[];
};

/**
 * Describes the message hami.frontend.v1.GetMembershipApplicationByUploadTokenResponse.
 * Use `create(GetMembershipApplicationByUploadTokenResponseSchema)` to create a new message.
 */
export const GetMembershipApplicationByUploadTokenResponseSchema: GenMessage<GetMembershipApplicationByUploadTokenResponse> = /*@__PURE__*/
  messageDesc(file_membership_application_service, 21);

/**
 * @generated from enum hami.frontend.v1.ReviewType
 */
export enum ReviewType {
  /**
   * @generated from enum value: REVIEW_TYPE_UNKNOWN = 0;
   */
  REVIEW_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: APPROVE = 1;
   */
  APPROVE = 1,

  /**
   * @generated from enum value: REJECT = 2;
   */
  REJECT = 2,

  /**
   * @generated from enum value: REMAND = 3;
   */
  REMAND = 3,

  /**
   * @generated from enum value: NOT_REVIEWED = 4;
   */
  NOT_REVIEWED = 4,
}

/**
 * Describes the enum hami.frontend.v1.ReviewType.
 */
export const ReviewTypeSchema: GenEnum<ReviewType> = /*@__PURE__*/
  enumDesc(file_membership_application_service, 0);

/**
 * @generated from enum hami.frontend.v1.ApplicantType
 */
export enum ApplicantType {
  /**
   * @generated from enum value: APPLICANT_TYPE_UNKNOWN = 0;
   */
  APPLICANT_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: INDIVIDUAL = 1;
   */
  INDIVIDUAL = 1,

  /**
   * @generated from enum value: CORPORATE = 2;
   */
  CORPORATE = 2,
}

/**
 * Describes the enum hami.frontend.v1.ApplicantType.
 */
export const ApplicantTypeSchema: GenEnum<ApplicantType> = /*@__PURE__*/
  enumDesc(file_membership_application_service, 1);

/**
 * @generated from enum hami.frontend.v1.DocumentType
 */
export enum DocumentType {
  /**
   * @generated from enum value: DOCUMENT_TYPE_UNKNOWN = 0;
   */
  DOCUMENT_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: IDENTITY_FRONT = 1;
   */
  IDENTITY_FRONT = 1,

  /**
   * @generated from enum value: IDENTITY_BACK = 2;
   */
  IDENTITY_BACK = 2,

  /**
   * 法人KYC書類
   *
   * 印鑑証明書
   *
   * @generated from enum value: CORP_SEAL_CERT = 3;
   */
  CORP_SEAL_CERT = 3,

  /**
   * 履歴事項（全部/現在）証明書
   *
   * @generated from enum value: CORP_REGISTRY_CERT = 4;
   */
  CORP_REGISTRY_CERT = 4,
}

/**
 * Describes the enum hami.frontend.v1.DocumentType.
 */
export const DocumentTypeSchema: GenEnum<DocumentType> = /*@__PURE__*/
  enumDesc(file_membership_application_service, 2);

/**
 * @generated from service hami.frontend.v1.MembershipApplicationService
 */
export const MembershipApplicationService: GenService<{
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.SendVerificationMail
   */
  sendVerificationMail: {
    methodKind: "unary";
    input: typeof SendVerificationMailRequestSchema;
    output: typeof SendVerificationMailResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.VerifyMailToken
   */
  verifyMailToken: {
    methodKind: "unary";
    input: typeof VerifyMailTokenRequestSchema;
    output: typeof VerifyMailTokenResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.ApplyForMembership
   */
  applyForMembership: {
    methodKind: "unary";
    input: typeof ApplyForMembershipRequestSchema;
    output: typeof ApplyForMembershipResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.GetPersonalUploadUrl
   */
  getPersonalUploadUrl: {
    methodKind: "unary";
    input: typeof GetPersonalUploadUrlRequestSchema;
    output: typeof GetUploadUrlResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.GetCorporateUploadUrl
   */
  getCorporateUploadUrl: {
    methodKind: "unary";
    input: typeof GetCorporateUploadUrlRequestSchema;
    output: typeof GetUploadUrlResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.CompleteDocumentUpload
   */
  completeDocumentUpload: {
    methodKind: "unary";
    input: typeof CompleteDocumentUploadRequestSchema;
    output: typeof CompleteDocumentUploadResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.GetMembershipApplicationStatus
   */
  getMembershipApplicationStatus: {
    methodKind: "unary";
    input: typeof GetMembershipApplicationStatusRequestSchema;
    output: typeof GetMembershipApplicationStatusResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.GetApplicantTypeByUploadToken
   */
  getApplicantTypeByUploadToken: {
    methodKind: "unary";
    input: typeof GetApplicantTypeByUploadTokenRequestSchema;
    output: typeof GetApplicantTypeByUploadTokenResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.GetUploadedDocuments
   */
  getUploadedDocuments: {
    methodKind: "unary";
    input: typeof GetUploadedDocumentsRequestSchema;
    output: typeof GetUploadedDocumentsResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.MembershipApplicationService.GetMembershipApplicationByUploadToken
   */
  getMembershipApplicationByUploadToken: {
    methodKind: "unary";
    input: typeof GetMembershipApplicationByUploadTokenRequestSchema;
    output: typeof GetMembershipApplicationByUploadTokenResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_membership_application_service, 0);

