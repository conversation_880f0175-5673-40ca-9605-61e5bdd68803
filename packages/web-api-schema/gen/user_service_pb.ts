// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file user_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file user_service.proto.
 */
export const file_user_service: GenFile = /*@__PURE__*/
  fileDesc("ChJ1c2VyX3NlcnZpY2UucHJvdG8SEGhhbWkuZnJvbnRlbmQudjEiLAobUmVxdWVzdFBhc3N3b3JkUmVzZXRSZXF1ZXN0Eg0KBWVtYWlsGAEgASgJIi8KHFJlcXVlc3RQYXNzd29yZFJlc2V0UmVzcG9uc2USDwoHbWVzc2FnZRgBIAEoCSI7ChRSZXNldFBhc3N3b3JkUmVxdWVzdBINCgV0b2tlbhgBIAEoCRIUCgxuZXdfcGFzc3dvcmQYAiABKAkiKAoVUmVzZXRQYXNzd29yZFJlc3BvbnNlEg8KB21lc3NhZ2UYASABKAkiRwoVQ2hhbmdlUGFzc3dvcmRSZXF1ZXN0EhgKEGN1cnJlbnRfcGFzc3dvcmQYASABKAkSFAoMbmV3X3Bhc3N3b3JkGAIgASgJIikKFkNoYW5nZVBhc3N3b3JkUmVzcG9uc2USDwoHbWVzc2FnZRgBIAEoCSIvCgxMb2dpblJlcXVlc3QSDQoFZW1haWwYASABKAkSEAoIcGFzc3dvcmQYAiABKAkiRAoNTG9naW5SZXNwb25zZRIVCg1zZXNzaW9uX3Rva2VuGAEgASgJEhwKFG11c3RfY2hhbmdlX3Bhc3N3b3JkGAIgASgIIiUKDEdldE1lUmVxdWVzdBIVCg1zZXNzaW9uX3Rva2VuGAEgASgJIh0KDUdldE1lUmVzcG9uc2USDAoEbmFtZRgBIAEoCSImCg1Mb2dvdXRSZXF1ZXN0EhUKDXNlc3Npb25fdG9rZW4YASABKAkiIQoOTG9nb3V0UmVzcG9uc2USDwoHbWVzc2FnZRgBIAEoCTKsBAoLVXNlclNlcnZpY2USdQoUUmVxdWVzdFBhc3N3b3JkUmVzZXQSLS5oYW1pLmZyb250ZW5kLnYxLlJlcXVlc3RQYXNzd29yZFJlc2V0UmVxdWVzdBouLmhhbWkuZnJvbnRlbmQudjEuUmVxdWVzdFBhc3N3b3JkUmVzZXRSZXNwb25zZRJgCg1SZXNldFBhc3N3b3JkEiYuaGFtaS5mcm9udGVuZC52MS5SZXNldFBhc3N3b3JkUmVxdWVzdBonLmhhbWkuZnJvbnRlbmQudjEuUmVzZXRQYXNzd29yZFJlc3BvbnNlEmMKDkNoYW5nZVBhc3N3b3JkEicuaGFtaS5mcm9udGVuZC52MS5DaGFuZ2VQYXNzd29yZFJlcXVlc3QaKC5oYW1pLmZyb250ZW5kLnYxLkNoYW5nZVBhc3N3b3JkUmVzcG9uc2USSAoFTG9naW4SHi5oYW1pLmZyb250ZW5kLnYxLkxvZ2luUmVxdWVzdBofLmhhbWkuZnJvbnRlbmQudjEuTG9naW5SZXNwb25zZRJICgVHZXRNZRIeLmhhbWkuZnJvbnRlbmQudjEuR2V0TWVSZXF1ZXN0Gh8uaGFtaS5mcm9udGVuZC52MS5HZXRNZVJlc3BvbnNlEksKBkxvZ291dBIfLmhhbWkuZnJvbnRlbmQudjEuTG9nb3V0UmVxdWVzdBogLmhhbWkuZnJvbnRlbmQudjEuTG9nb3V0UmVzcG9uc2ViBnByb3RvMw");

/**
 * @generated from message hami.frontend.v1.RequestPasswordResetRequest
 */
export type RequestPasswordResetRequest = Message<"hami.frontend.v1.RequestPasswordResetRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;
};

/**
 * Describes the message hami.frontend.v1.RequestPasswordResetRequest.
 * Use `create(RequestPasswordResetRequestSchema)` to create a new message.
 */
export const RequestPasswordResetRequestSchema: GenMessage<RequestPasswordResetRequest> = /*@__PURE__*/
  messageDesc(file_user_service, 0);

/**
 * @generated from message hami.frontend.v1.RequestPasswordResetResponse
 */
export type RequestPasswordResetResponse = Message<"hami.frontend.v1.RequestPasswordResetResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.RequestPasswordResetResponse.
 * Use `create(RequestPasswordResetResponseSchema)` to create a new message.
 */
export const RequestPasswordResetResponseSchema: GenMessage<RequestPasswordResetResponse> = /*@__PURE__*/
  messageDesc(file_user_service, 1);

/**
 * @generated from message hami.frontend.v1.ResetPasswordRequest
 */
export type ResetPasswordRequest = Message<"hami.frontend.v1.ResetPasswordRequest"> & {
  /**
   * @generated from field: string token = 1;
   */
  token: string;

  /**
   * @generated from field: string new_password = 2;
   */
  newPassword: string;
};

/**
 * Describes the message hami.frontend.v1.ResetPasswordRequest.
 * Use `create(ResetPasswordRequestSchema)` to create a new message.
 */
export const ResetPasswordRequestSchema: GenMessage<ResetPasswordRequest> = /*@__PURE__*/
  messageDesc(file_user_service, 2);

/**
 * @generated from message hami.frontend.v1.ResetPasswordResponse
 */
export type ResetPasswordResponse = Message<"hami.frontend.v1.ResetPasswordResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.ResetPasswordResponse.
 * Use `create(ResetPasswordResponseSchema)` to create a new message.
 */
export const ResetPasswordResponseSchema: GenMessage<ResetPasswordResponse> = /*@__PURE__*/
  messageDesc(file_user_service, 3);

/**
 * @generated from message hami.frontend.v1.ChangePasswordRequest
 */
export type ChangePasswordRequest = Message<"hami.frontend.v1.ChangePasswordRequest"> & {
  /**
   * @generated from field: string current_password = 1;
   */
  currentPassword: string;

  /**
   * @generated from field: string new_password = 2;
   */
  newPassword: string;
};

/**
 * Describes the message hami.frontend.v1.ChangePasswordRequest.
 * Use `create(ChangePasswordRequestSchema)` to create a new message.
 */
export const ChangePasswordRequestSchema: GenMessage<ChangePasswordRequest> = /*@__PURE__*/
  messageDesc(file_user_service, 4);

/**
 * @generated from message hami.frontend.v1.ChangePasswordResponse
 */
export type ChangePasswordResponse = Message<"hami.frontend.v1.ChangePasswordResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.ChangePasswordResponse.
 * Use `create(ChangePasswordResponseSchema)` to create a new message.
 */
export const ChangePasswordResponseSchema: GenMessage<ChangePasswordResponse> = /*@__PURE__*/
  messageDesc(file_user_service, 5);

/**
 * @generated from message hami.frontend.v1.LoginRequest
 */
export type LoginRequest = Message<"hami.frontend.v1.LoginRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;
};

/**
 * Describes the message hami.frontend.v1.LoginRequest.
 * Use `create(LoginRequestSchema)` to create a new message.
 */
export const LoginRequestSchema: GenMessage<LoginRequest> = /*@__PURE__*/
  messageDesc(file_user_service, 6);

/**
 * @generated from message hami.frontend.v1.LoginResponse
 */
export type LoginResponse = Message<"hami.frontend.v1.LoginResponse"> & {
  /**
   * @generated from field: string session_token = 1;
   */
  sessionToken: string;

  /**
   * @generated from field: bool must_change_password = 2;
   */
  mustChangePassword: boolean;
};

/**
 * Describes the message hami.frontend.v1.LoginResponse.
 * Use `create(LoginResponseSchema)` to create a new message.
 */
export const LoginResponseSchema: GenMessage<LoginResponse> = /*@__PURE__*/
  messageDesc(file_user_service, 7);

/**
 * @generated from message hami.frontend.v1.GetMeRequest
 */
export type GetMeRequest = Message<"hami.frontend.v1.GetMeRequest"> & {
  /**
   * @generated from field: string session_token = 1;
   */
  sessionToken: string;
};

/**
 * Describes the message hami.frontend.v1.GetMeRequest.
 * Use `create(GetMeRequestSchema)` to create a new message.
 */
export const GetMeRequestSchema: GenMessage<GetMeRequest> = /*@__PURE__*/
  messageDesc(file_user_service, 8);

/**
 * @generated from message hami.frontend.v1.GetMeResponse
 */
export type GetMeResponse = Message<"hami.frontend.v1.GetMeResponse"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;
};

/**
 * Describes the message hami.frontend.v1.GetMeResponse.
 * Use `create(GetMeResponseSchema)` to create a new message.
 */
export const GetMeResponseSchema: GenMessage<GetMeResponse> = /*@__PURE__*/
  messageDesc(file_user_service, 9);

/**
 * @generated from message hami.frontend.v1.LogoutRequest
 */
export type LogoutRequest = Message<"hami.frontend.v1.LogoutRequest"> & {
  /**
   * @generated from field: string session_token = 1;
   */
  sessionToken: string;
};

/**
 * Describes the message hami.frontend.v1.LogoutRequest.
 * Use `create(LogoutRequestSchema)` to create a new message.
 */
export const LogoutRequestSchema: GenMessage<LogoutRequest> = /*@__PURE__*/
  messageDesc(file_user_service, 10);

/**
 * @generated from message hami.frontend.v1.LogoutResponse
 */
export type LogoutResponse = Message<"hami.frontend.v1.LogoutResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.LogoutResponse.
 * Use `create(LogoutResponseSchema)` to create a new message.
 */
export const LogoutResponseSchema: GenMessage<LogoutResponse> = /*@__PURE__*/
  messageDesc(file_user_service, 11);

/**
 * @generated from service hami.frontend.v1.UserService
 */
export const UserService: GenService<{
  /**
   * @generated from rpc hami.frontend.v1.UserService.RequestPasswordReset
   */
  requestPasswordReset: {
    methodKind: "unary";
    input: typeof RequestPasswordResetRequestSchema;
    output: typeof RequestPasswordResetResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.UserService.ResetPassword
   */
  resetPassword: {
    methodKind: "unary";
    input: typeof ResetPasswordRequestSchema;
    output: typeof ResetPasswordResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.UserService.ChangePassword
   */
  changePassword: {
    methodKind: "unary";
    input: typeof ChangePasswordRequestSchema;
    output: typeof ChangePasswordResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.UserService.Login
   */
  login: {
    methodKind: "unary";
    input: typeof LoginRequestSchema;
    output: typeof LoginResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.UserService.GetMe
   */
  getMe: {
    methodKind: "unary";
    input: typeof GetMeRequestSchema;
    output: typeof GetMeResponseSchema;
  },
  /**
   * @generated from rpc hami.frontend.v1.UserService.Logout
   */
  logout: {
    methodKind: "unary";
    input: typeof LogoutRequestSchema;
    output: typeof LogoutResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_user_service, 0);

