// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file annual_bundle_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file annual_bundle_service.proto.
 */
export const file_annual_bundle_service: GenFile = /*@__PURE__*/
  fileDesc("Chthbm51YWxfYnVuZGxlX3NlcnZpY2UucHJvdG8SEGhhbWkuZnJvbnRlbmQudjEiOwokTGlzdEFubnVhbEJ1bmRsZXNCeUZpc2NhbFllYXJSZXF1ZXN0EhMKC2Zpc2NhbF95ZWFyGAEgASgFIlgKJUxpc3RBbm51YWxCdW5kbGVzQnlGaXNjYWxZZWFyUmVzcG9uc2USLwoHYnVuZGxlcxgBIAMoCzIeLmhhbWkuZnJvbnRlbmQudjEuQW5udWFsQnVuZGxlIsQCCgxBbm51YWxCdW5kbGUSGAoQYW5udWFsX2J1bmRsZV9pZBgBIAEoBRITCgtmaXNjYWxfeWVhchgCIAEoBRIMCgRuYW1lGAMgASgJEg4KBnNoYXJlcxgEIAEoBRJDCg5wdWJsaXNoX3N0YXR1cxgFIAEoDjIrLmhhbWkuZnJvbnRlbmQudjEuQW5udWFsQnVuZGxlUHVibGlzaFN0YXR1cxJLChJyZWNydWl0bWVudF9zdGF0dXMYBiABKA4yLy5oYW1pLmZyb250ZW5kLnYxLkFubnVhbEJ1bmRsZVJlY3J1aXRtZW50U3RhdHVzEjsKBmhvcnNlcxgHIAMoCzIrLmhhbWkuZnJvbnRlbmQudjEuQW5udWFsQnVuZGxlSG9yc2VSZWxhdGlvbhIYChBwZXJfc2hhcmVfYW1vdW50GAggASgFInMKGUFubnVhbEJ1bmRsZUhvcnNlUmVsYXRpb24SEAoIaG9yc2VfaWQYASABKAUSEgoKaG9yc2VfbmFtZRgCIAEoCRIYChByZWNydWl0bWVudF9uYW1lGAMgASgJEhYKDnJlY3J1aXRtZW50X25vGAQgASgFKmgKGUFubnVhbEJ1bmRsZVB1Ymxpc2hTdGF0dXMSLAooQU5OVUFMX0JVTkRMRV9QVUJMSVNIX1NUQVRVU19VTlNQRUNJRklFRBAAEg0KCUFCX1BVQkxJQxABEg4KCkFCX1BSSVZBVEUQAiqNAQodQW5udWFsQnVuZGxlUmVjcnVpdG1lbnRTdGF0dXMSMAosQU5OVUFMX0JVTkRMRV9SRUNSVUlUTUVOVF9TVEFUVVNfVU5TUEVDSUZJRUQQABIPCgtBQl9VUENPTUlORxABEg0KCUFCX0FDVElWRRACEg0KCUFCX0NMT1NFRBADEgsKB0FCX0ZVTEwQBDKoAQoTQW5udWFsQnVuZGxlU2VydmljZRKQAQodTGlzdEFubnVhbEJ1bmRsZXNCeUZpc2NhbFllYXISNi5oYW1pLmZyb250ZW5kLnYxLkxpc3RBbm51YWxCdW5kbGVzQnlGaXNjYWxZZWFyUmVxdWVzdBo3LmhhbWkuZnJvbnRlbmQudjEuTGlzdEFubnVhbEJ1bmRsZXNCeUZpc2NhbFllYXJSZXNwb25zZWIGcHJvdG8z");

/**
 * @generated from message hami.frontend.v1.ListAnnualBundlesByFiscalYearRequest
 */
export type ListAnnualBundlesByFiscalYearRequest = Message<"hami.frontend.v1.ListAnnualBundlesByFiscalYearRequest"> & {
  /**
   * @generated from field: int32 fiscal_year = 1;
   */
  fiscalYear: number;
};

/**
 * Describes the message hami.frontend.v1.ListAnnualBundlesByFiscalYearRequest.
 * Use `create(ListAnnualBundlesByFiscalYearRequestSchema)` to create a new message.
 */
export const ListAnnualBundlesByFiscalYearRequestSchema: GenMessage<ListAnnualBundlesByFiscalYearRequest> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 0);

/**
 * @generated from message hami.frontend.v1.ListAnnualBundlesByFiscalYearResponse
 */
export type ListAnnualBundlesByFiscalYearResponse = Message<"hami.frontend.v1.ListAnnualBundlesByFiscalYearResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.AnnualBundle bundles = 1;
   */
  bundles: AnnualBundle[];
};

/**
 * Describes the message hami.frontend.v1.ListAnnualBundlesByFiscalYearResponse.
 * Use `create(ListAnnualBundlesByFiscalYearResponseSchema)` to create a new message.
 */
export const ListAnnualBundlesByFiscalYearResponseSchema: GenMessage<ListAnnualBundlesByFiscalYearResponse> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 1);

/**
 * @generated from message hami.frontend.v1.AnnualBundle
 */
export type AnnualBundle = Message<"hami.frontend.v1.AnnualBundle"> & {
  /**
   * @generated from field: int32 annual_bundle_id = 1;
   */
  annualBundleId: number;

  /**
   * @generated from field: int32 fiscal_year = 2;
   */
  fiscalYear: number;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: int32 shares = 4;
   */
  shares: number;

  /**
   * @generated from field: hami.frontend.v1.AnnualBundlePublishStatus publish_status = 5;
   */
  publishStatus: AnnualBundlePublishStatus;

  /**
   * @generated from field: hami.frontend.v1.AnnualBundleRecruitmentStatus recruitment_status = 6;
   */
  recruitmentStatus: AnnualBundleRecruitmentStatus;

  /**
   * 表示用の最小限の紐付け情報
   *
   * @generated from field: repeated hami.frontend.v1.AnnualBundleHorseRelation horses = 7;
   */
  horses: AnnualBundleHorseRelation[];

  /**
   * バンドル一口あたりの金額（= 各馬 amount_total / shares_total の合計）
   *
   * @generated from field: int32 per_share_amount = 8;
   */
  perShareAmount: number;
};

/**
 * Describes the message hami.frontend.v1.AnnualBundle.
 * Use `create(AnnualBundleSchema)` to create a new message.
 */
export const AnnualBundleSchema: GenMessage<AnnualBundle> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 2);

/**
 * @generated from message hami.frontend.v1.AnnualBundleHorseRelation
 */
export type AnnualBundleHorseRelation = Message<"hami.frontend.v1.AnnualBundleHorseRelation"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string horse_name = 2;
   */
  horseName: string;

  /**
   * @generated from field: string recruitment_name = 3;
   */
  recruitmentName: string;

  /**
   * @generated from field: int32 recruitment_no = 4;
   */
  recruitmentNo: number;
};

/**
 * Describes the message hami.frontend.v1.AnnualBundleHorseRelation.
 * Use `create(AnnualBundleHorseRelationSchema)` to create a new message.
 */
export const AnnualBundleHorseRelationSchema: GenMessage<AnnualBundleHorseRelation> = /*@__PURE__*/
  messageDesc(file_annual_bundle_service, 3);

/**
 * 年度バンドルの公開ステータス
 *
 * @generated from enum hami.frontend.v1.AnnualBundlePublishStatus
 */
export enum AnnualBundlePublishStatus {
  /**
   * @generated from enum value: ANNUAL_BUNDLE_PUBLISH_STATUS_UNSPECIFIED = 0;
   */
  ANNUAL_BUNDLE_PUBLISH_STATUS_UNSPECIFIED = 0,

  /**
   * 公開
   *
   * @generated from enum value: AB_PUBLIC = 1;
   */
  AB_PUBLIC = 1,

  /**
   * 非公開
   *
   * @generated from enum value: AB_PRIVATE = 2;
   */
  AB_PRIVATE = 2,
}

/**
 * Describes the enum hami.frontend.v1.AnnualBundlePublishStatus.
 */
export const AnnualBundlePublishStatusSchema: GenEnum<AnnualBundlePublishStatus> = /*@__PURE__*/
  enumDesc(file_annual_bundle_service, 0);

/**
 * 年度バンドルの募集ステータス
 *
 * @generated from enum hami.frontend.v1.AnnualBundleRecruitmentStatus
 */
export enum AnnualBundleRecruitmentStatus {
  /**
   * @generated from enum value: ANNUAL_BUNDLE_RECRUITMENT_STATUS_UNSPECIFIED = 0;
   */
  ANNUAL_BUNDLE_RECRUITMENT_STATUS_UNSPECIFIED = 0,

  /**
   * 募集前
   *
   * @generated from enum value: AB_UPCOMING = 1;
   */
  AB_UPCOMING = 1,

  /**
   * 募集中
   *
   * @generated from enum value: AB_ACTIVE = 2;
   */
  AB_ACTIVE = 2,

  /**
   * 募集終了
   *
   * @generated from enum value: AB_CLOSED = 3;
   */
  AB_CLOSED = 3,

  /**
   * 満口
   *
   * @generated from enum value: AB_FULL = 4;
   */
  AB_FULL = 4,
}

/**
 * Describes the enum hami.frontend.v1.AnnualBundleRecruitmentStatus.
 */
export const AnnualBundleRecruitmentStatusSchema: GenEnum<AnnualBundleRecruitmentStatus> = /*@__PURE__*/
  enumDesc(file_annual_bundle_service, 1);

/**
 * 年度バンドル（フロントエンド向け）
 *
 * @generated from service hami.frontend.v1.AnnualBundleService
 */
export const AnnualBundleService: GenService<{
  /**
   * 年度を指定して年度バンドルを一覧取得（公開中のみ、0件可）
   *
   * @generated from rpc hami.frontend.v1.AnnualBundleService.ListAnnualBundlesByFiscalYear
   */
  listAnnualBundlesByFiscalYear: {
    methodKind: "unary";
    input: typeof ListAnnualBundlesByFiscalYearRequestSchema;
    output: typeof ListAnnualBundlesByFiscalYearResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_annual_bundle_service, 0);

