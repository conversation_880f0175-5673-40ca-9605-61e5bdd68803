// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file member_claim_and_pay_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file member_claim_and_pay_service.proto.
 */
export const file_member_claim_and_pay_service: GenFile = /*@__PURE__*/
  fileDesc("CiJtZW1iZXJfY2xhaW1fYW5kX3BheV9zZXJ2aWNlLnByb3RvEhBoYW1pLmZyb250ZW5kLnYxIqgCChFNZW1iZXJDbGFpbUFuZFBheRIfChdtZW1iZXJfY2xhaW1fYW5kX3BheV9pZBgBIAEoBRIRCgltZW1iZXJfaWQYAiABKAUSFQoNb2NjdXJyZWRfeWVhchgDIAEoBRIWCg5vY2N1cnJlZF9tb250aBgEIAEoBRIUCgxvY2N1cnJlZF9kYXkYBSABKAUSFAoMY2xhaW1fYW1vdW50GAYgASgFEhIKCnBheV9hbW91bnQYByABKAUSHwoSc3RhdGVtZW50X2ZpbGVfa2V5GAggASgJSACIAQESIAoTc3RhdGVtZW50X2lzc3VlZF9hdBgJIAEoCUgBiAEBQhUKE19zdGF0ZW1lbnRfZmlsZV9rZXlCFgoUX3N0YXRlbWVudF9pc3N1ZWRfYXQiHwodTGlzdE1lbWJlckNsYWltQW5kUGF5c1JlcXVlc3QiZAoeTGlzdE1lbWJlckNsYWltQW5kUGF5c1Jlc3BvbnNlEkIKFW1lbWJlcl9jbGFpbV9hbmRfcGF5cxgBIAMoCzIjLmhhbWkuZnJvbnRlbmQudjEuTWVtYmVyQ2xhaW1BbmRQYXkiQwolUmVxdWVzdFN0YXRlbWVudFBkZkRvd25sb2FkVXJsUmVxdWVzdBIaChJzdGF0ZW1lbnRfZmlsZV9rZXkYASABKAkiPgomUmVxdWVzdFN0YXRlbWVudFBkZkRvd25sb2FkVXJsUmVzcG9uc2USFAoMZG93bmxvYWRfdXJsGAEgASgJMq0CChhNZW1iZXJDbGFpbUFuZFBheVNlcnZpY2USewoWTGlzdE1lbWJlckNsYWltQW5kUGF5cxIvLmhhbWkuZnJvbnRlbmQudjEuTGlzdE1lbWJlckNsYWltQW5kUGF5c1JlcXVlc3QaMC5oYW1pLmZyb250ZW5kLnYxLkxpc3RNZW1iZXJDbGFpbUFuZFBheXNSZXNwb25zZRKTAQoeUmVxdWVzdFN0YXRlbWVudFBkZkRvd25sb2FkVXJsEjcuaGFtaS5mcm9udGVuZC52MS5SZXF1ZXN0U3RhdGVtZW50UGRmRG93bmxvYWRVcmxSZXF1ZXN0GjguaGFtaS5mcm9udGVuZC52MS5SZXF1ZXN0U3RhdGVtZW50UGRmRG93bmxvYWRVcmxSZXNwb25zZWIGcHJvdG8z");

/**
 * 会員向け請求・支払い情報
 *
 * @generated from message hami.frontend.v1.MemberClaimAndPay
 */
export type MemberClaimAndPay = Message<"hami.frontend.v1.MemberClaimAndPay"> & {
  /**
   * 請求と支払いID
   *
   * @generated from field: int32 member_claim_and_pay_id = 1;
   */
  memberClaimAndPayId: number;

  /**
   * 会員ID
   *
   * @generated from field: int32 member_id = 2;
   */
  memberId: number;

  /**
   * 発生日（年）
   *
   * @generated from field: int32 occurred_year = 3;
   */
  occurredYear: number;

  /**
   * 発生日（月）
   *
   * @generated from field: int32 occurred_month = 4;
   */
  occurredMonth: number;

  /**
   * 発生日（日）
   *
   * @generated from field: int32 occurred_day = 5;
   */
  occurredDay: number;

  /**
   * 請求金額
   *
   * @generated from field: int32 claim_amount = 6;
   */
  claimAmount: number;

  /**
   * 支払い金額
   *
   * @generated from field: int32 pay_amount = 7;
   */
  payAmount: number;

  /**
   * 明細書ファイルキー
   *
   * @generated from field: optional string statement_file_key = 8;
   */
  statementFileKey?: string;

  /**
   * 明細書発行日時（ISO 8601形式）
   *
   * @generated from field: optional string statement_issued_at = 9;
   */
  statementIssuedAt?: string;
};

/**
 * Describes the message hami.frontend.v1.MemberClaimAndPay.
 * Use `create(MemberClaimAndPaySchema)` to create a new message.
 */
export const MemberClaimAndPaySchema: GenMessage<MemberClaimAndPay> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 0);

/**
 * 請求・支払い一覧取得リクエスト
 *
 * リクエストパラメータなし（認証された会員の全データを取得）
 *
 * @generated from message hami.frontend.v1.ListMemberClaimAndPaysRequest
 */
export type ListMemberClaimAndPaysRequest = Message<"hami.frontend.v1.ListMemberClaimAndPaysRequest"> & {
};

/**
 * Describes the message hami.frontend.v1.ListMemberClaimAndPaysRequest.
 * Use `create(ListMemberClaimAndPaysRequestSchema)` to create a new message.
 */
export const ListMemberClaimAndPaysRequestSchema: GenMessage<ListMemberClaimAndPaysRequest> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 1);

/**
 * 請求・支払い一覧取得レスポンス
 *
 * @generated from message hami.frontend.v1.ListMemberClaimAndPaysResponse
 */
export type ListMemberClaimAndPaysResponse = Message<"hami.frontend.v1.ListMemberClaimAndPaysResponse"> & {
  /**
   * 請求・支払い一覧
   *
   * @generated from field: repeated hami.frontend.v1.MemberClaimAndPay member_claim_and_pays = 1;
   */
  memberClaimAndPays: MemberClaimAndPay[];
};

/**
 * Describes the message hami.frontend.v1.ListMemberClaimAndPaysResponse.
 * Use `create(ListMemberClaimAndPaysResponseSchema)` to create a new message.
 */
export const ListMemberClaimAndPaysResponseSchema: GenMessage<ListMemberClaimAndPaysResponse> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 2);

/**
 * 明細書PDFダウンロードURL取得リクエスト
 *
 * @generated from message hami.frontend.v1.RequestStatementPdfDownloadUrlRequest
 */
export type RequestStatementPdfDownloadUrlRequest = Message<"hami.frontend.v1.RequestStatementPdfDownloadUrlRequest"> & {
  /**
   * 明細書ファイルキー
   *
   * @generated from field: string statement_file_key = 1;
   */
  statementFileKey: string;
};

/**
 * Describes the message hami.frontend.v1.RequestStatementPdfDownloadUrlRequest.
 * Use `create(RequestStatementPdfDownloadUrlRequestSchema)` to create a new message.
 */
export const RequestStatementPdfDownloadUrlRequestSchema: GenMessage<RequestStatementPdfDownloadUrlRequest> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 3);

/**
 * 明細書PDFダウンロードURL取得レスポンス
 *
 * @generated from message hami.frontend.v1.RequestStatementPdfDownloadUrlResponse
 */
export type RequestStatementPdfDownloadUrlResponse = Message<"hami.frontend.v1.RequestStatementPdfDownloadUrlResponse"> & {
  /**
   * ダウンロードURL
   *
   * @generated from field: string download_url = 1;
   */
  downloadUrl: string;
};

/**
 * Describes the message hami.frontend.v1.RequestStatementPdfDownloadUrlResponse.
 * Use `create(RequestStatementPdfDownloadUrlResponseSchema)` to create a new message.
 */
export const RequestStatementPdfDownloadUrlResponseSchema: GenMessage<RequestStatementPdfDownloadUrlResponse> = /*@__PURE__*/
  messageDesc(file_member_claim_and_pay_service, 4);

/**
 * 会員向け請求・支払いサービス
 *
 * @generated from service hami.frontend.v1.MemberClaimAndPayService
 */
export const MemberClaimAndPayService: GenService<{
  /**
   * 請求・支払い一覧取得
   *
   * @generated from rpc hami.frontend.v1.MemberClaimAndPayService.ListMemberClaimAndPays
   */
  listMemberClaimAndPays: {
    methodKind: "unary";
    input: typeof ListMemberClaimAndPaysRequestSchema;
    output: typeof ListMemberClaimAndPaysResponseSchema;
  },
  /**
   * 明細書PDFダウンロードURL取得
   *
   * @generated from rpc hami.frontend.v1.MemberClaimAndPayService.RequestStatementPdfDownloadUrl
   */
  requestStatementPdfDownloadUrl: {
    methodKind: "unary";
    input: typeof RequestStatementPdfDownloadUrlRequestSchema;
    output: typeof RequestStatementPdfDownloadUrlResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_member_claim_and_pay_service, 0);

