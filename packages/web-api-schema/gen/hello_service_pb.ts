// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file hello_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hello_service.proto.
 */
export const file_hello_service: GenFile = /*@__PURE__*/
  fileDesc("ChNoZWxsb19zZXJ2aWNlLnByb3RvEhBoYW1pLmZyb250ZW5kLnYxIh8KD1NheUhlbGxvUmVxdWVzdBIMCgRuYW1lGAEgASgJIiMKEFNheUhlbGxvUmVzcG9uc2USDwoHbWVzc2FnZRgBIAEoCTJjCgxIZWxsb1NlcnZpY2USUwoIU2F5SGVsbG8SIS5oYW1pLmZyb250ZW5kLnYxLlNheUhlbGxvUmVxdWVzdBoiLmhhbWkuZnJvbnRlbmQudjEuU2F5SGVsbG9SZXNwb25zZSIAYgZwcm90bzM");

/**
 * @generated from message hami.frontend.v1.SayHelloRequest
 */
export type SayHelloRequest = Message<"hami.frontend.v1.SayHelloRequest"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;
};

/**
 * Describes the message hami.frontend.v1.SayHelloRequest.
 * Use `create(SayHelloRequestSchema)` to create a new message.
 */
export const SayHelloRequestSchema: GenMessage<SayHelloRequest> = /*@__PURE__*/
  messageDesc(file_hello_service, 0);

/**
 * @generated from message hami.frontend.v1.SayHelloResponse
 */
export type SayHelloResponse = Message<"hami.frontend.v1.SayHelloResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;
};

/**
 * Describes the message hami.frontend.v1.SayHelloResponse.
 * Use `create(SayHelloResponseSchema)` to create a new message.
 */
export const SayHelloResponseSchema: GenMessage<SayHelloResponse> = /*@__PURE__*/
  messageDesc(file_hello_service, 1);

/**
 * @generated from service hami.frontend.v1.HelloService
 */
export const HelloService: GenService<{
  /**
   * @generated from rpc hami.frontend.v1.HelloService.SayHello
   */
  sayHello: {
    methodKind: "unary";
    input: typeof SayHelloRequestSchema;
    output: typeof SayHelloResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hello_service, 0);

