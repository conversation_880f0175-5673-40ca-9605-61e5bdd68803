// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file investment_application_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file investment_application_service.proto.
 */
export const file_investment_application_service: GenFile = /*@__PURE__*/
  fileDesc("CiRpbnZlc3RtZW50X2FwcGxpY2F0aW9uX3NlcnZpY2UucHJvdG8SEGhhbWkuZnJvbnRlbmQudjEijQIKGUludmVzdG1lbnRBcHBsaWNhdGlvbkl0ZW0SHQoQcmVjcnVpdG1lbnRfeWVhchgBIAEoBUgAiAEBEhsKDnJlY3J1aXRtZW50X25vGAIgASgFSAGIAQESGAoQcmVxdWVzdGVkX251bWJlchgDIAEoBRIhChlyZWplY3RfcGFydGlhbF9hbGxvY2F0aW9uGAQgASgIEhsKE2luc3RhbGxtZW50X3BheW1lbnQYBSABKAgSHQoQYW5udWFsX2J1bmRsZV9pZBgGIAEoBUgCiAEBQhMKEV9yZWNydWl0bWVudF95ZWFyQhEKD19yZWNydWl0bWVudF9ub0ITChFfYW5udWFsX2J1bmRsZV9pZCJyCiJDcmVhdGVJbnZlc3RtZW50QXBwbGljYXRpb25SZXF1ZXN0EjoKBWl0ZW1zGAEgAygLMisuaGFtaS5mcm9udGVuZC52MS5JbnZlc3RtZW50QXBwbGljYXRpb25JdGVtEhAKCGlzX3dob2xlGAIgASgIIiUKI0NyZWF0ZUludmVzdG1lbnRBcHBsaWNhdGlvblJlc3BvbnNlMqsBChxJbnZlc3RtZW50QXBwbGljYXRpb25TZXJ2aWNlEooBChtDcmVhdGVJbnZlc3RtZW50QXBwbGljYXRpb24SNC5oYW1pLmZyb250ZW5kLnYxLkNyZWF0ZUludmVzdG1lbnRBcHBsaWNhdGlvblJlcXVlc3QaNS5oYW1pLmZyb250ZW5kLnYxLkNyZWF0ZUludmVzdG1lbnRBcHBsaWNhdGlvblJlc3BvbnNlYgZwcm90bzM");

/**
 * 出資申込アイテム
 *
 * @generated from message hami.frontend.v1.InvestmentApplicationItem
 */
export type InvestmentApplicationItem = Message<"hami.frontend.v1.InvestmentApplicationItem"> & {
  /**
   * @generated from field: optional int32 recruitment_year = 1;
   */
  recruitmentYear?: number;

  /**
   * @generated from field: optional int32 recruitment_no = 2;
   */
  recruitmentNo?: number;

  /**
   * 希望口数
   *
   * @generated from field: int32 requested_number = 3;
   */
  requestedNumber: number;

  /**
   * 希望口数に満たない場合に出資を希望しないフラグ
   *
   * @generated from field: bool reject_partial_allocation = 4;
   */
  rejectPartialAllocation: boolean;

  /**
   * 分割払いフラグ
   *
   * @generated from field: bool installment_payment = 5;
   */
  installmentPayment: boolean;

  /**
   * 年度バンドルID
   *
   * @generated from field: optional int32 annual_bundle_id = 6;
   */
  annualBundleId?: number;
};

/**
 * Describes the message hami.frontend.v1.InvestmentApplicationItem.
 * Use `create(InvestmentApplicationItemSchema)` to create a new message.
 */
export const InvestmentApplicationItemSchema: GenMessage<InvestmentApplicationItem> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 0);

/**
 * 出資申込作成リクエスト
 *
 * @generated from message hami.frontend.v1.CreateInvestmentApplicationRequest
 */
export type CreateInvestmentApplicationRequest = Message<"hami.frontend.v1.CreateInvestmentApplicationRequest"> & {
  /**
   * 出資申込アイテムのリスト
   *
   * @generated from field: repeated hami.frontend.v1.InvestmentApplicationItem items = 1;
   */
  items: InvestmentApplicationItem[];

  /**
   * @generated from field: bool is_whole = 2;
   */
  isWhole: boolean;
};

/**
 * Describes the message hami.frontend.v1.CreateInvestmentApplicationRequest.
 * Use `create(CreateInvestmentApplicationRequestSchema)` to create a new message.
 */
export const CreateInvestmentApplicationRequestSchema: GenMessage<CreateInvestmentApplicationRequest> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 1);

/**
 * 出資申込作成レスポンス
 *
 * @generated from message hami.frontend.v1.CreateInvestmentApplicationResponse
 */
export type CreateInvestmentApplicationResponse = Message<"hami.frontend.v1.CreateInvestmentApplicationResponse"> & {
};

/**
 * Describes the message hami.frontend.v1.CreateInvestmentApplicationResponse.
 * Use `create(CreateInvestmentApplicationResponseSchema)` to create a new message.
 */
export const CreateInvestmentApplicationResponseSchema: GenMessage<CreateInvestmentApplicationResponse> = /*@__PURE__*/
  messageDesc(file_investment_application_service, 2);

/**
 * 出資申込サービス
 *
 * @generated from service hami.frontend.v1.InvestmentApplicationService
 */
export const InvestmentApplicationService: GenService<{
  /**
   * 出資申込作成
   *
   * @generated from rpc hami.frontend.v1.InvestmentApplicationService.CreateInvestmentApplication
   */
  createInvestmentApplication: {
    methodKind: "unary";
    input: typeof CreateInvestmentApplicationRequestSchema;
    output: typeof CreateInvestmentApplicationResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_investment_application_service, 0);

