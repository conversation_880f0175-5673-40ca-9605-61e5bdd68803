// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file contact_inquiry_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file contact_inquiry_service.proto.
 */
export const file_contact_inquiry_service: GenFile = /*@__PURE__*/
  fileDesc("Ch1jb250YWN0X2lucXVpcnlfc2VydmljZS5wcm90bxIQaGFtaS5mcm9udGVuZC52MSLxAQobQ3JlYXRlQ29udGFjdElucXVpcnlSZXF1ZXN0EhUKDWlucXVpcmVyX25hbWUYASABKAkSFgoOaW5xdWlyZXJfZW1haWwYAiABKAkSGwoOaW5xdWlyZXJfcGhvbmUYAyABKAlIAIgBARIzCghjYXRlZ29yeRgEIAEoDjIhLmhhbWkuZnJvbnRlbmQudjEuSW5xdWlyeUNhdGVnb3J5Eg8KB3N1YmplY3QYBSABKAkSDwoHbWVzc2FnZRgGIAEoCRIcChRhdHRhY2htZW50X2ZpbGVfa2V5cxgHIAMoCUIRCg9faW5xdWlyZXJfcGhvbmUiQwocQ3JlYXRlQ29udGFjdElucXVpcnlSZXNwb25zZRIPCgdtZXNzYWdlGAEgASgJEhIKCmlucXVpcnlfaWQYAiABKAkiVQoXVXBsb2FkQXR0YWNobWVudFJlcXVlc3QSEQoJZmlsZV9uYW1lGAEgASgJEhQKDGNvbnRlbnRfdHlwZRgCIAEoCRIRCglmaWxlX3NpemUYAyABKAMiVAoYVXBsb2FkQXR0YWNobWVudFJlc3BvbnNlEhIKCnVwbG9hZF91cmwYASABKAkSEAoIZmlsZV9rZXkYAiABKAkSEgoKZXhwaXJlc19pbhgDIAEoBSqrAgoPSW5xdWlyeUNhdGVnb3J5EiAKHElOUVVJUllfQ0FURUdPUllfVU5TUEVDSUZJRUQQABIcChhJTlFVSVJZX0NBVEVHT1JZX0dFTkVSQUwQARIfChtJTlFVSVJZX0NBVEVHT1JZX0lOVkVTVE1FTlQQAhIcChhJTlFVSVJZX0NBVEVHT1JZX0FDQ09VTlQQAxIeChpJTlFVSVJZX0NBVEVHT1JZX1RFQ0hOSUNBTBAEEhwKGElOUVVJUllfQ0FURUdPUllfQklMTElORxAFEh8KG0lOUVVJUllfQ0FURUdPUllfSE9SU0VfSU5GTxAGEh4KGklOUVVJUllfQ0FURUdPUllfQ09NUExBSU5UEAcSGgoWSU5RVUlSWV9DQVRFR09SWV9PVEhFUhAIMvkBChVDb250YWN0SW5xdWlyeVNlcnZpY2USdQoUQ3JlYXRlQ29udGFjdElucXVpcnkSLS5oYW1pLmZyb250ZW5kLnYxLkNyZWF0ZUNvbnRhY3RJbnF1aXJ5UmVxdWVzdBouLmhhbWkuZnJvbnRlbmQudjEuQ3JlYXRlQ29udGFjdElucXVpcnlSZXNwb25zZRJpChBVcGxvYWRBdHRhY2htZW50EikuaGFtaS5mcm9udGVuZC52MS5VcGxvYWRBdHRhY2htZW50UmVxdWVzdBoqLmhhbWkuZnJvbnRlbmQudjEuVXBsb2FkQXR0YWNobWVudFJlc3BvbnNlYgZwcm90bzM", [file_google_protobuf_timestamp]);

/**
 * お問い合わせ作成リクエスト
 *
 * @generated from message hami.frontend.v1.CreateContactInquiryRequest
 */
export type CreateContactInquiryRequest = Message<"hami.frontend.v1.CreateContactInquiryRequest"> & {
  /**
   * 問い合わせ者名
   *
   * @generated from field: string inquirer_name = 1;
   */
  inquirerName: string;

  /**
   * 問い合わせ者メールアドレス
   *
   * @generated from field: string inquirer_email = 2;
   */
  inquirerEmail: string;

  /**
   * 問い合わせ者電話番号
   *
   * @generated from field: optional string inquirer_phone = 3;
   */
  inquirerPhone?: string;

  /**
   * 問い合わせカテゴリ
   *
   * @generated from field: hami.frontend.v1.InquiryCategory category = 4;
   */
  category: InquiryCategory;

  /**
   * 件名
   *
   * @generated from field: string subject = 5;
   */
  subject: string;

  /**
   * 問い合わせ内容
   *
   * @generated from field: string message = 6;
   */
  message: string;

  /**
   * アップロード済みファイルkey
   *
   * @generated from field: repeated string attachment_file_keys = 7;
   */
  attachmentFileKeys: string[];
};

/**
 * Describes the message hami.frontend.v1.CreateContactInquiryRequest.
 * Use `create(CreateContactInquiryRequestSchema)` to create a new message.
 */
export const CreateContactInquiryRequestSchema: GenMessage<CreateContactInquiryRequest> = /*@__PURE__*/
  messageDesc(file_contact_inquiry_service, 0);

/**
 * お問い合わせ作成レスポンス
 *
 * @generated from message hami.frontend.v1.CreateContactInquiryResponse
 */
export type CreateContactInquiryResponse = Message<"hami.frontend.v1.CreateContactInquiryResponse"> & {
  /**
   * 成功メッセージ
   *
   * @generated from field: string message = 1;
   */
  message: string;

  /**
   * 参照用ID（公開ID）
   *
   * @generated from field: string inquiry_id = 2;
   */
  inquiryId: string;
};

/**
 * Describes the message hami.frontend.v1.CreateContactInquiryResponse.
 * Use `create(CreateContactInquiryResponseSchema)` to create a new message.
 */
export const CreateContactInquiryResponseSchema: GenMessage<CreateContactInquiryResponse> = /*@__PURE__*/
  messageDesc(file_contact_inquiry_service, 1);

/**
 * 添付ファイルアップロードリクエスト
 *
 * @generated from message hami.frontend.v1.UploadAttachmentRequest
 */
export type UploadAttachmentRequest = Message<"hami.frontend.v1.UploadAttachmentRequest"> & {
  /**
   * ファイル名
   *
   * @generated from field: string file_name = 1;
   */
  fileName: string;

  /**
   * Content-Type
   *
   * @generated from field: string content_type = 2;
   */
  contentType: string;

  /**
   * ファイルサイズ（bytes）
   *
   * @generated from field: int64 file_size = 3;
   */
  fileSize: bigint;
};

/**
 * Describes the message hami.frontend.v1.UploadAttachmentRequest.
 * Use `create(UploadAttachmentRequestSchema)` to create a new message.
 */
export const UploadAttachmentRequestSchema: GenMessage<UploadAttachmentRequest> = /*@__PURE__*/
  messageDesc(file_contact_inquiry_service, 2);

/**
 * 添付ファイルアップロードレスポンス
 *
 * @generated from message hami.frontend.v1.UploadAttachmentResponse
 */
export type UploadAttachmentResponse = Message<"hami.frontend.v1.UploadAttachmentResponse"> & {
  /**
   * アップロード用署名付きURL
   *
   * @generated from field: string upload_url = 1;
   */
  uploadUrl: string;

  /**
   * ファイルキー（一時保存用）
   *
   * @generated from field: string file_key = 2;
   */
  fileKey: string;

  /**
   * URL有効期限（秒）
   *
   * @generated from field: int32 expires_in = 3;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.frontend.v1.UploadAttachmentResponse.
 * Use `create(UploadAttachmentResponseSchema)` to create a new message.
 */
export const UploadAttachmentResponseSchema: GenMessage<UploadAttachmentResponse> = /*@__PURE__*/
  messageDesc(file_contact_inquiry_service, 3);

/**
 * お問い合わせカテゴリ
 *
 * @generated from enum hami.frontend.v1.InquiryCategory
 */
export enum InquiryCategory {
  /**
   * @generated from enum value: INQUIRY_CATEGORY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 一般的な問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_GENERAL = 1;
   */
  GENERAL = 1,

  /**
   * 出資に関する問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_INVESTMENT = 2;
   */
  INVESTMENT = 2,

  /**
   * アカウント・ログインに関する問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_ACCOUNT = 3;
   */
  ACCOUNT = 3,

  /**
   * 技術的な問題
   *
   * @generated from enum value: INQUIRY_CATEGORY_TECHNICAL = 4;
   */
  TECHNICAL = 4,

  /**
   * 請求・支払いに関する問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_BILLING = 5;
   */
  BILLING = 5,

  /**
   * 馬の情報に関する問い合わせ
   *
   * @generated from enum value: INQUIRY_CATEGORY_HORSE_INFO = 6;
   */
  HORSE_INFO = 6,

  /**
   * 苦情・要望
   *
   * @generated from enum value: INQUIRY_CATEGORY_COMPLAINT = 7;
   */
  COMPLAINT = 7,

  /**
   * その他
   *
   * @generated from enum value: INQUIRY_CATEGORY_OTHER = 8;
   */
  OTHER = 8,
}

/**
 * Describes the enum hami.frontend.v1.InquiryCategory.
 */
export const InquiryCategorySchema: GenEnum<InquiryCategory> = /*@__PURE__*/
  enumDesc(file_contact_inquiry_service, 0);

/**
 * お問い合わせサービス（フロントエンド用）
 *
 * @generated from service hami.frontend.v1.ContactInquiryService
 */
export const ContactInquiryService: GenService<{
  /**
   * お問い合わせ送信
   *
   * @generated from rpc hami.frontend.v1.ContactInquiryService.CreateContactInquiry
   */
  createContactInquiry: {
    methodKind: "unary";
    input: typeof CreateContactInquiryRequestSchema;
    output: typeof CreateContactInquiryResponseSchema;
  },
  /**
   * 添付ファイルアップロード
   *
   * @generated from rpc hami.frontend.v1.ContactInquiryService.UploadAttachment
   */
  uploadAttachment: {
    methodKind: "unary";
    input: typeof UploadAttachmentRequestSchema;
    output: typeof UploadAttachmentResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_contact_inquiry_service, 0);

