// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file recruitment_horse_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Gender } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file recruitment_horse_service.proto.
 */
export const file_recruitment_horse_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums]);

/**
 * 募集馬一覧取得リクエスト
 *
 * @generated from message hami.frontend.v1.ListRecruitmentHorsesRequest
 */
export type ListRecruitmentHorsesRequest = Message<"hami.frontend.v1.ListRecruitmentHorsesRequest"> & {
  /**
   * 対象年度
   *
   * @generated from field: int32 target_year = 1;
   */
  targetYear: number;

  /**
   * 注目の募集馬に限るかどうか
   *
   * @generated from field: bool featured_horse_only = 2;
   */
  featuredHorseOnly: boolean;

  /**
   * ソート条件
   *
   * @generated from field: optional hami.frontend.v1.SortField sort_field = 3;
   */
  sortField?: SortField;

  /**
   * @generated from field: optional hami.frontend.v1.SortOrder sort_order = 4;
   */
  sortOrder?: SortOrder;

  /**
   * ページネーション
   *
   * @generated from field: int32 page = 5;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 6;
   */
  pageSize: number;
};

/**
 * Describes the message hami.frontend.v1.ListRecruitmentHorsesRequest.
 * Use `create(ListRecruitmentHorsesRequestSchema)` to create a new message.
 */
export const ListRecruitmentHorsesRequestSchema: GenMessage<ListRecruitmentHorsesRequest> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 0);

/**
 * 募集馬一覧取得レスポンス
 *
 * @generated from message hami.frontend.v1.ListRecruitmentHorsesResponse
 */
export type ListRecruitmentHorsesResponse = Message<"hami.frontend.v1.ListRecruitmentHorsesResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.RecruitmentHorseItem horses = 1;
   */
  horses: RecruitmentHorseItem[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 current_page = 3;
   */
  currentPage: number;

  /**
   * @generated from field: int32 total_pages = 4;
   */
  totalPages: number;

  /**
   * @generated from field: bool has_next_page = 5;
   */
  hasNextPage: boolean;

  /**
   * @generated from field: bool has_previous_page = 6;
   */
  hasPreviousPage: boolean;
};

/**
 * Describes the message hami.frontend.v1.ListRecruitmentHorsesResponse.
 * Use `create(ListRecruitmentHorsesResponseSchema)` to create a new message.
 */
export const ListRecruitmentHorsesResponseSchema: GenMessage<ListRecruitmentHorsesResponse> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 1);

/**
 * 募集馬アイテム（一覧表示・詳細表示共用）
 *
 * @generated from message hami.frontend.v1.RecruitmentHorseItem
 */
export type RecruitmentHorseItem = Message<"hami.frontend.v1.RecruitmentHorseItem"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: int32 recruitment_year = 2;
   */
  recruitmentYear: number;

  /**
   * 募集番号
   *
   * @generated from field: int32 recruitment_no = 3;
   */
  recruitmentNo: number;

  /**
   * 募集名
   *
   * @generated from field: string recruitment_name = 4;
   */
  recruitmentName: string;

  /**
   * 馬名
   *
   * @generated from field: string horse_name = 5;
   */
  horseName: string;

  /**
   * 英語名
   *
   * @generated from field: string horse_name_en = 6;
   */
  horseNameEn: string;

  /**
   * 性別
   *
   * @generated from field: hami.frontend.v1.Gender gender = 7;
   */
  gender: Gender;

  /**
   * 毛色
   *
   * @generated from field: string coat_color = 8;
   */
  coatColor: string;

  /**
   * @generated from field: int32 birth_year = 9;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 10;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 11;
   */
  birthDay: number;

  /**
   * 血統情報
   *
   * 父
   *
   * @generated from field: string sire_name = 12;
   */
  sireName: string;

  /**
   * 母
   *
   * @generated from field: string dam_name = 13;
   */
  damName: string;

  /**
   * 母父
   *
   * @generated from field: string broodmare_sire_name = 14;
   */
  broodmareSireName: string;

  /**
   * 生産・育成情報
   *
   * 生産地
   *
   * @generated from field: string birth_place = 15;
   */
  birthPlace: string;

  /**
   * 生産牧場
   *
   * @generated from field: string breeder_name = 16;
   */
  breederName: string;

  /**
   * 育成牧場
   *
   * @generated from field: string training_farm = 17;
   */
  trainingFarm: string;

  /**
   * 厩舎
   *
   * @generated from field: string stable_name = 18;
   */
  stableName: string;

  /**
   * 所属（栗東/美浦/地方）
   *
   * @generated from field: string affiliation = 19;
   */
  affiliation: string;

  /**
   * 募集情報
   *
   * 募集口数
   *
   * @generated from field: int32 shares_total = 20;
   */
  sharesTotal: number;

  /**
   * 募集総額
   *
   * @generated from field: int32 amount_total = 21;
   */
  amountTotal: number;

  /**
   * 申込最大口数
   *
   * @generated from field: int32 max_shares_per_order = 22;
   */
  maxSharesPerOrder: number;

  /**
   * 特別募集フラグ
   *
   * @generated from field: bool special_flag = 23;
   */
  specialFlag: boolean;

  /**
   * 募集ステータス
   *
   * @generated from field: hami.frontend.v1.RecruitmentStatus recruitment_status = 24;
   */
  recruitmentStatus: RecruitmentStatus;

  /**
   * @generated from field: bool featured = 25;
   */
  featured: boolean;

  /**
   * 一括出資の対象外フラグ（一覧・詳細で利用）
   *
   * @generated from field: bool excluded_from_bulk_investment = 29;
   */
  excludedFromBulkInvestment: boolean;

  /**
   * 利益相反（競合）フラグ
   *
   * @generated from field: bool conflict_of_interest = 31;
   */
  conflictOfInterest: boolean;

  /**
   * 画像情報
   *
   * プロフィール画像URL
   *
   * @generated from field: repeated string image_urls = 26;
   */
  imageUrls: string[];

  /**
   * 顔写真URL
   *
   * @generated from field: optional string face_image_url = 30;
   */
  faceImageUrl?: string;

  /**
   * 表示用情報
   *
   * ステータス表示ラベル
   *
   * @generated from field: string status_label = 27;
   */
  statusLabel: string;

  /**
   * 生年月日（フォーマット済み）
   *
   * @generated from field: string birth_date_formatted = 28;
   */
  birthDateFormatted: string;

  /**
   * クロス情報
   *
   * クロス詳細（例: "Northern Dancer 4x4"）
   *
   * @generated from field: string cross_detail = 32;
   */
  crossDetail: string;

  /**
   * PDFリンク（詳細画面向け）
   *
   * 血統表PDF
   *
   * @generated from field: optional string pedigree_url = 33;
   */
  pedigreeUrl?: string;

  /**
   * ブラックタイプPDF
   *
   * @generated from field: optional string black_type_url = 34;
   */
  blackTypeUrl?: string;

  /**
   * 動画情報（一覧表示用の有無・最新動画ID/開始秒）
   *
   * 動画が1件以上存在するか
   *
   * @generated from field: optional bool has_video = 35;
   */
  hasVideo?: boolean;

  /**
   * 最新のYouTube動画ID
   *
   * @generated from field: optional string latest_youtube_id = 36;
   */
  latestYoutubeId?: string;

  /**
   * 最新動画の開始秒
   *
   * @generated from field: optional int32 latest_start_at_seconds = 37;
   */
  latestStartAtSeconds?: number;
};

/**
 * Describes the message hami.frontend.v1.RecruitmentHorseItem.
 * Use `create(RecruitmentHorseItemSchema)` to create a new message.
 */
export const RecruitmentHorseItemSchema: GenMessage<RecruitmentHorseItem> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 2);

/**
 * 募集馬詳細取得リクエスト
 *
 * @generated from message hami.frontend.v1.GetRecruitmentHorseRequest
 */
export type GetRecruitmentHorseRequest = Message<"hami.frontend.v1.GetRecruitmentHorseRequest"> & {
  /**
   * @generated from field: int32 recruitment_year = 1;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 2;
   */
  recruitmentNo: number;
};

/**
 * Describes the message hami.frontend.v1.GetRecruitmentHorseRequest.
 * Use `create(GetRecruitmentHorseRequestSchema)` to create a new message.
 */
export const GetRecruitmentHorseRequestSchema: GenMessage<GetRecruitmentHorseRequest> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 3);

/**
 * 募集馬詳細取得レスポンス
 *
 * @generated from message hami.frontend.v1.GetRecruitmentHorseResponse
 */
export type GetRecruitmentHorseResponse = Message<"hami.frontend.v1.GetRecruitmentHorseResponse"> & {
  /**
   * @generated from field: hami.frontend.v1.RecruitmentHorseDetail horse = 1;
   */
  horse?: RecruitmentHorseDetail;
};

/**
 * Describes the message hami.frontend.v1.GetRecruitmentHorseResponse.
 * Use `create(GetRecruitmentHorseResponseSchema)` to create a new message.
 */
export const GetRecruitmentHorseResponseSchema: GenMessage<GetRecruitmentHorseResponse> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 4);

/**
 * 募集馬詳細情報
 *
 * @generated from message hami.frontend.v1.RecruitmentHorseDetail
 */
export type RecruitmentHorseDetail = Message<"hami.frontend.v1.RecruitmentHorseDetail"> & {
  /**
   * RecruitmentHorseItemの全フィールド + 追加詳細情報
   *
   * @generated from field: hami.frontend.v1.RecruitmentHorseItem basic_info = 1;
   */
  basicInfo?: RecruitmentHorseItem;

  /**
   * 詳細情報
   *
   * 名前の由来
   *
   * @generated from field: string name_origin = 2;
   */
  nameOrigin: string;

  /**
   * 募集コメント
   *
   * @generated from field: string comment = 3;
   */
  comment: string;

  /**
   * 備考
   *
   * @generated from field: string note_content = 4;
   */
  noteContent: string;

  /**
   * 一括出資の対象外フラグ
   *
   * @generated from field: bool excluded_from_bulk_investment = 5;
   */
  excludedFromBulkInvestment: boolean;
};

/**
 * Describes the message hami.frontend.v1.RecruitmentHorseDetail.
 * Use `create(RecruitmentHorseDetailSchema)` to create a new message.
 */
export const RecruitmentHorseDetailSchema: GenMessage<RecruitmentHorseDetail> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 5);

/**
 * 募集馬の父馬名一覧 取得リクエスト
 *
 * @generated from message hami.frontend.v1.ListRecruitmentSireNamesRequest
 */
export type ListRecruitmentSireNamesRequest = Message<"hami.frontend.v1.ListRecruitmentSireNamesRequest"> & {
  /**
   * 対象年度
   *
   * @generated from field: int32 target_year = 1;
   */
  targetYear: number;
};

/**
 * Describes the message hami.frontend.v1.ListRecruitmentSireNamesRequest.
 * Use `create(ListRecruitmentSireNamesRequestSchema)` to create a new message.
 */
export const ListRecruitmentSireNamesRequestSchema: GenMessage<ListRecruitmentSireNamesRequest> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 6);

/**
 * 募集馬の父馬名一覧 取得レスポンス
 *
 * @generated from message hami.frontend.v1.ListRecruitmentSireNamesResponse
 */
export type ListRecruitmentSireNamesResponse = Message<"hami.frontend.v1.ListRecruitmentSireNamesResponse"> & {
  /**
   * 父馬名（重複排除・アルファベット順などは実装側に委ねる）
   *
   * @generated from field: repeated string sire_names = 1;
   */
  sireNames: string[];
};

/**
 * Describes the message hami.frontend.v1.ListRecruitmentSireNamesResponse.
 * Use `create(ListRecruitmentSireNamesResponseSchema)` to create a new message.
 */
export const ListRecruitmentSireNamesResponseSchema: GenMessage<ListRecruitmentSireNamesResponse> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 7);

/**
 * 動画情報（募集馬ページ向け）
 *
 * @generated from message hami.frontend.v1.RecruitmentHorseVideo
 */
export type RecruitmentHorseVideo = Message<"hami.frontend.v1.RecruitmentHorseVideo"> & {
  /**
   * @generated from field: int32 video_id = 1;
   */
  videoId: number;

  /**
   * @generated from field: int32 video_year = 2;
   */
  videoYear: number;

  /**
   * @generated from field: int32 video_month = 3;
   */
  videoMonth: number;

  /**
   * @generated from field: int32 video_day = 4;
   */
  videoDay: number;

  /**
   * @generated from field: string title = 5;
   */
  title: string;

  /**
   * @generated from field: optional string description = 6;
   */
  description?: string;

  /**
   * @generated from field: string youtube_video_id = 7;
   */
  youtubeVideoId: string;

  /**
   * @generated from field: optional int32 start_at_seconds = 8;
   */
  startAtSeconds?: number;

  /**
   * @generated from field: optional string thumbnail_url = 9;
   */
  thumbnailUrl?: string;
};

/**
 * Describes the message hami.frontend.v1.RecruitmentHorseVideo.
 * Use `create(RecruitmentHorseVideoSchema)` to create a new message.
 */
export const RecruitmentHorseVideoSchema: GenMessage<RecruitmentHorseVideo> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 8);

/**
 * 動画一覧取得リクエスト
 *
 * @generated from message hami.frontend.v1.ListRecruitmentHorseVideosRequest
 */
export type ListRecruitmentHorseVideosRequest = Message<"hami.frontend.v1.ListRecruitmentHorseVideosRequest"> & {
  /**
   * @generated from field: int32 recruitment_year = 1;
   */
  recruitmentYear: number;

  /**
   * @generated from field: int32 recruitment_no = 2;
   */
  recruitmentNo: number;

  /**
   * 取得件数制限（デフォルト：全件）
   *
   * @generated from field: optional int32 limit = 3;
   */
  limit?: number;
};

/**
 * Describes the message hami.frontend.v1.ListRecruitmentHorseVideosRequest.
 * Use `create(ListRecruitmentHorseVideosRequestSchema)` to create a new message.
 */
export const ListRecruitmentHorseVideosRequestSchema: GenMessage<ListRecruitmentHorseVideosRequest> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 9);

/**
 * 動画一覧取得レスポンス
 *
 * @generated from message hami.frontend.v1.ListRecruitmentHorseVideosResponse
 */
export type ListRecruitmentHorseVideosResponse = Message<"hami.frontend.v1.ListRecruitmentHorseVideosResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.RecruitmentHorseVideo videos = 1;
   */
  videos: RecruitmentHorseVideo[];
};

/**
 * Describes the message hami.frontend.v1.ListRecruitmentHorseVideosResponse.
 * Use `create(ListRecruitmentHorseVideosResponseSchema)` to create a new message.
 */
export const ListRecruitmentHorseVideosResponseSchema: GenMessage<ListRecruitmentHorseVideosResponse> = /*@__PURE__*/
  messageDesc(file_recruitment_horse_service, 10);

/**
 * 募集ステータス
 *
 * @generated from enum hami.frontend.v1.RecruitmentStatus
 */
export enum RecruitmentStatus {
  /**
   * @generated from enum value: RECRUITMENT_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 募集前
   *
   * @generated from enum value: RECRUITMENT_STATUS_UPCOMING = 1;
   */
  UPCOMING = 1,

  /**
   * 募集中
   *
   * @generated from enum value: RECRUITMENT_STATUS_ACTIVE = 2;
   */
  ACTIVE = 2,

  /**
   * 満口
   *
   * @generated from enum value: RECRUITMENT_STATUS_FULL = 3;
   */
  FULL = 3,

  /**
   * 募集終了
   *
   * @generated from enum value: RECRUITMENT_STATUS_CLOSED = 4;
   */
  CLOSED = 4,
}

/**
 * Describes the enum hami.frontend.v1.RecruitmentStatus.
 */
export const RecruitmentStatusSchema: GenEnum<RecruitmentStatus> = /*@__PURE__*/
  enumDesc(file_recruitment_horse_service, 0);

/**
 * ソート項目
 *
 * @generated from enum hami.frontend.v1.SortField
 */
export enum SortField {
  /**
   * @generated from enum value: SORT_FIELD_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 募集番号
   *
   * @generated from enum value: SORT_FIELD_RECRUIT_NO = 1;
   */
  RECRUIT_NO = 1,

  /**
   * 馬名
   *
   * @generated from enum value: SORT_FIELD_HORSE_NAME = 2;
   */
  HORSE_NAME = 2,

  /**
   * 募集総額
   *
   * @generated from enum value: SORT_FIELD_AMOUNT_TOTAL = 3;
   */
  AMOUNT_TOTAL = 3,

  /**
   * 生年月日
   *
   * @generated from enum value: SORT_FIELD_BIRTH_DATE = 4;
   */
  BIRTH_DATE = 4,

  /**
   * 作成日時
   *
   * @generated from enum value: SORT_FIELD_CREATED_AT = 5;
   */
  CREATED_AT = 5,
}

/**
 * Describes the enum hami.frontend.v1.SortField.
 */
export const SortFieldSchema: GenEnum<SortField> = /*@__PURE__*/
  enumDesc(file_recruitment_horse_service, 1);

/**
 * ソート順
 *
 * @generated from enum hami.frontend.v1.SortOrder
 */
export enum SortOrder {
  /**
   * @generated from enum value: SORT_ORDER_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 昇順
   *
   * @generated from enum value: SORT_ORDER_ASC = 1;
   */
  ASC = 1,

  /**
   * 降順
   *
   * @generated from enum value: SORT_ORDER_DESC = 2;
   */
  DESC = 2,
}

/**
 * Describes the enum hami.frontend.v1.SortOrder.
 */
export const SortOrderSchema: GenEnum<SortOrder> = /*@__PURE__*/
  enumDesc(file_recruitment_horse_service, 2);

/**
 * 募集馬一覧サービス（フロントエンド向け）
 *
 * @generated from service hami.frontend.v1.RecruitmentHorseService
 */
export const RecruitmentHorseService: GenService<{
  /**
   * 募集馬一覧取得（ソート・ページネーション対応）
   *
   * @generated from rpc hami.frontend.v1.RecruitmentHorseService.ListRecruitmentHorses
   */
  listRecruitmentHorses: {
    methodKind: "unary";
    input: typeof ListRecruitmentHorsesRequestSchema;
    output: typeof ListRecruitmentHorsesResponseSchema;
  },
  /**
   * 募集馬詳細取得
   *
   * @generated from rpc hami.frontend.v1.RecruitmentHorseService.GetRecruitmentHorse
   */
  getRecruitmentHorse: {
    methodKind: "unary";
    input: typeof GetRecruitmentHorseRequestSchema;
    output: typeof GetRecruitmentHorseResponseSchema;
  },
  /**
   * 募集馬の父馬名一覧取得
   *
   * @generated from rpc hami.frontend.v1.RecruitmentHorseService.ListRecruitmentSireNames
   */
  listRecruitmentSireNames: {
    methodKind: "unary";
    input: typeof ListRecruitmentSireNamesRequestSchema;
    output: typeof ListRecruitmentSireNamesResponseSchema;
  },
  /**
   * 募集馬の動画一覧取得（YouTube埋め込み）
   *
   * @generated from rpc hami.frontend.v1.RecruitmentHorseService.ListRecruitmentHorseVideos
   */
  listRecruitmentHorseVideos: {
    methodKind: "unary";
    input: typeof ListRecruitmentHorseVideosRequestSchema;
    output: typeof ListRecruitmentHorseVideosResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_recruitment_horse_service, 0);

