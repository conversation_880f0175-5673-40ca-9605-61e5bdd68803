// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file member_information_change_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file member_information_change_service.proto.
 */
export const file_member_information_change_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * 変更申請作成リクエスト
 *
 * @generated from message hami.frontend.v1.CreateMemberInformationChangeApplicationRequest
 */
export type CreateMemberInformationChangeApplicationRequest = Message<"hami.frontend.v1.CreateMemberInformationChangeApplicationRequest"> & {
  /**
   * @generated from field: optional string postal_code = 1;
   */
  postalCode?: string;

  /**
   * @generated from field: optional string prefecture = 2;
   */
  prefecture?: string;

  /**
   * @generated from field: optional string address = 3;
   */
  address?: string;

  /**
   * @generated from field: optional string apartment = 4;
   */
  apartment?: string;

  /**
   * @generated from field: optional string phone_number = 5;
   */
  phoneNumber?: string;

  /**
   * @generated from field: google.protobuf.Timestamp requested_change_date = 7;
   */
  requestedChangeDate?: Timestamp;

  /**
   * @generated from field: optional string reason = 8;
   */
  reason?: string;
};

/**
 * Describes the message hami.frontend.v1.CreateMemberInformationChangeApplicationRequest.
 * Use `create(CreateMemberInformationChangeApplicationRequestSchema)` to create a new message.
 */
export const CreateMemberInformationChangeApplicationRequestSchema: GenMessage<CreateMemberInformationChangeApplicationRequest> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 0);

/**
 * 変更申請作成レスポンス
 *
 * @generated from message hami.frontend.v1.CreateMemberInformationChangeApplicationResponse
 */
export type CreateMemberInformationChangeApplicationResponse = Message<"hami.frontend.v1.CreateMemberInformationChangeApplicationResponse"> & {
  /**
   * @generated from field: hami.frontend.v1.MemberInformationChangeApplication application = 1;
   */
  application?: MemberInformationChangeApplication;
};

/**
 * Describes the message hami.frontend.v1.CreateMemberInformationChangeApplicationResponse.
 * Use `create(CreateMemberInformationChangeApplicationResponseSchema)` to create a new message.
 */
export const CreateMemberInformationChangeApplicationResponseSchema: GenMessage<CreateMemberInformationChangeApplicationResponse> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 1);

/**
 * 変更申請情報
 *
 * @generated from message hami.frontend.v1.MemberInformationChangeApplication
 */
export type MemberInformationChangeApplication = Message<"hami.frontend.v1.MemberInformationChangeApplication"> & {
  /**
   * @generated from field: int32 member_information_change_application_id = 1;
   */
  memberInformationChangeApplicationId: number;

  /**
   * @generated from field: int32 member_id = 2;
   */
  memberId: number;

  /**
   * @generated from field: optional string postal_code = 3;
   */
  postalCode?: string;

  /**
   * @generated from field: optional string prefecture = 4;
   */
  prefecture?: string;

  /**
   * @generated from field: optional string address = 5;
   */
  address?: string;

  /**
   * @generated from field: optional string apartment = 6;
   */
  apartment?: string;

  /**
   * @generated from field: optional string phone_number = 7;
   */
  phoneNumber?: string;

  /**
   * @generated from field: google.protobuf.Timestamp requested_change_date = 9;
   */
  requestedChangeDate?: Timestamp;

  /**
   * @generated from field: optional string reason = 10;
   */
  reason?: string;

  /**
   * @generated from field: hami.frontend.v1.MemberInformationChangeStatus status = 11;
   */
  status: MemberInformationChangeStatus;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 12;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 13;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hami.frontend.v1.MemberInformationChangeApplication.
 * Use `create(MemberInformationChangeApplicationSchema)` to create a new message.
 */
export const MemberInformationChangeApplicationSchema: GenMessage<MemberInformationChangeApplication> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 2);

/**
 * 会員情報取得リクエスト
 *
 * 認証されたユーザーの会員情報を取得するため、パラメータは不要
 *
 * @generated from message hami.frontend.v1.GetMemberInformationRequest
 */
export type GetMemberInformationRequest = Message<"hami.frontend.v1.GetMemberInformationRequest"> & {
};

/**
 * Describes the message hami.frontend.v1.GetMemberInformationRequest.
 * Use `create(GetMemberInformationRequestSchema)` to create a new message.
 */
export const GetMemberInformationRequestSchema: GenMessage<GetMemberInformationRequest> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 3);

/**
 * 会員情報取得レスポンス
 *
 * @generated from message hami.frontend.v1.GetMemberInformationResponse
 */
export type GetMemberInformationResponse = Message<"hami.frontend.v1.GetMemberInformationResponse"> & {
  /**
   * @generated from field: hami.frontend.v1.Member member = 1;
   */
  member?: Member;
};

/**
 * Describes the message hami.frontend.v1.GetMemberInformationResponse.
 * Use `create(GetMemberInformationResponseSchema)` to create a new message.
 */
export const GetMemberInformationResponseSchema: GenMessage<GetMemberInformationResponse> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 4);

/**
 * 会員情報
 *
 * @generated from message hami.frontend.v1.Member
 */
export type Member = Message<"hami.frontend.v1.Member"> & {
  /**
   * @generated from field: int32 member_id = 1;
   */
  memberId: number;

  /**
   * @generated from field: string member_number = 2;
   */
  memberNumber: string;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: string first_name = 4;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 5;
   */
  lastName: string;

  /**
   * @generated from field: string first_name_kana = 6;
   */
  firstNameKana: string;

  /**
   * @generated from field: string last_name_kana = 7;
   */
  lastNameKana: string;

  /**
   * @generated from field: string postal_code = 8;
   */
  postalCode: string;

  /**
   * @generated from field: string prefecture = 9;
   */
  prefecture: string;

  /**
   * @generated from field: string address = 10;
   */
  address: string;

  /**
   * @generated from field: optional string apartment = 11;
   */
  apartment?: string;

  /**
   * @generated from field: string phone_number = 12;
   */
  phoneNumber: string;

  /**
   * @generated from field: int32 birth_year = 13;
   */
  birthYear: number;

  /**
   * @generated from field: int32 birth_month = 14;
   */
  birthMonth: number;

  /**
   * @generated from field: int32 birth_day = 15;
   */
  birthDay: number;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 20;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 21;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hami.frontend.v1.Member.
 * Use `create(MemberSchema)` to create a new message.
 */
export const MemberSchema: GenMessage<Member> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 5);

/**
 * 退会申請リクエスト
 *
 * @generated from message hami.frontend.v1.ApplyForRetirementRequest
 */
export type ApplyForRetirementRequest = Message<"hami.frontend.v1.ApplyForRetirementRequest"> & {
};

/**
 * Describes the message hami.frontend.v1.ApplyForRetirementRequest.
 * Use `create(ApplyForRetirementRequestSchema)` to create a new message.
 */
export const ApplyForRetirementRequestSchema: GenMessage<ApplyForRetirementRequest> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 6);

/**
 * 退会申請レスポンス
 *
 * @generated from message hami.frontend.v1.ApplyForRetirementResponse
 */
export type ApplyForRetirementResponse = Message<"hami.frontend.v1.ApplyForRetirementResponse"> & {
};

/**
 * Describes the message hami.frontend.v1.ApplyForRetirementResponse.
 * Use `create(ApplyForRetirementResponseSchema)` to create a new message.
 */
export const ApplyForRetirementResponseSchema: GenMessage<ApplyForRetirementResponse> = /*@__PURE__*/
  messageDesc(file_member_information_change_service, 7);

/**
 * 変更申請ステータス
 *
 * @generated from enum hami.frontend.v1.MemberInformationChangeStatus
 */
export enum MemberInformationChangeStatus {
  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_PENDING = 1;
   */
  PENDING = 1,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_APPROVED = 2;
   */
  APPROVED = 2,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_REJECTED = 3;
   */
  REJECTED = 3,

  /**
   * @generated from enum value: MEMBER_INFORMATION_CHANGE_STATUS_CANCELLED = 4;
   */
  CANCELLED = 4,
}

/**
 * Describes the enum hami.frontend.v1.MemberInformationChangeStatus.
 */
export const MemberInformationChangeStatusSchema: GenEnum<MemberInformationChangeStatus> = /*@__PURE__*/
  enumDesc(file_member_information_change_service, 0);

/**
 * 登録情報変更申請サービス
 *
 * @generated from service hami.frontend.v1.MemberInformationChangeService
 */
export const MemberInformationChangeService: GenService<{
  /**
   * 会員情報取得
   *
   * @generated from rpc hami.frontend.v1.MemberInformationChangeService.GetMemberInformation
   */
  getMemberInformation: {
    methodKind: "unary";
    input: typeof GetMemberInformationRequestSchema;
    output: typeof GetMemberInformationResponseSchema;
  },
  /**
   * 変更申請作成
   *
   * @generated from rpc hami.frontend.v1.MemberInformationChangeService.CreateMemberInformationChangeApplication
   */
  createMemberInformationChangeApplication: {
    methodKind: "unary";
    input: typeof CreateMemberInformationChangeApplicationRequestSchema;
    output: typeof CreateMemberInformationChangeApplicationResponseSchema;
  },
  /**
   * 退会申請
   *
   * @generated from rpc hami.frontend.v1.MemberInformationChangeService.ApplyForRetirement
   */
  applyForRetirement: {
    methodKind: "unary";
    input: typeof ApplyForRetirementRequestSchema;
    output: typeof ApplyForRetirementResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_member_information_change_service, 0);

