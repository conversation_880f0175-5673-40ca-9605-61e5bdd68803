// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file common_enums.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file common_enums.proto.
 */
export const file_common_enums: GenFile = /*@__PURE__*/
  fileDesc("ChJjb21tb25fZW51bXMucHJvdG8SEGhhbWkuZnJvbnRlbmQudjEqRQoGR2VuZGVyEhYKEkdFTkRFUl9VTlNQRUNJRklFRBAAEgwKCFNUQUxMSU9OEAESCAoETUFSRRACEgsKB0dFTERJTkcQAyqKAQoJQ29hdENvbG9yEhoKFkNPQVRfQ09MT1JfVU5TUEVDSUZJRUQQABIHCgNCQVkQARIMCghEQVJLX0JBWRACEgkKBUJST1dOEAMSCQoFQkxBQ0sQBBIICgRHUkFZEAUSDAoIQ0hFU1ROVVQQBhIRCg1EQVJLX0NIRVNUTlVUEAcSCQoFV0hJVEUQCCqPAQoKSG9yc2VDbGFzcxIbChdIT1JTRV9DTEFTU19VTlNQRUNJRklFRBAAEggKBE9QRU4QARIOCgpUSFJFRV9XSU5TEAISDAoIVFdPX1dJTlMQAxILCgdPTkVfV0lOEAQSCgoGTUFJREVOEAUSCgoGTk9WSUNFEAYSCQoFT1RIRVIQBxIMCghOT19DTEFTUxAIKp4BCgtNZXNzYWdlVHlwZRIcChhNRVNTQUdFX1RZUEVfVU5TUEVDSUZJRUQQABIbChdNRVNTQUdFX1RZUEVfSU5ESVZJRFVBTBABEhoKFk1FU1NBR0VfVFlQRV9CUk9BRENBU1QQAhIdChlNRVNTQUdFX1RZUEVfTk9USUZJQ0FUSU9OEAMSGQoVTUVTU0FHRV9UWVBFX1JFTUlOREVSEAQqhQEKDkF0dGFjaG1lbnRUeXBlEh8KG0FUVEFDSE1FTlRfVFlQRV9VTlNQRUNJRklFRBAAEhkKFUFUVEFDSE1FTlRfVFlQRV9JTUFHRRABEhwKGEFUVEFDSE1FTlRfVFlQRV9ET0NVTUVOVBACEhkKFUFUVEFDSE1FTlRfVFlQRV9PVEhFUhADYgZwcm90bzM");

/**
 * 性別
 *
 * @generated from enum hami.frontend.v1.Gender
 */
export enum Gender {
  /**
   * @generated from enum value: GENDER_UNSPECIFIED = 0;
   */
  GENDER_UNSPECIFIED = 0,

  /**
   * 牡馬
   *
   * @generated from enum value: STALLION = 1;
   */
  STALLION = 1,

  /**
   * 牝馬
   *
   * @generated from enum value: MARE = 2;
   */
  MARE = 2,

  /**
   * せん馬
   *
   * @generated from enum value: GELDING = 3;
   */
  GELDING = 3,
}

/**
 * Describes the enum hami.frontend.v1.Gender.
 */
export const GenderSchema: GenEnum<Gender> = /*@__PURE__*/
  enumDesc(file_common_enums, 0);

/**
 * 毛色
 *
 * @generated from enum hami.frontend.v1.CoatColor
 */
export enum CoatColor {
  /**
   * @generated from enum value: COAT_COLOR_UNSPECIFIED = 0;
   */
  COAT_COLOR_UNSPECIFIED = 0,

  /**
   * 鹿毛
   *
   * @generated from enum value: BAY = 1;
   */
  BAY = 1,

  /**
   * 黒鹿毛
   *
   * @generated from enum value: DARK_BAY = 2;
   */
  DARK_BAY = 2,

  /**
   * 青鹿毛
   *
   * @generated from enum value: BROWN = 3;
   */
  BROWN = 3,

  /**
   * 青毛
   *
   * @generated from enum value: BLACK = 4;
   */
  BLACK = 4,

  /**
   * 芦毛
   *
   * @generated from enum value: GRAY = 5;
   */
  GRAY = 5,

  /**
   * 栗毛
   *
   * @generated from enum value: CHESTNUT = 6;
   */
  CHESTNUT = 6,

  /**
   * 栃栗毛
   *
   * @generated from enum value: DARK_CHESTNUT = 7;
   */
  DARK_CHESTNUT = 7,

  /**
   * 白毛
   *
   * @generated from enum value: WHITE = 8;
   */
  WHITE = 8,
}

/**
 * Describes the enum hami.frontend.v1.CoatColor.
 */
export const CoatColorSchema: GenEnum<CoatColor> = /*@__PURE__*/
  enumDesc(file_common_enums, 1);

/**
 * 馬のクラス
 *
 * @generated from enum hami.frontend.v1.HorseClass
 */
export enum HorseClass {
  /**
   * @generated from enum value: HORSE_CLASS_UNSPECIFIED = 0;
   */
  HORSE_CLASS_UNSPECIFIED = 0,

  /**
   * オープン
   *
   * @generated from enum value: OPEN = 1;
   */
  OPEN = 1,

  /**
   * 3勝クラス
   *
   * @generated from enum value: THREE_WINS = 2;
   */
  THREE_WINS = 2,

  /**
   * 2勝クラス
   *
   * @generated from enum value: TWO_WINS = 3;
   */
  TWO_WINS = 3,

  /**
   * 1勝クラス
   *
   * @generated from enum value: ONE_WIN = 4;
   */
  ONE_WIN = 4,

  /**
   * 新馬
   *
   * @generated from enum value: MAIDEN = 5;
   */
  MAIDEN = 5,

  /**
   * 未勝利
   *
   * @generated from enum value: NOVICE = 6;
   */
  NOVICE = 6,

  /**
   * その他
   *
   * @generated from enum value: OTHER = 7;
   */
  OTHER = 7,

  /**
   * クラス未定
   *
   * @generated from enum value: NO_CLASS = 8;
   */
  NO_CLASS = 8,
}

/**
 * Describes the enum hami.frontend.v1.HorseClass.
 */
export const HorseClassSchema: GenEnum<HorseClass> = /*@__PURE__*/
  enumDesc(file_common_enums, 2);

/**
 * メッセージ種別
 *
 * @generated from enum hami.frontend.v1.MessageType
 */
export enum MessageType {
  /**
   * @generated from enum value: MESSAGE_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 個別メッセージ
   *
   * @generated from enum value: MESSAGE_TYPE_INDIVIDUAL = 1;
   */
  INDIVIDUAL = 1,

  /**
   * 一斉送信
   *
   * @generated from enum value: MESSAGE_TYPE_BROADCAST = 2;
   */
  BROADCAST = 2,

  /**
   * 通知メッセージ
   *
   * @generated from enum value: MESSAGE_TYPE_NOTIFICATION = 3;
   */
  NOTIFICATION = 3,

  /**
   * リマインダー
   *
   * @generated from enum value: MESSAGE_TYPE_REMINDER = 4;
   */
  REMINDER = 4,
}

/**
 * Describes the enum hami.frontend.v1.MessageType.
 */
export const MessageTypeSchema: GenEnum<MessageType> = /*@__PURE__*/
  enumDesc(file_common_enums, 3);

/**
 * 添付ファイル種別
 *
 * @generated from enum hami.frontend.v1.AttachmentType
 */
export enum AttachmentType {
  /**
   * @generated from enum value: ATTACHMENT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * 画像ファイル（JPEG, PNG, GIF等）
   *
   * @generated from enum value: ATTACHMENT_TYPE_IMAGE = 1;
   */
  IMAGE = 1,

  /**
   * 文書ファイル（PDF, Word, Excel等）
   *
   * @generated from enum value: ATTACHMENT_TYPE_DOCUMENT = 2;
   */
  DOCUMENT = 2,

  /**
   * その他のファイル
   *
   * @generated from enum value: ATTACHMENT_TYPE_OTHER = 3;
   */
  OTHER = 3,
}

/**
 * Describes the enum hami.frontend.v1.AttachmentType.
 */
export const AttachmentTypeSchema: GenEnum<AttachmentType> = /*@__PURE__*/
  enumDesc(file_common_enums, 4);

