// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file member_message_service.proto (package hami.frontend.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { AttachmentType, MessageType } from "./common_enums_pb";
import { file_common_enums } from "./common_enums_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file member_message_service.proto.
 */
export const file_member_message_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_common_enums]);

/**
 * 会員向けメッセージ情報（管理情報を除外）
 *
 * @generated from message hami.frontend.v1.MemberMessage
 */
export type MemberMessage = Message<"hami.frontend.v1.MemberMessage"> & {
  /**
   * 外部公開用ID（public_idを使用）
   *
   * @generated from field: string message_id = 1;
   */
  messageId: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string body = 3;
   */
  body: string;

  /**
   * @generated from field: hami.frontend.v1.MessageType message_type = 4;
   */
  messageType: MessageType;

  /**
   * ISO 8601形式
   *
   * @generated from field: string sent_at = 5;
   */
  sentAt: string;

  /**
   * @generated from field: repeated hami.frontend.v1.MemberMessageAttachment attachments = 6;
   */
  attachments: MemberMessageAttachment[];

  /**
   * 既読フラグ
   *
   * @generated from field: bool is_read = 7;
   */
  isRead: boolean;

  /**
   * 既読日時（ISO 8601形式）
   *
   * @generated from field: optional string read_at = 8;
   */
  readAt?: string;
};

/**
 * Describes the message hami.frontend.v1.MemberMessage.
 * Use `create(MemberMessageSchema)` to create a new message.
 */
export const MemberMessageSchema: GenMessage<MemberMessage> = /*@__PURE__*/
  messageDesc(file_member_message_service, 0);

/**
 * 会員向け添付ファイル情報
 *
 * @generated from message hami.frontend.v1.MemberMessageAttachment
 */
export type MemberMessageAttachment = Message<"hami.frontend.v1.MemberMessageAttachment"> & {
  /**
   * @generated from field: int64 message_attachment_id = 1;
   */
  messageAttachmentId: bigint;

  /**
   * @generated from field: string file_name = 2;
   */
  fileName: string;

  /**
   * @generated from field: string mime_type = 3;
   */
  mimeType: string;

  /**
   * @generated from field: int64 file_size = 4;
   */
  fileSize: bigint;

  /**
   * @generated from field: hami.frontend.v1.AttachmentType attachment_type = 5;
   */
  attachmentType: AttachmentType;

  /**
   * @generated from field: int32 display_order = 6;
   */
  displayOrder: number;
};

/**
 * Describes the message hami.frontend.v1.MemberMessageAttachment.
 * Use `create(MemberMessageAttachmentSchema)` to create a new message.
 */
export const MemberMessageAttachmentSchema: GenMessage<MemberMessageAttachment> = /*@__PURE__*/
  messageDesc(file_member_message_service, 1);

/**
 * 受信メッセージ一覧取得リクエスト
 *
 * @generated from message hami.frontend.v1.ListReceivedMessagesRequest
 */
export type ListReceivedMessagesRequest = Message<"hami.frontend.v1.ListReceivedMessagesRequest"> & {
  /**
   * メッセージ種別フィルタ
   *
   * @generated from field: optional hami.frontend.v1.MessageType message_type = 1;
   */
  messageType?: MessageType;

  /**
   * 未読のみ表示
   *
   * @generated from field: optional bool unread_only = 2;
   */
  unreadOnly?: boolean;

  /**
   * @generated from field: int32 page = 3;
   */
  page: number;

  /**
   * @generated from field: int32 page_size = 4;
   */
  pageSize: number;

  /**
   * "sent_at", "read_at"
   *
   * @generated from field: string sort_by = 5;
   */
  sortBy: string;

  /**
   * "desc", "asc"
   *
   * @generated from field: string sort_order = 6;
   */
  sortOrder: string;
};

/**
 * Describes the message hami.frontend.v1.ListReceivedMessagesRequest.
 * Use `create(ListReceivedMessagesRequestSchema)` to create a new message.
 */
export const ListReceivedMessagesRequestSchema: GenMessage<ListReceivedMessagesRequest> = /*@__PURE__*/
  messageDesc(file_member_message_service, 2);

/**
 * @generated from message hami.frontend.v1.ListReceivedMessagesResponse
 */
export type ListReceivedMessagesResponse = Message<"hami.frontend.v1.ListReceivedMessagesResponse"> & {
  /**
   * @generated from field: repeated hami.frontend.v1.MemberMessage messages = 1;
   */
  messages: MemberMessage[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;

  /**
   * @generated from field: int32 total_pages = 3;
   */
  totalPages: number;

  /**
   * @generated from field: bool has_next_page = 4;
   */
  hasNextPage: boolean;

  /**
   * @generated from field: bool has_prev_page = 5;
   */
  hasPrevPage: boolean;

  /**
   * 未読メッセージ数
   *
   * @generated from field: int32 unread_count = 6;
   */
  unreadCount: number;
};

/**
 * Describes the message hami.frontend.v1.ListReceivedMessagesResponse.
 * Use `create(ListReceivedMessagesResponseSchema)` to create a new message.
 */
export const ListReceivedMessagesResponseSchema: GenMessage<ListReceivedMessagesResponse> = /*@__PURE__*/
  messageDesc(file_member_message_service, 3);

/**
 * メッセージ詳細取得リクエスト
 *
 * @generated from message hami.frontend.v1.GetMessageRequest
 */
export type GetMessageRequest = Message<"hami.frontend.v1.GetMessageRequest"> & {
  /**
   * @generated from field: string message_id = 1;
   */
  messageId: string;
};

/**
 * Describes the message hami.frontend.v1.GetMessageRequest.
 * Use `create(GetMessageRequestSchema)` to create a new message.
 */
export const GetMessageRequestSchema: GenMessage<GetMessageRequest> = /*@__PURE__*/
  messageDesc(file_member_message_service, 4);

/**
 * @generated from message hami.frontend.v1.GetMessageResponse
 */
export type GetMessageResponse = Message<"hami.frontend.v1.GetMessageResponse"> & {
  /**
   * @generated from field: hami.frontend.v1.MemberMessage message = 1;
   */
  message?: MemberMessage;
};

/**
 * Describes the message hami.frontend.v1.GetMessageResponse.
 * Use `create(GetMessageResponseSchema)` to create a new message.
 */
export const GetMessageResponseSchema: GenMessage<GetMessageResponse> = /*@__PURE__*/
  messageDesc(file_member_message_service, 5);

/**
 * 既読更新リクエスト
 *
 * @generated from message hami.frontend.v1.MarkAsReadRequest
 */
export type MarkAsReadRequest = Message<"hami.frontend.v1.MarkAsReadRequest"> & {
  /**
   * @generated from field: string message_id = 1;
   */
  messageId: string;
};

/**
 * Describes the message hami.frontend.v1.MarkAsReadRequest.
 * Use `create(MarkAsReadRequestSchema)` to create a new message.
 */
export const MarkAsReadRequestSchema: GenMessage<MarkAsReadRequest> = /*@__PURE__*/
  messageDesc(file_member_message_service, 6);

/**
 * @generated from message hami.frontend.v1.MarkAsReadResponse
 */
export type MarkAsReadResponse = Message<"hami.frontend.v1.MarkAsReadResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * 既読日時（ISO 8601形式）
   *
   * @generated from field: optional string read_at = 2;
   */
  readAt?: string;
};

/**
 * Describes the message hami.frontend.v1.MarkAsReadResponse.
 * Use `create(MarkAsReadResponseSchema)` to create a new message.
 */
export const MarkAsReadResponseSchema: GenMessage<MarkAsReadResponse> = /*@__PURE__*/
  messageDesc(file_member_message_service, 7);

/**
 * 未読メッセージ数取得リクエスト
 *
 * リクエストパラメータなし（認証された会員の未読数を取得）
 *
 * @generated from message hami.frontend.v1.GetUnreadCountRequest
 */
export type GetUnreadCountRequest = Message<"hami.frontend.v1.GetUnreadCountRequest"> & {
};

/**
 * Describes the message hami.frontend.v1.GetUnreadCountRequest.
 * Use `create(GetUnreadCountRequestSchema)` to create a new message.
 */
export const GetUnreadCountRequestSchema: GenMessage<GetUnreadCountRequest> = /*@__PURE__*/
  messageDesc(file_member_message_service, 8);

/**
 * @generated from message hami.frontend.v1.GetUnreadCountResponse
 */
export type GetUnreadCountResponse = Message<"hami.frontend.v1.GetUnreadCountResponse"> & {
  /**
   * @generated from field: int32 unread_count = 1;
   */
  unreadCount: number;
};

/**
 * Describes the message hami.frontend.v1.GetUnreadCountResponse.
 * Use `create(GetUnreadCountResponseSchema)` to create a new message.
 */
export const GetUnreadCountResponseSchema: GenMessage<GetUnreadCountResponse> = /*@__PURE__*/
  messageDesc(file_member_message_service, 9);

/**
 * 添付ファイルダウンロードURL取得リクエスト
 *
 * @generated from message hami.frontend.v1.GetAttachmentDownloadUrlRequest
 */
export type GetAttachmentDownloadUrlRequest = Message<"hami.frontend.v1.GetAttachmentDownloadUrlRequest"> & {
  /**
   * @generated from field: int64 message_attachment_id = 1;
   */
  messageAttachmentId: bigint;
};

/**
 * Describes the message hami.frontend.v1.GetAttachmentDownloadUrlRequest.
 * Use `create(GetAttachmentDownloadUrlRequestSchema)` to create a new message.
 */
export const GetAttachmentDownloadUrlRequestSchema: GenMessage<GetAttachmentDownloadUrlRequest> = /*@__PURE__*/
  messageDesc(file_member_message_service, 10);

/**
 * @generated from message hami.frontend.v1.GetAttachmentDownloadUrlResponse
 */
export type GetAttachmentDownloadUrlResponse = Message<"hami.frontend.v1.GetAttachmentDownloadUrlResponse"> & {
  /**
   * 署名付きURL
   *
   * @generated from field: string download_url = 1;
   */
  downloadUrl: string;

  /**
   * 有効期限（秒）
   *
   * @generated from field: int32 expires_in = 2;
   */
  expiresIn: number;
};

/**
 * Describes the message hami.frontend.v1.GetAttachmentDownloadUrlResponse.
 * Use `create(GetAttachmentDownloadUrlResponseSchema)` to create a new message.
 */
export const GetAttachmentDownloadUrlResponseSchema: GenMessage<GetAttachmentDownloadUrlResponse> = /*@__PURE__*/
  messageDesc(file_member_message_service, 11);

/**
 * 会員向けメッセージサービス
 *
 * @generated from service hami.frontend.v1.MemberMessageService
 */
export const MemberMessageService: GenService<{
  /**
   * 受信メッセージ一覧取得
   *
   * @generated from rpc hami.frontend.v1.MemberMessageService.ListReceivedMessages
   */
  listReceivedMessages: {
    methodKind: "unary";
    input: typeof ListReceivedMessagesRequestSchema;
    output: typeof ListReceivedMessagesResponseSchema;
  },
  /**
   * メッセージ詳細取得
   *
   * @generated from rpc hami.frontend.v1.MemberMessageService.GetMessage
   */
  getMessage: {
    methodKind: "unary";
    input: typeof GetMessageRequestSchema;
    output: typeof GetMessageResponseSchema;
  },
  /**
   * メッセージ既読更新
   *
   * @generated from rpc hami.frontend.v1.MemberMessageService.MarkAsRead
   */
  markAsRead: {
    methodKind: "unary";
    input: typeof MarkAsReadRequestSchema;
    output: typeof MarkAsReadResponseSchema;
  },
  /**
   * 未読メッセージ数取得
   *
   * @generated from rpc hami.frontend.v1.MemberMessageService.GetUnreadCount
   */
  getUnreadCount: {
    methodKind: "unary";
    input: typeof GetUnreadCountRequestSchema;
    output: typeof GetUnreadCountResponseSchema;
  },
  /**
   * 添付ファイルダウンロードURL取得
   *
   * @generated from rpc hami.frontend.v1.MemberMessageService.GetAttachmentDownloadUrl
   */
  getAttachmentDownloadUrl: {
    methodKind: "unary";
    input: typeof GetAttachmentDownloadUrlRequestSchema;
    output: typeof GetAttachmentDownloadUrlResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_member_message_service, 0);

