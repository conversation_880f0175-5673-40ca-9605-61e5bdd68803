{"cSpell.enabled": true, "cSpell.enableFiletypes": ["typescript", "javascript", "typescriptreact", "javascriptreact", "markdown", "json", "yaml", "yml", "proto", "dockerfile", "terraform"], "cSpell.checkOnlyEnabledFileTypes": false, "cSpell.showStatus": true, "cSpell.showSuggestions": true, "cSpell.numSuggestions": 8, "cSpell.minWordLength": 4, "cSpell.maxNumberOfProblems": 100, "cSpell.maxDuplicateProblems": 5, "cSpell.allowCompoundWords": true, "cSpell.ignorePaths": ["node_modules/**", "dist/**", "build/**", "coverage/**", "*.min.js", "*.min.css", "pnpm-lock.yaml", "package-lock.json", "yarn.lock", ".git/**", ".next/**", "**/*.log", "**/*.map", "infra/**/*.tf", "packages/*/gen/**", "packages/*/node_modules/**"], "cSpell.diagnosticLevel": "Warning", "cSpell.showCommandsInEditorContextMenu": true, "cSpell.autoFormatConfigFile": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "javascript.suggest.autoImports": true, "files.associations": {"*.proto": "proto3"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/coverage": true, "**/.next": true, "**/packages/*/gen": true, "pnpm-lock.yaml": true, "package-lock.json": true, "yarn.lock": true}}